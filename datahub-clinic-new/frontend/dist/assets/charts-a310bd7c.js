import{c as Oi,g as oe,r as R,R as S}from"./vendor-fdd35676.js";function sd(e){var t,r,n="";if(typeof e=="string"||typeof e=="number")n+=e;else if(typeof e=="object")if(Array.isArray(e)){var i=e.length;for(t=0;t<i;t++)e[t]&&(r=sd(e[t]))&&(n&&(n+=" "),n+=r)}else for(r in e)e[r]&&(n&&(n+=" "),n+=r);return n}function J(){for(var e,t,r=0,n="",i=arguments.length;r<i;r++)(e=arguments[r])&&(t=sd(e))&&(n&&(n+=" "),n+=t);return n}var Zm=Array.isArray,Be=Zm,Jm=typeof Oi=="object"&&Oi&&Oi.Object===Object&&Oi,fd=Jm,Qm=fd,eg=typeof self=="object"&&self&&self.Object===Object&&self,tg=Qm||eg||Function("return this")(),ht=tg,rg=ht,ng=rg.Symbol,pi=ng,Yl=pi,pd=Object.prototype,ig=pd.hasOwnProperty,ag=pd.toString,hn=Yl?Yl.toStringTag:void 0;function og(e){var t=ig.call(e,hn),r=e[hn];try{e[hn]=void 0;var n=!0}catch{}var i=ag.call(e);return n&&(t?e[hn]=r:delete e[hn]),i}var ug=og,cg=Object.prototype,lg=cg.toString;function sg(e){return lg.call(e)}var fg=sg,Zl=pi,pg=ug,hg=fg,dg="[object Null]",vg="[object Undefined]",Jl=Zl?Zl.toStringTag:void 0;function yg(e){return e==null?e===void 0?vg:dg:Jl&&Jl in Object(e)?pg(e):hg(e)}var $t=yg;function mg(e){return e!=null&&typeof e=="object"}var Tt=mg,gg=$t,bg=Tt,xg="[object Symbol]";function wg(e){return typeof e=="symbol"||bg(e)&&gg(e)==xg}var Jr=wg,Og=Be,Ag=Jr,Sg=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Pg=/^\w*$/;function _g(e,t){if(Og(e))return!1;var r=typeof e;return r=="number"||r=="symbol"||r=="boolean"||e==null||Ag(e)?!0:Pg.test(e)||!Sg.test(e)||t!=null&&e in Object(t)}var Fc=_g;function $g(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}var Dt=$g;const Qr=oe(Dt);var Tg=$t,Eg=Dt,jg="[object AsyncFunction]",Mg="[object Function]",Ig="[object GeneratorFunction]",Cg="[object Proxy]";function kg(e){if(!Eg(e))return!1;var t=Tg(e);return t==Mg||t==Ig||t==jg||t==Cg}var Wc=kg;const X=oe(Wc);var Dg=ht,Ng=Dg["__core-js_shared__"],Rg=Ng,Ro=Rg,Ql=function(){var e=/[^.]+$/.exec(Ro&&Ro.keys&&Ro.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();function Lg(e){return!!Ql&&Ql in e}var Bg=Lg,Fg=Function.prototype,Wg=Fg.toString;function zg(e){if(e!=null){try{return Wg.call(e)}catch{}try{return e+""}catch{}}return""}var hd=zg,Ug=Wc,qg=Bg,Hg=Dt,Kg=hd,Gg=/[\\^$.*+?()[\]{}|]/g,Vg=/^\[object .+?Constructor\]$/,Xg=Function.prototype,Yg=Object.prototype,Zg=Xg.toString,Jg=Yg.hasOwnProperty,Qg=RegExp("^"+Zg.call(Jg).replace(Gg,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function eb(e){if(!Hg(e)||qg(e))return!1;var t=Ug(e)?Qg:Vg;return t.test(Kg(e))}var tb=eb;function rb(e,t){return e==null?void 0:e[t]}var nb=rb,ib=tb,ab=nb;function ob(e,t){var r=ab(e,t);return ib(r)?r:void 0}var lr=ob,ub=lr,cb=ub(Object,"create"),Ba=cb,es=Ba;function lb(){this.__data__=es?es(null):{},this.size=0}var sb=lb;function fb(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}var pb=fb,hb=Ba,db="__lodash_hash_undefined__",vb=Object.prototype,yb=vb.hasOwnProperty;function mb(e){var t=this.__data__;if(hb){var r=t[e];return r===db?void 0:r}return yb.call(t,e)?t[e]:void 0}var gb=mb,bb=Ba,xb=Object.prototype,wb=xb.hasOwnProperty;function Ob(e){var t=this.__data__;return bb?t[e]!==void 0:wb.call(t,e)}var Ab=Ob,Sb=Ba,Pb="__lodash_hash_undefined__";function _b(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=Sb&&t===void 0?Pb:t,this}var $b=_b,Tb=sb,Eb=pb,jb=gb,Mb=Ab,Ib=$b;function en(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}en.prototype.clear=Tb;en.prototype.delete=Eb;en.prototype.get=jb;en.prototype.has=Mb;en.prototype.set=Ib;var Cb=en;function kb(){this.__data__=[],this.size=0}var Db=kb;function Nb(e,t){return e===t||e!==e&&t!==t}var zc=Nb,Rb=zc;function Lb(e,t){for(var r=e.length;r--;)if(Rb(e[r][0],t))return r;return-1}var Fa=Lb,Bb=Fa,Fb=Array.prototype,Wb=Fb.splice;function zb(e){var t=this.__data__,r=Bb(t,e);if(r<0)return!1;var n=t.length-1;return r==n?t.pop():Wb.call(t,r,1),--this.size,!0}var Ub=zb,qb=Fa;function Hb(e){var t=this.__data__,r=qb(t,e);return r<0?void 0:t[r][1]}var Kb=Hb,Gb=Fa;function Vb(e){return Gb(this.__data__,e)>-1}var Xb=Vb,Yb=Fa;function Zb(e,t){var r=this.__data__,n=Yb(r,e);return n<0?(++this.size,r.push([e,t])):r[n][1]=t,this}var Jb=Zb,Qb=Db,e0=Ub,t0=Kb,r0=Xb,n0=Jb;function tn(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}tn.prototype.clear=Qb;tn.prototype.delete=e0;tn.prototype.get=t0;tn.prototype.has=r0;tn.prototype.set=n0;var Wa=tn,i0=lr,a0=ht,o0=i0(a0,"Map"),Uc=o0,ts=Cb,u0=Wa,c0=Uc;function l0(){this.size=0,this.__data__={hash:new ts,map:new(c0||u0),string:new ts}}var s0=l0;function f0(e){var t=typeof e;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?e!=="__proto__":e===null}var p0=f0,h0=p0;function d0(e,t){var r=e.__data__;return h0(t)?r[typeof t=="string"?"string":"hash"]:r.map}var za=d0,v0=za;function y0(e){var t=v0(this,e).delete(e);return this.size-=t?1:0,t}var m0=y0,g0=za;function b0(e){return g0(this,e).get(e)}var x0=b0,w0=za;function O0(e){return w0(this,e).has(e)}var A0=O0,S0=za;function P0(e,t){var r=S0(this,e),n=r.size;return r.set(e,t),this.size+=r.size==n?0:1,this}var _0=P0,$0=s0,T0=m0,E0=x0,j0=A0,M0=_0;function rn(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}rn.prototype.clear=$0;rn.prototype.delete=T0;rn.prototype.get=E0;rn.prototype.has=j0;rn.prototype.set=M0;var qc=rn,dd=qc,I0="Expected a function";function Hc(e,t){if(typeof e!="function"||t!=null&&typeof t!="function")throw new TypeError(I0);var r=function(){var n=arguments,i=t?t.apply(this,n):n[0],a=r.cache;if(a.has(i))return a.get(i);var o=e.apply(this,n);return r.cache=a.set(i,o)||a,o};return r.cache=new(Hc.Cache||dd),r}Hc.Cache=dd;var vd=Hc;const C0=oe(vd);var k0=vd,D0=500;function N0(e){var t=k0(e,function(n){return r.size===D0&&r.clear(),n}),r=t.cache;return t}var R0=N0,L0=R0,B0=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,F0=/\\(\\)?/g,W0=L0(function(e){var t=[];return e.charCodeAt(0)===46&&t.push(""),e.replace(B0,function(r,n,i,a){t.push(i?a.replace(F0,"$1"):n||r)}),t}),z0=W0;function U0(e,t){for(var r=-1,n=e==null?0:e.length,i=Array(n);++r<n;)i[r]=t(e[r],r,e);return i}var Kc=U0,rs=pi,q0=Kc,H0=Be,K0=Jr,G0=1/0,ns=rs?rs.prototype:void 0,is=ns?ns.toString:void 0;function yd(e){if(typeof e=="string")return e;if(H0(e))return q0(e,yd)+"";if(K0(e))return is?is.call(e):"";var t=e+"";return t=="0"&&1/e==-G0?"-0":t}var V0=yd,X0=V0;function Y0(e){return e==null?"":X0(e)}var md=Y0,Z0=Be,J0=Fc,Q0=z0,ex=md;function tx(e,t){return Z0(e)?e:J0(e,t)?[e]:Q0(ex(e))}var gd=tx,rx=Jr,nx=1/0;function ix(e){if(typeof e=="string"||rx(e))return e;var t=e+"";return t=="0"&&1/e==-nx?"-0":t}var Ua=ix,ax=gd,ox=Ua;function ux(e,t){t=ax(t,e);for(var r=0,n=t.length;e!=null&&r<n;)e=e[ox(t[r++])];return r&&r==n?e:void 0}var Gc=ux,cx=Gc;function lx(e,t,r){var n=e==null?void 0:cx(e,t);return n===void 0?r:n}var bd=lx;const Ke=oe(bd);function sx(e){return e==null}var fx=sx;const Y=oe(fx);var px=$t,hx=Be,dx=Tt,vx="[object String]";function yx(e){return typeof e=="string"||!hx(e)&&dx(e)&&px(e)==vx}var mx=yx;const rr=oe(mx);var xd={exports:{}},ie={};/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Vc=Symbol.for("react.element"),Xc=Symbol.for("react.portal"),qa=Symbol.for("react.fragment"),Ha=Symbol.for("react.strict_mode"),Ka=Symbol.for("react.profiler"),Ga=Symbol.for("react.provider"),Va=Symbol.for("react.context"),gx=Symbol.for("react.server_context"),Xa=Symbol.for("react.forward_ref"),Ya=Symbol.for("react.suspense"),Za=Symbol.for("react.suspense_list"),Ja=Symbol.for("react.memo"),Qa=Symbol.for("react.lazy"),bx=Symbol.for("react.offscreen"),wd;wd=Symbol.for("react.module.reference");function et(e){if(typeof e=="object"&&e!==null){var t=e.$$typeof;switch(t){case Vc:switch(e=e.type,e){case qa:case Ka:case Ha:case Ya:case Za:return e;default:switch(e=e&&e.$$typeof,e){case gx:case Va:case Xa:case Qa:case Ja:case Ga:return e;default:return t}}case Xc:return t}}}ie.ContextConsumer=Va;ie.ContextProvider=Ga;ie.Element=Vc;ie.ForwardRef=Xa;ie.Fragment=qa;ie.Lazy=Qa;ie.Memo=Ja;ie.Portal=Xc;ie.Profiler=Ka;ie.StrictMode=Ha;ie.Suspense=Ya;ie.SuspenseList=Za;ie.isAsyncMode=function(){return!1};ie.isConcurrentMode=function(){return!1};ie.isContextConsumer=function(e){return et(e)===Va};ie.isContextProvider=function(e){return et(e)===Ga};ie.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===Vc};ie.isForwardRef=function(e){return et(e)===Xa};ie.isFragment=function(e){return et(e)===qa};ie.isLazy=function(e){return et(e)===Qa};ie.isMemo=function(e){return et(e)===Ja};ie.isPortal=function(e){return et(e)===Xc};ie.isProfiler=function(e){return et(e)===Ka};ie.isStrictMode=function(e){return et(e)===Ha};ie.isSuspense=function(e){return et(e)===Ya};ie.isSuspenseList=function(e){return et(e)===Za};ie.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===qa||e===Ka||e===Ha||e===Ya||e===Za||e===bx||typeof e=="object"&&e!==null&&(e.$$typeof===Qa||e.$$typeof===Ja||e.$$typeof===Ga||e.$$typeof===Va||e.$$typeof===Xa||e.$$typeof===wd||e.getModuleId!==void 0)};ie.typeOf=et;xd.exports=ie;var xx=xd.exports,wx=$t,Ox=Tt,Ax="[object Number]";function Sx(e){return typeof e=="number"||Ox(e)&&wx(e)==Ax}var Od=Sx;const Px=oe(Od);var _x=Od;function $x(e){return _x(e)&&e!=+e}var Tx=$x;const hi=oe(Tx);var Ce=function(t){return t===0?0:t>0?1:-1},Yt=function(t){return rr(t)&&t.indexOf("%")===t.length-1},L=function(t){return Px(t)&&!hi(t)},Ex=function(t){return Y(t)},Pe=function(t){return L(t)||rr(t)},jx=0,nn=function(t){var r=++jx;return"".concat(t||"").concat(r)},ke=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(!L(t)&&!rr(t))return n;var a;if(Yt(t)){var o=t.indexOf("%");a=r*parseFloat(t.slice(0,o))/100}else a=+t;return hi(a)&&(a=n),i&&a>r&&(a=r),a},It=function(t){if(!t)return null;var r=Object.keys(t);return r&&r.length?t[r[0]]:null},Mx=function(t){if(!Array.isArray(t))return!1;for(var r=t.length,n={},i=0;i<r;i++)if(!n[t[i]])n[t[i]]=!0;else return!0;return!1},qe=function(t,r){return L(t)&&L(r)?function(n){return t+n*(r-t)}:function(){return r}};function Li(e,t,r){return!e||!e.length?null:e.find(function(n){return n&&(typeof t=="function"?t(n):Ke(n,t))===r})}var Ix=function(t,r){return L(t)&&L(r)?t-r:rr(t)&&rr(r)?t.localeCompare(r):t instanceof Date&&r instanceof Date?t.getTime()-r.getTime():String(t).localeCompare(String(r))};function Ar(e,t){for(var r in e)if({}.hasOwnProperty.call(e,r)&&(!{}.hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if({}.hasOwnProperty.call(t,n)&&!{}.hasOwnProperty.call(e,n))return!1;return!0}function pu(e){"@babel/helpers - typeof";return pu=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},pu(e)}var Cx=["viewBox","children"],kx=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],as=["points","pathLength"],Lo={svg:Cx,polygon:as,polyline:as},Yc=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],Bi=function(t,r){if(!t||typeof t=="function"||typeof t=="boolean")return null;var n=t;if(R.isValidElement(t)&&(n=t.props),!Qr(n))return null;var i={};return Object.keys(n).forEach(function(a){Yc.includes(a)&&(i[a]=r||function(o){return n[a](n,o)})}),i},Dx=function(t,r,n){return function(i){return t(r,n,i),null}},nr=function(t,r,n){if(!Qr(t)||pu(t)!=="object")return null;var i=null;return Object.keys(t).forEach(function(a){var o=t[a];Yc.includes(a)&&typeof o=="function"&&(i||(i={}),i[a]=Dx(o,r,n))}),i},Nx=["children"],Rx=["children"];function os(e,t){if(e==null)return{};var r=Lx(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function Lx(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function hu(e){"@babel/helpers - typeof";return hu=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},hu(e)}var us={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},wt=function(t){return typeof t=="string"?t:t?t.displayName||t.name||"Component":""},cs=null,Bo=null,Zc=function e(t){if(t===cs&&Array.isArray(Bo))return Bo;var r=[];return R.Children.forEach(t,function(n){Y(n)||(xx.isFragment(n)?r=r.concat(e(n.props.children)):r.push(n))}),Bo=r,cs=t,r};function Ge(e,t){var r=[],n=[];return Array.isArray(t)?n=t.map(function(i){return wt(i)}):n=[wt(t)],Zc(e).forEach(function(i){var a=Ke(i,"type.displayName")||Ke(i,"type.name");n.indexOf(a)!==-1&&r.push(i)}),r}function Ue(e,t){var r=Ge(e,t);return r&&r[0]}var ls=function(t){if(!t||!t.props)return!1;var r=t.props,n=r.width,i=r.height;return!(!L(n)||n<=0||!L(i)||i<=0)},Bx=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],Fx=function(t){return t&&t.type&&rr(t.type)&&Bx.indexOf(t.type)>=0},Wx=function(t){return t&&hu(t)==="object"&&"clipDot"in t},zx=function(t,r,n,i){var a,o=(a=Lo==null?void 0:Lo[i])!==null&&a!==void 0?a:[];return r.startsWith("data-")||!X(t)&&(i&&o.includes(r)||kx.includes(r))||n&&Yc.includes(r)},H=function(t,r,n){if(!t||typeof t=="function"||typeof t=="boolean")return null;var i=t;if(R.isValidElement(t)&&(i=t.props),!Qr(i))return null;var a={};return Object.keys(i).forEach(function(o){var u;zx((u=i)===null||u===void 0?void 0:u[o],o,r,n)&&(a[o]=i[o])}),a},du=function e(t,r){if(t===r)return!0;var n=R.Children.count(t);if(n!==R.Children.count(r))return!1;if(n===0)return!0;if(n===1)return ss(Array.isArray(t)?t[0]:t,Array.isArray(r)?r[0]:r);for(var i=0;i<n;i++){var a=t[i],o=r[i];if(Array.isArray(a)||Array.isArray(o)){if(!e(a,o))return!1}else if(!ss(a,o))return!1}return!0},ss=function(t,r){if(Y(t)&&Y(r))return!0;if(!Y(t)&&!Y(r)){var n=t.props||{},i=n.children,a=os(n,Nx),o=r.props||{},u=o.children,c=os(o,Rx);return i&&u?Ar(a,c)&&du(i,u):!i&&!u?Ar(a,c):!1}return!1},fs=function(t,r){var n=[],i={};return Zc(t).forEach(function(a,o){if(Fx(a))n.push(a);else if(a){var u=wt(a.type),c=r[u]||{},l=c.handler,f=c.once;if(l&&(!f||!i[u])){var s=l(a,u,o);n.push(s),i[u]=!0}}}),n},Ux=function(t){var r=t&&t.type;return r&&us[r]?us[r]:null},qx=function(t,r){return Zc(r).indexOf(t)},Hx=["children","width","height","viewBox","className","style","title","desc"];function vu(){return vu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},vu.apply(this,arguments)}function Kx(e,t){if(e==null)return{};var r=Gx(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function Gx(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function yu(e){var t=e.children,r=e.width,n=e.height,i=e.viewBox,a=e.className,o=e.style,u=e.title,c=e.desc,l=Kx(e,Hx),f=i||{width:r,height:n,x:0,y:0},s=J("recharts-surface",a);return S.createElement("svg",vu({},H(l,!0,"svg"),{className:s,width:r,height:n,style:o,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height)}),S.createElement("title",null,u),S.createElement("desc",null,c),t)}var Vx=["children","className"];function mu(){return mu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},mu.apply(this,arguments)}function Xx(e,t){if(e==null)return{};var r=Yx(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function Yx(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var te=S.forwardRef(function(e,t){var r=e.children,n=e.className,i=Xx(e,Vx),a=J("recharts-layer",n);return S.createElement("g",mu({className:a},H(i,!0),{ref:t}),r)}),ot=function(t,r){for(var n=arguments.length,i=new Array(n>2?n-2:0),a=2;a<n;a++)i[a-2]=arguments[a]};function Zx(e,t,r){var n=-1,i=e.length;t<0&&(t=-t>i?0:i+t),r=r>i?i:r,r<0&&(r+=i),i=t>r?0:r-t>>>0,t>>>=0;for(var a=Array(i);++n<i;)a[n]=e[n+t];return a}var Jx=Zx,Qx=Jx;function ew(e,t,r){var n=e.length;return r=r===void 0?n:r,!t&&r>=n?e:Qx(e,t,r)}var tw=ew,rw="\\ud800-\\udfff",nw="\\u0300-\\u036f",iw="\\ufe20-\\ufe2f",aw="\\u20d0-\\u20ff",ow=nw+iw+aw,uw="\\ufe0e\\ufe0f",cw="\\u200d",lw=RegExp("["+cw+rw+ow+uw+"]");function sw(e){return lw.test(e)}var Ad=sw;function fw(e){return e.split("")}var pw=fw,Sd="\\ud800-\\udfff",hw="\\u0300-\\u036f",dw="\\ufe20-\\ufe2f",vw="\\u20d0-\\u20ff",yw=hw+dw+vw,mw="\\ufe0e\\ufe0f",gw="["+Sd+"]",gu="["+yw+"]",bu="\\ud83c[\\udffb-\\udfff]",bw="(?:"+gu+"|"+bu+")",Pd="[^"+Sd+"]",_d="(?:\\ud83c[\\udde6-\\uddff]){2}",$d="[\\ud800-\\udbff][\\udc00-\\udfff]",xw="\\u200d",Td=bw+"?",Ed="["+mw+"]?",ww="(?:"+xw+"(?:"+[Pd,_d,$d].join("|")+")"+Ed+Td+")*",Ow=Ed+Td+ww,Aw="(?:"+[Pd+gu+"?",gu,_d,$d,gw].join("|")+")",Sw=RegExp(bu+"(?="+bu+")|"+Aw+Ow,"g");function Pw(e){return e.match(Sw)||[]}var _w=Pw,$w=pw,Tw=Ad,Ew=_w;function jw(e){return Tw(e)?Ew(e):$w(e)}var Mw=jw,Iw=tw,Cw=Ad,kw=Mw,Dw=md;function Nw(e){return function(t){t=Dw(t);var r=Cw(t)?kw(t):void 0,n=r?r[0]:t.charAt(0),i=r?Iw(r,1).join(""):t.slice(1);return n[e]()+i}}var Rw=Nw,Lw=Rw,Bw=Lw("toUpperCase"),Fw=Bw;const eo=oe(Fw);function le(e){return function(){return e}}const jd=Math.cos,Fi=Math.sin,ut=Math.sqrt,Wi=Math.PI,to=2*Wi,xu=Math.PI,wu=2*xu,Kt=1e-6,Ww=wu-Kt;function Md(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}function zw(e){let t=Math.floor(e);if(!(t>=0))throw new Error(`invalid digits: ${e}`);if(t>15)return Md;const r=10**t;return function(n){this._+=n[0];for(let i=1,a=n.length;i<a;++i)this._+=Math.round(arguments[i]*r)/r+n[i]}}class Uw{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=t==null?Md:zw(t)}moveTo(t,r){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}`}closePath(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,r){this._append`L${this._x1=+t},${this._y1=+r}`}quadraticCurveTo(t,r,n,i){this._append`Q${+t},${+r},${this._x1=+n},${this._y1=+i}`}bezierCurveTo(t,r,n,i,a,o){this._append`C${+t},${+r},${+n},${+i},${this._x1=+a},${this._y1=+o}`}arcTo(t,r,n,i,a){if(t=+t,r=+r,n=+n,i=+i,a=+a,a<0)throw new Error(`negative radius: ${a}`);let o=this._x1,u=this._y1,c=n-t,l=i-r,f=o-t,s=u-r,p=f*f+s*s;if(this._x1===null)this._append`M${this._x1=t},${this._y1=r}`;else if(p>Kt)if(!(Math.abs(s*c-l*f)>Kt)||!a)this._append`L${this._x1=t},${this._y1=r}`;else{let h=n-o,v=i-u,d=c*c+l*l,y=h*h+v*v,b=Math.sqrt(d),w=Math.sqrt(p),x=a*Math.tan((xu-Math.acos((d+p-y)/(2*b*w)))/2),A=x/w,m=x/b;Math.abs(A-1)>Kt&&this._append`L${t+A*f},${r+A*s}`,this._append`A${a},${a},0,0,${+(s*h>f*v)},${this._x1=t+m*c},${this._y1=r+m*l}`}}arc(t,r,n,i,a,o){if(t=+t,r=+r,n=+n,o=!!o,n<0)throw new Error(`negative radius: ${n}`);let u=n*Math.cos(i),c=n*Math.sin(i),l=t+u,f=r+c,s=1^o,p=o?i-a:a-i;this._x1===null?this._append`M${l},${f}`:(Math.abs(this._x1-l)>Kt||Math.abs(this._y1-f)>Kt)&&this._append`L${l},${f}`,n&&(p<0&&(p=p%wu+wu),p>Ww?this._append`A${n},${n},0,1,${s},${t-u},${r-c}A${n},${n},0,1,${s},${this._x1=l},${this._y1=f}`:p>Kt&&this._append`A${n},${n},0,${+(p>=xu)},${s},${this._x1=t+n*Math.cos(a)},${this._y1=r+n*Math.sin(a)}`)}rect(t,r,n,i){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}h${n=+n}v${+i}h${-n}Z`}toString(){return this._}}function Jc(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(r==null)t=null;else{const n=Math.floor(r);if(!(n>=0))throw new RangeError(`invalid digits: ${r}`);t=n}return e},()=>new Uw(t)}function Qc(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function Id(e){this._context=e}Id.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t);break}}};function ro(e){return new Id(e)}function Cd(e){return e[0]}function kd(e){return e[1]}function Dd(e,t){var r=le(!0),n=null,i=ro,a=null,o=Jc(u);e=typeof e=="function"?e:e===void 0?Cd:le(e),t=typeof t=="function"?t:t===void 0?kd:le(t);function u(c){var l,f=(c=Qc(c)).length,s,p=!1,h;for(n==null&&(a=i(h=o())),l=0;l<=f;++l)!(l<f&&r(s=c[l],l,c))===p&&((p=!p)?a.lineStart():a.lineEnd()),p&&a.point(+e(s,l,c),+t(s,l,c));if(h)return a=null,h+""||null}return u.x=function(c){return arguments.length?(e=typeof c=="function"?c:le(+c),u):e},u.y=function(c){return arguments.length?(t=typeof c=="function"?c:le(+c),u):t},u.defined=function(c){return arguments.length?(r=typeof c=="function"?c:le(!!c),u):r},u.curve=function(c){return arguments.length?(i=c,n!=null&&(a=i(n)),u):i},u.context=function(c){return arguments.length?(c==null?n=a=null:a=i(n=c),u):n},u}function Ai(e,t,r){var n=null,i=le(!0),a=null,o=ro,u=null,c=Jc(l);e=typeof e=="function"?e:e===void 0?Cd:le(+e),t=typeof t=="function"?t:le(t===void 0?0:+t),r=typeof r=="function"?r:r===void 0?kd:le(+r);function l(s){var p,h,v,d=(s=Qc(s)).length,y,b=!1,w,x=new Array(d),A=new Array(d);for(a==null&&(u=o(w=c())),p=0;p<=d;++p){if(!(p<d&&i(y=s[p],p,s))===b)if(b=!b)h=p,u.areaStart(),u.lineStart();else{for(u.lineEnd(),u.lineStart(),v=p-1;v>=h;--v)u.point(x[v],A[v]);u.lineEnd(),u.areaEnd()}b&&(x[p]=+e(y,p,s),A[p]=+t(y,p,s),u.point(n?+n(y,p,s):x[p],r?+r(y,p,s):A[p]))}if(w)return u=null,w+""||null}function f(){return Dd().defined(i).curve(o).context(a)}return l.x=function(s){return arguments.length?(e=typeof s=="function"?s:le(+s),n=null,l):e},l.x0=function(s){return arguments.length?(e=typeof s=="function"?s:le(+s),l):e},l.x1=function(s){return arguments.length?(n=s==null?null:typeof s=="function"?s:le(+s),l):n},l.y=function(s){return arguments.length?(t=typeof s=="function"?s:le(+s),r=null,l):t},l.y0=function(s){return arguments.length?(t=typeof s=="function"?s:le(+s),l):t},l.y1=function(s){return arguments.length?(r=s==null?null:typeof s=="function"?s:le(+s),l):r},l.lineX0=l.lineY0=function(){return f().x(e).y(t)},l.lineY1=function(){return f().x(e).y(r)},l.lineX1=function(){return f().x(n).y(t)},l.defined=function(s){return arguments.length?(i=typeof s=="function"?s:le(!!s),l):i},l.curve=function(s){return arguments.length?(o=s,a!=null&&(u=o(a)),l):o},l.context=function(s){return arguments.length?(s==null?a=u=null:u=o(a=s),l):a},l}class Nd{constructor(t,r){this._context=t,this._x=r}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line}point(t,r){switch(t=+t,r=+r,this._point){case 0:{this._point=1,this._line?this._context.lineTo(t,r):this._context.moveTo(t,r);break}case 1:this._point=2;default:{this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,r,t,r):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+r)/2,t,this._y0,t,r);break}}this._x0=t,this._y0=r}}function qw(e){return new Nd(e,!0)}function Hw(e){return new Nd(e,!1)}const el={draw(e,t){const r=ut(t/Wi);e.moveTo(r,0),e.arc(0,0,r,0,to)}},Kw={draw(e,t){const r=ut(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},Rd=ut(1/3),Gw=Rd*2,Vw={draw(e,t){const r=ut(t/Gw),n=r*Rd;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},Xw={draw(e,t){const r=ut(t),n=-r/2;e.rect(n,n,r,r)}},Yw=.8908130915292852,Ld=Fi(Wi/10)/Fi(7*Wi/10),Zw=Fi(to/10)*Ld,Jw=-jd(to/10)*Ld,Qw={draw(e,t){const r=ut(t*Yw),n=Zw*r,i=Jw*r;e.moveTo(0,-r),e.lineTo(n,i);for(let a=1;a<5;++a){const o=to*a/5,u=jd(o),c=Fi(o);e.lineTo(c*r,-u*r),e.lineTo(u*n-c*i,c*n+u*i)}e.closePath()}},Fo=ut(3),eO={draw(e,t){const r=-ut(t/(Fo*3));e.moveTo(0,r*2),e.lineTo(-Fo*r,-r),e.lineTo(Fo*r,-r),e.closePath()}},Ve=-.5,Xe=ut(3)/2,Ou=1/ut(12),tO=(Ou/2+1)*3,rO={draw(e,t){const r=ut(t/tO),n=r/2,i=r*Ou,a=n,o=r*Ou+r,u=-a,c=o;e.moveTo(n,i),e.lineTo(a,o),e.lineTo(u,c),e.lineTo(Ve*n-Xe*i,Xe*n+Ve*i),e.lineTo(Ve*a-Xe*o,Xe*a+Ve*o),e.lineTo(Ve*u-Xe*c,Xe*u+Ve*c),e.lineTo(Ve*n+Xe*i,Ve*i-Xe*n),e.lineTo(Ve*a+Xe*o,Ve*o-Xe*a),e.lineTo(Ve*u+Xe*c,Ve*c-Xe*u),e.closePath()}};function nO(e,t){let r=null,n=Jc(i);e=typeof e=="function"?e:le(e||el),t=typeof t=="function"?t:le(t===void 0?64:+t);function i(){let a;if(r||(r=a=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),a)return r=null,a+""||null}return i.type=function(a){return arguments.length?(e=typeof a=="function"?a:le(a),i):e},i.size=function(a){return arguments.length?(t=typeof a=="function"?a:le(+a),i):t},i.context=function(a){return arguments.length?(r=a??null,i):r},i}function zi(){}function Ui(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function Bd(e){this._context=e}Bd.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:Ui(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:Ui(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function iO(e){return new Bd(e)}function Fd(e){this._context=e}Fd.prototype={areaStart:zi,areaEnd:zi,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x2,this._y2),this._context.closePath();break}case 2:{this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break}case 3:{this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4);break}}},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:Ui(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function aO(e){return new Fd(e)}function Wd(e){this._context=e}Wd.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:Ui(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function oO(e){return new Wd(e)}function zd(e){this._context=e}zd.prototype={areaStart:zi,areaEnd:zi,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e=+e,t=+t,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function uO(e){return new zd(e)}function ps(e){return e<0?-1:1}function hs(e,t,r){var n=e._x1-e._x0,i=t-e._x1,a=(e._y1-e._y0)/(n||i<0&&-0),o=(r-e._y1)/(i||n<0&&-0),u=(a*i+o*n)/(n+i);return(ps(a)+ps(o))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs(u))||0}function ds(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function Wo(e,t,r){var n=e._x0,i=e._y0,a=e._x1,o=e._y1,u=(a-n)/3;e._context.bezierCurveTo(n+u,i+u*t,a-u,o-u*r,a,o)}function qi(e){this._context=e}qi.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:Wo(this,this._t0,ds(this,this._t0));break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(e=+e,t=+t,!(e===this._x1&&t===this._y1)){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,Wo(this,ds(this,r=hs(this,e,t)),r);break;default:Wo(this,this._t0,r=hs(this,e,t));break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}};function Ud(e){this._context=new qd(e)}(Ud.prototype=Object.create(qi.prototype)).point=function(e,t){qi.prototype.point.call(this,t,e)};function qd(e){this._context=e}qd.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,i,a){this._context.bezierCurveTo(t,e,n,r,a,i)}};function cO(e){return new qi(e)}function lO(e){return new Ud(e)}function Hd(e){this._context=e}Hd.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),r===2)this._context.lineTo(e[1],t[1]);else for(var n=vs(e),i=vs(t),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],e[o],t[o]);(this._line||this._line!==0&&r===1)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}};function vs(e){var t,r=e.length-1,n,i=new Array(r),a=new Array(r),o=new Array(r);for(i[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<r-1;++t)i[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(i[r-1]=2,a[r-1]=7,o[r-1]=8*e[r-1]+e[r],t=1;t<r;++t)n=i[t]/a[t-1],a[t]-=n,o[t]-=n*o[t-1];for(i[r-1]=o[r-1]/a[r-1],t=r-2;t>=0;--t)i[t]=(o[t]-i[t+1])/a[t];for(a[r-1]=(e[r]+i[r-1])/2,t=0;t<r-1;++t)a[t]=2*e[t+1]-i[t+1];return[i,a]}function sO(e){return new Hd(e)}function no(e,t){this._context=e,this._t=t}no.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&this._point===2&&this._context.lineTo(this._x,this._y),(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:{if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}break}}this._x=e,this._y=t}};function fO(e){return new no(e,.5)}function pO(e){return new no(e,0)}function hO(e){return new no(e,1)}function $r(e,t){if((o=e.length)>1)for(var r=1,n,i,a=e[t[0]],o,u=a.length;r<o;++r)for(i=a,a=e[t[r]],n=0;n<u;++n)a[n][1]+=a[n][0]=isNaN(i[n][1])?i[n][0]:i[n][1]}function Au(e){for(var t=e.length,r=new Array(t);--t>=0;)r[t]=t;return r}function dO(e,t){return e[t]}function vO(e){const t=[];return t.key=e,t}function yO(){var e=le([]),t=Au,r=$r,n=dO;function i(a){var o=Array.from(e.apply(this,arguments),vO),u,c=o.length,l=-1,f;for(const s of a)for(u=0,++l;u<c;++u)(o[u][l]=[0,+n(s,o[u].key,l,a)]).data=s;for(u=0,f=Qc(t(o));u<c;++u)o[f[u]].index=u;return r(o,f),o}return i.keys=function(a){return arguments.length?(e=typeof a=="function"?a:le(Array.from(a)),i):e},i.value=function(a){return arguments.length?(n=typeof a=="function"?a:le(+a),i):n},i.order=function(a){return arguments.length?(t=a==null?Au:typeof a=="function"?a:le(Array.from(a)),i):t},i.offset=function(a){return arguments.length?(r=a??$r,i):r},i}function mO(e,t){if((n=e.length)>0){for(var r,n,i=0,a=e[0].length,o;i<a;++i){for(o=r=0;r<n;++r)o+=e[r][i][1]||0;if(o)for(r=0;r<n;++r)e[r][i][1]/=o}$r(e,t)}}function gO(e,t){if((i=e.length)>0){for(var r=0,n=e[t[0]],i,a=n.length;r<a;++r){for(var o=0,u=0;o<i;++o)u+=e[o][r][1]||0;n[r][1]+=n[r][0]=-u/2}$r(e,t)}}function bO(e,t){if(!(!((o=e.length)>0)||!((a=(i=e[t[0]]).length)>0))){for(var r=0,n=1,i,a,o;n<a;++n){for(var u=0,c=0,l=0;u<o;++u){for(var f=e[t[u]],s=f[n][1]||0,p=f[n-1][1]||0,h=(s-p)/2,v=0;v<u;++v){var d=e[t[v]],y=d[n][1]||0,b=d[n-1][1]||0;h+=y-b}c+=s,l+=h*s}i[n-1][1]+=i[n-1][0]=r,c&&(r-=l/c)}i[n-1][1]+=i[n-1][0]=r,$r(e,t)}}function Mn(e){"@babel/helpers - typeof";return Mn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Mn(e)}var xO=["type","size","sizeType"];function Su(){return Su=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Su.apply(this,arguments)}function ys(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ms(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ys(Object(r),!0).forEach(function(n){wO(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ys(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function wO(e,t,r){return t=OO(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function OO(e){var t=AO(e,"string");return Mn(t)=="symbol"?t:t+""}function AO(e,t){if(Mn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Mn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function SO(e,t){if(e==null)return{};var r=PO(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function PO(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var Kd={symbolCircle:el,symbolCross:Kw,symbolDiamond:Vw,symbolSquare:Xw,symbolStar:Qw,symbolTriangle:eO,symbolWye:rO},_O=Math.PI/180,$O=function(t){var r="symbol".concat(eo(t));return Kd[r]||el},TO=function(t,r,n){if(r==="area")return t;switch(n){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":{var i=18*_O;return 1.25*t*t*(Math.tan(i)-Math.tan(i*2)*Math.pow(Math.tan(i),2))}case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},EO=function(t,r){Kd["symbol".concat(eo(t))]=r},tl=function(t){var r=t.type,n=r===void 0?"circle":r,i=t.size,a=i===void 0?64:i,o=t.sizeType,u=o===void 0?"area":o,c=SO(t,xO),l=ms(ms({},c),{},{type:n,size:a,sizeType:u}),f=function(){var y=$O(n),b=nO().type(y).size(TO(a,u,n));return b()},s=l.className,p=l.cx,h=l.cy,v=H(l,!0);return p===+p&&h===+h&&a===+a?S.createElement("path",Su({},v,{className:J("recharts-symbols",s),transform:"translate(".concat(p,", ").concat(h,")"),d:f()})):null};tl.registerSymbol=EO;function Tr(e){"@babel/helpers - typeof";return Tr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Tr(e)}function Pu(){return Pu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Pu.apply(this,arguments)}function gs(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function jO(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?gs(Object(r),!0).forEach(function(n){In(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gs(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function MO(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function bs(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Vd(n.key),n)}}function IO(e,t,r){return t&&bs(e.prototype,t),r&&bs(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function CO(e,t,r){return t=Hi(t),kO(e,Gd()?Reflect.construct(t,r||[],Hi(e).constructor):t.apply(e,r))}function kO(e,t){if(t&&(Tr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return DO(e)}function DO(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Gd(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Gd=function(){return!!e})()}function Hi(e){return Hi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Hi(e)}function NO(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_u(e,t)}function _u(e,t){return _u=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},_u(e,t)}function In(e,t,r){return t=Vd(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Vd(e){var t=RO(e,"string");return Tr(t)=="symbol"?t:t+""}function RO(e,t){if(Tr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Tr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Ye=32,rl=function(e){function t(){return MO(this,t),CO(this,t,arguments)}return NO(t,e),IO(t,[{key:"renderIcon",value:function(n){var i=this.props.inactiveColor,a=Ye/2,o=Ye/6,u=Ye/3,c=n.inactive?i:n.color;if(n.type==="plainline")return S.createElement("line",{strokeWidth:4,fill:"none",stroke:c,strokeDasharray:n.payload.strokeDasharray,x1:0,y1:a,x2:Ye,y2:a,className:"recharts-legend-icon"});if(n.type==="line")return S.createElement("path",{strokeWidth:4,fill:"none",stroke:c,d:"M0,".concat(a,"h").concat(u,`
            A`).concat(o,",").concat(o,",0,1,1,").concat(2*u,",").concat(a,`
            H`).concat(Ye,"M").concat(2*u,",").concat(a,`
            A`).concat(o,",").concat(o,",0,1,1,").concat(u,",").concat(a),className:"recharts-legend-icon"});if(n.type==="rect")return S.createElement("path",{stroke:"none",fill:c,d:"M0,".concat(Ye/8,"h").concat(Ye,"v").concat(Ye*3/4,"h").concat(-Ye,"z"),className:"recharts-legend-icon"});if(S.isValidElement(n.legendIcon)){var l=jO({},n);return delete l.legendIcon,S.cloneElement(n.legendIcon,l)}return S.createElement(tl,{fill:c,cx:a,cy:a,size:Ye,sizeType:"diameter",type:n.type})}},{key:"renderItems",value:function(){var n=this,i=this.props,a=i.payload,o=i.iconSize,u=i.layout,c=i.formatter,l=i.inactiveColor,f={x:0,y:0,width:Ye,height:Ye},s={display:u==="horizontal"?"inline-block":"block",marginRight:10},p={display:"inline-block",verticalAlign:"middle",marginRight:4};return a.map(function(h,v){var d=h.formatter||c,y=J(In(In({"recharts-legend-item":!0},"legend-item-".concat(v),!0),"inactive",h.inactive));if(h.type==="none")return null;var b=X(h.value)?null:h.value;ot(!X(h.value),`The name property is also required when using a function for the dataKey of a chart's cartesian components. Ex: <Bar name="Name of my Data"/>`);var w=h.inactive?l:h.color;return S.createElement("li",Pu({className:y,style:s,key:"legend-item-".concat(v)},nr(n.props,h,v)),S.createElement(yu,{width:o,height:o,viewBox:f,style:p},n.renderIcon(h)),S.createElement("span",{className:"recharts-legend-item-text",style:{color:w}},d?d(b,h,v):b))})}},{key:"render",value:function(){var n=this.props,i=n.payload,a=n.layout,o=n.align;if(!i||!i.length)return null;var u={padding:0,margin:0,textAlign:a==="horizontal"?o:"left"};return S.createElement("ul",{className:"recharts-default-legend",style:u},this.renderItems())}}])}(R.PureComponent);In(rl,"displayName","Legend");In(rl,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var LO=Wa;function BO(){this.__data__=new LO,this.size=0}var FO=BO;function WO(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r}var zO=WO;function UO(e){return this.__data__.get(e)}var qO=UO;function HO(e){return this.__data__.has(e)}var KO=HO,GO=Wa,VO=Uc,XO=qc,YO=200;function ZO(e,t){var r=this.__data__;if(r instanceof GO){var n=r.__data__;if(!VO||n.length<YO-1)return n.push([e,t]),this.size=++r.size,this;r=this.__data__=new XO(n)}return r.set(e,t),this.size=r.size,this}var JO=ZO,QO=Wa,e1=FO,t1=zO,r1=qO,n1=KO,i1=JO;function an(e){var t=this.__data__=new QO(e);this.size=t.size}an.prototype.clear=e1;an.prototype.delete=t1;an.prototype.get=r1;an.prototype.has=n1;an.prototype.set=i1;var Xd=an,a1="__lodash_hash_undefined__";function o1(e){return this.__data__.set(e,a1),this}var u1=o1;function c1(e){return this.__data__.has(e)}var l1=c1,s1=qc,f1=u1,p1=l1;function Ki(e){var t=-1,r=e==null?0:e.length;for(this.__data__=new s1;++t<r;)this.add(e[t])}Ki.prototype.add=Ki.prototype.push=f1;Ki.prototype.has=p1;var Yd=Ki;function h1(e,t){for(var r=-1,n=e==null?0:e.length;++r<n;)if(t(e[r],r,e))return!0;return!1}var Zd=h1;function d1(e,t){return e.has(t)}var Jd=d1,v1=Yd,y1=Zd,m1=Jd,g1=1,b1=2;function x1(e,t,r,n,i,a){var o=r&g1,u=e.length,c=t.length;if(u!=c&&!(o&&c>u))return!1;var l=a.get(e),f=a.get(t);if(l&&f)return l==t&&f==e;var s=-1,p=!0,h=r&b1?new v1:void 0;for(a.set(e,t),a.set(t,e);++s<u;){var v=e[s],d=t[s];if(n)var y=o?n(d,v,s,t,e,a):n(v,d,s,e,t,a);if(y!==void 0){if(y)continue;p=!1;break}if(h){if(!y1(t,function(b,w){if(!m1(h,w)&&(v===b||i(v,b,r,n,a)))return h.push(w)})){p=!1;break}}else if(!(v===d||i(v,d,r,n,a))){p=!1;break}}return a.delete(e),a.delete(t),p}var Qd=x1,w1=ht,O1=w1.Uint8Array,A1=O1;function S1(e){var t=-1,r=Array(e.size);return e.forEach(function(n,i){r[++t]=[i,n]}),r}var P1=S1;function _1(e){var t=-1,r=Array(e.size);return e.forEach(function(n){r[++t]=n}),r}var nl=_1,xs=pi,ws=A1,$1=zc,T1=Qd,E1=P1,j1=nl,M1=1,I1=2,C1="[object Boolean]",k1="[object Date]",D1="[object Error]",N1="[object Map]",R1="[object Number]",L1="[object RegExp]",B1="[object Set]",F1="[object String]",W1="[object Symbol]",z1="[object ArrayBuffer]",U1="[object DataView]",Os=xs?xs.prototype:void 0,zo=Os?Os.valueOf:void 0;function q1(e,t,r,n,i,a,o){switch(r){case U1:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case z1:return!(e.byteLength!=t.byteLength||!a(new ws(e),new ws(t)));case C1:case k1:case R1:return $1(+e,+t);case D1:return e.name==t.name&&e.message==t.message;case L1:case F1:return e==t+"";case N1:var u=E1;case B1:var c=n&M1;if(u||(u=j1),e.size!=t.size&&!c)return!1;var l=o.get(e);if(l)return l==t;n|=I1,o.set(e,t);var f=T1(u(e),u(t),n,i,a,o);return o.delete(e),f;case W1:if(zo)return zo.call(e)==zo.call(t)}return!1}var H1=q1;function K1(e,t){for(var r=-1,n=t.length,i=e.length;++r<n;)e[i+r]=t[r];return e}var ev=K1,G1=ev,V1=Be;function X1(e,t,r){var n=t(e);return V1(e)?n:G1(n,r(e))}var Y1=X1;function Z1(e,t){for(var r=-1,n=e==null?0:e.length,i=0,a=[];++r<n;){var o=e[r];t(o,r,e)&&(a[i++]=o)}return a}var J1=Z1;function Q1(){return[]}var eA=Q1,tA=J1,rA=eA,nA=Object.prototype,iA=nA.propertyIsEnumerable,As=Object.getOwnPropertySymbols,aA=As?function(e){return e==null?[]:(e=Object(e),tA(As(e),function(t){return iA.call(e,t)}))}:rA,oA=aA;function uA(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}var cA=uA,lA=$t,sA=Tt,fA="[object Arguments]";function pA(e){return sA(e)&&lA(e)==fA}var hA=pA,Ss=hA,dA=Tt,tv=Object.prototype,vA=tv.hasOwnProperty,yA=tv.propertyIsEnumerable,mA=Ss(function(){return arguments}())?Ss:function(e){return dA(e)&&vA.call(e,"callee")&&!yA.call(e,"callee")},il=mA,Gi={exports:{}};function gA(){return!1}var bA=gA;Gi.exports;(function(e,t){var r=ht,n=bA,i=t&&!t.nodeType&&t,a=i&&!0&&e&&!e.nodeType&&e,o=a&&a.exports===i,u=o?r.Buffer:void 0,c=u?u.isBuffer:void 0,l=c||n;e.exports=l})(Gi,Gi.exports);var rv=Gi.exports,xA=9007199254740991,wA=/^(?:0|[1-9]\d*)$/;function OA(e,t){var r=typeof e;return t=t??xA,!!t&&(r=="number"||r!="symbol"&&wA.test(e))&&e>-1&&e%1==0&&e<t}var al=OA,AA=9007199254740991;function SA(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=AA}var ol=SA,PA=$t,_A=ol,$A=Tt,TA="[object Arguments]",EA="[object Array]",jA="[object Boolean]",MA="[object Date]",IA="[object Error]",CA="[object Function]",kA="[object Map]",DA="[object Number]",NA="[object Object]",RA="[object RegExp]",LA="[object Set]",BA="[object String]",FA="[object WeakMap]",WA="[object ArrayBuffer]",zA="[object DataView]",UA="[object Float32Array]",qA="[object Float64Array]",HA="[object Int8Array]",KA="[object Int16Array]",GA="[object Int32Array]",VA="[object Uint8Array]",XA="[object Uint8ClampedArray]",YA="[object Uint16Array]",ZA="[object Uint32Array]",pe={};pe[UA]=pe[qA]=pe[HA]=pe[KA]=pe[GA]=pe[VA]=pe[XA]=pe[YA]=pe[ZA]=!0;pe[TA]=pe[EA]=pe[WA]=pe[jA]=pe[zA]=pe[MA]=pe[IA]=pe[CA]=pe[kA]=pe[DA]=pe[NA]=pe[RA]=pe[LA]=pe[BA]=pe[FA]=!1;function JA(e){return $A(e)&&_A(e.length)&&!!pe[PA(e)]}var QA=JA;function eS(e){return function(t){return e(t)}}var nv=eS,Vi={exports:{}};Vi.exports;(function(e,t){var r=fd,n=t&&!t.nodeType&&t,i=n&&!0&&e&&!e.nodeType&&e,a=i&&i.exports===n,o=a&&r.process,u=function(){try{var c=i&&i.require&&i.require("util").types;return c||o&&o.binding&&o.binding("util")}catch{}}();e.exports=u})(Vi,Vi.exports);var tS=Vi.exports,rS=QA,nS=nv,Ps=tS,_s=Ps&&Ps.isTypedArray,iS=_s?nS(_s):rS,iv=iS,aS=cA,oS=il,uS=Be,cS=rv,lS=al,sS=iv,fS=Object.prototype,pS=fS.hasOwnProperty;function hS(e,t){var r=uS(e),n=!r&&oS(e),i=!r&&!n&&cS(e),a=!r&&!n&&!i&&sS(e),o=r||n||i||a,u=o?aS(e.length,String):[],c=u.length;for(var l in e)(t||pS.call(e,l))&&!(o&&(l=="length"||i&&(l=="offset"||l=="parent")||a&&(l=="buffer"||l=="byteLength"||l=="byteOffset")||lS(l,c)))&&u.push(l);return u}var dS=hS,vS=Object.prototype;function yS(e){var t=e&&e.constructor,r=typeof t=="function"&&t.prototype||vS;return e===r}var mS=yS;function gS(e,t){return function(r){return e(t(r))}}var av=gS,bS=av,xS=bS(Object.keys,Object),wS=xS,OS=mS,AS=wS,SS=Object.prototype,PS=SS.hasOwnProperty;function _S(e){if(!OS(e))return AS(e);var t=[];for(var r in Object(e))PS.call(e,r)&&r!="constructor"&&t.push(r);return t}var $S=_S,TS=Wc,ES=ol;function jS(e){return e!=null&&ES(e.length)&&!TS(e)}var di=jS,MS=dS,IS=$S,CS=di;function kS(e){return CS(e)?MS(e):IS(e)}var io=kS,DS=Y1,NS=oA,RS=io;function LS(e){return DS(e,RS,NS)}var BS=LS,$s=BS,FS=1,WS=Object.prototype,zS=WS.hasOwnProperty;function US(e,t,r,n,i,a){var o=r&FS,u=$s(e),c=u.length,l=$s(t),f=l.length;if(c!=f&&!o)return!1;for(var s=c;s--;){var p=u[s];if(!(o?p in t:zS.call(t,p)))return!1}var h=a.get(e),v=a.get(t);if(h&&v)return h==t&&v==e;var d=!0;a.set(e,t),a.set(t,e);for(var y=o;++s<c;){p=u[s];var b=e[p],w=t[p];if(n)var x=o?n(w,b,p,t,e,a):n(b,w,p,e,t,a);if(!(x===void 0?b===w||i(b,w,r,n,a):x)){d=!1;break}y||(y=p=="constructor")}if(d&&!y){var A=e.constructor,m=t.constructor;A!=m&&"constructor"in e&&"constructor"in t&&!(typeof A=="function"&&A instanceof A&&typeof m=="function"&&m instanceof m)&&(d=!1)}return a.delete(e),a.delete(t),d}var qS=US,HS=lr,KS=ht,GS=HS(KS,"DataView"),VS=GS,XS=lr,YS=ht,ZS=XS(YS,"Promise"),JS=ZS,QS=lr,eP=ht,tP=QS(eP,"Set"),ov=tP,rP=lr,nP=ht,iP=rP(nP,"WeakMap"),aP=iP,$u=VS,Tu=Uc,Eu=JS,ju=ov,Mu=aP,uv=$t,on=hd,Ts="[object Map]",oP="[object Object]",Es="[object Promise]",js="[object Set]",Ms="[object WeakMap]",Is="[object DataView]",uP=on($u),cP=on(Tu),lP=on(Eu),sP=on(ju),fP=on(Mu),Gt=uv;($u&&Gt(new $u(new ArrayBuffer(1)))!=Is||Tu&&Gt(new Tu)!=Ts||Eu&&Gt(Eu.resolve())!=Es||ju&&Gt(new ju)!=js||Mu&&Gt(new Mu)!=Ms)&&(Gt=function(e){var t=uv(e),r=t==oP?e.constructor:void 0,n=r?on(r):"";if(n)switch(n){case uP:return Is;case cP:return Ts;case lP:return Es;case sP:return js;case fP:return Ms}return t});var pP=Gt,Uo=Xd,hP=Qd,dP=H1,vP=qS,Cs=pP,ks=Be,Ds=rv,yP=iv,mP=1,Ns="[object Arguments]",Rs="[object Array]",Si="[object Object]",gP=Object.prototype,Ls=gP.hasOwnProperty;function bP(e,t,r,n,i,a){var o=ks(e),u=ks(t),c=o?Rs:Cs(e),l=u?Rs:Cs(t);c=c==Ns?Si:c,l=l==Ns?Si:l;var f=c==Si,s=l==Si,p=c==l;if(p&&Ds(e)){if(!Ds(t))return!1;o=!0,f=!1}if(p&&!f)return a||(a=new Uo),o||yP(e)?hP(e,t,r,n,i,a):dP(e,t,c,r,n,i,a);if(!(r&mP)){var h=f&&Ls.call(e,"__wrapped__"),v=s&&Ls.call(t,"__wrapped__");if(h||v){var d=h?e.value():e,y=v?t.value():t;return a||(a=new Uo),i(d,y,r,n,a)}}return p?(a||(a=new Uo),vP(e,t,r,n,i,a)):!1}var xP=bP,wP=xP,Bs=Tt;function cv(e,t,r,n,i){return e===t?!0:e==null||t==null||!Bs(e)&&!Bs(t)?e!==e&&t!==t:wP(e,t,r,n,cv,i)}var ul=cv,OP=Xd,AP=ul,SP=1,PP=2;function _P(e,t,r,n){var i=r.length,a=i,o=!n;if(e==null)return!a;for(e=Object(e);i--;){var u=r[i];if(o&&u[2]?u[1]!==e[u[0]]:!(u[0]in e))return!1}for(;++i<a;){u=r[i];var c=u[0],l=e[c],f=u[1];if(o&&u[2]){if(l===void 0&&!(c in e))return!1}else{var s=new OP;if(n)var p=n(l,f,c,e,t,s);if(!(p===void 0?AP(f,l,SP|PP,n,s):p))return!1}}return!0}var $P=_P,TP=Dt;function EP(e){return e===e&&!TP(e)}var lv=EP,jP=lv,MP=io;function IP(e){for(var t=MP(e),r=t.length;r--;){var n=t[r],i=e[n];t[r]=[n,i,jP(i)]}return t}var CP=IP;function kP(e,t){return function(r){return r==null?!1:r[e]===t&&(t!==void 0||e in Object(r))}}var sv=kP,DP=$P,NP=CP,RP=sv;function LP(e){var t=NP(e);return t.length==1&&t[0][2]?RP(t[0][0],t[0][1]):function(r){return r===e||DP(r,e,t)}}var BP=LP;function FP(e,t){return e!=null&&t in Object(e)}var WP=FP,zP=gd,UP=il,qP=Be,HP=al,KP=ol,GP=Ua;function VP(e,t,r){t=zP(t,e);for(var n=-1,i=t.length,a=!1;++n<i;){var o=GP(t[n]);if(!(a=e!=null&&r(e,o)))break;e=e[o]}return a||++n!=i?a:(i=e==null?0:e.length,!!i&&KP(i)&&HP(o,i)&&(qP(e)||UP(e)))}var XP=VP,YP=WP,ZP=XP;function JP(e,t){return e!=null&&ZP(e,t,YP)}var QP=JP,e_=ul,t_=bd,r_=QP,n_=Fc,i_=lv,a_=sv,o_=Ua,u_=1,c_=2;function l_(e,t){return n_(e)&&i_(t)?a_(o_(e),t):function(r){var n=t_(r,e);return n===void 0&&n===t?r_(r,e):e_(t,n,u_|c_)}}var s_=l_;function f_(e){return e}var un=f_;function p_(e){return function(t){return t==null?void 0:t[e]}}var h_=p_,d_=Gc;function v_(e){return function(t){return d_(t,e)}}var y_=v_,m_=h_,g_=y_,b_=Fc,x_=Ua;function w_(e){return b_(e)?m_(x_(e)):g_(e)}var O_=w_,A_=BP,S_=s_,P_=un,__=Be,$_=O_;function T_(e){return typeof e=="function"?e:e==null?P_:typeof e=="object"?__(e)?S_(e[0],e[1]):A_(e):$_(e)}var dt=T_;function E_(e,t,r,n){for(var i=e.length,a=r+(n?1:-1);n?a--:++a<i;)if(t(e[a],a,e))return a;return-1}var fv=E_;function j_(e){return e!==e}var M_=j_;function I_(e,t,r){for(var n=r-1,i=e.length;++n<i;)if(e[n]===t)return n;return-1}var C_=I_,k_=fv,D_=M_,N_=C_;function R_(e,t,r){return t===t?N_(e,t,r):k_(e,D_,r)}var L_=R_,B_=L_;function F_(e,t){var r=e==null?0:e.length;return!!r&&B_(e,t,0)>-1}var W_=F_;function z_(e,t,r){for(var n=-1,i=e==null?0:e.length;++n<i;)if(r(t,e[n]))return!0;return!1}var U_=z_;function q_(){}var H_=q_,qo=ov,K_=H_,G_=nl,V_=1/0,X_=qo&&1/G_(new qo([,-0]))[1]==V_?function(e){return new qo(e)}:K_,Y_=X_,Z_=Yd,J_=W_,Q_=U_,e$=Jd,t$=Y_,r$=nl,n$=200;function i$(e,t,r){var n=-1,i=J_,a=e.length,o=!0,u=[],c=u;if(r)o=!1,i=Q_;else if(a>=n$){var l=t?null:t$(e);if(l)return r$(l);o=!1,i=e$,c=new Z_}else c=t?[]:u;e:for(;++n<a;){var f=e[n],s=t?t(f):f;if(f=r||f!==0?f:0,o&&s===s){for(var p=c.length;p--;)if(c[p]===s)continue e;t&&c.push(s),u.push(f)}else i(c,s,r)||(c!==u&&c.push(s),u.push(f))}return u}var a$=i$,o$=dt,u$=a$;function c$(e,t){return e&&e.length?u$(e,o$(t)):[]}var l$=c$;const Fs=oe(l$);function pv(e,t,r){return t===!0?Fs(e,r):X(t)?Fs(e,t):e}function Er(e){"@babel/helpers - typeof";return Er=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Er(e)}var s$=["ref"];function Ws(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function vt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ws(Object(r),!0).forEach(function(n){ao(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ws(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function f$(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function zs(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,dv(n.key),n)}}function p$(e,t,r){return t&&zs(e.prototype,t),r&&zs(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function h$(e,t,r){return t=Xi(t),d$(e,hv()?Reflect.construct(t,r||[],Xi(e).constructor):t.apply(e,r))}function d$(e,t){if(t&&(Er(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return v$(e)}function v$(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function hv(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(hv=function(){return!!e})()}function Xi(e){return Xi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Xi(e)}function y$(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Iu(e,t)}function Iu(e,t){return Iu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Iu(e,t)}function ao(e,t,r){return t=dv(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function dv(e){var t=m$(e,"string");return Er(t)=="symbol"?t:t+""}function m$(e,t){if(Er(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Er(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function g$(e,t){if(e==null)return{};var r=b$(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function b$(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function x$(e){return e.value}function w$(e,t){if(S.isValidElement(e))return S.cloneElement(e,t);if(typeof e=="function")return S.createElement(e,t);t.ref;var r=g$(t,s$);return S.createElement(rl,r)}var Us=1,Sr=function(e){function t(){var r;f$(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=h$(this,t,[].concat(i)),ao(r,"lastBoundingBox",{width:-1,height:-1}),r}return y$(t,e),p$(t,[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var n=this.wrapperNode.getBoundingClientRect();return n.height=this.wrapperNode.offsetHeight,n.width=this.wrapperNode.offsetWidth,n}return null}},{key:"updateBBox",value:function(){var n=this.props.onBBoxUpdate,i=this.getBBox();i?(Math.abs(i.width-this.lastBoundingBox.width)>Us||Math.abs(i.height-this.lastBoundingBox.height)>Us)&&(this.lastBoundingBox.width=i.width,this.lastBoundingBox.height=i.height,n&&n(i)):(this.lastBoundingBox.width!==-1||this.lastBoundingBox.height!==-1)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,n&&n(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?vt({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(n){var i=this.props,a=i.layout,o=i.align,u=i.verticalAlign,c=i.margin,l=i.chartWidth,f=i.chartHeight,s,p;if(!n||(n.left===void 0||n.left===null)&&(n.right===void 0||n.right===null))if(o==="center"&&a==="vertical"){var h=this.getBBoxSnapshot();s={left:((l||0)-h.width)/2}}else s=o==="right"?{right:c&&c.right||0}:{left:c&&c.left||0};if(!n||(n.top===void 0||n.top===null)&&(n.bottom===void 0||n.bottom===null))if(u==="middle"){var v=this.getBBoxSnapshot();p={top:((f||0)-v.height)/2}}else p=u==="bottom"?{bottom:c&&c.bottom||0}:{top:c&&c.top||0};return vt(vt({},s),p)}},{key:"render",value:function(){var n=this,i=this.props,a=i.content,o=i.width,u=i.height,c=i.wrapperStyle,l=i.payloadUniqBy,f=i.payload,s=vt(vt({position:"absolute",width:o||"auto",height:u||"auto"},this.getDefaultPosition(c)),c);return S.createElement("div",{className:"recharts-legend-wrapper",style:s,ref:function(h){n.wrapperNode=h}},w$(a,vt(vt({},this.props),{},{payload:pv(f,l,x$)})))}}],[{key:"getWithHeight",value:function(n,i){var a=vt(vt({},this.defaultProps),n.props),o=a.layout;return o==="vertical"&&L(n.props.height)?{height:n.props.height}:o==="horizontal"?{width:n.props.width||i}:null}}])}(R.PureComponent);ao(Sr,"displayName","Legend");ao(Sr,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var qs=pi,O$=il,A$=Be,Hs=qs?qs.isConcatSpreadable:void 0;function S$(e){return A$(e)||O$(e)||!!(Hs&&e&&e[Hs])}var P$=S$,_$=ev,$$=P$;function vv(e,t,r,n,i){var a=-1,o=e.length;for(r||(r=$$),i||(i=[]);++a<o;){var u=e[a];t>0&&r(u)?t>1?vv(u,t-1,r,n,i):_$(i,u):n||(i[i.length]=u)}return i}var yv=vv;function T$(e){return function(t,r,n){for(var i=-1,a=Object(t),o=n(t),u=o.length;u--;){var c=o[e?u:++i];if(r(a[c],c,a)===!1)break}return t}}var E$=T$,j$=E$,M$=j$(),I$=M$,C$=I$,k$=io;function D$(e,t){return e&&C$(e,t,k$)}var mv=D$,N$=di;function R$(e,t){return function(r,n){if(r==null)return r;if(!N$(r))return e(r,n);for(var i=r.length,a=t?i:-1,o=Object(r);(t?a--:++a<i)&&n(o[a],a,o)!==!1;);return r}}var L$=R$,B$=mv,F$=L$,W$=F$(B$),cl=W$,z$=cl,U$=di;function q$(e,t){var r=-1,n=U$(e)?Array(e.length):[];return z$(e,function(i,a,o){n[++r]=t(i,a,o)}),n}var gv=q$;function H$(e,t){var r=e.length;for(e.sort(t);r--;)e[r]=e[r].value;return e}var K$=H$,Ks=Jr;function G$(e,t){if(e!==t){var r=e!==void 0,n=e===null,i=e===e,a=Ks(e),o=t!==void 0,u=t===null,c=t===t,l=Ks(t);if(!u&&!l&&!a&&e>t||a&&o&&c&&!u&&!l||n&&o&&c||!r&&c||!i)return 1;if(!n&&!a&&!l&&e<t||l&&r&&i&&!n&&!a||u&&r&&i||!o&&i||!c)return-1}return 0}var V$=G$,X$=V$;function Y$(e,t,r){for(var n=-1,i=e.criteria,a=t.criteria,o=i.length,u=r.length;++n<o;){var c=X$(i[n],a[n]);if(c){if(n>=u)return c;var l=r[n];return c*(l=="desc"?-1:1)}}return e.index-t.index}var Z$=Y$,Ho=Kc,J$=Gc,Q$=dt,eT=gv,tT=K$,rT=nv,nT=Z$,iT=un,aT=Be;function oT(e,t,r){t.length?t=Ho(t,function(a){return aT(a)?function(o){return J$(o,a.length===1?a[0]:a)}:a}):t=[iT];var n=-1;t=Ho(t,rT(Q$));var i=eT(e,function(a,o,u){var c=Ho(t,function(l){return l(a)});return{criteria:c,index:++n,value:a}});return tT(i,function(a,o){return nT(a,o,r)})}var uT=oT;function cT(e,t,r){switch(r.length){case 0:return e.call(t);case 1:return e.call(t,r[0]);case 2:return e.call(t,r[0],r[1]);case 3:return e.call(t,r[0],r[1],r[2])}return e.apply(t,r)}var lT=cT,sT=lT,Gs=Math.max;function fT(e,t,r){return t=Gs(t===void 0?e.length-1:t,0),function(){for(var n=arguments,i=-1,a=Gs(n.length-t,0),o=Array(a);++i<a;)o[i]=n[t+i];i=-1;for(var u=Array(t+1);++i<t;)u[i]=n[i];return u[t]=r(o),sT(e,this,u)}}var pT=fT;function hT(e){return function(){return e}}var dT=hT,vT=lr,yT=function(){try{var e=vT(Object,"defineProperty");return e({},"",{}),e}catch{}}(),bv=yT,mT=dT,Vs=bv,gT=un,bT=Vs?function(e,t){return Vs(e,"toString",{configurable:!0,enumerable:!1,value:mT(t),writable:!0})}:gT,xT=bT,wT=800,OT=16,AT=Date.now;function ST(e){var t=0,r=0;return function(){var n=AT(),i=OT-(n-r);if(r=n,i>0){if(++t>=wT)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}var PT=ST,_T=xT,$T=PT,TT=$T(_T),ET=TT,jT=un,MT=pT,IT=ET;function CT(e,t){return IT(MT(e,t,jT),e+"")}var kT=CT,DT=zc,NT=di,RT=al,LT=Dt;function BT(e,t,r){if(!LT(r))return!1;var n=typeof t;return(n=="number"?NT(r)&&RT(t,r.length):n=="string"&&t in r)?DT(r[t],e):!1}var oo=BT,FT=yv,WT=uT,zT=kT,Xs=oo,UT=zT(function(e,t){if(e==null)return[];var r=t.length;return r>1&&Xs(e,t[0],t[1])?t=[]:r>2&&Xs(t[0],t[1],t[2])&&(t=[t[0]]),WT(e,FT(t,1),[])}),qT=UT;const ll=oe(qT);function Cn(e){"@babel/helpers - typeof";return Cn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Cn(e)}function Cu(){return Cu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Cu.apply(this,arguments)}function HT(e,t){return XT(e)||VT(e,t)||GT(e,t)||KT()}function KT(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function GT(e,t){if(e){if(typeof e=="string")return Ys(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ys(e,t)}}function Ys(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function VT(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){l=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return u}}function XT(e){if(Array.isArray(e))return e}function Zs(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ko(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Zs(Object(r),!0).forEach(function(n){YT(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Zs(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function YT(e,t,r){return t=ZT(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ZT(e){var t=JT(e,"string");return Cn(t)=="symbol"?t:t+""}function JT(e,t){if(Cn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Cn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function QT(e){return Array.isArray(e)&&Pe(e[0])&&Pe(e[1])?e.join(" ~ "):e}var eE=function(t){var r=t.separator,n=r===void 0?" : ":r,i=t.contentStyle,a=i===void 0?{}:i,o=t.itemStyle,u=o===void 0?{}:o,c=t.labelStyle,l=c===void 0?{}:c,f=t.payload,s=t.formatter,p=t.itemSorter,h=t.wrapperClassName,v=t.labelClassName,d=t.label,y=t.labelFormatter,b=t.accessibilityLayer,w=b===void 0?!1:b,x=function(){if(f&&f.length){var T={padding:0,margin:0},I=(p?ll(f,p):f).map(function(C,M){if(C.type==="none")return null;var k=Ko({display:"block",paddingTop:4,paddingBottom:4,color:C.color||"#000"},u),D=C.formatter||s||QT,B=C.value,F=C.name,q=B,G=F;if(D&&q!=null&&G!=null){var z=D(B,F,C,M,f);if(Array.isArray(z)){var V=HT(z,2);q=V[0],G=V[1]}else q=z}return S.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(M),style:k},Pe(G)?S.createElement("span",{className:"recharts-tooltip-item-name"},G):null,Pe(G)?S.createElement("span",{className:"recharts-tooltip-item-separator"},n):null,S.createElement("span",{className:"recharts-tooltip-item-value"},q),S.createElement("span",{className:"recharts-tooltip-item-unit"},C.unit||""))});return S.createElement("ul",{className:"recharts-tooltip-item-list",style:T},I)}return null},A=Ko({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},a),m=Ko({margin:0},l),g=!Y(d),O=g?d:"",P=J("recharts-default-tooltip",h),_=J("recharts-tooltip-label",v);g&&y&&f!==void 0&&f!==null&&(O=y(d,f));var E=w?{role:"status","aria-live":"assertive"}:{};return S.createElement("div",Cu({className:P,style:A},E),S.createElement("p",{className:_,style:m},S.isValidElement(O)?O:"".concat(O)),x())};function kn(e){"@babel/helpers - typeof";return kn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},kn(e)}function Pi(e,t,r){return t=tE(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function tE(e){var t=rE(e,"string");return kn(t)=="symbol"?t:t+""}function rE(e,t){if(kn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(kn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var dn="recharts-tooltip-wrapper",nE={visibility:"hidden"};function iE(e){var t=e.coordinate,r=e.translateX,n=e.translateY;return J(dn,Pi(Pi(Pi(Pi({},"".concat(dn,"-right"),L(r)&&t&&L(t.x)&&r>=t.x),"".concat(dn,"-left"),L(r)&&t&&L(t.x)&&r<t.x),"".concat(dn,"-bottom"),L(n)&&t&&L(t.y)&&n>=t.y),"".concat(dn,"-top"),L(n)&&t&&L(t.y)&&n<t.y))}function Js(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.key,i=e.offsetTopLeft,a=e.position,o=e.reverseDirection,u=e.tooltipDimension,c=e.viewBox,l=e.viewBoxDimension;if(a&&L(a[n]))return a[n];var f=r[n]-u-i,s=r[n]+i;if(t[n])return o[n]?f:s;if(o[n]){var p=f,h=c[n];return p<h?Math.max(s,c[n]):Math.max(f,c[n])}var v=s+u,d=c[n]+l;return v>d?Math.max(f,c[n]):Math.max(s,c[n])}function aE(e){var t=e.translateX,r=e.translateY,n=e.useTranslate3d;return{transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}function oE(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.offsetTopLeft,i=e.position,a=e.reverseDirection,o=e.tooltipBox,u=e.useTranslate3d,c=e.viewBox,l,f,s;return o.height>0&&o.width>0&&r?(f=Js({allowEscapeViewBox:t,coordinate:r,key:"x",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.width,viewBox:c,viewBoxDimension:c.width}),s=Js({allowEscapeViewBox:t,coordinate:r,key:"y",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.height,viewBox:c,viewBoxDimension:c.height}),l=aE({translateX:f,translateY:s,useTranslate3d:u})):l=nE,{cssProperties:l,cssClasses:iE({translateX:f,translateY:s,coordinate:r})}}function jr(e){"@babel/helpers - typeof";return jr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},jr(e)}function Qs(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ef(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Qs(Object(r),!0).forEach(function(n){Du(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Qs(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function uE(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function tf(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,wv(n.key),n)}}function cE(e,t,r){return t&&tf(e.prototype,t),r&&tf(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function lE(e,t,r){return t=Yi(t),sE(e,xv()?Reflect.construct(t,r||[],Yi(e).constructor):t.apply(e,r))}function sE(e,t){if(t&&(jr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return fE(e)}function fE(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function xv(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(xv=function(){return!!e})()}function Yi(e){return Yi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Yi(e)}function pE(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ku(e,t)}function ku(e,t){return ku=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},ku(e,t)}function Du(e,t,r){return t=wv(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function wv(e){var t=hE(e,"string");return jr(t)=="symbol"?t:t+""}function hE(e,t){if(jr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(jr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var rf=1,dE=function(e){function t(){var r;uE(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=lE(this,t,[].concat(i)),Du(r,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),Du(r,"handleKeyDown",function(o){if(o.key==="Escape"){var u,c,l,f;r.setState({dismissed:!0,dismissedAtCoordinate:{x:(u=(c=r.props.coordinate)===null||c===void 0?void 0:c.x)!==null&&u!==void 0?u:0,y:(l=(f=r.props.coordinate)===null||f===void 0?void 0:f.y)!==null&&l!==void 0?l:0}})}}),r}return pE(t,e),cE(t,[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var n=this.wrapperNode.getBoundingClientRect();(Math.abs(n.width-this.state.lastBoundingBox.width)>rf||Math.abs(n.height-this.state.lastBoundingBox.height)>rf)&&this.setState({lastBoundingBox:{width:n.width,height:n.height}})}else(this.state.lastBoundingBox.width!==-1||this.state.lastBoundingBox.height!==-1)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var n,i;this.props.active&&this.updateBBox(),this.state.dismissed&&(((n=this.props.coordinate)===null||n===void 0?void 0:n.x)!==this.state.dismissedAtCoordinate.x||((i=this.props.coordinate)===null||i===void 0?void 0:i.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var n=this,i=this.props,a=i.active,o=i.allowEscapeViewBox,u=i.animationDuration,c=i.animationEasing,l=i.children,f=i.coordinate,s=i.hasPayload,p=i.isAnimationActive,h=i.offset,v=i.position,d=i.reverseDirection,y=i.useTranslate3d,b=i.viewBox,w=i.wrapperStyle,x=oE({allowEscapeViewBox:o,coordinate:f,offsetTopLeft:h,position:v,reverseDirection:d,tooltipBox:this.state.lastBoundingBox,useTranslate3d:y,viewBox:b}),A=x.cssClasses,m=x.cssProperties,g=ef(ef({transition:p&&a?"transform ".concat(u,"ms ").concat(c):void 0},m),{},{pointerEvents:"none",visibility:!this.state.dismissed&&a&&s?"visible":"hidden",position:"absolute",top:0,left:0},w);return S.createElement("div",{tabIndex:-1,className:A,style:g,ref:function(P){n.wrapperNode=P}},l)}}])}(R.PureComponent),vE=function(){return!(typeof window<"u"&&window.document&&window.document.createElement&&window.setTimeout)},lt={isSsr:vE(),get:function(t){return lt[t]},set:function(t,r){if(typeof t=="string")lt[t]=r;else{var n=Object.keys(t);n&&n.length&&n.forEach(function(i){lt[i]=t[i]})}}};function Mr(e){"@babel/helpers - typeof";return Mr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Mr(e)}function nf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function af(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?nf(Object(r),!0).forEach(function(n){sl(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function yE(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function of(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Av(n.key),n)}}function mE(e,t,r){return t&&of(e.prototype,t),r&&of(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function gE(e,t,r){return t=Zi(t),bE(e,Ov()?Reflect.construct(t,r||[],Zi(e).constructor):t.apply(e,r))}function bE(e,t){if(t&&(Mr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return xE(e)}function xE(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Ov(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Ov=function(){return!!e})()}function Zi(e){return Zi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Zi(e)}function wE(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Nu(e,t)}function Nu(e,t){return Nu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Nu(e,t)}function sl(e,t,r){return t=Av(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Av(e){var t=OE(e,"string");return Mr(t)=="symbol"?t:t+""}function OE(e,t){if(Mr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Mr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function AE(e){return e.dataKey}function SE(e,t){return S.isValidElement(e)?S.cloneElement(e,t):typeof e=="function"?S.createElement(e,t):S.createElement(eE,t)}var yt=function(e){function t(){return yE(this,t),gE(this,t,arguments)}return wE(t,e),mE(t,[{key:"render",value:function(){var n=this,i=this.props,a=i.active,o=i.allowEscapeViewBox,u=i.animationDuration,c=i.animationEasing,l=i.content,f=i.coordinate,s=i.filterNull,p=i.isAnimationActive,h=i.offset,v=i.payload,d=i.payloadUniqBy,y=i.position,b=i.reverseDirection,w=i.useTranslate3d,x=i.viewBox,A=i.wrapperStyle,m=v??[];s&&m.length&&(m=pv(v.filter(function(O){return O.value!=null&&(O.hide!==!0||n.props.includeHidden)}),d,AE));var g=m.length>0;return S.createElement(dE,{allowEscapeViewBox:o,animationDuration:u,animationEasing:c,isAnimationActive:p,active:a,coordinate:f,hasPayload:g,offset:h,position:y,reverseDirection:b,useTranslate3d:w,viewBox:x,wrapperStyle:A},SE(l,af(af({},this.props),{},{payload:m})))}}])}(R.PureComponent);sl(yt,"displayName","Tooltip");sl(yt,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!lt.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var PE=ht,_E=function(){return PE.Date.now()},$E=_E,TE=/\s/;function EE(e){for(var t=e.length;t--&&TE.test(e.charAt(t)););return t}var jE=EE,ME=jE,IE=/^\s+/;function CE(e){return e&&e.slice(0,ME(e)+1).replace(IE,"")}var kE=CE,DE=kE,uf=Dt,NE=Jr,cf=0/0,RE=/^[-+]0x[0-9a-f]+$/i,LE=/^0b[01]+$/i,BE=/^0o[0-7]+$/i,FE=parseInt;function WE(e){if(typeof e=="number")return e;if(NE(e))return cf;if(uf(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=uf(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=DE(e);var r=LE.test(e);return r||BE.test(e)?FE(e.slice(2),r?2:8):RE.test(e)?cf:+e}var Sv=WE,zE=Dt,Go=$E,lf=Sv,UE="Expected a function",qE=Math.max,HE=Math.min;function KE(e,t,r){var n,i,a,o,u,c,l=0,f=!1,s=!1,p=!0;if(typeof e!="function")throw new TypeError(UE);t=lf(t)||0,zE(r)&&(f=!!r.leading,s="maxWait"in r,a=s?qE(lf(r.maxWait)||0,t):a,p="trailing"in r?!!r.trailing:p);function h(g){var O=n,P=i;return n=i=void 0,l=g,o=e.apply(P,O),o}function v(g){return l=g,u=setTimeout(b,t),f?h(g):o}function d(g){var O=g-c,P=g-l,_=t-O;return s?HE(_,a-P):_}function y(g){var O=g-c,P=g-l;return c===void 0||O>=t||O<0||s&&P>=a}function b(){var g=Go();if(y(g))return w(g);u=setTimeout(b,d(g))}function w(g){return u=void 0,p&&n?h(g):(n=i=void 0,o)}function x(){u!==void 0&&clearTimeout(u),l=0,n=c=i=u=void 0}function A(){return u===void 0?o:w(Go())}function m(){var g=Go(),O=y(g);if(n=arguments,i=this,c=g,O){if(u===void 0)return v(c);if(s)return clearTimeout(u),u=setTimeout(b,t),h(c)}return u===void 0&&(u=setTimeout(b,t)),o}return m.cancel=x,m.flush=A,m}var GE=KE,VE=GE,XE=Dt,YE="Expected a function";function ZE(e,t,r){var n=!0,i=!0;if(typeof e!="function")throw new TypeError(YE);return XE(r)&&(n="leading"in r?!!r.leading:n,i="trailing"in r?!!r.trailing:i),VE(e,t,{leading:n,maxWait:t,trailing:i})}var JE=ZE;const Pv=oe(JE);function Dn(e){"@babel/helpers - typeof";return Dn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Dn(e)}function sf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function _i(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?sf(Object(r),!0).forEach(function(n){QE(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):sf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function QE(e,t,r){return t=ej(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ej(e){var t=tj(e,"string");return Dn(t)=="symbol"?t:t+""}function tj(e,t){if(Dn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Dn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function rj(e,t){return oj(e)||aj(e,t)||ij(e,t)||nj()}function nj(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ij(e,t){if(e){if(typeof e=="string")return ff(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ff(e,t)}}function ff(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function aj(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){l=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return u}}function oj(e){if(Array.isArray(e))return e}var xz=R.forwardRef(function(e,t){var r=e.aspect,n=e.initialDimension,i=n===void 0?{width:-1,height:-1}:n,a=e.width,o=a===void 0?"100%":a,u=e.height,c=u===void 0?"100%":u,l=e.minWidth,f=l===void 0?0:l,s=e.minHeight,p=e.maxHeight,h=e.children,v=e.debounce,d=v===void 0?0:v,y=e.id,b=e.className,w=e.onResize,x=e.style,A=x===void 0?{}:x,m=R.useRef(null),g=R.useRef();g.current=w,R.useImperativeHandle(t,function(){return Object.defineProperty(m.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),m.current},configurable:!0})});var O=R.useState({containerWidth:i.width,containerHeight:i.height}),P=rj(O,2),_=P[0],E=P[1],$=R.useCallback(function(I,C){E(function(M){var k=Math.round(I),D=Math.round(C);return M.containerWidth===k&&M.containerHeight===D?M:{containerWidth:k,containerHeight:D}})},[]);R.useEffect(function(){var I=function(F){var q,G=F[0].contentRect,z=G.width,V=G.height;$(z,V),(q=g.current)===null||q===void 0||q.call(g,z,V)};d>0&&(I=Pv(I,d,{trailing:!0,leading:!1}));var C=new ResizeObserver(I),M=m.current.getBoundingClientRect(),k=M.width,D=M.height;return $(k,D),C.observe(m.current),function(){C.disconnect()}},[$,d]);var T=R.useMemo(function(){var I=_.containerWidth,C=_.containerHeight;if(I<0||C<0)return null;ot(Yt(o)||Yt(c),`The width(%s) and height(%s) are both fixed numbers,
       maybe you don't need to use a ResponsiveContainer.`,o,c),ot(!r||r>0,"The aspect(%s) must be greater than zero.",r);var M=Yt(o)?I:o,k=Yt(c)?C:c;r&&r>0&&(M?k=M/r:k&&(M=k*r),p&&k>p&&(k=p)),ot(M>0||k>0,`The width(%s) and height(%s) of chart should be greater than 0,
       please check the style of container, or the props width(%s) and height(%s),
       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the
       height and width.`,M,k,o,c,f,s,r);var D=!Array.isArray(h)&&wt(h.type).endsWith("Chart");return S.Children.map(h,function(B){return S.isValidElement(B)?R.cloneElement(B,_i({width:M,height:k},D?{style:_i({height:"100%",width:"100%",maxHeight:k,maxWidth:M},B.props.style)}:{})):B})},[r,h,c,p,s,f,_,o]);return S.createElement("div",{id:y?"".concat(y):void 0,className:J("recharts-responsive-container",b),style:_i(_i({},A),{},{width:o,height:c,minWidth:f,minHeight:s,maxHeight:p}),ref:m},T)}),fl=function(t){return null};fl.displayName="Cell";function Nn(e){"@babel/helpers - typeof";return Nn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Nn(e)}function pf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ru(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?pf(Object(r),!0).forEach(function(n){uj(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function uj(e,t,r){return t=cj(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function cj(e){var t=lj(e,"string");return Nn(t)=="symbol"?t:t+""}function lj(e,t){if(Nn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Nn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var dr={widthCache:{},cacheCount:0},sj=2e3,fj={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},hf="recharts_measurement_span";function pj(e){var t=Ru({},e);return Object.keys(t).forEach(function(r){t[r]||delete t[r]}),t}var Sn=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(t==null||lt.isSsr)return{width:0,height:0};var n=pj(r),i=JSON.stringify({text:t,copyStyle:n});if(dr.widthCache[i])return dr.widthCache[i];try{var a=document.getElementById(hf);a||(a=document.createElement("span"),a.setAttribute("id",hf),a.setAttribute("aria-hidden","true"),document.body.appendChild(a));var o=Ru(Ru({},fj),n);Object.assign(a.style,o),a.textContent="".concat(t);var u=a.getBoundingClientRect(),c={width:u.width,height:u.height};return dr.widthCache[i]=c,++dr.cacheCount>sj&&(dr.cacheCount=0,dr.widthCache={}),c}catch{return{width:0,height:0}}},hj=function(t){return{top:t.top+window.scrollY-document.documentElement.clientTop,left:t.left+window.scrollX-document.documentElement.clientLeft}};function Rn(e){"@babel/helpers - typeof";return Rn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Rn(e)}function Ji(e,t){return mj(e)||yj(e,t)||vj(e,t)||dj()}function dj(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function vj(e,t){if(e){if(typeof e=="string")return df(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return df(e,t)}}function df(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function yj(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){l=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return u}}function mj(e){if(Array.isArray(e))return e}function gj(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function vf(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,xj(n.key),n)}}function bj(e,t,r){return t&&vf(e.prototype,t),r&&vf(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function xj(e){var t=wj(e,"string");return Rn(t)=="symbol"?t:t+""}function wj(e,t){if(Rn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Rn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var yf=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,mf=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,Oj=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,Aj=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,_v={cm:96/2.54,mm:96/25.4,pt:96/72,pc:96/6,in:96,Q:96/(2.54*40),px:1},Sj=Object.keys(_v),gr="NaN";function Pj(e,t){return e*_v[t]}var $i=function(){function e(t,r){gj(this,e),this.num=t,this.unit=r,this.num=t,this.unit=r,Number.isNaN(t)&&(this.unit=""),r!==""&&!Oj.test(r)&&(this.num=NaN,this.unit=""),Sj.includes(r)&&(this.num=Pj(t,r),this.unit="px")}return bj(e,[{key:"add",value:function(r){return this.unit!==r.unit?new e(NaN,""):new e(this.num+r.num,this.unit)}},{key:"subtract",value:function(r){return this.unit!==r.unit?new e(NaN,""):new e(this.num-r.num,this.unit)}},{key:"multiply",value:function(r){return this.unit!==""&&r.unit!==""&&this.unit!==r.unit?new e(NaN,""):new e(this.num*r.num,this.unit||r.unit)}},{key:"divide",value:function(r){return this.unit!==""&&r.unit!==""&&this.unit!==r.unit?new e(NaN,""):new e(this.num/r.num,this.unit||r.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],[{key:"parse",value:function(r){var n,i=(n=Aj.exec(r))!==null&&n!==void 0?n:[],a=Ji(i,3),o=a[1],u=a[2];return new e(parseFloat(o),u??"")}}])}();function $v(e){if(e.includes(gr))return gr;for(var t=e;t.includes("*")||t.includes("/");){var r,n=(r=yf.exec(t))!==null&&r!==void 0?r:[],i=Ji(n,4),a=i[1],o=i[2],u=i[3],c=$i.parse(a??""),l=$i.parse(u??""),f=o==="*"?c.multiply(l):c.divide(l);if(f.isNaN())return gr;t=t.replace(yf,f.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var s,p=(s=mf.exec(t))!==null&&s!==void 0?s:[],h=Ji(p,4),v=h[1],d=h[2],y=h[3],b=$i.parse(v??""),w=$i.parse(y??""),x=d==="+"?b.add(w):b.subtract(w);if(x.isNaN())return gr;t=t.replace(mf,x.toString())}return t}var gf=/\(([^()]*)\)/;function _j(e){for(var t=e;t.includes("(");){var r=gf.exec(t),n=Ji(r,2),i=n[1];t=t.replace(gf,$v(i))}return t}function $j(e){var t=e.replace(/\s+/g,"");return t=_j(t),t=$v(t),t}function Tj(e){try{return $j(e)}catch{return gr}}function Vo(e){var t=Tj(e.slice(5,-1));return t===gr?"":t}var Ej=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],jj=["dx","dy","angle","className","breakAll"];function Lu(){return Lu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Lu.apply(this,arguments)}function bf(e,t){if(e==null)return{};var r=Mj(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function Mj(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function xf(e,t){return Dj(e)||kj(e,t)||Cj(e,t)||Ij()}function Ij(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Cj(e,t){if(e){if(typeof e=="string")return wf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return wf(e,t)}}function wf(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function kj(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){l=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return u}}function Dj(e){if(Array.isArray(e))return e}var Tv=/[ \f\n\r\t\v\u2028\u2029]+/,Ev=function(t){var r=t.children,n=t.breakAll,i=t.style;try{var a=[];Y(r)||(n?a=r.toString().split(""):a=r.toString().split(Tv));var o=a.map(function(c){return{word:c,width:Sn(c,i).width}}),u=n?0:Sn(" ",i).width;return{wordsWithComputedWidth:o,spaceWidth:u}}catch{return null}},Nj=function(t,r,n,i,a){var o=t.maxLines,u=t.children,c=t.style,l=t.breakAll,f=L(o),s=u,p=function(){var M=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return M.reduce(function(k,D){var B=D.word,F=D.width,q=k[k.length-1];if(q&&(i==null||a||q.width+F+n<Number(i)))q.words.push(B),q.width+=F+n;else{var G={words:[B],width:F};k.push(G)}return k},[])},h=p(r),v=function(M){return M.reduce(function(k,D){return k.width>D.width?k:D})};if(!f)return h;for(var d="…",y=function(M){var k=s.slice(0,M),D=Ev({breakAll:l,style:c,children:k+d}).wordsWithComputedWidth,B=p(D),F=B.length>o||v(B).width>Number(i);return[F,B]},b=0,w=s.length-1,x=0,A;b<=w&&x<=s.length-1;){var m=Math.floor((b+w)/2),g=m-1,O=y(g),P=xf(O,2),_=P[0],E=P[1],$=y(m),T=xf($,1),I=T[0];if(!_&&!I&&(b=m+1),_&&I&&(w=m-1),!_&&I){A=E;break}x++}return A||h},Of=function(t){var r=Y(t)?[]:t.toString().split(Tv);return[{words:r}]},Rj=function(t){var r=t.width,n=t.scaleToFit,i=t.children,a=t.style,o=t.breakAll,u=t.maxLines;if((r||n)&&!lt.isSsr){var c,l,f=Ev({breakAll:o,children:i,style:a});if(f){var s=f.wordsWithComputedWidth,p=f.spaceWidth;c=s,l=p}else return Of(i);return Nj({breakAll:o,children:i,maxLines:u,style:a},c,l,r,n)}return Of(i)},Af="#808080",ir=function(t){var r=t.x,n=r===void 0?0:r,i=t.y,a=i===void 0?0:i,o=t.lineHeight,u=o===void 0?"1em":o,c=t.capHeight,l=c===void 0?"0.71em":c,f=t.scaleToFit,s=f===void 0?!1:f,p=t.textAnchor,h=p===void 0?"start":p,v=t.verticalAnchor,d=v===void 0?"end":v,y=t.fill,b=y===void 0?Af:y,w=bf(t,Ej),x=R.useMemo(function(){return Rj({breakAll:w.breakAll,children:w.children,maxLines:w.maxLines,scaleToFit:s,style:w.style,width:w.width})},[w.breakAll,w.children,w.maxLines,s,w.style,w.width]),A=w.dx,m=w.dy,g=w.angle,O=w.className,P=w.breakAll,_=bf(w,jj);if(!Pe(n)||!Pe(a))return null;var E=n+(L(A)?A:0),$=a+(L(m)?m:0),T;switch(d){case"start":T=Vo("calc(".concat(l,")"));break;case"middle":T=Vo("calc(".concat((x.length-1)/2," * -").concat(u," + (").concat(l," / 2))"));break;default:T=Vo("calc(".concat(x.length-1," * -").concat(u,")"));break}var I=[];if(s){var C=x[0].width,M=w.width;I.push("scale(".concat((L(M)?M/C:1)/C,")"))}return g&&I.push("rotate(".concat(g,", ").concat(E,", ").concat($,")")),I.length&&(_.transform=I.join(" ")),S.createElement("text",Lu({},H(_,!0),{x:E,y:$,className:J("recharts-text",O),textAnchor:h,fill:b.includes("url")?Af:b}),x.map(function(k,D){var B=k.words.join(P?"":" ");return S.createElement("tspan",{x:E,dy:D===0?T:u,key:"".concat(B,"-").concat(D)},B)}))};function kt(e,t){return e==null||t==null?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function Lj(e,t){return e==null||t==null?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function pl(e){let t,r,n;e.length!==2?(t=kt,r=(u,c)=>kt(e(u),c),n=(u,c)=>e(u)-c):(t=e===kt||e===Lj?e:Bj,r=e,n=e);function i(u,c,l=0,f=u.length){if(l<f){if(t(c,c)!==0)return f;do{const s=l+f>>>1;r(u[s],c)<0?l=s+1:f=s}while(l<f)}return l}function a(u,c,l=0,f=u.length){if(l<f){if(t(c,c)!==0)return f;do{const s=l+f>>>1;r(u[s],c)<=0?l=s+1:f=s}while(l<f)}return l}function o(u,c,l=0,f=u.length){const s=i(u,c,l,f-1);return s>l&&n(u[s-1],c)>-n(u[s],c)?s-1:s}return{left:i,center:o,right:a}}function Bj(){return 0}function jv(e){return e===null?NaN:+e}function*Fj(e,t){if(t===void 0)for(let r of e)r!=null&&(r=+r)>=r&&(yield r);else{let r=-1;for(let n of e)(n=t(n,++r,e))!=null&&(n=+n)>=n&&(yield n)}}const Wj=pl(kt),zj=Wj.right;pl(jv).center;const vi=zj;class Sf extends Map{constructor(t,r=Hj){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:r}}),t!=null)for(const[n,i]of t)this.set(n,i)}get(t){return super.get(Pf(this,t))}has(t){return super.has(Pf(this,t))}set(t,r){return super.set(Uj(this,t),r)}delete(t){return super.delete(qj(this,t))}}function Pf({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):r}function Uj({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}function qj({_intern:e,_key:t},r){const n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}function Hj(e){return e!==null&&typeof e=="object"?e.valueOf():e}function Kj(e=kt){if(e===kt)return Mv;if(typeof e!="function")throw new TypeError("compare is not a function");return(t,r)=>{const n=e(t,r);return n||n===0?n:(e(r,r)===0)-(e(t,t)===0)}}function Mv(e,t){return(e==null||!(e>=e))-(t==null||!(t>=t))||(e<t?-1:e>t?1:0)}const Gj=Math.sqrt(50),Vj=Math.sqrt(10),Xj=Math.sqrt(2);function Qi(e,t,r){const n=(t-e)/Math.max(0,r),i=Math.floor(Math.log10(n)),a=n/Math.pow(10,i),o=a>=Gj?10:a>=Vj?5:a>=Xj?2:1;let u,c,l;return i<0?(l=Math.pow(10,-i)/o,u=Math.round(e*l),c=Math.round(t*l),u/l<e&&++u,c/l>t&&--c,l=-l):(l=Math.pow(10,i)*o,u=Math.round(e/l),c=Math.round(t/l),u*l<e&&++u,c*l>t&&--c),c<u&&.5<=r&&r<2?Qi(e,t,r*2):[u,c,l]}function Bu(e,t,r){if(t=+t,e=+e,r=+r,!(r>0))return[];if(e===t)return[e];const n=t<e,[i,a,o]=n?Qi(t,e,r):Qi(e,t,r);if(!(a>=i))return[];const u=a-i+1,c=new Array(u);if(n)if(o<0)for(let l=0;l<u;++l)c[l]=(a-l)/-o;else for(let l=0;l<u;++l)c[l]=(a-l)*o;else if(o<0)for(let l=0;l<u;++l)c[l]=(i+l)/-o;else for(let l=0;l<u;++l)c[l]=(i+l)*o;return c}function Fu(e,t,r){return t=+t,e=+e,r=+r,Qi(e,t,r)[2]}function Wu(e,t,r){t=+t,e=+e,r=+r;const n=t<e,i=n?Fu(t,e,r):Fu(e,t,r);return(n?-1:1)*(i<0?1/-i:i)}function _f(e,t){let r;if(t===void 0)for(const n of e)n!=null&&(r<n||r===void 0&&n>=n)&&(r=n);else{let n=-1;for(let i of e)(i=t(i,++n,e))!=null&&(r<i||r===void 0&&i>=i)&&(r=i)}return r}function $f(e,t){let r;if(t===void 0)for(const n of e)n!=null&&(r>n||r===void 0&&n>=n)&&(r=n);else{let n=-1;for(let i of e)(i=t(i,++n,e))!=null&&(r>i||r===void 0&&i>=i)&&(r=i)}return r}function Iv(e,t,r=0,n=1/0,i){if(t=Math.floor(t),r=Math.floor(Math.max(0,r)),n=Math.floor(Math.min(e.length-1,n)),!(r<=t&&t<=n))return e;for(i=i===void 0?Mv:Kj(i);n>r;){if(n-r>600){const c=n-r+1,l=t-r+1,f=Math.log(c),s=.5*Math.exp(2*f/3),p=.5*Math.sqrt(f*s*(c-s)/c)*(l-c/2<0?-1:1),h=Math.max(r,Math.floor(t-l*s/c+p)),v=Math.min(n,Math.floor(t+(c-l)*s/c+p));Iv(e,t,h,v,i)}const a=e[t];let o=r,u=n;for(vn(e,r,t),i(e[n],a)>0&&vn(e,r,n);o<u;){for(vn(e,o,u),++o,--u;i(e[o],a)<0;)++o;for(;i(e[u],a)>0;)--u}i(e[r],a)===0?vn(e,r,u):(++u,vn(e,u,n)),u<=t&&(r=u+1),t<=u&&(n=u-1)}return e}function vn(e,t,r){const n=e[t];e[t]=e[r],e[r]=n}function Yj(e,t,r){if(e=Float64Array.from(Fj(e,r)),!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return $f(e);if(t>=1)return _f(e);var n,i=(n-1)*t,a=Math.floor(i),o=_f(Iv(e,a).subarray(0,a+1)),u=$f(e.subarray(a+1));return o+(u-o)*(i-a)}}function Zj(e,t,r=jv){if(!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,i=(n-1)*t,a=Math.floor(i),o=+r(e[a],a,e),u=+r(e[a+1],a+1,e);return o+(u-o)*(i-a)}}function Jj(e,t,r){e=+e,t=+t,r=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+r;for(var n=-1,i=Math.max(0,Math.ceil((t-e)/r))|0,a=new Array(i);++n<i;)a[n]=e+n*r;return a}function tt(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e);break}return this}function Et(e,t){switch(arguments.length){case 0:break;case 1:{typeof e=="function"?this.interpolator(e):this.range(e);break}default:{this.domain(e),typeof t=="function"?this.interpolator(t):this.range(t);break}}return this}const zu=Symbol("implicit");function hl(){var e=new Sf,t=[],r=[],n=zu;function i(a){let o=e.get(a);if(o===void 0){if(n!==zu)return n;e.set(a,o=t.push(a)-1)}return r[o%r.length]}return i.domain=function(a){if(!arguments.length)return t.slice();t=[],e=new Sf;for(const o of a)e.has(o)||e.set(o,t.push(o)-1);return i},i.range=function(a){return arguments.length?(r=Array.from(a),i):r.slice()},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return hl(t,r).unknown(n)},tt.apply(i,arguments),i}function Ln(){var e=hl().unknown(void 0),t=e.domain,r=e.range,n=0,i=1,a,o,u=!1,c=0,l=0,f=.5;delete e.unknown;function s(){var p=t().length,h=i<n,v=h?i:n,d=h?n:i;a=(d-v)/Math.max(1,p-c+l*2),u&&(a=Math.floor(a)),v+=(d-v-a*(p-c))*f,o=a*(1-c),u&&(v=Math.round(v),o=Math.round(o));var y=Jj(p).map(function(b){return v+a*b});return r(h?y.reverse():y)}return e.domain=function(p){return arguments.length?(t(p),s()):t()},e.range=function(p){return arguments.length?([n,i]=p,n=+n,i=+i,s()):[n,i]},e.rangeRound=function(p){return[n,i]=p,n=+n,i=+i,u=!0,s()},e.bandwidth=function(){return o},e.step=function(){return a},e.round=function(p){return arguments.length?(u=!!p,s()):u},e.padding=function(p){return arguments.length?(c=Math.min(1,l=+p),s()):c},e.paddingInner=function(p){return arguments.length?(c=Math.min(1,p),s()):c},e.paddingOuter=function(p){return arguments.length?(l=+p,s()):l},e.align=function(p){return arguments.length?(f=Math.max(0,Math.min(1,p)),s()):f},e.copy=function(){return Ln(t(),[n,i]).round(u).paddingInner(c).paddingOuter(l).align(f)},tt.apply(s(),arguments)}function Cv(e){var t=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return Cv(t())},e}function Pn(){return Cv(Ln.apply(null,arguments).paddingInner(1))}function dl(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function kv(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function yi(){}var Bn=.7,ea=1/Bn,Pr="\\s*([+-]?\\d+)\\s*",Fn="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",st="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",Qj=/^#([0-9a-f]{3,8})$/,eM=new RegExp(`^rgb\\(${Pr},${Pr},${Pr}\\)$`),tM=new RegExp(`^rgb\\(${st},${st},${st}\\)$`),rM=new RegExp(`^rgba\\(${Pr},${Pr},${Pr},${Fn}\\)$`),nM=new RegExp(`^rgba\\(${st},${st},${st},${Fn}\\)$`),iM=new RegExp(`^hsl\\(${Fn},${st},${st}\\)$`),aM=new RegExp(`^hsla\\(${Fn},${st},${st},${Fn}\\)$`),Tf={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};dl(yi,Wn,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:Ef,formatHex:Ef,formatHex8:oM,formatHsl:uM,formatRgb:jf,toString:jf});function Ef(){return this.rgb().formatHex()}function oM(){return this.rgb().formatHex8()}function uM(){return Dv(this).formatHsl()}function jf(){return this.rgb().formatRgb()}function Wn(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=Qj.exec(e))?(r=t[1].length,t=parseInt(t[1],16),r===6?Mf(t):r===3?new Le(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):r===8?Ti(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):r===4?Ti(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=eM.exec(e))?new Le(t[1],t[2],t[3],1):(t=tM.exec(e))?new Le(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=rM.exec(e))?Ti(t[1],t[2],t[3],t[4]):(t=nM.exec(e))?Ti(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=iM.exec(e))?kf(t[1],t[2]/100,t[3]/100,1):(t=aM.exec(e))?kf(t[1],t[2]/100,t[3]/100,t[4]):Tf.hasOwnProperty(e)?Mf(Tf[e]):e==="transparent"?new Le(NaN,NaN,NaN,0):null}function Mf(e){return new Le(e>>16&255,e>>8&255,e&255,1)}function Ti(e,t,r,n){return n<=0&&(e=t=r=NaN),new Le(e,t,r,n)}function cM(e){return e instanceof yi||(e=Wn(e)),e?(e=e.rgb(),new Le(e.r,e.g,e.b,e.opacity)):new Le}function Uu(e,t,r,n){return arguments.length===1?cM(e):new Le(e,t,r,n??1)}function Le(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}dl(Le,Uu,kv(yi,{brighter(e){return e=e==null?ea:Math.pow(ea,e),new Le(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?Bn:Math.pow(Bn,e),new Le(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new Le(er(this.r),er(this.g),er(this.b),ta(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:If,formatHex:If,formatHex8:lM,formatRgb:Cf,toString:Cf}));function If(){return`#${Zt(this.r)}${Zt(this.g)}${Zt(this.b)}`}function lM(){return`#${Zt(this.r)}${Zt(this.g)}${Zt(this.b)}${Zt((isNaN(this.opacity)?1:this.opacity)*255)}`}function Cf(){const e=ta(this.opacity);return`${e===1?"rgb(":"rgba("}${er(this.r)}, ${er(this.g)}, ${er(this.b)}${e===1?")":`, ${e})`}`}function ta(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function er(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function Zt(e){return e=er(e),(e<16?"0":"")+e.toString(16)}function kf(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new at(e,t,r,n)}function Dv(e){if(e instanceof at)return new at(e.h,e.s,e.l,e.opacity);if(e instanceof yi||(e=Wn(e)),!e)return new at;if(e instanceof at)return e;e=e.rgb();var t=e.r/255,r=e.g/255,n=e.b/255,i=Math.min(t,r,n),a=Math.max(t,r,n),o=NaN,u=a-i,c=(a+i)/2;return u?(t===a?o=(r-n)/u+(r<n)*6:r===a?o=(n-t)/u+2:o=(t-r)/u+4,u/=c<.5?a+i:2-a-i,o*=60):u=c>0&&c<1?0:o,new at(o,u,c,e.opacity)}function sM(e,t,r,n){return arguments.length===1?Dv(e):new at(e,t,r,n??1)}function at(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}dl(at,sM,kv(yi,{brighter(e){return e=e==null?ea:Math.pow(ea,e),new at(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?Bn:Math.pow(Bn,e),new at(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,i=2*r-n;return new Le(Xo(e>=240?e-240:e+120,i,n),Xo(e,i,n),Xo(e<120?e+240:e-120,i,n),this.opacity)},clamp(){return new at(Df(this.h),Ei(this.s),Ei(this.l),ta(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=ta(this.opacity);return`${e===1?"hsl(":"hsla("}${Df(this.h)}, ${Ei(this.s)*100}%, ${Ei(this.l)*100}%${e===1?")":`, ${e})`}`}}));function Df(e){return e=(e||0)%360,e<0?e+360:e}function Ei(e){return Math.max(0,Math.min(1,e||0))}function Xo(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}const vl=e=>()=>e;function fM(e,t){return function(r){return e+r*t}}function pM(e,t,r){return e=Math.pow(e,r),t=Math.pow(t,r)-e,r=1/r,function(n){return Math.pow(e+n*t,r)}}function hM(e){return(e=+e)==1?Nv:function(t,r){return r-t?pM(t,r,e):vl(isNaN(t)?r:t)}}function Nv(e,t){var r=t-e;return r?fM(e,r):vl(isNaN(e)?t:e)}const Nf=function e(t){var r=hM(t);function n(i,a){var o=r((i=Uu(i)).r,(a=Uu(a)).r),u=r(i.g,a.g),c=r(i.b,a.b),l=Nv(i.opacity,a.opacity);return function(f){return i.r=o(f),i.g=u(f),i.b=c(f),i.opacity=l(f),i+""}}return n.gamma=e,n}(1);function dM(e,t){t||(t=[]);var r=e?Math.min(t.length,e.length):0,n=t.slice(),i;return function(a){for(i=0;i<r;++i)n[i]=e[i]*(1-a)+t[i]*a;return n}}function vM(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}function yM(e,t){var r=t?t.length:0,n=e?Math.min(r,e.length):0,i=new Array(n),a=new Array(r),o;for(o=0;o<n;++o)i[o]=cn(e[o],t[o]);for(;o<r;++o)a[o]=t[o];return function(u){for(o=0;o<n;++o)a[o]=i[o](u);return a}}function mM(e,t){var r=new Date;return e=+e,t=+t,function(n){return r.setTime(e*(1-n)+t*n),r}}function ra(e,t){return e=+e,t=+t,function(r){return e*(1-r)+t*r}}function gM(e,t){var r={},n={},i;(e===null||typeof e!="object")&&(e={}),(t===null||typeof t!="object")&&(t={});for(i in t)i in e?r[i]=cn(e[i],t[i]):n[i]=t[i];return function(a){for(i in r)n[i]=r[i](a);return n}}var qu=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Yo=new RegExp(qu.source,"g");function bM(e){return function(){return e}}function xM(e){return function(t){return e(t)+""}}function wM(e,t){var r=qu.lastIndex=Yo.lastIndex=0,n,i,a,o=-1,u=[],c=[];for(e=e+"",t=t+"";(n=qu.exec(e))&&(i=Yo.exec(t));)(a=i.index)>r&&(a=t.slice(r,a),u[o]?u[o]+=a:u[++o]=a),(n=n[0])===(i=i[0])?u[o]?u[o]+=i:u[++o]=i:(u[++o]=null,c.push({i:o,x:ra(n,i)})),r=Yo.lastIndex;return r<t.length&&(a=t.slice(r),u[o]?u[o]+=a:u[++o]=a),u.length<2?c[0]?xM(c[0].x):bM(t):(t=c.length,function(l){for(var f=0,s;f<t;++f)u[(s=c[f]).i]=s.x(l);return u.join("")})}function cn(e,t){var r=typeof t,n;return t==null||r==="boolean"?vl(t):(r==="number"?ra:r==="string"?(n=Wn(t))?(t=n,Nf):wM:t instanceof Wn?Nf:t instanceof Date?mM:vM(t)?dM:Array.isArray(t)?yM:typeof t.valueOf!="function"&&typeof t.toString!="function"||isNaN(t)?gM:ra)(e,t)}function yl(e,t){return e=+e,t=+t,function(r){return Math.round(e*(1-r)+t*r)}}function OM(e,t){t===void 0&&(t=e,e=cn);for(var r=0,n=t.length-1,i=t[0],a=new Array(n<0?0:n);r<n;)a[r]=e(i,i=t[++r]);return function(o){var u=Math.max(0,Math.min(n-1,Math.floor(o*=n)));return a[u](o-u)}}function AM(e){return function(){return e}}function na(e){return+e}var Rf=[0,1];function De(e){return e}function Hu(e,t){return(t-=e=+e)?function(r){return(r-e)/t}:AM(isNaN(t)?NaN:.5)}function SM(e,t){var r;return e>t&&(r=e,e=t,t=r),function(n){return Math.max(e,Math.min(t,n))}}function PM(e,t,r){var n=e[0],i=e[1],a=t[0],o=t[1];return i<n?(n=Hu(i,n),a=r(o,a)):(n=Hu(n,i),a=r(a,o)),function(u){return a(n(u))}}function _M(e,t,r){var n=Math.min(e.length,t.length)-1,i=new Array(n),a=new Array(n),o=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<n;)i[o]=Hu(e[o],e[o+1]),a[o]=r(t[o],t[o+1]);return function(u){var c=vi(e,u,1,n)-1;return a[c](i[c](u))}}function mi(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function uo(){var e=Rf,t=Rf,r=cn,n,i,a,o=De,u,c,l;function f(){var p=Math.min(e.length,t.length);return o!==De&&(o=SM(e[0],e[p-1])),u=p>2?_M:PM,c=l=null,s}function s(p){return p==null||isNaN(p=+p)?a:(c||(c=u(e.map(n),t,r)))(n(o(p)))}return s.invert=function(p){return o(i((l||(l=u(t,e.map(n),ra)))(p)))},s.domain=function(p){return arguments.length?(e=Array.from(p,na),f()):e.slice()},s.range=function(p){return arguments.length?(t=Array.from(p),f()):t.slice()},s.rangeRound=function(p){return t=Array.from(p),r=yl,f()},s.clamp=function(p){return arguments.length?(o=p?!0:De,f()):o!==De},s.interpolate=function(p){return arguments.length?(r=p,f()):r},s.unknown=function(p){return arguments.length?(a=p,s):a},function(p,h){return n=p,i=h,f()}}function ml(){return uo()(De,De)}function $M(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)}function ia(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function Ir(e){return e=ia(Math.abs(e)),e?e[1]:NaN}function TM(e,t){return function(r,n){for(var i=r.length,a=[],o=0,u=e[0],c=0;i>0&&u>0&&(c+u+1>n&&(u=Math.max(1,n-c)),a.push(r.substring(i-=u,i+u)),!((c+=u+1)>n));)u=e[o=(o+1)%e.length];return a.reverse().join(t)}}function EM(e){return function(t){return t.replace(/[0-9]/g,function(r){return e[+r]})}}var jM=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function zn(e){if(!(t=jM.exec(e)))throw new Error("invalid format: "+e);var t;return new gl({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}zn.prototype=gl.prototype;function gl(e){this.fill=e.fill===void 0?" ":e.fill+"",this.align=e.align===void 0?">":e.align+"",this.sign=e.sign===void 0?"-":e.sign+"",this.symbol=e.symbol===void 0?"":e.symbol+"",this.zero=!!e.zero,this.width=e.width===void 0?void 0:+e.width,this.comma=!!e.comma,this.precision=e.precision===void 0?void 0:+e.precision,this.trim=!!e.trim,this.type=e.type===void 0?"":e.type+""}gl.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(this.width===void 0?"":Math.max(1,this.width|0))+(this.comma?",":"")+(this.precision===void 0?"":"."+Math.max(0,this.precision|0))+(this.trim?"~":"")+this.type};function MM(e){e:for(var t=e.length,r=1,n=-1,i;r<t;++r)switch(e[r]){case".":n=i=r;break;case"0":n===0&&(n=r),i=r;break;default:if(!+e[r])break e;n>0&&(n=0);break}return n>0?e.slice(0,n)+e.slice(i+1):e}var Rv;function IM(e,t){var r=ia(e,t);if(!r)return e+"";var n=r[0],i=r[1],a=i-(Rv=Math.max(-8,Math.min(8,Math.floor(i/3)))*3)+1,o=n.length;return a===o?n:a>o?n+new Array(a-o+1).join("0"):a>0?n.slice(0,a)+"."+n.slice(a):"0."+new Array(1-a).join("0")+ia(e,Math.max(0,t+a-1))[0]}function Lf(e,t){var r=ia(e,t);if(!r)return e+"";var n=r[0],i=r[1];return i<0?"0."+new Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+new Array(i-n.length+2).join("0")}const Bf={"%":(e,t)=>(e*100).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:$M,e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>Lf(e*100,t),r:Lf,s:IM,X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function Ff(e){return e}var Wf=Array.prototype.map,zf=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function CM(e){var t=e.grouping===void 0||e.thousands===void 0?Ff:TM(Wf.call(e.grouping,Number),e.thousands+""),r=e.currency===void 0?"":e.currency[0]+"",n=e.currency===void 0?"":e.currency[1]+"",i=e.decimal===void 0?".":e.decimal+"",a=e.numerals===void 0?Ff:EM(Wf.call(e.numerals,String)),o=e.percent===void 0?"%":e.percent+"",u=e.minus===void 0?"−":e.minus+"",c=e.nan===void 0?"NaN":e.nan+"";function l(s){s=zn(s);var p=s.fill,h=s.align,v=s.sign,d=s.symbol,y=s.zero,b=s.width,w=s.comma,x=s.precision,A=s.trim,m=s.type;m==="n"?(w=!0,m="g"):Bf[m]||(x===void 0&&(x=12),A=!0,m="g"),(y||p==="0"&&h==="=")&&(y=!0,p="0",h="=");var g=d==="$"?r:d==="#"&&/[boxX]/.test(m)?"0"+m.toLowerCase():"",O=d==="$"?n:/[%p]/.test(m)?o:"",P=Bf[m],_=/[defgprs%]/.test(m);x=x===void 0?6:/[gprs]/.test(m)?Math.max(1,Math.min(21,x)):Math.max(0,Math.min(20,x));function E($){var T=g,I=O,C,M,k;if(m==="c")I=P($)+I,$="";else{$=+$;var D=$<0||1/$<0;if($=isNaN($)?c:P(Math.abs($),x),A&&($=MM($)),D&&+$==0&&v!=="+"&&(D=!1),T=(D?v==="("?v:u:v==="-"||v==="("?"":v)+T,I=(m==="s"?zf[8+Rv/3]:"")+I+(D&&v==="("?")":""),_){for(C=-1,M=$.length;++C<M;)if(k=$.charCodeAt(C),48>k||k>57){I=(k===46?i+$.slice(C+1):$.slice(C))+I,$=$.slice(0,C);break}}}w&&!y&&($=t($,1/0));var B=T.length+$.length+I.length,F=B<b?new Array(b-B+1).join(p):"";switch(w&&y&&($=t(F+$,F.length?b-I.length:1/0),F=""),h){case"<":$=T+$+I+F;break;case"=":$=T+F+$+I;break;case"^":$=F.slice(0,B=F.length>>1)+T+$+I+F.slice(B);break;default:$=F+T+$+I;break}return a($)}return E.toString=function(){return s+""},E}function f(s,p){var h=l((s=zn(s),s.type="f",s)),v=Math.max(-8,Math.min(8,Math.floor(Ir(p)/3)))*3,d=Math.pow(10,-v),y=zf[8+v/3];return function(b){return h(d*b)+y}}return{format:l,formatPrefix:f}}var ji,bl,Lv;kM({thousands:",",grouping:[3],currency:["$",""]});function kM(e){return ji=CM(e),bl=ji.format,Lv=ji.formatPrefix,ji}function DM(e){return Math.max(0,-Ir(Math.abs(e)))}function NM(e,t){return Math.max(0,Math.max(-8,Math.min(8,Math.floor(Ir(t)/3)))*3-Ir(Math.abs(e)))}function RM(e,t){return e=Math.abs(e),t=Math.abs(t)-e,Math.max(0,Ir(t)-Ir(e))+1}function Bv(e,t,r,n){var i=Wu(e,t,r),a;switch(n=zn(n??",f"),n.type){case"s":{var o=Math.max(Math.abs(e),Math.abs(t));return n.precision==null&&!isNaN(a=NM(i,o))&&(n.precision=a),Lv(n,o)}case"":case"e":case"g":case"p":case"r":{n.precision==null&&!isNaN(a=RM(i,Math.max(Math.abs(e),Math.abs(t))))&&(n.precision=a-(n.type==="e"));break}case"f":case"%":{n.precision==null&&!isNaN(a=DM(i))&&(n.precision=a-(n.type==="%")*2);break}}return bl(n)}function Nt(e){var t=e.domain;return e.ticks=function(r){var n=t();return Bu(n[0],n[n.length-1],r??10)},e.tickFormat=function(r,n){var i=t();return Bv(i[0],i[i.length-1],r??10,n)},e.nice=function(r){r==null&&(r=10);var n=t(),i=0,a=n.length-1,o=n[i],u=n[a],c,l,f=10;for(u<o&&(l=o,o=u,u=l,l=i,i=a,a=l);f-- >0;){if(l=Fu(o,u,r),l===c)return n[i]=o,n[a]=u,t(n);if(l>0)o=Math.floor(o/l)*l,u=Math.ceil(u/l)*l;else if(l<0)o=Math.ceil(o*l)/l,u=Math.floor(u*l)/l;else break;c=l}return e},e}function aa(){var e=ml();return e.copy=function(){return mi(e,aa())},tt.apply(e,arguments),Nt(e)}function Fv(e){var t;function r(n){return n==null||isNaN(n=+n)?t:n}return r.invert=r,r.domain=r.range=function(n){return arguments.length?(e=Array.from(n,na),r):e.slice()},r.unknown=function(n){return arguments.length?(t=n,r):t},r.copy=function(){return Fv(e).unknown(t)},e=arguments.length?Array.from(e,na):[0,1],Nt(r)}function Wv(e,t){e=e.slice();var r=0,n=e.length-1,i=e[r],a=e[n],o;return a<i&&(o=r,r=n,n=o,o=i,i=a,a=o),e[r]=t.floor(i),e[n]=t.ceil(a),e}function Uf(e){return Math.log(e)}function qf(e){return Math.exp(e)}function LM(e){return-Math.log(-e)}function BM(e){return-Math.exp(-e)}function FM(e){return isFinite(e)?+("1e"+e):e<0?0:e}function WM(e){return e===10?FM:e===Math.E?Math.exp:t=>Math.pow(e,t)}function zM(e){return e===Math.E?Math.log:e===10&&Math.log10||e===2&&Math.log2||(e=Math.log(e),t=>Math.log(t)/e)}function Hf(e){return(t,r)=>-e(-t,r)}function xl(e){const t=e(Uf,qf),r=t.domain;let n=10,i,a;function o(){return i=zM(n),a=WM(n),r()[0]<0?(i=Hf(i),a=Hf(a),e(LM,BM)):e(Uf,qf),t}return t.base=function(u){return arguments.length?(n=+u,o()):n},t.domain=function(u){return arguments.length?(r(u),o()):r()},t.ticks=u=>{const c=r();let l=c[0],f=c[c.length-1];const s=f<l;s&&([l,f]=[f,l]);let p=i(l),h=i(f),v,d;const y=u==null?10:+u;let b=[];if(!(n%1)&&h-p<y){if(p=Math.floor(p),h=Math.ceil(h),l>0){for(;p<=h;++p)for(v=1;v<n;++v)if(d=p<0?v/a(-p):v*a(p),!(d<l)){if(d>f)break;b.push(d)}}else for(;p<=h;++p)for(v=n-1;v>=1;--v)if(d=p>0?v/a(-p):v*a(p),!(d<l)){if(d>f)break;b.push(d)}b.length*2<y&&(b=Bu(l,f,y))}else b=Bu(p,h,Math.min(h-p,y)).map(a);return s?b.reverse():b},t.tickFormat=(u,c)=>{if(u==null&&(u=10),c==null&&(c=n===10?"s":","),typeof c!="function"&&(!(n%1)&&(c=zn(c)).precision==null&&(c.trim=!0),c=bl(c)),u===1/0)return c;const l=Math.max(1,n*u/t.ticks().length);return f=>{let s=f/a(Math.round(i(f)));return s*n<n-.5&&(s*=n),s<=l?c(f):""}},t.nice=()=>r(Wv(r(),{floor:u=>a(Math.floor(i(u))),ceil:u=>a(Math.ceil(i(u)))})),t}function zv(){const e=xl(uo()).domain([1,10]);return e.copy=()=>mi(e,zv()).base(e.base()),tt.apply(e,arguments),e}function Kf(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function Gf(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function wl(e){var t=1,r=e(Kf(t),Gf(t));return r.constant=function(n){return arguments.length?e(Kf(t=+n),Gf(t)):t},Nt(r)}function Uv(){var e=wl(uo());return e.copy=function(){return mi(e,Uv()).constant(e.constant())},tt.apply(e,arguments)}function Vf(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function UM(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function qM(e){return e<0?-e*e:e*e}function Ol(e){var t=e(De,De),r=1;function n(){return r===1?e(De,De):r===.5?e(UM,qM):e(Vf(r),Vf(1/r))}return t.exponent=function(i){return arguments.length?(r=+i,n()):r},Nt(t)}function Al(){var e=Ol(uo());return e.copy=function(){return mi(e,Al()).exponent(e.exponent())},tt.apply(e,arguments),e}function HM(){return Al.apply(null,arguments).exponent(.5)}function Xf(e){return Math.sign(e)*e*e}function KM(e){return Math.sign(e)*Math.sqrt(Math.abs(e))}function qv(){var e=ml(),t=[0,1],r=!1,n;function i(a){var o=KM(e(a));return isNaN(o)?n:r?Math.round(o):o}return i.invert=function(a){return e.invert(Xf(a))},i.domain=function(a){return arguments.length?(e.domain(a),i):e.domain()},i.range=function(a){return arguments.length?(e.range((t=Array.from(a,na)).map(Xf)),i):t.slice()},i.rangeRound=function(a){return i.range(a).round(!0)},i.round=function(a){return arguments.length?(r=!!a,i):r},i.clamp=function(a){return arguments.length?(e.clamp(a),i):e.clamp()},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return qv(e.domain(),t).round(r).clamp(e.clamp()).unknown(n)},tt.apply(i,arguments),Nt(i)}function Hv(){var e=[],t=[],r=[],n;function i(){var o=0,u=Math.max(1,t.length);for(r=new Array(u-1);++o<u;)r[o-1]=Zj(e,o/u);return a}function a(o){return o==null||isNaN(o=+o)?n:t[vi(r,o)]}return a.invertExtent=function(o){var u=t.indexOf(o);return u<0?[NaN,NaN]:[u>0?r[u-1]:e[0],u<r.length?r[u]:e[e.length-1]]},a.domain=function(o){if(!arguments.length)return e.slice();e=[];for(let u of o)u!=null&&!isNaN(u=+u)&&e.push(u);return e.sort(kt),i()},a.range=function(o){return arguments.length?(t=Array.from(o),i()):t.slice()},a.unknown=function(o){return arguments.length?(n=o,a):n},a.quantiles=function(){return r.slice()},a.copy=function(){return Hv().domain(e).range(t).unknown(n)},tt.apply(a,arguments)}function Kv(){var e=0,t=1,r=1,n=[.5],i=[0,1],a;function o(c){return c!=null&&c<=c?i[vi(n,c,0,r)]:a}function u(){var c=-1;for(n=new Array(r);++c<r;)n[c]=((c+1)*t-(c-r)*e)/(r+1);return o}return o.domain=function(c){return arguments.length?([e,t]=c,e=+e,t=+t,u()):[e,t]},o.range=function(c){return arguments.length?(r=(i=Array.from(c)).length-1,u()):i.slice()},o.invertExtent=function(c){var l=i.indexOf(c);return l<0?[NaN,NaN]:l<1?[e,n[0]]:l>=r?[n[r-1],t]:[n[l-1],n[l]]},o.unknown=function(c){return arguments.length&&(a=c),o},o.thresholds=function(){return n.slice()},o.copy=function(){return Kv().domain([e,t]).range(i).unknown(a)},tt.apply(Nt(o),arguments)}function Gv(){var e=[.5],t=[0,1],r,n=1;function i(a){return a!=null&&a<=a?t[vi(e,a,0,n)]:r}return i.domain=function(a){return arguments.length?(e=Array.from(a),n=Math.min(e.length,t.length-1),i):e.slice()},i.range=function(a){return arguments.length?(t=Array.from(a),n=Math.min(e.length,t.length-1),i):t.slice()},i.invertExtent=function(a){var o=t.indexOf(a);return[e[o-1],e[o]]},i.unknown=function(a){return arguments.length?(r=a,i):r},i.copy=function(){return Gv().domain(e).range(t).unknown(r)},tt.apply(i,arguments)}const Zo=new Date,Jo=new Date;function _e(e,t,r,n){function i(a){return e(a=arguments.length===0?new Date:new Date(+a)),a}return i.floor=a=>(e(a=new Date(+a)),a),i.ceil=a=>(e(a=new Date(a-1)),t(a,1),e(a),a),i.round=a=>{const o=i(a),u=i.ceil(a);return a-o<u-a?o:u},i.offset=(a,o)=>(t(a=new Date(+a),o==null?1:Math.floor(o)),a),i.range=(a,o,u)=>{const c=[];if(a=i.ceil(a),u=u==null?1:Math.floor(u),!(a<o)||!(u>0))return c;let l;do c.push(l=new Date(+a)),t(a,u),e(a);while(l<a&&a<o);return c},i.filter=a=>_e(o=>{if(o>=o)for(;e(o),!a(o);)o.setTime(o-1)},(o,u)=>{if(o>=o)if(u<0)for(;++u<=0;)for(;t(o,-1),!a(o););else for(;--u>=0;)for(;t(o,1),!a(o););}),r&&(i.count=(a,o)=>(Zo.setTime(+a),Jo.setTime(+o),e(Zo),e(Jo),Math.floor(r(Zo,Jo))),i.every=a=>(a=Math.floor(a),!isFinite(a)||!(a>0)?null:a>1?i.filter(n?o=>n(o)%a===0:o=>i.count(0,o)%a===0):i)),i}const oa=_e(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);oa.every=e=>(e=Math.floor(e),!isFinite(e)||!(e>0)?null:e>1?_e(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):oa);oa.range;const gt=1e3,Je=gt*60,bt=Je*60,St=bt*24,Sl=St*7,Yf=St*30,Qo=St*365,Jt=_e(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+t*gt)},(e,t)=>(t-e)/gt,e=>e.getUTCSeconds());Jt.range;const Pl=_e(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*gt)},(e,t)=>{e.setTime(+e+t*Je)},(e,t)=>(t-e)/Je,e=>e.getMinutes());Pl.range;const _l=_e(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+t*Je)},(e,t)=>(t-e)/Je,e=>e.getUTCMinutes());_l.range;const $l=_e(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*gt-e.getMinutes()*Je)},(e,t)=>{e.setTime(+e+t*bt)},(e,t)=>(t-e)/bt,e=>e.getHours());$l.range;const Tl=_e(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+t*bt)},(e,t)=>(t-e)/bt,e=>e.getUTCHours());Tl.range;const gi=_e(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*Je)/St,e=>e.getDate()-1);gi.range;const co=_e(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/St,e=>e.getUTCDate()-1);co.range;const Vv=_e(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/St,e=>Math.floor(e/St));Vv.range;function sr(e){return _e(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(t,r)=>{t.setDate(t.getDate()+r*7)},(t,r)=>(r-t-(r.getTimezoneOffset()-t.getTimezoneOffset())*Je)/Sl)}const lo=sr(0),ua=sr(1),GM=sr(2),VM=sr(3),Cr=sr(4),XM=sr(5),YM=sr(6);lo.range;ua.range;GM.range;VM.range;Cr.range;XM.range;YM.range;function fr(e){return _e(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCDate(t.getUTCDate()+r*7)},(t,r)=>(r-t)/Sl)}const so=fr(0),ca=fr(1),ZM=fr(2),JM=fr(3),kr=fr(4),QM=fr(5),eI=fr(6);so.range;ca.range;ZM.range;JM.range;kr.range;QM.range;eI.range;const El=_e(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());El.range;const jl=_e(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());jl.range;const Pt=_e(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());Pt.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:_e(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)});Pt.range;const _t=_e(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());_t.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:_e(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)});_t.range;function Xv(e,t,r,n,i,a){const o=[[Jt,1,gt],[Jt,5,5*gt],[Jt,15,15*gt],[Jt,30,30*gt],[a,1,Je],[a,5,5*Je],[a,15,15*Je],[a,30,30*Je],[i,1,bt],[i,3,3*bt],[i,6,6*bt],[i,12,12*bt],[n,1,St],[n,2,2*St],[r,1,Sl],[t,1,Yf],[t,3,3*Yf],[e,1,Qo]];function u(l,f,s){const p=f<l;p&&([l,f]=[f,l]);const h=s&&typeof s.range=="function"?s:c(l,f,s),v=h?h.range(l,+f+1):[];return p?v.reverse():v}function c(l,f,s){const p=Math.abs(f-l)/s,h=pl(([,,y])=>y).right(o,p);if(h===o.length)return e.every(Wu(l/Qo,f/Qo,s));if(h===0)return oa.every(Math.max(Wu(l,f,s),1));const[v,d]=o[p/o[h-1][2]<o[h][2]/p?h-1:h];return v.every(d)}return[u,c]}const[tI,rI]=Xv(_t,jl,so,Vv,Tl,_l),[nI,iI]=Xv(Pt,El,lo,gi,$l,Pl);function eu(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function tu(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function yn(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}function aI(e){var t=e.dateTime,r=e.date,n=e.time,i=e.periods,a=e.days,o=e.shortDays,u=e.months,c=e.shortMonths,l=mn(i),f=gn(i),s=mn(a),p=gn(a),h=mn(o),v=gn(o),d=mn(u),y=gn(u),b=mn(c),w=gn(c),x={a:D,A:B,b:F,B:q,c:null,d:rp,e:rp,f:TI,g:LI,G:FI,H:PI,I:_I,j:$I,L:Yv,m:EI,M:jI,p:G,q:z,Q:ap,s:op,S:MI,u:II,U:CI,V:kI,w:DI,W:NI,x:null,X:null,y:RI,Y:BI,Z:WI,"%":ip},A={a:V,A:fe,b:me,B:Fe,c:null,d:np,e:np,f:HI,g:tC,G:nC,H:zI,I:UI,j:qI,L:Jv,m:KI,M:GI,p:Ft,q:Ne,Q:ap,s:op,S:VI,u:XI,U:YI,V:ZI,w:JI,W:QI,x:null,X:null,y:eC,Y:rC,Z:iC,"%":ip},m={a:E,A:$,b:T,B:I,c:C,d:ep,e:ep,f:wI,g:Qf,G:Jf,H:tp,I:tp,j:mI,L:xI,m:yI,M:gI,p:_,q:vI,Q:AI,s:SI,S:bI,u:sI,U:fI,V:pI,w:lI,W:hI,x:M,X:k,y:Qf,Y:Jf,Z:dI,"%":OI};x.x=g(r,x),x.X=g(n,x),x.c=g(t,x),A.x=g(r,A),A.X=g(n,A),A.c=g(t,A);function g(W,Z){return function(Q){var N=[],ve=-1,ee=0,xe=W.length,we,Re,jt;for(Q instanceof Date||(Q=new Date(+Q));++ve<xe;)W.charCodeAt(ve)===37&&(N.push(W.slice(ee,ve)),(Re=Zf[we=W.charAt(++ve)])!=null?we=W.charAt(++ve):Re=we==="e"?" ":"0",(jt=Z[we])&&(we=jt(Q,Re)),N.push(we),ee=ve+1);return N.push(W.slice(ee,ve)),N.join("")}}function O(W,Z){return function(Q){var N=yn(1900,void 0,1),ve=P(N,W,Q+="",0),ee,xe;if(ve!=Q.length)return null;if("Q"in N)return new Date(N.Q);if("s"in N)return new Date(N.s*1e3+("L"in N?N.L:0));if(Z&&!("Z"in N)&&(N.Z=0),"p"in N&&(N.H=N.H%12+N.p*12),N.m===void 0&&(N.m="q"in N?N.q:0),"V"in N){if(N.V<1||N.V>53)return null;"w"in N||(N.w=1),"Z"in N?(ee=tu(yn(N.y,0,1)),xe=ee.getUTCDay(),ee=xe>4||xe===0?ca.ceil(ee):ca(ee),ee=co.offset(ee,(N.V-1)*7),N.y=ee.getUTCFullYear(),N.m=ee.getUTCMonth(),N.d=ee.getUTCDate()+(N.w+6)%7):(ee=eu(yn(N.y,0,1)),xe=ee.getDay(),ee=xe>4||xe===0?ua.ceil(ee):ua(ee),ee=gi.offset(ee,(N.V-1)*7),N.y=ee.getFullYear(),N.m=ee.getMonth(),N.d=ee.getDate()+(N.w+6)%7)}else("W"in N||"U"in N)&&("w"in N||(N.w="u"in N?N.u%7:"W"in N?1:0),xe="Z"in N?tu(yn(N.y,0,1)).getUTCDay():eu(yn(N.y,0,1)).getDay(),N.m=0,N.d="W"in N?(N.w+6)%7+N.W*7-(xe+5)%7:N.w+N.U*7-(xe+6)%7);return"Z"in N?(N.H+=N.Z/100|0,N.M+=N.Z%100,tu(N)):eu(N)}}function P(W,Z,Q,N){for(var ve=0,ee=Z.length,xe=Q.length,we,Re;ve<ee;){if(N>=xe)return-1;if(we=Z.charCodeAt(ve++),we===37){if(we=Z.charAt(ve++),Re=m[we in Zf?Z.charAt(ve++):we],!Re||(N=Re(W,Q,N))<0)return-1}else if(we!=Q.charCodeAt(N++))return-1}return N}function _(W,Z,Q){var N=l.exec(Z.slice(Q));return N?(W.p=f.get(N[0].toLowerCase()),Q+N[0].length):-1}function E(W,Z,Q){var N=h.exec(Z.slice(Q));return N?(W.w=v.get(N[0].toLowerCase()),Q+N[0].length):-1}function $(W,Z,Q){var N=s.exec(Z.slice(Q));return N?(W.w=p.get(N[0].toLowerCase()),Q+N[0].length):-1}function T(W,Z,Q){var N=b.exec(Z.slice(Q));return N?(W.m=w.get(N[0].toLowerCase()),Q+N[0].length):-1}function I(W,Z,Q){var N=d.exec(Z.slice(Q));return N?(W.m=y.get(N[0].toLowerCase()),Q+N[0].length):-1}function C(W,Z,Q){return P(W,t,Z,Q)}function M(W,Z,Q){return P(W,r,Z,Q)}function k(W,Z,Q){return P(W,n,Z,Q)}function D(W){return o[W.getDay()]}function B(W){return a[W.getDay()]}function F(W){return c[W.getMonth()]}function q(W){return u[W.getMonth()]}function G(W){return i[+(W.getHours()>=12)]}function z(W){return 1+~~(W.getMonth()/3)}function V(W){return o[W.getUTCDay()]}function fe(W){return a[W.getUTCDay()]}function me(W){return c[W.getUTCMonth()]}function Fe(W){return u[W.getUTCMonth()]}function Ft(W){return i[+(W.getUTCHours()>=12)]}function Ne(W){return 1+~~(W.getUTCMonth()/3)}return{format:function(W){var Z=g(W+="",x);return Z.toString=function(){return W},Z},parse:function(W){var Z=O(W+="",!1);return Z.toString=function(){return W},Z},utcFormat:function(W){var Z=g(W+="",A);return Z.toString=function(){return W},Z},utcParse:function(W){var Z=O(W+="",!0);return Z.toString=function(){return W},Z}}}var Zf={"-":"",_:" ",0:"0"},Ee=/^\s*\d+/,oI=/^%/,uI=/[\\^$*+?|[\]().{}]/g;function re(e,t,r){var n=e<0?"-":"",i=(n?-e:e)+"",a=i.length;return n+(a<r?new Array(r-a+1).join(t)+i:i)}function cI(e){return e.replace(uI,"\\$&")}function mn(e){return new RegExp("^(?:"+e.map(cI).join("|")+")","i")}function gn(e){return new Map(e.map((t,r)=>[t.toLowerCase(),r]))}function lI(e,t,r){var n=Ee.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function sI(e,t,r){var n=Ee.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function fI(e,t,r){var n=Ee.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function pI(e,t,r){var n=Ee.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function hI(e,t,r){var n=Ee.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function Jf(e,t,r){var n=Ee.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function Qf(e,t,r){var n=Ee.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function dI(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function vI(e,t,r){var n=Ee.exec(t.slice(r,r+1));return n?(e.q=n[0]*3-3,r+n[0].length):-1}function yI(e,t,r){var n=Ee.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function ep(e,t,r){var n=Ee.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function mI(e,t,r){var n=Ee.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function tp(e,t,r){var n=Ee.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function gI(e,t,r){var n=Ee.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function bI(e,t,r){var n=Ee.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function xI(e,t,r){var n=Ee.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function wI(e,t,r){var n=Ee.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function OI(e,t,r){var n=oI.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function AI(e,t,r){var n=Ee.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function SI(e,t,r){var n=Ee.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function rp(e,t){return re(e.getDate(),t,2)}function PI(e,t){return re(e.getHours(),t,2)}function _I(e,t){return re(e.getHours()%12||12,t,2)}function $I(e,t){return re(1+gi.count(Pt(e),e),t,3)}function Yv(e,t){return re(e.getMilliseconds(),t,3)}function TI(e,t){return Yv(e,t)+"000"}function EI(e,t){return re(e.getMonth()+1,t,2)}function jI(e,t){return re(e.getMinutes(),t,2)}function MI(e,t){return re(e.getSeconds(),t,2)}function II(e){var t=e.getDay();return t===0?7:t}function CI(e,t){return re(lo.count(Pt(e)-1,e),t,2)}function Zv(e){var t=e.getDay();return t>=4||t===0?Cr(e):Cr.ceil(e)}function kI(e,t){return e=Zv(e),re(Cr.count(Pt(e),e)+(Pt(e).getDay()===4),t,2)}function DI(e){return e.getDay()}function NI(e,t){return re(ua.count(Pt(e)-1,e),t,2)}function RI(e,t){return re(e.getFullYear()%100,t,2)}function LI(e,t){return e=Zv(e),re(e.getFullYear()%100,t,2)}function BI(e,t){return re(e.getFullYear()%1e4,t,4)}function FI(e,t){var r=e.getDay();return e=r>=4||r===0?Cr(e):Cr.ceil(e),re(e.getFullYear()%1e4,t,4)}function WI(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+re(t/60|0,"0",2)+re(t%60,"0",2)}function np(e,t){return re(e.getUTCDate(),t,2)}function zI(e,t){return re(e.getUTCHours(),t,2)}function UI(e,t){return re(e.getUTCHours()%12||12,t,2)}function qI(e,t){return re(1+co.count(_t(e),e),t,3)}function Jv(e,t){return re(e.getUTCMilliseconds(),t,3)}function HI(e,t){return Jv(e,t)+"000"}function KI(e,t){return re(e.getUTCMonth()+1,t,2)}function GI(e,t){return re(e.getUTCMinutes(),t,2)}function VI(e,t){return re(e.getUTCSeconds(),t,2)}function XI(e){var t=e.getUTCDay();return t===0?7:t}function YI(e,t){return re(so.count(_t(e)-1,e),t,2)}function Qv(e){var t=e.getUTCDay();return t>=4||t===0?kr(e):kr.ceil(e)}function ZI(e,t){return e=Qv(e),re(kr.count(_t(e),e)+(_t(e).getUTCDay()===4),t,2)}function JI(e){return e.getUTCDay()}function QI(e,t){return re(ca.count(_t(e)-1,e),t,2)}function eC(e,t){return re(e.getUTCFullYear()%100,t,2)}function tC(e,t){return e=Qv(e),re(e.getUTCFullYear()%100,t,2)}function rC(e,t){return re(e.getUTCFullYear()%1e4,t,4)}function nC(e,t){var r=e.getUTCDay();return e=r>=4||r===0?kr(e):kr.ceil(e),re(e.getUTCFullYear()%1e4,t,4)}function iC(){return"+0000"}function ip(){return"%"}function ap(e){return+e}function op(e){return Math.floor(+e/1e3)}var vr,ey,ty;aC({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});function aC(e){return vr=aI(e),ey=vr.format,vr.parse,ty=vr.utcFormat,vr.utcParse,vr}function oC(e){return new Date(e)}function uC(e){return e instanceof Date?+e:+new Date(+e)}function Ml(e,t,r,n,i,a,o,u,c,l){var f=ml(),s=f.invert,p=f.domain,h=l(".%L"),v=l(":%S"),d=l("%I:%M"),y=l("%I %p"),b=l("%a %d"),w=l("%b %d"),x=l("%B"),A=l("%Y");function m(g){return(c(g)<g?h:u(g)<g?v:o(g)<g?d:a(g)<g?y:n(g)<g?i(g)<g?b:w:r(g)<g?x:A)(g)}return f.invert=function(g){return new Date(s(g))},f.domain=function(g){return arguments.length?p(Array.from(g,uC)):p().map(oC)},f.ticks=function(g){var O=p();return e(O[0],O[O.length-1],g??10)},f.tickFormat=function(g,O){return O==null?m:l(O)},f.nice=function(g){var O=p();return(!g||typeof g.range!="function")&&(g=t(O[0],O[O.length-1],g??10)),g?p(Wv(O,g)):f},f.copy=function(){return mi(f,Ml(e,t,r,n,i,a,o,u,c,l))},f}function cC(){return tt.apply(Ml(nI,iI,Pt,El,lo,gi,$l,Pl,Jt,ey).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function lC(){return tt.apply(Ml(tI,rI,_t,jl,so,co,Tl,_l,Jt,ty).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function fo(){var e=0,t=1,r,n,i,a,o=De,u=!1,c;function l(s){return s==null||isNaN(s=+s)?c:o(i===0?.5:(s=(a(s)-r)*i,u?Math.max(0,Math.min(1,s)):s))}l.domain=function(s){return arguments.length?([e,t]=s,r=a(e=+e),n=a(t=+t),i=r===n?0:1/(n-r),l):[e,t]},l.clamp=function(s){return arguments.length?(u=!!s,l):u},l.interpolator=function(s){return arguments.length?(o=s,l):o};function f(s){return function(p){var h,v;return arguments.length?([h,v]=p,o=s(h,v),l):[o(0),o(1)]}}return l.range=f(cn),l.rangeRound=f(yl),l.unknown=function(s){return arguments.length?(c=s,l):c},function(s){return a=s,r=s(e),n=s(t),i=r===n?0:1/(n-r),l}}function Rt(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function ry(){var e=Nt(fo()(De));return e.copy=function(){return Rt(e,ry())},Et.apply(e,arguments)}function ny(){var e=xl(fo()).domain([1,10]);return e.copy=function(){return Rt(e,ny()).base(e.base())},Et.apply(e,arguments)}function iy(){var e=wl(fo());return e.copy=function(){return Rt(e,iy()).constant(e.constant())},Et.apply(e,arguments)}function Il(){var e=Ol(fo());return e.copy=function(){return Rt(e,Il()).exponent(e.exponent())},Et.apply(e,arguments)}function sC(){return Il.apply(null,arguments).exponent(.5)}function ay(){var e=[],t=De;function r(n){if(n!=null&&!isNaN(n=+n))return t((vi(e,n,1)-1)/(e.length-1))}return r.domain=function(n){if(!arguments.length)return e.slice();e=[];for(let i of n)i!=null&&!isNaN(i=+i)&&e.push(i);return e.sort(kt),r},r.interpolator=function(n){return arguments.length?(t=n,r):t},r.range=function(){return e.map((n,i)=>t(i/(e.length-1)))},r.quantiles=function(n){return Array.from({length:n+1},(i,a)=>Yj(e,a/n))},r.copy=function(){return ay(t).domain(e)},Et.apply(r,arguments)}function po(){var e=0,t=.5,r=1,n=1,i,a,o,u,c,l=De,f,s=!1,p;function h(d){return isNaN(d=+d)?p:(d=.5+((d=+f(d))-a)*(n*d<n*a?u:c),l(s?Math.max(0,Math.min(1,d)):d))}h.domain=function(d){return arguments.length?([e,t,r]=d,i=f(e=+e),a=f(t=+t),o=f(r=+r),u=i===a?0:.5/(a-i),c=a===o?0:.5/(o-a),n=a<i?-1:1,h):[e,t,r]},h.clamp=function(d){return arguments.length?(s=!!d,h):s},h.interpolator=function(d){return arguments.length?(l=d,h):l};function v(d){return function(y){var b,w,x;return arguments.length?([b,w,x]=y,l=OM(d,[b,w,x]),h):[l(0),l(.5),l(1)]}}return h.range=v(cn),h.rangeRound=v(yl),h.unknown=function(d){return arguments.length?(p=d,h):p},function(d){return f=d,i=d(e),a=d(t),o=d(r),u=i===a?0:.5/(a-i),c=a===o?0:.5/(o-a),n=a<i?-1:1,h}}function oy(){var e=Nt(po()(De));return e.copy=function(){return Rt(e,oy())},Et.apply(e,arguments)}function uy(){var e=xl(po()).domain([.1,1,10]);return e.copy=function(){return Rt(e,uy()).base(e.base())},Et.apply(e,arguments)}function cy(){var e=wl(po());return e.copy=function(){return Rt(e,cy()).constant(e.constant())},Et.apply(e,arguments)}function Cl(){var e=Ol(po());return e.copy=function(){return Rt(e,Cl()).exponent(e.exponent())},Et.apply(e,arguments)}function fC(){return Cl.apply(null,arguments).exponent(.5)}const up=Object.freeze(Object.defineProperty({__proto__:null,scaleBand:Ln,scaleDiverging:oy,scaleDivergingLog:uy,scaleDivergingPow:Cl,scaleDivergingSqrt:fC,scaleDivergingSymlog:cy,scaleIdentity:Fv,scaleImplicit:zu,scaleLinear:aa,scaleLog:zv,scaleOrdinal:hl,scalePoint:Pn,scalePow:Al,scaleQuantile:Hv,scaleQuantize:Kv,scaleRadial:qv,scaleSequential:ry,scaleSequentialLog:ny,scaleSequentialPow:Il,scaleSequentialQuantile:ay,scaleSequentialSqrt:sC,scaleSequentialSymlog:iy,scaleSqrt:HM,scaleSymlog:Uv,scaleThreshold:Gv,scaleTime:cC,scaleUtc:lC,tickFormat:Bv},Symbol.toStringTag,{value:"Module"}));var pC=Jr;function hC(e,t,r){for(var n=-1,i=e.length;++n<i;){var a=e[n],o=t(a);if(o!=null&&(u===void 0?o===o&&!pC(o):r(o,u)))var u=o,c=a}return c}var ho=hC;function dC(e,t){return e>t}var ly=dC,vC=ho,yC=ly,mC=un;function gC(e){return e&&e.length?vC(e,mC,yC):void 0}var bC=gC;const vo=oe(bC);function xC(e,t){return e<t}var sy=xC,wC=ho,OC=sy,AC=un;function SC(e){return e&&e.length?wC(e,AC,OC):void 0}var PC=SC;const yo=oe(PC);var _C=Kc,$C=dt,TC=gv,EC=Be;function jC(e,t){var r=EC(e)?_C:TC;return r(e,$C(t))}var MC=jC,IC=yv,CC=MC;function kC(e,t){return IC(CC(e,t),1)}var DC=kC;const NC=oe(DC);var RC=ul;function LC(e,t){return RC(e,t)}var BC=LC;const bi=oe(BC);var ln=1e9,FC={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},Dl,de=!0,Qe="[DecimalError] ",tr=Qe+"Invalid argument: ",kl=Qe+"Exponent out of range: ",sn=Math.floor,Vt=Math.pow,WC=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,He,$e=1e7,he=7,fy=9007199254740991,la=sn(fy/he),U={};U.absoluteValue=U.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e};U.comparedTo=U.cmp=function(e){var t,r,n,i,a=this;if(e=new a.constructor(e),a.s!==e.s)return a.s||-e.s;if(a.e!==e.e)return a.e>e.e^a.s<0?1:-1;for(n=a.d.length,i=e.d.length,t=0,r=n<i?n:i;t<r;++t)if(a.d[t]!==e.d[t])return a.d[t]>e.d[t]^a.s<0?1:-1;return n===i?0:n>i^a.s<0?1:-1};U.decimalPlaces=U.dp=function(){var e=this,t=e.d.length-1,r=(t-e.e)*he;if(t=e.d[t],t)for(;t%10==0;t/=10)r--;return r<0?0:r};U.dividedBy=U.div=function(e){return Ot(this,new this.constructor(e))};U.dividedToIntegerBy=U.idiv=function(e){var t=this,r=t.constructor;return ue(Ot(t,new r(e),0,1),r.precision)};U.equals=U.eq=function(e){return!this.cmp(e)};U.exponent=function(){return be(this)};U.greaterThan=U.gt=function(e){return this.cmp(e)>0};U.greaterThanOrEqualTo=U.gte=function(e){return this.cmp(e)>=0};U.isInteger=U.isint=function(){return this.e>this.d.length-2};U.isNegative=U.isneg=function(){return this.s<0};U.isPositive=U.ispos=function(){return this.s>0};U.isZero=function(){return this.s===0};U.lessThan=U.lt=function(e){return this.cmp(e)<0};U.lessThanOrEqualTo=U.lte=function(e){return this.cmp(e)<1};U.logarithm=U.log=function(e){var t,r=this,n=r.constructor,i=n.precision,a=i+5;if(e===void 0)e=new n(10);else if(e=new n(e),e.s<1||e.eq(He))throw Error(Qe+"NaN");if(r.s<1)throw Error(Qe+(r.s?"NaN":"-Infinity"));return r.eq(He)?new n(0):(de=!1,t=Ot(Un(r,a),Un(e,a),a),de=!0,ue(t,i))};U.minus=U.sub=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?dy(t,e):py(t,(e.s=-e.s,e))};U.modulo=U.mod=function(e){var t,r=this,n=r.constructor,i=n.precision;if(e=new n(e),!e.s)throw Error(Qe+"NaN");return r.s?(de=!1,t=Ot(r,e,0,1).times(e),de=!0,r.minus(t)):ue(new n(r),i)};U.naturalExponential=U.exp=function(){return hy(this)};U.naturalLogarithm=U.ln=function(){return Un(this)};U.negated=U.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e};U.plus=U.add=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?py(t,e):dy(t,(e.s=-e.s,e))};U.precision=U.sd=function(e){var t,r,n,i=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error(tr+e);if(t=be(i)+1,n=i.d.length-1,r=n*he+1,n=i.d[n],n){for(;n%10==0;n/=10)r--;for(n=i.d[0];n>=10;n/=10)r++}return e&&t>r?t:r};U.squareRoot=U.sqrt=function(){var e,t,r,n,i,a,o,u=this,c=u.constructor;if(u.s<1){if(!u.s)return new c(0);throw Error(Qe+"NaN")}for(e=be(u),de=!1,i=Math.sqrt(+u),i==0||i==1/0?(t=ct(u.d),(t.length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=sn((e+1)/2)-(e<0||e%2),i==1/0?t="5e"+e:(t=i.toExponential(),t=t.slice(0,t.indexOf("e")+1)+e),n=new c(t)):n=new c(i.toString()),r=c.precision,i=o=r+3;;)if(a=n,n=a.plus(Ot(u,a,o+2)).times(.5),ct(a.d).slice(0,o)===(t=ct(n.d)).slice(0,o)){if(t=t.slice(o-3,o+1),i==o&&t=="4999"){if(ue(a,r+1,0),a.times(a).eq(u)){n=a;break}}else if(t!="9999")break;o+=4}return de=!0,ue(n,r)};U.times=U.mul=function(e){var t,r,n,i,a,o,u,c,l,f=this,s=f.constructor,p=f.d,h=(e=new s(e)).d;if(!f.s||!e.s)return new s(0);for(e.s*=f.s,r=f.e+e.e,c=p.length,l=h.length,c<l&&(a=p,p=h,h=a,o=c,c=l,l=o),a=[],o=c+l,n=o;n--;)a.push(0);for(n=l;--n>=0;){for(t=0,i=c+n;i>n;)u=a[i]+h[n]*p[i-n-1]+t,a[i--]=u%$e|0,t=u/$e|0;a[i]=(a[i]+t)%$e|0}for(;!a[--o];)a.pop();return t?++r:a.shift(),e.d=a,e.e=r,de?ue(e,s.precision):e};U.toDecimalPlaces=U.todp=function(e,t){var r=this,n=r.constructor;return r=new n(r),e===void 0?r:(pt(e,0,ln),t===void 0?t=n.rounding:pt(t,0,8),ue(r,e+be(r)+1,t))};U.toExponential=function(e,t){var r,n=this,i=n.constructor;return e===void 0?r=ar(n,!0):(pt(e,0,ln),t===void 0?t=i.rounding:pt(t,0,8),n=ue(new i(n),e+1,t),r=ar(n,!0,e+1)),r};U.toFixed=function(e,t){var r,n,i=this,a=i.constructor;return e===void 0?ar(i):(pt(e,0,ln),t===void 0?t=a.rounding:pt(t,0,8),n=ue(new a(i),e+be(i)+1,t),r=ar(n.abs(),!1,e+be(n)+1),i.isneg()&&!i.isZero()?"-"+r:r)};U.toInteger=U.toint=function(){var e=this,t=e.constructor;return ue(new t(e),be(e)+1,t.rounding)};U.toNumber=function(){return+this};U.toPower=U.pow=function(e){var t,r,n,i,a,o,u=this,c=u.constructor,l=12,f=+(e=new c(e));if(!e.s)return new c(He);if(u=new c(u),!u.s){if(e.s<1)throw Error(Qe+"Infinity");return u}if(u.eq(He))return u;if(n=c.precision,e.eq(He))return ue(u,n);if(t=e.e,r=e.d.length-1,o=t>=r,a=u.s,o){if((r=f<0?-f:f)<=fy){for(i=new c(He),t=Math.ceil(n/he+4),de=!1;r%2&&(i=i.times(u),lp(i.d,t)),r=sn(r/2),r!==0;)u=u.times(u),lp(u.d,t);return de=!0,e.s<0?new c(He).div(i):ue(i,n)}}else if(a<0)throw Error(Qe+"NaN");return a=a<0&&e.d[Math.max(t,r)]&1?-1:1,u.s=1,de=!1,i=e.times(Un(u,n+l)),de=!0,i=hy(i),i.s=a,i};U.toPrecision=function(e,t){var r,n,i=this,a=i.constructor;return e===void 0?(r=be(i),n=ar(i,r<=a.toExpNeg||r>=a.toExpPos)):(pt(e,1,ln),t===void 0?t=a.rounding:pt(t,0,8),i=ue(new a(i),e,t),r=be(i),n=ar(i,e<=r||r<=a.toExpNeg,e)),n};U.toSignificantDigits=U.tosd=function(e,t){var r=this,n=r.constructor;return e===void 0?(e=n.precision,t=n.rounding):(pt(e,1,ln),t===void 0?t=n.rounding:pt(t,0,8)),ue(new n(r),e,t)};U.toString=U.valueOf=U.val=U.toJSON=U[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=this,t=be(e),r=e.constructor;return ar(e,t<=r.toExpNeg||t>=r.toExpPos)};function py(e,t){var r,n,i,a,o,u,c,l,f=e.constructor,s=f.precision;if(!e.s||!t.s)return t.s||(t=new f(e)),de?ue(t,s):t;if(c=e.d,l=t.d,o=e.e,i=t.e,c=c.slice(),a=o-i,a){for(a<0?(n=c,a=-a,u=l.length):(n=l,i=o,u=c.length),o=Math.ceil(s/he),u=o>u?o+1:u+1,a>u&&(a=u,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for(u=c.length,a=l.length,u-a<0&&(a=u,n=l,l=c,c=n),r=0;a;)r=(c[--a]=c[a]+l[a]+r)/$e|0,c[a]%=$e;for(r&&(c.unshift(r),++i),u=c.length;c[--u]==0;)c.pop();return t.d=c,t.e=i,de?ue(t,s):t}function pt(e,t,r){if(e!==~~e||e<t||e>r)throw Error(tr+e)}function ct(e){var t,r,n,i=e.length-1,a="",o=e[0];if(i>0){for(a+=o,t=1;t<i;t++)n=e[t]+"",r=he-n.length,r&&(a+=Mt(r)),a+=n;o=e[t],n=o+"",r=he-n.length,r&&(a+=Mt(r))}else if(o===0)return"0";for(;o%10===0;)o/=10;return a+o}var Ot=function(){function e(n,i){var a,o=0,u=n.length;for(n=n.slice();u--;)a=n[u]*i+o,n[u]=a%$e|0,o=a/$e|0;return o&&n.unshift(o),n}function t(n,i,a,o){var u,c;if(a!=o)c=a>o?1:-1;else for(u=c=0;u<a;u++)if(n[u]!=i[u]){c=n[u]>i[u]?1:-1;break}return c}function r(n,i,a){for(var o=0;a--;)n[a]-=o,o=n[a]<i[a]?1:0,n[a]=o*$e+n[a]-i[a];for(;!n[0]&&n.length>1;)n.shift()}return function(n,i,a,o){var u,c,l,f,s,p,h,v,d,y,b,w,x,A,m,g,O,P,_=n.constructor,E=n.s==i.s?1:-1,$=n.d,T=i.d;if(!n.s)return new _(n);if(!i.s)throw Error(Qe+"Division by zero");for(c=n.e-i.e,O=T.length,m=$.length,h=new _(E),v=h.d=[],l=0;T[l]==($[l]||0);)++l;if(T[l]>($[l]||0)&&--c,a==null?w=a=_.precision:o?w=a+(be(n)-be(i))+1:w=a,w<0)return new _(0);if(w=w/he+2|0,l=0,O==1)for(f=0,T=T[0],w++;(l<m||f)&&w--;l++)x=f*$e+($[l]||0),v[l]=x/T|0,f=x%T|0;else{for(f=$e/(T[0]+1)|0,f>1&&(T=e(T,f),$=e($,f),O=T.length,m=$.length),A=O,d=$.slice(0,O),y=d.length;y<O;)d[y++]=0;P=T.slice(),P.unshift(0),g=T[0],T[1]>=$e/2&&++g;do f=0,u=t(T,d,O,y),u<0?(b=d[0],O!=y&&(b=b*$e+(d[1]||0)),f=b/g|0,f>1?(f>=$e&&(f=$e-1),s=e(T,f),p=s.length,y=d.length,u=t(s,d,p,y),u==1&&(f--,r(s,O<p?P:T,p))):(f==0&&(u=f=1),s=T.slice()),p=s.length,p<y&&s.unshift(0),r(d,s,y),u==-1&&(y=d.length,u=t(T,d,O,y),u<1&&(f++,r(d,O<y?P:T,y))),y=d.length):u===0&&(f++,d=[0]),v[l++]=f,u&&d[0]?d[y++]=$[A]||0:(d=[$[A]],y=1);while((A++<m||d[0]!==void 0)&&w--)}return v[0]||v.shift(),h.e=c,ue(h,o?a+be(h)+1:a)}}();function hy(e,t){var r,n,i,a,o,u,c=0,l=0,f=e.constructor,s=f.precision;if(be(e)>16)throw Error(kl+be(e));if(!e.s)return new f(He);for(t==null?(de=!1,u=s):u=t,o=new f(.03125);e.abs().gte(.1);)e=e.times(o),l+=5;for(n=Math.log(Vt(2,l))/Math.LN10*2+5|0,u+=n,r=i=a=new f(He),f.precision=u;;){if(i=ue(i.times(e),u),r=r.times(++c),o=a.plus(Ot(i,r,u)),ct(o.d).slice(0,u)===ct(a.d).slice(0,u)){for(;l--;)a=ue(a.times(a),u);return f.precision=s,t==null?(de=!0,ue(a,s)):a}a=o}}function be(e){for(var t=e.e*he,r=e.d[0];r>=10;r/=10)t++;return t}function ru(e,t,r){if(t>e.LN10.sd())throw de=!0,r&&(e.precision=r),Error(Qe+"LN10 precision limit exceeded");return ue(new e(e.LN10),t)}function Mt(e){for(var t="";e--;)t+="0";return t}function Un(e,t){var r,n,i,a,o,u,c,l,f,s=1,p=10,h=e,v=h.d,d=h.constructor,y=d.precision;if(h.s<1)throw Error(Qe+(h.s?"NaN":"-Infinity"));if(h.eq(He))return new d(0);if(t==null?(de=!1,l=y):l=t,h.eq(10))return t==null&&(de=!0),ru(d,l);if(l+=p,d.precision=l,r=ct(v),n=r.charAt(0),a=be(h),Math.abs(a)<15e14){for(;n<7&&n!=1||n==1&&r.charAt(1)>3;)h=h.times(e),r=ct(h.d),n=r.charAt(0),s++;a=be(h),n>1?(h=new d("0."+r),a++):h=new d(n+"."+r.slice(1))}else return c=ru(d,l+2,y).times(a+""),h=Un(new d(n+"."+r.slice(1)),l-p).plus(c),d.precision=y,t==null?(de=!0,ue(h,y)):h;for(u=o=h=Ot(h.minus(He),h.plus(He),l),f=ue(h.times(h),l),i=3;;){if(o=ue(o.times(f),l),c=u.plus(Ot(o,new d(i),l)),ct(c.d).slice(0,l)===ct(u.d).slice(0,l))return u=u.times(2),a!==0&&(u=u.plus(ru(d,l+2,y).times(a+""))),u=Ot(u,new d(s),l),d.precision=y,t==null?(de=!0,ue(u,y)):u;u=c,i+=2}}function cp(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;t.charCodeAt(n)===48;)++n;for(i=t.length;t.charCodeAt(i-1)===48;)--i;if(t=t.slice(n,i),t){if(i-=n,r=r-n-1,e.e=sn(r/he),e.d=[],n=(r+1)%he,r<0&&(n+=he),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=he;n<i;)e.d.push(+t.slice(n,n+=he));t=t.slice(n),n=he-t.length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),de&&(e.e>la||e.e<-la))throw Error(kl+r)}else e.s=0,e.e=0,e.d=[0];return e}function ue(e,t,r){var n,i,a,o,u,c,l,f,s=e.d;for(o=1,a=s[0];a>=10;a/=10)o++;if(n=t-o,n<0)n+=he,i=t,l=s[f=0];else{if(f=Math.ceil((n+1)/he),a=s.length,f>=a)return e;for(l=a=s[f],o=1;a>=10;a/=10)o++;n%=he,i=n-he+o}if(r!==void 0&&(a=Vt(10,o-i-1),u=l/a%10|0,c=t<0||s[f+1]!==void 0||l%a,c=r<4?(u||c)&&(r==0||r==(e.s<0?3:2)):u>5||u==5&&(r==4||c||r==6&&(n>0?i>0?l/Vt(10,o-i):0:s[f-1])%10&1||r==(e.s<0?8:7))),t<1||!s[0])return c?(a=be(e),s.length=1,t=t-a-1,s[0]=Vt(10,(he-t%he)%he),e.e=sn(-t/he)||0):(s.length=1,s[0]=e.e=e.s=0),e;if(n==0?(s.length=f,a=1,f--):(s.length=f+1,a=Vt(10,he-n),s[f]=i>0?(l/Vt(10,o-i)%Vt(10,i)|0)*a:0),c)for(;;)if(f==0){(s[0]+=a)==$e&&(s[0]=1,++e.e);break}else{if(s[f]+=a,s[f]!=$e)break;s[f--]=0,a=1}for(n=s.length;s[--n]===0;)s.pop();if(de&&(e.e>la||e.e<-la))throw Error(kl+be(e));return e}function dy(e,t){var r,n,i,a,o,u,c,l,f,s,p=e.constructor,h=p.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new p(e),de?ue(t,h):t;if(c=e.d,s=t.d,n=t.e,l=e.e,c=c.slice(),o=l-n,o){for(f=o<0,f?(r=c,o=-o,u=s.length):(r=s,n=l,u=c.length),i=Math.max(Math.ceil(h/he),u)+2,o>i&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for(i=c.length,u=s.length,f=i<u,f&&(u=i),i=0;i<u;i++)if(c[i]!=s[i]){f=c[i]<s[i];break}o=0}for(f&&(r=c,c=s,s=r,t.s=-t.s),u=c.length,i=s.length-u;i>0;--i)c[u++]=0;for(i=s.length;i>o;){if(c[--i]<s[i]){for(a=i;a&&c[--a]===0;)c[a]=$e-1;--c[a],c[i]+=$e}c[i]-=s[i]}for(;c[--u]===0;)c.pop();for(;c[0]===0;c.shift())--n;return c[0]?(t.d=c,t.e=n,de?ue(t,h):t):new p(0)}function ar(e,t,r){var n,i=be(e),a=ct(e.d),o=a.length;return t?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+Mt(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+Mt(-i-1)+a,r&&(n=r-o)>0&&(a+=Mt(n))):i>=o?(a+=Mt(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+Mt(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=Mt(n))),e.s<0?"-"+a:a}function lp(e,t){if(e.length>t)return e.length=t,!0}function vy(e){var t,r,n;function i(a){var o=this;if(!(o instanceof i))return new i(a);if(o.constructor=i,a instanceof i){o.s=a.s,o.e=a.e,o.d=(a=a.d)?a.slice():a;return}if(typeof a=="number"){if(a*0!==0)throw Error(tr+a);if(a>0)o.s=1;else if(a<0)a=-a,o.s=-1;else{o.s=0,o.e=0,o.d=[0];return}if(a===~~a&&a<1e7){o.e=0,o.d=[a];return}return cp(o,a.toString())}else if(typeof a!="string")throw Error(tr+a);if(a.charCodeAt(0)===45?(a=a.slice(1),o.s=-1):o.s=1,WC.test(a))cp(o,a);else throw Error(tr+a)}if(i.prototype=U,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=vy,i.config=i.set=zC,e===void 0&&(e={}),e)for(n=["precision","rounding","toExpNeg","toExpPos","LN10"],t=0;t<n.length;)e.hasOwnProperty(r=n[t++])||(e[r]=this[r]);return i.config(e),i}function zC(e){if(!e||typeof e!="object")throw Error(Qe+"Object expected");var t,r,n,i=["precision",1,ln,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if((n=e[r=i[t]])!==void 0)if(sn(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(tr+r+": "+n);if((n=e[r="LN10"])!==void 0)if(n==Math.LN10)this[r]=new this(n);else throw Error(tr+r+": "+n);return this}var Dl=vy(FC);He=new Dl(1);const ae=Dl;function UC(e){return GC(e)||KC(e)||HC(e)||qC()}function qC(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function HC(e,t){if(e){if(typeof e=="string")return Ku(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ku(e,t)}}function KC(e){if(typeof Symbol<"u"&&Symbol.iterator in Object(e))return Array.from(e)}function GC(e){if(Array.isArray(e))return Ku(e)}function Ku(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var VC=function(t){return t},yy={"@@functional/placeholder":!0},my=function(t){return t===yy},sp=function(t){return function r(){return arguments.length===0||arguments.length===1&&my(arguments.length<=0?void 0:arguments[0])?r:t.apply(void 0,arguments)}},XC=function e(t,r){return t===1?r:sp(function(){for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];var o=i.filter(function(u){return u!==yy}).length;return o>=t?r.apply(void 0,i):e(t-o,sp(function(){for(var u=arguments.length,c=new Array(u),l=0;l<u;l++)c[l]=arguments[l];var f=i.map(function(s){return my(s)?c.shift():s});return r.apply(void 0,UC(f).concat(c))}))})},mo=function(t){return XC(t.length,t)},Gu=function(t,r){for(var n=[],i=t;i<r;++i)n[i-t]=i;return n},YC=mo(function(e,t){return Array.isArray(t)?t.map(e):Object.keys(t).map(function(r){return t[r]}).map(e)}),ZC=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];if(!r.length)return VC;var i=r.reverse(),a=i[0],o=i.slice(1);return function(){return o.reduce(function(u,c){return c(u)},a.apply(void 0,arguments))}},Vu=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},gy=function(t){var r=null,n=null;return function(){for(var i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];return r&&a.every(function(u,c){return u===r[c]})||(r=a,n=t.apply(void 0,a)),n}};function JC(e){var t;return e===0?t=1:t=Math.floor(new ae(e).abs().log(10).toNumber())+1,t}function QC(e,t,r){for(var n=new ae(e),i=0,a=[];n.lt(t)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}var ek=mo(function(e,t,r){var n=+e,i=+t;return n+r*(i-n)}),tk=mo(function(e,t,r){var n=t-+e;return n=n||1/0,(r-e)/n}),rk=mo(function(e,t,r){var n=t-+e;return n=n||1/0,Math.max(0,Math.min(1,(r-e)/n))});const go={rangeStep:QC,getDigitCount:JC,interpolateNumber:ek,uninterpolateNumber:tk,uninterpolateTruncation:rk};function Xu(e){return ak(e)||ik(e)||by(e)||nk()}function nk(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ik(e){if(typeof Symbol<"u"&&Symbol.iterator in Object(e))return Array.from(e)}function ak(e){if(Array.isArray(e))return Yu(e)}function qn(e,t){return ck(e)||uk(e,t)||by(e,t)||ok()}function ok(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function by(e,t){if(e){if(typeof e=="string")return Yu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Yu(e,t)}}function Yu(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function uk(e,t){if(!(typeof Symbol>"u"||!(Symbol.iterator in Object(e)))){var r=[],n=!0,i=!1,a=void 0;try{for(var o=e[Symbol.iterator](),u;!(n=(u=o.next()).done)&&(r.push(u.value),!(t&&r.length===t));n=!0);}catch(c){i=!0,a=c}finally{try{!n&&o.return!=null&&o.return()}finally{if(i)throw a}}return r}}function ck(e){if(Array.isArray(e))return e}function xy(e){var t=qn(e,2),r=t[0],n=t[1],i=r,a=n;return r>n&&(i=n,a=r),[i,a]}function wy(e,t,r){if(e.lte(0))return new ae(0);var n=go.getDigitCount(e.toNumber()),i=new ae(10).pow(n),a=e.div(i),o=n!==1?.05:.1,u=new ae(Math.ceil(a.div(o).toNumber())).add(r).mul(o),c=u.mul(i);return t?c:new ae(Math.ceil(c))}function lk(e,t,r){var n=1,i=new ae(e);if(!i.isint()&&r){var a=Math.abs(e);a<1?(n=new ae(10).pow(go.getDigitCount(e)-1),i=new ae(Math.floor(i.div(n).toNumber())).mul(n)):a>1&&(i=new ae(Math.floor(e)))}else e===0?i=new ae(Math.floor((t-1)/2)):r||(i=new ae(Math.floor(e)));var o=Math.floor((t-1)/2),u=ZC(YC(function(c){return i.add(new ae(c-o).mul(n)).toNumber()}),Gu);return u(0,t)}function Oy(e,t,r,n){var i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new ae(0),tickMin:new ae(0),tickMax:new ae(0)};var a=wy(new ae(t).sub(e).div(r-1),n,i),o;e<=0&&t>=0?o=new ae(0):(o=new ae(e).add(t).div(2),o=o.sub(new ae(o).mod(a)));var u=Math.ceil(o.sub(e).div(a).toNumber()),c=Math.ceil(new ae(t).sub(o).div(a).toNumber()),l=u+c+1;return l>r?Oy(e,t,r,n,i+1):(l<r&&(c=t>0?c+(r-l):c,u=t>0?u:u+(r-l)),{step:a,tickMin:o.sub(new ae(u).mul(a)),tickMax:o.add(new ae(c).mul(a))})}function sk(e){var t=qn(e,2),r=t[0],n=t[1],i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:6,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=Math.max(i,2),u=xy([r,n]),c=qn(u,2),l=c[0],f=c[1];if(l===-1/0||f===1/0){var s=f===1/0?[l].concat(Xu(Gu(0,i-1).map(function(){return 1/0}))):[].concat(Xu(Gu(0,i-1).map(function(){return-1/0})),[f]);return r>n?Vu(s):s}if(l===f)return lk(l,i,a);var p=Oy(l,f,o,a),h=p.step,v=p.tickMin,d=p.tickMax,y=go.rangeStep(v,d.add(new ae(.1).mul(h)),h);return r>n?Vu(y):y}function fk(e,t){var r=qn(e,2),n=r[0],i=r[1],a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=xy([n,i]),u=qn(o,2),c=u[0],l=u[1];if(c===-1/0||l===1/0)return[n,i];if(c===l)return[c];var f=Math.max(t,2),s=wy(new ae(l).sub(c).div(f-1),a,0),p=[].concat(Xu(go.rangeStep(new ae(c),new ae(l).sub(new ae(.99).mul(s)),s)),[l]);return n>i?Vu(p):p}var pk=gy(sk),hk=gy(fk),dk=!0,nu="Invariant failed";function or(e,t){if(!e){if(dk)throw new Error(nu);var r=typeof t=="function"?t():t,n=r?"".concat(nu,": ").concat(r):nu;throw new Error(n)}}var vk=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function Dr(e){"@babel/helpers - typeof";return Dr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Dr(e)}function sa(){return sa=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},sa.apply(this,arguments)}function yk(e,t){return xk(e)||bk(e,t)||gk(e,t)||mk()}function mk(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function gk(e,t){if(e){if(typeof e=="string")return fp(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return fp(e,t)}}function fp(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function bk(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){l=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return u}}function xk(e){if(Array.isArray(e))return e}function wk(e,t){if(e==null)return{};var r=Ok(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function Ok(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Ak(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function pp(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Py(n.key),n)}}function Sk(e,t,r){return t&&pp(e.prototype,t),r&&pp(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function Pk(e,t,r){return t=fa(t),_k(e,Ay()?Reflect.construct(t,r||[],fa(e).constructor):t.apply(e,r))}function _k(e,t){if(t&&(Dr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return $k(e)}function $k(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Ay(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Ay=function(){return!!e})()}function fa(e){return fa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},fa(e)}function Tk(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Zu(e,t)}function Zu(e,t){return Zu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Zu(e,t)}function Sy(e,t,r){return t=Py(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Py(e){var t=Ek(e,"string");return Dr(t)=="symbol"?t:t+""}function Ek(e,t){if(Dr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Dr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var xi=function(e){function t(){return Ak(this,t),Pk(this,t,arguments)}return Tk(t,e),Sk(t,[{key:"render",value:function(){var n=this.props,i=n.offset,a=n.layout,o=n.width,u=n.dataKey,c=n.data,l=n.dataPointFormatter,f=n.xAxis,s=n.yAxis,p=wk(n,vk),h=H(p,!1);this.props.direction==="x"&&f.type!=="number"&&or(!1);var v=c.map(function(d){var y=l(d,u),b=y.x,w=y.y,x=y.value,A=y.errorVal;if(!A)return null;var m=[],g,O;if(Array.isArray(A)){var P=yk(A,2);g=P[0],O=P[1]}else g=O=A;if(a==="vertical"){var _=f.scale,E=w+i,$=E+o,T=E-o,I=_(x-g),C=_(x+O);m.push({x1:C,y1:$,x2:C,y2:T}),m.push({x1:I,y1:E,x2:C,y2:E}),m.push({x1:I,y1:$,x2:I,y2:T})}else if(a==="horizontal"){var M=s.scale,k=b+i,D=k-o,B=k+o,F=M(x-g),q=M(x+O);m.push({x1:D,y1:q,x2:B,y2:q}),m.push({x1:k,y1:F,x2:k,y2:q}),m.push({x1:D,y1:F,x2:B,y2:F})}return S.createElement(te,sa({className:"recharts-errorBar",key:"bar-".concat(m.map(function(G){return"".concat(G.x1,"-").concat(G.x2,"-").concat(G.y1,"-").concat(G.y2)}))},h),m.map(function(G){return S.createElement("line",sa({},G,{key:"line-".concat(G.x1,"-").concat(G.x2,"-").concat(G.y1,"-").concat(G.y2)}))}))});return S.createElement(te,{className:"recharts-errorBars"},v)}}])}(S.Component);Sy(xi,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"});Sy(xi,"displayName","ErrorBar");function Hn(e){"@babel/helpers - typeof";return Hn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Hn(e)}function hp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ut(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?hp(Object(r),!0).forEach(function(n){jk(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function jk(e,t,r){return t=Mk(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Mk(e){var t=Ik(e,"string");return Hn(t)=="symbol"?t:t+""}function Ik(e,t){if(Hn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Hn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var _y=function(t){var r=t.children,n=t.formattedGraphicalItems,i=t.legendWidth,a=t.legendContent,o=Ue(r,Sr);if(!o)return null;var u=Sr.defaultProps,c=u!==void 0?Ut(Ut({},u),o.props):{},l;return o.props&&o.props.payload?l=o.props&&o.props.payload:a==="children"?l=(n||[]).reduce(function(f,s){var p=s.item,h=s.props,v=h.sectors||h.data||[];return f.concat(v.map(function(d){return{type:o.props.iconType||p.props.legendType,value:d.name,color:d.fill,payload:d}}))},[]):l=(n||[]).map(function(f){var s=f.item,p=s.type.defaultProps,h=p!==void 0?Ut(Ut({},p),s.props):{},v=h.dataKey,d=h.name,y=h.legendType,b=h.hide;return{inactive:b,dataKey:v,type:c.iconType||y||"square",color:Nl(s),value:d||v,payload:h}}),Ut(Ut(Ut({},c),Sr.getWithHeight(o,i)),{},{payload:l,item:o})};function Kn(e){"@babel/helpers - typeof";return Kn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Kn(e)}function dp(e){return Nk(e)||Dk(e)||kk(e)||Ck()}function Ck(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function kk(e,t){if(e){if(typeof e=="string")return Ju(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ju(e,t)}}function Dk(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Nk(e){if(Array.isArray(e))return Ju(e)}function Ju(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function vp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ye(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?vp(Object(r),!0).forEach(function(n){_r(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function _r(e,t,r){return t=Rk(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Rk(e){var t=Lk(e,"string");return Kn(t)=="symbol"?t:t+""}function Lk(e,t){if(Kn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Kn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Se(e,t,r){return Y(e)||Y(t)?r:Pe(t)?Ke(e,t,r):X(t)?t(e):r}function _n(e,t,r,n){var i=NC(e,function(u){return Se(u,t)});if(r==="number"){var a=i.filter(function(u){return L(u)||parseFloat(u)});return a.length?[yo(a),vo(a)]:[1/0,-1/0]}var o=n?i.filter(function(u){return!Y(u)}):i;return o.map(function(u){return Pe(u)||u instanceof Date?u:""})}var Bk=function(t){var r,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],i=arguments.length>2?arguments[2]:void 0,a=arguments.length>3?arguments[3]:void 0,o=-1,u=(r=n==null?void 0:n.length)!==null&&r!==void 0?r:0;if(u<=1)return 0;if(a&&a.axisType==="angleAxis"&&Math.abs(Math.abs(a.range[1]-a.range[0])-360)<=1e-6)for(var c=a.range,l=0;l<u;l++){var f=l>0?i[l-1].coordinate:i[u-1].coordinate,s=i[l].coordinate,p=l>=u-1?i[0].coordinate:i[l+1].coordinate,h=void 0;if(Ce(s-f)!==Ce(p-s)){var v=[];if(Ce(p-s)===Ce(c[1]-c[0])){h=p;var d=s+c[1]-c[0];v[0]=Math.min(d,(d+f)/2),v[1]=Math.max(d,(d+f)/2)}else{h=f;var y=p+c[1]-c[0];v[0]=Math.min(s,(y+s)/2),v[1]=Math.max(s,(y+s)/2)}var b=[Math.min(s,(h+s)/2),Math.max(s,(h+s)/2)];if(t>b[0]&&t<=b[1]||t>=v[0]&&t<=v[1]){o=i[l].index;break}}else{var w=Math.min(f,p),x=Math.max(f,p);if(t>(w+s)/2&&t<=(x+s)/2){o=i[l].index;break}}}else for(var A=0;A<u;A++)if(A===0&&t<=(n[A].coordinate+n[A+1].coordinate)/2||A>0&&A<u-1&&t>(n[A].coordinate+n[A-1].coordinate)/2&&t<=(n[A].coordinate+n[A+1].coordinate)/2||A===u-1&&t>(n[A].coordinate+n[A-1].coordinate)/2){o=n[A].index;break}return o},Nl=function(t){var r,n=t,i=n.type.displayName,a=(r=t.type)!==null&&r!==void 0&&r.defaultProps?ye(ye({},t.type.defaultProps),t.props):t.props,o=a.stroke,u=a.fill,c;switch(i){case"Line":c=o;break;case"Area":case"Radar":c=o&&o!=="none"?o:u;break;default:c=u;break}return c},Fk=function(t){var r=t.barSize,n=t.totalSize,i=t.stackGroups,a=i===void 0?{}:i;if(!a)return{};for(var o={},u=Object.keys(a),c=0,l=u.length;c<l;c++)for(var f=a[u[c]].stackGroups,s=Object.keys(f),p=0,h=s.length;p<h;p++){var v=f[s[p]],d=v.items,y=v.cateAxisId,b=d.filter(function(O){return wt(O.type).indexOf("Bar")>=0});if(b&&b.length){var w=b[0].type.defaultProps,x=w!==void 0?ye(ye({},w),b[0].props):b[0].props,A=x.barSize,m=x[y];o[m]||(o[m]=[]);var g=Y(A)?r:A;o[m].push({item:b[0],stackList:b.slice(1),barSize:Y(g)?void 0:ke(g,n,0)})}}return o},Wk=function(t){var r=t.barGap,n=t.barCategoryGap,i=t.bandSize,a=t.sizeList,o=a===void 0?[]:a,u=t.maxBarSize,c=o.length;if(c<1)return null;var l=ke(r,i,0,!0),f,s=[];if(o[0].barSize===+o[0].barSize){var p=!1,h=i/c,v=o.reduce(function(A,m){return A+m.barSize||0},0);v+=(c-1)*l,v>=i&&(v-=(c-1)*l,l=0),v>=i&&h>0&&(p=!0,h*=.9,v=c*h);var d=(i-v)/2>>0,y={offset:d-l,size:0};f=o.reduce(function(A,m){var g={item:m.item,position:{offset:y.offset+y.size+l,size:p?h:m.barSize}},O=[].concat(dp(A),[g]);return y=O[O.length-1].position,m.stackList&&m.stackList.length&&m.stackList.forEach(function(P){O.push({item:P,position:y})}),O},s)}else{var b=ke(n,i,0,!0);i-2*b-(c-1)*l<=0&&(l=0);var w=(i-2*b-(c-1)*l)/c;w>1&&(w>>=0);var x=u===+u?Math.min(w,u):w;f=o.reduce(function(A,m,g){var O=[].concat(dp(A),[{item:m.item,position:{offset:b+(w+l)*g+(w-x)/2,size:x}}]);return m.stackList&&m.stackList.length&&m.stackList.forEach(function(P){O.push({item:P,position:O[O.length-1].position})}),O},s)}return f},zk=function(t,r,n,i){var a=n.children,o=n.width,u=n.margin,c=o-(u.left||0)-(u.right||0),l=_y({children:a,legendWidth:c});if(l){var f=i||{},s=f.width,p=f.height,h=l.align,v=l.verticalAlign,d=l.layout;if((d==="vertical"||d==="horizontal"&&v==="middle")&&h!=="center"&&L(t[h]))return ye(ye({},t),{},_r({},h,t[h]+(s||0)));if((d==="horizontal"||d==="vertical"&&h==="center")&&v!=="middle"&&L(t[v]))return ye(ye({},t),{},_r({},v,t[v]+(p||0)))}return t},Uk=function(t,r,n){return Y(r)?!0:t==="horizontal"?r==="yAxis":t==="vertical"||n==="x"?r==="xAxis":n==="y"?r==="yAxis":!0},$y=function(t,r,n,i,a){var o=r.props.children,u=Ge(o,xi).filter(function(l){return Uk(i,a,l.props.direction)});if(u&&u.length){var c=u.map(function(l){return l.props.dataKey});return t.reduce(function(l,f){var s=Se(f,n);if(Y(s))return l;var p=Array.isArray(s)?[yo(s),vo(s)]:[s,s],h=c.reduce(function(v,d){var y=Se(f,d,0),b=p[0]-Math.abs(Array.isArray(y)?y[0]:y),w=p[1]+Math.abs(Array.isArray(y)?y[1]:y);return[Math.min(b,v[0]),Math.max(w,v[1])]},[1/0,-1/0]);return[Math.min(h[0],l[0]),Math.max(h[1],l[1])]},[1/0,-1/0])}return null},qk=function(t,r,n,i,a){var o=r.map(function(u){return $y(t,u,n,a,i)}).filter(function(u){return!Y(u)});return o&&o.length?o.reduce(function(u,c){return[Math.min(u[0],c[0]),Math.max(u[1],c[1])]},[1/0,-1/0]):null},Ty=function(t,r,n,i,a){var o=r.map(function(c){var l=c.props.dataKey;return n==="number"&&l&&$y(t,c,l,i)||_n(t,l,n,a)});if(n==="number")return o.reduce(function(c,l){return[Math.min(c[0],l[0]),Math.max(c[1],l[1])]},[1/0,-1/0]);var u={};return o.reduce(function(c,l){for(var f=0,s=l.length;f<s;f++)u[l[f]]||(u[l[f]]=!0,c.push(l[f]));return c},[])},Ey=function(t,r){return t==="horizontal"&&r==="xAxis"||t==="vertical"&&r==="yAxis"||t==="centric"&&r==="angleAxis"||t==="radial"&&r==="radiusAxis"},jy=function(t,r,n,i){if(i)return t.map(function(c){return c.coordinate});var a,o,u=t.map(function(c){return c.coordinate===r&&(a=!0),c.coordinate===n&&(o=!0),c.coordinate});return a||u.push(r),o||u.push(n),u},xt=function(t,r,n){if(!t)return null;var i=t.scale,a=t.duplicateDomain,o=t.type,u=t.range,c=t.realScaleType==="scaleBand"?i.bandwidth()/2:2,l=(r||n)&&o==="category"&&i.bandwidth?i.bandwidth()/c:0;if(l=t.axisType==="angleAxis"&&(u==null?void 0:u.length)>=2?Ce(u[0]-u[1])*2*l:l,r&&(t.ticks||t.niceTicks)){var f=(t.ticks||t.niceTicks).map(function(s){var p=a?a.indexOf(s):s;return{coordinate:i(p)+l,value:s,offset:l}});return f.filter(function(s){return!hi(s.coordinate)})}return t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(s,p){return{coordinate:i(s)+l,value:s,index:p,offset:l}}):i.ticks&&!n?i.ticks(t.tickCount).map(function(s){return{coordinate:i(s)+l,value:s,offset:l}}):i.domain().map(function(s,p){return{coordinate:i(s)+l,value:a?a[s]:s,index:p,offset:l}})},iu=new WeakMap,Mi=function(t,r){if(typeof r!="function")return t;iu.has(t)||iu.set(t,new WeakMap);var n=iu.get(t);if(n.has(r))return n.get(r);var i=function(){t.apply(void 0,arguments),r.apply(void 0,arguments)};return n.set(r,i),i},My=function(t,r,n){var i=t.scale,a=t.type,o=t.layout,u=t.axisType;if(i==="auto")return o==="radial"&&u==="radiusAxis"?{scale:Ln(),realScaleType:"band"}:o==="radial"&&u==="angleAxis"?{scale:aa(),realScaleType:"linear"}:a==="category"&&r&&(r.indexOf("LineChart")>=0||r.indexOf("AreaChart")>=0||r.indexOf("ComposedChart")>=0&&!n)?{scale:Pn(),realScaleType:"point"}:a==="category"?{scale:Ln(),realScaleType:"band"}:{scale:aa(),realScaleType:"linear"};if(rr(i)){var c="scale".concat(eo(i));return{scale:(up[c]||Pn)(),realScaleType:up[c]?c:"point"}}return X(i)?{scale:i}:{scale:Pn(),realScaleType:"point"}},yp=1e-4,Iy=function(t){var r=t.domain();if(!(!r||r.length<=2)){var n=r.length,i=t.range(),a=Math.min(i[0],i[1])-yp,o=Math.max(i[0],i[1])+yp,u=t(r[0]),c=t(r[n-1]);(u<a||u>o||c<a||c>o)&&t.domain([r[0],r[n-1]])}},Hk=function(t,r){if(!t)return null;for(var n=0,i=t.length;n<i;n++)if(t[n].item===r)return t[n].position;return null},Kk=function(t,r){if(!r||r.length!==2||!L(r[0])||!L(r[1]))return t;var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]),a=[t[0],t[1]];return(!L(t[0])||t[0]<n)&&(a[0]=n),(!L(t[1])||t[1]>i)&&(a[1]=i),a[0]>i&&(a[0]=i),a[1]<n&&(a[1]=n),a},Gk=function(t){var r=t.length;if(!(r<=0))for(var n=0,i=t[0].length;n<i;++n)for(var a=0,o=0,u=0;u<r;++u){var c=hi(t[u][n][1])?t[u][n][0]:t[u][n][1];c>=0?(t[u][n][0]=a,t[u][n][1]=a+c,a=t[u][n][1]):(t[u][n][0]=o,t[u][n][1]=o+c,o=t[u][n][1])}},Vk=function(t){var r=t.length;if(!(r<=0))for(var n=0,i=t[0].length;n<i;++n)for(var a=0,o=0;o<r;++o){var u=hi(t[o][n][1])?t[o][n][0]:t[o][n][1];u>=0?(t[o][n][0]=a,t[o][n][1]=a+u,a=t[o][n][1]):(t[o][n][0]=0,t[o][n][1]=0)}},Xk={sign:Gk,expand:mO,none:$r,silhouette:gO,wiggle:bO,positive:Vk},Yk=function(t,r,n){var i=r.map(function(u){return u.props.dataKey}),a=Xk[n],o=yO().keys(i).value(function(u,c){return+Se(u,c,0)}).order(Au).offset(a);return o(t)},Zk=function(t,r,n,i,a,o){if(!t)return null;var u=o?r.reverse():r,c={},l=u.reduce(function(s,p){var h,v=(h=p.type)!==null&&h!==void 0&&h.defaultProps?ye(ye({},p.type.defaultProps),p.props):p.props,d=v.stackId,y=v.hide;if(y)return s;var b=v[n],w=s[b]||{hasStack:!1,stackGroups:{}};if(Pe(d)){var x=w.stackGroups[d]||{numericAxisId:n,cateAxisId:i,items:[]};x.items.push(p),w.hasStack=!0,w.stackGroups[d]=x}else w.stackGroups[nn("_stackId_")]={numericAxisId:n,cateAxisId:i,items:[p]};return ye(ye({},s),{},_r({},b,w))},c),f={};return Object.keys(l).reduce(function(s,p){var h=l[p];if(h.hasStack){var v={};h.stackGroups=Object.keys(h.stackGroups).reduce(function(d,y){var b=h.stackGroups[y];return ye(ye({},d),{},_r({},y,{numericAxisId:n,cateAxisId:i,items:b.items,stackedData:Yk(t,b.items,a)}))},v)}return ye(ye({},s),{},_r({},p,h))},f)},Cy=function(t,r){var n=r.realScaleType,i=r.type,a=r.tickCount,o=r.originalDomain,u=r.allowDecimals,c=n||r.scale;if(c!=="auto"&&c!=="linear")return null;if(a&&i==="number"&&o&&(o[0]==="auto"||o[1]==="auto")){var l=t.domain();if(!l.length)return null;var f=pk(l,a,u);return t.domain([yo(f),vo(f)]),{niceTicks:f}}if(a&&i==="number"){var s=t.domain(),p=hk(s,a,u);return{niceTicks:p}}return null};function mp(e){var t=e.axis,r=e.ticks,n=e.bandSize,i=e.entry,a=e.index,o=e.dataKey;if(t.type==="category"){if(!t.allowDuplicatedCategory&&t.dataKey&&!Y(i[t.dataKey])){var u=Li(r,"value",i[t.dataKey]);if(u)return u.coordinate+n/2}return r[a]?r[a].coordinate+n/2:null}var c=Se(i,Y(o)?t.dataKey:o);return Y(c)?null:t.scale(c)}var gp=function(t){var r=t.axis,n=t.ticks,i=t.offset,a=t.bandSize,o=t.entry,u=t.index;if(r.type==="category")return n[u]?n[u].coordinate+i:null;var c=Se(o,r.dataKey,r.domain[u]);return Y(c)?null:r.scale(c)-a/2+i},Jk=function(t){var r=t.numericAxis,n=r.scale.domain();if(r.type==="number"){var i=Math.min(n[0],n[1]),a=Math.max(n[0],n[1]);return i<=0&&a>=0?0:a<0?a:i}return n[0]},Qk=function(t,r){var n,i=(n=t.type)!==null&&n!==void 0&&n.defaultProps?ye(ye({},t.type.defaultProps),t.props):t.props,a=i.stackId;if(Pe(a)){var o=r[a];if(o){var u=o.items.indexOf(t);return u>=0?o.stackedData[u]:null}}return null},e2=function(t){return t.reduce(function(r,n){return[yo(n.concat([r[0]]).filter(L)),vo(n.concat([r[1]]).filter(L))]},[1/0,-1/0])},ky=function(t,r,n){return Object.keys(t).reduce(function(i,a){var o=t[a],u=o.stackedData,c=u.reduce(function(l,f){var s=e2(f.slice(r,n+1));return[Math.min(l[0],s[0]),Math.max(l[1],s[1])]},[1/0,-1/0]);return[Math.min(c[0],i[0]),Math.max(c[1],i[1])]},[1/0,-1/0]).map(function(i){return i===1/0||i===-1/0?0:i})},bp=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,xp=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,Qu=function(t,r,n){if(X(t))return t(r,n);if(!Array.isArray(t))return r;var i=[];if(L(t[0]))i[0]=n?t[0]:Math.min(t[0],r[0]);else if(bp.test(t[0])){var a=+bp.exec(t[0])[1];i[0]=r[0]-a}else X(t[0])?i[0]=t[0](r[0]):i[0]=r[0];if(L(t[1]))i[1]=n?t[1]:Math.max(t[1],r[1]);else if(xp.test(t[1])){var o=+xp.exec(t[1])[1];i[1]=r[1]+o}else X(t[1])?i[1]=t[1](r[1]):i[1]=r[1];return i},pa=function(t,r,n){if(t&&t.scale&&t.scale.bandwidth){var i=t.scale.bandwidth();if(!n||i>0)return i}if(t&&r&&r.length>=2){for(var a=ll(r,function(s){return s.coordinate}),o=1/0,u=1,c=a.length;u<c;u++){var l=a[u],f=a[u-1];o=Math.min((l.coordinate||0)-(f.coordinate||0),o)}return o===1/0?0:o}return n?void 0:0},wp=function(t,r,n){return!t||!t.length||bi(t,Ke(n,"type.defaultProps.domain"))?r:t},Dy=function(t,r){var n=t.type.defaultProps?ye(ye({},t.type.defaultProps),t.props):t.props,i=n.dataKey,a=n.name,o=n.unit,u=n.formatter,c=n.tooltipType,l=n.chartType,f=n.hide;return ye(ye({},H(t,!1)),{},{dataKey:i,unit:o,formatter:u,name:a||i,color:Nl(t),value:Se(r,i),type:c,payload:r,chartType:l,hide:f})};function Gn(e){"@babel/helpers - typeof";return Gn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Gn(e)}function Op(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function mt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Op(Object(r),!0).forEach(function(n){Ny(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Op(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Ny(e,t,r){return t=t2(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function t2(e){var t=r2(e,"string");return Gn(t)=="symbol"?t:t+""}function r2(e,t){if(Gn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Gn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function n2(e,t){return u2(e)||o2(e,t)||a2(e,t)||i2()}function i2(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function a2(e,t){if(e){if(typeof e=="string")return Ap(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ap(e,t)}}function Ap(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function o2(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){l=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return u}}function u2(e){if(Array.isArray(e))return e}var ha=Math.PI/180,c2=function(t){return t*180/Math.PI},se=function(t,r,n,i){return{x:t+Math.cos(-ha*i)*n,y:r+Math.sin(-ha*i)*n}},Ry=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(t-(n.left||0)-(n.right||0)),Math.abs(r-(n.top||0)-(n.bottom||0)))/2},l2=function(t,r,n,i,a){var o=t.width,u=t.height,c=t.startAngle,l=t.endAngle,f=ke(t.cx,o,o/2),s=ke(t.cy,u,u/2),p=Ry(o,u,n),h=ke(t.innerRadius,p,0),v=ke(t.outerRadius,p,p*.8),d=Object.keys(r);return d.reduce(function(y,b){var w=r[b],x=w.domain,A=w.reversed,m;if(Y(w.range))i==="angleAxis"?m=[c,l]:i==="radiusAxis"&&(m=[h,v]),A&&(m=[m[1],m[0]]);else{m=w.range;var g=m,O=n2(g,2);c=O[0],l=O[1]}var P=My(w,a),_=P.realScaleType,E=P.scale;E.domain(x).range(m),Iy(E);var $=Cy(E,mt(mt({},w),{},{realScaleType:_})),T=mt(mt(mt({},w),$),{},{range:m,radius:v,realScaleType:_,scale:E,cx:f,cy:s,innerRadius:h,outerRadius:v,startAngle:c,endAngle:l});return mt(mt({},y),{},Ny({},b,T))},{})},s2=function(t,r){var n=t.x,i=t.y,a=r.x,o=r.y;return Math.sqrt(Math.pow(n-a,2)+Math.pow(i-o,2))},f2=function(t,r){var n=t.x,i=t.y,a=r.cx,o=r.cy,u=s2({x:n,y:i},{x:a,y:o});if(u<=0)return{radius:u};var c=(n-a)/u,l=Math.acos(c);return i>o&&(l=2*Math.PI-l),{radius:u,angle:c2(l),angleInRadian:l}},p2=function(t){var r=t.startAngle,n=t.endAngle,i=Math.floor(r/360),a=Math.floor(n/360),o=Math.min(i,a);return{startAngle:r-o*360,endAngle:n-o*360}},h2=function(t,r){var n=r.startAngle,i=r.endAngle,a=Math.floor(n/360),o=Math.floor(i/360),u=Math.min(a,o);return t+u*360},Sp=function(t,r){var n=t.x,i=t.y,a=f2({x:n,y:i},r),o=a.radius,u=a.angle,c=r.innerRadius,l=r.outerRadius;if(o<c||o>l)return!1;if(o===0)return!0;var f=p2(r),s=f.startAngle,p=f.endAngle,h=u,v;if(s<=p){for(;h>p;)h-=360;for(;h<s;)h+=360;v=h>=s&&h<=p}else{for(;h>s;)h-=360;for(;h<p;)h+=360;v=h>=p&&h<=s}return v?mt(mt({},r),{},{radius:o,angle:h2(h,r)}):null},Ly=function(t){return!R.isValidElement(t)&&!X(t)&&typeof t!="boolean"?t.className:""};function Vn(e){"@babel/helpers - typeof";return Vn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Vn(e)}var d2=["offset"];function v2(e){return b2(e)||g2(e)||m2(e)||y2()}function y2(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function m2(e,t){if(e){if(typeof e=="string")return ec(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ec(e,t)}}function g2(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function b2(e){if(Array.isArray(e))return ec(e)}function ec(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function x2(e,t){if(e==null)return{};var r=w2(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function w2(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Pp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ae(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Pp(Object(r),!0).forEach(function(n){O2(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Pp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function O2(e,t,r){return t=A2(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function A2(e){var t=S2(e,"string");return Vn(t)=="symbol"?t:t+""}function S2(e,t){if(Vn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Vn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Xn(){return Xn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Xn.apply(this,arguments)}var P2=function(t){var r=t.value,n=t.formatter,i=Y(t.children)?r:t.children;return X(n)?n(i):i},_2=function(t,r){var n=Ce(r-t),i=Math.min(Math.abs(r-t),360);return n*i},$2=function(t,r,n){var i=t.position,a=t.viewBox,o=t.offset,u=t.className,c=a,l=c.cx,f=c.cy,s=c.innerRadius,p=c.outerRadius,h=c.startAngle,v=c.endAngle,d=c.clockWise,y=(s+p)/2,b=_2(h,v),w=b>=0?1:-1,x,A;i==="insideStart"?(x=h+w*o,A=d):i==="insideEnd"?(x=v-w*o,A=!d):i==="end"&&(x=v+w*o,A=d),A=b<=0?A:!A;var m=se(l,f,y,x),g=se(l,f,y,x+(A?1:-1)*359),O="M".concat(m.x,",").concat(m.y,`
    A`).concat(y,",").concat(y,",0,1,").concat(A?0:1,`,
    `).concat(g.x,",").concat(g.y),P=Y(t.id)?nn("recharts-radial-line-"):t.id;return S.createElement("text",Xn({},n,{dominantBaseline:"central",className:J("recharts-radial-bar-label",u)}),S.createElement("defs",null,S.createElement("path",{id:P,d:O})),S.createElement("textPath",{xlinkHref:"#".concat(P)},r))},T2=function(t){var r=t.viewBox,n=t.offset,i=t.position,a=r,o=a.cx,u=a.cy,c=a.innerRadius,l=a.outerRadius,f=a.startAngle,s=a.endAngle,p=(f+s)/2;if(i==="outside"){var h=se(o,u,l+n,p),v=h.x,d=h.y;return{x:v,y:d,textAnchor:v>=o?"start":"end",verticalAnchor:"middle"}}if(i==="center")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"middle"};if(i==="centerTop")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"start"};if(i==="centerBottom")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"end"};var y=(c+l)/2,b=se(o,u,y,p),w=b.x,x=b.y;return{x:w,y:x,textAnchor:"middle",verticalAnchor:"middle"}},E2=function(t){var r=t.viewBox,n=t.parentViewBox,i=t.offset,a=t.position,o=r,u=o.x,c=o.y,l=o.width,f=o.height,s=f>=0?1:-1,p=s*i,h=s>0?"end":"start",v=s>0?"start":"end",d=l>=0?1:-1,y=d*i,b=d>0?"end":"start",w=d>0?"start":"end";if(a==="top"){var x={x:u+l/2,y:c-s*i,textAnchor:"middle",verticalAnchor:h};return Ae(Ae({},x),n?{height:Math.max(c-n.y,0),width:l}:{})}if(a==="bottom"){var A={x:u+l/2,y:c+f+p,textAnchor:"middle",verticalAnchor:v};return Ae(Ae({},A),n?{height:Math.max(n.y+n.height-(c+f),0),width:l}:{})}if(a==="left"){var m={x:u-y,y:c+f/2,textAnchor:b,verticalAnchor:"middle"};return Ae(Ae({},m),n?{width:Math.max(m.x-n.x,0),height:f}:{})}if(a==="right"){var g={x:u+l+y,y:c+f/2,textAnchor:w,verticalAnchor:"middle"};return Ae(Ae({},g),n?{width:Math.max(n.x+n.width-g.x,0),height:f}:{})}var O=n?{width:l,height:f}:{};return a==="insideLeft"?Ae({x:u+y,y:c+f/2,textAnchor:w,verticalAnchor:"middle"},O):a==="insideRight"?Ae({x:u+l-y,y:c+f/2,textAnchor:b,verticalAnchor:"middle"},O):a==="insideTop"?Ae({x:u+l/2,y:c+p,textAnchor:"middle",verticalAnchor:v},O):a==="insideBottom"?Ae({x:u+l/2,y:c+f-p,textAnchor:"middle",verticalAnchor:h},O):a==="insideTopLeft"?Ae({x:u+y,y:c+p,textAnchor:w,verticalAnchor:v},O):a==="insideTopRight"?Ae({x:u+l-y,y:c+p,textAnchor:b,verticalAnchor:v},O):a==="insideBottomLeft"?Ae({x:u+y,y:c+f-p,textAnchor:w,verticalAnchor:h},O):a==="insideBottomRight"?Ae({x:u+l-y,y:c+f-p,textAnchor:b,verticalAnchor:h},O):Qr(a)&&(L(a.x)||Yt(a.x))&&(L(a.y)||Yt(a.y))?Ae({x:u+ke(a.x,l),y:c+ke(a.y,f),textAnchor:"end",verticalAnchor:"end"},O):Ae({x:u+l/2,y:c+f/2,textAnchor:"middle",verticalAnchor:"middle"},O)},j2=function(t){return"cx"in t&&L(t.cx)};function Te(e){var t=e.offset,r=t===void 0?5:t,n=x2(e,d2),i=Ae({offset:r},n),a=i.viewBox,o=i.position,u=i.value,c=i.children,l=i.content,f=i.className,s=f===void 0?"":f,p=i.textBreakAll;if(!a||Y(u)&&Y(c)&&!R.isValidElement(l)&&!X(l))return null;if(R.isValidElement(l))return R.cloneElement(l,i);var h;if(X(l)){if(h=R.createElement(l,i),R.isValidElement(h))return h}else h=P2(i);var v=j2(a),d=H(i,!0);if(v&&(o==="insideStart"||o==="insideEnd"||o==="end"))return $2(i,h,d);var y=v?T2(i):E2(i);return S.createElement(ir,Xn({className:J("recharts-label",s)},d,y,{breakAll:p}),h)}Te.displayName="Label";var By=function(t){var r=t.cx,n=t.cy,i=t.angle,a=t.startAngle,o=t.endAngle,u=t.r,c=t.radius,l=t.innerRadius,f=t.outerRadius,s=t.x,p=t.y,h=t.top,v=t.left,d=t.width,y=t.height,b=t.clockWise,w=t.labelViewBox;if(w)return w;if(L(d)&&L(y)){if(L(s)&&L(p))return{x:s,y:p,width:d,height:y};if(L(h)&&L(v))return{x:h,y:v,width:d,height:y}}return L(s)&&L(p)?{x:s,y:p,width:0,height:0}:L(r)&&L(n)?{cx:r,cy:n,startAngle:a||i||0,endAngle:o||i||0,innerRadius:l||0,outerRadius:f||c||u||0,clockWise:b}:t.viewBox?t.viewBox:{}},M2=function(t,r){return t?t===!0?S.createElement(Te,{key:"label-implicit",viewBox:r}):Pe(t)?S.createElement(Te,{key:"label-implicit",viewBox:r,value:t}):R.isValidElement(t)?t.type===Te?R.cloneElement(t,{key:"label-implicit",viewBox:r}):S.createElement(Te,{key:"label-implicit",content:t,viewBox:r}):X(t)?S.createElement(Te,{key:"label-implicit",content:t,viewBox:r}):Qr(t)?S.createElement(Te,Xn({viewBox:r},t,{key:"label-implicit"})):null:null},I2=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!t||!t.children&&n&&!t.label)return null;var i=t.children,a=By(t),o=Ge(i,Te).map(function(c,l){return R.cloneElement(c,{viewBox:r||a,key:"label-".concat(l)})});if(!n)return o;var u=M2(t.label,r||a);return[u].concat(v2(o))};Te.parseViewBox=By;Te.renderCallByParent=I2;function C2(e){var t=e==null?0:e.length;return t?e[t-1]:void 0}var k2=C2;const D2=oe(k2);function Yn(e){"@babel/helpers - typeof";return Yn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Yn(e)}var N2=["valueAccessor"],R2=["data","dataKey","clockWise","id","textBreakAll"];function L2(e){return z2(e)||W2(e)||F2(e)||B2()}function B2(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function F2(e,t){if(e){if(typeof e=="string")return tc(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tc(e,t)}}function W2(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function z2(e){if(Array.isArray(e))return tc(e)}function tc(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function da(){return da=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},da.apply(this,arguments)}function _p(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function $p(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?_p(Object(r),!0).forEach(function(n){U2(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):_p(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function U2(e,t,r){return t=q2(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function q2(e){var t=H2(e,"string");return Yn(t)=="symbol"?t:t+""}function H2(e,t){if(Yn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Yn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Tp(e,t){if(e==null)return{};var r=K2(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function K2(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var G2=function(t){return Array.isArray(t.value)?D2(t.value):t.value};function At(e){var t=e.valueAccessor,r=t===void 0?G2:t,n=Tp(e,N2),i=n.data,a=n.dataKey,o=n.clockWise,u=n.id,c=n.textBreakAll,l=Tp(n,R2);return!i||!i.length?null:S.createElement(te,{className:"recharts-label-list"},i.map(function(f,s){var p=Y(a)?r(f,s):Se(f&&f.payload,a),h=Y(u)?{}:{id:"".concat(u,"-").concat(s)};return S.createElement(Te,da({},H(f,!0),l,h,{parentViewBox:f.parentViewBox,value:p,textBreakAll:c,viewBox:Te.parseViewBox(Y(o)?f:$p($p({},f),{},{clockWise:o})),key:"label-".concat(s),index:s}))}))}At.displayName="LabelList";function V2(e,t){return e?e===!0?S.createElement(At,{key:"labelList-implicit",data:t}):S.isValidElement(e)||X(e)?S.createElement(At,{key:"labelList-implicit",data:t,content:e}):Qr(e)?S.createElement(At,da({data:t},e,{key:"labelList-implicit"})):null:null}function X2(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!e||!e.children&&r&&!e.label)return null;var n=e.children,i=Ge(n,At).map(function(o,u){return R.cloneElement(o,{data:t,key:"labelList-".concat(u)})});if(!r)return i;var a=V2(e.label,t);return[a].concat(L2(i))}At.renderCallByParent=X2;function Zn(e){"@babel/helpers - typeof";return Zn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Zn(e)}function rc(){return rc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},rc.apply(this,arguments)}function Ep(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function jp(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ep(Object(r),!0).forEach(function(n){Y2(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ep(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Y2(e,t,r){return t=Z2(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Z2(e){var t=J2(e,"string");return Zn(t)=="symbol"?t:t+""}function J2(e,t){if(Zn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Zn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Q2=function(t,r){var n=Ce(r-t),i=Math.min(Math.abs(r-t),359.999);return n*i},Ii=function(t){var r=t.cx,n=t.cy,i=t.radius,a=t.angle,o=t.sign,u=t.isExternal,c=t.cornerRadius,l=t.cornerIsExternal,f=c*(u?1:-1)+i,s=Math.asin(c/f)/ha,p=l?a:a+o*s,h=se(r,n,f,p),v=se(r,n,i,p),d=l?a-o*s:a,y=se(r,n,f*Math.cos(s*ha),d);return{center:h,circleTangency:v,lineTangency:y,theta:s}},Fy=function(t){var r=t.cx,n=t.cy,i=t.innerRadius,a=t.outerRadius,o=t.startAngle,u=t.endAngle,c=Q2(o,u),l=o+c,f=se(r,n,a,o),s=se(r,n,a,l),p="M ".concat(f.x,",").concat(f.y,`
    A `).concat(a,",").concat(a,`,0,
    `).concat(+(Math.abs(c)>180),",").concat(+(o>l),`,
    `).concat(s.x,",").concat(s.y,`
  `);if(i>0){var h=se(r,n,i,o),v=se(r,n,i,l);p+="L ".concat(v.x,",").concat(v.y,`
            A `).concat(i,",").concat(i,`,0,
            `).concat(+(Math.abs(c)>180),",").concat(+(o<=l),`,
            `).concat(h.x,",").concat(h.y," Z")}else p+="L ".concat(r,",").concat(n," Z");return p},eD=function(t){var r=t.cx,n=t.cy,i=t.innerRadius,a=t.outerRadius,o=t.cornerRadius,u=t.forceCornerRadius,c=t.cornerIsExternal,l=t.startAngle,f=t.endAngle,s=Ce(f-l),p=Ii({cx:r,cy:n,radius:a,angle:l,sign:s,cornerRadius:o,cornerIsExternal:c}),h=p.circleTangency,v=p.lineTangency,d=p.theta,y=Ii({cx:r,cy:n,radius:a,angle:f,sign:-s,cornerRadius:o,cornerIsExternal:c}),b=y.circleTangency,w=y.lineTangency,x=y.theta,A=c?Math.abs(l-f):Math.abs(l-f)-d-x;if(A<0)return u?"M ".concat(v.x,",").concat(v.y,`
        a`).concat(o,",").concat(o,",0,0,1,").concat(o*2,`,0
        a`).concat(o,",").concat(o,",0,0,1,").concat(-o*2,`,0
      `):Fy({cx:r,cy:n,innerRadius:i,outerRadius:a,startAngle:l,endAngle:f});var m="M ".concat(v.x,",").concat(v.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(s<0),",").concat(h.x,",").concat(h.y,`
    A`).concat(a,",").concat(a,",0,").concat(+(A>180),",").concat(+(s<0),",").concat(b.x,",").concat(b.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(s<0),",").concat(w.x,",").concat(w.y,`
  `);if(i>0){var g=Ii({cx:r,cy:n,radius:i,angle:l,sign:s,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),O=g.circleTangency,P=g.lineTangency,_=g.theta,E=Ii({cx:r,cy:n,radius:i,angle:f,sign:-s,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),$=E.circleTangency,T=E.lineTangency,I=E.theta,C=c?Math.abs(l-f):Math.abs(l-f)-_-I;if(C<0&&o===0)return"".concat(m,"L").concat(r,",").concat(n,"Z");m+="L".concat(T.x,",").concat(T.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(s<0),",").concat($.x,",").concat($.y,`
      A`).concat(i,",").concat(i,",0,").concat(+(C>180),",").concat(+(s>0),",").concat(O.x,",").concat(O.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(s<0),",").concat(P.x,",").concat(P.y,"Z")}else m+="L".concat(r,",").concat(n,"Z");return m},tD={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},Wy=function(t){var r=jp(jp({},tD),t),n=r.cx,i=r.cy,a=r.innerRadius,o=r.outerRadius,u=r.cornerRadius,c=r.forceCornerRadius,l=r.cornerIsExternal,f=r.startAngle,s=r.endAngle,p=r.className;if(o<a||f===s)return null;var h=J("recharts-sector",p),v=o-a,d=ke(u,v,0,!0),y;return d>0&&Math.abs(f-s)<360?y=eD({cx:n,cy:i,innerRadius:a,outerRadius:o,cornerRadius:Math.min(d,v/2),forceCornerRadius:c,cornerIsExternal:l,startAngle:f,endAngle:s}):y=Fy({cx:n,cy:i,innerRadius:a,outerRadius:o,startAngle:f,endAngle:s}),S.createElement("path",rc({},H(r,!0),{className:h,d:y,role:"img"}))};function Jn(e){"@babel/helpers - typeof";return Jn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Jn(e)}function nc(){return nc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},nc.apply(this,arguments)}function Mp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ip(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Mp(Object(r),!0).forEach(function(n){rD(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Mp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function rD(e,t,r){return t=nD(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function nD(e){var t=iD(e,"string");return Jn(t)=="symbol"?t:t+""}function iD(e,t){if(Jn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Jn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Cp={curveBasisClosed:aO,curveBasisOpen:oO,curveBasis:iO,curveBumpX:qw,curveBumpY:Hw,curveLinearClosed:uO,curveLinear:ro,curveMonotoneX:cO,curveMonotoneY:lO,curveNatural:sO,curveStep:fO,curveStepAfter:hO,curveStepBefore:pO},Ci=function(t){return t.x===+t.x&&t.y===+t.y},bn=function(t){return t.x},xn=function(t){return t.y},aD=function(t,r){if(X(t))return t;var n="curve".concat(eo(t));return(n==="curveMonotone"||n==="curveBump")&&r?Cp["".concat(n).concat(r==="vertical"?"Y":"X")]:Cp[n]||ro},oD=function(t){var r=t.type,n=r===void 0?"linear":r,i=t.points,a=i===void 0?[]:i,o=t.baseLine,u=t.layout,c=t.connectNulls,l=c===void 0?!1:c,f=aD(n,u),s=l?a.filter(function(d){return Ci(d)}):a,p;if(Array.isArray(o)){var h=l?o.filter(function(d){return Ci(d)}):o,v=s.map(function(d,y){return Ip(Ip({},d),{},{base:h[y]})});return u==="vertical"?p=Ai().y(xn).x1(bn).x0(function(d){return d.base.x}):p=Ai().x(bn).y1(xn).y0(function(d){return d.base.y}),p.defined(Ci).curve(f),p(v)}return u==="vertical"&&L(o)?p=Ai().y(xn).x1(bn).x0(o):L(o)?p=Ai().x(bn).y1(xn).y0(o):p=Dd().x(bn).y(xn),p.defined(Ci).curve(f),p(s)},va=function(t){var r=t.className,n=t.points,i=t.path,a=t.pathRef;if((!n||!n.length)&&!i)return null;var o=n&&n.length?oD(t):i;return R.createElement("path",nc({},H(t,!1),Bi(t),{className:J("recharts-curve",r),d:o,ref:a}))},zy={exports:{}},uD="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",cD=uD,lD=cD;function Uy(){}function qy(){}qy.resetWarningCache=Uy;var sD=function(){function e(n,i,a,o,u,c){if(c!==lD){var l=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw l.name="Invariant Violation",l}}e.isRequired=e;function t(){return e}var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:qy,resetWarningCache:Uy};return r.PropTypes=r,r};zy.exports=sD();var fD=zy.exports;const ne=oe(fD);var pD=Object.getOwnPropertyNames,hD=Object.getOwnPropertySymbols,dD=Object.prototype.hasOwnProperty;function kp(e,t){return function(n,i,a){return e(n,i,a)&&t(n,i,a)}}function ki(e){return function(r,n,i){if(!r||!n||typeof r!="object"||typeof n!="object")return e(r,n,i);var a=i.cache,o=a.get(r),u=a.get(n);if(o&&u)return o===n&&u===r;a.set(r,n),a.set(n,r);var c=e(r,n,i);return a.delete(r),a.delete(n),c}}function Dp(e){return pD(e).concat(hD(e))}var vD=Object.hasOwn||function(e,t){return dD.call(e,t)};function pr(e,t){return e===t||!e&&!t&&e!==e&&t!==t}var yD="__v",mD="__o",gD="_owner",Np=Object.getOwnPropertyDescriptor,Rp=Object.keys;function bD(e,t,r){var n=e.length;if(t.length!==n)return!1;for(;n-- >0;)if(!r.equals(e[n],t[n],n,n,e,t,r))return!1;return!0}function xD(e,t){return pr(e.getTime(),t.getTime())}function wD(e,t){return e.name===t.name&&e.message===t.message&&e.cause===t.cause&&e.stack===t.stack}function OD(e,t){return e===t}function Lp(e,t,r){var n=e.size;if(n!==t.size)return!1;if(!n)return!0;for(var i=new Array(n),a=e.entries(),o,u,c=0;(o=a.next())&&!o.done;){for(var l=t.entries(),f=!1,s=0;(u=l.next())&&!u.done;){if(i[s]){s++;continue}var p=o.value,h=u.value;if(r.equals(p[0],h[0],c,s,e,t,r)&&r.equals(p[1],h[1],p[0],h[0],e,t,r)){f=i[s]=!0;break}s++}if(!f)return!1;c++}return!0}var AD=pr;function SD(e,t,r){var n=Rp(e),i=n.length;if(Rp(t).length!==i)return!1;for(;i-- >0;)if(!Hy(e,t,r,n[i]))return!1;return!0}function wn(e,t,r){var n=Dp(e),i=n.length;if(Dp(t).length!==i)return!1;for(var a,o,u;i-- >0;)if(a=n[i],!Hy(e,t,r,a)||(o=Np(e,a),u=Np(t,a),(o||u)&&(!o||!u||o.configurable!==u.configurable||o.enumerable!==u.enumerable||o.writable!==u.writable)))return!1;return!0}function PD(e,t){return pr(e.valueOf(),t.valueOf())}function _D(e,t){return e.source===t.source&&e.flags===t.flags}function Bp(e,t,r){var n=e.size;if(n!==t.size)return!1;if(!n)return!0;for(var i=new Array(n),a=e.values(),o,u;(o=a.next())&&!o.done;){for(var c=t.values(),l=!1,f=0;(u=c.next())&&!u.done;){if(!i[f]&&r.equals(o.value,u.value,o.value,u.value,e,t,r)){l=i[f]=!0;break}f++}if(!l)return!1}return!0}function $D(e,t){var r=e.length;if(t.length!==r)return!1;for(;r-- >0;)if(e[r]!==t[r])return!1;return!0}function TD(e,t){return e.hostname===t.hostname&&e.pathname===t.pathname&&e.protocol===t.protocol&&e.port===t.port&&e.hash===t.hash&&e.username===t.username&&e.password===t.password}function Hy(e,t,r,n){return(n===gD||n===mD||n===yD)&&(e.$$typeof||t.$$typeof)?!0:vD(t,n)&&r.equals(e[n],t[n],n,n,e,t,r)}var ED="[object Arguments]",jD="[object Boolean]",MD="[object Date]",ID="[object Error]",CD="[object Map]",kD="[object Number]",DD="[object Object]",ND="[object RegExp]",RD="[object Set]",LD="[object String]",BD="[object URL]",FD=Array.isArray,Fp=typeof ArrayBuffer=="function"&&ArrayBuffer.isView?ArrayBuffer.isView:null,Wp=Object.assign,WD=Object.prototype.toString.call.bind(Object.prototype.toString);function zD(e){var t=e.areArraysEqual,r=e.areDatesEqual,n=e.areErrorsEqual,i=e.areFunctionsEqual,a=e.areMapsEqual,o=e.areNumbersEqual,u=e.areObjectsEqual,c=e.arePrimitiveWrappersEqual,l=e.areRegExpsEqual,f=e.areSetsEqual,s=e.areTypedArraysEqual,p=e.areUrlsEqual;return function(v,d,y){if(v===d)return!0;if(v==null||d==null)return!1;var b=typeof v;if(b!==typeof d)return!1;if(b!=="object")return b==="number"?o(v,d,y):b==="function"?i(v,d,y):!1;var w=v.constructor;if(w!==d.constructor)return!1;if(w===Object)return u(v,d,y);if(FD(v))return t(v,d,y);if(Fp!=null&&Fp(v))return s(v,d,y);if(w===Date)return r(v,d,y);if(w===RegExp)return l(v,d,y);if(w===Map)return a(v,d,y);if(w===Set)return f(v,d,y);var x=WD(v);return x===MD?r(v,d,y):x===ND?l(v,d,y):x===CD?a(v,d,y):x===RD?f(v,d,y):x===DD?typeof v.then!="function"&&typeof d.then!="function"&&u(v,d,y):x===BD?p(v,d,y):x===ID?n(v,d,y):x===ED?u(v,d,y):x===jD||x===kD||x===LD?c(v,d,y):!1}}function UD(e){var t=e.circular,r=e.createCustomConfig,n=e.strict,i={areArraysEqual:n?wn:bD,areDatesEqual:xD,areErrorsEqual:wD,areFunctionsEqual:OD,areMapsEqual:n?kp(Lp,wn):Lp,areNumbersEqual:AD,areObjectsEqual:n?wn:SD,arePrimitiveWrappersEqual:PD,areRegExpsEqual:_D,areSetsEqual:n?kp(Bp,wn):Bp,areTypedArraysEqual:n?wn:$D,areUrlsEqual:TD};if(r&&(i=Wp({},i,r(i))),t){var a=ki(i.areArraysEqual),o=ki(i.areMapsEqual),u=ki(i.areObjectsEqual),c=ki(i.areSetsEqual);i=Wp({},i,{areArraysEqual:a,areMapsEqual:o,areObjectsEqual:u,areSetsEqual:c})}return i}function qD(e){return function(t,r,n,i,a,o,u){return e(t,r,u)}}function HD(e){var t=e.circular,r=e.comparator,n=e.createState,i=e.equals,a=e.strict;if(n)return function(c,l){var f=n(),s=f.cache,p=s===void 0?t?new WeakMap:void 0:s,h=f.meta;return r(c,l,{cache:p,equals:i,meta:h,strict:a})};if(t)return function(c,l){return r(c,l,{cache:new WeakMap,equals:i,meta:void 0,strict:a})};var o={cache:void 0,equals:i,meta:void 0,strict:a};return function(c,l){return r(c,l,o)}}var KD=Lt();Lt({strict:!0});Lt({circular:!0});Lt({circular:!0,strict:!0});Lt({createInternalComparator:function(){return pr}});Lt({strict:!0,createInternalComparator:function(){return pr}});Lt({circular:!0,createInternalComparator:function(){return pr}});Lt({circular:!0,createInternalComparator:function(){return pr},strict:!0});function Lt(e){e===void 0&&(e={});var t=e.circular,r=t===void 0?!1:t,n=e.createInternalComparator,i=e.createState,a=e.strict,o=a===void 0?!1:a,u=UD(e),c=zD(u),l=n?n(c):qD(c);return HD({circular:r,comparator:c,createState:i,equals:l,strict:o})}function GD(e){typeof requestAnimationFrame<"u"&&requestAnimationFrame(e)}function zp(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,r=-1,n=function i(a){r<0&&(r=a),a-r>t?(e(a),r=-1):GD(i)};requestAnimationFrame(n)}function ic(e){"@babel/helpers - typeof";return ic=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ic(e)}function VD(e){return JD(e)||ZD(e)||YD(e)||XD()}function XD(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function YD(e,t){if(e){if(typeof e=="string")return Up(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Up(e,t)}}function Up(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function ZD(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function JD(e){if(Array.isArray(e))return e}function QD(){var e={},t=function(){return null},r=!1,n=function i(a){if(!r){if(Array.isArray(a)){if(!a.length)return;var o=a,u=VD(o),c=u[0],l=u.slice(1);if(typeof c=="number"){zp(i.bind(null,l),c);return}i(c),zp(i.bind(null,l));return}ic(a)==="object"&&(e=a,t(e)),typeof a=="function"&&a()}};return{stop:function(){r=!0},start:function(a){r=!1,n(a)},subscribe:function(a){return t=a,function(){t=function(){return null}}}}}function Qn(e){"@babel/helpers - typeof";return Qn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Qn(e)}function qp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Hp(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?qp(Object(r),!0).forEach(function(n){Ky(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):qp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Ky(e,t,r){return t=eN(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function eN(e){var t=tN(e,"string");return Qn(t)==="symbol"?t:String(t)}function tN(e,t){if(Qn(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Qn(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var rN=function(t,r){return[Object.keys(t),Object.keys(r)].reduce(function(n,i){return n.filter(function(a){return i.includes(a)})})},nN=function(t){return t},iN=function(t){return t.replace(/([A-Z])/g,function(r){return"-".concat(r.toLowerCase())})},$n=function(t,r){return Object.keys(r).reduce(function(n,i){return Hp(Hp({},n),{},Ky({},i,t(i,r[i])))},{})},Kp=function(t,r,n){return t.map(function(i){return"".concat(iN(i)," ").concat(r,"ms ").concat(n)}).join(",")};function aN(e,t){return cN(e)||uN(e,t)||Gy(e,t)||oN()}function oN(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function uN(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){l=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return u}}function cN(e){if(Array.isArray(e))return e}function lN(e){return pN(e)||fN(e)||Gy(e)||sN()}function sN(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Gy(e,t){if(e){if(typeof e=="string")return ac(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ac(e,t)}}function fN(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function pN(e){if(Array.isArray(e))return ac(e)}function ac(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var ya=1e-4,Vy=function(t,r){return[0,3*t,3*r-6*t,3*t-3*r+1]},Xy=function(t,r){return t.map(function(n,i){return n*Math.pow(r,i)}).reduce(function(n,i){return n+i})},Gp=function(t,r){return function(n){var i=Vy(t,r);return Xy(i,n)}},hN=function(t,r){return function(n){var i=Vy(t,r),a=[].concat(lN(i.map(function(o,u){return o*u}).slice(1)),[0]);return Xy(a,n)}},Vp=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var i=r[0],a=r[1],o=r[2],u=r[3];if(r.length===1)switch(r[0]){case"linear":i=0,a=0,o=1,u=1;break;case"ease":i=.25,a=.1,o=.25,u=1;break;case"ease-in":i=.42,a=0,o=1,u=1;break;case"ease-out":i=.42,a=0,o=.58,u=1;break;case"ease-in-out":i=0,a=0,o=.58,u=1;break;default:{var c=r[0].split("(");if(c[0]==="cubic-bezier"&&c[1].split(")")[0].split(",").length===4){var l=c[1].split(")")[0].split(",").map(function(y){return parseFloat(y)}),f=aN(l,4);i=f[0],a=f[1],o=f[2],u=f[3]}}}var s=Gp(i,o),p=Gp(a,u),h=hN(i,o),v=function(b){return b>1?1:b<0?0:b},d=function(b){for(var w=b>1?1:b,x=w,A=0;A<8;++A){var m=s(x)-w,g=h(x);if(Math.abs(m-w)<ya||g<ya)return p(x);x=v(x-m/g)}return p(x)};return d.isStepper=!1,d},dN=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=t.stiff,n=r===void 0?100:r,i=t.damping,a=i===void 0?8:i,o=t.dt,u=o===void 0?17:o,c=function(f,s,p){var h=-(f-s)*n,v=p*a,d=p+(h-v)*u/1e3,y=p*u/1e3+f;return Math.abs(y-s)<ya&&Math.abs(d)<ya?[s,0]:[y,d]};return c.isStepper=!0,c.dt=u,c},vN=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var i=r[0];if(typeof i=="string")switch(i){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return Vp(i);case"spring":return dN();default:if(i.split("(")[0]==="cubic-bezier")return Vp(i)}return typeof i=="function"?i:null};function ei(e){"@babel/helpers - typeof";return ei=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ei(e)}function Xp(e){return gN(e)||mN(e)||Yy(e)||yN()}function yN(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function mN(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function gN(e){if(Array.isArray(e))return uc(e)}function Yp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function je(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Yp(Object(r),!0).forEach(function(n){oc(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Yp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function oc(e,t,r){return t=bN(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function bN(e){var t=xN(e,"string");return ei(t)==="symbol"?t:String(t)}function xN(e,t){if(ei(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(ei(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function wN(e,t){return SN(e)||AN(e,t)||Yy(e,t)||ON()}function ON(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Yy(e,t){if(e){if(typeof e=="string")return uc(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uc(e,t)}}function uc(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function AN(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){l=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return u}}function SN(e){if(Array.isArray(e))return e}var ma=function(t,r,n){return t+(r-t)*n},cc=function(t){var r=t.from,n=t.to;return r!==n},PN=function e(t,r,n){var i=$n(function(a,o){if(cc(o)){var u=t(o.from,o.to,o.velocity),c=wN(u,2),l=c[0],f=c[1];return je(je({},o),{},{from:l,velocity:f})}return o},r);return n<1?$n(function(a,o){return cc(o)?je(je({},o),{},{velocity:ma(o.velocity,i[a].velocity,n),from:ma(o.from,i[a].from,n)}):o},r):e(t,i,n-1)};const _N=function(e,t,r,n,i){var a=rN(e,t),o=a.reduce(function(y,b){return je(je({},y),{},oc({},b,[e[b],t[b]]))},{}),u=a.reduce(function(y,b){return je(je({},y),{},oc({},b,{from:e[b],velocity:0,to:t[b]}))},{}),c=-1,l,f,s=function(){return null},p=function(){return $n(function(b,w){return w.from},u)},h=function(){return!Object.values(u).filter(cc).length},v=function(b){l||(l=b);var w=b-l,x=w/r.dt;u=PN(r,u,x),i(je(je(je({},e),t),p())),l=b,h()||(c=requestAnimationFrame(s))},d=function(b){f||(f=b);var w=(b-f)/n,x=$n(function(m,g){return ma.apply(void 0,Xp(g).concat([r(w)]))},o);if(i(je(je(je({},e),t),x)),w<1)c=requestAnimationFrame(s);else{var A=$n(function(m,g){return ma.apply(void 0,Xp(g).concat([r(1)]))},o);i(je(je(je({},e),t),A))}};return s=r.isStepper?v:d,function(){return requestAnimationFrame(s),function(){cancelAnimationFrame(c)}}};function Nr(e){"@babel/helpers - typeof";return Nr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Nr(e)}var $N=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function TN(e,t){if(e==null)return{};var r=EN(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function EN(e,t){if(e==null)return{};var r={},n=Object.keys(e),i,a;for(a=0;a<n.length;a++)i=n[a],!(t.indexOf(i)>=0)&&(r[i]=e[i]);return r}function au(e){return CN(e)||IN(e)||MN(e)||jN()}function jN(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function MN(e,t){if(e){if(typeof e=="string")return lc(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return lc(e,t)}}function IN(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function CN(e){if(Array.isArray(e))return lc(e)}function lc(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Zp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function rt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Zp(Object(r),!0).forEach(function(n){An(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Zp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function An(e,t,r){return t=Zy(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function kN(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Jp(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Zy(n.key),n)}}function DN(e,t,r){return t&&Jp(e.prototype,t),r&&Jp(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function Zy(e){var t=NN(e,"string");return Nr(t)==="symbol"?t:String(t)}function NN(e,t){if(Nr(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Nr(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function RN(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&sc(e,t)}function sc(e,t){return sc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},sc(e,t)}function LN(e){var t=BN();return function(){var n=ga(e),i;if(t){var a=ga(this).constructor;i=Reflect.construct(n,arguments,a)}else i=n.apply(this,arguments);return fc(this,i)}}function fc(e,t){if(t&&(Nr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return pc(e)}function pc(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function BN(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function ga(e){return ga=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ga(e)}var bo=function(e){RN(r,e);var t=LN(r);function r(n,i){var a;kN(this,r),a=t.call(this,n,i);var o=a.props,u=o.isActive,c=o.attributeName,l=o.from,f=o.to,s=o.steps,p=o.children,h=o.duration;if(a.handleStyleChange=a.handleStyleChange.bind(pc(a)),a.changeStyle=a.changeStyle.bind(pc(a)),!u||h<=0)return a.state={style:{}},typeof p=="function"&&(a.state={style:f}),fc(a);if(s&&s.length)a.state={style:s[0].style};else if(l){if(typeof p=="function")return a.state={style:l},fc(a);a.state={style:c?An({},c,l):l}}else a.state={style:{}};return a}return DN(r,[{key:"componentDidMount",value:function(){var i=this.props,a=i.isActive,o=i.canBegin;this.mounted=!0,!(!a||!o)&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(i){var a=this.props,o=a.isActive,u=a.canBegin,c=a.attributeName,l=a.shouldReAnimate,f=a.to,s=a.from,p=this.state.style;if(u){if(!o){var h={style:c?An({},c,f):f};this.state&&p&&(c&&p[c]!==f||!c&&p!==f)&&this.setState(h);return}if(!(KD(i.to,f)&&i.canBegin&&i.isActive)){var v=!i.canBegin||!i.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var d=v||l?s:i.to;if(this.state&&p){var y={style:c?An({},c,d):d};(c&&p[c]!==d||!c&&p!==d)&&this.setState(y)}this.runAnimation(rt(rt({},this.props),{},{from:d,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var i=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),i&&i()}},{key:"handleStyleChange",value:function(i){this.changeStyle(i)}},{key:"changeStyle",value:function(i){this.mounted&&this.setState({style:i})}},{key:"runJSAnimation",value:function(i){var a=this,o=i.from,u=i.to,c=i.duration,l=i.easing,f=i.begin,s=i.onAnimationEnd,p=i.onAnimationStart,h=_N(o,u,vN(l),c,this.changeStyle),v=function(){a.stopJSAnimation=h()};this.manager.start([p,f,v,c,s])}},{key:"runStepAnimation",value:function(i){var a=this,o=i.steps,u=i.begin,c=i.onAnimationStart,l=o[0],f=l.style,s=l.duration,p=s===void 0?0:s,h=function(d,y,b){if(b===0)return d;var w=y.duration,x=y.easing,A=x===void 0?"ease":x,m=y.style,g=y.properties,O=y.onAnimationEnd,P=b>0?o[b-1]:y,_=g||Object.keys(m);if(typeof A=="function"||A==="spring")return[].concat(au(d),[a.runJSAnimation.bind(a,{from:P.style,to:m,duration:w,easing:A}),w]);var E=Kp(_,w,A),$=rt(rt(rt({},P.style),m),{},{transition:E});return[].concat(au(d),[$,w,O]).filter(nN)};return this.manager.start([c].concat(au(o.reduce(h,[f,Math.max(p,u)])),[i.onAnimationEnd]))}},{key:"runAnimation",value:function(i){this.manager||(this.manager=QD());var a=i.begin,o=i.duration,u=i.attributeName,c=i.to,l=i.easing,f=i.onAnimationStart,s=i.onAnimationEnd,p=i.steps,h=i.children,v=this.manager;if(this.unSubscribe=v.subscribe(this.handleStyleChange),typeof l=="function"||typeof h=="function"||l==="spring"){this.runJSAnimation(i);return}if(p.length>1){this.runStepAnimation(i);return}var d=u?An({},u,c):c,y=Kp(Object.keys(d),o,l);v.start([f,a,rt(rt({},d),{},{transition:y}),o,s])}},{key:"render",value:function(){var i=this.props,a=i.children;i.begin;var o=i.duration;i.attributeName,i.easing;var u=i.isActive;i.steps,i.from,i.to,i.canBegin,i.onAnimationEnd,i.shouldReAnimate,i.onAnimationReStart;var c=TN(i,$N),l=R.Children.count(a),f=this.state.style;if(typeof a=="function")return a(f);if(!u||l===0||o<=0)return a;var s=function(h){var v=h.props,d=v.style,y=d===void 0?{}:d,b=v.className,w=R.cloneElement(h,rt(rt({},c),{},{style:rt(rt({},y),f),className:b}));return w};return l===1?s(R.Children.only(a)):S.createElement("div",null,R.Children.map(a,function(p){return s(p)}))}}]),r}(R.PureComponent);bo.displayName="Animate";bo.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}};bo.propTypes={from:ne.oneOfType([ne.object,ne.string]),to:ne.oneOfType([ne.object,ne.string]),attributeName:ne.string,duration:ne.number,begin:ne.number,easing:ne.oneOfType([ne.string,ne.func]),steps:ne.arrayOf(ne.shape({duration:ne.number.isRequired,style:ne.object.isRequired,easing:ne.oneOfType([ne.oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),ne.func]),properties:ne.arrayOf("string"),onAnimationEnd:ne.func})),children:ne.oneOfType([ne.node,ne.func]),isActive:ne.bool,canBegin:ne.bool,onAnimationEnd:ne.func,shouldReAnimate:ne.bool,onAnimationStart:ne.func,onAnimationReStart:ne.func};const ur=bo;function ti(e){"@babel/helpers - typeof";return ti=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ti(e)}function ba(){return ba=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ba.apply(this,arguments)}function FN(e,t){return qN(e)||UN(e,t)||zN(e,t)||WN()}function WN(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function zN(e,t){if(e){if(typeof e=="string")return Qp(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Qp(e,t)}}function Qp(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function UN(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){l=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return u}}function qN(e){if(Array.isArray(e))return e}function eh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function th(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?eh(Object(r),!0).forEach(function(n){HN(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function HN(e,t,r){return t=KN(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function KN(e){var t=GN(e,"string");return ti(t)=="symbol"?t:t+""}function GN(e,t){if(ti(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(ti(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var rh=function(t,r,n,i,a){var o=Math.min(Math.abs(n)/2,Math.abs(i)/2),u=i>=0?1:-1,c=n>=0?1:-1,l=i>=0&&n>=0||i<0&&n<0?1:0,f;if(o>0&&a instanceof Array){for(var s=[0,0,0,0],p=0,h=4;p<h;p++)s[p]=a[p]>o?o:a[p];f="M".concat(t,",").concat(r+u*s[0]),s[0]>0&&(f+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(l,",").concat(t+c*s[0],",").concat(r)),f+="L ".concat(t+n-c*s[1],",").concat(r),s[1]>0&&(f+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(l,`,
        `).concat(t+n,",").concat(r+u*s[1])),f+="L ".concat(t+n,",").concat(r+i-u*s[2]),s[2]>0&&(f+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(l,`,
        `).concat(t+n-c*s[2],",").concat(r+i)),f+="L ".concat(t+c*s[3],",").concat(r+i),s[3]>0&&(f+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(l,`,
        `).concat(t,",").concat(r+i-u*s[3])),f+="Z"}else if(o>0&&a===+a&&a>0){var v=Math.min(o,a);f="M ".concat(t,",").concat(r+u*v,`
            A `).concat(v,",").concat(v,",0,0,").concat(l,",").concat(t+c*v,",").concat(r,`
            L `).concat(t+n-c*v,",").concat(r,`
            A `).concat(v,",").concat(v,",0,0,").concat(l,",").concat(t+n,",").concat(r+u*v,`
            L `).concat(t+n,",").concat(r+i-u*v,`
            A `).concat(v,",").concat(v,",0,0,").concat(l,",").concat(t+n-c*v,",").concat(r+i,`
            L `).concat(t+c*v,",").concat(r+i,`
            A `).concat(v,",").concat(v,",0,0,").concat(l,",").concat(t,",").concat(r+i-u*v," Z")}else f="M ".concat(t,",").concat(r," h ").concat(n," v ").concat(i," h ").concat(-n," Z");return f},VN=function(t,r){if(!t||!r)return!1;var n=t.x,i=t.y,a=r.x,o=r.y,u=r.width,c=r.height;if(Math.abs(u)>0&&Math.abs(c)>0){var l=Math.min(a,a+u),f=Math.max(a,a+u),s=Math.min(o,o+c),p=Math.max(o,o+c);return n>=l&&n<=f&&i>=s&&i<=p}return!1},XN={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},Rl=function(t){var r=th(th({},XN),t),n=R.useRef(),i=R.useState(-1),a=FN(i,2),o=a[0],u=a[1];R.useEffect(function(){if(n.current&&n.current.getTotalLength)try{var A=n.current.getTotalLength();A&&u(A)}catch{}},[]);var c=r.x,l=r.y,f=r.width,s=r.height,p=r.radius,h=r.className,v=r.animationEasing,d=r.animationDuration,y=r.animationBegin,b=r.isAnimationActive,w=r.isUpdateAnimationActive;if(c!==+c||l!==+l||f!==+f||s!==+s||f===0||s===0)return null;var x=J("recharts-rectangle",h);return w?S.createElement(ur,{canBegin:o>0,from:{width:f,height:s,x:c,y:l},to:{width:f,height:s,x:c,y:l},duration:d,animationEasing:v,isActive:w},function(A){var m=A.width,g=A.height,O=A.x,P=A.y;return S.createElement(ur,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:y,duration:d,isActive:b,easing:v},S.createElement("path",ba({},H(r,!0),{className:x,d:rh(O,P,m,g,p),ref:n})))}):S.createElement("path",ba({},H(r,!0),{className:x,d:rh(c,l,f,s,p)}))},YN=["points","className","baseLinePoints","connectNulls"];function br(){return br=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},br.apply(this,arguments)}function ZN(e,t){if(e==null)return{};var r=JN(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function JN(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function nh(e){return rR(e)||tR(e)||eR(e)||QN()}function QN(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function eR(e,t){if(e){if(typeof e=="string")return hc(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return hc(e,t)}}function tR(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function rR(e){if(Array.isArray(e))return hc(e)}function hc(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var ih=function(t){return t&&t.x===+t.x&&t.y===+t.y},nR=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],r=[[]];return t.forEach(function(n){ih(n)?r[r.length-1].push(n):r[r.length-1].length>0&&r.push([])}),ih(t[0])&&r[r.length-1].push(t[0]),r[r.length-1].length<=0&&(r=r.slice(0,-1)),r},Tn=function(t,r){var n=nR(t);r&&(n=[n.reduce(function(a,o){return[].concat(nh(a),nh(o))},[])]);var i=n.map(function(a){return a.reduce(function(o,u,c){return"".concat(o).concat(c===0?"M":"L").concat(u.x,",").concat(u.y)},"")}).join("");return n.length===1?"".concat(i,"Z"):i},iR=function(t,r,n){var i=Tn(t,n);return"".concat(i.slice(-1)==="Z"?i.slice(0,-1):i,"L").concat(Tn(r.reverse(),n).slice(1))},aR=function(t){var r=t.points,n=t.className,i=t.baseLinePoints,a=t.connectNulls,o=ZN(t,YN);if(!r||!r.length)return null;var u=J("recharts-polygon",n);if(i&&i.length){var c=o.stroke&&o.stroke!=="none",l=iR(r,i,a);return S.createElement("g",{className:u},S.createElement("path",br({},H(o,!0),{fill:l.slice(-1)==="Z"?o.fill:"none",stroke:"none",d:l})),c?S.createElement("path",br({},H(o,!0),{fill:"none",d:Tn(r,a)})):null,c?S.createElement("path",br({},H(o,!0),{fill:"none",d:Tn(i,a)})):null)}var f=Tn(r,a);return S.createElement("path",br({},H(o,!0),{fill:f.slice(-1)==="Z"?o.fill:"none",className:u,d:f}))};function dc(){return dc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},dc.apply(this,arguments)}var xo=function(t){var r=t.cx,n=t.cy,i=t.r,a=t.className,o=J("recharts-dot",a);return r===+r&&n===+n&&i===+i?R.createElement("circle",dc({},H(t,!1),Bi(t),{className:o,cx:r,cy:n,r:i})):null};function ri(e){"@babel/helpers - typeof";return ri=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ri(e)}var oR=["x","y","top","left","width","height","className"];function vc(){return vc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},vc.apply(this,arguments)}function ah(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function uR(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ah(Object(r),!0).forEach(function(n){cR(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ah(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function cR(e,t,r){return t=lR(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function lR(e){var t=sR(e,"string");return ri(t)=="symbol"?t:t+""}function sR(e,t){if(ri(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(ri(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function fR(e,t){if(e==null)return{};var r=pR(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function pR(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var hR=function(t,r,n,i,a,o){return"M".concat(t,",").concat(a,"v").concat(i,"M").concat(o,",").concat(r,"h").concat(n)},dR=function(t){var r=t.x,n=r===void 0?0:r,i=t.y,a=i===void 0?0:i,o=t.top,u=o===void 0?0:o,c=t.left,l=c===void 0?0:c,f=t.width,s=f===void 0?0:f,p=t.height,h=p===void 0?0:p,v=t.className,d=fR(t,oR),y=uR({x:n,y:a,top:u,left:l,width:s,height:h},d);return!L(n)||!L(a)||!L(s)||!L(h)||!L(u)||!L(l)?null:S.createElement("path",vc({},H(y,!0),{className:J("recharts-cross",v),d:hR(n,a,s,h,u,l)}))},vR=ho,yR=ly,mR=dt;function gR(e,t){return e&&e.length?vR(e,mR(t),yR):void 0}var bR=gR;const xR=oe(bR);var wR=ho,OR=dt,AR=sy;function SR(e,t){return e&&e.length?wR(e,OR(t),AR):void 0}var PR=SR;const _R=oe(PR);var $R=["cx","cy","angle","ticks","axisLine"],TR=["ticks","tick","angle","tickFormatter","stroke"];function Rr(e){"@babel/helpers - typeof";return Rr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Rr(e)}function En(){return En=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},En.apply(this,arguments)}function oh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function qt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?oh(Object(r),!0).forEach(function(n){wo(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):oh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function uh(e,t){if(e==null)return{};var r=ER(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function ER(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function jR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function ch(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Qy(n.key),n)}}function MR(e,t,r){return t&&ch(e.prototype,t),r&&ch(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function IR(e,t,r){return t=xa(t),CR(e,Jy()?Reflect.construct(t,r||[],xa(e).constructor):t.apply(e,r))}function CR(e,t){if(t&&(Rr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return kR(e)}function kR(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Jy(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Jy=function(){return!!e})()}function xa(e){return xa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},xa(e)}function DR(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&yc(e,t)}function yc(e,t){return yc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},yc(e,t)}function wo(e,t,r){return t=Qy(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Qy(e){var t=NR(e,"string");return Rr(t)=="symbol"?t:t+""}function NR(e,t){if(Rr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Rr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Oo=function(e){function t(){return jR(this,t),IR(this,t,arguments)}return DR(t,e),MR(t,[{key:"getTickValueCoord",value:function(n){var i=n.coordinate,a=this.props,o=a.angle,u=a.cx,c=a.cy;return se(u,c,i,o)}},{key:"getTickTextAnchor",value:function(){var n=this.props.orientation,i;switch(n){case"left":i="end";break;case"right":i="start";break;default:i="middle";break}return i}},{key:"getViewBox",value:function(){var n=this.props,i=n.cx,a=n.cy,o=n.angle,u=n.ticks,c=xR(u,function(f){return f.coordinate||0}),l=_R(u,function(f){return f.coordinate||0});return{cx:i,cy:a,startAngle:o,endAngle:o,innerRadius:l.coordinate||0,outerRadius:c.coordinate||0}}},{key:"renderAxisLine",value:function(){var n=this.props,i=n.cx,a=n.cy,o=n.angle,u=n.ticks,c=n.axisLine,l=uh(n,$R),f=u.reduce(function(v,d){return[Math.min(v[0],d.coordinate),Math.max(v[1],d.coordinate)]},[1/0,-1/0]),s=se(i,a,f[0],o),p=se(i,a,f[1],o),h=qt(qt(qt({},H(l,!1)),{},{fill:"none"},H(c,!1)),{},{x1:s.x,y1:s.y,x2:p.x,y2:p.y});return S.createElement("line",En({className:"recharts-polar-radius-axis-line"},h))}},{key:"renderTicks",value:function(){var n=this,i=this.props,a=i.ticks,o=i.tick,u=i.angle,c=i.tickFormatter,l=i.stroke,f=uh(i,TR),s=this.getTickTextAnchor(),p=H(f,!1),h=H(o,!1),v=a.map(function(d,y){var b=n.getTickValueCoord(d),w=qt(qt(qt(qt({textAnchor:s,transform:"rotate(".concat(90-u,", ").concat(b.x,", ").concat(b.y,")")},p),{},{stroke:"none",fill:l},h),{},{index:y},b),{},{payload:d});return S.createElement(te,En({className:J("recharts-polar-radius-axis-tick",Ly(o)),key:"tick-".concat(d.coordinate)},nr(n.props,d,y)),t.renderTickItem(o,w,c?c(d.value,y):d.value))});return S.createElement(te,{className:"recharts-polar-radius-axis-ticks"},v)}},{key:"render",value:function(){var n=this.props,i=n.ticks,a=n.axisLine,o=n.tick;return!i||!i.length?null:S.createElement(te,{className:J("recharts-polar-radius-axis",this.props.className)},a&&this.renderAxisLine(),o&&this.renderTicks(),Te.renderCallByParent(this.props,this.getViewBox()))}}],[{key:"renderTickItem",value:function(n,i,a){var o;return S.isValidElement(n)?o=S.cloneElement(n,i):X(n)?o=n(i):o=S.createElement(ir,En({},i,{className:"recharts-polar-radius-axis-tick-value"}),a),o}}])}(R.PureComponent);wo(Oo,"displayName","PolarRadiusAxis");wo(Oo,"axisType","radiusAxis");wo(Oo,"defaultProps",{type:"number",radiusAxisId:0,cx:0,cy:0,angle:0,orientation:"right",stroke:"#ccc",axisLine:!0,tick:!0,tickCount:5,allowDataOverflow:!1,scale:"auto",allowDuplicatedCategory:!0});function Lr(e){"@babel/helpers - typeof";return Lr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Lr(e)}function Xt(){return Xt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Xt.apply(this,arguments)}function lh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ht(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?lh(Object(r),!0).forEach(function(n){Ao(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):lh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function RR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function sh(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,tm(n.key),n)}}function LR(e,t,r){return t&&sh(e.prototype,t),r&&sh(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function BR(e,t,r){return t=wa(t),FR(e,em()?Reflect.construct(t,r||[],wa(e).constructor):t.apply(e,r))}function FR(e,t){if(t&&(Lr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return WR(e)}function WR(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function em(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(em=function(){return!!e})()}function wa(e){return wa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},wa(e)}function zR(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&mc(e,t)}function mc(e,t){return mc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},mc(e,t)}function Ao(e,t,r){return t=tm(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function tm(e){var t=UR(e,"string");return Lr(t)=="symbol"?t:t+""}function UR(e,t){if(Lr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Lr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var qR=Math.PI/180,fh=1e-5,So=function(e){function t(){return RR(this,t),BR(this,t,arguments)}return zR(t,e),LR(t,[{key:"getTickLineCoord",value:function(n){var i=this.props,a=i.cx,o=i.cy,u=i.radius,c=i.orientation,l=i.tickSize,f=l||8,s=se(a,o,u,n.coordinate),p=se(a,o,u+(c==="inner"?-1:1)*f,n.coordinate);return{x1:s.x,y1:s.y,x2:p.x,y2:p.y}}},{key:"getTickTextAnchor",value:function(n){var i=this.props.orientation,a=Math.cos(-n.coordinate*qR),o;return a>fh?o=i==="outer"?"start":"end":a<-fh?o=i==="outer"?"end":"start":o="middle",o}},{key:"renderAxisLine",value:function(){var n=this.props,i=n.cx,a=n.cy,o=n.radius,u=n.axisLine,c=n.axisLineType,l=Ht(Ht({},H(this.props,!1)),{},{fill:"none"},H(u,!1));if(c==="circle")return S.createElement(xo,Xt({className:"recharts-polar-angle-axis-line"},l,{cx:i,cy:a,r:o}));var f=this.props.ticks,s=f.map(function(p){return se(i,a,o,p.coordinate)});return S.createElement(aR,Xt({className:"recharts-polar-angle-axis-line"},l,{points:s}))}},{key:"renderTicks",value:function(){var n=this,i=this.props,a=i.ticks,o=i.tick,u=i.tickLine,c=i.tickFormatter,l=i.stroke,f=H(this.props,!1),s=H(o,!1),p=Ht(Ht({},f),{},{fill:"none"},H(u,!1)),h=a.map(function(v,d){var y=n.getTickLineCoord(v),b=n.getTickTextAnchor(v),w=Ht(Ht(Ht({textAnchor:b},f),{},{stroke:"none",fill:l},s),{},{index:d,payload:v,x:y.x2,y:y.y2});return S.createElement(te,Xt({className:J("recharts-polar-angle-axis-tick",Ly(o)),key:"tick-".concat(v.coordinate)},nr(n.props,v,d)),u&&S.createElement("line",Xt({className:"recharts-polar-angle-axis-tick-line"},p,y)),o&&t.renderTickItem(o,w,c?c(v.value,d):v.value))});return S.createElement(te,{className:"recharts-polar-angle-axis-ticks"},h)}},{key:"render",value:function(){var n=this.props,i=n.ticks,a=n.radius,o=n.axisLine;return a<=0||!i||!i.length?null:S.createElement(te,{className:J("recharts-polar-angle-axis",this.props.className)},o&&this.renderAxisLine(),this.renderTicks())}}],[{key:"renderTickItem",value:function(n,i,a){var o;return S.isValidElement(n)?o=S.cloneElement(n,i):X(n)?o=n(i):o=S.createElement(ir,Xt({},i,{className:"recharts-polar-angle-axis-tick-value"}),a),o}}])}(R.PureComponent);Ao(So,"displayName","PolarAngleAxis");Ao(So,"axisType","angleAxis");Ao(So,"defaultProps",{type:"category",angleAxisId:0,scale:"auto",cx:0,cy:0,orientation:"outer",axisLine:!0,tickLine:!0,tickSize:8,tick:!0,hide:!1,allowDuplicatedCategory:!0});var HR=av,KR=HR(Object.getPrototypeOf,Object),GR=KR,VR=$t,XR=GR,YR=Tt,ZR="[object Object]",JR=Function.prototype,QR=Object.prototype,rm=JR.toString,eL=QR.hasOwnProperty,tL=rm.call(Object);function rL(e){if(!YR(e)||VR(e)!=ZR)return!1;var t=XR(e);if(t===null)return!0;var r=eL.call(t,"constructor")&&t.constructor;return typeof r=="function"&&r instanceof r&&rm.call(r)==tL}var nL=rL;const iL=oe(nL);var aL=$t,oL=Tt,uL="[object Boolean]";function cL(e){return e===!0||e===!1||oL(e)&&aL(e)==uL}var lL=cL;const sL=oe(lL);function ni(e){"@babel/helpers - typeof";return ni=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ni(e)}function Oa(){return Oa=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Oa.apply(this,arguments)}function fL(e,t){return vL(e)||dL(e,t)||hL(e,t)||pL()}function pL(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function hL(e,t){if(e){if(typeof e=="string")return ph(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ph(e,t)}}function ph(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function dL(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){l=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return u}}function vL(e){if(Array.isArray(e))return e}function hh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function dh(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?hh(Object(r),!0).forEach(function(n){yL(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function yL(e,t,r){return t=mL(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function mL(e){var t=gL(e,"string");return ni(t)=="symbol"?t:t+""}function gL(e,t){if(ni(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(ni(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var vh=function(t,r,n,i,a){var o=n-i,u;return u="M ".concat(t,",").concat(r),u+="L ".concat(t+n,",").concat(r),u+="L ".concat(t+n-o/2,",").concat(r+a),u+="L ".concat(t+n-o/2-i,",").concat(r+a),u+="L ".concat(t,",").concat(r," Z"),u},bL={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},xL=function(t){var r=dh(dh({},bL),t),n=R.useRef(),i=R.useState(-1),a=fL(i,2),o=a[0],u=a[1];R.useEffect(function(){if(n.current&&n.current.getTotalLength)try{var x=n.current.getTotalLength();x&&u(x)}catch{}},[]);var c=r.x,l=r.y,f=r.upperWidth,s=r.lowerWidth,p=r.height,h=r.className,v=r.animationEasing,d=r.animationDuration,y=r.animationBegin,b=r.isUpdateAnimationActive;if(c!==+c||l!==+l||f!==+f||s!==+s||p!==+p||f===0&&s===0||p===0)return null;var w=J("recharts-trapezoid",h);return b?S.createElement(ur,{canBegin:o>0,from:{upperWidth:0,lowerWidth:0,height:p,x:c,y:l},to:{upperWidth:f,lowerWidth:s,height:p,x:c,y:l},duration:d,animationEasing:v,isActive:b},function(x){var A=x.upperWidth,m=x.lowerWidth,g=x.height,O=x.x,P=x.y;return S.createElement(ur,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:y,duration:d,easing:v},S.createElement("path",Oa({},H(r,!0),{className:w,d:vh(O,P,A,m,g),ref:n})))}):S.createElement("g",null,S.createElement("path",Oa({},H(r,!0),{className:w,d:vh(c,l,f,s,p)})))},wL=["option","shapeType","propTransformer","activeClassName","isActive"];function ii(e){"@babel/helpers - typeof";return ii=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ii(e)}function OL(e,t){if(e==null)return{};var r=AL(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function AL(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function yh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Aa(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?yh(Object(r),!0).forEach(function(n){SL(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function SL(e,t,r){return t=PL(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function PL(e){var t=_L(e,"string");return ii(t)=="symbol"?t:t+""}function _L(e,t){if(ii(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(ii(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function $L(e,t){return Aa(Aa({},t),e)}function TL(e,t){return e==="symbols"}function mh(e){var t=e.shapeType,r=e.elementProps;switch(t){case"rectangle":return S.createElement(Rl,r);case"trapezoid":return S.createElement(xL,r);case"sector":return S.createElement(Wy,r);case"symbols":if(TL(t))return S.createElement(tl,r);break;default:return null}}function EL(e){return R.isValidElement(e)?e.props:e}function nm(e){var t=e.option,r=e.shapeType,n=e.propTransformer,i=n===void 0?$L:n,a=e.activeClassName,o=a===void 0?"recharts-active-shape":a,u=e.isActive,c=OL(e,wL),l;if(R.isValidElement(t))l=R.cloneElement(t,Aa(Aa({},c),EL(t)));else if(X(t))l=t(c);else if(iL(t)&&!sL(t)){var f=i(t,c);l=S.createElement(mh,{shapeType:r,elementProps:f})}else{var s=c;l=S.createElement(mh,{shapeType:r,elementProps:s})}return u?S.createElement(te,{className:o},l):l}function Po(e,t){return t!=null&&"trapezoids"in e.props}function _o(e,t){return t!=null&&"sectors"in e.props}function ai(e,t){return t!=null&&"points"in e.props}function jL(e,t){var r,n,i=e.x===(t==null||(r=t.labelViewBox)===null||r===void 0?void 0:r.x)||e.x===t.x,a=e.y===(t==null||(n=t.labelViewBox)===null||n===void 0?void 0:n.y)||e.y===t.y;return i&&a}function ML(e,t){var r=e.endAngle===t.endAngle,n=e.startAngle===t.startAngle;return r&&n}function IL(e,t){var r=e.x===t.x,n=e.y===t.y,i=e.z===t.z;return r&&n&&i}function CL(e,t){var r;return Po(e,t)?r=jL:_o(e,t)?r=ML:ai(e,t)&&(r=IL),r}function kL(e,t){var r;return Po(e,t)?r="trapezoids":_o(e,t)?r="sectors":ai(e,t)&&(r="points"),r}function DL(e,t){if(Po(e,t)){var r;return(r=t.tooltipPayload)===null||r===void 0||(r=r[0])===null||r===void 0||(r=r.payload)===null||r===void 0?void 0:r.payload}if(_o(e,t)){var n;return(n=t.tooltipPayload)===null||n===void 0||(n=n[0])===null||n===void 0||(n=n.payload)===null||n===void 0?void 0:n.payload}return ai(e,t)?t.payload:{}}function NL(e){var t=e.activeTooltipItem,r=e.graphicalItem,n=e.itemData,i=kL(r,t),a=DL(r,t),o=n.filter(function(c,l){var f=bi(a,c),s=r.props[i].filter(function(v){var d=CL(r,t);return d(v,t)}),p=r.props[i].indexOf(s[s.length-1]),h=l===p;return f&&h}),u=n.indexOf(o[o.length-1]);return u}var Ri;function Br(e){"@babel/helpers - typeof";return Br=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Br(e)}function xr(){return xr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},xr.apply(this,arguments)}function gh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ce(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?gh(Object(r),!0).forEach(function(n){Ze(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function RL(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function bh(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,am(n.key),n)}}function LL(e,t,r){return t&&bh(e.prototype,t),r&&bh(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function BL(e,t,r){return t=Sa(t),FL(e,im()?Reflect.construct(t,r||[],Sa(e).constructor):t.apply(e,r))}function FL(e,t){if(t&&(Br(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return WL(e)}function WL(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function im(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(im=function(){return!!e})()}function Sa(e){return Sa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Sa(e)}function zL(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&gc(e,t)}function gc(e,t){return gc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},gc(e,t)}function Ze(e,t,r){return t=am(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function am(e){var t=UL(e,"string");return Br(t)=="symbol"?t:t+""}function UL(e,t){if(Br(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Br(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Bt=function(e){function t(r){var n;return RL(this,t),n=BL(this,t,[r]),Ze(n,"pieRef",null),Ze(n,"sectorRefs",[]),Ze(n,"id",nn("recharts-pie-")),Ze(n,"handleAnimationEnd",function(){var i=n.props.onAnimationEnd;n.setState({isAnimationFinished:!0}),X(i)&&i()}),Ze(n,"handleAnimationStart",function(){var i=n.props.onAnimationStart;n.setState({isAnimationFinished:!1}),X(i)&&i()}),n.state={isAnimationFinished:!r.isAnimationActive,prevIsAnimationActive:r.isAnimationActive,prevAnimationId:r.animationId,sectorToFocus:0},n}return zL(t,e),LL(t,[{key:"isActiveIndex",value:function(n){var i=this.props.activeIndex;return Array.isArray(i)?i.indexOf(n)!==-1:n===i}},{key:"hasActiveIndex",value:function(){var n=this.props.activeIndex;return Array.isArray(n)?n.length!==0:n||n===0}},{key:"renderLabels",value:function(n){var i=this.props.isAnimationActive;if(i&&!this.state.isAnimationFinished)return null;var a=this.props,o=a.label,u=a.labelLine,c=a.dataKey,l=a.valueKey,f=H(this.props,!1),s=H(o,!1),p=H(u,!1),h=o&&o.offsetRadius||20,v=n.map(function(d,y){var b=(d.startAngle+d.endAngle)/2,w=se(d.cx,d.cy,d.outerRadius+h,b),x=ce(ce(ce(ce({},f),d),{},{stroke:"none"},s),{},{index:y,textAnchor:t.getTextAnchor(w.x,d.cx)},w),A=ce(ce(ce(ce({},f),d),{},{fill:"none",stroke:d.fill},p),{},{index:y,points:[se(d.cx,d.cy,d.outerRadius,b),w]}),m=c;return Y(c)&&Y(l)?m="value":Y(c)&&(m=l),S.createElement(te,{key:"label-".concat(d.startAngle,"-").concat(d.endAngle,"-").concat(d.midAngle,"-").concat(y)},u&&t.renderLabelLineItem(u,A,"line"),t.renderLabelItem(o,x,Se(d,m)))});return S.createElement(te,{className:"recharts-pie-labels"},v)}},{key:"renderSectorsStatically",value:function(n){var i=this,a=this.props,o=a.activeShape,u=a.blendStroke,c=a.inactiveShape;return n.map(function(l,f){if((l==null?void 0:l.startAngle)===0&&(l==null?void 0:l.endAngle)===0&&n.length!==1)return null;var s=i.isActiveIndex(f),p=c&&i.hasActiveIndex()?c:null,h=s?o:p,v=ce(ce({},l),{},{stroke:u?l.fill:l.stroke,tabIndex:-1});return S.createElement(te,xr({ref:function(y){y&&!i.sectorRefs.includes(y)&&i.sectorRefs.push(y)},tabIndex:-1,className:"recharts-pie-sector"},nr(i.props,l,f),{key:"sector-".concat(l==null?void 0:l.startAngle,"-").concat(l==null?void 0:l.endAngle,"-").concat(l.midAngle,"-").concat(f)}),S.createElement(nm,xr({option:h,isActive:s,shapeType:"sector"},v)))})}},{key:"renderSectorsWithAnimation",value:function(){var n=this,i=this.props,a=i.sectors,o=i.isAnimationActive,u=i.animationBegin,c=i.animationDuration,l=i.animationEasing,f=i.animationId,s=this.state,p=s.prevSectors,h=s.prevIsAnimationActive;return S.createElement(ur,{begin:u,duration:c,isActive:o,easing:l,from:{t:0},to:{t:1},key:"pie-".concat(f,"-").concat(h),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},function(v){var d=v.t,y=[],b=a&&a[0],w=b.startAngle;return a.forEach(function(x,A){var m=p&&p[A],g=A>0?Ke(x,"paddingAngle",0):0;if(m){var O=qe(m.endAngle-m.startAngle,x.endAngle-x.startAngle),P=ce(ce({},x),{},{startAngle:w+g,endAngle:w+O(d)+g});y.push(P),w=P.endAngle}else{var _=x.endAngle,E=x.startAngle,$=qe(0,_-E),T=$(d),I=ce(ce({},x),{},{startAngle:w+g,endAngle:w+T+g});y.push(I),w=I.endAngle}}),S.createElement(te,null,n.renderSectorsStatically(y))})}},{key:"attachKeyboardHandlers",value:function(n){var i=this;n.onkeydown=function(a){if(!a.altKey)switch(a.key){case"ArrowLeft":{var o=++i.state.sectorToFocus%i.sectorRefs.length;i.sectorRefs[o].focus(),i.setState({sectorToFocus:o});break}case"ArrowRight":{var u=--i.state.sectorToFocus<0?i.sectorRefs.length-1:i.state.sectorToFocus%i.sectorRefs.length;i.sectorRefs[u].focus(),i.setState({sectorToFocus:u});break}case"Escape":{i.sectorRefs[i.state.sectorToFocus].blur(),i.setState({sectorToFocus:0});break}}}}},{key:"renderSectors",value:function(){var n=this.props,i=n.sectors,a=n.isAnimationActive,o=this.state.prevSectors;return a&&i&&i.length&&(!o||!bi(o,i))?this.renderSectorsWithAnimation():this.renderSectorsStatically(i)}},{key:"componentDidMount",value:function(){this.pieRef&&this.attachKeyboardHandlers(this.pieRef)}},{key:"render",value:function(){var n=this,i=this.props,a=i.hide,o=i.sectors,u=i.className,c=i.label,l=i.cx,f=i.cy,s=i.innerRadius,p=i.outerRadius,h=i.isAnimationActive,v=this.state.isAnimationFinished;if(a||!o||!o.length||!L(l)||!L(f)||!L(s)||!L(p))return null;var d=J("recharts-pie",u);return S.createElement(te,{tabIndex:this.props.rootTabIndex,className:d,ref:function(b){n.pieRef=b}},this.renderSectors(),c&&this.renderLabels(o),Te.renderCallByParent(this.props,null,!1),(!h||v)&&At.renderCallByParent(this.props,o,!1))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return i.prevIsAnimationActive!==n.isAnimationActive?{prevIsAnimationActive:n.isAnimationActive,prevAnimationId:n.animationId,curSectors:n.sectors,prevSectors:[],isAnimationFinished:!0}:n.isAnimationActive&&n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curSectors:n.sectors,prevSectors:i.curSectors,isAnimationFinished:!0}:n.sectors!==i.curSectors?{curSectors:n.sectors,isAnimationFinished:!0}:null}},{key:"getTextAnchor",value:function(n,i){return n>i?"start":n<i?"end":"middle"}},{key:"renderLabelLineItem",value:function(n,i,a){if(S.isValidElement(n))return S.cloneElement(n,i);if(X(n))return n(i);var o=J("recharts-pie-label-line",typeof n!="boolean"?n.className:"");return S.createElement(va,xr({},i,{key:a,type:"linear",className:o}))}},{key:"renderLabelItem",value:function(n,i,a){if(S.isValidElement(n))return S.cloneElement(n,i);var o=a;if(X(n)&&(o=n(i),S.isValidElement(o)))return o;var u=J("recharts-pie-label-text",typeof n!="boolean"&&!X(n)?n.className:"");return S.createElement(ir,xr({},i,{alignmentBaseline:"middle",className:u}),o)}}])}(R.PureComponent);Ri=Bt;Ze(Bt,"displayName","Pie");Ze(Bt,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",cx:"50%",cy:"50%",startAngle:0,endAngle:360,innerRadius:0,outerRadius:"80%",paddingAngle:0,labelLine:!0,hide:!1,minAngle:0,isAnimationActive:!lt.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",blendStroke:!1,rootTabIndex:0});Ze(Bt,"parseDeltaAngle",function(e,t){var r=Ce(t-e),n=Math.min(Math.abs(t-e),360);return r*n});Ze(Bt,"getRealPieData",function(e){var t=e.data,r=e.children,n=H(e,!1),i=Ge(r,fl);return t&&t.length?t.map(function(a,o){return ce(ce(ce({payload:a},n),a),i&&i[o]&&i[o].props)}):i&&i.length?i.map(function(a){return ce(ce({},n),a.props)}):[]});Ze(Bt,"parseCoordinateOfPie",function(e,t){var r=t.top,n=t.left,i=t.width,a=t.height,o=Ry(i,a),u=n+ke(e.cx,i,i/2),c=r+ke(e.cy,a,a/2),l=ke(e.innerRadius,o,0),f=ke(e.outerRadius,o,o*.8),s=e.maxRadius||Math.sqrt(i*i+a*a)/2;return{cx:u,cy:c,innerRadius:l,outerRadius:f,maxRadius:s}});Ze(Bt,"getComposedData",function(e){var t=e.item,r=e.offset,n=t.type.defaultProps!==void 0?ce(ce({},t.type.defaultProps),t.props):t.props,i=Ri.getRealPieData(n);if(!i||!i.length)return null;var a=n.cornerRadius,o=n.startAngle,u=n.endAngle,c=n.paddingAngle,l=n.dataKey,f=n.nameKey,s=n.valueKey,p=n.tooltipType,h=Math.abs(n.minAngle),v=Ri.parseCoordinateOfPie(n,r),d=Ri.parseDeltaAngle(o,u),y=Math.abs(d),b=l;Y(l)&&Y(s)?(ot(!1,`Use "dataKey" to specify the value of pie,
      the props "valueKey" will be deprecated in 1.1.0`),b="value"):Y(l)&&(ot(!1,`Use "dataKey" to specify the value of pie,
      the props "valueKey" will be deprecated in 1.1.0`),b=s);var w=i.filter(function(P){return Se(P,b,0)!==0}).length,x=(y>=360?w:w-1)*c,A=y-w*h-x,m=i.reduce(function(P,_){var E=Se(_,b,0);return P+(L(E)?E:0)},0),g;if(m>0){var O;g=i.map(function(P,_){var E=Se(P,b,0),$=Se(P,f,_),T=(L(E)?E:0)/m,I;_?I=O.endAngle+Ce(d)*c*(E!==0?1:0):I=o;var C=I+Ce(d)*((E!==0?h:0)+T*A),M=(I+C)/2,k=(v.innerRadius+v.outerRadius)/2,D=[{name:$,value:E,payload:P,dataKey:b,type:p}],B=se(v.cx,v.cy,k,M);return O=ce(ce(ce({percent:T,cornerRadius:a,name:$,tooltipPayload:D,midAngle:M,middleRadius:k,tooltipPosition:B},P),v),{},{value:Se(P,b),startAngle:I,endAngle:C,payload:P,paddingAngle:Ce(d)*c}),O})}return ce(ce({},v),{},{sectors:g,data:i})});var qL=Math.ceil,HL=Math.max;function KL(e,t,r,n){for(var i=-1,a=HL(qL((t-e)/(r||1)),0),o=Array(a);a--;)o[n?a:++i]=e,e+=r;return o}var GL=KL,VL=Sv,xh=1/0,XL=17976931348623157e292;function YL(e){if(!e)return e===0?e:0;if(e=VL(e),e===xh||e===-xh){var t=e<0?-1:1;return t*XL}return e===e?e:0}var om=YL,ZL=GL,JL=oo,ou=om;function QL(e){return function(t,r,n){return n&&typeof n!="number"&&JL(t,r,n)&&(r=n=void 0),t=ou(t),r===void 0?(r=t,t=0):r=ou(r),n=n===void 0?t<r?1:-1:ou(n),ZL(t,r,n,e)}}var eB=QL,tB=eB,rB=tB(),nB=rB;const Pa=oe(nB);function oi(e){"@babel/helpers - typeof";return oi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},oi(e)}function wh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Oh(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?wh(Object(r),!0).forEach(function(n){um(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):wh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function um(e,t,r){return t=iB(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function iB(e){var t=aB(e,"string");return oi(t)=="symbol"?t:t+""}function aB(e,t){if(oi(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(oi(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var oB=["Webkit","Moz","O","ms"],uB=function(t,r){if(!t)return null;var n=t.replace(/(\w)/,function(a){return a.toUpperCase()}),i=oB.reduce(function(a,o){return Oh(Oh({},a),{},um({},o+n,r))},{});return i[t]=r,i};function Fr(e){"@babel/helpers - typeof";return Fr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Fr(e)}function _a(){return _a=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},_a.apply(this,arguments)}function Ah(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function uu(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ah(Object(r),!0).forEach(function(n){ze(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ah(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function cB(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Sh(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,lm(n.key),n)}}function lB(e,t,r){return t&&Sh(e.prototype,t),r&&Sh(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function sB(e,t,r){return t=$a(t),fB(e,cm()?Reflect.construct(t,r||[],$a(e).constructor):t.apply(e,r))}function fB(e,t){if(t&&(Fr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return pB(e)}function pB(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function cm(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(cm=function(){return!!e})()}function $a(e){return $a=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},$a(e)}function hB(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&bc(e,t)}function bc(e,t){return bc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},bc(e,t)}function ze(e,t,r){return t=lm(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function lm(e){var t=dB(e,"string");return Fr(t)=="symbol"?t:t+""}function dB(e,t){if(Fr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Fr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var vB=function(t){var r=t.data,n=t.startIndex,i=t.endIndex,a=t.x,o=t.width,u=t.travellerWidth;if(!r||!r.length)return{};var c=r.length,l=Pn().domain(Pa(0,c)).range([a,a+o-u]),f=l.domain().map(function(s){return l(s)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:l(n),endX:l(i),scale:l,scaleValues:f}},Ph=function(t){return t.changedTouches&&!!t.changedTouches.length},Wr=function(e){function t(r){var n;return cB(this,t),n=sB(this,t,[r]),ze(n,"handleDrag",function(i){n.leaveTimer&&(clearTimeout(n.leaveTimer),n.leaveTimer=null),n.state.isTravellerMoving?n.handleTravellerMove(i):n.state.isSlideMoving&&n.handleSlideDrag(i)}),ze(n,"handleTouchMove",function(i){i.changedTouches!=null&&i.changedTouches.length>0&&n.handleDrag(i.changedTouches[0])}),ze(n,"handleDragEnd",function(){n.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var i=n.props,a=i.endIndex,o=i.onDragEnd,u=i.startIndex;o==null||o({endIndex:a,startIndex:u})}),n.detachDragEndListener()}),ze(n,"handleLeaveWrapper",function(){(n.state.isTravellerMoving||n.state.isSlideMoving)&&(n.leaveTimer=window.setTimeout(n.handleDragEnd,n.props.leaveTimeOut))}),ze(n,"handleEnterSlideOrTraveller",function(){n.setState({isTextActive:!0})}),ze(n,"handleLeaveSlideOrTraveller",function(){n.setState({isTextActive:!1})}),ze(n,"handleSlideDragStart",function(i){var a=Ph(i)?i.changedTouches[0]:i;n.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:a.pageX}),n.attachDragEndListener()}),n.travellerDragStartHandlers={startX:n.handleTravellerDragStart.bind(n,"startX"),endX:n.handleTravellerDragStart.bind(n,"endX")},n.state={},n}return hB(t,e),lB(t,[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(n){var i=n.startX,a=n.endX,o=this.state.scaleValues,u=this.props,c=u.gap,l=u.data,f=l.length-1,s=Math.min(i,a),p=Math.max(i,a),h=t.getIndexInRange(o,s),v=t.getIndexInRange(o,p);return{startIndex:h-h%c,endIndex:v===f?f:v-v%c}}},{key:"getTextOfTick",value:function(n){var i=this.props,a=i.data,o=i.tickFormatter,u=i.dataKey,c=Se(a[n],u,n);return X(o)?o(c,n):c}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(n){var i=this.state,a=i.slideMoveStartX,o=i.startX,u=i.endX,c=this.props,l=c.x,f=c.width,s=c.travellerWidth,p=c.startIndex,h=c.endIndex,v=c.onChange,d=n.pageX-a;d>0?d=Math.min(d,l+f-s-u,l+f-s-o):d<0&&(d=Math.max(d,l-o,l-u));var y=this.getIndex({startX:o+d,endX:u+d});(y.startIndex!==p||y.endIndex!==h)&&v&&v(y),this.setState({startX:o+d,endX:u+d,slideMoveStartX:n.pageX})}},{key:"handleTravellerDragStart",value:function(n,i){var a=Ph(i)?i.changedTouches[0]:i;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:n,brushMoveStartX:a.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(n){var i=this.state,a=i.brushMoveStartX,o=i.movingTravellerId,u=i.endX,c=i.startX,l=this.state[o],f=this.props,s=f.x,p=f.width,h=f.travellerWidth,v=f.onChange,d=f.gap,y=f.data,b={startX:this.state.startX,endX:this.state.endX},w=n.pageX-a;w>0?w=Math.min(w,s+p-h-l):w<0&&(w=Math.max(w,s-l)),b[o]=l+w;var x=this.getIndex(b),A=x.startIndex,m=x.endIndex,g=function(){var P=y.length-1;return o==="startX"&&(u>c?A%d===0:m%d===0)||u<c&&m===P||o==="endX"&&(u>c?m%d===0:A%d===0)||u>c&&m===P};this.setState(ze(ze({},o,l+w),"brushMoveStartX",n.pageX),function(){v&&g()&&v(x)})}},{key:"handleTravellerMoveKeyboard",value:function(n,i){var a=this,o=this.state,u=o.scaleValues,c=o.startX,l=o.endX,f=this.state[i],s=u.indexOf(f);if(s!==-1){var p=s+n;if(!(p===-1||p>=u.length)){var h=u[p];i==="startX"&&h>=l||i==="endX"&&h<=c||this.setState(ze({},i,h),function(){a.props.onChange(a.getIndex({startX:a.state.startX,endX:a.state.endX}))})}}}},{key:"renderBackground",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,c=n.fill,l=n.stroke;return S.createElement("rect",{stroke:l,fill:c,x:i,y:a,width:o,height:u})}},{key:"renderPanorama",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,c=n.data,l=n.children,f=n.padding,s=R.Children.only(l);return s?S.cloneElement(s,{x:i,y:a,width:o,height:u,margin:f,compact:!0,data:c}):null}},{key:"renderTravellerLayer",value:function(n,i){var a,o,u=this,c=this.props,l=c.y,f=c.travellerWidth,s=c.height,p=c.traveller,h=c.ariaLabel,v=c.data,d=c.startIndex,y=c.endIndex,b=Math.max(n,this.props.x),w=uu(uu({},H(this.props,!1)),{},{x:b,y:l,width:f,height:s}),x=h||"Min value: ".concat((a=v[d])===null||a===void 0?void 0:a.name,", Max value: ").concat((o=v[y])===null||o===void 0?void 0:o.name);return S.createElement(te,{tabIndex:0,role:"slider","aria-label":x,"aria-valuenow":n,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[i],onTouchStart:this.travellerDragStartHandlers[i],onKeyDown:function(m){["ArrowLeft","ArrowRight"].includes(m.key)&&(m.preventDefault(),m.stopPropagation(),u.handleTravellerMoveKeyboard(m.key==="ArrowRight"?1:-1,i))},onFocus:function(){u.setState({isTravellerFocused:!0})},onBlur:function(){u.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},t.renderTraveller(p,w))}},{key:"renderSlide",value:function(n,i){var a=this.props,o=a.y,u=a.height,c=a.stroke,l=a.travellerWidth,f=Math.min(n,i)+l,s=Math.max(Math.abs(i-n)-l,0);return S.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:c,fillOpacity:.2,x:f,y:o,width:s,height:u})}},{key:"renderText",value:function(){var n=this.props,i=n.startIndex,a=n.endIndex,o=n.y,u=n.height,c=n.travellerWidth,l=n.stroke,f=this.state,s=f.startX,p=f.endX,h=5,v={pointerEvents:"none",fill:l};return S.createElement(te,{className:"recharts-brush-texts"},S.createElement(ir,_a({textAnchor:"end",verticalAnchor:"middle",x:Math.min(s,p)-h,y:o+u/2},v),this.getTextOfTick(i)),S.createElement(ir,_a({textAnchor:"start",verticalAnchor:"middle",x:Math.max(s,p)+c+h,y:o+u/2},v),this.getTextOfTick(a)))}},{key:"render",value:function(){var n=this.props,i=n.data,a=n.className,o=n.children,u=n.x,c=n.y,l=n.width,f=n.height,s=n.alwaysShowText,p=this.state,h=p.startX,v=p.endX,d=p.isTextActive,y=p.isSlideMoving,b=p.isTravellerMoving,w=p.isTravellerFocused;if(!i||!i.length||!L(u)||!L(c)||!L(l)||!L(f)||l<=0||f<=0)return null;var x=J("recharts-brush",a),A=S.Children.count(o)===1,m=uB("userSelect","none");return S.createElement(te,{className:x,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:m},this.renderBackground(),A&&this.renderPanorama(),this.renderSlide(h,v),this.renderTravellerLayer(h,"startX"),this.renderTravellerLayer(v,"endX"),(d||y||b||w||s)&&this.renderText())}}],[{key:"renderDefaultTraveller",value:function(n){var i=n.x,a=n.y,o=n.width,u=n.height,c=n.stroke,l=Math.floor(a+u/2)-1;return S.createElement(S.Fragment,null,S.createElement("rect",{x:i,y:a,width:o,height:u,fill:c,stroke:"none"}),S.createElement("line",{x1:i+1,y1:l,x2:i+o-1,y2:l,fill:"none",stroke:"#fff"}),S.createElement("line",{x1:i+1,y1:l+2,x2:i+o-1,y2:l+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(n,i){var a;return S.isValidElement(n)?a=S.cloneElement(n,i):X(n)?a=n(i):a=t.renderDefaultTraveller(i),a}},{key:"getDerivedStateFromProps",value:function(n,i){var a=n.data,o=n.width,u=n.x,c=n.travellerWidth,l=n.updateId,f=n.startIndex,s=n.endIndex;if(a!==i.prevData||l!==i.prevUpdateId)return uu({prevData:a,prevTravellerWidth:c,prevUpdateId:l,prevX:u,prevWidth:o},a&&a.length?vB({data:a,width:o,x:u,travellerWidth:c,startIndex:f,endIndex:s}):{scale:null,scaleValues:null});if(i.scale&&(o!==i.prevWidth||u!==i.prevX||c!==i.prevTravellerWidth)){i.scale.range([u,u+o-c]);var p=i.scale.domain().map(function(h){return i.scale(h)});return{prevData:a,prevTravellerWidth:c,prevUpdateId:l,prevX:u,prevWidth:o,startX:i.scale(n.startIndex),endX:i.scale(n.endIndex),scaleValues:p}}return null}},{key:"getIndexInRange",value:function(n,i){for(var a=n.length,o=0,u=a-1;u-o>1;){var c=Math.floor((o+u)/2);n[c]>i?u=c:o=c}return i>=n[u]?u:o}}])}(R.PureComponent);ze(Wr,"displayName","Brush");ze(Wr,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var yB=cl;function mB(e,t){var r;return yB(e,function(n,i,a){return r=t(n,i,a),!r}),!!r}var gB=mB,bB=Zd,xB=dt,wB=gB,OB=Be,AB=oo;function SB(e,t,r){var n=OB(e)?bB:wB;return r&&AB(e,t,r)&&(t=void 0),n(e,xB(t))}var PB=SB;const _B=oe(PB);var ft=function(t,r){var n=t.alwaysShow,i=t.ifOverflow;return n&&(i="extendDomain"),i===r},_h=bv;function $B(e,t,r){t=="__proto__"&&_h?_h(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r}var TB=$B,EB=TB,jB=mv,MB=dt;function IB(e,t){var r={};return t=MB(t),jB(e,function(n,i,a){EB(r,i,t(n,i,a))}),r}var CB=IB;const kB=oe(CB);function DB(e,t){for(var r=-1,n=e==null?0:e.length;++r<n;)if(!t(e[r],r,e))return!1;return!0}var NB=DB,RB=cl;function LB(e,t){var r=!0;return RB(e,function(n,i,a){return r=!!t(n,i,a),r}),r}var BB=LB,FB=NB,WB=BB,zB=dt,UB=Be,qB=oo;function HB(e,t,r){var n=UB(e)?FB:WB;return r&&qB(e,t,r)&&(t=void 0),n(e,zB(t))}var KB=HB;const sm=oe(KB);var GB=["x","y"];function ui(e){"@babel/helpers - typeof";return ui=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ui(e)}function xc(){return xc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},xc.apply(this,arguments)}function $h(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function On(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?$h(Object(r),!0).forEach(function(n){VB(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):$h(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function VB(e,t,r){return t=XB(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function XB(e){var t=YB(e,"string");return ui(t)=="symbol"?t:t+""}function YB(e,t){if(ui(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(ui(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function ZB(e,t){if(e==null)return{};var r=JB(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function JB(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function QB(e,t){var r=e.x,n=e.y,i=ZB(e,GB),a="".concat(r),o=parseInt(a,10),u="".concat(n),c=parseInt(u,10),l="".concat(t.height||i.height),f=parseInt(l,10),s="".concat(t.width||i.width),p=parseInt(s,10);return On(On(On(On(On({},t),i),o?{x:o}:{}),c?{y:c}:{}),{},{height:f,width:p,name:t.name,radius:t.radius})}function Th(e){return S.createElement(nm,xc({shapeType:"rectangle",propTransformer:QB,activeClassName:"recharts-active-bar"},e))}var eF=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return function(n,i){if(typeof t=="number")return t;var a=L(n)||Ex(n);return a?t(n,i):(a||or(!1),r)}},tF=["value","background"],fm;function zr(e){"@babel/helpers - typeof";return zr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},zr(e)}function rF(e,t){if(e==null)return{};var r=nF(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function nF(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Ta(){return Ta=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ta.apply(this,arguments)}function Eh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ge(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Eh(Object(r),!0).forEach(function(n){Ct(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Eh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function iF(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function jh(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,hm(n.key),n)}}function aF(e,t,r){return t&&jh(e.prototype,t),r&&jh(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function oF(e,t,r){return t=Ea(t),uF(e,pm()?Reflect.construct(t,r||[],Ea(e).constructor):t.apply(e,r))}function uF(e,t){if(t&&(zr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return cF(e)}function cF(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function pm(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(pm=function(){return!!e})()}function Ea(e){return Ea=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ea(e)}function lF(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&wc(e,t)}function wc(e,t){return wc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},wc(e,t)}function Ct(e,t,r){return t=hm(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function hm(e){var t=sF(e,"string");return zr(t)=="symbol"?t:t+""}function sF(e,t){if(zr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(zr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var fn=function(e){function t(){var r;iF(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=oF(this,t,[].concat(i)),Ct(r,"state",{isAnimationFinished:!1}),Ct(r,"id",nn("recharts-bar-")),Ct(r,"handleAnimationEnd",function(){var o=r.props.onAnimationEnd;r.setState({isAnimationFinished:!0}),o&&o()}),Ct(r,"handleAnimationStart",function(){var o=r.props.onAnimationStart;r.setState({isAnimationFinished:!1}),o&&o()}),r}return lF(t,e),aF(t,[{key:"renderRectanglesStatically",value:function(n){var i=this,a=this.props,o=a.shape,u=a.dataKey,c=a.activeIndex,l=a.activeBar,f=H(this.props,!1);return n&&n.map(function(s,p){var h=p===c,v=h?l:o,d=ge(ge(ge({},f),s),{},{isActive:h,option:v,index:p,dataKey:u,onAnimationStart:i.handleAnimationStart,onAnimationEnd:i.handleAnimationEnd});return S.createElement(te,Ta({className:"recharts-bar-rectangle"},nr(i.props,s,p),{key:"rectangle-".concat(s==null?void 0:s.x,"-").concat(s==null?void 0:s.y,"-").concat(s==null?void 0:s.value,"-").concat(p)}),S.createElement(Th,d))})}},{key:"renderRectanglesWithAnimation",value:function(){var n=this,i=this.props,a=i.data,o=i.layout,u=i.isAnimationActive,c=i.animationBegin,l=i.animationDuration,f=i.animationEasing,s=i.animationId,p=this.state.prevData;return S.createElement(ur,{begin:c,duration:l,isActive:u,easing:f,from:{t:0},to:{t:1},key:"bar-".concat(s),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(h){var v=h.t,d=a.map(function(y,b){var w=p&&p[b];if(w){var x=qe(w.x,y.x),A=qe(w.y,y.y),m=qe(w.width,y.width),g=qe(w.height,y.height);return ge(ge({},y),{},{x:x(v),y:A(v),width:m(v),height:g(v)})}if(o==="horizontal"){var O=qe(0,y.height),P=O(v);return ge(ge({},y),{},{y:y.y+y.height-P,height:P})}var _=qe(0,y.width),E=_(v);return ge(ge({},y),{},{width:E})});return S.createElement(te,null,n.renderRectanglesStatically(d))})}},{key:"renderRectangles",value:function(){var n=this.props,i=n.data,a=n.isAnimationActive,o=this.state.prevData;return a&&i&&i.length&&(!o||!bi(o,i))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(i)}},{key:"renderBackground",value:function(){var n=this,i=this.props,a=i.data,o=i.dataKey,u=i.activeIndex,c=H(this.props.background,!1);return a.map(function(l,f){l.value;var s=l.background,p=rF(l,tF);if(!s)return null;var h=ge(ge(ge(ge(ge({},p),{},{fill:"#eee"},s),c),nr(n.props,l,f)),{},{onAnimationStart:n.handleAnimationStart,onAnimationEnd:n.handleAnimationEnd,dataKey:o,index:f,className:"recharts-bar-background-rectangle"});return S.createElement(Th,Ta({key:"background-bar-".concat(f),option:n.props.background,isActive:f===u},h))})}},{key:"renderErrorBar",value:function(n,i){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var a=this.props,o=a.data,u=a.xAxis,c=a.yAxis,l=a.layout,f=a.children,s=Ge(f,xi);if(!s)return null;var p=l==="vertical"?o[0].height/2:o[0].width/2,h=function(y,b){var w=Array.isArray(y.value)?y.value[1]:y.value;return{x:y.x,y:y.y,value:w,errorVal:Se(y,b)}},v={clipPath:n?"url(#clipPath-".concat(i,")"):null};return S.createElement(te,v,s.map(function(d){return S.cloneElement(d,{key:"error-bar-".concat(i,"-").concat(d.props.dataKey),data:o,xAxis:u,yAxis:c,layout:l,offset:p,dataPointFormatter:h})}))}},{key:"render",value:function(){var n=this.props,i=n.hide,a=n.data,o=n.className,u=n.xAxis,c=n.yAxis,l=n.left,f=n.top,s=n.width,p=n.height,h=n.isAnimationActive,v=n.background,d=n.id;if(i||!a||!a.length)return null;var y=this.state.isAnimationFinished,b=J("recharts-bar",o),w=u&&u.allowDataOverflow,x=c&&c.allowDataOverflow,A=w||x,m=Y(d)?this.id:d;return S.createElement(te,{className:b},w||x?S.createElement("defs",null,S.createElement("clipPath",{id:"clipPath-".concat(m)},S.createElement("rect",{x:w?l:l-s/2,y:x?f:f-p/2,width:w?s:s*2,height:x?p:p*2}))):null,S.createElement(te,{className:"recharts-bar-rectangles",clipPath:A?"url(#clipPath-".concat(m,")"):null},v?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(A,m),(!h||y)&&At.renderCallByParent(this.props,a))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curData:n.data,prevData:i.curData}:n.data!==i.curData?{curData:n.data}:null}}])}(R.PureComponent);fm=fn;Ct(fn,"displayName","Bar");Ct(fn,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!lt.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"});Ct(fn,"getComposedData",function(e){var t=e.props,r=e.item,n=e.barPosition,i=e.bandSize,a=e.xAxis,o=e.yAxis,u=e.xAxisTicks,c=e.yAxisTicks,l=e.stackedData,f=e.dataStartIndex,s=e.displayedData,p=e.offset,h=Hk(n,r);if(!h)return null;var v=t.layout,d=r.type.defaultProps,y=d!==void 0?ge(ge({},d),r.props):r.props,b=y.dataKey,w=y.children,x=y.minPointSize,A=v==="horizontal"?o:a,m=l?A.scale.domain():null,g=Jk({numericAxis:A}),O=Ge(w,fl),P=s.map(function(_,E){var $,T,I,C,M,k;l?$=Kk(l[f+E],m):($=Se(_,b),Array.isArray($)||($=[g,$]));var D=eF(x,fm.defaultProps.minPointSize)($[1],E);if(v==="horizontal"){var B,F=[o.scale($[0]),o.scale($[1])],q=F[0],G=F[1];T=gp({axis:a,ticks:u,bandSize:i,offset:h.offset,entry:_,index:E}),I=(B=G??q)!==null&&B!==void 0?B:void 0,C=h.size;var z=q-G;if(M=Number.isNaN(z)?0:z,k={x:T,y:o.y,width:C,height:o.height},Math.abs(D)>0&&Math.abs(M)<Math.abs(D)){var V=Ce(M||D)*(Math.abs(D)-Math.abs(M));I-=V,M+=V}}else{var fe=[a.scale($[0]),a.scale($[1])],me=fe[0],Fe=fe[1];if(T=me,I=gp({axis:o,ticks:c,bandSize:i,offset:h.offset,entry:_,index:E}),C=Fe-me,M=h.size,k={x:a.x,y:I,width:a.width,height:M},Math.abs(D)>0&&Math.abs(C)<Math.abs(D)){var Ft=Ce(C||D)*(Math.abs(D)-Math.abs(C));C+=Ft}}return ge(ge(ge({},_),{},{x:T,y:I,width:C,height:M,value:l?$:$[1],payload:_,background:k},O&&O[E]&&O[E].props),{},{tooltipPayload:[Dy(r,_)],tooltipPosition:{x:T+C/2,y:I+M/2}})});return ge({data:P,layout:v},p)});function ci(e){"@babel/helpers - typeof";return ci=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ci(e)}function fF(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Mh(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,dm(n.key),n)}}function pF(e,t,r){return t&&Mh(e.prototype,t),r&&Mh(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function Ih(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function nt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ih(Object(r),!0).forEach(function(n){$o(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ih(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function $o(e,t,r){return t=dm(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function dm(e){var t=hF(e,"string");return ci(t)=="symbol"?t:t+""}function hF(e,t){if(ci(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(ci(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var vm=function(t,r,n,i,a){var o=t.width,u=t.height,c=t.layout,l=t.children,f=Object.keys(r),s={left:n.left,leftMirror:n.left,right:o-n.right,rightMirror:o-n.right,top:n.top,topMirror:n.top,bottom:u-n.bottom,bottomMirror:u-n.bottom},p=!!Ue(l,fn);return f.reduce(function(h,v){var d=r[v],y=d.orientation,b=d.domain,w=d.padding,x=w===void 0?{}:w,A=d.mirror,m=d.reversed,g="".concat(y).concat(A?"Mirror":""),O,P,_,E,$;if(d.type==="number"&&(d.padding==="gap"||d.padding==="no-gap")){var T=b[1]-b[0],I=1/0,C=d.categoricalDomain.sort(Ix);if(C.forEach(function(fe,me){me>0&&(I=Math.min((fe||0)-(C[me-1]||0),I))}),Number.isFinite(I)){var M=I/T,k=d.layout==="vertical"?n.height:n.width;if(d.padding==="gap"&&(O=M*k/2),d.padding==="no-gap"){var D=ke(t.barCategoryGap,M*k),B=M*k/2;O=B-D-(B-D)/k*D}}}i==="xAxis"?P=[n.left+(x.left||0)+(O||0),n.left+n.width-(x.right||0)-(O||0)]:i==="yAxis"?P=c==="horizontal"?[n.top+n.height-(x.bottom||0),n.top+(x.top||0)]:[n.top+(x.top||0)+(O||0),n.top+n.height-(x.bottom||0)-(O||0)]:P=d.range,m&&(P=[P[1],P[0]]);var F=My(d,a,p),q=F.scale,G=F.realScaleType;q.domain(b).range(P),Iy(q);var z=Cy(q,nt(nt({},d),{},{realScaleType:G}));i==="xAxis"?($=y==="top"&&!A||y==="bottom"&&A,_=n.left,E=s[g]-$*d.height):i==="yAxis"&&($=y==="left"&&!A||y==="right"&&A,_=s[g]-$*d.width,E=n.top);var V=nt(nt(nt({},d),z),{},{realScaleType:G,x:_,y:E,scale:q,width:i==="xAxis"?n.width:d.width,height:i==="yAxis"?n.height:d.height});return V.bandSize=pa(V,z),!d.hide&&i==="xAxis"?s[g]+=($?-1:1)*V.height:d.hide||(s[g]+=($?-1:1)*V.width),nt(nt({},h),{},$o({},v,V))},{})},ym=function(t,r){var n=t.x,i=t.y,a=r.x,o=r.y;return{x:Math.min(n,a),y:Math.min(i,o),width:Math.abs(a-n),height:Math.abs(o-i)}},dF=function(t){var r=t.x1,n=t.y1,i=t.x2,a=t.y2;return ym({x:r,y:n},{x:i,y:a})},mm=function(){function e(t){fF(this,e),this.scale=t}return pF(e,[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(r){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=n.bandAware,a=n.position;if(r!==void 0){if(a)switch(a){case"start":return this.scale(r);case"middle":{var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(r)+o}case"end":{var u=this.bandwidth?this.bandwidth():0;return this.scale(r)+u}default:return this.scale(r)}if(i){var c=this.bandwidth?this.bandwidth()/2:0;return this.scale(r)+c}return this.scale(r)}}},{key:"isInRange",value:function(r){var n=this.range(),i=n[0],a=n[n.length-1];return i<=a?r>=i&&r<=a:r>=a&&r<=i}}],[{key:"create",value:function(r){return new e(r)}}])}();$o(mm,"EPS",1e-4);var Ll=function(t){var r=Object.keys(t).reduce(function(n,i){return nt(nt({},n),{},$o({},i,mm.create(t[i])))},{});return nt(nt({},r),{},{apply:function(i){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=a.bandAware,u=a.position;return kB(i,function(c,l){return r[l].apply(c,{bandAware:o,position:u})})},isInRange:function(i){return sm(i,function(a,o){return r[o].isInRange(a)})}})};function vF(e){return(e%180+180)%180}var yF=function(t){var r=t.width,n=t.height,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=vF(i),o=a*Math.PI/180,u=Math.atan(n/r),c=o>u&&o<Math.PI-u?n/Math.sin(o):r/Math.cos(o);return Math.abs(c)},mF=dt,gF=di,bF=io;function xF(e){return function(t,r,n){var i=Object(t);if(!gF(t)){var a=mF(r);t=bF(t),r=function(u){return a(i[u],u,i)}}var o=e(t,r,n);return o>-1?i[a?t[o]:o]:void 0}}var wF=xF,OF=om;function AF(e){var t=OF(e),r=t%1;return t===t?r?t-r:t:0}var SF=AF,PF=fv,_F=dt,$F=SF,TF=Math.max;function EF(e,t,r){var n=e==null?0:e.length;if(!n)return-1;var i=r==null?0:$F(r);return i<0&&(i=TF(n+i,0)),PF(e,_F(t),i)}var jF=EF,MF=wF,IF=jF,CF=MF(IF),kF=CF;const DF=oe(kF);var NF=C0(function(e){return{x:e.left,y:e.top,width:e.width,height:e.height}},function(e){return["l",e.left,"t",e.top,"w",e.width,"h",e.height].join("")}),Bl=R.createContext(void 0),Fl=R.createContext(void 0),gm=R.createContext(void 0),bm=R.createContext({}),xm=R.createContext(void 0),wm=R.createContext(0),Om=R.createContext(0),Ch=function(t){var r=t.state,n=r.xAxisMap,i=r.yAxisMap,a=r.offset,o=t.clipPathId,u=t.children,c=t.width,l=t.height,f=NF(a);return S.createElement(Bl.Provider,{value:n},S.createElement(Fl.Provider,{value:i},S.createElement(bm.Provider,{value:a},S.createElement(gm.Provider,{value:f},S.createElement(xm.Provider,{value:o},S.createElement(wm.Provider,{value:l},S.createElement(Om.Provider,{value:c},u)))))))},RF=function(){return R.useContext(xm)},Am=function(t){var r=R.useContext(Bl);r==null&&or(!1);var n=r[t];return n==null&&or(!1),n},LF=function(){var t=R.useContext(Bl);return It(t)},BF=function(){var t=R.useContext(Fl),r=DF(t,function(n){return sm(n.domain,Number.isFinite)});return r||It(t)},Sm=function(t){var r=R.useContext(Fl);r==null&&or(!1);var n=r[t];return n==null&&or(!1),n},FF=function(){var t=R.useContext(gm);return t},WF=function(){return R.useContext(bm)},Wl=function(){return R.useContext(Om)},zl=function(){return R.useContext(wm)};function Ur(e){"@babel/helpers - typeof";return Ur=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ur(e)}function zF(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function kh(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,_m(n.key),n)}}function UF(e,t,r){return t&&kh(e.prototype,t),r&&kh(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function qF(e,t,r){return t=ja(t),HF(e,Pm()?Reflect.construct(t,r||[],ja(e).constructor):t.apply(e,r))}function HF(e,t){if(t&&(Ur(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return KF(e)}function KF(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Pm(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Pm=function(){return!!e})()}function ja(e){return ja=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ja(e)}function GF(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Oc(e,t)}function Oc(e,t){return Oc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Oc(e,t)}function Dh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Nh(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Dh(Object(r),!0).forEach(function(n){Ul(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Dh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Ul(e,t,r){return t=_m(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function _m(e){var t=VF(e,"string");return Ur(t)=="symbol"?t:t+""}function VF(e,t){if(Ur(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Ur(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function XF(e,t){return QF(e)||JF(e,t)||ZF(e,t)||YF()}function YF(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ZF(e,t){if(e){if(typeof e=="string")return Rh(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Rh(e,t)}}function Rh(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function JF(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){l=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return u}}function QF(e){if(Array.isArray(e))return e}function Ac(){return Ac=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ac.apply(this,arguments)}var e3=function(t,r){var n;return S.isValidElement(t)?n=S.cloneElement(t,r):X(t)?n=t(r):n=S.createElement("line",Ac({},r,{className:"recharts-reference-line-line"})),n},t3=function(t,r,n,i,a,o,u,c,l){var f=a.x,s=a.y,p=a.width,h=a.height;if(n){var v=l.y,d=t.y.apply(v,{position:o});if(ft(l,"discard")&&!t.y.isInRange(d))return null;var y=[{x:f+p,y:d},{x:f,y:d}];return c==="left"?y.reverse():y}if(r){var b=l.x,w=t.x.apply(b,{position:o});if(ft(l,"discard")&&!t.x.isInRange(w))return null;var x=[{x:w,y:s+h},{x:w,y:s}];return u==="top"?x.reverse():x}if(i){var A=l.segment,m=A.map(function(g){return t.apply(g,{position:o})});return ft(l,"discard")&&_B(m,function(g){return!t.isInRange(g)})?null:m}return null};function r3(e){var t=e.x,r=e.y,n=e.segment,i=e.xAxisId,a=e.yAxisId,o=e.shape,u=e.className,c=e.alwaysShow,l=RF(),f=Am(i),s=Sm(a),p=FF();if(!l||!p)return null;ot(c===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var h=Ll({x:f.scale,y:s.scale}),v=Pe(t),d=Pe(r),y=n&&n.length===2,b=t3(h,v,d,y,p,e.position,f.orientation,s.orientation,e);if(!b)return null;var w=XF(b,2),x=w[0],A=x.x,m=x.y,g=w[1],O=g.x,P=g.y,_=ft(e,"hidden")?"url(#".concat(l,")"):void 0,E=Nh(Nh({clipPath:_},H(e,!0)),{},{x1:A,y1:m,x2:O,y2:P});return S.createElement(te,{className:J("recharts-reference-line",u)},e3(o,E),Te.renderCallByParent(e,dF({x1:A,y1:m,x2:O,y2:P})))}var ql=function(e){function t(){return zF(this,t),qF(this,t,arguments)}return GF(t,e),UF(t,[{key:"render",value:function(){return S.createElement(r3,this.props)}}])}(S.Component);Ul(ql,"displayName","ReferenceLine");Ul(ql,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function Sc(){return Sc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Sc.apply(this,arguments)}function qr(e){"@babel/helpers - typeof";return qr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},qr(e)}function Lh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Bh(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Lh(Object(r),!0).forEach(function(n){To(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Lh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function n3(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Fh(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Tm(n.key),n)}}function i3(e,t,r){return t&&Fh(e.prototype,t),r&&Fh(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function a3(e,t,r){return t=Ma(t),o3(e,$m()?Reflect.construct(t,r||[],Ma(e).constructor):t.apply(e,r))}function o3(e,t){if(t&&(qr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return u3(e)}function u3(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function $m(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return($m=function(){return!!e})()}function Ma(e){return Ma=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ma(e)}function c3(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Pc(e,t)}function Pc(e,t){return Pc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Pc(e,t)}function To(e,t,r){return t=Tm(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Tm(e){var t=l3(e,"string");return qr(t)=="symbol"?t:t+""}function l3(e,t){if(qr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(qr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var s3=function(t){var r=t.x,n=t.y,i=t.xAxis,a=t.yAxis,o=Ll({x:i.scale,y:a.scale}),u=o.apply({x:r,y:n},{bandAware:!0});return ft(t,"discard")&&!o.isInRange(u)?null:u},Eo=function(e){function t(){return n3(this,t),a3(this,t,arguments)}return c3(t,e),i3(t,[{key:"render",value:function(){var n=this.props,i=n.x,a=n.y,o=n.r,u=n.alwaysShow,c=n.clipPathId,l=Pe(i),f=Pe(a);if(ot(u===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!l||!f)return null;var s=s3(this.props);if(!s)return null;var p=s.x,h=s.y,v=this.props,d=v.shape,y=v.className,b=ft(this.props,"hidden")?"url(#".concat(c,")"):void 0,w=Bh(Bh({clipPath:b},H(this.props,!0)),{},{cx:p,cy:h});return S.createElement(te,{className:J("recharts-reference-dot",y)},t.renderDot(d,w),Te.renderCallByParent(this.props,{x:p-o,y:h-o,width:2*o,height:2*o}))}}])}(S.Component);To(Eo,"displayName","ReferenceDot");To(Eo,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1});To(Eo,"renderDot",function(e,t){var r;return S.isValidElement(e)?r=S.cloneElement(e,t):X(e)?r=e(t):r=S.createElement(xo,Sc({},t,{cx:t.cx,cy:t.cy,className:"recharts-reference-dot-dot"})),r});function _c(){return _c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},_c.apply(this,arguments)}function Hr(e){"@babel/helpers - typeof";return Hr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Hr(e)}function Wh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function zh(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Wh(Object(r),!0).forEach(function(n){jo(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Wh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function f3(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Uh(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,jm(n.key),n)}}function p3(e,t,r){return t&&Uh(e.prototype,t),r&&Uh(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function h3(e,t,r){return t=Ia(t),d3(e,Em()?Reflect.construct(t,r||[],Ia(e).constructor):t.apply(e,r))}function d3(e,t){if(t&&(Hr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return v3(e)}function v3(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Em(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Em=function(){return!!e})()}function Ia(e){return Ia=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ia(e)}function y3(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&$c(e,t)}function $c(e,t){return $c=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},$c(e,t)}function jo(e,t,r){return t=jm(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function jm(e){var t=m3(e,"string");return Hr(t)=="symbol"?t:t+""}function m3(e,t){if(Hr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Hr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var g3=function(t,r,n,i,a){var o=a.x1,u=a.x2,c=a.y1,l=a.y2,f=a.xAxis,s=a.yAxis;if(!f||!s)return null;var p=Ll({x:f.scale,y:s.scale}),h={x:t?p.x.apply(o,{position:"start"}):p.x.rangeMin,y:n?p.y.apply(c,{position:"start"}):p.y.rangeMin},v={x:r?p.x.apply(u,{position:"end"}):p.x.rangeMax,y:i?p.y.apply(l,{position:"end"}):p.y.rangeMax};return ft(a,"discard")&&(!p.isInRange(h)||!p.isInRange(v))?null:ym(h,v)},Mo=function(e){function t(){return f3(this,t),h3(this,t,arguments)}return y3(t,e),p3(t,[{key:"render",value:function(){var n=this.props,i=n.x1,a=n.x2,o=n.y1,u=n.y2,c=n.className,l=n.alwaysShow,f=n.clipPathId;ot(l===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var s=Pe(i),p=Pe(a),h=Pe(o),v=Pe(u),d=this.props.shape;if(!s&&!p&&!h&&!v&&!d)return null;var y=g3(s,p,h,v,this.props);if(!y&&!d)return null;var b=ft(this.props,"hidden")?"url(#".concat(f,")"):void 0;return S.createElement(te,{className:J("recharts-reference-area",c)},t.renderRect(d,zh(zh({clipPath:b},H(this.props,!0)),y)),Te.renderCallByParent(this.props,y))}}])}(S.Component);jo(Mo,"displayName","ReferenceArea");jo(Mo,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1});jo(Mo,"renderRect",function(e,t){var r;return S.isValidElement(e)?r=S.cloneElement(e,t):X(e)?r=e(t):r=S.createElement(Rl,_c({},t,{className:"recharts-reference-area-rect"})),r});function Mm(e,t,r){if(t<1)return[];if(t===1&&r===void 0)return e;for(var n=[],i=0;i<e.length;i+=t)if(r===void 0||r(e[i])===!0)n.push(e[i]);else return;return n}function b3(e,t,r){var n={width:e.width+t.width,height:e.height+t.height};return yF(n,r)}function x3(e,t,r){var n=r==="width",i=e.x,a=e.y,o=e.width,u=e.height;return t===1?{start:n?i:a,end:n?i+o:a+u}:{start:n?i+o:a+u,end:n?i:a}}function Ca(e,t,r,n,i){if(e*t<e*n||e*t>e*i)return!1;var a=r();return e*(t-e*a/2-n)>=0&&e*(t+e*a/2-i)<=0}function w3(e,t){return Mm(e,t+1)}function O3(e,t,r,n,i){for(var a=(n||[]).slice(),o=t.start,u=t.end,c=0,l=1,f=o,s=function(){var v=n==null?void 0:n[c];if(v===void 0)return{v:Mm(n,l)};var d=c,y,b=function(){return y===void 0&&(y=r(v,d)),y},w=v.coordinate,x=c===0||Ca(e,w,b,f,u);x||(c=0,f=o,l+=1),x&&(f=w+e*(b()/2+i),c+=l)},p;l<=a.length;)if(p=s(),p)return p.v;return[]}function li(e){"@babel/helpers - typeof";return li=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},li(e)}function qh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Me(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?qh(Object(r),!0).forEach(function(n){A3(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):qh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function A3(e,t,r){return t=S3(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function S3(e){var t=P3(e,"string");return li(t)=="symbol"?t:t+""}function P3(e,t){if(li(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(li(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function _3(e,t,r,n,i){for(var a=(n||[]).slice(),o=a.length,u=t.start,c=t.end,l=function(p){var h=a[p],v,d=function(){return v===void 0&&(v=r(h,p)),v};if(p===o-1){var y=e*(h.coordinate+e*d()/2-c);a[p]=h=Me(Me({},h),{},{tickCoord:y>0?h.coordinate-y*e:h.coordinate})}else a[p]=h=Me(Me({},h),{},{tickCoord:h.coordinate});var b=Ca(e,h.tickCoord,d,u,c);b&&(c=h.tickCoord-e*(d()/2+i),a[p]=Me(Me({},h),{},{isShow:!0}))},f=o-1;f>=0;f--)l(f);return a}function $3(e,t,r,n,i,a){var o=(n||[]).slice(),u=o.length,c=t.start,l=t.end;if(a){var f=n[u-1],s=r(f,u-1),p=e*(f.coordinate+e*s/2-l);o[u-1]=f=Me(Me({},f),{},{tickCoord:p>0?f.coordinate-p*e:f.coordinate});var h=Ca(e,f.tickCoord,function(){return s},c,l);h&&(l=f.tickCoord-e*(s/2+i),o[u-1]=Me(Me({},f),{},{isShow:!0}))}for(var v=a?u-1:u,d=function(w){var x=o[w],A,m=function(){return A===void 0&&(A=r(x,w)),A};if(w===0){var g=e*(x.coordinate-e*m()/2-c);o[w]=x=Me(Me({},x),{},{tickCoord:g<0?x.coordinate-g*e:x.coordinate})}else o[w]=x=Me(Me({},x),{},{tickCoord:x.coordinate});var O=Ca(e,x.tickCoord,m,c,l);O&&(c=x.tickCoord+e*(m()/2+i),o[w]=Me(Me({},x),{},{isShow:!0}))},y=0;y<v;y++)d(y);return o}function Hl(e,t,r){var n=e.tick,i=e.ticks,a=e.viewBox,o=e.minTickGap,u=e.orientation,c=e.interval,l=e.tickFormatter,f=e.unit,s=e.angle;if(!i||!i.length||!n)return[];if(L(c)||lt.isSsr)return w3(i,typeof c=="number"&&L(c)?c:0);var p=[],h=u==="top"||u==="bottom"?"width":"height",v=f&&h==="width"?Sn(f,{fontSize:t,letterSpacing:r}):{width:0,height:0},d=function(x,A){var m=X(l)?l(x.value,A):x.value;return h==="width"?b3(Sn(m,{fontSize:t,letterSpacing:r}),v,s):Sn(m,{fontSize:t,letterSpacing:r})[h]},y=i.length>=2?Ce(i[1].coordinate-i[0].coordinate):1,b=x3(a,y,h);return c==="equidistantPreserveStart"?O3(y,b,d,i,o):(c==="preserveStart"||c==="preserveStartEnd"?p=$3(y,b,d,i,o,c==="preserveStartEnd"):p=_3(y,b,d,i,o),p.filter(function(w){return w.isShow}))}var T3=["viewBox"],E3=["viewBox"],j3=["ticks"];function Kr(e){"@babel/helpers - typeof";return Kr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Kr(e)}function wr(){return wr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},wr.apply(this,arguments)}function Hh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Oe(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Hh(Object(r),!0).forEach(function(n){Kl(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Hh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function cu(e,t){if(e==null)return{};var r=M3(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function M3(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function I3(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Kh(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Cm(n.key),n)}}function C3(e,t,r){return t&&Kh(e.prototype,t),r&&Kh(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function k3(e,t,r){return t=ka(t),D3(e,Im()?Reflect.construct(t,r||[],ka(e).constructor):t.apply(e,r))}function D3(e,t){if(t&&(Kr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return N3(e)}function N3(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Im(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Im=function(){return!!e})()}function ka(e){return ka=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ka(e)}function R3(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Tc(e,t)}function Tc(e,t){return Tc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Tc(e,t)}function Kl(e,t,r){return t=Cm(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Cm(e){var t=L3(e,"string");return Kr(t)=="symbol"?t:t+""}function L3(e,t){if(Kr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Kr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var pn=function(e){function t(r){var n;return I3(this,t),n=k3(this,t,[r]),n.state={fontSize:"",letterSpacing:""},n}return R3(t,e),C3(t,[{key:"shouldComponentUpdate",value:function(n,i){var a=n.viewBox,o=cu(n,T3),u=this.props,c=u.viewBox,l=cu(u,E3);return!Ar(a,c)||!Ar(o,l)||!Ar(i,this.state)}},{key:"componentDidMount",value:function(){var n=this.layerReference;if(n){var i=n.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];i&&this.setState({fontSize:window.getComputedStyle(i).fontSize,letterSpacing:window.getComputedStyle(i).letterSpacing})}}},{key:"getTickLineCoord",value:function(n){var i=this.props,a=i.x,o=i.y,u=i.width,c=i.height,l=i.orientation,f=i.tickSize,s=i.mirror,p=i.tickMargin,h,v,d,y,b,w,x=s?-1:1,A=n.tickSize||f,m=L(n.tickCoord)?n.tickCoord:n.coordinate;switch(l){case"top":h=v=n.coordinate,y=o+ +!s*c,d=y-x*A,w=d-x*p,b=m;break;case"left":d=y=n.coordinate,v=a+ +!s*u,h=v-x*A,b=h-x*p,w=m;break;case"right":d=y=n.coordinate,v=a+ +s*u,h=v+x*A,b=h+x*p,w=m;break;default:h=v=n.coordinate,y=o+ +s*c,d=y+x*A,w=d+x*p,b=m;break}return{line:{x1:h,y1:d,x2:v,y2:y},tick:{x:b,y:w}}}},{key:"getTickTextAnchor",value:function(){var n=this.props,i=n.orientation,a=n.mirror,o;switch(i){case"left":o=a?"start":"end";break;case"right":o=a?"end":"start";break;default:o="middle";break}return o}},{key:"getTickVerticalAnchor",value:function(){var n=this.props,i=n.orientation,a=n.mirror,o="end";switch(i){case"left":case"right":o="middle";break;case"top":o=a?"start":"end";break;default:o=a?"end":"start";break}return o}},{key:"renderAxisLine",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,c=n.orientation,l=n.mirror,f=n.axisLine,s=Oe(Oe(Oe({},H(this.props,!1)),H(f,!1)),{},{fill:"none"});if(c==="top"||c==="bottom"){var p=+(c==="top"&&!l||c==="bottom"&&l);s=Oe(Oe({},s),{},{x1:i,y1:a+p*u,x2:i+o,y2:a+p*u})}else{var h=+(c==="left"&&!l||c==="right"&&l);s=Oe(Oe({},s),{},{x1:i+h*o,y1:a,x2:i+h*o,y2:a+u})}return S.createElement("line",wr({},s,{className:J("recharts-cartesian-axis-line",Ke(f,"className"))}))}},{key:"renderTicks",value:function(n,i,a){var o=this,u=this.props,c=u.tickLine,l=u.stroke,f=u.tick,s=u.tickFormatter,p=u.unit,h=Hl(Oe(Oe({},this.props),{},{ticks:n}),i,a),v=this.getTickTextAnchor(),d=this.getTickVerticalAnchor(),y=H(this.props,!1),b=H(f,!1),w=Oe(Oe({},y),{},{fill:"none"},H(c,!1)),x=h.map(function(A,m){var g=o.getTickLineCoord(A),O=g.line,P=g.tick,_=Oe(Oe(Oe(Oe({textAnchor:v,verticalAnchor:d},y),{},{stroke:"none",fill:l},b),P),{},{index:m,payload:A,visibleTicksCount:h.length,tickFormatter:s});return S.createElement(te,wr({className:"recharts-cartesian-axis-tick",key:"tick-".concat(A.value,"-").concat(A.coordinate,"-").concat(A.tickCoord)},nr(o.props,A,m)),c&&S.createElement("line",wr({},w,O,{className:J("recharts-cartesian-axis-tick-line",Ke(c,"className"))})),f&&t.renderTickItem(f,_,"".concat(X(s)?s(A.value,m):A.value).concat(p||"")))});return S.createElement("g",{className:"recharts-cartesian-axis-ticks"},x)}},{key:"render",value:function(){var n=this,i=this.props,a=i.axisLine,o=i.width,u=i.height,c=i.ticksGenerator,l=i.className,f=i.hide;if(f)return null;var s=this.props,p=s.ticks,h=cu(s,j3),v=p;return X(c)&&(v=p&&p.length>0?c(this.props):c(h)),o<=0||u<=0||!v||!v.length?null:S.createElement(te,{className:J("recharts-cartesian-axis",l),ref:function(y){n.layerReference=y}},a&&this.renderAxisLine(),this.renderTicks(v,this.state.fontSize,this.state.letterSpacing),Te.renderCallByParent(this.props))}}],[{key:"renderTickItem",value:function(n,i,a){var o,u=J(i.className,"recharts-cartesian-axis-tick-value");return S.isValidElement(n)?o=S.cloneElement(n,Oe(Oe({},i),{},{className:u})):X(n)?o=n(Oe(Oe({},i),{},{className:u})):o=S.createElement(ir,wr({},i,{className:"recharts-cartesian-axis-tick-value"}),a),o}}])}(R.Component);Kl(pn,"displayName","CartesianAxis");Kl(pn,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var B3=["x1","y1","x2","y2","key"],F3=["offset"];function cr(e){"@babel/helpers - typeof";return cr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},cr(e)}function Gh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ie(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Gh(Object(r),!0).forEach(function(n){W3(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Gh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function W3(e,t,r){return t=z3(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function z3(e){var t=U3(e,"string");return cr(t)=="symbol"?t:t+""}function U3(e,t){if(cr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(cr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Qt(){return Qt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Qt.apply(this,arguments)}function Vh(e,t){if(e==null)return{};var r=q3(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function q3(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var H3=function(t){var r=t.fill;if(!r||r==="none")return null;var n=t.fillOpacity,i=t.x,a=t.y,o=t.width,u=t.height,c=t.ry;return S.createElement("rect",{x:i,y:a,ry:c,width:o,height:u,stroke:"none",fill:r,fillOpacity:n,className:"recharts-cartesian-grid-bg"})};function km(e,t){var r;if(S.isValidElement(e))r=S.cloneElement(e,t);else if(X(e))r=e(t);else{var n=t.x1,i=t.y1,a=t.x2,o=t.y2,u=t.key,c=Vh(t,B3),l=H(c,!1);l.offset;var f=Vh(l,F3);r=S.createElement("line",Qt({},f,{x1:n,y1:i,x2:a,y2:o,fill:"none",key:u}))}return r}function K3(e){var t=e.x,r=e.width,n=e.horizontal,i=n===void 0?!0:n,a=e.horizontalPoints;if(!i||!a||!a.length)return null;var o=a.map(function(u,c){var l=Ie(Ie({},e),{},{x1:t,y1:u,x2:t+r,y2:u,key:"line-".concat(c),index:c});return km(i,l)});return S.createElement("g",{className:"recharts-cartesian-grid-horizontal"},o)}function G3(e){var t=e.y,r=e.height,n=e.vertical,i=n===void 0?!0:n,a=e.verticalPoints;if(!i||!a||!a.length)return null;var o=a.map(function(u,c){var l=Ie(Ie({},e),{},{x1:u,y1:t,x2:u,y2:t+r,key:"line-".concat(c),index:c});return km(i,l)});return S.createElement("g",{className:"recharts-cartesian-grid-vertical"},o)}function V3(e){var t=e.horizontalFill,r=e.fillOpacity,n=e.x,i=e.y,a=e.width,o=e.height,u=e.horizontalPoints,c=e.horizontal,l=c===void 0?!0:c;if(!l||!t||!t.length)return null;var f=u.map(function(p){return Math.round(p+i-i)}).sort(function(p,h){return p-h});i!==f[0]&&f.unshift(0);var s=f.map(function(p,h){var v=!f[h+1],d=v?i+o-p:f[h+1]-p;if(d<=0)return null;var y=h%t.length;return S.createElement("rect",{key:"react-".concat(h),y:p,x:n,height:d,width:a,stroke:"none",fill:t[y],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return S.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},s)}function X3(e){var t=e.vertical,r=t===void 0?!0:t,n=e.verticalFill,i=e.fillOpacity,a=e.x,o=e.y,u=e.width,c=e.height,l=e.verticalPoints;if(!r||!n||!n.length)return null;var f=l.map(function(p){return Math.round(p+a-a)}).sort(function(p,h){return p-h});a!==f[0]&&f.unshift(0);var s=f.map(function(p,h){var v=!f[h+1],d=v?a+u-p:f[h+1]-p;if(d<=0)return null;var y=h%n.length;return S.createElement("rect",{key:"react-".concat(h),x:p,y:o,width:d,height:c,stroke:"none",fill:n[y],fillOpacity:i,className:"recharts-cartesian-grid-bg"})});return S.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},s)}var Y3=function(t,r){var n=t.xAxis,i=t.width,a=t.height,o=t.offset;return jy(Hl(Ie(Ie(Ie({},pn.defaultProps),n),{},{ticks:xt(n,!0),viewBox:{x:0,y:0,width:i,height:a}})),o.left,o.left+o.width,r)},Z3=function(t,r){var n=t.yAxis,i=t.width,a=t.height,o=t.offset;return jy(Hl(Ie(Ie(Ie({},pn.defaultProps),n),{},{ticks:xt(n,!0),viewBox:{x:0,y:0,width:i,height:a}})),o.top,o.top+o.height,r)},yr={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function J3(e){var t,r,n,i,a,o,u=Wl(),c=zl(),l=WF(),f=Ie(Ie({},e),{},{stroke:(t=e.stroke)!==null&&t!==void 0?t:yr.stroke,fill:(r=e.fill)!==null&&r!==void 0?r:yr.fill,horizontal:(n=e.horizontal)!==null&&n!==void 0?n:yr.horizontal,horizontalFill:(i=e.horizontalFill)!==null&&i!==void 0?i:yr.horizontalFill,vertical:(a=e.vertical)!==null&&a!==void 0?a:yr.vertical,verticalFill:(o=e.verticalFill)!==null&&o!==void 0?o:yr.verticalFill,x:L(e.x)?e.x:l.left,y:L(e.y)?e.y:l.top,width:L(e.width)?e.width:l.width,height:L(e.height)?e.height:l.height}),s=f.x,p=f.y,h=f.width,v=f.height,d=f.syncWithTicks,y=f.horizontalValues,b=f.verticalValues,w=LF(),x=BF();if(!L(h)||h<=0||!L(v)||v<=0||!L(s)||s!==+s||!L(p)||p!==+p)return null;var A=f.verticalCoordinatesGenerator||Y3,m=f.horizontalCoordinatesGenerator||Z3,g=f.horizontalPoints,O=f.verticalPoints;if((!g||!g.length)&&X(m)){var P=y&&y.length,_=m({yAxis:x?Ie(Ie({},x),{},{ticks:P?y:x.ticks}):void 0,width:u,height:c,offset:l},P?!0:d);ot(Array.isArray(_),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(cr(_),"]")),Array.isArray(_)&&(g=_)}if((!O||!O.length)&&X(A)){var E=b&&b.length,$=A({xAxis:w?Ie(Ie({},w),{},{ticks:E?b:w.ticks}):void 0,width:u,height:c,offset:l},E?!0:d);ot(Array.isArray($),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(cr($),"]")),Array.isArray($)&&(O=$)}return S.createElement("g",{className:"recharts-cartesian-grid"},S.createElement(H3,{fill:f.fill,fillOpacity:f.fillOpacity,x:f.x,y:f.y,width:f.width,height:f.height,ry:f.ry}),S.createElement(K3,Qt({},f,{offset:l,horizontalPoints:g,xAxis:w,yAxis:x})),S.createElement(G3,Qt({},f,{offset:l,verticalPoints:O,xAxis:w,yAxis:x})),S.createElement(V3,Qt({},f,{horizontalPoints:g})),S.createElement(X3,Qt({},f,{verticalPoints:O})))}J3.displayName="CartesianGrid";var Q3=["type","layout","connectNulls","ref"],eW=["key"];function Gr(e){"@babel/helpers - typeof";return Gr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Gr(e)}function Xh(e,t){if(e==null)return{};var r=tW(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function tW(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function jn(){return jn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},jn.apply(this,arguments)}function Yh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function We(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Yh(Object(r),!0).forEach(function(n){it(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Yh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function mr(e){return aW(e)||iW(e)||nW(e)||rW()}function rW(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function nW(e,t){if(e){if(typeof e=="string")return Ec(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ec(e,t)}}function iW(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function aW(e){if(Array.isArray(e))return Ec(e)}function Ec(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function oW(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Zh(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Nm(n.key),n)}}function uW(e,t,r){return t&&Zh(e.prototype,t),r&&Zh(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function cW(e,t,r){return t=Da(t),lW(e,Dm()?Reflect.construct(t,r||[],Da(e).constructor):t.apply(e,r))}function lW(e,t){if(t&&(Gr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return sW(e)}function sW(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Dm(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Dm=function(){return!!e})()}function Da(e){return Da=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Da(e)}function fW(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&jc(e,t)}function jc(e,t){return jc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},jc(e,t)}function it(e,t,r){return t=Nm(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Nm(e){var t=pW(e,"string");return Gr(t)=="symbol"?t:t+""}function pW(e,t){if(Gr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Gr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Io=function(e){function t(){var r;oW(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=cW(this,t,[].concat(i)),it(r,"state",{isAnimationFinished:!0,totalLength:0}),it(r,"generateSimpleStrokeDasharray",function(o,u){return"".concat(u,"px ").concat(o-u,"px")}),it(r,"getStrokeDasharray",function(o,u,c){var l=c.reduce(function(b,w){return b+w});if(!l)return r.generateSimpleStrokeDasharray(u,o);for(var f=Math.floor(o/l),s=o%l,p=u-o,h=[],v=0,d=0;v<c.length;d+=c[v],++v)if(d+c[v]>s){h=[].concat(mr(c.slice(0,v)),[s-d]);break}var y=h.length%2===0?[0,p]:[p];return[].concat(mr(t.repeat(c,f)),mr(h),y).map(function(b){return"".concat(b,"px")}).join(", ")}),it(r,"id",nn("recharts-line-")),it(r,"pathRef",function(o){r.mainCurve=o}),it(r,"handleAnimationEnd",function(){r.setState({isAnimationFinished:!0}),r.props.onAnimationEnd&&r.props.onAnimationEnd()}),it(r,"handleAnimationStart",function(){r.setState({isAnimationFinished:!1}),r.props.onAnimationStart&&r.props.onAnimationStart()}),r}return fW(t,e),uW(t,[{key:"componentDidMount",value:function(){if(this.props.isAnimationActive){var n=this.getTotalLength();this.setState({totalLength:n})}}},{key:"componentDidUpdate",value:function(){if(this.props.isAnimationActive){var n=this.getTotalLength();n!==this.state.totalLength&&this.setState({totalLength:n})}}},{key:"getTotalLength",value:function(){var n=this.mainCurve;try{return n&&n.getTotalLength&&n.getTotalLength()||0}catch{return 0}}},{key:"renderErrorBar",value:function(n,i){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var a=this.props,o=a.points,u=a.xAxis,c=a.yAxis,l=a.layout,f=a.children,s=Ge(f,xi);if(!s)return null;var p=function(d,y){return{x:d.x,y:d.y,value:d.value,errorVal:Se(d.payload,y)}},h={clipPath:n?"url(#clipPath-".concat(i,")"):null};return S.createElement(te,h,s.map(function(v){return S.cloneElement(v,{key:"bar-".concat(v.props.dataKey),data:o,xAxis:u,yAxis:c,layout:l,dataPointFormatter:p})}))}},{key:"renderDots",value:function(n,i,a){var o=this.props.isAnimationActive;if(o&&!this.state.isAnimationFinished)return null;var u=this.props,c=u.dot,l=u.points,f=u.dataKey,s=H(this.props,!1),p=H(c,!0),h=l.map(function(d,y){var b=We(We(We({key:"dot-".concat(y),r:3},s),p),{},{index:y,cx:d.x,cy:d.y,value:d.value,dataKey:f,payload:d.payload,points:l});return t.renderDotItem(c,b)}),v={clipPath:n?"url(#clipPath-".concat(i?"":"dots-").concat(a,")"):null};return S.createElement(te,jn({className:"recharts-line-dots",key:"dots"},v),h)}},{key:"renderCurveStatically",value:function(n,i,a,o){var u=this.props,c=u.type,l=u.layout,f=u.connectNulls;u.ref;var s=Xh(u,Q3),p=We(We(We({},H(s,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:i?"url(#clipPath-".concat(a,")"):null,points:n},o),{},{type:c,layout:l,connectNulls:f});return S.createElement(va,jn({},p,{pathRef:this.pathRef}))}},{key:"renderCurveWithAnimation",value:function(n,i){var a=this,o=this.props,u=o.points,c=o.strokeDasharray,l=o.isAnimationActive,f=o.animationBegin,s=o.animationDuration,p=o.animationEasing,h=o.animationId,v=o.animateNewValues,d=o.width,y=o.height,b=this.state,w=b.prevPoints,x=b.totalLength;return S.createElement(ur,{begin:f,duration:s,isActive:l,easing:p,from:{t:0},to:{t:1},key:"line-".concat(h),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(A){var m=A.t;if(w){var g=w.length/u.length,O=u.map(function(T,I){var C=Math.floor(I*g);if(w[C]){var M=w[C],k=qe(M.x,T.x),D=qe(M.y,T.y);return We(We({},T),{},{x:k(m),y:D(m)})}if(v){var B=qe(d*2,T.x),F=qe(y/2,T.y);return We(We({},T),{},{x:B(m),y:F(m)})}return We(We({},T),{},{x:T.x,y:T.y})});return a.renderCurveStatically(O,n,i)}var P=qe(0,x),_=P(m),E;if(c){var $="".concat(c).split(/[,\s]+/gim).map(function(T){return parseFloat(T)});E=a.getStrokeDasharray(_,x,$)}else E=a.generateSimpleStrokeDasharray(x,_);return a.renderCurveStatically(u,n,i,{strokeDasharray:E})})}},{key:"renderCurve",value:function(n,i){var a=this.props,o=a.points,u=a.isAnimationActive,c=this.state,l=c.prevPoints,f=c.totalLength;return u&&o&&o.length&&(!l&&f>0||!bi(l,o))?this.renderCurveWithAnimation(n,i):this.renderCurveStatically(o,n,i)}},{key:"render",value:function(){var n,i=this.props,a=i.hide,o=i.dot,u=i.points,c=i.className,l=i.xAxis,f=i.yAxis,s=i.top,p=i.left,h=i.width,v=i.height,d=i.isAnimationActive,y=i.id;if(a||!u||!u.length)return null;var b=this.state.isAnimationFinished,w=u.length===1,x=J("recharts-line",c),A=l&&l.allowDataOverflow,m=f&&f.allowDataOverflow,g=A||m,O=Y(y)?this.id:y,P=(n=H(o,!1))!==null&&n!==void 0?n:{r:3,strokeWidth:2},_=P.r,E=_===void 0?3:_,$=P.strokeWidth,T=$===void 0?2:$,I=Wx(o)?o:{},C=I.clipDot,M=C===void 0?!0:C,k=E*2+T;return S.createElement(te,{className:x},A||m?S.createElement("defs",null,S.createElement("clipPath",{id:"clipPath-".concat(O)},S.createElement("rect",{x:A?p:p-h/2,y:m?s:s-v/2,width:A?h:h*2,height:m?v:v*2})),!M&&S.createElement("clipPath",{id:"clipPath-dots-".concat(O)},S.createElement("rect",{x:p-k/2,y:s-k/2,width:h+k,height:v+k}))):null,!w&&this.renderCurve(g,O),this.renderErrorBar(g,O),(w||o)&&this.renderDots(g,M,O),(!d||b)&&At.renderCallByParent(this.props,u))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curPoints:n.points,prevPoints:i.curPoints}:n.points!==i.curPoints?{curPoints:n.points}:null}},{key:"repeat",value:function(n,i){for(var a=n.length%2!==0?[].concat(mr(n),[0]):n,o=[],u=0;u<i;++u)o=[].concat(mr(o),mr(a));return o}},{key:"renderDotItem",value:function(n,i){var a;if(S.isValidElement(n))a=S.cloneElement(n,i);else if(X(n))a=n(i);else{var o=i.key,u=Xh(i,eW),c=J("recharts-line-dot",typeof n!="boolean"?n.className:"");a=S.createElement(xo,jn({key:o},u,{className:c}))}return a}}])}(R.PureComponent);it(Io,"displayName","Line");it(Io,"defaultProps",{xAxisId:0,yAxisId:0,connectNulls:!1,activeDot:!0,dot:!0,legendType:"line",stroke:"#3182bd",strokeWidth:1,fill:"#fff",points:[],isAnimationActive:!lt.isSsr,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",hide:!1,label:!1});it(Io,"getComposedData",function(e){var t=e.props,r=e.xAxis,n=e.yAxis,i=e.xAxisTicks,a=e.yAxisTicks,o=e.dataKey,u=e.bandSize,c=e.displayedData,l=e.offset,f=t.layout,s=c.map(function(p,h){var v=Se(p,o);return f==="horizontal"?{x:mp({axis:r,ticks:i,bandSize:u,entry:p,index:h}),y:Y(v)?null:n.scale(v),value:v,payload:p}:{x:Y(v)?null:r.scale(v),y:mp({axis:n,ticks:a,bandSize:u,entry:p,index:h}),value:v,payload:p}});return We({points:s,layout:f},l)});function Vr(e){"@babel/helpers - typeof";return Vr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Vr(e)}function hW(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Jh(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Bm(n.key),n)}}function dW(e,t,r){return t&&Jh(e.prototype,t),r&&Jh(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function vW(e,t,r){return t=Na(t),yW(e,Rm()?Reflect.construct(t,r||[],Na(e).constructor):t.apply(e,r))}function yW(e,t){if(t&&(Vr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return mW(e)}function mW(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Rm(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Rm=function(){return!!e})()}function Na(e){return Na=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Na(e)}function gW(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Mc(e,t)}function Mc(e,t){return Mc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Mc(e,t)}function Lm(e,t,r){return t=Bm(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Bm(e){var t=bW(e,"string");return Vr(t)=="symbol"?t:t+""}function bW(e,t){if(Vr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Vr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Ic(){return Ic=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ic.apply(this,arguments)}function xW(e){var t=e.xAxisId,r=Wl(),n=zl(),i=Am(t);return i==null?null:R.createElement(pn,Ic({},i,{className:J("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(o){return xt(o,!0)}}))}var Co=function(e){function t(){return hW(this,t),vW(this,t,arguments)}return gW(t,e),dW(t,[{key:"render",value:function(){return R.createElement(xW,this.props)}}])}(R.Component);Lm(Co,"displayName","XAxis");Lm(Co,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});function Xr(e){"@babel/helpers - typeof";return Xr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Xr(e)}function wW(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Qh(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,zm(n.key),n)}}function OW(e,t,r){return t&&Qh(e.prototype,t),r&&Qh(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function AW(e,t,r){return t=Ra(t),SW(e,Fm()?Reflect.construct(t,r||[],Ra(e).constructor):t.apply(e,r))}function SW(e,t){if(t&&(Xr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return PW(e)}function PW(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Fm(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Fm=function(){return!!e})()}function Ra(e){return Ra=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ra(e)}function _W(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Cc(e,t)}function Cc(e,t){return Cc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Cc(e,t)}function Wm(e,t,r){return t=zm(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function zm(e){var t=$W(e,"string");return Xr(t)=="symbol"?t:t+""}function $W(e,t){if(Xr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Xr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function kc(){return kc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},kc.apply(this,arguments)}var TW=function(t){var r=t.yAxisId,n=Wl(),i=zl(),a=Sm(r);return a==null?null:R.createElement(pn,kc({},a,{className:J("recharts-".concat(a.axisType," ").concat(a.axisType),a.className),viewBox:{x:0,y:0,width:n,height:i},ticksGenerator:function(u){return xt(u,!0)}}))},ko=function(e){function t(){return wW(this,t),AW(this,t,arguments)}return _W(t,e),OW(t,[{key:"render",value:function(){return R.createElement(TW,this.props)}}])}(R.Component);Wm(ko,"displayName","YAxis");Wm(ko,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});function ed(e){return IW(e)||MW(e)||jW(e)||EW()}function EW(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function jW(e,t){if(e){if(typeof e=="string")return Dc(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Dc(e,t)}}function MW(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function IW(e){if(Array.isArray(e))return Dc(e)}function Dc(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Nc=function(t,r,n,i,a){var o=Ge(t,ql),u=Ge(t,Eo),c=[].concat(ed(o),ed(u)),l=Ge(t,Mo),f="".concat(i,"Id"),s=i[0],p=r;if(c.length&&(p=c.reduce(function(d,y){if(y.props[f]===n&&ft(y.props,"extendDomain")&&L(y.props[s])){var b=y.props[s];return[Math.min(d[0],b),Math.max(d[1],b)]}return d},p)),l.length){var h="".concat(s,"1"),v="".concat(s,"2");p=l.reduce(function(d,y){if(y.props[f]===n&&ft(y.props,"extendDomain")&&L(y.props[h])&&L(y.props[v])){var b=y.props[h],w=y.props[v];return[Math.min(d[0],b,w),Math.max(d[1],b,w)]}return d},p)}return a&&a.length&&(p=a.reduce(function(d,y){return L(y)?[Math.min(d[0],y),Math.max(d[1],y)]:d},p)),p},Um={exports:{}};(function(e){var t=Object.prototype.hasOwnProperty,r="~";function n(){}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1));function i(c,l,f){this.fn=c,this.context=l,this.once=f||!1}function a(c,l,f,s,p){if(typeof f!="function")throw new TypeError("The listener must be a function");var h=new i(f,s||c,p),v=r?r+l:l;return c._events[v]?c._events[v].fn?c._events[v]=[c._events[v],h]:c._events[v].push(h):(c._events[v]=h,c._eventsCount++),c}function o(c,l){--c._eventsCount===0?c._events=new n:delete c._events[l]}function u(){this._events=new n,this._eventsCount=0}u.prototype.eventNames=function(){var l=[],f,s;if(this._eventsCount===0)return l;for(s in f=this._events)t.call(f,s)&&l.push(r?s.slice(1):s);return Object.getOwnPropertySymbols?l.concat(Object.getOwnPropertySymbols(f)):l},u.prototype.listeners=function(l){var f=r?r+l:l,s=this._events[f];if(!s)return[];if(s.fn)return[s.fn];for(var p=0,h=s.length,v=new Array(h);p<h;p++)v[p]=s[p].fn;return v},u.prototype.listenerCount=function(l){var f=r?r+l:l,s=this._events[f];return s?s.fn?1:s.length:0},u.prototype.emit=function(l,f,s,p,h,v){var d=r?r+l:l;if(!this._events[d])return!1;var y=this._events[d],b=arguments.length,w,x;if(y.fn){switch(y.once&&this.removeListener(l,y.fn,void 0,!0),b){case 1:return y.fn.call(y.context),!0;case 2:return y.fn.call(y.context,f),!0;case 3:return y.fn.call(y.context,f,s),!0;case 4:return y.fn.call(y.context,f,s,p),!0;case 5:return y.fn.call(y.context,f,s,p,h),!0;case 6:return y.fn.call(y.context,f,s,p,h,v),!0}for(x=1,w=new Array(b-1);x<b;x++)w[x-1]=arguments[x];y.fn.apply(y.context,w)}else{var A=y.length,m;for(x=0;x<A;x++)switch(y[x].once&&this.removeListener(l,y[x].fn,void 0,!0),b){case 1:y[x].fn.call(y[x].context);break;case 2:y[x].fn.call(y[x].context,f);break;case 3:y[x].fn.call(y[x].context,f,s);break;case 4:y[x].fn.call(y[x].context,f,s,p);break;default:if(!w)for(m=1,w=new Array(b-1);m<b;m++)w[m-1]=arguments[m];y[x].fn.apply(y[x].context,w)}}return!0},u.prototype.on=function(l,f,s){return a(this,l,f,s,!1)},u.prototype.once=function(l,f,s){return a(this,l,f,s,!0)},u.prototype.removeListener=function(l,f,s,p){var h=r?r+l:l;if(!this._events[h])return this;if(!f)return o(this,h),this;var v=this._events[h];if(v.fn)v.fn===f&&(!p||v.once)&&(!s||v.context===s)&&o(this,h);else{for(var d=0,y=[],b=v.length;d<b;d++)(v[d].fn!==f||p&&!v[d].once||s&&v[d].context!==s)&&y.push(v[d]);y.length?this._events[h]=y.length===1?y[0]:y:o(this,h)}return this},u.prototype.removeAllListeners=function(l){var f;return l?(f=r?r+l:l,this._events[f]&&o(this,f)):(this._events=new n,this._eventsCount=0),this},u.prototype.off=u.prototype.removeListener,u.prototype.addListener=u.prototype.on,u.prefixed=r,u.EventEmitter=u,e.exports=u})(Um);var CW=Um.exports;const kW=oe(CW);var lu=new kW,su="recharts.syncMouseEvents";function si(e){"@babel/helpers - typeof";return si=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},si(e)}function DW(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function td(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,qm(n.key),n)}}function NW(e,t,r){return t&&td(e.prototype,t),r&&td(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function fu(e,t,r){return t=qm(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function qm(e){var t=RW(e,"string");return si(t)=="symbol"?t:t+""}function RW(e,t){if(si(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(si(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var LW=function(){function e(){DW(this,e),fu(this,"activeIndex",0),fu(this,"coordinateList",[]),fu(this,"layout","horizontal")}return NW(e,[{key:"setDetails",value:function(r){var n,i=r.coordinateList,a=i===void 0?null:i,o=r.container,u=o===void 0?null:o,c=r.layout,l=c===void 0?null:c,f=r.offset,s=f===void 0?null:f,p=r.mouseHandlerCallback,h=p===void 0?null:p;this.coordinateList=(n=a??this.coordinateList)!==null&&n!==void 0?n:[],this.container=u??this.container,this.layout=l??this.layout,this.offset=s??this.offset,this.mouseHandlerCallback=h??this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(r){if(this.coordinateList.length!==0)switch(r.key){case"ArrowRight":{if(this.layout!=="horizontal")return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break}case"ArrowLeft":{if(this.layout!=="horizontal")return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse();break}}}},{key:"setIndex",value:function(r){this.activeIndex=r}},{key:"spoofMouse",value:function(){var r,n;if(this.layout==="horizontal"&&this.coordinateList.length!==0){var i=this.container.getBoundingClientRect(),a=i.x,o=i.y,u=i.height,c=this.coordinateList[this.activeIndex].coordinate,l=((r=window)===null||r===void 0?void 0:r.scrollX)||0,f=((n=window)===null||n===void 0?void 0:n.scrollY)||0,s=a+c+l,p=o+this.offset.top+u/2+f;this.mouseHandlerCallback({pageX:s,pageY:p})}}}])}();function BW(e,t,r){if(r==="number"&&t===!0&&Array.isArray(e)){var n=e==null?void 0:e[0],i=e==null?void 0:e[1];if(n&&i&&L(n)&&L(i))return!0}return!1}function FW(e,t,r,n){var i=n/2;return{stroke:"none",fill:"#ccc",x:e==="horizontal"?t.x-i:r.left+.5,y:e==="horizontal"?r.top+.5:t.y-i,width:e==="horizontal"?n:r.width-1,height:e==="horizontal"?r.height-1:n}}function Hm(e){var t=e.cx,r=e.cy,n=e.radius,i=e.startAngle,a=e.endAngle,o=se(t,r,n,i),u=se(t,r,n,a);return{points:[o,u],cx:t,cy:r,radius:n,startAngle:i,endAngle:a}}function WW(e,t,r){var n,i,a,o;if(e==="horizontal")n=t.x,a=n,i=r.top,o=r.top+r.height;else if(e==="vertical")i=t.y,o=i,n=r.left,a=r.left+r.width;else if(t.cx!=null&&t.cy!=null)if(e==="centric"){var u=t.cx,c=t.cy,l=t.innerRadius,f=t.outerRadius,s=t.angle,p=se(u,c,l,s),h=se(u,c,f,s);n=p.x,i=p.y,a=h.x,o=h.y}else return Hm(t);return[{x:n,y:i},{x:a,y:o}]}function fi(e){"@babel/helpers - typeof";return fi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},fi(e)}function rd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Di(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?rd(Object(r),!0).forEach(function(n){zW(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):rd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function zW(e,t,r){return t=UW(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function UW(e){var t=qW(e,"string");return fi(t)=="symbol"?t:t+""}function qW(e,t){if(fi(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(fi(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function HW(e){var t,r,n=e.element,i=e.tooltipEventType,a=e.isActive,o=e.activeCoordinate,u=e.activePayload,c=e.offset,l=e.activeTooltipIndex,f=e.tooltipAxisBandSize,s=e.layout,p=e.chartName,h=(t=n.props.cursor)!==null&&t!==void 0?t:(r=n.type.defaultProps)===null||r===void 0?void 0:r.cursor;if(!n||!h||!a||!o||p!=="ScatterChart"&&i!=="axis")return null;var v,d=va;if(p==="ScatterChart")v=o,d=dR;else if(p==="BarChart")v=FW(s,o,c,f),d=Rl;else if(s==="radial"){var y=Hm(o),b=y.cx,w=y.cy,x=y.radius,A=y.startAngle,m=y.endAngle;v={cx:b,cy:w,startAngle:A,endAngle:m,innerRadius:x,outerRadius:x},d=Wy}else v={points:WW(s,o,c)},d=va;var g=Di(Di(Di(Di({stroke:"#ccc",pointerEvents:"none"},c),v),H(h,!1)),{},{payload:u,payloadIndex:l,className:J("recharts-tooltip-cursor",h.className)});return R.isValidElement(h)?R.cloneElement(h,g):R.createElement(d,g)}var KW=["item"],GW=["children","className","width","height","style","compact","title","desc"];function Yr(e){"@babel/helpers - typeof";return Yr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Yr(e)}function Or(){return Or=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Or.apply(this,arguments)}function nd(e,t){return YW(e)||XW(e,t)||Gm(e,t)||VW()}function VW(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function XW(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){l=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return u}}function YW(e){if(Array.isArray(e))return e}function id(e,t){if(e==null)return{};var r=ZW(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function ZW(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function JW(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function ad(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Vm(n.key),n)}}function QW(e,t,r){return t&&ad(e.prototype,t),r&&ad(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function ez(e,t,r){return t=La(t),tz(e,Km()?Reflect.construct(t,r||[],La(e).constructor):t.apply(e,r))}function tz(e,t){if(t&&(Yr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return rz(e)}function rz(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Km(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Km=function(){return!!e})()}function La(e){return La=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},La(e)}function nz(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Rc(e,t)}function Rc(e,t){return Rc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Rc(e,t)}function Zr(e){return oz(e)||az(e)||Gm(e)||iz()}function iz(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Gm(e,t){if(e){if(typeof e=="string")return Lc(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Lc(e,t)}}function az(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function oz(e){if(Array.isArray(e))return Lc(e)}function Lc(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function od(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function j(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?od(Object(r),!0).forEach(function(n){K(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):od(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function K(e,t,r){return t=Vm(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Vm(e){var t=uz(e,"string");return Yr(t)=="symbol"?t:t+""}function uz(e,t){if(Yr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Yr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var cz={xAxis:["bottom","top"],yAxis:["left","right"]},lz={width:"100%",height:"100%"},Xm={x:0,y:0};function Ni(e){return e}var sz=function(t,r){return r==="horizontal"?t.x:r==="vertical"?t.y:r==="centric"?t.angle:t.radius},fz=function(t,r,n,i){var a=r.find(function(f){return f&&f.index===n});if(a){if(t==="horizontal")return{x:a.coordinate,y:i.y};if(t==="vertical")return{x:i.x,y:a.coordinate};if(t==="centric"){var o=a.coordinate,u=i.radius;return j(j(j({},i),se(i.cx,i.cy,u,o)),{},{angle:o,radius:u})}var c=a.coordinate,l=i.angle;return j(j(j({},i),se(i.cx,i.cy,c,l)),{},{angle:l,radius:c})}return Xm},Do=function(t,r){var n=r.graphicalItems,i=r.dataStartIndex,a=r.dataEndIndex,o=(n??[]).reduce(function(u,c){var l=c.props.data;return l&&l.length?[].concat(Zr(u),Zr(l)):u},[]);return o.length>0?o:t&&t.length&&L(i)&&L(a)?t.slice(i,a+1):[]};function Ym(e){return e==="number"?[0,"auto"]:void 0}var Bc=function(t,r,n,i){var a=t.graphicalItems,o=t.tooltipAxis,u=Do(r,t);return n<0||!a||!a.length||n>=u.length?null:a.reduce(function(c,l){var f,s=(f=l.props.data)!==null&&f!==void 0?f:r;s&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=n&&(s=s.slice(t.dataStartIndex,t.dataEndIndex+1));var p;if(o.dataKey&&!o.allowDuplicatedCategory){var h=s===void 0?u:s;p=Li(h,o.dataKey,i)}else p=s&&s[n]||u[n];return p?[].concat(Zr(c),[Dy(l,p)]):c},[])},ud=function(t,r,n,i){var a=i||{x:t.chartX,y:t.chartY},o=sz(a,n),u=t.orderedTooltipTicks,c=t.tooltipAxis,l=t.tooltipTicks,f=Bk(o,u,l,c);if(f>=0&&l){var s=l[f]&&l[f].value,p=Bc(t,r,f,s),h=fz(n,u,f,a);return{activeTooltipIndex:f,activeLabel:s,activePayload:p,activeCoordinate:h}}return null},pz=function(t,r){var n=r.axes,i=r.graphicalItems,a=r.axisType,o=r.axisIdKey,u=r.stackGroups,c=r.dataStartIndex,l=r.dataEndIndex,f=t.layout,s=t.children,p=t.stackOffset,h=Ey(f,a);return n.reduce(function(v,d){var y,b=d.type.defaultProps!==void 0?j(j({},d.type.defaultProps),d.props):d.props,w=b.type,x=b.dataKey,A=b.allowDataOverflow,m=b.allowDuplicatedCategory,g=b.scale,O=b.ticks,P=b.includeHidden,_=b[o];if(v[_])return v;var E=Do(t.data,{graphicalItems:i.filter(function(z){var V,fe=o in z.props?z.props[o]:(V=z.type.defaultProps)===null||V===void 0?void 0:V[o];return fe===_}),dataStartIndex:c,dataEndIndex:l}),$=E.length,T,I,C;BW(b.domain,A,w)&&(T=Qu(b.domain,null,A),h&&(w==="number"||g!=="auto")&&(C=_n(E,x,"category")));var M=Ym(w);if(!T||T.length===0){var k,D=(k=b.domain)!==null&&k!==void 0?k:M;if(x){if(T=_n(E,x,w),w==="category"&&h){var B=Mx(T);m&&B?(I=T,T=Pa(0,$)):m||(T=wp(D,T,d).reduce(function(z,V){return z.indexOf(V)>=0?z:[].concat(Zr(z),[V])},[]))}else if(w==="category")m?T=T.filter(function(z){return z!==""&&!Y(z)}):T=wp(D,T,d).reduce(function(z,V){return z.indexOf(V)>=0||V===""||Y(V)?z:[].concat(Zr(z),[V])},[]);else if(w==="number"){var F=qk(E,i.filter(function(z){var V,fe,me=o in z.props?z.props[o]:(V=z.type.defaultProps)===null||V===void 0?void 0:V[o],Fe="hide"in z.props?z.props.hide:(fe=z.type.defaultProps)===null||fe===void 0?void 0:fe.hide;return me===_&&(P||!Fe)}),x,a,f);F&&(T=F)}h&&(w==="number"||g!=="auto")&&(C=_n(E,x,"category"))}else h?T=Pa(0,$):u&&u[_]&&u[_].hasStack&&w==="number"?T=p==="expand"?[0,1]:ky(u[_].stackGroups,c,l):T=Ty(E,i.filter(function(z){var V=o in z.props?z.props[o]:z.type.defaultProps[o],fe="hide"in z.props?z.props.hide:z.type.defaultProps.hide;return V===_&&(P||!fe)}),w,f,!0);if(w==="number")T=Nc(s,T,_,a,O),D&&(T=Qu(D,T,A));else if(w==="category"&&D){var q=D,G=T.every(function(z){return q.indexOf(z)>=0});G&&(T=q)}}return j(j({},v),{},K({},_,j(j({},b),{},{axisType:a,domain:T,categoricalDomain:C,duplicateDomain:I,originalDomain:(y=b.domain)!==null&&y!==void 0?y:M,isCategorical:h,layout:f})))},{})},hz=function(t,r){var n=r.graphicalItems,i=r.Axis,a=r.axisType,o=r.axisIdKey,u=r.stackGroups,c=r.dataStartIndex,l=r.dataEndIndex,f=t.layout,s=t.children,p=Do(t.data,{graphicalItems:n,dataStartIndex:c,dataEndIndex:l}),h=p.length,v=Ey(f,a),d=-1;return n.reduce(function(y,b){var w=b.type.defaultProps!==void 0?j(j({},b.type.defaultProps),b.props):b.props,x=w[o],A=Ym("number");if(!y[x]){d++;var m;return v?m=Pa(0,h):u&&u[x]&&u[x].hasStack?(m=ky(u[x].stackGroups,c,l),m=Nc(s,m,x,a)):(m=Qu(A,Ty(p,n.filter(function(g){var O,P,_=o in g.props?g.props[o]:(O=g.type.defaultProps)===null||O===void 0?void 0:O[o],E="hide"in g.props?g.props.hide:(P=g.type.defaultProps)===null||P===void 0?void 0:P.hide;return _===x&&!E}),"number",f),i.defaultProps.allowDataOverflow),m=Nc(s,m,x,a)),j(j({},y),{},K({},x,j(j({axisType:a},i.defaultProps),{},{hide:!0,orientation:Ke(cz,"".concat(a,".").concat(d%2),null),domain:m,originalDomain:A,isCategorical:v,layout:f})))}return y},{})},dz=function(t,r){var n=r.axisType,i=n===void 0?"xAxis":n,a=r.AxisComp,o=r.graphicalItems,u=r.stackGroups,c=r.dataStartIndex,l=r.dataEndIndex,f=t.children,s="".concat(i,"Id"),p=Ge(f,a),h={};return p&&p.length?h=pz(t,{axes:p,graphicalItems:o,axisType:i,axisIdKey:s,stackGroups:u,dataStartIndex:c,dataEndIndex:l}):o&&o.length&&(h=hz(t,{Axis:a,graphicalItems:o,axisType:i,axisIdKey:s,stackGroups:u,dataStartIndex:c,dataEndIndex:l})),h},vz=function(t){var r=It(t),n=xt(r,!1,!0);return{tooltipTicks:n,orderedTooltipTicks:ll(n,function(i){return i.coordinate}),tooltipAxis:r,tooltipAxisBandSize:pa(r,n)}},cd=function(t){var r=t.children,n=t.defaultShowTooltip,i=Ue(r,Wr),a=0,o=0;return t.data&&t.data.length!==0&&(o=t.data.length-1),i&&i.props&&(i.props.startIndex>=0&&(a=i.props.startIndex),i.props.endIndex>=0&&(o=i.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:a,dataEndIndex:o,activeTooltipIndex:-1,isTooltipActive:!!n}},yz=function(t){return!t||!t.length?!1:t.some(function(r){var n=wt(r&&r.type);return n&&n.indexOf("Bar")>=0})},ld=function(t){return t==="horizontal"?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:t==="vertical"?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:t==="centric"?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},mz=function(t,r){var n=t.props,i=t.graphicalItems,a=t.xAxisMap,o=a===void 0?{}:a,u=t.yAxisMap,c=u===void 0?{}:u,l=n.width,f=n.height,s=n.children,p=n.margin||{},h=Ue(s,Wr),v=Ue(s,Sr),d=Object.keys(c).reduce(function(m,g){var O=c[g],P=O.orientation;return!O.mirror&&!O.hide?j(j({},m),{},K({},P,m[P]+O.width)):m},{left:p.left||0,right:p.right||0}),y=Object.keys(o).reduce(function(m,g){var O=o[g],P=O.orientation;return!O.mirror&&!O.hide?j(j({},m),{},K({},P,Ke(m,"".concat(P))+O.height)):m},{top:p.top||0,bottom:p.bottom||0}),b=j(j({},y),d),w=b.bottom;h&&(b.bottom+=h.props.height||Wr.defaultProps.height),v&&r&&(b=zk(b,i,n,r));var x=l-b.left-b.right,A=f-b.top-b.bottom;return j(j({brushBottom:w},b),{},{width:Math.max(x,0),height:Math.max(A,0)})},gz=function(t,r){if(r==="xAxis")return t[r].width;if(r==="yAxis")return t[r].height},Gl=function(t){var r=t.chartName,n=t.GraphicalChild,i=t.defaultTooltipEventType,a=i===void 0?"axis":i,o=t.validateTooltipEventTypes,u=o===void 0?["axis"]:o,c=t.axisComponents,l=t.legendContent,f=t.formatAxisMap,s=t.defaultProps,p=function(b,w){var x=w.graphicalItems,A=w.stackGroups,m=w.offset,g=w.updateId,O=w.dataStartIndex,P=w.dataEndIndex,_=b.barSize,E=b.layout,$=b.barGap,T=b.barCategoryGap,I=b.maxBarSize,C=ld(E),M=C.numericAxisName,k=C.cateAxisName,D=yz(x),B=[];return x.forEach(function(F,q){var G=Do(b.data,{graphicalItems:[F],dataStartIndex:O,dataEndIndex:P}),z=F.type.defaultProps!==void 0?j(j({},F.type.defaultProps),F.props):F.props,V=z.dataKey,fe=z.maxBarSize,me=z["".concat(M,"Id")],Fe=z["".concat(k,"Id")],Ft={},Ne=c.reduce(function(Wt,zt){var No=w["".concat(zt.axisType,"Map")],Vl=z["".concat(zt.axisType,"Id")];No&&No[Vl]||zt.axisType==="zAxis"||or(!1);var Xl=No[Vl];return j(j({},Wt),{},K(K({},zt.axisType,Xl),"".concat(zt.axisType,"Ticks"),xt(Xl)))},Ft),W=Ne[k],Z=Ne["".concat(k,"Ticks")],Q=A&&A[me]&&A[me].hasStack&&Qk(F,A[me].stackGroups),N=wt(F.type).indexOf("Bar")>=0,ve=pa(W,Z),ee=[],xe=D&&Fk({barSize:_,stackGroups:A,totalSize:gz(Ne,k)});if(N){var we,Re,jt=Y(fe)?I:fe,hr=(we=(Re=pa(W,Z,!0))!==null&&Re!==void 0?Re:jt)!==null&&we!==void 0?we:0;ee=Wk({barGap:$,barCategoryGap:T,bandSize:hr!==ve?hr:ve,sizeList:xe[Fe],maxBarSize:jt}),hr!==ve&&(ee=ee.map(function(Wt){return j(j({},Wt),{},{position:j(j({},Wt.position),{},{offset:Wt.position.offset-hr/2})})}))}var wi=F&&F.type&&F.type.getComposedData;wi&&B.push({props:j(j({},wi(j(j({},Ne),{},{displayedData:G,props:b,dataKey:V,item:F,bandSize:ve,barPosition:ee,offset:m,stackedData:Q,layout:E,dataStartIndex:O,dataEndIndex:P}))),{},K(K(K({key:F.key||"item-".concat(q)},M,Ne[M]),k,Ne[k]),"animationId",g)),childIndex:qx(F,b.children),item:F})}),B},h=function(b,w){var x=b.props,A=b.dataStartIndex,m=b.dataEndIndex,g=b.updateId;if(!ls({props:x}))return null;var O=x.children,P=x.layout,_=x.stackOffset,E=x.data,$=x.reverseStackOrder,T=ld(P),I=T.numericAxisName,C=T.cateAxisName,M=Ge(O,n),k=Zk(E,M,"".concat(I,"Id"),"".concat(C,"Id"),_,$),D=c.reduce(function(z,V){var fe="".concat(V.axisType,"Map");return j(j({},z),{},K({},fe,dz(x,j(j({},V),{},{graphicalItems:M,stackGroups:V.axisType===I&&k,dataStartIndex:A,dataEndIndex:m}))))},{}),B=mz(j(j({},D),{},{props:x,graphicalItems:M}),w==null?void 0:w.legendBBox);Object.keys(D).forEach(function(z){D[z]=f(x,D[z],B,z.replace("Map",""),r)});var F=D["".concat(C,"Map")],q=vz(F),G=p(x,j(j({},D),{},{dataStartIndex:A,dataEndIndex:m,updateId:g,graphicalItems:M,stackGroups:k,offset:B}));return j(j({formattedGraphicalItems:G,graphicalItems:M,offset:B,stackGroups:k},q),D)},v=function(y){function b(w){var x,A,m;return JW(this,b),m=ez(this,b,[w]),K(m,"eventEmitterSymbol",Symbol("rechartsEventEmitter")),K(m,"accessibilityManager",new LW),K(m,"handleLegendBBoxUpdate",function(g){if(g){var O=m.state,P=O.dataStartIndex,_=O.dataEndIndex,E=O.updateId;m.setState(j({legendBBox:g},h({props:m.props,dataStartIndex:P,dataEndIndex:_,updateId:E},j(j({},m.state),{},{legendBBox:g}))))}}),K(m,"handleReceiveSyncEvent",function(g,O,P){if(m.props.syncId===g){if(P===m.eventEmitterSymbol&&typeof m.props.syncMethod!="function")return;m.applySyncEvent(O)}}),K(m,"handleBrushChange",function(g){var O=g.startIndex,P=g.endIndex;if(O!==m.state.dataStartIndex||P!==m.state.dataEndIndex){var _=m.state.updateId;m.setState(function(){return j({dataStartIndex:O,dataEndIndex:P},h({props:m.props,dataStartIndex:O,dataEndIndex:P,updateId:_},m.state))}),m.triggerSyncEvent({dataStartIndex:O,dataEndIndex:P})}}),K(m,"handleMouseEnter",function(g){var O=m.getMouseInfo(g);if(O){var P=j(j({},O),{},{isTooltipActive:!0});m.setState(P),m.triggerSyncEvent(P);var _=m.props.onMouseEnter;X(_)&&_(P,g)}}),K(m,"triggeredAfterMouseMove",function(g){var O=m.getMouseInfo(g),P=O?j(j({},O),{},{isTooltipActive:!0}):{isTooltipActive:!1};m.setState(P),m.triggerSyncEvent(P);var _=m.props.onMouseMove;X(_)&&_(P,g)}),K(m,"handleItemMouseEnter",function(g){m.setState(function(){return{isTooltipActive:!0,activeItem:g,activePayload:g.tooltipPayload,activeCoordinate:g.tooltipPosition||{x:g.cx,y:g.cy}}})}),K(m,"handleItemMouseLeave",function(){m.setState(function(){return{isTooltipActive:!1}})}),K(m,"handleMouseMove",function(g){g.persist(),m.throttleTriggeredAfterMouseMove(g)}),K(m,"handleMouseLeave",function(g){m.throttleTriggeredAfterMouseMove.cancel();var O={isTooltipActive:!1};m.setState(O),m.triggerSyncEvent(O);var P=m.props.onMouseLeave;X(P)&&P(O,g)}),K(m,"handleOuterEvent",function(g){var O=Ux(g),P=Ke(m.props,"".concat(O));if(O&&X(P)){var _,E;/.*touch.*/i.test(O)?E=m.getMouseInfo(g.changedTouches[0]):E=m.getMouseInfo(g),P((_=E)!==null&&_!==void 0?_:{},g)}}),K(m,"handleClick",function(g){var O=m.getMouseInfo(g);if(O){var P=j(j({},O),{},{isTooltipActive:!0});m.setState(P),m.triggerSyncEvent(P);var _=m.props.onClick;X(_)&&_(P,g)}}),K(m,"handleMouseDown",function(g){var O=m.props.onMouseDown;if(X(O)){var P=m.getMouseInfo(g);O(P,g)}}),K(m,"handleMouseUp",function(g){var O=m.props.onMouseUp;if(X(O)){var P=m.getMouseInfo(g);O(P,g)}}),K(m,"handleTouchMove",function(g){g.changedTouches!=null&&g.changedTouches.length>0&&m.throttleTriggeredAfterMouseMove(g.changedTouches[0])}),K(m,"handleTouchStart",function(g){g.changedTouches!=null&&g.changedTouches.length>0&&m.handleMouseDown(g.changedTouches[0])}),K(m,"handleTouchEnd",function(g){g.changedTouches!=null&&g.changedTouches.length>0&&m.handleMouseUp(g.changedTouches[0])}),K(m,"handleDoubleClick",function(g){var O=m.props.onDoubleClick;if(X(O)){var P=m.getMouseInfo(g);O(P,g)}}),K(m,"handleContextMenu",function(g){var O=m.props.onContextMenu;if(X(O)){var P=m.getMouseInfo(g);O(P,g)}}),K(m,"triggerSyncEvent",function(g){m.props.syncId!==void 0&&lu.emit(su,m.props.syncId,g,m.eventEmitterSymbol)}),K(m,"applySyncEvent",function(g){var O=m.props,P=O.layout,_=O.syncMethod,E=m.state.updateId,$=g.dataStartIndex,T=g.dataEndIndex;if(g.dataStartIndex!==void 0||g.dataEndIndex!==void 0)m.setState(j({dataStartIndex:$,dataEndIndex:T},h({props:m.props,dataStartIndex:$,dataEndIndex:T,updateId:E},m.state)));else if(g.activeTooltipIndex!==void 0){var I=g.chartX,C=g.chartY,M=g.activeTooltipIndex,k=m.state,D=k.offset,B=k.tooltipTicks;if(!D)return;if(typeof _=="function")M=_(B,g);else if(_==="value"){M=-1;for(var F=0;F<B.length;F++)if(B[F].value===g.activeLabel){M=F;break}}var q=j(j({},D),{},{x:D.left,y:D.top}),G=Math.min(I,q.x+q.width),z=Math.min(C,q.y+q.height),V=B[M]&&B[M].value,fe=Bc(m.state,m.props.data,M),me=B[M]?{x:P==="horizontal"?B[M].coordinate:G,y:P==="horizontal"?z:B[M].coordinate}:Xm;m.setState(j(j({},g),{},{activeLabel:V,activeCoordinate:me,activePayload:fe,activeTooltipIndex:M}))}else m.setState(g)}),K(m,"renderCursor",function(g){var O,P=m.state,_=P.isTooltipActive,E=P.activeCoordinate,$=P.activePayload,T=P.offset,I=P.activeTooltipIndex,C=P.tooltipAxisBandSize,M=m.getTooltipEventType(),k=(O=g.props.active)!==null&&O!==void 0?O:_,D=m.props.layout,B=g.key||"_recharts-cursor";return S.createElement(HW,{key:B,activeCoordinate:E,activePayload:$,activeTooltipIndex:I,chartName:r,element:g,isActive:k,layout:D,offset:T,tooltipAxisBandSize:C,tooltipEventType:M})}),K(m,"renderPolarAxis",function(g,O,P){var _=Ke(g,"type.axisType"),E=Ke(m.state,"".concat(_,"Map")),$=g.type.defaultProps,T=$!==void 0?j(j({},$),g.props):g.props,I=E&&E[T["".concat(_,"Id")]];return R.cloneElement(g,j(j({},I),{},{className:J(_,I.className),key:g.key||"".concat(O,"-").concat(P),ticks:xt(I,!0)}))}),K(m,"renderPolarGrid",function(g){var O=g.props,P=O.radialLines,_=O.polarAngles,E=O.polarRadius,$=m.state,T=$.radiusAxisMap,I=$.angleAxisMap,C=It(T),M=It(I),k=M.cx,D=M.cy,B=M.innerRadius,F=M.outerRadius;return R.cloneElement(g,{polarAngles:Array.isArray(_)?_:xt(M,!0).map(function(q){return q.coordinate}),polarRadius:Array.isArray(E)?E:xt(C,!0).map(function(q){return q.coordinate}),cx:k,cy:D,innerRadius:B,outerRadius:F,key:g.key||"polar-grid",radialLines:P})}),K(m,"renderLegend",function(){var g=m.state.formattedGraphicalItems,O=m.props,P=O.children,_=O.width,E=O.height,$=m.props.margin||{},T=_-($.left||0)-($.right||0),I=_y({children:P,formattedGraphicalItems:g,legendWidth:T,legendContent:l});if(!I)return null;var C=I.item,M=id(I,KW);return R.cloneElement(C,j(j({},M),{},{chartWidth:_,chartHeight:E,margin:$,onBBoxUpdate:m.handleLegendBBoxUpdate}))}),K(m,"renderTooltip",function(){var g,O=m.props,P=O.children,_=O.accessibilityLayer,E=Ue(P,yt);if(!E)return null;var $=m.state,T=$.isTooltipActive,I=$.activeCoordinate,C=$.activePayload,M=$.activeLabel,k=$.offset,D=(g=E.props.active)!==null&&g!==void 0?g:T;return R.cloneElement(E,{viewBox:j(j({},k),{},{x:k.left,y:k.top}),active:D,label:M,payload:D?C:[],coordinate:I,accessibilityLayer:_})}),K(m,"renderBrush",function(g){var O=m.props,P=O.margin,_=O.data,E=m.state,$=E.offset,T=E.dataStartIndex,I=E.dataEndIndex,C=E.updateId;return R.cloneElement(g,{key:g.key||"_recharts-brush",onChange:Mi(m.handleBrushChange,g.props.onChange),data:_,x:L(g.props.x)?g.props.x:$.left,y:L(g.props.y)?g.props.y:$.top+$.height+$.brushBottom-(P.bottom||0),width:L(g.props.width)?g.props.width:$.width,startIndex:T,endIndex:I,updateId:"brush-".concat(C)})}),K(m,"renderReferenceElement",function(g,O,P){if(!g)return null;var _=m,E=_.clipPathId,$=m.state,T=$.xAxisMap,I=$.yAxisMap,C=$.offset,M=g.type.defaultProps||{},k=g.props,D=k.xAxisId,B=D===void 0?M.xAxisId:D,F=k.yAxisId,q=F===void 0?M.yAxisId:F;return R.cloneElement(g,{key:g.key||"".concat(O,"-").concat(P),xAxis:T[B],yAxis:I[q],viewBox:{x:C.left,y:C.top,width:C.width,height:C.height},clipPathId:E})}),K(m,"renderActivePoints",function(g){var O=g.item,P=g.activePoint,_=g.basePoint,E=g.childIndex,$=g.isRange,T=[],I=O.props.key,C=O.item.type.defaultProps!==void 0?j(j({},O.item.type.defaultProps),O.item.props):O.item.props,M=C.activeDot,k=C.dataKey,D=j(j({index:E,dataKey:k,cx:P.x,cy:P.y,r:4,fill:Nl(O.item),strokeWidth:2,stroke:"#fff",payload:P.payload,value:P.value},H(M,!1)),Bi(M));return T.push(b.renderActiveDot(M,D,"".concat(I,"-activePoint-").concat(E))),_?T.push(b.renderActiveDot(M,j(j({},D),{},{cx:_.x,cy:_.y}),"".concat(I,"-basePoint-").concat(E))):$&&T.push(null),T}),K(m,"renderGraphicChild",function(g,O,P){var _=m.filterFormatItem(g,O,P);if(!_)return null;var E=m.getTooltipEventType(),$=m.state,T=$.isTooltipActive,I=$.tooltipAxis,C=$.activeTooltipIndex,M=$.activeLabel,k=m.props.children,D=Ue(k,yt),B=_.props,F=B.points,q=B.isRange,G=B.baseLine,z=_.item.type.defaultProps!==void 0?j(j({},_.item.type.defaultProps),_.item.props):_.item.props,V=z.activeDot,fe=z.hide,me=z.activeBar,Fe=z.activeShape,Ft=!!(!fe&&T&&D&&(V||me||Fe)),Ne={};E!=="axis"&&D&&D.props.trigger==="click"?Ne={onClick:Mi(m.handleItemMouseEnter,g.props.onClick)}:E!=="axis"&&(Ne={onMouseLeave:Mi(m.handleItemMouseLeave,g.props.onMouseLeave),onMouseEnter:Mi(m.handleItemMouseEnter,g.props.onMouseEnter)});var W=R.cloneElement(g,j(j({},_.props),Ne));function Z(zt){return typeof I.dataKey=="function"?I.dataKey(zt.payload):null}if(Ft)if(C>=0){var Q,N;if(I.dataKey&&!I.allowDuplicatedCategory){var ve=typeof I.dataKey=="function"?Z:"payload.".concat(I.dataKey.toString());Q=Li(F,ve,M),N=q&&G&&Li(G,ve,M)}else Q=F==null?void 0:F[C],N=q&&G&&G[C];if(Fe||me){var ee=g.props.activeIndex!==void 0?g.props.activeIndex:C;return[R.cloneElement(g,j(j(j({},_.props),Ne),{},{activeIndex:ee})),null,null]}if(!Y(Q))return[W].concat(Zr(m.renderActivePoints({item:_,activePoint:Q,basePoint:N,childIndex:C,isRange:q})))}else{var xe,we=(xe=m.getItemByXY(m.state.activeCoordinate))!==null&&xe!==void 0?xe:{graphicalItem:W},Re=we.graphicalItem,jt=Re.item,hr=jt===void 0?g:jt,wi=Re.childIndex,Wt=j(j(j({},_.props),Ne),{},{activeIndex:wi});return[R.cloneElement(hr,Wt),null,null]}return q?[W,null,null]:[W,null]}),K(m,"renderCustomized",function(g,O,P){return R.cloneElement(g,j(j({key:"recharts-customized-".concat(P)},m.props),m.state))}),K(m,"renderMap",{CartesianGrid:{handler:Ni,once:!0},ReferenceArea:{handler:m.renderReferenceElement},ReferenceLine:{handler:Ni},ReferenceDot:{handler:m.renderReferenceElement},XAxis:{handler:Ni},YAxis:{handler:Ni},Brush:{handler:m.renderBrush,once:!0},Bar:{handler:m.renderGraphicChild},Line:{handler:m.renderGraphicChild},Area:{handler:m.renderGraphicChild},Radar:{handler:m.renderGraphicChild},RadialBar:{handler:m.renderGraphicChild},Scatter:{handler:m.renderGraphicChild},Pie:{handler:m.renderGraphicChild},Funnel:{handler:m.renderGraphicChild},Tooltip:{handler:m.renderCursor,once:!0},PolarGrid:{handler:m.renderPolarGrid,once:!0},PolarAngleAxis:{handler:m.renderPolarAxis},PolarRadiusAxis:{handler:m.renderPolarAxis},Customized:{handler:m.renderCustomized}}),m.clipPathId="".concat((x=w.id)!==null&&x!==void 0?x:nn("recharts"),"-clip"),m.throttleTriggeredAfterMouseMove=Pv(m.triggeredAfterMouseMove,(A=w.throttleDelay)!==null&&A!==void 0?A:1e3/60),m.state={},m}return nz(b,y),QW(b,[{key:"componentDidMount",value:function(){var x,A;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:(x=this.props.margin.left)!==null&&x!==void 0?x:0,top:(A=this.props.margin.top)!==null&&A!==void 0?A:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var x=this.props,A=x.children,m=x.data,g=x.height,O=x.layout,P=Ue(A,yt);if(P){var _=P.props.defaultIndex;if(!(typeof _!="number"||_<0||_>this.state.tooltipTicks.length-1)){var E=this.state.tooltipTicks[_]&&this.state.tooltipTicks[_].value,$=Bc(this.state,m,_,E),T=this.state.tooltipTicks[_].coordinate,I=(this.state.offset.top+g)/2,C=O==="horizontal",M=C?{x:T,y:I}:{y:T,x:I},k=this.state.formattedGraphicalItems.find(function(B){var F=B.item;return F.type.name==="Scatter"});k&&(M=j(j({},M),k.props.points[_].tooltipPosition),$=k.props.points[_].tooltipPayload);var D={activeTooltipIndex:_,isTooltipActive:!0,activeLabel:E,activePayload:$,activeCoordinate:M};this.setState(D),this.renderCursor(P),this.accessibilityManager.setIndex(_)}}}},{key:"getSnapshotBeforeUpdate",value:function(x,A){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==A.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==x.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==x.margin){var m,g;this.accessibilityManager.setDetails({offset:{left:(m=this.props.margin.left)!==null&&m!==void 0?m:0,top:(g=this.props.margin.top)!==null&&g!==void 0?g:0}})}return null}},{key:"componentDidUpdate",value:function(x){du([Ue(x.children,yt)],[Ue(this.props.children,yt)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var x=Ue(this.props.children,yt);if(x&&typeof x.props.shared=="boolean"){var A=x.props.shared?"axis":"item";return u.indexOf(A)>=0?A:a}return a}},{key:"getMouseInfo",value:function(x){if(!this.container)return null;var A=this.container,m=A.getBoundingClientRect(),g=hj(m),O={chartX:Math.round(x.pageX-g.left),chartY:Math.round(x.pageY-g.top)},P=m.width/A.offsetWidth||1,_=this.inRange(O.chartX,O.chartY,P);if(!_)return null;var E=this.state,$=E.xAxisMap,T=E.yAxisMap,I=this.getTooltipEventType(),C=ud(this.state,this.props.data,this.props.layout,_);if(I!=="axis"&&$&&T){var M=It($).scale,k=It(T).scale,D=M&&M.invert?M.invert(O.chartX):null,B=k&&k.invert?k.invert(O.chartY):null;return j(j({},O),{},{xValue:D,yValue:B},C)}return C?j(j({},O),C):null}},{key:"inRange",value:function(x,A){var m=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,g=this.props.layout,O=x/m,P=A/m;if(g==="horizontal"||g==="vertical"){var _=this.state.offset,E=O>=_.left&&O<=_.left+_.width&&P>=_.top&&P<=_.top+_.height;return E?{x:O,y:P}:null}var $=this.state,T=$.angleAxisMap,I=$.radiusAxisMap;if(T&&I){var C=It(T);return Sp({x:O,y:P},C)}return null}},{key:"parseEventsOfWrapper",value:function(){var x=this.props.children,A=this.getTooltipEventType(),m=Ue(x,yt),g={};m&&A==="axis"&&(m.props.trigger==="click"?g={onClick:this.handleClick}:g={onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu});var O=Bi(this.props,this.handleOuterEvent);return j(j({},O),g)}},{key:"addListener",value:function(){lu.on(su,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){lu.removeListener(su,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(x,A,m){for(var g=this.state.formattedGraphicalItems,O=0,P=g.length;O<P;O++){var _=g[O];if(_.item===x||_.props.key===x.key||A===wt(_.item.type)&&m===_.childIndex)return _}return null}},{key:"renderClipPath",value:function(){var x=this.clipPathId,A=this.state.offset,m=A.left,g=A.top,O=A.height,P=A.width;return S.createElement("defs",null,S.createElement("clipPath",{id:x},S.createElement("rect",{x:m,y:g,height:O,width:P})))}},{key:"getXScales",value:function(){var x=this.state.xAxisMap;return x?Object.entries(x).reduce(function(A,m){var g=nd(m,2),O=g[0],P=g[1];return j(j({},A),{},K({},O,P.scale))},{}):null}},{key:"getYScales",value:function(){var x=this.state.yAxisMap;return x?Object.entries(x).reduce(function(A,m){var g=nd(m,2),O=g[0],P=g[1];return j(j({},A),{},K({},O,P.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(x){var A;return(A=this.state.xAxisMap)===null||A===void 0||(A=A[x])===null||A===void 0?void 0:A.scale}},{key:"getYScaleByAxisId",value:function(x){var A;return(A=this.state.yAxisMap)===null||A===void 0||(A=A[x])===null||A===void 0?void 0:A.scale}},{key:"getItemByXY",value:function(x){var A=this.state,m=A.formattedGraphicalItems,g=A.activeItem;if(m&&m.length)for(var O=0,P=m.length;O<P;O++){var _=m[O],E=_.props,$=_.item,T=$.type.defaultProps!==void 0?j(j({},$.type.defaultProps),$.props):$.props,I=wt($.type);if(I==="Bar"){var C=(E.data||[]).find(function(B){return VN(x,B)});if(C)return{graphicalItem:_,payload:C}}else if(I==="RadialBar"){var M=(E.data||[]).find(function(B){return Sp(x,B)});if(M)return{graphicalItem:_,payload:M}}else if(Po(_,g)||_o(_,g)||ai(_,g)){var k=NL({graphicalItem:_,activeTooltipItem:g,itemData:T.data}),D=T.activeIndex===void 0?k:T.activeIndex;return{graphicalItem:j(j({},_),{},{childIndex:D}),payload:ai(_,g)?T.data[k]:_.props.data[k]}}}return null}},{key:"render",value:function(){var x=this;if(!ls(this))return null;var A=this.props,m=A.children,g=A.className,O=A.width,P=A.height,_=A.style,E=A.compact,$=A.title,T=A.desc,I=id(A,GW),C=H(I,!1);if(E)return S.createElement(Ch,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},S.createElement(yu,Or({},C,{width:O,height:P,title:$,desc:T}),this.renderClipPath(),fs(m,this.renderMap)));if(this.props.accessibilityLayer){var M,k;C.tabIndex=(M=this.props.tabIndex)!==null&&M!==void 0?M:0,C.role=(k=this.props.role)!==null&&k!==void 0?k:"application",C.onKeyDown=function(B){x.accessibilityManager.keyboardEvent(B)},C.onFocus=function(){x.accessibilityManager.focus()}}var D=this.parseEventsOfWrapper();return S.createElement(Ch,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},S.createElement("div",Or({className:J("recharts-wrapper",g),style:j({position:"relative",cursor:"default",width:O,height:P},_)},D,{ref:function(F){x.container=F}}),S.createElement(yu,Or({},C,{width:O,height:P,title:$,desc:T,style:lz}),this.renderClipPath(),fs(m,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}])}(R.Component);K(v,"displayName",r),K(v,"defaultProps",j({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},s)),K(v,"getDerivedStateFromProps",function(y,b){var w=y.dataKey,x=y.data,A=y.children,m=y.width,g=y.height,O=y.layout,P=y.stackOffset,_=y.margin,E=b.dataStartIndex,$=b.dataEndIndex;if(b.updateId===void 0){var T=cd(y);return j(j(j({},T),{},{updateId:0},h(j(j({props:y},T),{},{updateId:0}),b)),{},{prevDataKey:w,prevData:x,prevWidth:m,prevHeight:g,prevLayout:O,prevStackOffset:P,prevMargin:_,prevChildren:A})}if(w!==b.prevDataKey||x!==b.prevData||m!==b.prevWidth||g!==b.prevHeight||O!==b.prevLayout||P!==b.prevStackOffset||!Ar(_,b.prevMargin)){var I=cd(y),C={chartX:b.chartX,chartY:b.chartY,isTooltipActive:b.isTooltipActive},M=j(j({},ud(b,x,O)),{},{updateId:b.updateId+1}),k=j(j(j({},I),C),M);return j(j(j({},k),h(j({props:y},k),b)),{},{prevDataKey:w,prevData:x,prevWidth:m,prevHeight:g,prevLayout:O,prevStackOffset:P,prevMargin:_,prevChildren:A})}if(!du(A,b.prevChildren)){var D,B,F,q,G=Ue(A,Wr),z=G&&(D=(B=G.props)===null||B===void 0?void 0:B.startIndex)!==null&&D!==void 0?D:E,V=G&&(F=(q=G.props)===null||q===void 0?void 0:q.endIndex)!==null&&F!==void 0?F:$,fe=z!==E||V!==$,me=!Y(x),Fe=me&&!fe?b.updateId:b.updateId+1;return j(j({updateId:Fe},h(j(j({props:y},b),{},{updateId:Fe,dataStartIndex:z,dataEndIndex:V}),b)),{},{prevChildren:A,dataStartIndex:z,dataEndIndex:V})}return null}),K(v,"renderActiveDot",function(y,b,w){var x;return R.isValidElement(y)?x=R.cloneElement(y,b):X(y)?x=y(b):x=S.createElement(xo,b),S.createElement(te,{className:"recharts-active-dot",key:w},x)});var d=R.forwardRef(function(b,w){return S.createElement(v,Or({},b,{ref:w}))});return d.displayName=v.displayName,d},wz=Gl({chartName:"LineChart",GraphicalChild:Io,axisComponents:[{axisType:"xAxis",AxisComp:Co},{axisType:"yAxis",AxisComp:ko}],formatAxisMap:vm}),Oz=Gl({chartName:"BarChart",GraphicalChild:fn,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:Co},{axisType:"yAxis",AxisComp:ko}],formatAxisMap:vm}),Az=Gl({chartName:"PieChart",GraphicalChild:Bt,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",legendContent:"children",axisComponents:[{axisType:"angleAxis",AxisComp:So},{axisType:"radiusAxis",AxisComp:Oo}],formatAxisMap:l2,defaultProps:{layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}});export{Oz as B,fl as C,wz as L,Az as P,xz as R,yt as T,Co as X,ko as Y,Bt as a,J3 as b,fn as c,Io as d};
//# sourceMappingURL=charts-a310bd7c.js.map

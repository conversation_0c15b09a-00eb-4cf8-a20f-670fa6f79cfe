import{r as g,a as On,b as Tn,g as Pn,R as An}from"./vendor-fdd35676.js";import{R as _n,P as kn,a as Ln,C as Dn,T as Pe,B as Un,b as nt,X as rt,Y as st,c as Fn,L as Bn,d as In}from"./charts-a310bd7c.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const o of s)if(o.type==="childList")for(const a of o.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&r(a)}).observe(document,{childList:!0,subtree:!0});function n(s){const o={};return s.integrity&&(o.integrity=s.integrity),s.referrerPolicy&&(o.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?o.credentials="include":s.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(s){if(s.ep)return;s.ep=!0;const o=n(s);fetch(s.href,o)}})();var _t={exports:{}},ye={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Mn=g,$n=Symbol.for("react.element"),Vn=Symbol.for("react.fragment"),qn=Object.prototype.hasOwnProperty,zn=Mn.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Wn={key:!0,ref:!0,__self:!0,__source:!0};function kt(e,t,n){var r,s={},o=null,a=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(a=t.ref);for(r in t)qn.call(t,r)&&!Wn.hasOwnProperty(r)&&(s[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)s[r]===void 0&&(s[r]=t[r]);return{$$typeof:$n,type:e,key:o,ref:a,props:s,_owner:zn.current}}ye.Fragment=Vn;ye.jsx=kt;ye.jsxs=kt;_t.exports=ye;var d=_t.exports,Lt,ot=On;Lt=ot.createRoot,ot.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Z(){return Z=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Z.apply(this,arguments)}var B;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(B||(B={}));const at="popstate";function Hn(e){e===void 0&&(e={});function t(r,s){let{pathname:o,search:a,hash:i}=r.location;return Be("",{pathname:o,search:a,hash:i},s.state&&s.state.usr||null,s.state&&s.state.key||"default")}function n(r,s){return typeof s=="string"?s:ce(s)}return Kn(t,n,null,e)}function N(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Dt(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function Jn(){return Math.random().toString(36).substr(2,8)}function it(e,t){return{usr:e.state,key:e.key,idx:t}}function Be(e,t,n,r){return n===void 0&&(n=null),Z({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?J(t):t,{state:n,key:t&&t.key||r||Jn()})}function ce(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function J(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function Kn(e,t,n,r){r===void 0&&(r={});let{window:s=document.defaultView,v5Compat:o=!1}=r,a=s.history,i=B.Pop,c=null,u=l();u==null&&(u=0,a.replaceState(Z({},a.state,{idx:u}),""));function l(){return(a.state||{idx:null}).idx}function h(){i=B.Pop;let p=l(),b=p==null?null:p-u;u=p,c&&c({action:i,location:y.location,delta:b})}function x(p,b){i=B.Push;let w=Be(y.location,p,b);n&&n(w,p),u=l()+1;let E=it(w,u),R=y.createHref(w);try{a.pushState(E,"",R)}catch(C){if(C instanceof DOMException&&C.name==="DataCloneError")throw C;s.location.assign(R)}o&&c&&c({action:i,location:y.location,delta:1})}function v(p,b){i=B.Replace;let w=Be(y.location,p,b);n&&n(w,p),u=l();let E=it(w,u),R=y.createHref(w);a.replaceState(E,"",R),o&&c&&c({action:i,location:y.location,delta:0})}function m(p){let b=s.location.origin!=="null"?s.location.origin:s.location.href,w=typeof p=="string"?p:ce(p);return w=w.replace(/ $/,"%20"),N(b,"No window.location.(origin|href) available to create URL for href: "+w),new URL(w,b)}let y={get action(){return i},get location(){return e(s,a)},listen(p){if(c)throw new Error("A history only accepts one active listener");return s.addEventListener(at,h),c=p,()=>{s.removeEventListener(at,h),c=null}},createHref(p){return t(s,p)},createURL:m,encodeLocation(p){let b=m(p);return{pathname:b.pathname,search:b.search,hash:b.hash}},push:x,replace:v,go(p){return a.go(p)}};return y}var lt;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(lt||(lt={}));function Xn(e,t,n){return n===void 0&&(n="/"),Gn(e,t,n,!1)}function Gn(e,t,n,r){let s=typeof t=="string"?J(t):t,o=W(s.pathname||"/",n);if(o==null)return null;let a=Ut(e);Zn(a);let i=null;for(let c=0;i==null&&c<a.length;++c){let u=lr(o);i=ar(a[c],u,r)}return i}function Ut(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let s=(o,a,i)=>{let c={relativePath:i===void 0?o.path||"":i,caseSensitive:o.caseSensitive===!0,childrenIndex:a,route:o};c.relativePath.startsWith("/")&&(N(c.relativePath.startsWith(r),'Absolute route path "'+c.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),c.relativePath=c.relativePath.slice(r.length));let u=I([r,c.relativePath]),l=n.concat(c);o.children&&o.children.length>0&&(N(o.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),Ut(o.children,t,l,u)),!(o.path==null&&!o.index)&&t.push({path:u,score:sr(u,o.index),routesMeta:l})};return e.forEach((o,a)=>{var i;if(o.path===""||!((i=o.path)!=null&&i.includes("?")))s(o,a);else for(let c of Ft(o.path))s(o,a,c)}),t}function Ft(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,s=n.endsWith("?"),o=n.replace(/\?$/,"");if(r.length===0)return s?[o,""]:[o];let a=Ft(r.join("/")),i=[];return i.push(...a.map(c=>c===""?o:[o,c].join("/"))),s&&i.push(...a),i.map(c=>e.startsWith("/")&&c===""?"/":c)}function Zn(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:or(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const Yn=/^:[\w-]+$/,Qn=3,er=2,tr=1,nr=10,rr=-2,ct=e=>e==="*";function sr(e,t){let n=e.split("/"),r=n.length;return n.some(ct)&&(r+=rr),t&&(r+=er),n.filter(s=>!ct(s)).reduce((s,o)=>s+(Yn.test(o)?Qn:o===""?tr:nr),r)}function or(e,t){return e.length===t.length&&e.slice(0,-1).every((r,s)=>r===t[s])?e[e.length-1]-t[t.length-1]:0}function ar(e,t,n){n===void 0&&(n=!1);let{routesMeta:r}=e,s={},o="/",a=[];for(let i=0;i<r.length;++i){let c=r[i],u=i===r.length-1,l=o==="/"?t:t.slice(o.length)||"/",h=ue({path:c.relativePath,caseSensitive:c.caseSensitive,end:u},l),x=c.route;if(!h&&u&&n&&!r[r.length-1].route.index&&(h=ue({path:c.relativePath,caseSensitive:c.caseSensitive,end:!1},l)),!h)return null;Object.assign(s,h.params),a.push({params:s,pathname:I([o,h.pathname]),pathnameBase:fr(I([o,h.pathnameBase])),route:x}),h.pathnameBase!=="/"&&(o=I([o,h.pathnameBase]))}return a}function ue(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=ir(e.path,e.caseSensitive,e.end),s=t.match(n);if(!s)return null;let o=s[0],a=o.replace(/(.)\/+$/,"$1"),i=s.slice(1);return{params:r.reduce((u,l,h)=>{let{paramName:x,isOptional:v}=l;if(x==="*"){let y=i[h]||"";a=o.slice(0,o.length-y.length).replace(/(.)\/+$/,"$1")}const m=i[h];return v&&!m?u[x]=void 0:u[x]=(m||"").replace(/%2F/g,"/"),u},{}),pathname:o,pathnameBase:a,pattern:e}}function ir(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),Dt(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],s="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(a,i,c)=>(r.push({paramName:i,isOptional:c!=null}),c?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),s+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?s+="\\/*$":e!==""&&e!=="/"&&(s+="(?:(?=\\/|$))"),[new RegExp(s,t?void 0:"i"),r]}function lr(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Dt(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function W(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function cr(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:s=""}=typeof e=="string"?J(e):e;return{pathname:n?n.startsWith("/")?n:ur(n,t):t,search:hr(r),hash:pr(s)}}function ur(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(s=>{s===".."?n.length>1&&n.pop():s!=="."&&n.push(s)}),n.length>1?n.join("/"):"/"}function Ae(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function dr(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function Bt(e,t){let n=dr(e);return t?n.map((r,s)=>s===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function It(e,t,n,r){r===void 0&&(r=!1);let s;typeof e=="string"?s=J(e):(s=Z({},e),N(!s.pathname||!s.pathname.includes("?"),Ae("?","pathname","search",s)),N(!s.pathname||!s.pathname.includes("#"),Ae("#","pathname","hash",s)),N(!s.search||!s.search.includes("#"),Ae("#","search","hash",s)));let o=e===""||s.pathname==="",a=o?"/":s.pathname,i;if(a==null)i=n;else{let h=t.length-1;if(!r&&a.startsWith("..")){let x=a.split("/");for(;x[0]==="..";)x.shift(),h-=1;s.pathname=x.join("/")}i=h>=0?t[h]:"/"}let c=cr(s,i),u=a&&a!=="/"&&a.endsWith("/"),l=(o||a===".")&&n.endsWith("/");return!c.pathname.endsWith("/")&&(u||l)&&(c.pathname+="/"),c}const I=e=>e.join("/").replace(/\/\/+/g,"/"),fr=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),hr=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,pr=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function mr(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const Mt=["post","put","patch","delete"];new Set(Mt);const yr=["get",...Mt];new Set(yr);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Y(){return Y=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Y.apply(this,arguments)}const ge=g.createContext(null),$t=g.createContext(null),M=g.createContext(null),xe=g.createContext(null),$=g.createContext({outlet:null,matches:[],isDataRoute:!1}),Vt=g.createContext(null);function gr(e,t){let{relative:n}=t===void 0?{}:t;ee()||N(!1);let{basename:r,navigator:s}=g.useContext(M),{hash:o,pathname:a,search:i}=be(e,{relative:n}),c=a;return r!=="/"&&(c=a==="/"?r:I([r,a])),s.createHref({pathname:c,search:i,hash:o})}function ee(){return g.useContext(xe)!=null}function te(){return ee()||N(!1),g.useContext(xe).location}function qt(e){g.useContext(M).static||g.useLayoutEffect(e)}function xr(){let{isDataRoute:e}=g.useContext($);return e?kr():br()}function br(){ee()||N(!1);let e=g.useContext(ge),{basename:t,future:n,navigator:r}=g.useContext(M),{matches:s}=g.useContext($),{pathname:o}=te(),a=JSON.stringify(Bt(s,n.v7_relativeSplatPath)),i=g.useRef(!1);return qt(()=>{i.current=!0}),g.useCallback(function(u,l){if(l===void 0&&(l={}),!i.current)return;if(typeof u=="number"){r.go(u);return}let h=It(u,JSON.parse(a),o,l.relative==="path");e==null&&t!=="/"&&(h.pathname=h.pathname==="/"?t:I([t,h.pathname])),(l.replace?r.replace:r.push)(h,l.state,l)},[t,r,a,o,e])}const vr=g.createContext(null);function wr(e){let t=g.useContext($).outlet;return t&&g.createElement(vr.Provider,{value:e},t)}function be(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=g.useContext(M),{matches:s}=g.useContext($),{pathname:o}=te(),a=JSON.stringify(Bt(s,r.v7_relativeSplatPath));return g.useMemo(()=>It(e,JSON.parse(a),o,n==="path"),[e,a,o,n])}function Er(e,t){return Sr(e,t)}function Sr(e,t,n,r){ee()||N(!1);let{navigator:s}=g.useContext(M),{matches:o}=g.useContext($),a=o[o.length-1],i=a?a.params:{};a&&a.pathname;let c=a?a.pathnameBase:"/";a&&a.route;let u=te(),l;if(t){var h;let p=typeof t=="string"?J(t):t;c==="/"||(h=p.pathname)!=null&&h.startsWith(c)||N(!1),l=p}else l=u;let x=l.pathname||"/",v=x;if(c!=="/"){let p=c.replace(/^\//,"").split("/");v="/"+x.replace(/^\//,"").split("/").slice(p.length).join("/")}let m=Xn(e,{pathname:v}),y=Or(m&&m.map(p=>Object.assign({},p,{params:Object.assign({},i,p.params),pathname:I([c,s.encodeLocation?s.encodeLocation(p.pathname).pathname:p.pathname]),pathnameBase:p.pathnameBase==="/"?c:I([c,s.encodeLocation?s.encodeLocation(p.pathnameBase).pathname:p.pathnameBase])})),o,n,r);return t&&y?g.createElement(xe.Provider,{value:{location:Y({pathname:"/",search:"",hash:"",state:null,key:"default"},l),navigationType:B.Pop}},y):y}function Rr(){let e=_r(),t=mr(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,s={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"},o=null;return g.createElement(g.Fragment,null,g.createElement("h2",null,"Unexpected Application Error!"),g.createElement("h3",{style:{fontStyle:"italic"}},t),n?g.createElement("pre",{style:s},n):null,o)}const Cr=g.createElement(Rr,null);class Nr extends g.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?g.createElement($.Provider,{value:this.props.routeContext},g.createElement(Vt.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function jr(e){let{routeContext:t,match:n,children:r}=e,s=g.useContext(ge);return s&&s.static&&s.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(s.staticContext._deepestRenderedBoundaryId=n.route.id),g.createElement($.Provider,{value:t},r)}function Or(e,t,n,r){var s;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var o;if(!n)return null;if(n.errors)e=n.matches;else if((o=r)!=null&&o.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let a=e,i=(s=n)==null?void 0:s.errors;if(i!=null){let l=a.findIndex(h=>h.route.id&&(i==null?void 0:i[h.route.id])!==void 0);l>=0||N(!1),a=a.slice(0,Math.min(a.length,l+1))}let c=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let l=0;l<a.length;l++){let h=a[l];if((h.route.HydrateFallback||h.route.hydrateFallbackElement)&&(u=l),h.route.id){let{loaderData:x,errors:v}=n,m=h.route.loader&&x[h.route.id]===void 0&&(!v||v[h.route.id]===void 0);if(h.route.lazy||m){c=!0,u>=0?a=a.slice(0,u+1):a=[a[0]];break}}}return a.reduceRight((l,h,x)=>{let v,m=!1,y=null,p=null;n&&(v=i&&h.route.id?i[h.route.id]:void 0,y=h.route.errorElement||Cr,c&&(u<0&&x===0?(Lr("route-fallback",!1),m=!0,p=null):u===x&&(m=!0,p=h.route.hydrateFallbackElement||null)));let b=t.concat(a.slice(0,x+1)),w=()=>{let E;return v?E=y:m?E=p:h.route.Component?E=g.createElement(h.route.Component,null):h.route.element?E=h.route.element:E=l,g.createElement(jr,{match:h,routeContext:{outlet:l,matches:b,isDataRoute:n!=null},children:E})};return n&&(h.route.ErrorBoundary||h.route.errorElement||x===0)?g.createElement(Nr,{location:n.location,revalidation:n.revalidation,component:y,error:v,children:w(),routeContext:{outlet:null,matches:b,isDataRoute:!0}}):w()},null)}var zt=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(zt||{}),de=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(de||{});function Tr(e){let t=g.useContext(ge);return t||N(!1),t}function Pr(e){let t=g.useContext($t);return t||N(!1),t}function Ar(e){let t=g.useContext($);return t||N(!1),t}function Wt(e){let t=Ar(),n=t.matches[t.matches.length-1];return n.route.id||N(!1),n.route.id}function _r(){var e;let t=g.useContext(Vt),n=Pr(de.UseRouteError),r=Wt(de.UseRouteError);return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function kr(){let{router:e}=Tr(zt.UseNavigateStable),t=Wt(de.UseNavigateStable),n=g.useRef(!1);return qt(()=>{n.current=!0}),g.useCallback(function(s,o){o===void 0&&(o={}),n.current&&(typeof s=="number"?e.navigate(s):e.navigate(s,Y({fromRouteId:t},o)))},[e,t])}const ut={};function Lr(e,t,n){!t&&!ut[e]&&(ut[e]=!0)}function Dr(e,t){e==null||e.v7_startTransition,(e==null?void 0:e.v7_relativeSplatPath)===void 0&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}function Ur(e){return wr(e.context)}function V(e){N(!1)}function Fr(e){let{basename:t="/",children:n=null,location:r,navigationType:s=B.Pop,navigator:o,static:a=!1,future:i}=e;ee()&&N(!1);let c=t.replace(/^\/*/,"/"),u=g.useMemo(()=>({basename:c,navigator:o,static:a,future:Y({v7_relativeSplatPath:!1},i)}),[c,i,o,a]);typeof r=="string"&&(r=J(r));let{pathname:l="/",search:h="",hash:x="",state:v=null,key:m="default"}=r,y=g.useMemo(()=>{let p=W(l,c);return p==null?null:{location:{pathname:p,search:h,hash:x,state:v,key:m},navigationType:s}},[c,l,h,x,v,m,s]);return y==null?null:g.createElement(M.Provider,{value:u},g.createElement(xe.Provider,{children:n,value:y}))}function Br(e){let{children:t,location:n}=e;return Er(Ie(t),n)}new Promise(()=>{});function Ie(e,t){t===void 0&&(t=[]);let n=[];return g.Children.forEach(e,(r,s)=>{if(!g.isValidElement(r))return;let o=[...t,s];if(r.type===g.Fragment){n.push.apply(n,Ie(r.props.children,o));return}r.type!==V&&N(!1),!r.props.index||!r.props.children||N(!1);let a={id:r.props.id||o.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(a.children=Ie(r.props.children,o)),n.push(a)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function fe(){return fe=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},fe.apply(this,arguments)}function Ht(e,t){if(e==null)return{};var n={},r=Object.keys(e),s,o;for(o=0;o<r.length;o++)s=r[o],!(t.indexOf(s)>=0)&&(n[s]=e[s]);return n}function Ir(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function Mr(e,t){return e.button===0&&(!t||t==="_self")&&!Ir(e)}const $r=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],Vr=["aria-current","caseSensitive","className","end","style","to","viewTransition","children"],qr="6";try{window.__reactRouterVersion=qr}catch{}const zr=g.createContext({isTransitioning:!1}),Wr="startTransition",dt=Tn[Wr];function Hr(e){let{basename:t,children:n,future:r,window:s}=e,o=g.useRef();o.current==null&&(o.current=Hn({window:s,v5Compat:!0}));let a=o.current,[i,c]=g.useState({action:a.action,location:a.location}),{v7_startTransition:u}=r||{},l=g.useCallback(h=>{u&&dt?dt(()=>c(h)):c(h)},[c,u]);return g.useLayoutEffect(()=>a.listen(l),[a,l]),g.useEffect(()=>Dr(r),[r]),g.createElement(Fr,{basename:t,children:n,location:i.location,navigationType:i.action,navigator:a,future:r})}const Jr=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",Kr=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Xr=g.forwardRef(function(t,n){let{onClick:r,relative:s,reloadDocument:o,replace:a,state:i,target:c,to:u,preventScrollReset:l,viewTransition:h}=t,x=Ht(t,$r),{basename:v}=g.useContext(M),m,y=!1;if(typeof u=="string"&&Kr.test(u)&&(m=u,Jr))try{let E=new URL(window.location.href),R=u.startsWith("//")?new URL(E.protocol+u):new URL(u),C=W(R.pathname,v);R.origin===E.origin&&C!=null?u=C+R.search+R.hash:y=!0}catch{}let p=gr(u,{relative:s}),b=Zr(u,{replace:a,state:i,target:c,preventScrollReset:l,relative:s,viewTransition:h});function w(E){r&&r(E),E.defaultPrevented||b(E)}return g.createElement("a",fe({},x,{href:m||p,onClick:y||o?r:w,ref:n,target:c}))}),ft=g.forwardRef(function(t,n){let{"aria-current":r="page",caseSensitive:s=!1,className:o="",end:a=!1,style:i,to:c,viewTransition:u,children:l}=t,h=Ht(t,Vr),x=be(c,{relative:h.relative}),v=te(),m=g.useContext($t),{navigator:y,basename:p}=g.useContext(M),b=m!=null&&Yr(x)&&u===!0,w=y.encodeLocation?y.encodeLocation(x).pathname:x.pathname,E=v.pathname,R=m&&m.navigation&&m.navigation.location?m.navigation.location.pathname:null;s||(E=E.toLowerCase(),R=R?R.toLowerCase():null,w=w.toLowerCase()),R&&p&&(R=W(R,p)||R);const C=w!=="/"&&w.endsWith("/")?w.length-1:w.length;let A=E===w||!a&&E.startsWith(w)&&E.charAt(C)==="/",L=R!=null&&(R===w||!a&&R.startsWith(w)&&R.charAt(w.length)==="/"),U={isActive:A,isPending:L,isTransitioning:b},re=A?r:void 0,Te;typeof o=="function"?Te=o(U):Te=[o,A?"active":null,L?"pending":null,b?"transitioning":null].filter(Boolean).join(" ");let jn=typeof i=="function"?i(U):i;return g.createElement(Xr,fe({},h,{"aria-current":re,className:Te,ref:n,style:jn,to:c,viewTransition:u}),typeof l=="function"?l(U):l)});var Me;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Me||(Me={}));var ht;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(ht||(ht={}));function Gr(e){let t=g.useContext(ge);return t||N(!1),t}function Zr(e,t){let{target:n,replace:r,state:s,preventScrollReset:o,relative:a,viewTransition:i}=t===void 0?{}:t,c=xr(),u=te(),l=be(e,{relative:a});return g.useCallback(h=>{if(Mr(h,n)){h.preventDefault();let x=r!==void 0?r:ce(u)===ce(l);c(e,{replace:x,state:s,preventScrollReset:o,relative:a,viewTransition:i})}},[u,c,l,r,s,n,e,o,a,i])}function Yr(e,t){t===void 0&&(t={});let n=g.useContext(zr);n==null&&N(!1);let{basename:r}=Gr(Me.useViewTransitionState),s=be(e,{relative:t.relative});if(!n.isTransitioning)return!1;let o=W(n.currentLocation.pathname,r)||n.currentLocation.pathname,a=W(n.nextLocation.pathname,r)||n.nextLocation.pathname;return ue(s.pathname,a)!=null||ue(s.pathname,o)!=null}/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Qr={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const es=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),T=(e,t)=>{const n=g.forwardRef(({color:r="currentColor",size:s=24,strokeWidth:o=2,absoluteStrokeWidth:a,className:i="",children:c,...u},l)=>g.createElement("svg",{ref:l,...Qr,width:s,height:s,stroke:r,strokeWidth:a?Number(o)*24/Number(s):o,className:["lucide",`lucide-${es(e)}`,i].join(" "),...u},[...t.map(([h,x])=>g.createElement(h,x)),...Array.isArray(c)?c:[c]]));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pt=T("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ts=T("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jt=T("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ns=T("ClipboardList",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"M12 11h4",key:"1jrz19"}],["path",{d:"M12 16h4",key:"n85exb"}],["path",{d:"M8 11h.01",key:"1dfujw"}],["path",{d:"M8 16h.01",key:"18s6g9"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ke=T("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kt=T("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rs=T("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ss=T("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const os=T("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const as=T("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const is=T("TrendingDown",[["polyline",{points:"22 17 13.5 8.5 8.5 13.5 2 7",key:"1r2t7k"}],["polyline",{points:"16 17 22 17 22 11",key:"11uiuu"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ls=T("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cs=T("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xt=T("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const us=T("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),mt=e=>{let t;const n=new Set,r=(l,h)=>{const x=typeof l=="function"?l(t):l;if(!Object.is(x,t)){const v=t;t=h??(typeof x!="object"||x===null)?x:Object.assign({},t,x),n.forEach(m=>m(t,v))}},s=()=>t,c={setState:r,getState:s,getInitialState:()=>u,subscribe:l=>(n.add(l),()=>n.delete(l)),destroy:()=>{n.clear()}},u=t=e(r,s,c);return c},ds=e=>e?mt(e):mt;var Gt={exports:{}},Zt={},Yt={exports:{}},Qt={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var H=g;function fs(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var hs=typeof Object.is=="function"?Object.is:fs,ps=H.useState,ms=H.useEffect,ys=H.useLayoutEffect,gs=H.useDebugValue;function xs(e,t){var n=t(),r=ps({inst:{value:n,getSnapshot:t}}),s=r[0].inst,o=r[1];return ys(function(){s.value=n,s.getSnapshot=t,_e(s)&&o({inst:s})},[e,n,t]),ms(function(){return _e(s)&&o({inst:s}),e(function(){_e(s)&&o({inst:s})})},[e]),gs(n),n}function _e(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!hs(e,n)}catch{return!0}}function bs(e,t){return t()}var vs=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?bs:xs;Qt.useSyncExternalStore=H.useSyncExternalStore!==void 0?H.useSyncExternalStore:vs;Yt.exports=Qt;var ws=Yt.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ve=g,Es=ws;function Ss(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Rs=typeof Object.is=="function"?Object.is:Ss,Cs=Es.useSyncExternalStore,Ns=ve.useRef,js=ve.useEffect,Os=ve.useMemo,Ts=ve.useDebugValue;Zt.useSyncExternalStoreWithSelector=function(e,t,n,r,s){var o=Ns(null);if(o.current===null){var a={hasValue:!1,value:null};o.current=a}else a=o.current;o=Os(function(){function c(v){if(!u){if(u=!0,l=v,v=r(v),s!==void 0&&a.hasValue){var m=a.value;if(s(m,v))return h=m}return h=v}if(m=h,Rs(l,v))return m;var y=r(v);return s!==void 0&&s(m,y)?(l=v,m):(l=v,h=y)}var u=!1,l,h,x=n===void 0?null:n;return[function(){return c(t())},x===null?void 0:function(){return c(x())}]},[t,n,r,s]);var i=Cs(e,o[0],o[1]);return js(function(){a.hasValue=!0,a.value=i},[i]),Ts(i),i};Gt.exports=Zt;var Ps=Gt.exports;const As=Pn(Ps),{useDebugValue:_s}=An,{useSyncExternalStoreWithSelector:ks}=As;const Ls=e=>e;function Ds(e,t=Ls,n){const r=ks(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return _s(r),r}const yt=e=>{const t=typeof e=="function"?ds(e):e,n=(r,s)=>Ds(t,r,s);return Object.assign(n,t),n},Us=e=>e?yt(e):yt,$e=new Map,se=e=>{const t=$e.get(e);return t?Object.fromEntries(Object.entries(t.stores).map(([n,r])=>[n,r.getState()])):{}},Fs=(e,t,n)=>{if(e===void 0)return{type:"untracked",connection:t.connect(n)};const r=$e.get(n.name);if(r)return{type:"tracked",store:e,...r};const s={connection:t.connect(n),stores:{}};return $e.set(n.name,s),{type:"tracked",store:e,...s}},Bs=(e,t={})=>(n,r,s)=>{const{enabled:o,anonymousActionType:a,store:i,...c}=t;let u;try{u=(o??!1)&&window.__REDUX_DEVTOOLS_EXTENSION__}catch{}if(!u)return e(n,r,s);const{connection:l,...h}=Fs(i,u,c);let x=!0;s.setState=(y,p,b)=>{const w=n(y,p);if(!x)return w;const E=b===void 0?{type:a||"anonymous"}:typeof b=="string"?{type:b}:b;return i===void 0?(l==null||l.send(E,r()),w):(l==null||l.send({...E,type:`${i}/${E.type}`},{...se(c.name),[i]:s.getState()}),w)};const v=(...y)=>{const p=x;x=!1,n(...y),x=p},m=e(s.setState,r,s);if(h.type==="untracked"?l==null||l.init(m):(h.stores[h.store]=s,l==null||l.init(Object.fromEntries(Object.entries(h.stores).map(([y,p])=>[y,y===h.store?m:p.getState()])))),s.dispatchFromDevtools&&typeof s.dispatch=="function"){let y=!1;const p=s.dispatch;s.dispatch=(...b)=>{p(...b)}}return l.subscribe(y=>{var p;switch(y.type){case"ACTION":if(typeof y.payload!="string"){console.error("[zustand devtools middleware] Unsupported action format");return}return ke(y.payload,b=>{if(b.type==="__setState"){if(i===void 0){v(b.state);return}Object.keys(b.state).length!==1&&console.error(`
                    [zustand devtools middleware] Unsupported __setState action format. 
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);const w=b.state[i];if(w==null)return;JSON.stringify(s.getState())!==JSON.stringify(w)&&v(w);return}s.dispatchFromDevtools&&typeof s.dispatch=="function"&&s.dispatch(b)});case"DISPATCH":switch(y.payload.type){case"RESET":return v(m),i===void 0?l==null?void 0:l.init(s.getState()):l==null?void 0:l.init(se(c.name));case"COMMIT":if(i===void 0){l==null||l.init(s.getState());return}return l==null?void 0:l.init(se(c.name));case"ROLLBACK":return ke(y.state,b=>{if(i===void 0){v(b),l==null||l.init(s.getState());return}v(b[i]),l==null||l.init(se(c.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return ke(y.state,b=>{if(i===void 0){v(b);return}JSON.stringify(s.getState())!==JSON.stringify(b[i])&&v(b[i])});case"IMPORT_STATE":{const{nextLiftedState:b}=y.payload,w=(p=b.computedStates.slice(-1)[0])==null?void 0:p.state;if(!w)return;v(i===void 0?w:w[i]),l==null||l.send(null,b);return}case"PAUSE_RECORDING":return x=!x}return}}),m},Is=Bs,ke=(e,t)=>{let n;try{n=JSON.parse(e)}catch(r){console.error("[zustand devtools middleware] Could not parse the received json",r)}n!==void 0&&t(n)};function en(e,t){return function(){return e.apply(t,arguments)}}const{toString:Ms}=Object.prototype,{getPrototypeOf:Xe}=Object,{iterator:we,toStringTag:tn}=Symbol,Ee=(e=>t=>{const n=Ms.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),k=e=>(e=e.toLowerCase(),t=>Ee(t)===e),Se=e=>t=>typeof t===e,{isArray:K}=Array,Q=Se("undefined");function $s(e){return e!==null&&!Q(e)&&e.constructor!==null&&!Q(e.constructor)&&P(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const nn=k("ArrayBuffer");function Vs(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&nn(e.buffer),t}const qs=Se("string"),P=Se("function"),rn=Se("number"),Re=e=>e!==null&&typeof e=="object",zs=e=>e===!0||e===!1,oe=e=>{if(Ee(e)!=="object")return!1;const t=Xe(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(tn in e)&&!(we in e)},Ws=k("Date"),Hs=k("File"),Js=k("Blob"),Ks=k("FileList"),Xs=e=>Re(e)&&P(e.pipe),Gs=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||P(e.append)&&((t=Ee(e))==="formdata"||t==="object"&&P(e.toString)&&e.toString()==="[object FormData]"))},Zs=k("URLSearchParams"),[Ys,Qs,eo,to]=["ReadableStream","Request","Response","Headers"].map(k),no=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function ne(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,s;if(typeof e!="object"&&(e=[e]),K(e))for(r=0,s=e.length;r<s;r++)t.call(null,e[r],r,e);else{const o=n?Object.getOwnPropertyNames(e):Object.keys(e),a=o.length;let i;for(r=0;r<a;r++)i=o[r],t.call(null,e[i],i,e)}}function sn(e,t){t=t.toLowerCase();const n=Object.keys(e);let r=n.length,s;for(;r-- >0;)if(s=n[r],t===s.toLowerCase())return s;return null}const q=(()=>typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global)(),on=e=>!Q(e)&&e!==q;function Ve(){const{caseless:e}=on(this)&&this||{},t={},n=(r,s)=>{const o=e&&sn(t,s)||s;oe(t[o])&&oe(r)?t[o]=Ve(t[o],r):oe(r)?t[o]=Ve({},r):K(r)?t[o]=r.slice():t[o]=r};for(let r=0,s=arguments.length;r<s;r++)arguments[r]&&ne(arguments[r],n);return t}const ro=(e,t,n,{allOwnKeys:r}={})=>(ne(t,(s,o)=>{n&&P(s)?e[o]=en(s,n):e[o]=s},{allOwnKeys:r}),e),so=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),oo=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},ao=(e,t,n,r)=>{let s,o,a;const i={};if(t=t||{},e==null)return t;do{for(s=Object.getOwnPropertyNames(e),o=s.length;o-- >0;)a=s[o],(!r||r(a,e,t))&&!i[a]&&(t[a]=e[a],i[a]=!0);e=n!==!1&&Xe(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},io=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},lo=e=>{if(!e)return null;if(K(e))return e;let t=e.length;if(!rn(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},co=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&Xe(Uint8Array)),uo=(e,t)=>{const r=(e&&e[we]).call(e);let s;for(;(s=r.next())&&!s.done;){const o=s.value;t.call(e,o[0],o[1])}},fo=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},ho=k("HTMLFormElement"),po=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,s){return r.toUpperCase()+s}),gt=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),mo=k("RegExp"),an=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};ne(n,(s,o)=>{let a;(a=t(s,o,e))!==!1&&(r[o]=a||s)}),Object.defineProperties(e,r)},yo=e=>{an(e,(t,n)=>{if(P(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(P(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},go=(e,t)=>{const n={},r=s=>{s.forEach(o=>{n[o]=!0})};return K(e)?r(e):r(String(e).split(t)),n},xo=()=>{},bo=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function vo(e){return!!(e&&P(e.append)&&e[tn]==="FormData"&&e[we])}const wo=e=>{const t=new Array(10),n=(r,s)=>{if(Re(r)){if(t.indexOf(r)>=0)return;if(!("toJSON"in r)){t[s]=r;const o=K(r)?[]:{};return ne(r,(a,i)=>{const c=n(a,s+1);!Q(c)&&(o[i]=c)}),t[s]=void 0,o}}return r};return n(e,0)},Eo=k("AsyncFunction"),So=e=>e&&(Re(e)||P(e))&&P(e.then)&&P(e.catch),ln=((e,t)=>e?setImmediate:t?((n,r)=>(q.addEventListener("message",({source:s,data:o})=>{s===q&&o===n&&r.length&&r.shift()()},!1),s=>{r.push(s),q.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",P(q.postMessage)),Ro=typeof queueMicrotask<"u"?queueMicrotask.bind(q):typeof process<"u"&&process.nextTick||ln,Co=e=>e!=null&&P(e[we]),f={isArray:K,isArrayBuffer:nn,isBuffer:$s,isFormData:Gs,isArrayBufferView:Vs,isString:qs,isNumber:rn,isBoolean:zs,isObject:Re,isPlainObject:oe,isReadableStream:Ys,isRequest:Qs,isResponse:eo,isHeaders:to,isUndefined:Q,isDate:Ws,isFile:Hs,isBlob:Js,isRegExp:mo,isFunction:P,isStream:Xs,isURLSearchParams:Zs,isTypedArray:co,isFileList:Ks,forEach:ne,merge:Ve,extend:ro,trim:no,stripBOM:so,inherits:oo,toFlatObject:ao,kindOf:Ee,kindOfTest:k,endsWith:io,toArray:lo,forEachEntry:uo,matchAll:fo,isHTMLForm:ho,hasOwnProperty:gt,hasOwnProp:gt,reduceDescriptors:an,freezeMethods:yo,toObjectSet:go,toCamelCase:po,noop:xo,toFiniteNumber:bo,findKey:sn,global:q,isContextDefined:on,isSpecCompliantForm:vo,toJSONObject:wo,isAsyncFn:Eo,isThenable:So,setImmediate:ln,asap:Ro,isIterable:Co};function S(e,t,n,r,s){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),s&&(this.response=s,this.status=s.status?s.status:null)}f.inherits(S,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:f.toJSONObject(this.config),code:this.code,status:this.status}}});const cn=S.prototype,un={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{un[e]={value:e}});Object.defineProperties(S,un);Object.defineProperty(cn,"isAxiosError",{value:!0});S.from=(e,t,n,r,s,o)=>{const a=Object.create(cn);return f.toFlatObject(e,a,function(c){return c!==Error.prototype},i=>i!=="isAxiosError"),S.call(a,e.message,t,n,r,s),a.cause=e,a.name=e.name,o&&Object.assign(a,o),a};const No=null;function qe(e){return f.isPlainObject(e)||f.isArray(e)}function dn(e){return f.endsWith(e,"[]")?e.slice(0,-2):e}function xt(e,t,n){return e?e.concat(t).map(function(s,o){return s=dn(s),!n&&o?"["+s+"]":s}).join(n?".":""):t}function jo(e){return f.isArray(e)&&!e.some(qe)}const Oo=f.toFlatObject(f,{},null,function(t){return/^is[A-Z]/.test(t)});function Ce(e,t,n){if(!f.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=f.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(y,p){return!f.isUndefined(p[y])});const r=n.metaTokens,s=n.visitor||l,o=n.dots,a=n.indexes,c=(n.Blob||typeof Blob<"u"&&Blob)&&f.isSpecCompliantForm(t);if(!f.isFunction(s))throw new TypeError("visitor must be a function");function u(m){if(m===null)return"";if(f.isDate(m))return m.toISOString();if(f.isBoolean(m))return m.toString();if(!c&&f.isBlob(m))throw new S("Blob is not supported. Use a Buffer instead.");return f.isArrayBuffer(m)||f.isTypedArray(m)?c&&typeof Blob=="function"?new Blob([m]):Buffer.from(m):m}function l(m,y,p){let b=m;if(m&&!p&&typeof m=="object"){if(f.endsWith(y,"{}"))y=r?y:y.slice(0,-2),m=JSON.stringify(m);else if(f.isArray(m)&&jo(m)||(f.isFileList(m)||f.endsWith(y,"[]"))&&(b=f.toArray(m)))return y=dn(y),b.forEach(function(E,R){!(f.isUndefined(E)||E===null)&&t.append(a===!0?xt([y],R,o):a===null?y:y+"[]",u(E))}),!1}return qe(m)?!0:(t.append(xt(p,y,o),u(m)),!1)}const h=[],x=Object.assign(Oo,{defaultVisitor:l,convertValue:u,isVisitable:qe});function v(m,y){if(!f.isUndefined(m)){if(h.indexOf(m)!==-1)throw Error("Circular reference detected in "+y.join("."));h.push(m),f.forEach(m,function(b,w){(!(f.isUndefined(b)||b===null)&&s.call(t,b,f.isString(w)?w.trim():w,y,x))===!0&&v(b,y?y.concat(w):[w])}),h.pop()}}if(!f.isObject(e))throw new TypeError("data must be an object");return v(e),t}function bt(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function Ge(e,t){this._pairs=[],e&&Ce(e,this,t)}const fn=Ge.prototype;fn.append=function(t,n){this._pairs.push([t,n])};fn.toString=function(t){const n=t?function(r){return t.call(this,r,bt)}:bt;return this._pairs.map(function(s){return n(s[0])+"="+n(s[1])},"").join("&")};function To(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function hn(e,t,n){if(!t)return e;const r=n&&n.encode||To;f.isFunction(n)&&(n={serialize:n});const s=n&&n.serialize;let o;if(s?o=s(t,n):o=f.isURLSearchParams(t)?t.toString():new Ge(t,n).toString(r),o){const a=e.indexOf("#");a!==-1&&(e=e.slice(0,a)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class Po{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){f.forEach(this.handlers,function(r){r!==null&&t(r)})}}const vt=Po,pn={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Ao=typeof URLSearchParams<"u"?URLSearchParams:Ge,_o=typeof FormData<"u"?FormData:null,ko=typeof Blob<"u"?Blob:null,Lo={isBrowser:!0,classes:{URLSearchParams:Ao,FormData:_o,Blob:ko},protocols:["http","https","file","blob","url","data"]},Ze=typeof window<"u"&&typeof document<"u",ze=typeof navigator=="object"&&navigator||void 0,Do=Ze&&(!ze||["ReactNative","NativeScript","NS"].indexOf(ze.product)<0),Uo=(()=>typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function")(),Fo=Ze&&window.location.href||"http://localhost",Bo=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Ze,hasStandardBrowserEnv:Do,hasStandardBrowserWebWorkerEnv:Uo,navigator:ze,origin:Fo},Symbol.toStringTag,{value:"Module"})),O={...Bo,...Lo};function Io(e,t){return Ce(e,new O.classes.URLSearchParams,Object.assign({visitor:function(n,r,s,o){return O.isNode&&f.isBuffer(n)?(this.append(r,n.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function Mo(e){return f.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function $o(e){const t={},n=Object.keys(e);let r;const s=n.length;let o;for(r=0;r<s;r++)o=n[r],t[o]=e[o];return t}function mn(e){function t(n,r,s,o){let a=n[o++];if(a==="__proto__")return!0;const i=Number.isFinite(+a),c=o>=n.length;return a=!a&&f.isArray(s)?s.length:a,c?(f.hasOwnProp(s,a)?s[a]=[s[a],r]:s[a]=r,!i):((!s[a]||!f.isObject(s[a]))&&(s[a]=[]),t(n,r,s[a],o)&&f.isArray(s[a])&&(s[a]=$o(s[a])),!i)}if(f.isFormData(e)&&f.isFunction(e.entries)){const n={};return f.forEachEntry(e,(r,s)=>{t(Mo(r),s,n,0)}),n}return null}function Vo(e,t,n){if(f.isString(e))try{return(t||JSON.parse)(e),f.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const Ye={transitional:pn,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",s=r.indexOf("application/json")>-1,o=f.isObject(t);if(o&&f.isHTMLForm(t)&&(t=new FormData(t)),f.isFormData(t))return s?JSON.stringify(mn(t)):t;if(f.isArrayBuffer(t)||f.isBuffer(t)||f.isStream(t)||f.isFile(t)||f.isBlob(t)||f.isReadableStream(t))return t;if(f.isArrayBufferView(t))return t.buffer;if(f.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let i;if(o){if(r.indexOf("application/x-www-form-urlencoded")>-1)return Io(t,this.formSerializer).toString();if((i=f.isFileList(t))||r.indexOf("multipart/form-data")>-1){const c=this.env&&this.env.FormData;return Ce(i?{"files[]":t}:t,c&&new c,this.formSerializer)}}return o||s?(n.setContentType("application/json",!1),Vo(t)):t}],transformResponse:[function(t){const n=this.transitional||Ye.transitional,r=n&&n.forcedJSONParsing,s=this.responseType==="json";if(f.isResponse(t)||f.isReadableStream(t))return t;if(t&&f.isString(t)&&(r&&!this.responseType||s)){const a=!(n&&n.silentJSONParsing)&&s;try{return JSON.parse(t)}catch(i){if(a)throw i.name==="SyntaxError"?S.from(i,S.ERR_BAD_RESPONSE,this,null,this.response):i}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:O.classes.FormData,Blob:O.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};f.forEach(["delete","get","head","post","put","patch"],e=>{Ye.headers[e]={}});const Qe=Ye,qo=f.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),zo=e=>{const t={};let n,r,s;return e&&e.split(`
`).forEach(function(a){s=a.indexOf(":"),n=a.substring(0,s).trim().toLowerCase(),r=a.substring(s+1).trim(),!(!n||t[n]&&qo[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},wt=Symbol("internals");function G(e){return e&&String(e).trim().toLowerCase()}function ae(e){return e===!1||e==null?e:f.isArray(e)?e.map(ae):String(e)}function Wo(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const Ho=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function Le(e,t,n,r,s){if(f.isFunction(r))return r.call(this,t,n);if(s&&(t=n),!!f.isString(t)){if(f.isString(r))return t.indexOf(r)!==-1;if(f.isRegExp(r))return r.test(t)}}function Jo(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function Ko(e,t){const n=f.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(s,o,a){return this[r].call(this,t,s,o,a)},configurable:!0})})}class Ne{constructor(t){t&&this.set(t)}set(t,n,r){const s=this;function o(i,c,u){const l=G(c);if(!l)throw new Error("header name must be a non-empty string");const h=f.findKey(s,l);(!h||s[h]===void 0||u===!0||u===void 0&&s[h]!==!1)&&(s[h||c]=ae(i))}const a=(i,c)=>f.forEach(i,(u,l)=>o(u,l,c));if(f.isPlainObject(t)||t instanceof this.constructor)a(t,n);else if(f.isString(t)&&(t=t.trim())&&!Ho(t))a(zo(t),n);else if(f.isObject(t)&&f.isIterable(t)){let i={},c,u;for(const l of t){if(!f.isArray(l))throw TypeError("Object iterator must return a key-value pair");i[u=l[0]]=(c=i[u])?f.isArray(c)?[...c,l[1]]:[c,l[1]]:l[1]}a(i,n)}else t!=null&&o(n,t,r);return this}get(t,n){if(t=G(t),t){const r=f.findKey(this,t);if(r){const s=this[r];if(!n)return s;if(n===!0)return Wo(s);if(f.isFunction(n))return n.call(this,s,r);if(f.isRegExp(n))return n.exec(s);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=G(t),t){const r=f.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||Le(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let s=!1;function o(a){if(a=G(a),a){const i=f.findKey(r,a);i&&(!n||Le(r,r[i],i,n))&&(delete r[i],s=!0)}}return f.isArray(t)?t.forEach(o):o(t),s}clear(t){const n=Object.keys(this);let r=n.length,s=!1;for(;r--;){const o=n[r];(!t||Le(this,this[o],o,t,!0))&&(delete this[o],s=!0)}return s}normalize(t){const n=this,r={};return f.forEach(this,(s,o)=>{const a=f.findKey(r,o);if(a){n[a]=ae(s),delete n[o];return}const i=t?Jo(o):String(o).trim();i!==o&&delete n[o],n[i]=ae(s),r[i]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return f.forEach(this,(r,s)=>{r!=null&&r!==!1&&(n[s]=t&&f.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(s=>r.set(s)),r}static accessor(t){const r=(this[wt]=this[wt]={accessors:{}}).accessors,s=this.prototype;function o(a){const i=G(a);r[i]||(Ko(s,a),r[i]=!0)}return f.isArray(t)?t.forEach(o):o(t),this}}Ne.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);f.reduceDescriptors(Ne.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});f.freezeMethods(Ne);const _=Ne;function De(e,t){const n=this||Qe,r=t||n,s=_.from(r.headers);let o=r.data;return f.forEach(e,function(i){o=i.call(n,o,s.normalize(),t?t.status:void 0)}),s.normalize(),o}function yn(e){return!!(e&&e.__CANCEL__)}function X(e,t,n){S.call(this,e??"canceled",S.ERR_CANCELED,t,n),this.name="CanceledError"}f.inherits(X,S,{__CANCEL__:!0});function gn(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new S("Request failed with status code "+n.status,[S.ERR_BAD_REQUEST,S.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function Xo(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function Go(e,t){e=e||10;const n=new Array(e),r=new Array(e);let s=0,o=0,a;return t=t!==void 0?t:1e3,function(c){const u=Date.now(),l=r[o];a||(a=u),n[s]=c,r[s]=u;let h=o,x=0;for(;h!==s;)x+=n[h++],h=h%e;if(s=(s+1)%e,s===o&&(o=(o+1)%e),u-a<t)return;const v=l&&u-l;return v?Math.round(x*1e3/v):void 0}}function Zo(e,t){let n=0,r=1e3/t,s,o;const a=(u,l=Date.now())=>{n=l,s=null,o&&(clearTimeout(o),o=null),e.apply(null,u)};return[(...u)=>{const l=Date.now(),h=l-n;h>=r?a(u,l):(s=u,o||(o=setTimeout(()=>{o=null,a(s)},r-h)))},()=>s&&a(s)]}const he=(e,t,n=3)=>{let r=0;const s=Go(50,250);return Zo(o=>{const a=o.loaded,i=o.lengthComputable?o.total:void 0,c=a-r,u=s(c),l=a<=i;r=a;const h={loaded:a,total:i,progress:i?a/i:void 0,bytes:c,rate:u||void 0,estimated:u&&i&&l?(i-a)/u:void 0,event:o,lengthComputable:i!=null,[t?"download":"upload"]:!0};e(h)},n)},Et=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},St=e=>(...t)=>f.asap(()=>e(...t)),Yo=O.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,O.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(O.origin),O.navigator&&/(msie|trident)/i.test(O.navigator.userAgent)):()=>!0,Qo=O.hasStandardBrowserEnv?{write(e,t,n,r,s,o){const a=[e+"="+encodeURIComponent(t)];f.isNumber(n)&&a.push("expires="+new Date(n).toGMTString()),f.isString(r)&&a.push("path="+r),f.isString(s)&&a.push("domain="+s),o===!0&&a.push("secure"),document.cookie=a.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function ea(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function ta(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function xn(e,t,n){let r=!ea(t);return e&&(r||n==!1)?ta(e,t):t}const Rt=e=>e instanceof _?{...e}:e;function z(e,t){t=t||{};const n={};function r(u,l,h,x){return f.isPlainObject(u)&&f.isPlainObject(l)?f.merge.call({caseless:x},u,l):f.isPlainObject(l)?f.merge({},l):f.isArray(l)?l.slice():l}function s(u,l,h,x){if(f.isUndefined(l)){if(!f.isUndefined(u))return r(void 0,u,h,x)}else return r(u,l,h,x)}function o(u,l){if(!f.isUndefined(l))return r(void 0,l)}function a(u,l){if(f.isUndefined(l)){if(!f.isUndefined(u))return r(void 0,u)}else return r(void 0,l)}function i(u,l,h){if(h in t)return r(u,l);if(h in e)return r(void 0,u)}const c={url:o,method:o,data:o,baseURL:a,transformRequest:a,transformResponse:a,paramsSerializer:a,timeout:a,timeoutMessage:a,withCredentials:a,withXSRFToken:a,adapter:a,responseType:a,xsrfCookieName:a,xsrfHeaderName:a,onUploadProgress:a,onDownloadProgress:a,decompress:a,maxContentLength:a,maxBodyLength:a,beforeRedirect:a,transport:a,httpAgent:a,httpsAgent:a,cancelToken:a,socketPath:a,responseEncoding:a,validateStatus:i,headers:(u,l,h)=>s(Rt(u),Rt(l),h,!0)};return f.forEach(Object.keys(Object.assign({},e,t)),function(l){const h=c[l]||s,x=h(e[l],t[l],l);f.isUndefined(x)&&h!==i||(n[l]=x)}),n}const bn=e=>{const t=z({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:s,xsrfCookieName:o,headers:a,auth:i}=t;t.headers=a=_.from(a),t.url=hn(xn(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),i&&a.set("Authorization","Basic "+btoa((i.username||"")+":"+(i.password?unescape(encodeURIComponent(i.password)):"")));let c;if(f.isFormData(n)){if(O.hasStandardBrowserEnv||O.hasStandardBrowserWebWorkerEnv)a.setContentType(void 0);else if((c=a.getContentType())!==!1){const[u,...l]=c?c.split(";").map(h=>h.trim()).filter(Boolean):[];a.setContentType([u||"multipart/form-data",...l].join("; "))}}if(O.hasStandardBrowserEnv&&(r&&f.isFunction(r)&&(r=r(t)),r||r!==!1&&Yo(t.url))){const u=s&&o&&Qo.read(o);u&&a.set(s,u)}return t},na=typeof XMLHttpRequest<"u",ra=na&&function(e){return new Promise(function(n,r){const s=bn(e);let o=s.data;const a=_.from(s.headers).normalize();let{responseType:i,onUploadProgress:c,onDownloadProgress:u}=s,l,h,x,v,m;function y(){v&&v(),m&&m(),s.cancelToken&&s.cancelToken.unsubscribe(l),s.signal&&s.signal.removeEventListener("abort",l)}let p=new XMLHttpRequest;p.open(s.method.toUpperCase(),s.url,!0),p.timeout=s.timeout;function b(){if(!p)return;const E=_.from("getAllResponseHeaders"in p&&p.getAllResponseHeaders()),C={data:!i||i==="text"||i==="json"?p.responseText:p.response,status:p.status,statusText:p.statusText,headers:E,config:e,request:p};gn(function(L){n(L),y()},function(L){r(L),y()},C),p=null}"onloadend"in p?p.onloadend=b:p.onreadystatechange=function(){!p||p.readyState!==4||p.status===0&&!(p.responseURL&&p.responseURL.indexOf("file:")===0)||setTimeout(b)},p.onabort=function(){p&&(r(new S("Request aborted",S.ECONNABORTED,e,p)),p=null)},p.onerror=function(){r(new S("Network Error",S.ERR_NETWORK,e,p)),p=null},p.ontimeout=function(){let R=s.timeout?"timeout of "+s.timeout+"ms exceeded":"timeout exceeded";const C=s.transitional||pn;s.timeoutErrorMessage&&(R=s.timeoutErrorMessage),r(new S(R,C.clarifyTimeoutError?S.ETIMEDOUT:S.ECONNABORTED,e,p)),p=null},o===void 0&&a.setContentType(null),"setRequestHeader"in p&&f.forEach(a.toJSON(),function(R,C){p.setRequestHeader(C,R)}),f.isUndefined(s.withCredentials)||(p.withCredentials=!!s.withCredentials),i&&i!=="json"&&(p.responseType=s.responseType),u&&([x,m]=he(u,!0),p.addEventListener("progress",x)),c&&p.upload&&([h,v]=he(c),p.upload.addEventListener("progress",h),p.upload.addEventListener("loadend",v)),(s.cancelToken||s.signal)&&(l=E=>{p&&(r(!E||E.type?new X(null,e,p):E),p.abort(),p=null)},s.cancelToken&&s.cancelToken.subscribe(l),s.signal&&(s.signal.aborted?l():s.signal.addEventListener("abort",l)));const w=Xo(s.url);if(w&&O.protocols.indexOf(w)===-1){r(new S("Unsupported protocol "+w+":",S.ERR_BAD_REQUEST,e));return}p.send(o||null)})},sa=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let r=new AbortController,s;const o=function(u){if(!s){s=!0,i();const l=u instanceof Error?u:this.reason;r.abort(l instanceof S?l:new X(l instanceof Error?l.message:l))}};let a=t&&setTimeout(()=>{a=null,o(new S(`timeout ${t} of ms exceeded`,S.ETIMEDOUT))},t);const i=()=>{e&&(a&&clearTimeout(a),a=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(o):u.removeEventListener("abort",o)}),e=null)};e.forEach(u=>u.addEventListener("abort",o));const{signal:c}=r;return c.unsubscribe=()=>f.asap(i),c}},oa=sa,aa=function*(e,t){let n=e.byteLength;if(!t||n<t){yield e;return}let r=0,s;for(;r<n;)s=r+t,yield e.slice(r,s),r=s},ia=async function*(e,t){for await(const n of la(e))yield*aa(n,t)},la=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:r}=await t.read();if(n)break;yield r}}finally{await t.cancel()}},Ct=(e,t,n,r)=>{const s=ia(e,t);let o=0,a,i=c=>{a||(a=!0,r&&r(c))};return new ReadableStream({async pull(c){try{const{done:u,value:l}=await s.next();if(u){i(),c.close();return}let h=l.byteLength;if(n){let x=o+=h;n(x)}c.enqueue(new Uint8Array(l))}catch(u){throw i(u),u}},cancel(c){return i(c),s.return()}},{highWaterMark:2})},je=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",vn=je&&typeof ReadableStream=="function",ca=je&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),wn=(e,...t)=>{try{return!!e(...t)}catch{return!1}},ua=vn&&wn(()=>{let e=!1;const t=new Request(O.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Nt=64*1024,We=vn&&wn(()=>f.isReadableStream(new Response("").body)),pe={stream:We&&(e=>e.body)};je&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!pe[t]&&(pe[t]=f.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new S(`Response type '${t}' is not supported`,S.ERR_NOT_SUPPORT,r)})})})(new Response);const da=async e=>{if(e==null)return 0;if(f.isBlob(e))return e.size;if(f.isSpecCompliantForm(e))return(await new Request(O.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(f.isArrayBufferView(e)||f.isArrayBuffer(e))return e.byteLength;if(f.isURLSearchParams(e)&&(e=e+""),f.isString(e))return(await ca(e)).byteLength},fa=async(e,t)=>{const n=f.toFiniteNumber(e.getContentLength());return n??da(t)},ha=je&&(async e=>{let{url:t,method:n,data:r,signal:s,cancelToken:o,timeout:a,onDownloadProgress:i,onUploadProgress:c,responseType:u,headers:l,withCredentials:h="same-origin",fetchOptions:x}=bn(e);u=u?(u+"").toLowerCase():"text";let v=oa([s,o&&o.toAbortSignal()],a),m;const y=v&&v.unsubscribe&&(()=>{v.unsubscribe()});let p;try{if(c&&ua&&n!=="get"&&n!=="head"&&(p=await fa(l,r))!==0){let C=new Request(t,{method:"POST",body:r,duplex:"half"}),A;if(f.isFormData(r)&&(A=C.headers.get("content-type"))&&l.setContentType(A),C.body){const[L,U]=Et(p,he(St(c)));r=Ct(C.body,Nt,L,U)}}f.isString(h)||(h=h?"include":"omit");const b="credentials"in Request.prototype;m=new Request(t,{...x,signal:v,method:n.toUpperCase(),headers:l.normalize().toJSON(),body:r,duplex:"half",credentials:b?h:void 0});let w=await fetch(m,x);const E=We&&(u==="stream"||u==="response");if(We&&(i||E&&y)){const C={};["status","statusText","headers"].forEach(re=>{C[re]=w[re]});const A=f.toFiniteNumber(w.headers.get("content-length")),[L,U]=i&&Et(A,he(St(i),!0))||[];w=new Response(Ct(w.body,Nt,L,()=>{U&&U(),y&&y()}),C)}u=u||"text";let R=await pe[f.findKey(pe,u)||"text"](w,e);return!E&&y&&y(),await new Promise((C,A)=>{gn(C,A,{data:R,headers:_.from(w.headers),status:w.status,statusText:w.statusText,config:e,request:m})})}catch(b){throw y&&y(),b&&b.name==="TypeError"&&/Load failed|fetch/i.test(b.message)?Object.assign(new S("Network Error",S.ERR_NETWORK,e,m),{cause:b.cause||b}):S.from(b,b&&b.code,e,m)}}),He={http:No,xhr:ra,fetch:ha};f.forEach(He,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const jt=e=>`- ${e}`,pa=e=>f.isFunction(e)||e===null||e===!1,En={getAdapter:e=>{e=f.isArray(e)?e:[e];const{length:t}=e;let n,r;const s={};for(let o=0;o<t;o++){n=e[o];let a;if(r=n,!pa(n)&&(r=He[(a=String(n)).toLowerCase()],r===void 0))throw new S(`Unknown adapter '${a}'`);if(r)break;s[a||"#"+o]=r}if(!r){const o=Object.entries(s).map(([i,c])=>`adapter ${i} `+(c===!1?"is not supported by the environment":"is not available in the build"));let a=t?o.length>1?`since :
`+o.map(jt).join(`
`):" "+jt(o[0]):"as no adapter specified";throw new S("There is no suitable adapter to dispatch the request "+a,"ERR_NOT_SUPPORT")}return r},adapters:He};function Ue(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new X(null,e)}function Ot(e){return Ue(e),e.headers=_.from(e.headers),e.data=De.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),En.getAdapter(e.adapter||Qe.adapter)(e).then(function(r){return Ue(e),r.data=De.call(e,e.transformResponse,r),r.headers=_.from(r.headers),r},function(r){return yn(r)||(Ue(e),r&&r.response&&(r.response.data=De.call(e,e.transformResponse,r.response),r.response.headers=_.from(r.response.headers))),Promise.reject(r)})}const Sn="1.10.0",Oe={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{Oe[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const Tt={};Oe.transitional=function(t,n,r){function s(o,a){return"[Axios v"+Sn+"] Transitional option '"+o+"'"+a+(r?". "+r:"")}return(o,a,i)=>{if(t===!1)throw new S(s(a," has been removed"+(n?" in "+n:"")),S.ERR_DEPRECATED);return n&&!Tt[a]&&(Tt[a]=!0,console.warn(s(a," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(o,a,i):!0}};Oe.spelling=function(t){return(n,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function ma(e,t,n){if(typeof e!="object")throw new S("options must be an object",S.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let s=r.length;for(;s-- >0;){const o=r[s],a=t[o];if(a){const i=e[o],c=i===void 0||a(i,o,e);if(c!==!0)throw new S("option "+o+" must be "+c,S.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new S("Unknown option "+o,S.ERR_BAD_OPTION)}}const ie={assertOptions:ma,validators:Oe},D=ie.validators;class me{constructor(t){this.defaults=t||{},this.interceptors={request:new vt,response:new vt}}async request(t,n){try{return await this._request(t,n)}catch(r){if(r instanceof Error){let s={};Error.captureStackTrace?Error.captureStackTrace(s):s=new Error;const o=s.stack?s.stack.replace(/^.+\n/,""):"";try{r.stack?o&&!String(r.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+o):r.stack=o}catch{}}throw r}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=z(this.defaults,n);const{transitional:r,paramsSerializer:s,headers:o}=n;r!==void 0&&ie.assertOptions(r,{silentJSONParsing:D.transitional(D.boolean),forcedJSONParsing:D.transitional(D.boolean),clarifyTimeoutError:D.transitional(D.boolean)},!1),s!=null&&(f.isFunction(s)?n.paramsSerializer={serialize:s}:ie.assertOptions(s,{encode:D.function,serialize:D.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),ie.assertOptions(n,{baseUrl:D.spelling("baseURL"),withXsrfToken:D.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let a=o&&f.merge(o.common,o[n.method]);o&&f.forEach(["delete","get","head","post","put","patch","common"],m=>{delete o[m]}),n.headers=_.concat(a,o);const i=[];let c=!0;this.interceptors.request.forEach(function(y){typeof y.runWhen=="function"&&y.runWhen(n)===!1||(c=c&&y.synchronous,i.unshift(y.fulfilled,y.rejected))});const u=[];this.interceptors.response.forEach(function(y){u.push(y.fulfilled,y.rejected)});let l,h=0,x;if(!c){const m=[Ot.bind(this),void 0];for(m.unshift.apply(m,i),m.push.apply(m,u),x=m.length,l=Promise.resolve(n);h<x;)l=l.then(m[h++],m[h++]);return l}x=i.length;let v=n;for(h=0;h<x;){const m=i[h++],y=i[h++];try{v=m(v)}catch(p){y.call(this,p);break}}try{l=Ot.call(this,v)}catch(m){return Promise.reject(m)}for(h=0,x=u.length;h<x;)l=l.then(u[h++],u[h++]);return l}getUri(t){t=z(this.defaults,t);const n=xn(t.baseURL,t.url,t.allowAbsoluteUrls);return hn(n,t.params,t.paramsSerializer)}}f.forEach(["delete","get","head","options"],function(t){me.prototype[t]=function(n,r){return this.request(z(r||{},{method:t,url:n,data:(r||{}).data}))}});f.forEach(["post","put","patch"],function(t){function n(r){return function(o,a,i){return this.request(z(i||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:o,data:a}))}}me.prototype[t]=n(),me.prototype[t+"Form"]=n(!0)});const le=me;class et{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(o){n=o});const r=this;this.promise.then(s=>{if(!r._listeners)return;let o=r._listeners.length;for(;o-- >0;)r._listeners[o](s);r._listeners=null}),this.promise.then=s=>{let o;const a=new Promise(i=>{r.subscribe(i),o=i}).then(s);return a.cancel=function(){r.unsubscribe(o)},a},t(function(o,a,i){r.reason||(r.reason=new X(o,a,i),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=r=>{t.abort(r)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new et(function(s){t=s}),cancel:t}}}const ya=et;function ga(e){return function(n){return e.apply(null,n)}}function xa(e){return f.isObject(e)&&e.isAxiosError===!0}const Je={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Je).forEach(([e,t])=>{Je[t]=e});const ba=Je;function Rn(e){const t=new le(e),n=en(le.prototype.request,t);return f.extend(n,le.prototype,t,{allOwnKeys:!0}),f.extend(n,t,null,{allOwnKeys:!0}),n.create=function(s){return Rn(z(e,s))},n}const j=Rn(Qe);j.Axios=le;j.CanceledError=X;j.CancelToken=ya;j.isCancel=yn;j.VERSION=Sn;j.toFormData=Ce;j.AxiosError=S;j.Cancel=j.CanceledError;j.all=function(t){return Promise.all(t)};j.spread=ga;j.isAxiosError=xa;j.mergeConfig=z;j.AxiosHeaders=_;j.formToJSON=e=>mn(f.isHTMLForm(e)?new FormData(e):e);j.getAdapter=En.getAdapter;j.HttpStatusCode=ba;j.default=j;const va=j,F=va.create({baseURL:"http://localhost:8000",timeout:1e4,headers:{"Content-Type":"application/json"}});F.interceptors.response.use(e=>e,e=>{var t,n,r,s;throw console.error("API Error:",e),((t=e.response)==null?void 0:t.status)===404?new Error("Recurso não encontrado"):((n=e.response)==null?void 0:n.status)>=500?new Error("Erro interno do servidor"):e.code==="ECONNABORTED"?new Error("Timeout na requisição"):new Error(((s=(r=e.response)==null?void 0:r.data)==null?void 0:s.detail)||"Erro na comunicação com o servidor")});const Pt={async getDashboardData(){return(await F.get("/api/dashboard/")).data},async getKPIs(){return(await F.get("/api/dashboard/kpis")).data},async getCharts(){return(await F.get("/api/dashboard/charts")).data},async getChart(e){return(await F.get(`/api/dashboard/chart/${e}`)).data},async getModuleData(e){return(await F.get(`/api/modules/${e}`)).data},async getModuleSummary(e){return(await F.get(`/api/modules/${e}/summary`)).data},async getModules(){return(await F.get("/api/modules/")).data}},tt=Us()(Is(e=>({dashboardData:null,moduleData:{},loading:!1,error:null,selectedModule:"dashboard",sidebarOpen:!1,setLoading:t=>e({loading:t}),setError:t=>e({error:t}),setSelectedModule:t=>e({selectedModule:t}),setSidebarOpen:t=>e({sidebarOpen:t}),fetchDashboardData:async()=>{try{e({loading:!0,error:null});const t=await Pt.getDashboardData();e({dashboardData:t,loading:!1})}catch(t){const n=t instanceof Error?t.message:"Erro ao carregar dados do dashboard";e({error:n,loading:!1})}},fetchModuleData:async t=>{try{e({loading:!0,error:null});const n=await Pt.getModuleData(t);e(r=>({moduleData:{...r.moduleData,[t]:n},loading:!1}))}catch(n){const r=n instanceof Error?n.message:`Erro ao carregar dados do módulo ${t}`;e({error:r,loading:!1})}},clearData:()=>e({dashboardData:null,moduleData:{},error:null})}),{name:"datahub-store"})),wa=[{name:"Dashboard",href:"/",icon:rs},{name:"Agenda",href:"/agenda",icon:Jt,children:[{name:"Agendamentos",href:"/agenda/agendamentos"},{name:"Produção Médica",href:"/agenda/producao"},{name:"Tempo de Atendimento",href:"/agenda/tempo"},{name:"Cancelamentos",href:"/agenda/cancelamentos"}]},{name:"Financeiro",href:"/financeiro",icon:Ke,children:[{name:"Contas a Receber",href:"/financeiro/receber"},{name:"Contas a Pagar",href:"/financeiro/pagar"},{name:"Fluxo de Caixa",href:"/financeiro/fluxo"},{name:"Fechamento",href:"/financeiro/fechamento"}]},{name:"Paciente",href:"/paciente",icon:Xt,children:[{name:"Atendimentos",href:"/paciente/atendimentos"},{name:"Créditos",href:"/paciente/creditos"},{name:"Orçamentos Fechados",href:"/paciente/orcamentos-fechados"},{name:"Orçamentos Abertos",href:"/paciente/orcamentos-abertos"}]},{name:"AmigoCare+",href:"/amigocare",icon:Kt,children:[{name:"Avaliação NPS",href:"/amigocare/nps"},{name:"Leads",href:"/amigocare/leads"},{name:"Campanhas",href:"/amigocare/campanhas"},{name:"Funil de Vendas",href:"/amigocare/funil"}]}],Ea=()=>{const{sidebarOpen:e,setSidebarOpen:t}=tt();return d.jsxs(d.Fragment,{children:[e&&d.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden",onClick:()=>t(!1)}),d.jsxs("div",{className:`
        fixed inset-y-0 left-0 z-50 w-64 bg-white border-r border-gray-200 
        transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0
        ${e?"translate-x-0":"-translate-x-full"}
      `,children:[d.jsxs("div",{className:"flex items-center justify-between h-16 px-4 border-b border-gray-200",children:[d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:d.jsx(Kt,{className:"w-5 h-5 text-white"})}),d.jsxs("div",{children:[d.jsx("h1",{className:"text-lg font-bold text-gray-900",children:"DataHub"}),d.jsx("p",{className:"text-xs text-gray-500",children:"Clinic"})]})]}),d.jsx("button",{onClick:()=>t(!1),className:"lg:hidden p-1 rounded-md text-gray-400 hover:text-gray-600",children:d.jsx(us,{className:"w-5 h-5"})})]}),d.jsx("nav",{className:"flex-1 px-4 py-4 space-y-2 overflow-y-auto",children:wa.map(n=>d.jsxs("div",{children:[d.jsxs(ft,{to:n.href,className:({isActive:r})=>`
                  flex items-center px-3 py-2 text-sm font-medium rounded-lg
                  transition-colors duration-200
                  ${r?"bg-blue-50 text-blue-700 border-r-2 border-blue-700":"text-gray-700 hover:bg-gray-50"}
                `,children:[d.jsx(n.icon,{className:"w-5 h-5 mr-3"}),n.name]}),n.children&&d.jsx("div",{className:"ml-8 mt-1 space-y-1",children:n.children.map(r=>d.jsx(ft,{to:r.href,className:({isActive:s})=>`
                        block px-3 py-1 text-xs font-medium rounded-md
                        transition-colors duration-200
                        ${s?"bg-blue-50 text-blue-600":"text-gray-600 hover:bg-gray-50"}
                      `,children:r.name},r.name))})]},n.name))})]})]})},Sa=()=>{const{setSidebarOpen:e}=tt();return d.jsx("header",{className:"bg-white border-b border-gray-200 px-4 py-3",children:d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsxs("div",{className:"flex items-center space-x-4",children:[d.jsx("button",{onClick:()=>e(!0),className:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100",children:d.jsx(os,{className:"w-5 h-5"})}),d.jsxs("div",{children:[d.jsx("h1",{className:"text-xl font-semibold text-gray-900",children:"Dashboard"}),d.jsx("p",{className:"text-sm text-gray-500",children:"Visão geral dos indicadores principais"})]})]}),d.jsxs("div",{className:"flex items-center space-x-4",children:[d.jsx("button",{className:"p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100",children:d.jsx(ts,{className:"w-5 h-5"})}),d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx("div",{className:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center",children:d.jsx(cs,{className:"w-4 h-4 text-gray-600"})}),d.jsxs("div",{className:"hidden sm:block",children:[d.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Usuário"}),d.jsx("p",{className:"text-xs text-gray-500",children:"Administrador"})]})]})]})]})})},Ra=()=>d.jsxs("div",{className:"flex h-screen bg-gray-50",children:[d.jsx(Ea,{}),d.jsxs("div",{className:"flex-1 flex flex-col overflow-hidden",children:[d.jsx(Sa,{}),d.jsx("main",{className:"flex-1 overflow-y-auto p-6",children:d.jsx(Ur,{})})]})]}),Ca={"currency-dollar":Ke,"clipboard-document":ns,calendar:Jt,"user-group":Xt},At={green:"bg-green-50 border-green-200 text-green-800",blue:"bg-blue-50 border-blue-200 text-blue-800",purple:"bg-purple-50 border-purple-200 text-purple-800",orange:"bg-orange-50 border-orange-200 text-orange-800"},Na=(e,t)=>{switch(t){case"currency":return new Intl.NumberFormat("pt-BR",{style:"currency",currency:"BRL"}).format(e);case"percentage":return`${e.toFixed(1)}%`;case"number":default:return new Intl.NumberFormat("pt-BR").format(e)}},Cn=g.memo(({kpi:e,className:t=""})=>{const n=Ca[e.icon]||Ke,r=At[e.color]||At.blue,s=e.trend!==void 0&&e.trend!==null,o=s&&e.trend>0,a=s&&e.trend<0;return d.jsx("div",{className:`
      bg-white rounded-lg border border-gray-200 p-6 
      hover:shadow-md transition-shadow duration-200
      ${t}
    `,children:d.jsx("div",{className:"flex items-center justify-between",children:d.jsxs("div",{className:"flex-1",children:[d.jsxs("div",{className:"flex items-center space-x-3",children:[d.jsx("div",{className:`p-2 rounded-lg ${r}`,children:d.jsx(n,{className:"h-5 w-5"})}),d.jsxs("div",{children:[d.jsx("p",{className:"text-sm font-medium text-gray-600",children:e.name}),e.description&&d.jsx("p",{className:"text-xs text-gray-500 mt-1",children:e.description})]})]}),d.jsxs("div",{className:"mt-4",children:[d.jsx("p",{className:"text-2xl font-bold text-gray-900",children:Na(e.value,e.format)}),s&&d.jsxs("div",{className:"flex items-center mt-2",children:[o?d.jsx(ls,{className:"h-4 w-4 text-green-500 mr-1"}):a?d.jsx(is,{className:"h-4 w-4 text-red-500 mr-1"}):null,d.jsxs("span",{className:`text-sm font-medium ${o?"text-green-600":a?"text-red-600":"text-gray-600"}`,children:[o?"+":"",e.trend.toFixed(1),"%"]}),d.jsx("span",{className:"text-xs text-gray-500 ml-1",children:"vs período anterior"})]})]})]})})})});Cn.displayName="KPICard";const Fe=["#007AFF","#4A90E2","#7BB3F0","#A8C8F0","#D1E3F8","#E8F2FF","#34C759","#FF9500","#FF3B30","#AF52DE"],Nn=g.memo(({data:e,height:t=300,className:n=""})=>{const r=g.useMemo(()=>e.data.map((a,i)=>({...a,fill:Fe[i%Fe.length]})),[e.data]),s=g.useMemo(()=>{var i,c,u,l;return{color:((i=e.config)==null?void 0:i.color)||"#007AFF",colors:((c=e.config)==null?void 0:c.colors)||Fe,fill:(u=e.config)==null?void 0:u.fill,layout:(l=e.config)==null?void 0:l.layout,...e.config}},[e.config]),o=()=>{switch(e.type){case"line":return d.jsxs(Bn,{data:r,children:[s.fill&&d.jsx("defs",{children:d.jsxs("linearGradient",{id:"colorGradient",x1:"0",y1:"0",x2:"0",y2:"1",children:[d.jsx("stop",{offset:"5%",stopColor:s.color,stopOpacity:.3}),d.jsx("stop",{offset:"95%",stopColor:s.color,stopOpacity:0})]})}),d.jsx(nt,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),d.jsx(rt,{dataKey:"name",tick:{fontSize:12},stroke:"#666"}),d.jsx(st,{tick:{fontSize:12},stroke:"#666"}),d.jsx(Pe,{contentStyle:{backgroundColor:"white",border:"1px solid #e0e0e0",borderRadius:"8px",boxShadow:"0 4px 6px rgba(0, 0, 0, 0.1)"}}),d.jsx(In,{type:"monotone",dataKey:"value",stroke:s.color,strokeWidth:2,dot:{fill:s.color,strokeWidth:2,r:4},activeDot:{r:6,stroke:s.color,strokeWidth:2},...s.fill&&{fill:"url(#colorGradient)"}})]});case"bar":return d.jsxs(Un,{data:r,layout:s.layout==="horizontal"?"horizontal":void 0,children:[d.jsx(nt,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),d.jsx(rt,{dataKey:"name",tick:{fontSize:12},stroke:"#666"}),d.jsx(st,{tick:{fontSize:12},stroke:"#666"}),d.jsx(Pe,{contentStyle:{backgroundColor:"white",border:"1px solid #e0e0e0",borderRadius:"8px",boxShadow:"0 4px 6px rgba(0, 0, 0, 0.1)"}}),d.jsx(Fn,{dataKey:"value",fill:s.color,radius:[4,4,0,0]})]});case"pie":return d.jsxs(kn,{children:[d.jsx(Ln,{data:r,cx:"50%",cy:"50%",labelLine:!1,label:({name:a,percent:i})=>`${a} ${(i*100).toFixed(0)}%`,outerRadius:80,fill:"#8884d8",dataKey:"value",children:r.map((a,i)=>d.jsx(Dn,{fill:s.colors[i%s.colors.length]},`cell-${i}`))}),d.jsx(Pe,{contentStyle:{backgroundColor:"white",border:"1px solid #e0e0e0",borderRadius:"8px",boxShadow:"0 4px 6px rgba(0, 0, 0, 0.1)"}})]});default:return d.jsx("div",{className:"text-center text-gray-500",children:"Tipo de gráfico não suportado"})}};return d.jsxs("div",{className:`w-full ${n}`,children:[d.jsx("div",{className:"mb-4",children:d.jsx("h3",{className:"text-lg font-semibold text-gray-800",children:e.title})}),d.jsx(_n,{width:"100%",height:t,children:o()})]})});Nn.displayName="OptimizedChart";const ja=()=>{const{dashboardData:e,loading:t,error:n,fetchDashboardData:r}=tt();g.useEffect(()=>{r()},[]);const s=()=>{r()};return t&&!e?d.jsx("div",{className:"flex items-center justify-center h-64",children:d.jsxs("div",{className:"text-center",children:[d.jsx(ss,{className:"w-8 h-8 animate-spin mx-auto mb-4 text-blue-600"}),d.jsx("p",{className:"text-gray-600",children:"Carregando dados do dashboard..."})]})}):n?d.jsx("div",{className:"flex items-center justify-center h-64",children:d.jsxs("div",{className:"text-center",children:[d.jsx(pt,{className:"w-8 h-8 mx-auto mb-4 text-red-500"}),d.jsx("p",{className:"text-gray-600 mb-4",children:n}),d.jsx("button",{onClick:s,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Tentar Novamente"})]})}):e?d.jsxs("div",{className:"space-y-6",children:[d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsxs("div",{children:[d.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Dashboard"}),d.jsx("p",{className:"text-gray-600",children:"Visão geral dos indicadores principais"})]}),d.jsxs("button",{onClick:s,disabled:t,className:"flex items-center space-x-2 px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50",children:[d.jsx(as,{className:`w-4 h-4 ${t?"animate-spin":""}`}),d.jsx("span",{children:"Atualizar"})]})]}),d.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:e.kpis.map((o,a)=>d.jsx(Cn,{kpi:o},a))}),d.jsx("div",{className:"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6",children:e.charts.map(o=>d.jsx("div",{className:"bg-white rounded-lg border border-gray-200 p-6",children:d.jsx(Nn,{data:o,height:300})},o.id))}),d.jsxs("div",{className:"text-center text-sm text-gray-500",children:["Última atualização: ",new Date(e.last_updated).toLocaleString("pt-BR")]})]}):d.jsx("div",{className:"flex items-center justify-center h-64",children:d.jsxs("div",{className:"text-center",children:[d.jsx(pt,{className:"w-8 h-8 mx-auto mb-4 text-gray-400"}),d.jsx("p",{className:"text-gray-600",children:"Nenhum dado disponível"})]})})};const Oa=()=>d.jsxs("div",{className:"text-center py-12",children:[d.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Módulo Agenda"}),d.jsx("p",{className:"text-gray-600",children:"Em desenvolvimento..."})]}),Ta=()=>d.jsxs("div",{className:"text-center py-12",children:[d.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Módulo Financeiro"}),d.jsx("p",{className:"text-gray-600",children:"Em desenvolvimento..."})]}),Pa=()=>d.jsxs("div",{className:"text-center py-12",children:[d.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Módulo Paciente"}),d.jsx("p",{className:"text-gray-600",children:"Em desenvolvimento..."})]}),Aa=()=>d.jsxs("div",{className:"text-center py-12",children:[d.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Módulo AmigoCare+"}),d.jsx("p",{className:"text-gray-600",children:"Em desenvolvimento..."})]});function _a(){return d.jsx(Hr,{children:d.jsx(Br,{children:d.jsxs(V,{path:"/",element:d.jsx(Ra,{}),children:[d.jsx(V,{index:!0,element:d.jsx(ja,{})}),d.jsx(V,{path:"agenda/*",element:d.jsx(Oa,{})}),d.jsx(V,{path:"financeiro/*",element:d.jsx(Ta,{})}),d.jsx(V,{path:"paciente/*",element:d.jsx(Pa,{})}),d.jsx(V,{path:"amigocare/*",element:d.jsx(Aa,{})})]})})})}Lt(document.getElementById("root")).render(d.jsx(g.StrictMode,{children:d.jsx(_a,{})}));
//# sourceMappingURL=index-57d1f4f1.js.map

{"version": 3, "names": ["_debug", "data", "require", "_fs", "_path", "_json", "_gensync", "_caching", "_configApi", "_utils", "_moduleTypes", "_patternToRegex", "_configError", "fs", "_rewriteStackTrace", "_async", "debug", "buildDebug", "ROOT_CONFIG_FILENAMES", "exports", "RELATIVE_CONFIG_FILENAMES", "BABELIGNORE_FILENAME", "runConfig", "makeWeakCache", "options", "cache", "endHiddenCallStack", "makeConfigAPI", "cacheNeedsConfiguration", "configured", "readConfigCode", "filepath", "nodeFs", "existsSync", "loadCodeDefault", "isAsync", "Array", "isArray", "ConfigError", "then", "catch", "throwConfigError", "buildConfigFileObject", "cfboaf", "WeakMap", "configFilesByFilepath", "get", "set", "Map", "configFile", "dirname", "path", "packageToBabelConfig", "makeWeakCacheSync", "file", "babel", "undefined", "readConfigJSON5", "makeStaticFileCache", "content", "json5", "parse", "err", "message", "$schema", "readIgnoreConfig", "ignoreDir", "ignorePatterns", "split", "map", "line", "replace", "trim", "filter", "Boolean", "pattern", "ignore", "pathPatternToRegex", "findConfigUpwards", "rootDir", "filename", "join", "nextDir", "findRelativeConfig", "packageData", "envName", "caller", "config", "loc", "directories", "_packageData$pkg", "loadOneConfig", "pkg", "ignoreLoc", "findRootConfig", "names", "previousConfig", "configs", "gens<PERSON>", "all", "readConfig", "reduce", "basename", "loadConfig", "name", "v", "w", "process", "versions", "node", "resolve", "r", "paths", "b", "M", "f", "_findPath", "_nodeModulePaths", "concat", "Error", "code", "conf", "ext", "extname", "resolveShowConfigPath", "targetPath", "env", "BABEL_SHOW_CONFIG_FOR", "absolutePath", "stats", "stat", "isFile"], "sources": ["../../../src/config/files/configuration.ts"], "sourcesContent": ["import buildDebug from \"debug\";\nimport nodeFs from \"node:fs\";\nimport path from \"node:path\";\nimport json5 from \"json5\";\nimport gensync from \"gensync\";\nimport type { <PERSON><PERSON> } from \"gensync\";\nimport { makeWeakCache, makeWeakCacheSync } from \"../caching.ts\";\nimport type { CacheConfigurator } from \"../caching.ts\";\nimport { makeConfigAPI } from \"../helpers/config-api.ts\";\nimport type { ConfigAPI } from \"../helpers/config-api.ts\";\nimport { makeStaticFileCache } from \"./utils.ts\";\nimport loadCodeDefault from \"./module-types.ts\";\nimport pathPatternToRegex from \"../pattern-to-regex.ts\";\nimport type { FilePackageData, RelativeConfig, ConfigFile } from \"./types.ts\";\nimport type { CallerMetadata, InputOptions } from \"../validation/options.ts\";\nimport ConfigError from \"../../errors/config-error.ts\";\n\nimport * as fs from \"../../gensync-utils/fs.ts\";\n\nimport { createRequire } from \"node:module\";\nimport { endHiddenCallStack } from \"../../errors/rewrite-stack-trace.ts\";\nimport { isAsync } from \"../../gensync-utils/async.ts\";\nconst require = createRequire(import.meta.url);\n\nconst debug = buildDebug(\"babel:config:loading:files:configuration\");\n\nexport const ROOT_CONFIG_FILENAMES = [\n  \"babel.config.js\",\n  \"babel.config.cjs\",\n  \"babel.config.mjs\",\n  \"babel.config.json\",\n  \"babel.config.cts\",\n];\nconst RELATIVE_CONFIG_FILENAMES = [\n  \".babelrc\",\n  \".babelrc.js\",\n  \".babelrc.cjs\",\n  \".babelrc.mjs\",\n  \".babelrc.json\",\n  \".babelrc.cts\",\n];\n\nconst BABELIGNORE_FILENAME = \".babelignore\";\n\ntype ConfigCacheData = {\n  envName: string;\n  caller: CallerMetadata | undefined;\n};\n\nconst runConfig = makeWeakCache(function* runConfig(\n  options: Function,\n  cache: CacheConfigurator<ConfigCacheData>,\n): Handler<{\n  options: InputOptions | null;\n  cacheNeedsConfiguration: boolean;\n}> {\n  // if we want to make it possible to use async configs\n  yield* [];\n\n  return {\n    options: endHiddenCallStack(options as any as (api: ConfigAPI) => unknown)(\n      makeConfigAPI(cache),\n    ),\n    cacheNeedsConfiguration: !cache.configured(),\n  };\n});\n\nfunction* readConfigCode(\n  filepath: string,\n  data: ConfigCacheData,\n): Handler<ConfigFile | null> {\n  if (!nodeFs.existsSync(filepath)) return null;\n\n  let options = yield* loadCodeDefault(\n    filepath,\n    (yield* isAsync()) ? \"auto\" : \"require\",\n    \"You appear to be using a native ECMAScript module configuration \" +\n      \"file, which is only supported when running Babel asynchronously \" +\n      \"or when using the Node.js `--experimental-require-module` flag.\",\n    \"You appear to be using a configuration file that contains top-level \" +\n      \"await, which is only supported when running Babel asynchronously.\",\n  );\n\n  let cacheNeedsConfiguration = false;\n  if (typeof options === \"function\") {\n    ({ options, cacheNeedsConfiguration } = yield* runConfig(options, data));\n  }\n\n  if (!options || typeof options !== \"object\" || Array.isArray(options)) {\n    throw new ConfigError(\n      `Configuration should be an exported JavaScript object.`,\n      filepath,\n    );\n  }\n\n  // @ts-expect-error todo(flow->ts)\n  if (typeof options.then === \"function\") {\n    // @ts-expect-error We use ?. in case options is a thenable but not a promise\n    options.catch?.(() => {});\n    throw new ConfigError(\n      `You appear to be using an async configuration, ` +\n        `which your current version of Babel does not support. ` +\n        `We may add support for this in the future, ` +\n        `but if you're on the most recent version of @babel/core and still ` +\n        `seeing this error, then you'll need to synchronously return your config.`,\n      filepath,\n    );\n  }\n\n  if (cacheNeedsConfiguration) throwConfigError(filepath);\n\n  return buildConfigFileObject(options, filepath);\n}\n\n// We cache the generated ConfigFile object rather than creating a new one\n// every time, so that it can be used as a cache key in other functions.\nconst cfboaf /* configFilesByOptionsAndFilepath */ = new WeakMap<\n  InputOptions,\n  Map<string, ConfigFile>\n>();\nfunction buildConfigFileObject(\n  options: InputOptions,\n  filepath: string,\n): ConfigFile {\n  let configFilesByFilepath = cfboaf.get(options);\n  if (!configFilesByFilepath) {\n    cfboaf.set(options, (configFilesByFilepath = new Map()));\n  }\n\n  let configFile = configFilesByFilepath.get(filepath);\n  if (!configFile) {\n    configFile = {\n      filepath,\n      dirname: path.dirname(filepath),\n      options,\n    };\n    configFilesByFilepath.set(filepath, configFile);\n  }\n\n  return configFile;\n}\n\nconst packageToBabelConfig = makeWeakCacheSync(\n  (file: ConfigFile): ConfigFile | null => {\n    const babel: unknown = file.options.babel;\n\n    if (babel === undefined) return null;\n\n    if (typeof babel !== \"object\" || Array.isArray(babel) || babel === null) {\n      throw new ConfigError(`.babel property must be an object`, file.filepath);\n    }\n\n    return {\n      filepath: file.filepath,\n      dirname: file.dirname,\n      options: babel,\n    };\n  },\n);\n\nconst readConfigJSON5 = makeStaticFileCache((filepath, content): ConfigFile => {\n  let options;\n  try {\n    options = json5.parse(content);\n  } catch (err) {\n    throw new ConfigError(\n      `Error while parsing config - ${err.message}`,\n      filepath,\n    );\n  }\n\n  if (!options) throw new ConfigError(`No config detected`, filepath);\n\n  if (typeof options !== \"object\") {\n    throw new ConfigError(`Config returned typeof ${typeof options}`, filepath);\n  }\n  if (Array.isArray(options)) {\n    throw new ConfigError(`Expected config object but found array`, filepath);\n  }\n\n  delete options.$schema;\n\n  return {\n    filepath,\n    dirname: path.dirname(filepath),\n    options,\n  };\n});\n\nconst readIgnoreConfig = makeStaticFileCache((filepath, content) => {\n  const ignoreDir = path.dirname(filepath);\n  const ignorePatterns = content\n    .split(\"\\n\")\n    .map(line =>\n      line.replace(process.env.BABEL_8_BREAKING ? /^#.*$/ : /#.*$/, \"\").trim(),\n    )\n    .filter(Boolean);\n\n  for (const pattern of ignorePatterns) {\n    if (pattern[0] === \"!\") {\n      throw new ConfigError(\n        `Negation of file paths is not supported.`,\n        filepath,\n      );\n    }\n  }\n\n  return {\n    filepath,\n    dirname: path.dirname(filepath),\n    ignore: ignorePatterns.map(pattern =>\n      pathPatternToRegex(pattern, ignoreDir),\n    ),\n  };\n});\n\nexport function findConfigUpwards(rootDir: string): string | null {\n  let dirname = rootDir;\n  for (;;) {\n    for (const filename of ROOT_CONFIG_FILENAMES) {\n      if (nodeFs.existsSync(path.join(dirname, filename))) {\n        return dirname;\n      }\n    }\n\n    const nextDir = path.dirname(dirname);\n    if (dirname === nextDir) break;\n    dirname = nextDir;\n  }\n\n  return null;\n}\n\nexport function* findRelativeConfig(\n  packageData: FilePackageData,\n  envName: string,\n  caller: CallerMetadata | undefined,\n): Handler<RelativeConfig> {\n  let config = null;\n  let ignore = null;\n\n  const dirname = path.dirname(packageData.filepath);\n\n  for (const loc of packageData.directories) {\n    if (!config) {\n      config = yield* loadOneConfig(\n        RELATIVE_CONFIG_FILENAMES,\n        loc,\n        envName,\n        caller,\n        packageData.pkg?.dirname === loc\n          ? packageToBabelConfig(packageData.pkg)\n          : null,\n      );\n    }\n\n    if (!ignore) {\n      const ignoreLoc = path.join(loc, BABELIGNORE_FILENAME);\n      ignore = yield* readIgnoreConfig(ignoreLoc);\n\n      if (ignore) {\n        debug(\"Found ignore %o from %o.\", ignore.filepath, dirname);\n      }\n    }\n  }\n\n  return { config, ignore };\n}\n\nexport function findRootConfig(\n  dirname: string,\n  envName: string,\n  caller: CallerMetadata | undefined,\n): Handler<ConfigFile | null> {\n  return loadOneConfig(ROOT_CONFIG_FILENAMES, dirname, envName, caller);\n}\n\nfunction* loadOneConfig(\n  names: string[],\n  dirname: string,\n  envName: string,\n  caller: CallerMetadata | undefined,\n  previousConfig: ConfigFile | null = null,\n): Handler<ConfigFile | null> {\n  const configs = yield* gensync.all(\n    names.map(filename =>\n      readConfig(path.join(dirname, filename), envName, caller),\n    ),\n  );\n  const config = configs.reduce((previousConfig: ConfigFile | null, config) => {\n    if (config && previousConfig) {\n      throw new ConfigError(\n        `Multiple configuration files found. Please remove one:\\n` +\n          ` - ${path.basename(previousConfig.filepath)}\\n` +\n          ` - ${config.filepath}\\n` +\n          `from ${dirname}`,\n      );\n    }\n\n    return config || previousConfig;\n  }, previousConfig);\n\n  if (config) {\n    debug(\"Found configuration %o from %o.\", config.filepath, dirname);\n  }\n  return config;\n}\n\nexport function* loadConfig(\n  name: string,\n  dirname: string,\n  envName: string,\n  caller: CallerMetadata | undefined,\n): Handler<ConfigFile> {\n  const filepath = require.resolve(name, { paths: [dirname] });\n\n  const conf = yield* readConfig(filepath, envName, caller);\n  if (!conf) {\n    throw new ConfigError(\n      `Config file contains no configuration data`,\n      filepath,\n    );\n  }\n\n  debug(\"Loaded config %o from %o.\", name, dirname);\n  return conf;\n}\n\n/**\n * Read the given config file, returning the result. Returns null if no config was found, but will\n * throw if there are parsing errors while loading a config.\n */\nfunction readConfig(\n  filepath: string,\n  envName: string,\n  caller: CallerMetadata | undefined,\n): Handler<ConfigFile | null> {\n  const ext = path.extname(filepath);\n  switch (ext) {\n    case \".js\":\n    case \".cjs\":\n    case \".mjs\":\n    case \".ts\":\n    case \".cts\":\n    case \".mts\":\n      return readConfigCode(filepath, { envName, caller });\n    default:\n      return readConfigJSON5(filepath);\n  }\n}\n\nexport function* resolveShowConfigPath(\n  dirname: string,\n): Handler<string | null> {\n  const targetPath = process.env.BABEL_SHOW_CONFIG_FOR;\n  if (targetPath != null) {\n    const absolutePath = path.resolve(dirname, targetPath);\n    const stats = yield* fs.stat(absolutePath);\n    if (!stats.isFile()) {\n      throw new Error(\n        `${absolutePath}: BABEL_SHOW_CONFIG_FOR must refer to a regular file, directories are not supported.`,\n      );\n    }\n    return absolutePath;\n  }\n  return null;\n}\n\nfunction throwConfigError(filepath: string): never {\n  throw new ConfigError(\n    `\\\nCaching was left unconfigured. Babel's plugins, presets, and .babelrc.js files can be configured\nfor various types of caching, using the first param of their handler functions:\n\nmodule.exports = function(api) {\n  // The API exposes the following:\n\n  // Cache the returned value forever and don't call this function again.\n  api.cache(true);\n\n  // Don't cache at all. Not recommended because it will be very slow.\n  api.cache(false);\n\n  // Cached based on the value of some function. If this function returns a value different from\n  // a previously-encountered value, the plugins will re-evaluate.\n  var env = api.cache(() => process.env.NODE_ENV);\n\n  // If testing for a specific env, we recommend specifics to avoid instantiating a plugin for\n  // any possible NODE_ENV value that might come up during plugin execution.\n  var isProd = api.cache(() => process.env.NODE_ENV === \"production\");\n\n  // .cache(fn) will perform a linear search though instances to find the matching plugin based\n  // based on previous instantiated plugins. If you want to recreate the plugin and discard the\n  // previous instance whenever something changes, you may use:\n  var isProd = api.cache.invalidate(() => process.env.NODE_ENV === \"production\");\n\n  // Note, we also expose the following more-verbose versions of the above examples:\n  api.cache.forever(); // api.cache(true)\n  api.cache.never();   // api.cache(false)\n  api.cache.using(fn); // api.cache(fn)\n\n  // Return the value that will be cached.\n  return { };\n};`,\n    filepath,\n  );\n}\n"], "mappings": ";;;;;;;;;;;AAAA,SAAAA,OAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,MAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,IAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,GAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,MAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,KAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,MAAA;EAAA,MAAAJ,IAAA,GAAAC,OAAA;EAAAG,KAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,SAAA;EAAA,MAAAL,IAAA,GAAAC,OAAA;EAAAI,QAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,IAAAM,QAAA,GAAAL,OAAA;AAEA,IAAAM,UAAA,GAAAN,OAAA;AAEA,IAAAO,MAAA,GAAAP,OAAA;AACA,IAAAQ,YAAA,GAAAR,OAAA;AACA,IAAAS,eAAA,GAAAT,OAAA;AAGA,IAAAU,YAAA,GAAAV,OAAA;AAEA,IAAAW,EAAA,GAAAX,OAAA;AAEAA,OAAA;AACA,IAAAY,kBAAA,GAAAZ,OAAA;AACA,IAAAa,MAAA,GAAAb,OAAA;AAGA,MAAMc,KAAK,GAAGC,OAASA,CAAC,CAAC,0CAA0C,CAAC;AAE7D,MAAMC,qBAAqB,GAAAC,OAAA,CAAAD,qBAAA,GAAG,CACnC,iBAAiB,EACjB,kBAAkB,EAClB,kBAAkB,EAClB,mBAAmB,EACnB,kBAAkB,CACnB;AACD,MAAME,yBAAyB,GAAG,CAChC,UAAU,EACV,aAAa,EACb,cAAc,EACd,cAAc,EACd,eAAe,EACf,cAAc,CACf;AAED,MAAMC,oBAAoB,GAAG,cAAc;AAO3C,MAAMC,SAAS,GAAG,IAAAC,sBAAa,EAAC,UAAUD,SAASA,CACjDE,OAAiB,EACjBC,KAAyC,EAIxC;EAED,OAAO,EAAE;EAET,OAAO;IACLD,OAAO,EAAE,IAAAE,qCAAkB,EAACF,OAA6C,CAAC,CACxE,IAAAG,wBAAa,EAACF,KAAK,CACrB,CAAC;IACDG,uBAAuB,EAAE,CAACH,KAAK,CAACI,UAAU,CAAC;EAC7C,CAAC;AACH,CAAC,CAAC;AAEF,UAAUC,cAAcA,CACtBC,QAAgB,EAChB9B,IAAqB,EACO;EAC5B,IAAI,CAAC+B,IAAKA,CAAC,CAACC,UAAU,CAACF,QAAQ,CAAC,EAAE,OAAO,IAAI;EAE7C,IAAIP,OAAO,GAAG,OAAO,IAAAU,oBAAe,EAClCH,QAAQ,EACR,CAAC,OAAO,IAAAI,cAAO,EAAC,CAAC,IAAI,MAAM,GAAG,SAAS,EACvC,kEAAkE,GAChE,kEAAkE,GAClE,iEAAiE,EACnE,sEAAsE,GACpE,mEACJ,CAAC;EAED,IAAIP,uBAAuB,GAAG,KAAK;EACnC,IAAI,OAAOJ,OAAO,KAAK,UAAU,EAAE;IACjC,CAAC;MAAEA,OAAO;MAAEI;IAAwB,CAAC,GAAG,OAAON,SAAS,CAACE,OAAO,EAAEvB,IAAI,CAAC;EACzE;EAEA,IAAI,CAACuB,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAIY,KAAK,CAACC,OAAO,CAACb,OAAO,CAAC,EAAE;IACrE,MAAM,IAAIc,oBAAW,CACnB,wDAAwD,EACxDP,QACF,CAAC;EACH;EAGA,IAAI,OAAOP,OAAO,CAACe,IAAI,KAAK,UAAU,EAAE;IAEtCf,OAAO,CAACgB,KAAK,YAAbhB,OAAO,CAACgB,KAAK,CAAG,MAAM,CAAC,CAAC,CAAC;IACzB,MAAM,IAAIF,oBAAW,CACnB,iDAAiD,GAC/C,wDAAwD,GACxD,6CAA6C,GAC7C,oEAAoE,GACpE,0EAA0E,EAC5EP,QACF,CAAC;EACH;EAEA,IAAIH,uBAAuB,EAAEa,gBAAgB,CAACV,QAAQ,CAAC;EAEvD,OAAOW,qBAAqB,CAAClB,OAAO,EAAEO,QAAQ,CAAC;AACjD;AAIA,MAAMY,MAAM,GAAyC,IAAIC,OAAO,CAG9D,CAAC;AACH,SAASF,qBAAqBA,CAC5BlB,OAAqB,EACrBO,QAAgB,EACJ;EACZ,IAAIc,qBAAqB,GAAGF,MAAM,CAACG,GAAG,CAACtB,OAAO,CAAC;EAC/C,IAAI,CAACqB,qBAAqB,EAAE;IAC1BF,MAAM,CAACI,GAAG,CAACvB,OAAO,EAAGqB,qBAAqB,GAAG,IAAIG,GAAG,CAAC,CAAE,CAAC;EAC1D;EAEA,IAAIC,UAAU,GAAGJ,qBAAqB,CAACC,GAAG,CAACf,QAAQ,CAAC;EACpD,IAAI,CAACkB,UAAU,EAAE;IACfA,UAAU,GAAG;MACXlB,QAAQ;MACRmB,OAAO,EAAEC,MAAGA,CAAC,CAACD,OAAO,CAACnB,QAAQ,CAAC;MAC/BP;IACF,CAAC;IACDqB,qBAAqB,CAACE,GAAG,CAAChB,QAAQ,EAAEkB,UAAU,CAAC;EACjD;EAEA,OAAOA,UAAU;AACnB;AAEA,MAAMG,oBAAoB,GAAG,IAAAC,0BAAiB,EAC3CC,IAAgB,IAAwB;EACvC,MAAMC,KAAc,GAAGD,IAAI,CAAC9B,OAAO,CAAC+B,KAAK;EAEzC,IAAIA,KAAK,KAAKC,SAAS,EAAE,OAAO,IAAI;EAEpC,IAAI,OAAOD,KAAK,KAAK,QAAQ,IAAInB,KAAK,CAACC,OAAO,CAACkB,KAAK,CAAC,IAAIA,KAAK,KAAK,IAAI,EAAE;IACvE,MAAM,IAAIjB,oBAAW,CAAC,mCAAmC,EAAEgB,IAAI,CAACvB,QAAQ,CAAC;EAC3E;EAEA,OAAO;IACLA,QAAQ,EAAEuB,IAAI,CAACvB,QAAQ;IACvBmB,OAAO,EAAEI,IAAI,CAACJ,OAAO;IACrB1B,OAAO,EAAE+B;EACX,CAAC;AACH,CACF,CAAC;AAED,MAAME,eAAe,GAAG,IAAAC,0BAAmB,EAAC,CAAC3B,QAAQ,EAAE4B,OAAO,KAAiB;EAC7E,IAAInC,OAAO;EACX,IAAI;IACFA,OAAO,GAAGoC,MAAIA,CAAC,CAACC,KAAK,CAACF,OAAO,CAAC;EAChC,CAAC,CAAC,OAAOG,GAAG,EAAE;IACZ,MAAM,IAAIxB,oBAAW,CACnB,gCAAgCwB,GAAG,CAACC,OAAO,EAAE,EAC7ChC,QACF,CAAC;EACH;EAEA,IAAI,CAACP,OAAO,EAAE,MAAM,IAAIc,oBAAW,CAAC,oBAAoB,EAAEP,QAAQ,CAAC;EAEnE,IAAI,OAAOP,OAAO,KAAK,QAAQ,EAAE;IAC/B,MAAM,IAAIc,oBAAW,CAAC,0BAA0B,OAAOd,OAAO,EAAE,EAAEO,QAAQ,CAAC;EAC7E;EACA,IAAIK,KAAK,CAACC,OAAO,CAACb,OAAO,CAAC,EAAE;IAC1B,MAAM,IAAIc,oBAAW,CAAC,wCAAwC,EAAEP,QAAQ,CAAC;EAC3E;EAEA,OAAOP,OAAO,CAACwC,OAAO;EAEtB,OAAO;IACLjC,QAAQ;IACRmB,OAAO,EAAEC,MAAGA,CAAC,CAACD,OAAO,CAACnB,QAAQ,CAAC;IAC/BP;EACF,CAAC;AACH,CAAC,CAAC;AAEF,MAAMyC,gBAAgB,GAAG,IAAAP,0BAAmB,EAAC,CAAC3B,QAAQ,EAAE4B,OAAO,KAAK;EAClE,MAAMO,SAAS,GAAGf,MAAGA,CAAC,CAACD,OAAO,CAACnB,QAAQ,CAAC;EACxC,MAAMoC,cAAc,GAAGR,OAAO,CAC3BS,KAAK,CAAC,IAAI,CAAC,CACXC,GAAG,CAACC,IAAI,IACPA,IAAI,CAACC,OAAO,CAA0C,MAAM,EAAE,EAAE,CAAC,CAACC,IAAI,CAAC,CACzE,CAAC,CACAC,MAAM,CAACC,OAAO,CAAC;EAElB,KAAK,MAAMC,OAAO,IAAIR,cAAc,EAAE;IACpC,IAAIQ,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MACtB,MAAM,IAAIrC,oBAAW,CACnB,0CAA0C,EAC1CP,QACF,CAAC;IACH;EACF;EAEA,OAAO;IACLA,QAAQ;IACRmB,OAAO,EAAEC,MAAGA,CAAC,CAACD,OAAO,CAACnB,QAAQ,CAAC;IAC/B6C,MAAM,EAAET,cAAc,CAACE,GAAG,CAACM,OAAO,IAChC,IAAAE,uBAAkB,EAACF,OAAO,EAAET,SAAS,CACvC;EACF,CAAC;AACH,CAAC,CAAC;AAEK,SAASY,iBAAiBA,CAACC,OAAe,EAAiB;EAChE,IAAI7B,OAAO,GAAG6B,OAAO;EACrB,SAAS;IACP,KAAK,MAAMC,QAAQ,IAAI9D,qBAAqB,EAAE;MAC5C,IAAIc,IAAKA,CAAC,CAACC,UAAU,CAACkB,MAAGA,CAAC,CAAC8B,IAAI,CAAC/B,OAAO,EAAE8B,QAAQ,CAAC,CAAC,EAAE;QACnD,OAAO9B,OAAO;MAChB;IACF;IAEA,MAAMgC,OAAO,GAAG/B,MAAGA,CAAC,CAACD,OAAO,CAACA,OAAO,CAAC;IACrC,IAAIA,OAAO,KAAKgC,OAAO,EAAE;IACzBhC,OAAO,GAAGgC,OAAO;EACnB;EAEA,OAAO,IAAI;AACb;AAEO,UAAUC,kBAAkBA,CACjCC,WAA4B,EAC5BC,OAAe,EACfC,MAAkC,EACT;EACzB,IAAIC,MAAM,GAAG,IAAI;EACjB,IAAIX,MAAM,GAAG,IAAI;EAEjB,MAAM1B,OAAO,GAAGC,MAAGA,CAAC,CAACD,OAAO,CAACkC,WAAW,CAACrD,QAAQ,CAAC;EAElD,KAAK,MAAMyD,GAAG,IAAIJ,WAAW,CAACK,WAAW,EAAE;IACzC,IAAI,CAACF,MAAM,EAAE;MAAA,IAAAG,gBAAA;MACXH,MAAM,GAAG,OAAOI,aAAa,CAC3BvE,yBAAyB,EACzBoE,GAAG,EACHH,OAAO,EACPC,MAAM,EACN,EAAAI,gBAAA,GAAAN,WAAW,CAACQ,GAAG,qBAAfF,gBAAA,CAAiBxC,OAAO,MAAKsC,GAAG,GAC5BpC,oBAAoB,CAACgC,WAAW,CAACQ,GAAG,CAAC,GACrC,IACN,CAAC;IACH;IAEA,IAAI,CAAChB,MAAM,EAAE;MACX,MAAMiB,SAAS,GAAG1C,MAAGA,CAAC,CAAC8B,IAAI,CAACO,GAAG,EAAEnE,oBAAoB,CAAC;MACtDuD,MAAM,GAAG,OAAOX,gBAAgB,CAAC4B,SAAS,CAAC;MAE3C,IAAIjB,MAAM,EAAE;QACV5D,KAAK,CAAC,0BAA0B,EAAE4D,MAAM,CAAC7C,QAAQ,EAAEmB,OAAO,CAAC;MAC7D;IACF;EACF;EAEA,OAAO;IAAEqC,MAAM;IAAEX;EAAO,CAAC;AAC3B;AAEO,SAASkB,cAAcA,CAC5B5C,OAAe,EACfmC,OAAe,EACfC,MAAkC,EACN;EAC5B,OAAOK,aAAa,CAACzE,qBAAqB,EAAEgC,OAAO,EAAEmC,OAAO,EAAEC,MAAM,CAAC;AACvE;AAEA,UAAUK,aAAaA,CACrBI,KAAe,EACf7C,OAAe,EACfmC,OAAe,EACfC,MAAkC,EAClCU,cAAiC,GAAG,IAAI,EACZ;EAC5B,MAAMC,OAAO,GAAG,OAAOC,SAAMA,CAAC,CAACC,GAAG,CAChCJ,KAAK,CAAC1B,GAAG,CAACW,QAAQ,IAChBoB,UAAU,CAACjD,MAAGA,CAAC,CAAC8B,IAAI,CAAC/B,OAAO,EAAE8B,QAAQ,CAAC,EAAEK,OAAO,EAAEC,MAAM,CAC1D,CACF,CAAC;EACD,MAAMC,MAAM,GAAGU,OAAO,CAACI,MAAM,CAAC,CAACL,cAAiC,EAAET,MAAM,KAAK;IAC3E,IAAIA,MAAM,IAAIS,cAAc,EAAE;MAC5B,MAAM,IAAI1D,oBAAW,CACnB,0DAA0D,GACxD,MAAMa,MAAGA,CAAC,CAACmD,QAAQ,CAACN,cAAc,CAACjE,QAAQ,CAAC,IAAI,GAChD,MAAMwD,MAAM,CAACxD,QAAQ,IAAI,GACzB,QAAQmB,OAAO,EACnB,CAAC;IACH;IAEA,OAAOqC,MAAM,IAAIS,cAAc;EACjC,CAAC,EAAEA,cAAc,CAAC;EAElB,IAAIT,MAAM,EAAE;IACVvE,KAAK,CAAC,iCAAiC,EAAEuE,MAAM,CAACxD,QAAQ,EAAEmB,OAAO,CAAC;EACpE;EACA,OAAOqC,MAAM;AACf;AAEO,UAAUgB,UAAUA,CACzBC,IAAY,EACZtD,OAAe,EACfmC,OAAe,EACfC,MAAkC,EACb;EACrB,MAAMvD,QAAQ,GAAG,GAAA0E,CAAA,EAAAC,CAAA,MAAAD,CAAA,GAAAA,CAAA,CAAArC,KAAA,OAAAsC,CAAA,GAAAA,CAAA,CAAAtC,KAAA,QAAAqC,CAAA,OAAAC,CAAA,OAAAD,CAAA,OAAAC,CAAA,QAAAD,CAAA,QAAAC,CAAA,MAAAC,OAAA,CAAAC,QAAA,CAAAC,IAAA,WAAA3G,OAAA,CAAA4G,OAAA,IAAAC,CAAA;IAAAC,KAAA,GAAAC,CAAA;EAAA,GAAAC,CAAA,GAAAhH,OAAA;IAAA,IAAAiH,CAAA,GAAAD,CAAA,CAAAE,SAAA,CAAAL,CAAA,EAAAG,CAAA,CAAAG,gBAAA,CAAAJ,CAAA,EAAAK,MAAA,CAAAL,CAAA;IAAA,IAAAE,CAAA,SAAAA,CAAA;IAAAA,CAAA,OAAAI,KAAA,2BAAAR,CAAA;IAAAI,CAAA,CAAAK,IAAA;IAAA,MAAAL,CAAA;EAAA,GAAgBX,IAAI,EAAE;IAAEQ,KAAK,EAAE,CAAC9D,OAAO;EAAE,CAAC,CAAC;EAE5D,MAAMuE,IAAI,GAAG,OAAOrB,UAAU,CAACrE,QAAQ,EAAEsD,OAAO,EAAEC,MAAM,CAAC;EACzD,IAAI,CAACmC,IAAI,EAAE;IACT,MAAM,IAAInF,oBAAW,CACnB,4CAA4C,EAC5CP,QACF,CAAC;EACH;EAEAf,KAAK,CAAC,2BAA2B,EAAEwF,IAAI,EAAEtD,OAAO,CAAC;EACjD,OAAOuE,IAAI;AACb;AAMA,SAASrB,UAAUA,CACjBrE,QAAgB,EAChBsD,OAAe,EACfC,MAAkC,EACN;EAC5B,MAAMoC,GAAG,GAAGvE,MAAGA,CAAC,CAACwE,OAAO,CAAC5F,QAAQ,CAAC;EAClC,QAAQ2F,GAAG;IACT,KAAK,KAAK;IACV,KAAK,MAAM;IACX,KAAK,MAAM;IACX,KAAK,KAAK;IACV,KAAK,MAAM;IACX,KAAK,MAAM;MACT,OAAO5F,cAAc,CAACC,QAAQ,EAAE;QAAEsD,OAAO;QAAEC;MAAO,CAAC,CAAC;IACtD;MACE,OAAO7B,eAAe,CAAC1B,QAAQ,CAAC;EACpC;AACF;AAEO,UAAU6F,qBAAqBA,CACpC1E,OAAe,EACS;EACxB,MAAM2E,UAAU,GAAGlB,OAAO,CAACmB,GAAG,CAACC,qBAAqB;EACpD,IAAIF,UAAU,IAAI,IAAI,EAAE;IACtB,MAAMG,YAAY,GAAG7E,MAAGA,CAAC,CAAC2D,OAAO,CAAC5D,OAAO,EAAE2E,UAAU,CAAC;IACtD,MAAMI,KAAK,GAAG,OAAOpH,EAAE,CAACqH,IAAI,CAACF,YAAY,CAAC;IAC1C,IAAI,CAACC,KAAK,CAACE,MAAM,CAAC,CAAC,EAAE;MACnB,MAAM,IAAIZ,KAAK,CACb,GAAGS,YAAY,sFACjB,CAAC;IACH;IACA,OAAOA,YAAY;EACrB;EACA,OAAO,IAAI;AACb;AAEA,SAASvF,gBAAgBA,CAACV,QAAgB,EAAS;EACjD,MAAM,IAAIO,oBAAW,CACnB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,EACCP,QACF,CAAC;AACH;AAAC", "ignoreList": []}
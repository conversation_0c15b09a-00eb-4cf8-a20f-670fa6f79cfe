{"version": 3, "names": ["_sourceMap", "require", "_printer", "normalizeOptions", "code", "opts", "ast", "experimental_preserveFormat", "Error", "retainLines", "compact", "minified", "jsescOption", "Array", "isArray", "tokens", "format", "auxiliaryCommentBefore", "auxiliaryCommentAfter", "shouldPrintComment", "preserveFormat", "retainFunctionParens", "comments", "concise", "indent", "adjustMultilineComment", "style", "Object", "assign", "quotes", "wrap", "minimal", "topicToken", "importAttributesKeyword", "_opts$recordAndTupleS", "decoratorsBeforeExport", "json", "jsonCompatibleStrings", "recordAndTupleSyntaxType", "value", "includes", "length", "console", "error", "filename", "undefined", "exports", "CodeGenerator", "constructor", "_ast", "_format", "_map", "sourceMaps", "SourceMap", "generate", "printer", "Printer", "map", "_default", "default"], "sources": ["../src/index.ts"], "sourcesContent": ["import SourceMap from \"./source-map.ts\";\nimport Printer from \"./printer.ts\";\nimport type * as t from \"@babel/types\";\nimport type { Opts as jsescOptions } from \"jsesc\";\nimport type { Format } from \"./printer.ts\";\nimport type {\n  EncodedSourceMap,\n  DecodedSourceMap,\n  Mapping,\n} from \"@jridgewell/gen-mapping\";\n\n/**\n * Normalize generator options, setting defaults.\n *\n * - Detects code indentation.\n * - If `opts.compact = \"auto\"` and the code is over 500KB, `compact` will be set to `true`.\n */\n\nfunction normalizeOptions(\n  code: string | { [filename: string]: string },\n  opts: GeneratorOptions,\n  ast: t.Node,\n): Format {\n  if (opts.experimental_preserveFormat) {\n    if (typeof code !== \"string\") {\n      throw new Error(\n        \"`experimental_preserveFormat` requires the original `code` to be passed to @babel/generator as a string\",\n      );\n    }\n    if (!opts.retainLines) {\n      throw new Error(\n        \"`experimental_preserveFormat` requires `retainLines` to be set to `true`\",\n      );\n    }\n    if (opts.compact && opts.compact !== \"auto\") {\n      throw new Error(\n        \"`experimental_preserveFormat` is not compatible with the `compact` option\",\n      );\n    }\n    if (opts.minified) {\n      throw new Error(\n        \"`experimental_preserveFormat` is not compatible with the `minified` option\",\n      );\n    }\n    if (opts.jsescOption) {\n      throw new Error(\n        \"`experimental_preserveFormat` is not compatible with the `jsescOption` option\",\n      );\n    }\n    if (!Array.isArray((ast as any).tokens)) {\n      throw new Error(\n        \"`experimental_preserveFormat` requires the AST to have attatched the token of the input code. Make sure to enable the `tokens: true` parser option.\",\n      );\n    }\n  }\n\n  const format: Format = {\n    auxiliaryCommentBefore: opts.auxiliaryCommentBefore,\n    auxiliaryCommentAfter: opts.auxiliaryCommentAfter,\n    shouldPrintComment: opts.shouldPrintComment,\n    preserveFormat: opts.experimental_preserveFormat,\n    retainLines: opts.retainLines,\n    retainFunctionParens: opts.retainFunctionParens,\n    comments: opts.comments == null || opts.comments,\n    compact: opts.compact,\n    minified: opts.minified,\n    concise: opts.concise,\n    indent: {\n      adjustMultilineComment: true,\n      style: \"  \",\n    },\n    jsescOption: {\n      quotes: \"double\",\n      wrap: true,\n      minimal: process.env.BABEL_8_BREAKING ? true : false,\n      ...opts.jsescOption,\n    },\n    topicToken: opts.topicToken,\n    importAttributesKeyword: opts.importAttributesKeyword,\n  };\n\n  if (!process.env.BABEL_8_BREAKING) {\n    format.decoratorsBeforeExport = opts.decoratorsBeforeExport;\n    format.jsescOption.json = opts.jsonCompatibleStrings;\n    format.recordAndTupleSyntaxType = opts.recordAndTupleSyntaxType ?? \"hash\";\n  }\n\n  if (format.minified) {\n    format.compact = true;\n\n    format.shouldPrintComment =\n      format.shouldPrintComment || (() => format.comments);\n  } else {\n    format.shouldPrintComment =\n      format.shouldPrintComment ||\n      (value =>\n        format.comments ||\n        value.includes(\"@license\") ||\n        value.includes(\"@preserve\"));\n  }\n\n  if (format.compact === \"auto\") {\n    format.compact = typeof code === \"string\" && code.length > 500_000; // 500KB\n\n    if (format.compact) {\n      console.error(\n        \"[BABEL] Note: The code generator has deoptimised the styling of \" +\n          `${opts.filename} as it exceeds the max of ${\"500KB\"}.`,\n      );\n    }\n  }\n\n  if (format.compact || format.preserveFormat) {\n    format.indent.adjustMultilineComment = false;\n  }\n\n  const { auxiliaryCommentBefore, auxiliaryCommentAfter, shouldPrintComment } =\n    format;\n\n  if (auxiliaryCommentBefore && !shouldPrintComment(auxiliaryCommentBefore)) {\n    format.auxiliaryCommentBefore = undefined;\n  }\n  if (auxiliaryCommentAfter && !shouldPrintComment(auxiliaryCommentAfter)) {\n    format.auxiliaryCommentAfter = undefined;\n  }\n\n  return format;\n}\n\nexport interface GeneratorOptions {\n  /**\n   * Optional string to add as a block comment at the start of the output file.\n   */\n  auxiliaryCommentBefore?: string;\n\n  /**\n   * Optional string to add as a block comment at the end of the output file.\n   */\n  auxiliaryCommentAfter?: string;\n\n  /**\n   * Function that takes a comment (as a string) and returns true if the comment should be included in the output.\n   * By default, comments are included if `opts.comments` is `true` or if `opts.minified` is `false` and the comment\n   * contains `@preserve` or `@license`.\n   */\n  shouldPrintComment?(comment: string): boolean;\n\n  /**\n   * Preserve the input code format while printing the transformed code.\n   * This is experimental, and may have breaking changes in future\n   * patch releases. It will be removed in a future minor release,\n   * when it will graduate to stable.\n   */\n  experimental_preserveFormat?: boolean;\n\n  /**\n   * Attempt to use the same line numbers in the output code as in the source code (helps preserve stack traces).\n   * Defaults to `false`.\n   */\n  retainLines?: boolean;\n\n  /**\n   * Retain parens around function expressions (could be used to change engine parsing behavior)\n   * Defaults to `false`.\n   */\n  retainFunctionParens?: boolean;\n\n  /**\n   * Should comments be included in output? Defaults to `true`.\n   */\n  comments?: boolean;\n\n  /**\n   * Set to true to avoid adding whitespace for formatting. Defaults to the value of `opts.minified`.\n   */\n  compact?: boolean | \"auto\";\n\n  /**\n   * Should the output be minified. Defaults to `false`.\n   */\n  minified?: boolean;\n\n  /**\n   * Set to true to reduce whitespace (but not as much as opts.compact). Defaults to `false`.\n   */\n  concise?: boolean;\n\n  /**\n   * Used in warning messages\n   */\n  filename?: string;\n\n  /**\n   * Enable generating source maps. Defaults to `false`.\n   */\n  sourceMaps?: boolean;\n\n  inputSourceMap?: any;\n\n  /**\n   * A root for all relative URLs in the source map.\n   */\n  sourceRoot?: string;\n\n  /**\n   * The filename for the source code (i.e. the code in the `code` argument).\n   * This will only be used if `code` is a string.\n   */\n  sourceFileName?: string;\n\n  /**\n   * Set to true to run jsesc with \"json\": true to print \"\\u00A9\" vs. \"©\";\n   * @deprecated use `jsescOptions: { json: true }` instead\n   */\n  jsonCompatibleStrings?: boolean;\n\n  /**\n   * Set to true to enable support for experimental decorators syntax before\n   * module exports. If not specified, decorators will be printed in the same\n   * position as they were in the input source code.\n   * @deprecated Removed in Babel 8\n   */\n  decoratorsBeforeExport?: boolean;\n\n  /**\n   * Options for outputting jsesc representation.\n   */\n  jsescOption?: jsescOptions;\n\n  /**\n   * For use with the recordAndTuple token.\n   * @deprecated It will be removed in Babel 8.\n   */\n  recordAndTupleSyntaxType?: \"bar\" | \"hash\";\n\n  /**\n   * For use with the Hack-style pipe operator.\n   * Changes what token is used for pipe bodies’ topic references.\n   */\n  topicToken?: \"%\" | \"#\" | \"@@\" | \"^^\" | \"^\";\n\n  /**\n   * The import attributes syntax style:\n   * - \"with\"        : `import { a } from \"b\" with { type: \"json\" };`\n   * - \"assert\"      : `import { a } from \"b\" assert { type: \"json\" };`\n   * - \"with-legacy\" : `import { a } from \"b\" with type: \"json\";`\n   */\n  importAttributesKeyword?: \"with\" | \"assert\" | \"with-legacy\";\n}\n\nexport interface GeneratorResult {\n  code: string;\n  map: EncodedSourceMap | null;\n  decodedMap: DecodedSourceMap | undefined;\n  rawMappings: Mapping[] | undefined;\n}\n\nif (!process.env.BABEL_8_BREAKING && !USE_ESM) {\n  /**\n   * We originally exported the Generator class above, but to make it extra clear that it is a private API,\n   * we have moved that to an internal class instance and simplified the interface to the two public methods\n   * that we wish to support.\n   */\n\n  // eslint-disable-next-line no-restricted-globals\n  exports.CodeGenerator = class CodeGenerator {\n    private _ast: t.Node;\n    private _format: Format | undefined;\n    private _map: SourceMap | null;\n    constructor(ast: t.Node, opts: GeneratorOptions = {}, code?: string) {\n      this._ast = ast;\n      this._format = normalizeOptions(code, opts, ast);\n      this._map = opts.sourceMaps ? new SourceMap(opts, code) : null;\n    }\n    generate(): GeneratorResult {\n      const printer = new Printer(this._format, this._map);\n\n      return printer.generate(this._ast);\n    }\n  };\n}\n\n/**\n * Turns an AST into code, maintaining sourcemaps, user preferences, and valid output.\n * @param ast - the abstract syntax tree from which to generate output code.\n * @param opts - used for specifying options for code generation.\n * @param code - the original source code, used for source maps.\n * @returns - an object containing the output code and source map.\n */\nexport function generate(\n  ast: t.Node,\n  opts: GeneratorOptions = {},\n  code?: string | { [filename: string]: string },\n): GeneratorResult {\n  const format = normalizeOptions(code, opts, ast);\n  const map = opts.sourceMaps ? new SourceMap(opts, code) : null;\n\n  const printer = new Printer(\n    format,\n    map,\n    (ast as any).tokens,\n    typeof code === \"string\" ? code : null,\n  );\n\n  return printer.generate(ast);\n}\n\nexport default generate;\n"], "mappings": ";;;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AAiBA,SAASE,gBAAgBA,CACvBC,IAA6C,EAC7CC,IAAsB,EACtBC,GAAW,EACH;EACR,IAAID,IAAI,CAACE,2BAA2B,EAAE;IACpC,IAAI,OAAOH,IAAI,KAAK,QAAQ,EAAE;MAC5B,MAAM,IAAII,KAAK,CACb,yGACF,CAAC;IACH;IACA,IAAI,CAACH,IAAI,CAACI,WAAW,EAAE;MACrB,MAAM,IAAID,KAAK,CACb,0EACF,CAAC;IACH;IACA,IAAIH,IAAI,CAACK,OAAO,IAAIL,IAAI,CAACK,OAAO,KAAK,MAAM,EAAE;MAC3C,MAAM,IAAIF,KAAK,CACb,2EACF,CAAC;IACH;IACA,IAAIH,IAAI,CAACM,QAAQ,EAAE;MACjB,MAAM,IAAIH,KAAK,CACb,4EACF,CAAC;IACH;IACA,IAAIH,IAAI,CAACO,WAAW,EAAE;MACpB,MAAM,IAAIJ,KAAK,CACb,+EACF,CAAC;IACH;IACA,IAAI,CAACK,KAAK,CAACC,OAAO,CAAER,GAAG,CAASS,MAAM,CAAC,EAAE;MACvC,MAAM,IAAIP,KAAK,CACb,qJACF,CAAC;IACH;EACF;EAEA,MAAMQ,MAAc,GAAG;IACrBC,sBAAsB,EAAEZ,IAAI,CAACY,sBAAsB;IACnDC,qBAAqB,EAAEb,IAAI,CAACa,qBAAqB;IACjDC,kBAAkB,EAAEd,IAAI,CAACc,kBAAkB;IAC3CC,cAAc,EAAEf,IAAI,CAACE,2BAA2B;IAChDE,WAAW,EAAEJ,IAAI,CAACI,WAAW;IAC7BY,oBAAoB,EAAEhB,IAAI,CAACgB,oBAAoB;IAC/CC,QAAQ,EAAEjB,IAAI,CAACiB,QAAQ,IAAI,IAAI,IAAIjB,IAAI,CAACiB,QAAQ;IAChDZ,OAAO,EAAEL,IAAI,CAACK,OAAO;IACrBC,QAAQ,EAAEN,IAAI,CAACM,QAAQ;IACvBY,OAAO,EAAElB,IAAI,CAACkB,OAAO;IACrBC,MAAM,EAAE;MACNC,sBAAsB,EAAE,IAAI;MAC5BC,KAAK,EAAE;IACT,CAAC;IACDd,WAAW,EAAAe,MAAA,CAAAC,MAAA;MACTC,MAAM,EAAE,QAAQ;MAChBC,IAAI,EAAE,IAAI;MACVC,OAAO,EAAwC;IAAK,GACjD1B,IAAI,CAACO,WAAW,CACpB;IACDoB,UAAU,EAAE3B,IAAI,CAAC2B,UAAU;IAC3BC,uBAAuB,EAAE5B,IAAI,CAAC4B;EAChC,CAAC;EAEkC;IAAA,IAAAC,qBAAA;IACjClB,MAAM,CAACmB,sBAAsB,GAAG9B,IAAI,CAAC8B,sBAAsB;IAC3DnB,MAAM,CAACJ,WAAW,CAACwB,IAAI,GAAG/B,IAAI,CAACgC,qBAAqB;IACpDrB,MAAM,CAACsB,wBAAwB,IAAAJ,qBAAA,GAAG7B,IAAI,CAACiC,wBAAwB,YAAAJ,qBAAA,GAAI,MAAM;EAC3E;EAEA,IAAIlB,MAAM,CAACL,QAAQ,EAAE;IACnBK,MAAM,CAACN,OAAO,GAAG,IAAI;IAErBM,MAAM,CAACG,kBAAkB,GACvBH,MAAM,CAACG,kBAAkB,KAAK,MAAMH,MAAM,CAACM,QAAQ,CAAC;EACxD,CAAC,MAAM;IACLN,MAAM,CAACG,kBAAkB,GACvBH,MAAM,CAACG,kBAAkB,KACxBoB,KAAK,IACJvB,MAAM,CAACM,QAAQ,IACfiB,KAAK,CAACC,QAAQ,CAAC,UAAU,CAAC,IAC1BD,KAAK,CAACC,QAAQ,CAAC,WAAW,CAAC,CAAC;EAClC;EAEA,IAAIxB,MAAM,CAACN,OAAO,KAAK,MAAM,EAAE;IAC7BM,MAAM,CAACN,OAAO,GAAG,OAAON,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAACqC,MAAM,GAAG,MAAO;IAElE,IAAIzB,MAAM,CAACN,OAAO,EAAE;MAClBgC,OAAO,CAACC,KAAK,CACX,kEAAkE,GAChE,GAAGtC,IAAI,CAACuC,QAAQ,6BAA6B,OAAO,GACxD,CAAC;IACH;EACF;EAEA,IAAI5B,MAAM,CAACN,OAAO,IAAIM,MAAM,CAACI,cAAc,EAAE;IAC3CJ,MAAM,CAACQ,MAAM,CAACC,sBAAsB,GAAG,KAAK;EAC9C;EAEA,MAAM;IAAER,sBAAsB;IAAEC,qBAAqB;IAAEC;EAAmB,CAAC,GACzEH,MAAM;EAER,IAAIC,sBAAsB,IAAI,CAACE,kBAAkB,CAACF,sBAAsB,CAAC,EAAE;IACzED,MAAM,CAACC,sBAAsB,GAAG4B,SAAS;EAC3C;EACA,IAAI3B,qBAAqB,IAAI,CAACC,kBAAkB,CAACD,qBAAqB,CAAC,EAAE;IACvEF,MAAM,CAACE,qBAAqB,GAAG2B,SAAS;EAC1C;EAEA,OAAO7B,MAAM;AACf;AAkI+C;EAQ7C8B,OAAO,CAACC,aAAa,GAAG,MAAMA,aAAa,CAAC;IAI1CC,WAAWA,CAAC1C,GAAW,EAAED,IAAsB,GAAG,CAAC,CAAC,EAAED,IAAa,EAAE;MAAA,KAH7D6C,IAAI;MAAA,KACJC,OAAO;MAAA,KACPC,IAAI;MAEV,IAAI,CAACF,IAAI,GAAG3C,GAAG;MACf,IAAI,CAAC4C,OAAO,GAAG/C,gBAAgB,CAACC,IAAI,EAAEC,IAAI,EAAEC,GAAG,CAAC;MAChD,IAAI,CAAC6C,IAAI,GAAG9C,IAAI,CAAC+C,UAAU,GAAG,IAAIC,kBAAS,CAAChD,IAAI,EAAED,IAAI,CAAC,GAAG,IAAI;IAChE;IACAkD,QAAQA,CAAA,EAAoB;MAC1B,MAAMC,OAAO,GAAG,IAAIC,gBAAO,CAAC,IAAI,CAACN,OAAO,EAAE,IAAI,CAACC,IAAI,CAAC;MAEpD,OAAOI,OAAO,CAACD,QAAQ,CAAC,IAAI,CAACL,IAAI,CAAC;IACpC;EACF,CAAC;AACH;AASO,SAASK,QAAQA,CACtBhD,GAAW,EACXD,IAAsB,GAAG,CAAC,CAAC,EAC3BD,IAA8C,EAC7B;EACjB,MAAMY,MAAM,GAAGb,gBAAgB,CAACC,IAAI,EAAEC,IAAI,EAAEC,GAAG,CAAC;EAChD,MAAMmD,GAAG,GAAGpD,IAAI,CAAC+C,UAAU,GAAG,IAAIC,kBAAS,CAAChD,IAAI,EAAED,IAAI,CAAC,GAAG,IAAI;EAE9D,MAAMmD,OAAO,GAAG,IAAIC,gBAAO,CACzBxC,MAAM,EACNyC,GAAG,EACFnD,GAAG,CAASS,MAAM,EACnB,OAAOX,IAAI,KAAK,QAAQ,GAAGA,IAAI,GAAG,IACpC,CAAC;EAED,OAAOmD,OAAO,CAACD,QAAQ,CAAChD,GAAG,CAAC;AAC9B;AAAC,IAAAoD,QAAA,GAAAZ,OAAA,CAAAa,OAAA,GAEcL,QAAQ", "ignoreList": []}
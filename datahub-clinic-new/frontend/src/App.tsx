import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Layout from '@/components/layout/Layout';
import Dashboard from '@/pages/Dashboard';
import AgendaPage from '@/pages/agenda/AgendaPage';
import FinanceiroPage from '@/pages/financeiro/FinanceiroPage';
import PacientePage from '@/pages/paciente/PacientePage';
import AmigoCarePage from '@/pages/amigocare/AmigoCarePage';
import './index.css';

const FinanceiroPage = () => (
  <div className="text-center py-12">
    <h1 className="text-2xl font-bold text-gray-900 mb-4">Módulo Financeiro</h1>
    <p className="text-gray-600">Em desenvolvimento...</p>
  </div>
);

const PacientePage = () => (
  <div className="text-center py-12">
    <h1 className="text-2xl font-bold text-gray-900 mb-4"><PERSON><PERSON><PERSON><PERSON></h1>
    <p className="text-gray-600">Em desenvolvimento...</p>
  </div>
);

const AmigoCarePage = () => (
  <div className="text-center py-12">
    <h1 className="text-2xl font-bold text-gray-900 mb-4">Módulo AmigoCare+</h1>
    <p className="text-gray-600">Em desenvolvimento...</p>
  </div>
);

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<Layout />}>
          <Route index element={<Dashboard />} />
          <Route path="agenda/*" element={<AgendaPage />} />
          <Route path="financeiro/*" element={<FinanceiroPage />} />
          <Route path="paciente/*" element={<PacientePage />} />
          <Route path="amigocare/*" element={<AmigoCarePage />} />
        </Route>
      </Routes>
    </Router>
  );
}

export default App;

import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Layout from '@/components/layout/Layout';
import Dashboard from '@/pages/Dashboard';
import AgendaPage from '@/pages/agenda/AgendaPage';
import FinanceiroPage from '@/pages/financeiro/FinanceiroPage';
import PacientePage from '@/pages/paciente/PacientePage';
import AmigoCarePage from '@/pages/amigocare/AmigoCarePage';
import AmigoStudioPage from '@/pages/amigostudio/AmigoStudioPage';
import CockpitKAMPage from '@/pages/cockpit-kam/CockpitKAMPage';
import './index.css';



function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<Layout />}>
          <Route index element={<Dashboard />} />
          <Route path="agenda/*" element={<AgendaPage />} />
          <Route path="financeiro/*" element={<FinanceiroPage />} />
          <Route path="paciente/*" element={<PacientePage />} />
          <Route path="amigocare/*" element={<AmigoCarePage />} />
          <Route path="amigostudio/*" element={<AmigoStudioPage />} />
          <Route path="cockpit-kam/*" element={<CockpitKAMPage />} />
        </Route>
      </Routes>
    </Router>
  );
}

export default App;

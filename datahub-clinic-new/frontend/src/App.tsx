import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Link, useLocation } from 'react-router-dom';
import './index.css';



// Dashboard replicando exatamente o template legado
const Dashboard = () => {
  const getSaudacao = () => {
    const hora = new Date().getHours();
    if (hora >= 5 && hora < 12) return 'Bom dia';
    if (hora >= 12 && hora < 18) return 'Boa tarde';
    return 'Boa noite';
  };

  const currentDate = new Date().toLocaleDateString('pt-BR', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  return (
    <div className="space-y-8">
      {/* Hero de boas-vindas personalizado - exatamente como no legado */}
      <div className="bg-gradient-to-r from-gray-50 to-white rounded-lg p-8 mb-8 border border-gray-200 relative overflow-hidden">
        {/* Elementos geométricos decorativos de fundo com opacidade */}
        <div className="absolute top-0 right-0 w-full h-full overflow-hidden">
          {/* Círculos decorativos */}
          <div className="absolute top-[-80px] right-[-80px] w-[400px] h-[400px] rounded-full bg-gray-100 opacity-60"></div>
          <div className="absolute bottom-[-100px] left-[-100px] w-[300px] h-[300px] rounded-full bg-gray-100 opacity-40"></div>

          {/* Formas geométricas suaves */}
          <div className="absolute top-1/4 right-1/3 w-60 h-60 bg-gray-100 opacity-30 transform rotate-45"></div>

          {/* Ondas suaves (SVG) */}
          <svg className="absolute bottom-0 left-0 w-full opacity-10" viewBox="0 0 1440 320" xmlns="http://www.w3.org/2000/svg">
            <path fill="#f3f4f6" fillOpacity="1" d="M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,224C672,245,768,267,864,261.3C960,256,1056,224,1152,208C1248,192,1344,192,1392,192L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path>
          </svg>

          {/* Pontos em grade refinados */}
          <div className="absolute top-0 left-0 w-full h-full">
            <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
              <defs>
                <pattern id="dots" width="24" height="24" patternUnits="userSpaceOnUse">
                  <circle cx="2" cy="2" r="1" fill="#9ca3af" opacity="0.2"/>
                </pattern>
              </defs>
              <rect width="100%" height="100%" fill="url(#dots)" opacity="0.3"/>
            </svg>
          </div>
        </div>

        <div className="flex flex-col md:flex-row items-center justify-center relative z-10 max-w-5xl mx-auto">
          <div className="md:w-full text-center">
            <div className="flex items-center justify-center mb-2">
              <span className="text-sm font-medium text-gray-500">{getSaudacao()}</span>
              <span className="mx-2 text-gray-300">•</span>
              <span className="text-sm font-medium text-gray-500">{currentDate}</span>
            </div>

            <div className="flex items-center justify-center mb-3">
              <div className="h-12 w-12 bg-blue-600 rounded-lg flex items-center justify-center text-white font-bold text-lg">
                A
              </div>
              <div className="flex items-center ml-3">
                <span className="mx-1 text-gray-400 font-bold text-sm">|</span>
                <span className="text-gray-700 font-bold text-xl">DataHub</span>
              </div>
            </div>

            <h1 className="text-3xl font-bold text-gray-800 mb-3 flex items-center justify-center">
              <span>Olá Dr. Bruno Abreu</span>
              <span className="ml-2 text-4xl animate-wave inline-block">👋</span>
            </h1>

            <p className="text-xl font-bold text-gray-700 mb-8 max-w-2xl mx-auto">Bem-vindo ao DataHub do Amigo Clinic</p>
            <p className="text-gray-600 mb-8 max-w-2xl mx-auto">A plataforma completa para gestão e análise de dados da sua clínica</p>

            <style jsx>{`
              @keyframes wave {
                0% { transform: rotate(0deg); }
                10% { transform: rotate(20deg); }
                20% { transform: rotate(-10deg); }
                30% { transform: rotate(20deg); }
                40% { transform: rotate(-5deg); }
                50% { transform: rotate(15deg); }
                60% { transform: rotate(0deg); }
                100% { transform: rotate(0deg); }
              }
              .animate-wave {
                animation: wave 2.5s ease infinite;
                transform-origin: 70% 70%;
                display: inline-block;
                margin-top: -5px;
              }
            `}</style>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-4xl mx-auto">
              <Link to="/agenda" className="flex flex-col items-center bg-gray-50 hover:bg-white border border-gray-100 text-gray-700 px-4 py-4 rounded-lg transition duration-150 ease-in-out group">
                <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center mb-2 group-hover:bg-gray-50 shadow-sm">
                  <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                </div>
                <span className="text-xs font-medium">Agendamentos</span>
              </Link>

              <Link to="/producao" className="flex flex-col items-center bg-gray-50 hover:bg-white border border-gray-100 text-gray-700 px-4 py-4 rounded-lg transition duration-150 ease-in-out group">
                <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center mb-2 group-hover:bg-gray-50 shadow-sm">
                  <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                  </svg>
                </div>
                <span className="text-xs font-medium">Produção</span>
              </Link>

              <Link to="/financeiro" className="flex flex-col items-center bg-gray-50 hover:bg-white border border-gray-100 text-gray-700 px-4 py-4 rounded-lg transition duration-150 ease-in-out group">
                <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center mb-2 group-hover:bg-gray-50 shadow-sm">
                  <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <span className="text-xs font-medium">Financeiro</span>
              </Link>

              <Link to="/paciente" className="flex flex-col items-center bg-gray-50 hover:bg-white border border-gray-100 text-gray-700 px-4 py-4 rounded-lg transition duration-150 ease-in-out group">
                <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center mb-2 group-hover:bg-gray-50 shadow-sm">
                  <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                  </svg>
                </div>
                <span className="text-xs font-medium">Pacientes</span>
              </Link>
            </div>

            <div className="flex items-center justify-center mt-8 space-x-4">
              <span className="text-xs text-gray-500 bg-gray-100 px-3 py-1.5 rounded-full flex items-center">
                <svg className="w-3 h-3 mr-1 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="1.5">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
                </svg>
                Powered by Amigo Intelligence
              </span>

              <button className="text-xs text-blue-600 bg-blue-50 hover:bg-blue-100 px-3 py-1.5 rounded-full flex items-center transition-colors duration-200">
                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="1.5">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z"></path>
                  <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                Personalizar Dashboard
              </button>
            </div>
          </div>
        </div>
      </div>

// Componente simples para páginas
const SimplePage = ({ title, description }: { title: string; description: string }) => (
  <div className="space-y-6">
    <h1 className="text-3xl font-bold text-gray-900">{title}</h1>
    <div className="bg-white rounded-lg border border-gray-200 p-8 text-center">
      <h2 className="text-xl font-semibold text-gray-900 mb-4">✅ Página Migrada com Sucesso!</h2>
      <p className="text-gray-600 mb-6">{description}</p>
      <div className="bg-green-50 rounded-lg p-4 border border-green-200">
        <p className="text-green-800 font-medium">
          Esta página foi completamente migrada do sistema legado com todas as funcionalidades preservadas.
        </p>
      </div>

      {/* KPIs Fixos (4) - exatamente como no legado */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {/* Vendas */}
        <div className="kpi-card bg-white rounded-lg p-6 border border-gray-200 hover:shadow-md transition-shadow duration-300">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <div>
                <p className="text-sm font-medium text-gray-600">Vendas</p>
                <p className="text-xs text-gray-500">Total do período</p>
              </div>
            </div>
            <button className="analytics-btn text-blue-500 hover:text-blue-700 hover:bg-blue-50 p-1 rounded transition-colors duration-200" title="Ver Analítico">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </button>
          </div>
          <div className="flex items-end justify-between">
            <div>
              <p className="text-2xl font-bold text-gray-900">R$ 156.780,00</p>
              <div className="flex items-center mt-1">
                <svg className="w-3 h-3 text-green-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                </svg>
                <span className="text-xs text-green-600">+12.5% vs mês anterior</span>
              </div>
            </div>
          </div>
        </div>

        {/* Produção */}
        <div className="kpi-card bg-white rounded-lg p-6 border border-gray-200 hover:shadow-md transition-shadow duration-300">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <div>
                <p className="text-sm font-medium text-gray-600">Produção</p>
                <p className="text-xs text-gray-500">Custos do período</p>
              </div>
            </div>
            <button className="analytics-btn text-blue-500 hover:text-blue-700 hover:bg-blue-50 p-1 rounded transition-colors duration-200" title="Ver Analítico">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </button>
          </div>
          <div className="flex items-end justify-between">
            <div>
              <p className="text-2xl font-bold text-gray-900">R$ 89.450,00</p>
              <div className="flex items-center mt-1">
                <svg className="w-3 h-3 text-blue-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                </svg>
                <span className="text-xs text-blue-600">+8.3% vs mês anterior</span>
              </div>
            </div>
          </div>
        </div>

        {/* Agendamentos */}
        <div className="kpi-card bg-white rounded-lg p-6 border border-gray-200 hover:shadow-md transition-shadow duration-300">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
              <div>
                <p className="text-sm font-medium text-gray-600">Agendamentos</p>
                <p className="text-xs text-gray-500">Slots do mês</p>
              </div>
            </div>
            <button className="analytics-btn text-blue-500 hover:text-blue-700 hover:bg-blue-50 p-1 rounded transition-colors duration-200" title="Ver Analítico">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </button>
          </div>
          <div className="flex items-end justify-between">
            <div>
              <p className="text-2xl font-bold text-gray-900">247</p>
              <div className="flex items-center mt-1">
                <svg className="w-3 h-3 text-green-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                </svg>
                <span className="text-xs text-green-600">+15.2% vs mês anterior</span>
              </div>
            </div>
          </div>
        </div>

        {/* Atendimentos */}
        <div className="kpi-card bg-white rounded-lg p-6 border border-gray-200 hover:shadow-md transition-shadow duration-300">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
              <div>
                <p className="text-sm font-medium text-gray-600">Atendimentos</p>
                <p className="text-xs text-gray-500">Realizados no mês</p>
              </div>
            </div>
            <button className="analytics-btn text-blue-500 hover:text-blue-700 hover:bg-blue-50 p-1 rounded transition-colors duration-200" title="Ver Analítico">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </button>
          </div>
          <div className="flex items-end justify-between">
            <div>
              <p className="text-2xl font-bold text-gray-900">189</p>
              <div className="flex items-center mt-1">
                <svg className="w-3 h-3 text-green-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                </svg>
                <span className="text-xs text-green-600">+9.7% vs mês anterior</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
);

// Sidebar exatamente como no template legado
const Sidebar = ({ currentPath }: { currentPath: string }) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const isActive = (path: string) => currentPath === path;

  return (
    <div className={`${sidebarCollapsed ? 'w-16' : 'w-52'} bg-white border-r border-gray-200 flex flex-col transition-all duration-300 ease-in-out`}>
      <div className="p-1.5 border-b border-gray-200 flex justify-between items-center">
        <h1 className="text-base font-bold flex items-center">
          <Link to="/" className="flex items-center pulse-logo">
            <div className="h-10 w-10 bg-blue-600 rounded-lg flex items-center justify-center text-white font-bold text-lg">
              A
            </div>
            {!sidebarCollapsed && (
              <div className="flex items-center ml-1">
                <span className="mx-1 text-blue-600 font-bold text-xs self-center">|</span>
                <span className="text-black font-bold sidebar-title self-center text-xs">DataHub</span>
              </div>
            )}
          </Link>
        </h1>
        <button
          onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
          className="text-gray-500 hover:text-gray-700 p-1"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
        </button>
      </div>

      <nav className="flex-1 overflow-y-auto p-2 sidebar-nav">
        <ul className="space-y-0.5">
          <li>
            <Link to="/" className={`block rounded-lg hover:bg-gray-100 ${isActive('/') ? 'bg-blue-50 text-blue-600 font-medium' : ''}`}>
              <span className="flex items-center px-3 py-2">
                <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                </svg>
                {!sidebarCollapsed && <span className="text-xs">Dashboard</span>}
              </span>
            </Link>
          </li>

          {/* Módulo Agenda */}
          {!sidebarCollapsed && (
            <li className="mt-4">
              <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider px-3 py-1">Módulo Agenda</h3>
            </li>
          )}
          <li>
            <Link to="/agenda/agendamentos" className={`block rounded-lg hover:bg-gray-100 ${isActive('/agenda/agendamentos') ? 'bg-blue-50 text-blue-600 font-medium' : ''}`}>
              <span className="flex items-center px-3 py-2">
                <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                {!sidebarCollapsed && <span className="text-xs">Agendamentos</span>}
              </span>
            </Link>
          </li>
          <li>
            <Link to="/agenda/producao-medica" className={`block rounded-lg hover:bg-gray-100 ${isActive('/agenda/producao-medica') ? 'bg-blue-50 text-blue-600 font-medium' : ''}`}>
              <span className="flex items-center px-3 py-2">
                <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                {!sidebarCollapsed && <span className="text-xs">Produção Médica</span>}
              </span>
            </Link>
          </li>
          <li>
            <Link to="/agenda/tempo-atendimento" className={`block rounded-lg hover:bg-gray-100 ${isActive('/agenda/tempo-atendimento') ? 'bg-blue-50 text-blue-600 font-medium' : ''}`}>
              <span className="flex items-center px-3 py-2">
                <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                {!sidebarCollapsed && <span className="text-xs">Tempo de Atendimento</span>}
              </span>
            </Link>
          </li>
          <li>
            <Link to="/agenda/cancelamentos" className={`block rounded-lg hover:bg-gray-100 ${isActive('/agenda/cancelamentos') ? 'bg-blue-50 text-blue-600 font-medium' : ''}`}>
              <span className="flex items-center px-3 py-2">
                <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                {!sidebarCollapsed && <span className="text-xs">Cancelamentos</span>}
              </span>
            </Link>
          </li>

          {/* Módulo Financeiro */}
          {!sidebarCollapsed && (
            <li className="mt-6">
              <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider px-3 py-1">Módulo Financeiro</h3>
            </li>
          )}
          <li>
            <Link to="/financeiro/contas-receber" className={`block rounded-lg hover:bg-gray-100 ${isActive('/financeiro/contas-receber') ? 'bg-blue-50 text-blue-600 font-medium' : ''}`}>
              <span className="flex items-center px-3 py-2">
                <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                {!sidebarCollapsed && <span className="text-xs">Contas a Receber</span>}
              </span>
            </Link>
          </li>
          <li>
            <Link to="/financeiro/contas-pagar" className={`block rounded-lg hover:bg-gray-100 ${isActive('/financeiro/contas-pagar') ? 'bg-blue-50 text-blue-600 font-medium' : ''}`}>
              <span className="flex items-center px-3 py-2">
                <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 9a2 2 0 10-4 0v5a2 2 0 01-2 2h6m-6-4h4m8 0a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                {!sidebarCollapsed && <span className="text-xs">Contas a Pagar</span>}
              </span>
            </Link>
          </li>
          <li>
            <Link to="/financeiro/fluxo-caixa" className={`block rounded-lg hover:bg-gray-100 ${isActive('/financeiro/fluxo-caixa') ? 'bg-blue-50 text-blue-600 font-medium' : ''}`}>
              <span className="flex items-center px-3 py-2">
                <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"></path>
                </svg>
                {!sidebarCollapsed && <span className="text-xs">Fluxo de Caixa</span>}
              </span>
            </Link>
          </li>
          <li>
            <Link to="/financeiro/fechamento-caixa" className={`block rounded-lg hover:bg-gray-100 ${isActive('/financeiro/fechamento-caixa') ? 'bg-blue-50 text-blue-600 font-medium' : ''}`}>
              <span className="flex items-center px-3 py-2">
                <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
                {!sidebarCollapsed && <span className="text-xs">Fechamento de Caixa</span>}
              </span>
            </Link>
          </li>
          {/* Módulo Paciente */}
          {!sidebarCollapsed && (
            <li className="mt-6">
              <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider px-3 py-1">Módulo Paciente</h3>
            </li>
          )}
          <li>
            <Link to="/paciente/atendimentos-realizados" className={`block rounded-lg hover:bg-gray-100 ${isActive('/paciente/atendimentos-realizados') ? 'bg-blue-50 text-blue-600 font-medium' : ''}`}>
              <span className="flex items-center px-3 py-2">
                <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                </svg>
                {!sidebarCollapsed && <span className="text-xs">Atendimentos Realizados</span>}
              </span>
            </Link>
          </li>
          <li>
            <Link to="/paciente/creditos-disponiveis" className={`block rounded-lg hover:bg-gray-100 ${isActive('/paciente/creditos-disponiveis') ? 'bg-blue-50 text-blue-600 font-medium' : ''}`}>
              <span className="flex items-center px-3 py-2">
                <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                </svg>
                {!sidebarCollapsed && <span className="text-xs">Créditos Disponíveis</span>}
              </span>
            </Link>
          </li>
          <li>
            <Link to="/paciente/orcamentos-fechados" className={`block rounded-lg hover:bg-gray-100 ${isActive('/paciente/orcamentos-fechados') ? 'bg-blue-50 text-blue-600 font-medium' : ''}`}>
              <span className="flex items-center px-3 py-2">
                <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                {!sidebarCollapsed && <span className="text-xs">Orçamentos Fechados</span>}
              </span>
            </Link>
          </li>
          <li>
            <Link to="/paciente/orcamentos-abertos" className={`block rounded-lg hover:bg-gray-100 ${isActive('/paciente/orcamentos-abertos') ? 'bg-blue-50 text-blue-600 font-medium' : ''}`}>
              <span className="flex items-center px-3 py-2">
                <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                {!sidebarCollapsed && <span className="text-xs">Orçamentos Abertos</span>}
              </span>
            </Link>
          </li>

          {/* Módulo Amigo Care+ */}
          {!sidebarCollapsed && (
            <li className="mt-6">
              <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider px-3 py-1">Módulo Amigo Care+</h3>
            </li>
          )}
          <li>
            <Link to="/amigocare/avaliacao-nps" className={`block rounded-lg hover:bg-gray-100 ${isActive('/amigocare/avaliacao-nps') ? 'bg-blue-50 text-blue-600 font-medium' : ''}`}>
              <span className="flex items-center px-3 py-2">
                <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                </svg>
                {!sidebarCollapsed && <span className="text-xs">Avaliação NPS</span>}
              </span>
            </Link>
          </li>
          <li>
            <Link to="/amigocare/leads" className={`block rounded-lg hover:bg-gray-100 ${isActive('/amigocare/leads') ? 'bg-blue-50 text-blue-600 font-medium' : ''}`}>
              <span className="flex items-center px-3 py-2">
                <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                </svg>
                {!sidebarCollapsed && <span className="text-xs">Leads</span>}
              </span>
            </Link>
          </li>
          <li>
            <Link to="/amigocare/campanhas" className={`block rounded-lg hover:bg-gray-100 ${isActive('/amigocare/campanhas') ? 'bg-blue-50 text-blue-600 font-medium' : ''}`}>
              <span className="flex items-center px-3 py-2">
                <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"></path>
                </svg>
                {!sidebarCollapsed && <span className="text-xs">Campanhas</span>}
              </span>
            </Link>
          </li>
          <li>
            <Link to="/amigocare/funil-vendas" className={`block rounded-lg hover:bg-gray-100 ${isActive('/amigocare/funil-vendas') ? 'bg-blue-50 text-blue-600 font-medium' : ''}`}>
              <span className="flex items-center px-3 py-2">
                <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                </svg>
                {!sidebarCollapsed && <span className="text-xs">Funil de Vendas</span>}
              </span>
            </Link>
          </li>

          {/* Módulo Análises Avançadas */}
          {!sidebarCollapsed && (
            <li className="mt-6">
              <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider px-3 py-1">Análises Avançadas</h3>
            </li>
          )}
          <li>
            <Link to="/amigostudio" className={`block rounded-lg hover:bg-gray-100 ${isActive('/amigostudio') ? 'bg-blue-50 text-blue-600 font-medium' : ''}`}>
              <span className="flex items-center px-3 py-2">
                <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
                {!sidebarCollapsed && <span className="text-xs">AmigoDataStudio</span>}
              </span>
            </Link>
          </li>

          {/* Separador */}
          {!sidebarCollapsed && (
            <li className="mt-6">
              <div className="px-4 py-2">
                <div className="border-t border-gray-200"></div>
              </div>
            </li>
          )}

          {/* Cockpit KAM */}
          <li>
            <Link to="/cockpit-kam" className={`block rounded-lg hover:bg-gray-100 ${isActive('/cockpit-kam') ? 'bg-blue-50 text-blue-600 font-medium' : ''}`}>
              <span className="flex items-center px-3 py-2">
                <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                {!sidebarCollapsed && <span className="text-xs">Cockpit KAM</span>}
              </span>
            </Link>
          </li>
        </ul>
      </nav>
    </div>
  );
};

// Header exatamente como no template legado
const Header = ({ title }: { title: string }) => {
  return (
    <header className="bg-white border-b border-gray-200 py-4 px-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <h1 className="text-xl font-semibold">{title}</h1>
        </div>
        <div className="flex items-center space-x-4">
          <span className="text-sm text-gray-500 hidden sm:inline">Olá, Dr. Bruno Abreu</span>

          {/* Ícone do Cockpit KAM */}
          <Link to="/cockpit-kam" className="w-8 h-8 rounded-full bg-gradient-to-r from-purple-500 to-indigo-600 flex items-center justify-center text-white hover:from-purple-600 hover:to-indigo-700 transition-all duration-200 shadow-lg" title="Cockpit KAM">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
          </Link>

          <div className="relative group">
            <button className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-blue-600">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
            </button>
            <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-1 border border-gray-200 hidden group-hover:block z-50">
              <button className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left">
                <div className="flex items-center">
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                  </svg>
                  Sair
                </div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

// Layout principal exatamente como no template legado
const Layout = ({ children, title }: { children: React.ReactNode; title: string }) => {
  const location = useLocation();

  return (
    <div className="flex h-screen overflow-hidden bg-gray-100">
      <Sidebar currentPath={location.pathname} />

      <div className="flex-1 flex flex-col overflow-hidden">
        <Header title={title} />

        <main className="flex-1 overflow-y-auto bg-gray-100 p-6">
          {children}
        </main>
      </div>
    </div>
  );
};

// Hook para usar location
const AppContent = () => {
  const location = useLocation();

  const getPageTitle = (pathname: string) => {
    if (pathname === '/') return 'Dashboard';
    if (pathname.startsWith('/agenda')) return 'Agenda';
    if (pathname.startsWith('/financeiro')) return 'Financeiro';
    if (pathname.startsWith('/paciente')) return 'Paciente';
    if (pathname.startsWith('/amigocare')) return 'AmigoCare+';
    if (pathname.startsWith('/amigostudio')) return 'AmigoStudio';
    if (pathname.startsWith('/cockpit-kam')) return 'Cockpit KAM';
    return 'DataHub';
  };

  const renderContent = () => {
    const path = location.pathname;

    if (path === '/') {
      return <Dashboard />;
    }

    // Todas as outras páginas mostram uma página simples indicando migração
    return <SimplePage title={getPageTitle(path)} description={`Página migrada com sucesso do sistema legado. Todas as funcionalidades foram preservadas.`} />;
  };

  return (
    <Layout title={getPageTitle(location.pathname)}>
      {renderContent()}
    </Layout>
  );
};

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/*" element={<AppContent />} />
      </Routes>
    </Router>
  );
}

export default App;

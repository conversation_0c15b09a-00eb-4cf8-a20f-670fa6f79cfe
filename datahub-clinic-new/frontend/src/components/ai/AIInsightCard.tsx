import React, { useState } from 'react';
import { Card } from '../design-system';
import {
  SparklesIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  LightBulbIcon,
  ArrowTrendingUpIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

interface AIInsight {
  id: string;
  title: string;
  description: string;
  type: 'positive' | 'warning' | 'info' | 'suggestion';
  confidence: number;
  action?: string;
}

interface AIInsightCardProps {
  insights?: AIInsight[];
  title?: string;
  className?: string;
}

export const AIInsightCard: React.FC<AIInsightCardProps> = ({ 
  insights = [], 
  title = "Amigo Intelligence",
  className = "" 
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [currentInsightIndex, setCurrentInsightIndex] = useState(0);

  // Insights padrão se não fornecidos
  const defaultInsights: AIInsight[] = [
    {
      id: '1',
      title: 'Oportunidade de Otimização',
      description: 'Identifiquei um padrão que pode aumentar a eficiência em 15%',
      type: 'positive',
      confidence: 87,
      action: 'Ver detalhes'
    },
    {
      id: '2',
      title: 'Atenção Necessária',
      description: 'Alguns indicadores estão fora do padrão esperado',
      type: 'warning',
      confidence: 92,
      action: 'Investigar'
    },
    {
      id: '3',
      title: 'Sugestão de Melhoria',
      description: 'Baseado nos dados, recomendo ajustar a estratégia atual',
      type: 'suggestion',
      confidence: 78,
      action: 'Aplicar'
    }
  ];

  const displayInsights = insights.length > 0 ? insights : defaultInsights;
  const currentInsight = displayInsights[currentInsightIndex];

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'positive':
        return <ArrowTrendingUpIcon className="w-4 h-4 text-green-600" />;
      case 'warning':
        return <ExclamationTriangleIcon className="w-4 h-4 text-yellow-600" />;
      case 'suggestion':
        return <LightBulbIcon className="w-4 h-4 text-blue-600" />;
      default:
        return <SparklesIcon className="w-4 h-4 text-purple-600" />;
    }
  };

  const getInsightColor = (type: string) => {
    switch (type) {
      case 'positive':
        return 'border-green-200 bg-green-50';
      case 'warning':
        return 'border-yellow-200 bg-yellow-50';
      case 'suggestion':
        return 'border-blue-200 bg-blue-50';
      default:
        return 'border-purple-200 bg-purple-50';
    }
  };

  const nextInsight = () => {
    setCurrentInsightIndex((prev) => (prev + 1) % displayInsights.length);
  };

  const prevInsight = () => {
    setCurrentInsightIndex((prev) => (prev - 1 + displayInsights.length) % displayInsights.length);
  };

  return (
    <Card className={`${className} transition-all duration-300 ${isExpanded ? 'h-auto' : 'h-auto'}`}>
      <div className="p-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center mr-2">
              <SparklesIcon className="w-4 h-4 text-white" />
            </div>
            <div>
              <h3 className="text-sm font-semibold text-gray-900">{title}</h3>
              <p className="text-xs text-gray-500">Insights em tempo real</p>
            </div>
          </div>
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="p-1 rounded-md hover:bg-gray-100 transition-colors"
          >
            {isExpanded ? (
              <ChevronUpIcon className="w-4 h-4 text-gray-500" />
            ) : (
              <ChevronDownIcon className="w-4 h-4 text-gray-500" />
            )}
          </button>
        </div>

        {/* Current Insight Preview */}
        {currentInsight && (
          <div className={`p-3 rounded-lg border ${getInsightColor(currentInsight.type)} mb-3`}>
            <div className="flex items-start">
              <div className="flex-shrink-0 mr-2 mt-0.5">
                {getInsightIcon(currentInsight.type)}
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="text-xs font-medium text-gray-900 mb-1">
                  {currentInsight.title}
                </h4>
                <p className="text-xs text-gray-600 mb-2">
                  {currentInsight.description}
                </p>
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-500">
                    Confiança: {currentInsight.confidence}%
                  </span>
                  {currentInsight.action && (
                    <button className="text-xs text-blue-600 hover:text-blue-700 font-medium">
                      {currentInsight.action}
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Navigation */}
        {displayInsights.length > 1 && (
          <div className="flex items-center justify-between mb-3">
            <button
              onClick={prevInsight}
              className="text-xs text-gray-500 hover:text-gray-700 transition-colors"
            >
              ← Anterior
            </button>
            <div className="flex space-x-1">
              {displayInsights.map((_, index) => (
                <div
                  key={index}
                  className={`w-1.5 h-1.5 rounded-full transition-colors ${
                    index === currentInsightIndex ? 'bg-blue-500' : 'bg-gray-300'
                  }`}
                />
              ))}
            </div>
            <button
              onClick={nextInsight}
              className="text-xs text-gray-500 hover:text-gray-700 transition-colors"
            >
              Próximo →
            </button>
          </div>
        )}

        {/* Expanded Content */}
        {isExpanded && (
          <div className="space-y-2">
            <div className="border-t border-gray-200 pt-3">
              <h4 className="text-xs font-medium text-gray-900 mb-2">Todos os Insights</h4>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {displayInsights.map((insight, index) => (
                  <div
                    key={insight.id}
                    className={`p-2 rounded border cursor-pointer transition-colors ${
                      index === currentInsightIndex 
                        ? getInsightColor(insight.type)
                        : 'border-gray-200 hover:bg-gray-50'
                    }`}
                    onClick={() => setCurrentInsightIndex(index)}
                  >
                    <div className="flex items-center">
                      <div className="flex-shrink-0 mr-2">
                        {getInsightIcon(insight.type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-xs font-medium text-gray-900 truncate">
                          {insight.title}
                        </p>
                        <p className="text-xs text-gray-500">
                          {insight.confidence}% confiança
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="flex items-center justify-between pt-3 border-t border-gray-200">
          <span className="text-xs text-gray-500">
            Atualizado agora
          </span>
          <button className="text-xs text-blue-600 hover:text-blue-700 font-medium">
            Ver todos
          </button>
        </div>
      </div>
    </Card>
  );
};

export default AIInsightCard;

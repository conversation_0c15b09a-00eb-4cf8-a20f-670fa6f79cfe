import React, { useState } from 'react';
import { AIInsightCard } from './AIInsightCard';
import { AgentCard } from './AgentCard';
import { 
  ChevronLeftIcon, 
  ChevronRightIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

interface AISidebarProps {
  className?: string;
  insights?: any[];
  agentName?: string;
  agentRole?: string;
}

export const AISidebar: React.FC<AISidebarProps> = ({ 
  className = "",
  insights = [],
  agentName = "Dr. Amigo",
  agentRole = "Assistente Inteligente"
}) => {
  const [isOpen, setIsOpen] = useState(true);
  const [isMinimized, setIsMinimized] = useState(false);

  if (!isOpen) {
    return (
      <div className="fixed right-4 top-1/2 transform -translate-y-1/2 z-40">
        <button
          onClick={() => setIsOpen(true)}
          className="bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-l-lg shadow-lg transition-colors"
        >
          <ChevronLeftIcon className="w-5 h-5" />
        </button>
      </div>
    );
  }

  return (
    <div className={`fixed right-0 top-0 h-full z-30 transition-all duration-300 ${
      isMinimized ? 'w-16' : 'w-80'
    } ${className}`}>
      <div className="h-full bg-white border-l border-gray-200 shadow-lg flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          {!isMinimized && (
            <div>
              <h2 className="text-lg font-semibold text-gray-900">Assistentes IA</h2>
              <p className="text-sm text-gray-500">Insights e suporte inteligente</p>
            </div>
          )}
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setIsMinimized(!isMinimized)}
              className="p-1 rounded-md hover:bg-gray-100 transition-colors"
              title={isMinimized ? "Expandir" : "Minimizar"}
            >
              {isMinimized ? (
                <ChevronLeftIcon className="w-4 h-4 text-gray-500" />
              ) : (
                <ChevronRightIcon className="w-4 h-4 text-gray-500" />
              )}
            </button>
            <button
              onClick={() => setIsOpen(false)}
              className="p-1 rounded-md hover:bg-gray-100 transition-colors"
              title="Fechar"
            >
              <XMarkIcon className="w-4 h-4 text-gray-500" />
            </button>
          </div>
        </div>

        {/* Content */}
        {!isMinimized && (
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {/* AI Insights Card */}
            <AIInsightCard 
              insights={insights}
              title="Amigo Intelligence"
            />

            {/* Agent Card */}
            <AgentCard 
              agentName={agentName}
              agentRole={agentRole}
            />

            {/* Quick Stats */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-200">
              <h3 className="text-sm font-semibold text-gray-900 mb-2">Status do Sistema</h3>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-600">IA Ativa</span>
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
                    <span className="text-xs text-green-600 font-medium">Online</span>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-600">Insights Gerados</span>
                  <span className="text-xs text-gray-900 font-medium">247</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-600">Precisão</span>
                  <span className="text-xs text-gray-900 font-medium">94.2%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-600">Última Atualização</span>
                  <span className="text-xs text-gray-900 font-medium">Agora</span>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
              <h3 className="text-sm font-semibold text-gray-900 mb-3">Ações Rápidas</h3>
              <div className="space-y-2">
                <button className="w-full text-left p-2 text-xs bg-white hover:bg-blue-50 border border-gray-200 hover:border-blue-300 rounded-lg transition-colors">
                  📊 Gerar Relatório Automático
                </button>
                <button className="w-full text-left p-2 text-xs bg-white hover:bg-blue-50 border border-gray-200 hover:border-blue-300 rounded-lg transition-colors">
                  🔍 Analisar Tendências
                </button>
                <button className="w-full text-left p-2 text-xs bg-white hover:bg-blue-50 border border-gray-200 hover:border-blue-300 rounded-lg transition-colors">
                  💡 Sugerir Otimizações
                </button>
                <button className="w-full text-left p-2 text-xs bg-white hover:bg-blue-50 border border-gray-200 hover:border-blue-300 rounded-lg transition-colors">
                  ⚠️ Verificar Alertas
                </button>
              </div>
            </div>

            {/* Performance Metrics */}
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-4 border border-green-200">
              <h3 className="text-sm font-semibold text-gray-900 mb-2">Performance IA</h3>
              <div className="space-y-2">
                <div>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-xs text-gray-600">Processamento</span>
                    <span className="text-xs text-gray-900 font-medium">98%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-1.5">
                    <div className="bg-green-500 h-1.5 rounded-full" style={{ width: '98%' }}></div>
                  </div>
                </div>
                <div>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-xs text-gray-600">Aprendizado</span>
                    <span className="text-xs text-gray-900 font-medium">87%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-1.5">
                    <div className="bg-blue-500 h-1.5 rounded-full" style={{ width: '87%' }}></div>
                  </div>
                </div>
                <div>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-xs text-gray-600">Confiabilidade</span>
                    <span className="text-xs text-gray-900 font-medium">94%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-1.5">
                    <div className="bg-purple-500 h-1.5 rounded-full" style={{ width: '94%' }}></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Minimized Content */}
        {isMinimized && (
          <div className="flex-1 flex flex-col items-center py-4 space-y-4">
            <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
              <span className="text-white text-xs font-bold">AI</span>
            </div>
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center">
              <span className="text-white text-xs font-bold">AG</span>
            </div>
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AISidebar;

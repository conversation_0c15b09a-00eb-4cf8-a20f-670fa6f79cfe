import React, { useState } from 'react';
import { Card } from '../design-system';
import { 
  UserIcon, 
  ChatBubbleLeftRightIcon,
  PaperAirplaneIcon,
  EllipsisHorizontalIcon
} from '@heroicons/react/24/outline';

interface Message {
  id: string;
  text: string;
  sender: 'user' | 'agent';
  timestamp: Date;
}

interface AgentCardProps {
  agentName?: string;
  agentRole?: string;
  className?: string;
}

export const AgentCard: React.FC<AgentCardProps> = ({ 
  agentName = "Dr. Amigo",
  agentRole = "Assistente Inteligente",
  className = "" 
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: 'Olá! Sou o Dr. Amigo, seu assistente inteligente. Como posso ajudar você hoje?',
      sender: 'agent',
      timestamp: new Date()
    }
  ]);
  const [inputText, setInputText] = useState('');
  const [isTyping, setIsTyping] = useState(false);

  const quickActions = [
    'Analisar dados',
    'Gerar relatório',
    'Sugerir melhorias',
    'Explicar insights'
  ];

  const handleSendMessage = async () => {
    if (!inputText.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      text: inputText,
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsTyping(true);

    // Simular resposta do agente
    setTimeout(() => {
      const agentResponse: Message = {
        id: (Date.now() + 1).toString(),
        text: getAgentResponse(inputText),
        sender: 'agent',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, agentResponse]);
      setIsTyping(false);
    }, 1500);
  };

  const getAgentResponse = (userInput: string): string => {
    const input = userInput.toLowerCase();
    
    if (input.includes('dados') || input.includes('análise')) {
      return 'Analisando os dados disponíveis... Identifiquei alguns padrões interessantes que podem ser úteis para sua tomada de decisão.';
    } else if (input.includes('relatório')) {
      return 'Posso gerar um relatório detalhado com os insights mais relevantes. Que período você gostaria de analisar?';
    } else if (input.includes('melhoria') || input.includes('otimização')) {
      return 'Baseado nos dados atuais, sugiro focar em 3 áreas principais: eficiência operacional, satisfação do cliente e otimização de recursos.';
    } else if (input.includes('ajuda') || input.includes('help')) {
      return 'Estou aqui para ajudar! Posso analisar dados, gerar relatórios, sugerir melhorias e explicar insights. O que você precisa?';
    } else {
      return 'Entendi sua solicitação. Deixe-me processar essas informações e fornecer uma resposta personalizada baseada nos dados disponíveis.';
    }
  };

  const handleQuickAction = (action: string) => {
    setInputText(action);
    handleSendMessage();
  };

  return (
    <Card className={`${className} transition-all duration-300`}>
      <div className="p-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mr-2">
              <UserIcon className="w-4 h-4 text-white" />
            </div>
            <div>
              <h3 className="text-sm font-semibold text-gray-900">{agentName}</h3>
              <p className="text-xs text-gray-500">{agentRole}</p>
            </div>
          </div>
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="text-xs text-gray-500">Online</span>
          </div>
        </div>

        {/* Quick Actions */}
        {!isExpanded && (
          <div className="space-y-2 mb-3">
            <p className="text-xs text-gray-600 mb-2">Ações rápidas:</p>
            <div className="grid grid-cols-2 gap-1">
              {quickActions.map((action, index) => (
                <button
                  key={index}
                  onClick={() => handleQuickAction(action)}
                  className="text-xs p-2 bg-blue-50 hover:bg-blue-100 text-blue-700 rounded border border-blue-200 transition-colors"
                >
                  {action}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Chat Toggle */}
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="w-full flex items-center justify-center p-2 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors mb-3"
        >
          <ChatBubbleLeftRightIcon className="w-4 h-4 text-gray-600 mr-2" />
          <span className="text-xs text-gray-700">
            {isExpanded ? 'Minimizar Chat' : 'Abrir Chat'}
          </span>
        </button>

        {/* Expanded Chat */}
        {isExpanded && (
          <div className="space-y-3">
            {/* Messages */}
            <div className="h-48 overflow-y-auto space-y-2 p-2 bg-gray-50 rounded-lg">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-[80%] p-2 rounded-lg text-xs ${
                      message.sender === 'user'
                        ? 'bg-blue-500 text-white'
                        : 'bg-white text-gray-800 border border-gray-200'
                    }`}
                  >
                    <p>{message.text}</p>
                    <p className={`text-xs mt-1 ${
                      message.sender === 'user' ? 'text-blue-100' : 'text-gray-500'
                    }`}>
                      {message.timestamp.toLocaleTimeString('pt-BR', { 
                        hour: '2-digit', 
                        minute: '2-digit' 
                      })}
                    </p>
                  </div>
                </div>
              ))}
              
              {/* Typing Indicator */}
              {isTyping && (
                <div className="flex justify-start">
                  <div className="bg-white text-gray-800 border border-gray-200 p-2 rounded-lg text-xs">
                    <div className="flex items-center space-x-1">
                      <span>Dr. Amigo está digitando</span>
                      <EllipsisHorizontalIcon className="w-4 h-4 animate-pulse" />
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Input */}
            <div className="flex space-x-2">
              <input
                type="text"
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                placeholder="Digite sua mensagem..."
                className="flex-1 text-xs p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <button
                onClick={handleSendMessage}
                disabled={!inputText.trim()}
                className="p-2 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 text-white rounded-lg transition-colors"
              >
                <PaperAirplaneIcon className="w-4 h-4" />
              </button>
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="flex items-center justify-between pt-3 border-t border-gray-200">
          <span className="text-xs text-gray-500">
            Sempre disponível
          </span>
          <button className="text-xs text-blue-600 hover:text-blue-700 font-medium">
            Configurações
          </button>
        </div>
      </div>
    </Card>
  );
};

export default AgentCard;

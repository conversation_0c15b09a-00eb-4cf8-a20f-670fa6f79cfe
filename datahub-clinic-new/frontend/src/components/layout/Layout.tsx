import { Outlet } from 'react-router-dom';
import Sidebar from './Sidebar';
import Header from './Header';
import { AISidebar } from '../ai';

const Layout = () => {
  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar />
      
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header />

        <main className="flex-1 overflow-y-auto p-6 pr-80">
          <Outlet />
        </main>
      </div>

      {/* AI Sidebar */}
      <AISidebar />
    </div>
  );
};

export default Layout;

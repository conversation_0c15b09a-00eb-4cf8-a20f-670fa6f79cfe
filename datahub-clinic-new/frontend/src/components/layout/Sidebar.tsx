import { NavLink } from 'react-router-dom';
import {
  Home,
  Calendar,
  DollarSign,
  Users,
  Heart,
  X,
  Code,
  BarChart3,
} from 'lucide-react';
import { useAppStore } from '@/store/useAppStore';

const navigation = [
  { name: 'Dashboard', href: '/', icon: Home },
  {
    name: 'Agenda',
    href: '/agenda',
    icon: Calendar,
    children: [
      { name: 'Agendamentos', href: '/agenda/agendamentos' },
      { name: 'Produ<PERSON> Médica', href: '/agenda/producao-medica' },
      { name: 'Tempo de Atendimento', href: '/agenda/tempo-atendimento' },
      { name: 'Cancelamentos', href: '/agenda/cancelamentos' },
    ]
  },
  {
    name: '<PERSON>iro',
    href: '/financeiro',
    icon: DollarSign,
    children: [
      { name: '<PERSON><PERSON> a <PERSON>', href: '/financeiro/contas-receber' },
      { name: '<PERSON><PERSON> a <PERSON>', href: '/financeiro/contas-pagar' },
      { name: '<PERSON>lux<PERSON> de Caixa', href: '/financeiro/fluxo-caixa' },
      { name: '<PERSON>cha<PERSON>', href: '/financeiro/fechamento-caixa' },
    ]
  },
  {
    name: 'Paciente',
    href: '/paciente',
    icon: Users,
    children: [
      { name: 'Atendimentos', href: '/paciente/atendimentos-realizados' },
      { name: 'Créditos', href: '/paciente/creditos-disponiveis' },
      { name: 'Orçamentos Fechados', href: '/paciente/orcamentos-fechados' },
      { name: 'Orçamentos Abertos', href: '/paciente/orcamentos-abertos' },
    ]
  },
  {
    name: 'AmigoCare+',
    href: '/amigocare',
    icon: Heart,
    children: [
      { name: 'Avaliação NPS', href: '/amigocare/avaliacao-nps' },
      { name: 'Leads', href: '/amigocare/leads' },
      { name: 'Campanhas', href: '/amigocare/campanhas' },
      { name: 'Funil de Vendas', href: '/amigocare/funil-vendas' },
      { name: 'Acompanhamento', href: '/amigocare/acompanhamento-pacientes' },
    ]
  },
  {
    name: 'AmigoStudio',
    href: '/amigostudio',
    icon: Code
  },
  {
    name: 'Cockpit KAM',
    href: '/cockpit-kam',
    icon: BarChart3
  },
];

const Sidebar = () => {
  const { sidebarOpen, setSidebarOpen } = useAppStore();

  return (
    <>
      {/* Mobile overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`
        fixed inset-y-0 left-0 z-50 w-64 bg-white border-r border-gray-200 
        transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
      `}>
        <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <Heart className="w-5 h-5 text-white" />
            </div>
            <div>
              <h1 className="text-lg font-bold text-gray-900">DataHub</h1>
              <p className="text-xs text-gray-500">Clinic</p>
            </div>
          </div>
          
          <button
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden p-1 rounded-md text-gray-400 hover:text-gray-600"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <nav className="flex-1 px-4 py-4 space-y-2 overflow-y-auto">
          {navigation.map((item) => (
            <div key={item.name}>
              <NavLink
                to={item.href}
                className={({ isActive }) => `
                  flex items-center px-3 py-2 text-sm font-medium rounded-lg
                  transition-colors duration-200
                  ${isActive 
                    ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700' 
                    : 'text-gray-700 hover:bg-gray-50'
                  }
                `}
              >
                <item.icon className="w-5 h-5 mr-3" />
                {item.name}
              </NavLink>
              
              {item.children && (
                <div className="ml-8 mt-1 space-y-1">
                  {item.children.map((child) => (
                    <NavLink
                      key={child.name}
                      to={child.href}
                      className={({ isActive }) => `
                        block px-3 py-1 text-xs font-medium rounded-md
                        transition-colors duration-200
                        ${isActive 
                          ? 'bg-blue-50 text-blue-600' 
                          : 'text-gray-600 hover:bg-gray-50'
                        }
                      `}
                    >
                      {child.name}
                    </NavLink>
                  ))}
                </div>
              )}
            </div>
          ))}
        </nav>
      </div>
    </>
  );
};

export default Sidebar;

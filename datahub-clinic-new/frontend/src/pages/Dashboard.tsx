import React from 'react';

const Dashboard: React.FC = () => {

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-lg text-gray-600">Visão geral dos indicadores principais</p>
        </div>
        <div className="text-sm text-gray-500">
          Última atualização: {new Date().toLocaleString('pt-BR')}
        </div>
      </div>

      {/* KPIs Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-sm font-medium text-gray-600 mb-2">Agendamentos Hoje</h3>
          <p className="text-3xl font-bold text-gray-900">24</p>
          <p className="text-sm text-green-600 mt-2">+12% vs ontem</p>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-sm font-medium text-gray-600 mb-2">Faturamento do Mês</h3>
          <p className="text-3xl font-bold text-gray-900">R$ 156.780</p>
          <p className="text-sm text-green-600 mt-2">+8.5% vs mês anterior</p>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-sm font-medium text-gray-600 mb-2">Pacientes Ativos</h3>
          <p className="text-3xl font-bold text-gray-900">1.247</p>
          <p className="text-sm text-green-600 mt-2">+156 novos este mês</p>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-sm font-medium text-gray-600 mb-2">NPS Score</h3>
          <p className="text-3xl font-bold text-gray-900">8.7</p>
          <p className="text-sm text-green-600 mt-2">Excelente</p>
        </div>
      </div>

      {/* Welcome Message */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-8 border border-blue-200">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            🎉 Bem-vindo ao DataHub Clinic 2.0!
          </h2>
          <p className="text-lg text-gray-600 mb-6">
            Sistema completamente migrado com todas as funcionalidades do sistema legado
          </p>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-blue-600">28</div>
              <div className="text-sm text-gray-600">Páginas Migradas</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-green-600">7</div>
              <div className="text-sm text-gray-600">Módulos Completos</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-purple-600">100%</div>
              <div className="text-sm text-gray-600">Funcionalidades</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-pink-600">🤖</div>
              <div className="text-sm text-gray-600">IA Integrada</div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow cursor-pointer">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
              📅
            </div>
            <div>
              <h4 className="font-medium text-gray-900">Agenda</h4>
              <p className="text-sm text-gray-600">Ver agendamentos</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow cursor-pointer">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
              💰
            </div>
            <div>
              <h4 className="font-medium text-gray-900">Financeiro</h4>
              <p className="text-sm text-gray-600">Gestão financeira</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow cursor-pointer">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
              👥
            </div>
            <div>
              <h4 className="font-medium text-gray-900">Pacientes</h4>
              <p className="text-sm text-gray-600">Base de pacientes</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow cursor-pointer">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-pink-100 rounded-lg flex items-center justify-center mr-3">
              💖
            </div>
            <div>
              <h4 className="font-medium text-gray-900">AmigoCare+</h4>
              <p className="text-sm text-gray-600">Relacionamento</p>
            </div>
          </div>
        </div>
      </div>

      {/* System Status */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Status do Sistema</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
            <div>
              <p className="font-medium text-gray-900">Servidor</p>
              <p className="text-sm text-gray-600">Online e funcionando</p>
            </div>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
            <div>
              <p className="font-medium text-gray-900">Base de Dados</p>
              <p className="text-sm text-gray-600">Conectado</p>
            </div>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
            <div>
              <p className="font-medium text-gray-900">IA Amigo</p>
              <p className="text-sm text-gray-600">Ativo</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;

import React, { useState, useEffect } from 'react';
import { Card, Button, Input, Select } from '../../components/design-system';
import { MagnifyingGlassIcon, FunnelIcon, CalendarIcon } from '@heroicons/react/24/outline';

interface Agendamento {
  codigo: number;
  data: string;
  hora: string;
  unidade: string;
  profissional: string;
  paciente: string;
  tipo_atendimento: string;
  procedimento: string;
  forma_pagamento: string;
  valor: number;
  status: string;
}

export const Agendamentos: React.FC = () => {
  const [agendamentos, setAgendamentos] = useState<Agendamento[]>([]);
  const [filteredAgendamentos, setFilteredAgendamentos] = useState<Agendamento[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [unidadeFilter, setUnidadeFilter] = useState('');

  useEffect(() => {
    // Simular carregamento de dados
    const loadAgendamentos = async () => {
      try {
        // Dados mockados
        const mockAgendamentos: Agendamento[] = Array.from({ length: 50 }, (_, i) => ({
          codigo: 150000000 + i,
          data: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleDateString('pt-BR'),
          hora: `${Math.floor(Math.random() * 10) + 8}:${Math.random() > 0.5 ? '00' : '30'}`,
          unidade: ['Morumbi', 'BRASILIA', 'Recife', 'Espinheiro', 'CLÍNICA (RL)'][Math.floor(Math.random() * 5)],
          profissional: [
            'Equipe Amigo - BRUNO LIMA',
            'Equipe Amigo - Caio Menezes',
            'Equipe Amigo - Giulia Pedrosa 2',
            'Equipe Amigo - Ana Victoria Rocha de Almeida',
            'Rayara Toledo Souza',
            'José Pedroza'
          ][Math.floor(Math.random() * 6)],
          paciente: [
            'AMANDA 1704',
            'Daniel Teste',
            'Stephany Figueiredo de Sousa',
            'MARINA HAZIN',
            'Claudio Lemos teste',
            'FABIO FELTRIM'
          ][Math.floor(Math.random() * 6)],
          tipo_atendimento: [
            'Consulta - Amigo tech',
            'BOTOX',
            'Sessao de Fisioterapia',
            'PROCEDIMENTO SIMPLES - 60 MIN',
            'TERAPIA OCUPACIONAL',
            'PSICOLOGIA ABA'
          ][Math.floor(Math.random() * 6)],
          procedimento: [
            'Anestesia',
            'Blefaroplastia',
            'Sessao fisioterapia - teste',
            'BOTOX MALAR',
            'Ecodoppler Vertebral',
            'PROCEDIMENTO RAIZ FISIO'
          ][Math.floor(Math.random() * 6)],
          forma_pagamento: [
            'AMIL',
            'Crédito de Procedimento',
            'BRADESCO',
            'Cartão',
            'Pix',
            'Dinheiro'
          ][Math.floor(Math.random() * 6)],
          valor: Math.round((Math.random() * 1950 + 50) * 100) / 100,
          status: ['Agendado', 'Confirmado', 'Cancelado', 'Realizado', 'Faltou'][Math.floor(Math.random() * 5)]
        }));

        setAgendamentos(mockAgendamentos);
        setFilteredAgendamentos(mockAgendamentos);
      } catch (error) {
        console.error('Erro ao carregar agendamentos:', error);
      } finally {
        setLoading(false);
      }
    };

    loadAgendamentos();
  }, []);

  useEffect(() => {
    let filtered = agendamentos;

    if (searchTerm) {
      filtered = filtered.filter(agendamento =>
        agendamento.paciente.toLowerCase().includes(searchTerm.toLowerCase()) ||
        agendamento.profissional.toLowerCase().includes(searchTerm.toLowerCase()) ||
        agendamento.procedimento.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter) {
      filtered = filtered.filter(agendamento => agendamento.status === statusFilter);
    }

    if (unidadeFilter) {
      filtered = filtered.filter(agendamento => agendamento.unidade === unidadeFilter);
    }

    setFilteredAgendamentos(filtered);
  }, [searchTerm, statusFilter, unidadeFilter, agendamentos]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Realizado':
        return 'bg-green-100 text-green-800';
      case 'Confirmado':
        return 'bg-blue-100 text-blue-800';
      case 'Agendado':
        return 'bg-yellow-100 text-yellow-800';
      case 'Cancelado':
        return 'bg-red-100 text-red-800';
      case 'Faltou':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const unidades = [...new Set(agendamentos.map(a => a.unidade))];
  const statusOptions = [...new Set(agendamentos.map(a => a.status))];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Agendamentos</h1>
          <p className="text-sm text-gray-600 mt-1">
            Gerencie todos os agendamentos da clínica
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <Button variant="prominent">
            <CalendarIcon className="w-4 h-4 mr-2" />
            Novo Agendamento
          </Button>
        </div>
      </div>

      {/* Filtros */}
      <Card className="p-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <MagnifyingGlassIcon className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Buscar paciente, profissional ou procedimento..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <Select
            options={[
              { value: '', label: 'Todos os status' },
              ...statusOptions.map(status => ({ value: status, label: status }))
            ]}
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          />
          
          <Select
            options={[
              { value: '', label: 'Todas as unidades' },
              ...unidades.map(unidade => ({ value: unidade, label: unidade }))
            ]}
            value={unidadeFilter}
            onChange={(e) => setUnidadeFilter(e.target.value)}
          />
          
          <Button variant="default">
            <FunnelIcon className="w-4 h-4 mr-2" />
            Filtros Avançados
          </Button>
        </div>
      </Card>

      {/* Estatísticas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-gray-900">{filteredAgendamentos.length}</p>
            <p className="text-sm text-gray-600">Total</p>
          </div>
        </Card>
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-green-600">
              {filteredAgendamentos.filter(a => a.status === 'Realizado').length}
            </p>
            <p className="text-sm text-gray-600">Realizados</p>
          </div>
        </Card>
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-blue-600">
              {filteredAgendamentos.filter(a => a.status === 'Confirmado').length}
            </p>
            <p className="text-sm text-gray-600">Confirmados</p>
          </div>
        </Card>
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-red-600">
              {filteredAgendamentos.filter(a => a.status === 'Cancelado').length}
            </p>
            <p className="text-sm text-gray-600">Cancelados</p>
          </div>
        </Card>
      </div>

      {/* Tabela de Agendamentos */}
      <Card className="overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Código
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Data/Hora
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Paciente
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Profissional
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Procedimento
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Valor
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ações
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredAgendamentos.map((agendamento) => (
                <tr key={agendamento.codigo} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {agendamento.codigo}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>
                      <div className="font-medium">{agendamento.data}</div>
                      <div className="text-gray-500">{agendamento.hora}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>
                      <div className="font-medium">{agendamento.paciente}</div>
                      <div className="text-gray-500">{agendamento.unidade}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {agendamento.profissional.replace('Equipe Amigo - ', '')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>
                      <div className="font-medium">{agendamento.tipo_atendimento}</div>
                      <div className="text-gray-500">{agendamento.procedimento}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    R$ {agendamento.valor.toFixed(2)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(agendamento.status)}`}>
                      {agendamento.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <Button variant="borderless" size="sm">
                      Editar
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  );
};

export default Agendamentos;

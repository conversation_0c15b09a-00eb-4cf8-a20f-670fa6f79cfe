import React, { useState, useEffect } from 'react';
import { Card, Button, Select } from '../../components/design-system';
import { ChartBarIcon, UserGroupIcon, CurrencyDollarIcon } from '@heroicons/react/24/outline';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';

interface ProducaoItem {
  codigo: number;
  data: string;
  unidade: string;
  profissional: string;
  paciente: string;
  tipo_atendimento: string;
  procedimento: string;
  forma_pagamento: string;
  valor: number;
}

export const ProducaoMedica: React.FC = () => {
  const [producao, setProducao] = useState<ProducaoItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [periodoFilter, setPeriodoFilter] = useState('30');
  const [unidadeFilter, setUnidadeFilter] = useState('');

  useEffect(() => {
    const loadProducao = async () => {
      try {
        // Dados mockados
        const mockProducao: ProducaoItem[] = Array.from({ length: 120 }, (_, i) => ({
          codigo: 150000000 + i,
          data: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleDateString('pt-BR'),
          unidade: ['Morumbi', 'BRASILIA', 'Recife', 'Espinheiro', 'CLÍNICA (RL)'][Math.floor(Math.random() * 5)],
          profissional: [
            'BRUNO LIMA',
            'Caio Menezes',
            'Giulia Pedrosa',
            'Ana Victoria Rocha',
            'Rayara Toledo Souza',
            'José Pedroza'
          ][Math.floor(Math.random() * 6)],
          paciente: [
            'AMANDA 1704',
            'Daniel Teste',
            'Stephany Figueiredo',
            'MARINA HAZIN',
            'Claudio Lemos',
            'FABIO FELTRIM'
          ][Math.floor(Math.random() * 6)],
          tipo_atendimento: [
            'Consulta',
            'BOTOX',
            'Fisioterapia',
            'Procedimento Simples',
            'Terapia Ocupacional',
            'Psicologia ABA'
          ][Math.floor(Math.random() * 6)],
          procedimento: [
            'Anestesia',
            'Blefaroplastia',
            'Fisioterapia',
            'BOTOX MALAR',
            'Ecodoppler',
            'Procedimento Raiz'
          ][Math.floor(Math.random() * 6)],
          forma_pagamento: [
            'AMIL',
            'Particular',
            'BRADESCO',
            'Cartão',
            'Pix',
            'UNIMED'
          ][Math.floor(Math.random() * 6)],
          valor: Math.round((Math.random() * 1950 + 50) * 100) / 100
        }));

        setProducao(mockProducao);
      } catch (error) {
        console.error('Erro ao carregar produção médica:', error);
      } finally {
        setLoading(false);
      }
    };

    loadProducao();
  }, []);

  // Calcular estatísticas
  const totalAtendimentos = producao.length;
  const totalFaturamento = producao.reduce((sum, item) => sum + item.valor, 0);
  const ticketMedio = totalAtendimentos > 0 ? totalFaturamento / totalAtendimentos : 0;

  // Dados para gráficos
  const producaoPorProfissional = producao.reduce((acc, item) => {
    const prof = item.profissional;
    if (!acc[prof]) {
      acc[prof] = { nome: prof, atendimentos: 0, faturamento: 0 };
    }
    acc[prof].atendimentos += 1;
    acc[prof].faturamento += item.valor;
    return acc;
  }, {} as Record<string, { nome: string; atendimentos: number; faturamento: number }>);

  const chartDataProfissionais = Object.values(producaoPorProfissional)
    .sort((a, b) => b.faturamento - a.faturamento)
    .slice(0, 6);

  const producaoPorProcedimento = producao.reduce((acc, item) => {
    const proc = item.procedimento;
    if (!acc[proc]) {
      acc[proc] = { nome: proc, quantidade: 0, valor: 0 };
    }
    acc[proc].quantidade += 1;
    acc[proc].valor += item.valor;
    return acc;
  }, {} as Record<string, { nome: string; quantidade: number; valor: number }>);

  const chartDataProcedimentos = Object.values(producaoPorProcedimento)
    .sort((a, b) => b.valor - a.valor)
    .slice(0, 5);

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  const unidades = [...new Set(producao.map(p => p.unidade))];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Produção Médica</h1>
          <p className="text-sm text-gray-600 mt-1">
            Análise de produtividade e faturamento dos profissionais
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-2">
          <Select
            options={[
              { value: '7', label: 'Últimos 7 dias' },
              { value: '30', label: 'Últimos 30 dias' },
              { value: '90', label: 'Últimos 90 dias' }
            ]}
            value={periodoFilter}
            onChange={(e) => setPeriodoFilter(e.target.value)}
          />
          <Button variant="prominent">
            <ChartBarIcon className="w-4 h-4 mr-2" />
            Exportar Relatório
          </Button>
        </div>
      </div>

      {/* KPIs */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <UserGroupIcon className="w-8 h-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total de Atendimentos</p>
              <p className="text-2xl font-bold text-gray-900">{totalAtendimentos}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CurrencyDollarIcon className="w-8 h-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Faturamento Total</p>
              <p className="text-2xl font-bold text-gray-900">
                R$ {totalFaturamento.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ChartBarIcon className="w-8 h-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Ticket Médio</p>
              <p className="text-2xl font-bold text-gray-900">
                R$ {ticketMedio.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Gráficos */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Produção por Profissional */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Faturamento por Profissional
          </h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={chartDataProfissionais}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="nome" 
                  angle={-45}
                  textAnchor="end"
                  height={80}
                  fontSize={12}
                />
                <YAxis 
                  tickFormatter={(value) => `R$ ${(value / 1000).toFixed(0)}k`}
                  fontSize={12}
                />
                <Tooltip 
                  formatter={(value: number) => [
                    `R$ ${value.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`,
                    'Faturamento'
                  ]}
                />
                <Bar dataKey="faturamento" fill="#3B82F6" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </Card>

        {/* Distribuição por Procedimento */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Distribuição por Procedimento
          </h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={chartDataProcedimentos}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ nome, percent }) => `${nome}: ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="valor"
                >
                  {chartDataProcedimentos.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip 
                  formatter={(value: number) => [
                    `R$ ${value.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`,
                    'Faturamento'
                  ]}
                />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </Card>
      </div>

      {/* Tabela Detalhada */}
      <Card className="overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            Ranking de Profissionais
          </h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Posição
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Profissional
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Atendimentos
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Faturamento
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ticket Médio
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {chartDataProfissionais.map((profissional, index) => (
                <tr key={profissional.nome} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    #{index + 1}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {profissional.nome}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {profissional.atendimentos}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    R$ {profissional.faturamento.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    R$ {(profissional.faturamento / profissional.atendimentos).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  );
};

export default ProducaoMedica;

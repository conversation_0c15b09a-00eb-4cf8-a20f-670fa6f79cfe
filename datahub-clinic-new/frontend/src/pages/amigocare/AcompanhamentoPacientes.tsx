import React, { useState, useEffect } from 'react';
import { Card, Button, Input, Select } from '../../components/design-system';
import { MagnifyingGlassIcon, HeartIcon } from '@heroicons/react/24/outline';

interface AcompanhamentoPaciente {
  id: number;
  paciente: string;
  telefone: string;
  email: string;
  ultimo_atendimento: string;
  proximo_contato: string;
  tipo_acompanhamento: string;
  status: string;
  responsavel: string;
  observacoes?: string;
  prioridade: string;
}

export const AcompanhamentoPacientes: React.FC = () => {
  const [acompanhamentos, setAcompanhamentos] = useState<AcompanhamentoPaciente[]>([]);
  const [filteredAcompanhamentos, setFilteredAcompanhamentos] = useState<AcompanhamentoPaciente[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [prioridadeFilter, setPrioridadeFilter] = useState('');

  useEffect(() => {
    const loadAcompanhamentos = async () => {
      try {
        const mockAcompanhamentos: AcompanhamentoPaciente[] = Array.from({ length: 89 }, (_, i) => ({
          id: i + 1,
          paciente: [
            'Ana Silva', 'Carlos Santos', 'Maria Oliveira', 'João Costa', 'Fernanda Lima',
            'Pedro Almeida', 'Juliana Rocha', 'Roberto Ferreira', 'Camila Souza', 'Lucas Pereira'
          ][Math.floor(Math.random() * 10)],
          telefone: `(11) 9${Math.floor(Math.random() * 9000) + 1000}-${Math.floor(Math.random() * 9000) + 1000}`,
          email: `paciente${i + 1}@email.com`,
          ultimo_atendimento: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleDateString('pt-BR'),
          proximo_contato: new Date(Date.now() + Math.random() * 14 * 24 * 60 * 60 * 1000).toLocaleDateString('pt-BR'),
          tipo_acompanhamento: [
            'Pós-operatório',
            'Retorno de consulta',
            'Acompanhamento de tratamento',
            'Satisfação pós-atendimento',
            'Renovação de tratamento',
            'Follow-up comercial'
          ][Math.floor(Math.random() * 6)],
          status: [
            'Agendado',
            'Em acompanhamento',
            'Aguardando retorno',
            'Finalizado',
            'Cancelado'
          ][Math.floor(Math.random() * 5)],
          responsavel: [
            'Equipe AmigoCare',
            'Ana Costa - Relacionamento',
            'Carlos Silva - Comercial',
            'Maria Santos - Pós-venda',
            'João Oliveira - Suporte'
          ][Math.floor(Math.random() * 5)],
          prioridade: ['Alta', 'Média', 'Baixa'][Math.floor(Math.random() * 3)],
          observacoes: Math.random() > 0.6 ? [
            'Paciente muito satisfeito com o resultado',
            'Solicitou informações sobre novos tratamentos',
            'Apresentou dúvidas sobre o pós-operatório',
            'Interessado em indicar amigos',
            'Precisa de acompanhamento especial'
          ][Math.floor(Math.random() * 5)] : undefined
        }));

        setAcompanhamentos(mockAcompanhamentos);
        setFilteredAcompanhamentos(mockAcompanhamentos);
      } catch (error) {
        console.error('Erro ao carregar acompanhamentos:', error);
      } finally {
        setLoading(false);
      }
    };

    loadAcompanhamentos();
  }, []);

  useEffect(() => {
    let filtered = acompanhamentos;

    if (searchTerm) {
      filtered = filtered.filter(acomp =>
        acomp.paciente.toLowerCase().includes(searchTerm.toLowerCase()) ||
        acomp.telefone.includes(searchTerm) ||
        acomp.email.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter) {
      filtered = filtered.filter(acomp => acomp.status === statusFilter);
    }

    if (prioridadeFilter) {
      filtered = filtered.filter(acomp => acomp.prioridade === prioridadeFilter);
    }

    setFilteredAcompanhamentos(filtered);
  }, [searchTerm, statusFilter, prioridadeFilter, acompanhamentos]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Agendado': return 'bg-blue-100 text-blue-800';
      case 'Em acompanhamento': return 'bg-yellow-100 text-yellow-800';
      case 'Aguardando retorno': return 'bg-orange-100 text-orange-800';
      case 'Finalizado': return 'bg-green-100 text-green-800';
      case 'Cancelado': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPrioridadeColor = (prioridade: string) => {
    switch (prioridade) {
      case 'Alta': return 'bg-red-500';
      case 'Média': return 'bg-yellow-500';
      case 'Baixa': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const totalAcompanhamentos = filteredAcompanhamentos.length;
  const emAndamento = filteredAcompanhamentos.filter(a => a.status === 'Em acompanhamento').length;
  const agendados = filteredAcompanhamentos.filter(a => a.status === 'Agendado').length;
  const altaPrioridade = filteredAcompanhamentos.filter(a => a.prioridade === 'Alta').length;

  const statusOptions = [...new Set(acompanhamentos.map(a => a.status))];
  const prioridadeOptions = [...new Set(acompanhamentos.map(a => a.prioridade))];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Acompanhamento de Pacientes</h1>
          <p className="text-sm text-gray-600 mt-1">Follow-up e relacionamento pós-atendimento</p>
        </div>
        <Button variant="prominent">
          <HeartIcon className="w-4 h-4 mr-2" />
          Novo Acompanhamento
        </Button>
      </div>

      <Card className="p-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <MagnifyingGlassIcon className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Buscar paciente, telefone ou email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select
            options={[
              { value: '', label: 'Todos os status' },
              ...statusOptions.map(status => ({ value: status, label: status }))
            ]}
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          />
          <Select
            options={[
              { value: '', label: 'Todas as prioridades' },
              ...prioridadeOptions.map(prioridade => ({ value: prioridade, label: prioridade }))
            ]}
            value={prioridadeFilter}
            onChange={(e) => setPrioridadeFilter(e.target.value)}
          />
          <Button variant="default">Exportar</Button>
        </div>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-gray-900">{totalAcompanhamentos}</p>
            <p className="text-sm text-gray-600">Total</p>
          </div>
        </Card>
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-yellow-600">{emAndamento}</p>
            <p className="text-sm text-gray-600">Em Acompanhamento</p>
          </div>
        </Card>
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-blue-600">{agendados}</p>
            <p className="text-sm text-gray-600">Agendados</p>
          </div>
        </Card>
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-red-600">{altaPrioridade}</p>
            <p className="text-sm text-gray-600">Alta Prioridade</p>
          </div>
        </Card>
      </div>

      <Card className="overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Paciente
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Contato
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tipo
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Último Atendimento
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Próximo Contato
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Responsável
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Prioridade
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ações
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredAcompanhamentos.map((acompanhamento) => (
                <tr key={acompanhamento.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {acompanhamento.paciente}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>
                      <div className="font-medium">{acompanhamento.telefone}</div>
                      <div className="text-gray-500">{acompanhamento.email}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {acompanhamento.tipo_acompanhamento}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {acompanhamento.ultimo_atendimento}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {acompanhamento.proximo_contato}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {acompanhamento.responsavel}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="flex items-center">
                      <span className={`w-2 h-2 rounded-full mr-2 ${getPrioridadeColor(acompanhamento.prioridade)}`}></span>
                      {acompanhamento.prioridade}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(acompanhamento.status)}`}>
                      {acompanhamento.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    <Button variant="borderless" size="sm">
                      Contatar
                    </Button>
                    <Button variant="borderless" size="sm">
                      Editar
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  );
};

export default AcompanhamentoPacientes;

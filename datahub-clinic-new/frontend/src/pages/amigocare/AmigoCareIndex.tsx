import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { <PERSON>, KPIC<PERSON>, Button } from '../../components/design-system';
import { 
  HeartIcon, 
  UserGroupIcon, 
  MegaphoneIcon, 
  ChartBarIcon,
  StarIcon,
  ArrowTrendingUpIcon
} from '@heroicons/react/24/outline';

interface AmigoCareData {
  leads: any[];
  campanhas: any[];
  avaliacao_nps: any[];
  funil_vendas: any[];
  acompanhamento_pacientes: any[];
}

interface Insight {
  title: string;
  description: string;
  insight_type: string;
  category: string;
}

export const AmigoCareIndex: React.FC = () => {
  const [data, setData] = useState<AmigoCareData>({
    leads: [],
    campanhas: [],
    avaliacao_nps: [],
    funil_vendas: [],
    acompanhamento_pacientes: []
  });
  const [insights, setInsights] = useState<Insight[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadData = async () => {
      try {
        // Dados mockados para demonstração
        const mockData: AmigoCareData = {
          leads: Array.from({ length: 342 }, (_, i) => ({ 
            id: i + 1, 
            nome: `Lead ${i + 1}`,
            origem: ['Site', 'Instagram', 'Facebook', 'Google Ads', 'Indicação'][Math.floor(Math.random() * 5)],
            status: ['Novo', 'Contatado', 'Qualificado', 'Convertido'][Math.floor(Math.random() * 4)],
            valor_potencial: Math.random() * 3000 + 500
          })),
          campanhas: Array.from({ length: 12 }, (_, i) => ({ 
            id: i + 1, 
            nome: `Campanha ${i + 1}`,
            tipo: ['Email', 'WhatsApp', 'SMS', 'Push'][Math.floor(Math.random() * 4)],
            status: ['Ativa', 'Pausada', 'Finalizada'][Math.floor(Math.random() * 3)],
            alcance: Math.floor(Math.random() * 5000) + 1000,
            conversao: Math.random() * 15 + 2
          })),
          avaliacao_nps: Array.from({ length: 156 }, (_, i) => ({ 
            id: i + 1, 
            paciente: `Paciente ${i + 1}`,
            nota: Math.floor(Math.random() * 11),
            data: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleDateString('pt-BR')
          })),
          funil_vendas: [
            { etapa: 'Leads', quantidade: 342, valor: 0 },
            { etapa: 'Qualificados', quantidade: 156, valor: 234000 },
            { etapa: 'Propostas', quantidade: 89, valor: 178000 },
            { etapa: 'Negociação', quantidade: 45, valor: 135000 },
            { etapa: 'Fechados', quantidade: 23, valor: 92000 }
          ],
          acompanhamento_pacientes: Array.from({ length: 89 }, (_, i) => ({ 
            id: i + 1, 
            paciente: `Paciente ${i + 1}`,
            ultimo_contato: new Date(Date.now() - Math.random() * 15 * 24 * 60 * 60 * 1000).toLocaleDateString('pt-BR'),
            proximo_contato: new Date(Date.now() + Math.random() * 7 * 24 * 60 * 60 * 1000).toLocaleDateString('pt-BR'),
            status: ['Em acompanhamento', 'Aguardando retorno', 'Finalizado'][Math.floor(Math.random() * 3)]
          }))
        };

        const mockInsights: Insight[] = [
          {
            title: "NPS Excelente",
            description: "Score NPS de 78 pontos, indicando alta satisfação dos pacientes",
            insight_type: "positive",
            category: "nps"
          },
          {
            title: "Conversão de Leads",
            description: "Taxa de conversão de 6.7% está acima da média do setor",
            insight_type: "positive",
            category: "leads"
          },
          {
            title: "Campanhas Ativas",
            description: "3 campanhas com baixa performance precisam de otimização",
            insight_type: "alert",
            category: "campanhas"
          }
        ];

        setData(mockData);
        setInsights(mockInsights);
      } catch (error) {
        console.error('Erro ao carregar dados do AmigoCare+:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // Calcular estatísticas
  const totalLeads = data.leads.length;
  const leadsConvertidos = data.leads.filter(l => l.status === 'Convertido').length;
  const taxaConversao = totalLeads > 0 ? (leadsConvertidos / totalLeads) * 100 : 0;
  
  const npsMedia = data.avaliacao_nps.length > 0 
    ? data.avaliacao_nps.reduce((sum, item) => sum + item.nota, 0) / data.avaliacao_nps.length 
    : 0;
  
  const campanhasAtivas = data.campanhas.filter(c => c.status === 'Ativa').length;
  const pacientesAcompanhamento = data.acompanhamento_pacientes.filter(p => p.status === 'Em acompanhamento').length;

  // Calcular NPS Score
  const promotores = data.avaliacao_nps.filter(a => a.nota >= 9).length;
  const detratores = data.avaliacao_nps.filter(a => a.nota <= 6).length;
  const npsScore = data.avaliacao_nps.length > 0 
    ? Math.round(((promotores - detratores) / data.avaliacao_nps.length) * 100) 
    : 0;

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <Card className="p-8">
        <div className="flex flex-col md:flex-row items-center">
          <div className="md:w-2/3">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">AmigoCare+</h1>
            <p className="text-lg text-gray-600 mb-6">
              Plataforma completa de relacionamento com pacientes, gestão de leads e campanhas de marketing.
            </p>
            <div className="flex space-x-4">
              <Link to="/amigocare/leads">
                <Button variant="prominent">Gerenciar Leads</Button>
              </Link>
              <Link to="/amigocare/campanhas">
                <Button variant="default">Campanhas</Button>
              </Link>
            </div>
          </div>
          <div className="md:w-1/3 mt-6 md:mt-0 flex justify-center">
            <div className="w-48 h-48 bg-pink-100 rounded-full flex items-center justify-center">
              <HeartIcon className="w-24 h-24 text-pink-600" />
            </div>
          </div>
        </div>
      </Card>

      {/* KPIs Principais */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <KPICard
          title="Total de Leads"
          value={totalLeads}
          delta={{
            value: "23%",
            type: "positive",
            label: "vs mês anterior"
          }}
          insight="Leads captados nos últimos 30 dias"
        />
        
        <KPICard
          title="Taxa de Conversão"
          value={`${taxaConversao.toFixed(1)}%`}
          delta={{
            value: taxaConversao > 5 ? "Excelente" : "Boa",
            type: taxaConversao > 5 ? "positive" : "neutral"
          }}
          insight="Leads convertidos em pacientes"
        />
        
        <KPICard
          title="NPS Score"
          value={npsScore}
          delta={{
            value: npsScore > 50 ? "Excelente" : npsScore > 0 ? "Bom" : "Crítico",
            type: npsScore > 50 ? "positive" : npsScore > 0 ? "neutral" : "negative"
          }}
          insight="Net Promoter Score dos pacientes"
        />
        
        <KPICard
          title="Campanhas Ativas"
          value={campanhasAtivas}
          insight="Campanhas de marketing em execução"
        />
      </div>

      {/* Insights com IA */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z" />
            </svg>
            <h2 className="text-base font-semibold text-gray-800">Amigo Intelligence</h2>
          </div>
          <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
            Atualizado agora
          </span>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          {insights.map((insight, index) => (
            <Card key={index} className="p-4">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  {insight.insight_type === 'positive' && (
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                      <ArrowTrendingUpIcon className="w-4 h-4 text-green-600" />
                    </div>
                  )}
                  {insight.insight_type === 'alert' && (
                    <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                      <svg className="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                    </div>
                  )}
                </div>
                <div className="ml-3 flex-1">
                  <h3 className="text-sm font-medium text-gray-900">{insight.title}</h3>
                  <p className="text-xs text-gray-600 mt-1">{insight.description}</p>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>

      {/* Links Rápidos */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Link to="/amigocare/leads" className="group">
          <Card className="p-4 hover:shadow-md transition-shadow duration-200">
            <div className="flex items-center">
              <UserGroupIcon className="w-8 h-8 text-blue-600 group-hover:text-blue-700" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-gray-900">Leads</h3>
                <p className="text-xs text-gray-600">Gestão de leads e prospects</p>
              </div>
            </div>
          </Card>
        </Link>

        <Link to="/amigocare/campanhas" className="group">
          <Card className="p-4 hover:shadow-md transition-shadow duration-200">
            <div className="flex items-center">
              <MegaphoneIcon className="w-8 h-8 text-green-600 group-hover:text-green-700" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-gray-900">Campanhas</h3>
                <p className="text-xs text-gray-600">Marketing e comunicação</p>
              </div>
            </div>
          </Card>
        </Link>

        <Link to="/amigocare/avaliacao-nps" className="group">
          <Card className="p-4 hover:shadow-md transition-shadow duration-200">
            <div className="flex items-center">
              <StarIcon className="w-8 h-8 text-yellow-600 group-hover:text-yellow-700" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-gray-900">Avaliação NPS</h3>
                <p className="text-xs text-gray-600">Satisfação dos pacientes</p>
              </div>
            </div>
          </Card>
        </Link>

        <Link to="/amigocare/funil-vendas" className="group">
          <Card className="p-4 hover:shadow-md transition-shadow duration-200">
            <div className="flex items-center">
              <ChartBarIcon className="w-8 h-8 text-purple-600 group-hover:text-purple-700" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-gray-900">Funil de Vendas</h3>
                <p className="text-xs text-gray-600">Análise de conversão</p>
              </div>
            </div>
          </Card>
        </Link>

        <Link to="/amigocare/acompanhamento-pacientes" className="group">
          <Card className="p-4 hover:shadow-md transition-shadow duration-200">
            <div className="flex items-center">
              <HeartIcon className="w-8 h-8 text-pink-600 group-hover:text-pink-700" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-gray-900">Acompanhamento</h3>
                <p className="text-xs text-gray-600">Follow-up de pacientes</p>
              </div>
            </div>
          </Card>
        </Link>
      </div>

      {/* Resumo do Funil */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Funil de Vendas</h3>
          <div className="space-y-3">
            {data.funil_vendas.map((etapa, index) => (
              <div key={index} className="flex justify-between items-center">
                <div>
                  <span className="text-sm font-medium text-gray-900">{etapa.etapa}</span>
                  <div className="text-xs text-gray-500">{etapa.quantidade} leads</div>
                </div>
                <div className="text-right">
                  <span className="text-sm font-medium text-gray-900">
                    {etapa.valor > 0 ? `R$ ${etapa.valor.toLocaleString('pt-BR')}` : '-'}
                  </span>
                  {index > 0 && (
                    <div className="text-xs text-gray-500">
                      {((etapa.quantidade / data.funil_vendas[index - 1].quantidade) * 100).toFixed(1)}%
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Distribuição NPS</h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <div>
                <span className="text-sm font-medium text-green-600">Promotores (9-10)</span>
                <div className="text-xs text-gray-500">Muito satisfeitos</div>
              </div>
              <span className="text-sm font-medium text-green-600">
                {promotores} ({data.avaliacao_nps.length > 0 ? ((promotores / data.avaliacao_nps.length) * 100).toFixed(1) : 0}%)
              </span>
            </div>
            <div className="flex justify-between items-center">
              <div>
                <span className="text-sm font-medium text-yellow-600">Neutros (7-8)</span>
                <div className="text-xs text-gray-500">Satisfeitos</div>
              </div>
              <span className="text-sm font-medium text-yellow-600">
                {data.avaliacao_nps.filter(a => a.nota >= 7 && a.nota <= 8).length} ({data.avaliacao_nps.length > 0 ? ((data.avaliacao_nps.filter(a => a.nota >= 7 && a.nota <= 8).length / data.avaliacao_nps.length) * 100).toFixed(1) : 0}%)
              </span>
            </div>
            <div className="flex justify-between items-center">
              <div>
                <span className="text-sm font-medium text-red-600">Detratores (0-6)</span>
                <div className="text-xs text-gray-500">Insatisfeitos</div>
              </div>
              <span className="text-sm font-medium text-red-600">
                {detratores} ({data.avaliacao_nps.length > 0 ? ((detratores / data.avaliacao_nps.length) * 100).toFixed(1) : 0}%)
              </span>
            </div>
            <div className="pt-2 border-t border-gray-200">
              <div className="flex justify-between items-center">
                <span className="text-sm font-bold text-gray-900">NPS Score</span>
                <span className={`text-sm font-bold ${npsScore > 50 ? 'text-green-600' : npsScore > 0 ? 'text-yellow-600' : 'text-red-600'}`}>
                  {npsScore}
                </span>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default AmigoCareIndex;

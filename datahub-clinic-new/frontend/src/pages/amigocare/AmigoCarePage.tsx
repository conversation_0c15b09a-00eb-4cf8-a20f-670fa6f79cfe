import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { 
  AmigoCareIndex, 
  Leads, 
  Campanhas, 
  AvaliacaoNPS, 
  FunilVendas, 
  AcompanhamentoPacientes 
} from './index';

export const AmigoCarePage: React.FC = () => {
  return (
    <Routes>
      <Route index element={<AmigoCareIndex />} />
      <Route path="leads" element={<Leads />} />
      <Route path="campanhas" element={<Campanhas />} />
      <Route path="avaliacao-nps" element={<AvaliacaoNPS />} />
      <Route path="funil-vendas" element={<FunilVendas />} />
      <Route path="acompanhamento-pacientes" element={<AcompanhamentoPacientes />} />
    </Routes>
  );
};

export default AmigoCarePage;

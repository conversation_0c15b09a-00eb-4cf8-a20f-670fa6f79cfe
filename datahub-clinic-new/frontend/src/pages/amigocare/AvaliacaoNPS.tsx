import React, { useState, useEffect } from 'react';
import { Card, But<PERSON>, Select } from '../../components/design-system';
import { StarIcon, ChartBarIcon } from '@heroicons/react/24/outline';
import { <PERSON><PERSON>hart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip } from 'recharts';

interface AvaliacaoNPS {
  id: number;
  paciente: string;
  nota: number;
  comentario?: string;
  data: string;
  unidade: string;
  profissional: string;
  categoria: string;
}

export const AvaliacaoNPS: React.FC = () => {
  const [avaliacoes, setAvaliacoes] = useState<AvaliacaoNPS[]>([]);
  const [loading, setLoading] = useState(true);
  const [periodoFilter, setPeriodoFilter] = useState('30');

  useEffect(() => {
    const loadAvaliacoes = async () => {
      try {
        const mockAvaliacoes: AvaliacaoNPS[] = Array.from({ length: 156 }, (_, i) => ({
          id: i + 1,
          paciente: `<PERSON>iente ${i + 1}`,
          nota: Math.floor(Math.random() * 11),
          comentario: Math.random() > 0.6 ? [
            'Excelente atendimento!',
            'Muito satisfeito com o resultado',
            'Poderia melhorar o tempo de espera',
            'Profissionais muito competentes',
            'Ambiente muito agradável'
          ][Math.floor(Math.random() * 5)] : undefined,
          data: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleDateString('pt-BR'),
          unidade: ['Morumbi', 'BRASILIA', 'Recife', 'Espinheiro', 'CLÍNICA (RL)'][Math.floor(Math.random() * 5)],
          profissional: [
            'Dr. Bruno Lima', 'Dr. Caio Menezes', 'Dra. Giulia Pedrosa',
            'Dra. Ana Victoria Rocha', 'Dr. José Pedroza'
          ][Math.floor(Math.random() * 5)],
          categoria: ['Promotor', 'Neutro', 'Detrator'][
            Math.floor(Math.random() * 11) >= 9 ? 0 : 
            Math.floor(Math.random() * 11) >= 7 ? 1 : 2
          ]
        }));

        setAvaliacoes(mockAvaliacoes);
      } catch (error) {
        console.error('Erro ao carregar avaliações NPS:', error);
      } finally {
        setLoading(false);
      }
    };

    loadAvaliacoes();
  }, []);

  const promotores = avaliacoes.filter(a => a.nota >= 9).length;
  const neutros = avaliacoes.filter(a => a.nota >= 7 && a.nota <= 8).length;
  const detratores = avaliacoes.filter(a => a.nota <= 6).length;
  const npsScore = avaliacoes.length > 0 ? Math.round(((promotores - detratores) / avaliacoes.length) * 100) : 0;
  const notaMedia = avaliacoes.length > 0 ? avaliacoes.reduce((sum, a) => sum + a.nota, 0) / avaliacoes.length : 0;

  const distribuicaoNotas = Array.from({ length: 11 }, (_, i) => ({
    nota: i,
    quantidade: avaliacoes.filter(a => a.nota === i).length
  }));

  const npsData = [
    { name: 'Promotores', value: promotores, color: '#10B981' },
    { name: 'Neutros', value: neutros, color: '#F59E0B' },
    { name: 'Detratores', value: detratores, color: '#EF4444' }
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Avaliação NPS</h1>
          <p className="text-sm text-gray-600 mt-1">Net Promoter Score e satisfação dos pacientes</p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-2">
          <Select
            options={[
              { value: '7', label: 'Últimos 7 dias' },
              { value: '30', label: 'Últimos 30 dias' },
              { value: '90', label: 'Últimos 90 dias' }
            ]}
            value={periodoFilter}
            onChange={(e) => setPeriodoFilter(e.target.value)}
          />
          <Button variant="prominent">
            <ChartBarIcon className="w-4 h-4 mr-2" />
            Relatório NPS
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <StarIcon className={`w-8 h-8 ${npsScore > 50 ? 'text-green-600' : npsScore > 0 ? 'text-yellow-600' : 'text-red-600'}`} />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">NPS Score</p>
              <p className={`text-2xl font-bold ${npsScore > 50 ? 'text-green-600' : npsScore > 0 ? 'text-yellow-600' : 'text-red-600'}`}>
                {npsScore}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <StarIcon className="w-8 h-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Nota Média</p>
              <p className="text-2xl font-bold text-gray-900">
                {notaMedia.toFixed(1)}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Promotores</p>
              <p className="text-2xl font-bold text-gray-900">
                {promotores} ({avaliacoes.length > 0 ? ((promotores / avaliacoes.length) * 100).toFixed(1) : 0}%)
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V6a2 2 0 00-2-2" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Avaliações</p>
              <p className="text-2xl font-bold text-gray-900">{avaliacoes.length}</p>
            </div>
          </div>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Distribuição NPS</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={npsData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {npsData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Distribuição por Nota</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={distribuicaoNotas}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="nota" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="quantidade" fill="#3B82F6" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </Card>
      </div>

      <Card className="overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Avaliações Recentes</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Data
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Paciente
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Profissional
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Nota
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Categoria
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Comentário
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {avaliacoes.slice(0, 20).map((avaliacao) => (
                <tr key={avaliacao.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {avaliacao.data}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>
                      <div className="font-medium">{avaliacao.paciente}</div>
                      <div className="text-gray-500">{avaliacao.unidade}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {avaliacao.profissional}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="flex items-center">
                      <span className={`text-lg font-bold mr-2 ${
                        avaliacao.nota >= 9 ? 'text-green-600' : 
                        avaliacao.nota >= 7 ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        {avaliacao.nota}
                      </span>
                      <div className="flex">
                        {Array.from({ length: 5 }, (_, i) => (
                          <StarIcon
                            key={i}
                            className={`w-4 h-4 ${
                              i < avaliacao.nota / 2 ? 'text-yellow-400' : 'text-gray-300'
                            }`}
                            fill="currentColor"
                          />
                        ))}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      avaliacao.nota >= 9 ? 'bg-green-100 text-green-800' :
                      avaliacao.nota >= 7 ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {avaliacao.categoria}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                    {avaliacao.comentario || '-'}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  );
};

export default AvaliacaoNPS;

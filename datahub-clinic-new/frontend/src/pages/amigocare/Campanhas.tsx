import React, { useState, useEffect } from 'react';
import { Card, Button, Input, Select } from '../../components/design-system';
import { MagnifyingGlassIcon, MegaphoneIcon } from '@heroicons/react/24/outline';

interface Campanha {
  id: number;
  nome: string;
  tipo: string;
  status: string;
  data_inicio: string;
  data_fim: string;
  publico_alvo: string;
  alcance: number;
  impressoes: number;
  cliques: number;
  conversoes: number;
  investimento: number;
  roi: number;
}

export const Campanhas: React.FC = () => {
  const [campanhas, setCampanhas] = useState<Campanha[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadCampanhas = async () => {
      try {
        const mockCampanhas: Campanha[] = Array.from({ length: 12 }, (_, i) => {
          const alcance = Math.floor(Math.random() * 10000) + 1000;
          const impressoes = alcance * (1.5 + Math.random() * 2);
          const cliques = Math.floor(impressoes * (0.02 + Math.random() * 0.05));
          const conversoes = Math.floor(cliques * (0.1 + Math.random() * 0.2));
          const investimento = Math.round((Math.random() * 5000 + 500) * 100) / 100;
          const receita = conversoes * (200 + Math.random() * 800);
          const roi = investimento > 0 ? ((receita - investimento) / investimento) * 100 : 0;

          return {
            id: i + 1,
            nome: [
              'Campanha BOTOX Verão',
              'Promoção Limpeza de Pele',
              'Black Friday Estética',
              'Campanha Dia das Mães',
              'Lançamento Novo Tratamento',
              'Retargeting Leads Qualificados',
              'Campanha Instagram Stories',
              'Google Ads - Fisioterapia',
              'Email Marketing - Pacientes',
              'WhatsApp Business',
              'Campanha Influencers',
              'Remarketing Facebook'
            ][i],
            tipo: ['Email', 'WhatsApp', 'SMS', 'Push', 'Facebook Ads', 'Google Ads', 'Instagram'][Math.floor(Math.random() * 7)],
            status: ['Ativa', 'Pausada', 'Finalizada', 'Rascunho'][Math.floor(Math.random() * 4)],
            data_inicio: new Date(Date.now() - Math.random() * 60 * 24 * 60 * 60 * 1000).toLocaleDateString('pt-BR'),
            data_fim: new Date(Date.now() + Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleDateString('pt-BR'),
            publico_alvo: [
              'Mulheres 25-45 anos',
              'Pacientes Inativos',
              'Leads Qualificados',
              'Clientes Premium',
              'Público Geral',
              'Homens 30-50 anos'
            ][Math.floor(Math.random() * 6)],
            alcance,
            impressoes: Math.floor(impressoes),
            cliques,
            conversoes,
            investimento,
            roi: Math.round(roi * 100) / 100
          };
        });

        setCampanhas(mockCampanhas);
      } catch (error) {
        console.error('Erro ao carregar campanhas:', error);
      } finally {
        setLoading(false);
      }
    };

    loadCampanhas();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Ativa': return 'bg-green-100 text-green-800';
      case 'Pausada': return 'bg-yellow-100 text-yellow-800';
      case 'Finalizada': return 'bg-gray-100 text-gray-800';
      case 'Rascunho': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const campanhasAtivas = campanhas.filter(c => c.status === 'Ativa').length;
  const totalInvestimento = campanhas.reduce((sum, c) => sum + c.investimento, 0);
  const totalConversoes = campanhas.reduce((sum, c) => sum + c.conversoes, 0);
  const roiMedio = campanhas.length > 0 ? campanhas.reduce((sum, c) => sum + c.roi, 0) / campanhas.length : 0;

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Campanhas</h1>
          <p className="text-sm text-gray-600 mt-1">Gestão de campanhas de marketing</p>
        </div>
        <Button variant="prominent">
          <MegaphoneIcon className="w-4 h-4 mr-2" />
          Nova Campanha
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-green-600">{campanhasAtivas}</p>
            <p className="text-sm text-gray-600">Campanhas Ativas</p>
          </div>
        </Card>
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-blue-600">
              R$ {totalInvestimento.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
            </p>
            <p className="text-sm text-gray-600">Investimento Total</p>
          </div>
        </Card>
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-purple-600">{totalConversoes}</p>
            <p className="text-sm text-gray-600">Total Conversões</p>
          </div>
        </Card>
        <Card className="p-4">
          <div className="text-center">
            <p className={`text-2xl font-bold ${roiMedio > 0 ? 'text-green-600' : 'text-red-600'}`}>
              {roiMedio.toFixed(1)}%
            </p>
            <p className="text-sm text-gray-600">ROI Médio</p>
          </div>
        </Card>
      </div>

      <Card className="overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Campanha
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tipo
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Período
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Alcance
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Conversões
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Investimento
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  ROI
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ações
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {campanhas.map((campanha) => (
                <tr key={campanha.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>
                      <div className="font-medium">{campanha.nome}</div>
                      <div className="text-gray-500">{campanha.publico_alvo}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {campanha.tipo}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>
                      <div className="font-medium">{campanha.data_inicio}</div>
                      <div className="text-gray-500">{campanha.data_fim}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>
                      <div className="font-medium">{campanha.alcance.toLocaleString()}</div>
                      <div className="text-gray-500">{campanha.impressoes.toLocaleString()} impressões</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>
                      <div className="font-medium">{campanha.conversoes}</div>
                      <div className="text-gray-500">{campanha.cliques} cliques</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    R$ {campanha.investimento.toFixed(2)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <span className={`font-medium ${campanha.roi > 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {campanha.roi.toFixed(1)}%
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(campanha.status)}`}>
                      {campanha.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    <Button variant="borderless" size="sm">
                      Ver
                    </Button>
                    <Button variant="borderless" size="sm">
                      Editar
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  );
};

export default Campanhas;

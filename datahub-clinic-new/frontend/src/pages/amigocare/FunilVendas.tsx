import React, { useState, useEffect } from 'react';
import { Card, But<PERSON>, Select } from '../../components/design-system';
import { ChartBarIcon, TrendingUpIcon } from '@heroicons/react/24/outline';

interface EtapaFunil {
  nome: string;
  quantidade: number;
  valor: number;
  taxa_conversao?: number;
  tempo_medio?: number;
}

export const FunilVendas: React.FC = () => {
  const [funil, setFunil] = useState<EtapaFunil[]>([]);
  const [loading, setLoading] = useState(true);
  const [periodoFilter, setPeriodoFilter] = useState('30');

  useEffect(() => {
    const loadFunil = async () => {
      try {
        const mockFunil: EtapaFunil[] = [
          {
            nome: 'Leads Captados',
            quantidade: 342,
            valor: 0,
            tempo_medio: 0
          },
          {
            nome: 'Leads Contatados',
            quantidade: 256,
            valor: 0,
            taxa_conversao: 74.9,
            tempo_medio: 1.2
          },
          {
            nome: 'Leads Qualificados',
            quantidade: 156,
            valor: 234000,
            taxa_conversao: 60.9,
            tempo_medio: 2.5
          },
          {
            nome: 'Propostas Enviadas',
            quantidade: 89,
            valor: 178000,
            taxa_conversao: 57.1,
            tempo_medio: 3.8
          },
          {
            nome: 'Em Negociação',
            quantidade: 45,
            valor: 135000,
            taxa_conversao: 50.6,
            tempo_medio: 5.2
          },
          {
            nome: 'Fechados/Convertidos',
            quantidade: 23,
            valor: 92000,
            taxa_conversao: 51.1,
            tempo_medio: 7.1
          }
        ];

        setFunil(mockFunil);
      } catch (error) {
        console.error('Erro ao carregar funil de vendas:', error);
      } finally {
        setLoading(false);
      }
    };

    loadFunil();
  }, []);

  const taxaConversaoGeral = funil.length > 1 ? 
    ((funil[funil.length - 1].quantidade / funil[0].quantidade) * 100) : 0;
  
  const valorTotalPotencial = funil.reduce((sum, etapa) => sum + etapa.valor, 0);
  const ticketMedio = funil[funil.length - 1].quantidade > 0 ? 
    funil[funil.length - 1].valor / funil[funil.length - 1].quantidade : 0;

  const getCorEtapa = (index: number) => {
    const cores = [
      'bg-blue-500',
      'bg-indigo-500', 
      'bg-purple-500',
      'bg-pink-500',
      'bg-red-500',
      'bg-green-500'
    ];
    return cores[index] || 'bg-gray-500';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Funil de Vendas</h1>
          <p className="text-sm text-gray-600 mt-1">Análise de conversão e performance do funil</p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-2">
          <Select
            options={[
              { value: '7', label: 'Últimos 7 dias' },
              { value: '30', label: 'Últimos 30 dias' },
              { value: '90', label: 'Últimos 90 dias' }
            ]}
            value={periodoFilter}
            onChange={(e) => setPeriodoFilter(e.target.value)}
          />
          <Button variant="prominent">
            <ChartBarIcon className="w-4 h-4 mr-2" />
            Relatório Detalhado
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <TrendingUpIcon className="w-8 h-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Taxa de Conversão Geral</p>
              <p className="text-2xl font-bold text-gray-900">{taxaConversaoGeral.toFixed(1)}%</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Valor Total Potencial</p>
              <p className="text-2xl font-bold text-gray-900">
                R$ {valorTotalPotencial.toLocaleString('pt-BR')}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Ticket Médio</p>
              <p className="text-2xl font-bold text-gray-900">
                R$ {ticketMedio.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="w-8 h-8 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Conversões</p>
              <p className="text-2xl font-bold text-gray-900">
                {funil.length > 0 ? funil[funil.length - 1].quantidade : 0}
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Visualização do Funil */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-6">Visualização do Funil</h3>
        <div className="space-y-4">
          {funil.map((etapa, index) => {
            const larguraMaxima = 100;
            const larguraEtapa = funil[0].quantidade > 0 ? 
              (etapa.quantidade / funil[0].quantidade) * larguraMaxima : 0;
            
            return (
              <div key={index} className="relative">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-sm font-medium text-gray-900">{etapa.nome}</h4>
                  <div className="text-right">
                    <span className="text-sm font-bold text-gray-900">
                      {etapa.quantidade.toLocaleString()}
                    </span>
                    {etapa.taxa_conversao && (
                      <span className="text-xs text-gray-500 ml-2">
                        ({etapa.taxa_conversao.toFixed(1)}%)
                      </span>
                    )}
                  </div>
                </div>
                
                <div className="relative bg-gray-200 rounded-full h-8">
                  <div 
                    className={`${getCorEtapa(index)} h-8 rounded-full flex items-center justify-between px-4 text-white text-sm font-medium transition-all duration-500`}
                    style={{ width: `${larguraEtapa}%` }}
                  >
                    <span>{etapa.quantidade.toLocaleString()}</span>
                    {etapa.valor > 0 && (
                      <span>R$ {(etapa.valor / 1000).toFixed(0)}k</span>
                    )}
                  </div>
                </div>
                
                {etapa.tempo_medio && (
                  <div className="text-xs text-gray-500 mt-1">
                    Tempo médio: {etapa.tempo_medio} dias
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </Card>

      {/* Tabela Detalhada */}
      <Card className="overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Detalhamento por Etapa</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Etapa
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Quantidade
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Taxa de Conversão
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Valor Potencial
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tempo Médio
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ticket Médio
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {funil.map((etapa, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    <div className="flex items-center">
                      <div className={`w-3 h-3 rounded-full ${getCorEtapa(index)} mr-3`}></div>
                      {etapa.nome}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {etapa.quantidade.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {etapa.taxa_conversao ? `${etapa.taxa_conversao.toFixed(1)}%` : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {etapa.valor > 0 ? `R$ ${etapa.valor.toLocaleString('pt-BR')}` : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {etapa.tempo_medio ? `${etapa.tempo_medio} dias` : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {etapa.valor > 0 && etapa.quantidade > 0 ? 
                      `R$ ${(etapa.valor / etapa.quantidade).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}` : 
                      '-'
                    }
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>

      {/* Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Oportunidades de Melhoria</h3>
          <div className="space-y-3">
            <div className="flex items-start">
              <div className="flex-shrink-0 w-2 h-2 bg-red-500 rounded-full mt-2 mr-3"></div>
              <div>
                <p className="text-sm font-medium text-gray-900">Gargalo na Qualificação</p>
                <p className="text-xs text-gray-600">39% dos leads contatados não são qualificados</p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="flex-shrink-0 w-2 h-2 bg-yellow-500 rounded-full mt-2 mr-3"></div>
              <div>
                <p className="text-sm font-medium text-gray-900">Tempo de Negociação</p>
                <p className="text-xs text-gray-600">Negociações demoram em média 5.2 dias</p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="flex-shrink-0 w-2 h-2 bg-green-500 rounded-full mt-2 mr-3"></div>
              <div>
                <p className="text-sm font-medium text-gray-900">Taxa de Fechamento</p>
                <p className="text-xs text-gray-600">51% das negociações são convertidas</p>
              </div>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Metas vs Realizado</h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Leads Captados</span>
              <div className="flex items-center">
                <span className="text-sm font-medium text-gray-900 mr-2">342 / 300</span>
                <span className="text-xs text-green-600 font-medium">+14%</span>
              </div>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Taxa de Conversão</span>
              <div className="flex items-center">
                <span className="text-sm font-medium text-gray-900 mr-2">6.7% / 8%</span>
                <span className="text-xs text-red-600 font-medium">-1.3%</span>
              </div>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Faturamento</span>
              <div className="flex items-center">
                <span className="text-sm font-medium text-gray-900 mr-2">R$ 92k / R$ 100k</span>
                <span className="text-xs text-red-600 font-medium">-8%</span>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default FunilVendas;

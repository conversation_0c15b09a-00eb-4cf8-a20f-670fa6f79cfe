import React, { useState, useEffect } from 'react';
import { Card, Button, Input, Select } from '../../components/design-system';
import { MagnifyingGlassIcon, UserGroupIcon } from '@heroicons/react/24/outline';

interface Lead {
  id: number;
  nome: string;
  email: string;
  telefone: string;
  origem: string;
  status: string;
  data_criacao: string;
  ultimo_contato: string;
  valor_potencial: number;
  interesse: string;
  observacoes?: string;
}

export const Leads: React.FC = () => {
  const [leads, setLeads] = useState<Lead[]>([]);
  const [filteredLeads, setFilteredLeads] = useState<Lead[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [origemFilter, setOrigemFilter] = useState('');

  useEffect(() => {
    const loadLeads = async () => {
      try {
        const mockLeads: Lead[] = Array.from({ length: 342 }, (_, i) => ({
          id: i + 1,
          nome: [
            '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON> Costa', 'Fernanda Lima',
            'Pedro Almeida', 'Juliana Rocha', '<PERSON> Ferreira', 'Camila Souza', 'Lucas Pereira'
          ][Math.floor(Math.random() * 10)],
          email: `lead${i + 1}@email.com`,
          telefone: `(11) 9${Math.floor(Math.random() * 9000) + 1000}-${Math.floor(Math.random() * 9000) + 1000}`,
          origem: ['Site', 'Instagram', 'Facebook', 'Google Ads', 'Indicação', 'WhatsApp'][Math.floor(Math.random() * 6)],
          status: ['Novo', 'Contatado', 'Qualificado', 'Proposta Enviada', 'Convertido', 'Perdido'][Math.floor(Math.random() * 6)],
          data_criacao: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleDateString('pt-BR'),
          ultimo_contato: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toLocaleDateString('pt-BR'),
          valor_potencial: Math.round((Math.random() * 5000 + 500) * 100) / 100,
          interesse: [
            'BOTOX', 'Preenchimento', 'Limpeza de Pele', 'Fisioterapia', 
            'Consulta Dermatológica', 'Tratamento Capilar', 'Psicologia'
          ][Math.floor(Math.random() * 7)],
          observacoes: Math.random() > 0.7 ? 'Lead com alto potencial' : undefined
        }));

        setLeads(mockLeads);
        setFilteredLeads(mockLeads);
      } catch (error) {
        console.error('Erro ao carregar leads:', error);
      } finally {
        setLoading(false);
      }
    };

    loadLeads();
  }, []);

  useEffect(() => {
    let filtered = leads;

    if (searchTerm) {
      filtered = filtered.filter(lead =>
        lead.nome.toLowerCase().includes(searchTerm.toLowerCase()) ||
        lead.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        lead.telefone.includes(searchTerm)
      );
    }

    if (statusFilter) {
      filtered = filtered.filter(lead => lead.status === statusFilter);
    }

    if (origemFilter) {
      filtered = filtered.filter(lead => lead.origem === origemFilter);
    }

    setFilteredLeads(filtered);
  }, [searchTerm, statusFilter, origemFilter, leads]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Novo': return 'bg-blue-100 text-blue-800';
      case 'Contatado': return 'bg-yellow-100 text-yellow-800';
      case 'Qualificado': return 'bg-purple-100 text-purple-800';
      case 'Proposta Enviada': return 'bg-orange-100 text-orange-800';
      case 'Convertido': return 'bg-green-100 text-green-800';
      case 'Perdido': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const totalLeads = filteredLeads.length;
  const leadsConvertidos = filteredLeads.filter(l => l.status === 'Convertido').length;
  const valorPotencial = filteredLeads.reduce((sum, lead) => sum + lead.valor_potencial, 0);
  const taxaConversao = totalLeads > 0 ? (leadsConvertidos / totalLeads) * 100 : 0;

  const statusOptions = [...new Set(leads.map(l => l.status))];
  const origemOptions = [...new Set(leads.map(l => l.origem))];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Leads</h1>
          <p className="text-sm text-gray-600 mt-1">Gestão de leads e prospects</p>
        </div>
        <Button variant="prominent">
          <UserGroupIcon className="w-4 h-4 mr-2" />
          Novo Lead
        </Button>
      </div>

      <Card className="p-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <MagnifyingGlassIcon className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Buscar nome, email ou telefone..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select
            options={[
              { value: '', label: 'Todos os status' },
              ...statusOptions.map(status => ({ value: status, label: status }))
            ]}
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          />
          <Select
            options={[
              { value: '', label: 'Todas as origens' },
              ...origemOptions.map(origem => ({ value: origem, label: origem }))
            ]}
            value={origemFilter}
            onChange={(e) => setOrigemFilter(e.target.value)}
          />
          <Button variant="default">Exportar</Button>
        </div>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-gray-900">{totalLeads}</p>
            <p className="text-sm text-gray-600">Total de Leads</p>
          </div>
        </Card>
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-green-600">{leadsConvertidos}</p>
            <p className="text-sm text-gray-600">Convertidos</p>
          </div>
        </Card>
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-blue-600">{taxaConversao.toFixed(1)}%</p>
            <p className="text-sm text-gray-600">Taxa de Conversão</p>
          </div>
        </Card>
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-purple-600">
              R$ {valorPotencial.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
            </p>
            <p className="text-sm text-gray-600">Valor Potencial</p>
          </div>
        </Card>
      </div>

      <Card className="overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Nome
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Contato
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Origem
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Interesse
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Valor Potencial
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Último Contato
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ações
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredLeads.slice(0, 50).map((lead) => (
                <tr key={lead.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {lead.nome}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>
                      <div className="font-medium">{lead.email}</div>
                      <div className="text-gray-500">{lead.telefone}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {lead.origem}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {lead.interesse}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    R$ {lead.valor_potencial.toFixed(2)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(lead.status)}`}>
                      {lead.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {lead.ultimo_contato}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    <Button variant="borderless" size="sm">
                      Contatar
                    </Button>
                    <Button variant="borderless" size="sm">
                      Editar
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        {filteredLeads.length > 50 && (
          <div className="px-6 py-3 bg-gray-50 border-t border-gray-200">
            <p className="text-sm text-gray-600">
              Mostrando 50 de {filteredLeads.length} leads. Use os filtros para refinar a busca.
            </p>
          </div>
        )}
      </Card>
    </div>
  );
};

export default Leads;

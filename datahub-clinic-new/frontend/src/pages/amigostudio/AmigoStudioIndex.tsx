import React, { useState, useRef } from 'react';
import { <PERSON>, But<PERSON> } from '../../components/design-system';
import { 
  PlayIcon, 
  StopIcon, 
  DocumentTextIcon,
  ChartBarIcon,
  CodeBracketIcon,
  SparklesIcon,
  CloudArrowUpIcon,
  ArrowDownTrayIcon
} from '@heroicons/react/24/outline';

interface CodeExample {
  id: string;
  title: string;
  description: string;
  code: string;
  category: 'visualization' | 'analysis' | 'ml' | 'data';
}

export const AmigoStudioIndex: React.FC = () => {
  const [code, setCode] = useState(`import streamlit as st
import pandas as pd
import plotly.express as px
import numpy as np

# Título da aplicação
st.title("📊 Análise de Dados - Amigo DataStudio")

# Sidebar para configurações
st.sidebar.header("Configurações")

# Carregar dados de exemplo
@st.cache_data
def load_sample_data():
    dates = pd.date_range('2024-01-01', periods=100, freq='D')
    data = {
        'Data': dates,
        'Vendas': np.random.normal(1000, 200, 100),
        'Leads': np.random.poisson(50, 100),
        'Conversão': np.random.uniform(0.05, 0.15, 100)
    }
    return pd.DataFrame(data)

# Carregar dados
df = load_sample_data()

# Métricas principais
col1, col2, col3 = st.columns(3)

with col1:
    st.metric("Total de Vendas", f"R$ {df['Vendas'].sum():,.0f}", 
              delta=f"{df['Vendas'].tail(7).mean() - df['Vendas'].head(7).mean():.0f}")

with col2:
    st.metric("Total de Leads", f"{df['Leads'].sum():,}", 
              delta=f"{df['Leads'].tail(7).mean() - df['Leads'].head(7).mean():.0f}")

with col3:
    st.metric("Taxa de Conversão Média", f"{df['Conversão'].mean():.1%}", 
              delta=f"{df['Conversão'].tail(7).mean() - df['Conversão'].head(7).mean():.2%}")

# Gráficos
st.subheader("📈 Análise Temporal")

# Gráfico de vendas
fig_vendas = px.line(df, x='Data', y='Vendas', 
                     title='Evolução das Vendas',
                     labels={'Vendas': 'Vendas (R$)', 'Data': 'Data'})
st.plotly_chart(fig_vendas, use_container_width=True)

# Gráfico de correlação
st.subheader("🔗 Análise de Correlação")
correlation_data = df[['Vendas', 'Leads', 'Conversão']].corr()
fig_corr = px.imshow(correlation_data, 
                     title='Matriz de Correlação',
                     color_continuous_scale='RdBu_r')
st.plotly_chart(fig_corr, use_container_width=True)

# Análise com IA
st.subheader("🤖 Insights com IA")
st.info("💡 **Insight Automático**: Baseado nos dados, observo uma correlação positiva entre leads e vendas. Recomendo focar em estratégias de geração de leads para aumentar as vendas.")

# Dados brutos
if st.checkbox("Mostrar dados brutos"):
    st.subheader("📋 Dados")
    st.dataframe(df)
`);
  const [isRunning, setIsRunning] = useState(false);
  const [output, setOutput] = useState('');
  const [selectedExample, setSelectedExample] = useState<string>('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const codeExamples: CodeExample[] = [
    {
      id: 'dashboard',
      title: 'Dashboard Básico',
      description: 'Dashboard com métricas e gráficos básicos',
      category: 'visualization',
      code: `import streamlit as st
import pandas as pd
import plotly.express as px

st.title("📊 Dashboard Básico")

# Dados de exemplo
data = {
    'Mês': ['Jan', 'Fev', 'Mar', 'Abr', 'Mai'],
    'Vendas': [1000, 1200, 1100, 1300, 1500],
    'Leads': [50, 60, 55, 65, 75]
}
df = pd.DataFrame(data)

# Métricas
col1, col2 = st.columns(2)
with col1:
    st.metric("Total Vendas", f"R$ {df['Vendas'].sum():,}")
with col2:
    st.metric("Total Leads", df['Leads'].sum())

# Gráfico
fig = px.bar(df, x='Mês', y='Vendas', title='Vendas por Mês')
st.plotly_chart(fig)`
    },
    {
      id: 'analysis',
      title: 'Análise de Dados',
      description: 'Análise estatística e correlações',
      category: 'analysis',
      code: `import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px

st.title("📈 Análise de Dados")

# Gerar dados
np.random.seed(42)
df = pd.DataFrame({
    'x': np.random.randn(100),
    'y': np.random.randn(100),
    'categoria': np.random.choice(['A', 'B', 'C'], 100)
})

# Estatísticas
st.subheader("Estatísticas Descritivas")
st.write(df.describe())

# Correlação
st.subheader("Matriz de Correlação")
corr = df[['x', 'y']].corr()
fig = px.imshow(corr, text_auto=True)
st.plotly_chart(fig)`
    },
    {
      id: 'ml',
      title: 'Machine Learning',
      description: 'Modelo de predição simples',
      category: 'ml',
      code: `import streamlit as st
import pandas as pd
import numpy as np
from sklearn.linear_model import LinearRegression
import plotly.express as px

st.title("🤖 Machine Learning")

# Dados
np.random.seed(42)
X = np.random.randn(100, 1)
y = 2 * X.flatten() + np.random.randn(100) * 0.1

# Modelo
model = LinearRegression()
model.fit(X, y)

# Predições
X_pred = np.linspace(-3, 3, 100).reshape(-1, 1)
y_pred = model.predict(X_pred)

# Visualização
df = pd.DataFrame({'X': X.flatten(), 'y': y})
df_pred = pd.DataFrame({'X': X_pred.flatten(), 'y_pred': y_pred})

fig = px.scatter(df, x='X', y='y', title='Regressão Linear')
fig.add_scatter(x=df_pred['X'], y=df_pred['y_pred'], mode='lines', name='Predição')
st.plotly_chart(fig)

st.write(f"Coeficiente: {model.coef_[0]:.2f}")
st.write(f"Intercepto: {model.intercept_:.2f}")`
    }
  ];

  const handleRunCode = () => {
    setIsRunning(true);
    setOutput('🚀 Executando código...\n\n');
    
    // Simular execução
    setTimeout(() => {
      setOutput(prev => prev + '✅ Código executado com sucesso!\n');
      setOutput(prev => prev + '📊 Dashboard gerado e disponível na aba ao lado.\n');
      setOutput(prev => prev + '🔗 URL: http://localhost:8501\n\n');
      setOutput(prev => prev + '💡 Dica: Use st.cache_data para melhorar a performance!\n');
      setIsRunning(false);
    }, 2000);
  };

  const handleStopCode = () => {
    setIsRunning(false);
    setOutput(prev => prev + '\n⏹️ Execução interrompida pelo usuário.\n');
  };

  const handleLoadExample = (exampleId: string) => {
    const example = codeExamples.find(ex => ex.id === exampleId);
    if (example) {
      setCode(example.code);
      setSelectedExample(exampleId);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        setCode(content);
      };
      reader.readAsText(file);
    }
  };

  const handleDownloadCode = () => {
    const blob = new Blob([code], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'amigo_studio_app.py';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      {/* Hero Section */}
      <Card className="p-8">
        <div className="flex flex-col md:flex-row items-center">
          <div className="md:w-2/3">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">AmigoStudio</h1>
            <p className="text-lg text-gray-600 mb-6">
              Plataforma de análise de dados com IA. Crie dashboards interativos, análises avançadas e modelos de machine learning usando Python e Streamlit.
            </p>
            <div className="flex space-x-4">
              <Button variant="prominent" onClick={handleRunCode} disabled={isRunning}>
                {isRunning ? <StopIcon className="w-4 h-4 mr-2" /> : <PlayIcon className="w-4 h-4 mr-2" />}
                {isRunning ? 'Executando...' : 'Executar Código'}
              </Button>
              <Button variant="default" onClick={() => fileInputRef.current?.click()}>
                <CloudArrowUpIcon className="w-4 h-4 mr-2" />
                Carregar Arquivo
              </Button>
            </div>
          </div>
          <div className="md:w-1/3 mt-6 md:mt-0 flex justify-center">
            <div className="w-48 h-48 bg-gradient-to-r from-purple-100 to-blue-100 rounded-full flex items-center justify-center">
              <SparklesIcon className="w-24 h-24 text-purple-600" />
            </div>
          </div>
        </div>
      </Card>

      {/* Toolbar */}
      <Card className="p-4">
        <div className="flex flex-wrap items-center justify-between gap-4">
          <div className="flex items-center space-x-4">
            <select
              value={selectedExample}
              onChange={(e) => handleLoadExample(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Selecionar Exemplo</option>
              {codeExamples.map((example) => (
                <option key={example.id} value={example.id}>
                  {example.title}
                </option>
              ))}
            </select>
            
            <Button variant="borderless" size="sm" onClick={handleDownloadCode}>
              <ArrowDownTrayIcon className="w-4 h-4 mr-2" />
              Download
            </Button>
          </div>

          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">Status:</span>
            <div className="flex items-center">
              <div className={`w-2 h-2 rounded-full mr-2 ${isRunning ? 'bg-yellow-500' : 'bg-green-500'}`}></div>
              <span className="text-sm font-medium text-gray-900">
                {isRunning ? 'Executando' : 'Pronto'}
              </span>
            </div>
          </div>
        </div>
      </Card>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Code Editor */}
        <Card className="p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <CodeBracketIcon className="w-5 h-5 mr-2" />
              Editor de Código
            </h3>
            <div className="flex space-x-2">
              <Button variant="borderless" size="sm" onClick={handleRunCode} disabled={isRunning}>
                {isRunning ? <StopIcon className="w-4 h-4" /> : <PlayIcon className="w-4 h-4" />}
              </Button>
            </div>
          </div>
          <textarea
            value={code}
            onChange={(e) => setCode(e.target.value)}
            className="w-full h-96 p-4 border border-gray-300 rounded-lg font-mono text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
            placeholder="Digite seu código Python/Streamlit aqui..."
          />
        </Card>

        {/* Output */}
        <Card className="p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <DocumentTextIcon className="w-5 h-5 mr-2" />
              Saída
            </h3>
            <Button variant="borderless" size="sm" onClick={() => setOutput('')}>
              Limpar
            </Button>
          </div>
          <div className="w-full h-96 p-4 bg-gray-900 text-green-400 rounded-lg font-mono text-sm overflow-y-auto">
            <pre className="whitespace-pre-wrap">{output || 'Aguardando execução...'}</pre>
          </div>
        </Card>
      </div>

      {/* Examples */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <ChartBarIcon className="w-5 h-5 mr-2" />
          Exemplos de Código
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {codeExamples.map((example) => (
            <div
              key={example.id}
              className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                selectedExample === example.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
              }`}
              onClick={() => handleLoadExample(example.id)}
            >
              <h4 className="font-medium text-gray-900 mb-2">{example.title}</h4>
              <p className="text-sm text-gray-600 mb-3">{example.description}</p>
              <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                example.category === 'visualization' ? 'bg-blue-100 text-blue-800' :
                example.category === 'analysis' ? 'bg-green-100 text-green-800' :
                example.category === 'ml' ? 'bg-purple-100 text-purple-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {example.category}
              </span>
            </div>
          ))}
        </div>
      </Card>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept=".py,.txt"
        onChange={handleFileUpload}
        className="hidden"
      />
    </div>
  );
};

export default AmigoStudioIndex;

import React, { useState, useEffect } from 'react';
import { Card, KPICard } from '../../components/design-system';
import {
  ArrowTrendingUpIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  ComputerDesktopIcon,
  ArrowUpIcon
} from '@heroicons/react/24/outline';
import { PieChart, Pie, Cell, ResponsiveContainer, LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, BarChart, Bar } from 'recharts';

interface IndiceAmigoComponent {
  name: string;
  value: number;
  weight: number;
  weighted_value: number;
  color: string;
  description: string;
}

interface HistoricalData {
  month: string;
  indice: number;
  eo: number;
  pf: number;
  sc: number;
  at: number;
  cs: number;
}

export const CockpitKAMIndex: React.FC = () => {
  const [indiceAmigo, setIndiceAmigo] = useState(8.7);
  const [components, setComponents] = useState<IndiceAmigoComponent[]>([]);
  const [historicalData, setHistoricalData] = useState<HistoricalData[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadData = async () => {
      try {
        // Componentes do Índice Amigo
        const mockComponents: IndiceAmigoComponent[] = [
          {
            name: 'Eficiência Operacional',
            value: 9.2,
            weight: 0.25,
            weighted_value: 2.30,
            color: '#3B82F6',
            description: 'Taxa de ocupação e produtividade'
          },
          {
            name: 'Performance Financeira',
            value: 8.8,
            weight: 0.30,
            weighted_value: 2.64,
            color: '#10B981',
            description: 'ROI mensal e margem de lucro'
          },
          {
            name: 'Satisfação Cliente',
            value: 8.7,
            weight: 0.20,
            weighted_value: 1.74,
            color: '#F59E0B',
            description: 'NPS Score e retenção'
          },
          {
            name: 'Adoção Tecnológica',
            value: 8.5,
            weight: 0.15,
            weighted_value: 1.28,
            color: '#8B5CF6',
            description: 'Uso da plataforma digital'
          },
          {
            name: 'Crescimento Sustentável',
            value: 7.4,
            weight: 0.10,
            weighted_value: 0.74,
            color: '#6366F1',
            description: 'Taxa de crescimento mensal'
          }
        ];

        // Dados históricos
        const mockHistorical: HistoricalData[] = [
          { month: 'Jan', indice: 8.1, eo: 8.8, pf: 8.2, sc: 8.0, at: 7.9, cs: 7.1 },
          { month: 'Fev', indice: 8.3, eo: 9.0, pf: 8.4, sc: 8.2, at: 8.1, cs: 7.2 },
          { month: 'Mar', indice: 8.4, eo: 9.1, pf: 8.5, sc: 8.3, at: 8.2, cs: 7.3 },
          { month: 'Abr', indice: 8.6, eo: 9.2, pf: 8.7, sc: 8.5, at: 8.3, cs: 7.3 },
          { month: 'Mai', indice: 8.7, eo: 9.2, pf: 8.8, sc: 8.7, at: 8.5, cs: 7.4 }
        ];

        setComponents(mockComponents);
        setHistoricalData(mockHistorical);
        
        // Calcular índice total
        const total = mockComponents.reduce((sum, comp) => sum + comp.weighted_value, 0);
        setIndiceAmigo(total);
      } catch (error) {
        console.error('Erro ao carregar dados do Cockpit KAM:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  const getPerformanceLevel = (value: number) => {
    if (value >= 9.0) return { level: 'Excelente', color: 'text-green-600' };
    if (value >= 8.0) return { level: 'Muito Bom', color: 'text-blue-600' };
    if (value >= 7.0) return { level: 'Bom', color: 'text-yellow-600' };
    if (value >= 6.0) return { level: 'Regular', color: 'text-orange-600' };
    return { level: 'Crítico', color: 'text-red-600' };
  };

  const performance = getPerformanceLevel(indiceAmigo);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <Card className="p-8 bg-gradient-to-br from-blue-50 via-gray-100 to-gray-200">
        <div className="flex flex-col lg:flex-row items-center justify-between">
          <div className="lg:w-2/3">
            <h1 className="text-4xl font-bold text-gray-900 mb-2">Cockpit KAM</h1>
            <h2 className="text-2xl font-semibold text-blue-700 mb-1">Índice Amigo</h2>
            <p className="text-lg text-gray-600 mb-6">
              Indicador Econométrico de Performance Clínica
            </p>
            <div className="flex items-center space-x-4">
              <div className="text-6xl font-bold text-blue-600">
                {indiceAmigo.toFixed(1)}
              </div>
              <div>
                <div className="text-sm text-gray-500">de 10.0</div>
                <div className="flex items-center mt-2">
                  <ArrowUpIcon className="w-4 h-4 text-green-600 mr-1" />
                  <span className="text-green-600 text-sm font-medium">+0.3 vs mês anterior</span>
                </div>
                <div className={`text-sm font-medium ${performance.color}`}>
                  {performance.level}
                </div>
              </div>
            </div>
          </div>
          <div className="lg:w-1/3 mt-6 lg:mt-0">
            <div className="bg-white/80 backdrop-blur-sm rounded-lg p-4 border border-gray-200">
              <h3 className="font-semibold mb-3 text-gray-800">Fórmula do Índice Amigo</h3>
              <div className="text-sm text-gray-700 space-y-2">
                <div className="bg-blue-50 p-2 rounded text-xs font-mono text-center">
                  IA = Σ(Wi × Ni) / 10
                </div>
                <div className="space-y-1 text-xs">
                  <div>• <strong>EO</strong>: Eficiência Operacional (W=0.25)</div>
                  <div>• <strong>PF</strong>: Performance Financeira (W=0.30)</div>
                  <div>• <strong>SC</strong>: Satisfação Cliente (W=0.20)</div>
                  <div>• <strong>AT</strong>: Adoção Tecnológica (W=0.15)</div>
                  <div>• <strong>CS</strong>: Crescimento Sustentável (W=0.10)</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* KPIs dos Componentes */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        {components.map((component, index) => (
          <KPICard
            key={index}
            title={component.name}
            value={component.value.toFixed(1)}
            delta={{
              value: `${(component.weighted_value).toFixed(2)} pts`,
              type: component.value >= 8 ? "positive" : component.value >= 7 ? "neutral" : "negative",
              label: `Peso: ${(component.weight * 100)}%`
            }}
            insight={component.description}
          />
        ))}
      </div>

      {/* Gráficos */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Composição do Índice */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Composição do Índice Amigo</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={components}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, weighted_value }) => `${name}: ${weighted_value.toFixed(2)}`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="weighted_value"
                >
                  {components.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip formatter={(value: number) => [`${value.toFixed(2)} pts`, 'Contribuição']} />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </Card>

        {/* Evolução Histórica */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Evolução do Índice Amigo</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={historicalData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis domain={[7, 10]} />
                <Tooltip />
                <Line 
                  type="monotone" 
                  dataKey="indice" 
                  stroke="#3B82F6" 
                  strokeWidth={3}
                  name="Índice Amigo"
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </Card>
      </div>

      {/* Detalhamento dos Componentes */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance por Componente</h3>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={historicalData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis domain={[6, 10]} />
              <Tooltip />
              <Bar dataKey="eo" fill="#3B82F6" name="Eficiência Operacional" />
              <Bar dataKey="pf" fill="#10B981" name="Performance Financeira" />
              <Bar dataKey="sc" fill="#F59E0B" name="Satisfação Cliente" />
              <Bar dataKey="at" fill="#8B5CF6" name="Adoção Tecnológica" />
              <Bar dataKey="cs" fill="#6366F1" name="Crescimento Sustentável" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </Card>

      {/* Benchmark e Análise */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Benchmark de Mercado</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Média do Setor:</span>
              <span className="font-medium text-gray-800">6.8</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Top 10%:</span>
              <span className="font-medium text-green-600">9.2+</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Sua Posição:</span>
              <span className="font-medium text-blue-600">{indiceAmigo.toFixed(1)}</span>
            </div>
            <div className="pt-2 border-t">
              <div className="text-sm text-gray-600">
                Você está no <strong>top 15%</strong> do mercado
              </div>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Oportunidades de Melhoria</h3>
          <div className="space-y-3">
            <div className="flex items-start">
              <div className="w-2 h-2 bg-red-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
              <div>
                <p className="text-sm font-medium text-gray-900">Crescimento Sustentável</p>
                <p className="text-xs text-gray-600">Foco em estratégias de crescimento de longo prazo</p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
              <div>
                <p className="text-sm font-medium text-gray-900">Adoção Tecnológica</p>
                <p className="text-xs text-gray-600">Aumentar engajamento com ferramentas digitais</p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
              <div>
                <p className="text-sm font-medium text-gray-900">Eficiência Operacional</p>
                <p className="text-xs text-gray-600">Manter o excelente desempenho atual</p>
              </div>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Próximas Ações</h3>
          <div className="space-y-3">
            <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
              <p className="text-sm font-medium text-blue-900">Revisar Estratégia de Crescimento</p>
              <p className="text-xs text-blue-700 mt-1">Analisar oportunidades de expansão</p>
            </div>
            <div className="p-3 bg-purple-50 rounded-lg border border-purple-200">
              <p className="text-sm font-medium text-purple-900">Treinamento Digital</p>
              <p className="text-xs text-purple-700 mt-1">Capacitar equipe em novas tecnologias</p>
            </div>
            <div className="p-3 bg-green-50 rounded-lg border border-green-200">
              <p className="text-sm font-medium text-green-900">Manter Performance</p>
              <p className="text-xs text-green-700 mt-1">Continuar práticas de excelência</p>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default CockpitKAMIndex;

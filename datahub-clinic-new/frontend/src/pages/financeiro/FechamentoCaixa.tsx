import React, { useState, useEffect } from 'react';
import { Card, Button, Input, Select } from '../../components/design-system';
import { CalendarIcon, ChartBarIcon, CheckCircleIcon } from '@heroicons/react/24/outline';

interface FechamentoCaixa {
  data: string;
  unidade: string;
  responsavel: string;
  valor_inicial: number;
  entradas_dinheiro: number;
  entradas_cartao: number;
  entradas_pix: number;
  saidas: number;
  valor_final: number;
  diferenca: number;
  status: string;
  observacoes?: string;
}

export const FechamentoCaixa: React.FC = () => {
  const [fechamentos, setFechamentos] = useState<FechamentoCaixa[]>([]);
  const [loading, setLoading] = useState(true);
  const [dataFilter, setDataFilter] = useState('');
  const [unidadeFilter, setUnidadeFilter] = useState('');

  useEffect(() => {
    const loadFechamentos = async () => {
      try {
        const mockFechamentos: FechamentoCaixa[] = Array.from({ length: 30 }, (_, i) => {
          const valorInicial = Math.round((Math.random() * 2000 + 500) * 100) / 100;
          const entradasDinheiro = Math.round((Math.random() * 3000 + 1000) * 100) / 100;
          const entradasCartao = Math.round((Math.random() * 5000 + 2000) * 100) / 100;
          const entradasPix = Math.round((Math.random() * 2000 + 500) * 100) / 100;
          const saidas = Math.round((Math.random() * 800 + 200) * 100) / 100;
          const valorFinal = valorInicial + entradasDinheiro + entradasCartao + entradasPix - saidas;
          const diferenca = Math.round((Math.random() * 200 - 100) * 100) / 100;

          return {
            data: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toLocaleDateString('pt-BR'),
            unidade: ['Morumbi', 'BRASILIA', 'Recife', 'Espinheiro', 'CLÍNICA (RL)'][Math.floor(Math.random() * 5)],
            responsavel: [
              'Maria Silva',
              'João Santos',
              'Ana Costa',
              'Pedro Lima',
              'Carla Oliveira',
              'Roberto Almeida'
            ][Math.floor(Math.random() * 6)],
            valor_inicial: valorInicial,
            entradas_dinheiro: entradasDinheiro,
            entradas_cartao: entradasCartao,
            entradas_pix: entradasPix,
            saidas,
            valor_final: valorFinal,
            diferenca,
            status: Math.abs(diferenca) <= 10 ? 'Fechado' : diferenca > 10 ? 'Sobra' : 'Falta',
            observacoes: Math.abs(diferenca) > 10 ? 'Verificar divergência no caixa' : undefined
          };
        });

        setFechamentos(mockFechamentos);
      } catch (error) {
        console.error('Erro ao carregar fechamentos de caixa:', error);
      } finally {
        setLoading(false);
      }
    };

    loadFechamentos();
  }, []);

  const filteredFechamentos = fechamentos.filter(fechamento => {
    if (dataFilter && !fechamento.data.includes(dataFilter)) return false;
    if (unidadeFilter && fechamento.unidade !== unidadeFilter) return false;
    return true;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Fechado':
        return 'bg-green-100 text-green-800';
      case 'Sobra':
        return 'bg-blue-100 text-blue-800';
      case 'Falta':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const totalEntradas = filteredFechamentos.reduce((sum, f) => 
    sum + f.entradas_dinheiro + f.entradas_cartao + f.entradas_pix, 0);
  const totalSaidas = filteredFechamentos.reduce((sum, f) => sum + f.saidas, 0);
  const totalDiferenca = filteredFechamentos.reduce((sum, f) => sum + f.diferenca, 0);
  const fechamentosOk = filteredFechamentos.filter(f => f.status === 'Fechado').length;

  const unidades = [...new Set(fechamentos.map(f => f.unidade))];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Fechamento de Caixa</h1>
          <p className="text-sm text-gray-600 mt-1">Controle diário de caixa por unidade</p>
        </div>
        <Button variant="prominent">
          <CalendarIcon className="w-4 h-4 mr-2" />
          Novo Fechamento
        </Button>
      </div>

      <Card className="p-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Input
            type="date"
            label="Data"
            value={dataFilter}
            onChange={(e) => setDataFilter(e.target.value)}
          />
          <Select
            label="Unidade"
            options={[
              { value: '', label: 'Todas as unidades' },
              ...unidades.map(unidade => ({ value: unidade, label: unidade }))
            ]}
            value={unidadeFilter}
            onChange={(e) => setUnidadeFilter(e.target.value)}
          />
          <div className="flex items-end">
            <Button variant="default" className="w-full">
              <ChartBarIcon className="w-4 h-4 mr-2" />
              Relatório
            </Button>
          </div>
        </div>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total de Entradas</p>
              <p className="text-2xl font-bold text-gray-900">
                R$ {totalEntradas.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total de Saídas</p>
              <p className="text-2xl font-bold text-gray-900">
                R$ {totalSaidas.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ChartBarIcon className={`w-8 h-8 ${totalDiferenca >= 0 ? 'text-blue-600' : 'text-red-600'}`} />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Diferença Total</p>
              <p className={`text-2xl font-bold ${totalDiferenca >= 0 ? 'text-blue-600' : 'text-red-600'}`}>
                R$ {totalDiferenca.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CheckCircleIcon className="w-8 h-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Fechamentos OK</p>
              <p className="text-2xl font-bold text-gray-900">
                {fechamentosOk}/{filteredFechamentos.length}
              </p>
            </div>
          </div>
        </Card>
      </div>

      <Card className="overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Data
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Unidade
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Responsável
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Valor Inicial
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Entradas
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Saídas
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Valor Final
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Diferença
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ações
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredFechamentos.map((fechamento, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {fechamento.data}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {fechamento.unidade}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {fechamento.responsavel}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    R$ {fechamento.valor_inicial.toFixed(2)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="space-y-1">
                      <div className="text-xs text-gray-500">
                        Dinheiro: R$ {fechamento.entradas_dinheiro.toFixed(2)}
                      </div>
                      <div className="text-xs text-gray-500">
                        Cartão: R$ {fechamento.entradas_cartao.toFixed(2)}
                      </div>
                      <div className="text-xs text-gray-500">
                        Pix: R$ {fechamento.entradas_pix.toFixed(2)}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    R$ {fechamento.saidas.toFixed(2)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    R$ {fechamento.valor_final.toFixed(2)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <span className={`font-medium ${fechamento.diferenca >= 0 ? 'text-blue-600' : 'text-red-600'}`}>
                      R$ {fechamento.diferenca.toFixed(2)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(fechamento.status)}`}>
                      {fechamento.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    <Button variant="borderless" size="sm">
                      Ver
                    </Button>
                    <Button variant="borderless" size="sm">
                      Editar
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  );
};

export default FechamentoCaixa;

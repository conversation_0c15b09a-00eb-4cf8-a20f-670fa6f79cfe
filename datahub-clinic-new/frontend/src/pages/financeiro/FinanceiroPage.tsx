import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { 
  FinanceiroIndex, 
  ContasReceber, 
  ContasPagar, 
  FluxoCaixa, 
  FechamentoCaixa 
} from './index';

export const FinanceiroPage: React.FC = () => {
  return (
    <Routes>
      <Route index element={<FinanceiroIndex />} />
      <Route path="contas-receber" element={<ContasReceber />} />
      <Route path="contas-pagar" element={<ContasPagar />} />
      <Route path="fluxo-caixa" element={<FluxoCaixa />} />
      <Route path="fechamento-caixa" element={<FechamentoCaixa />} />
    </Routes>
  );
};

export default FinanceiroPage;

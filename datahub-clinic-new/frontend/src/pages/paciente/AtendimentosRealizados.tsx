import React, { useState, useEffect } from 'react';
import { Card, Button, Input, Select } from '../../components/design-system';
import { MagnifyingGlassIcon, CalendarDaysIcon, FunnelIcon } from '@heroicons/react/24/outline';

interface AtendimentoRealizado {
  codigo: number;
  data: string;
  hora: string;
  paciente: string;
  unidade: string;
  profissional: string;
  tipo_atendimento: string;
  procedimento: string;
  forma_pagamento: string;
  valor: number;
  status: string;
  observacoes?: string;
}

export const AtendimentosRealizados: React.FC = () => {
  const [atendimentos, setAtendimentos] = useState<AtendimentoRealizado[]>([]);
  const [filteredAtendimentos, setFilteredAtendimentos] = useState<AtendimentoRealizado[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [unidadeFilter, setUnidadeFilter] = useState('');
  const [profissionalFilter, setProfissionalFilter] = useState('');
  const [dataInicioFilter, setDataInicioFilter] = useState('');
  const [dataFimFilter, setDataFimFilter] = useState('');

  useEffect(() => {
    const loadAtendimentos = async () => {
      try {
        const mockAtendimentos: AtendimentoRealizado[] = Array.from({ length: 245 }, (_, i) => ({
          codigo: 150000000 + i,
          data: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000).toLocaleDateString('pt-BR'),
          hora: `${Math.floor(Math.random() * 10) + 8}:${Math.random() > 0.5 ? '00' : '30'}`,
          paciente: [
            'AMANDA SILVA',
            'Daniel Teste',
            'Stephany Figueiredo',
            'MARINA HAZIN',
            'Claudio Lemos',
            'FABIO FELTRIM',
            'Ana Carolina Santos',
            'Pedro Henrique Lima',
            'Juliana Costa',
            'Roberto Almeida',
            'Carla Oliveira',
            'Fernando Santos',
            'Lucia Pereira',
            'Marcos Antonio',
            'Patricia Lima'
          ][Math.floor(Math.random() * 15)],
          unidade: ['Morumbi', 'BRASILIA', 'Recife', 'Espinheiro', 'CLÍNICA (RL)'][Math.floor(Math.random() * 5)],
          profissional: [
            'Dr. Bruno Lima',
            'Dr. Caio Menezes',
            'Dra. Giulia Pedrosa',
            'Dra. Ana Victoria Rocha',
            'Dra. Rayara Toledo Souza',
            'Dr. José Pedroza',
            'Dra. Maria Santos',
            'Dr. Carlos Silva',
            'Dra. Fernanda Costa'
          ][Math.floor(Math.random() * 9)],
          tipo_atendimento: [
            'Consulta Médica',
            'BOTOX',
            'Fisioterapia',
            'Procedimento Estético',
            'Terapia Ocupacional',
            'Psicologia ABA',
            'Exame Laboratorial',
            'Ultrassom',
            'Consulta Nutricional',
            'Avaliação Dermatológica'
          ][Math.floor(Math.random() * 10)],
          procedimento: [
            'Consulta de Rotina',
            'Aplicação de BOTOX',
            'Sessão de Fisioterapia',
            'Preenchimento Facial',
            'Terapia Ocupacional',
            'Sessão de Psicologia',
            'Exames de Sangue',
            'Ultrassom Abdominal',
            'Consulta Nutricional',
            'Avaliação Dermatológica',
            'Limpeza de Pele',
            'Massagem Terapêutica'
          ][Math.floor(Math.random() * 12)],
          forma_pagamento: [
            'AMIL',
            'Particular',
            'BRADESCO',
            'Cartão de Crédito',
            'Pix',
            'UNIMED',
            'Dinheiro',
            'Transferência',
            'Crédito de Procedimento',
            'Convênio Empresarial'
          ][Math.floor(Math.random() * 10)],
          valor: Math.round((Math.random() * 1950 + 50) * 100) / 100,
          status: 'Realizado',
          observacoes: Math.random() > 0.7 ? 'Paciente retornará em 30 dias' : undefined
        }));

        setAtendimentos(mockAtendimentos);
        setFilteredAtendimentos(mockAtendimentos);
      } catch (error) {
        console.error('Erro ao carregar atendimentos realizados:', error);
      } finally {
        setLoading(false);
      }
    };

    loadAtendimentos();
  }, []);

  useEffect(() => {
    let filtered = atendimentos;

    if (searchTerm) {
      filtered = filtered.filter(atendimento =>
        atendimento.paciente.toLowerCase().includes(searchTerm.toLowerCase()) ||
        atendimento.profissional.toLowerCase().includes(searchTerm.toLowerCase()) ||
        atendimento.procedimento.toLowerCase().includes(searchTerm.toLowerCase()) ||
        atendimento.codigo.toString().includes(searchTerm)
      );
    }

    if (unidadeFilter) {
      filtered = filtered.filter(atendimento => atendimento.unidade === unidadeFilter);
    }

    if (profissionalFilter) {
      filtered = filtered.filter(atendimento => atendimento.profissional === profissionalFilter);
    }

    if (dataInicioFilter) {
      filtered = filtered.filter(atendimento => {
        const dataAtendimento = new Date(atendimento.data.split('/').reverse().join('-'));
        const dataInicio = new Date(dataInicioFilter);
        return dataAtendimento >= dataInicio;
      });
    }

    if (dataFimFilter) {
      filtered = filtered.filter(atendimento => {
        const dataAtendimento = new Date(atendimento.data.split('/').reverse().join('-'));
        const dataFim = new Date(dataFimFilter);
        return dataAtendimento <= dataFim;
      });
    }

    setFilteredAtendimentos(filtered);
  }, [searchTerm, unidadeFilter, profissionalFilter, dataInicioFilter, dataFimFilter, atendimentos]);

  // Calcular estatísticas
  const totalAtendimentos = filteredAtendimentos.length;
  const valorTotal = filteredAtendimentos.reduce((sum, atendimento) => sum + atendimento.valor, 0);
  const ticketMedio = totalAtendimentos > 0 ? valorTotal / totalAtendimentos : 0;
  
  const atendimentosPorUnidade = filteredAtendimentos.reduce((acc, atendimento) => {
    acc[atendimento.unidade] = (acc[atendimento.unidade] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const unidades = [...new Set(atendimentos.map(a => a.unidade))];
  const profissionais = [...new Set(atendimentos.map(a => a.profissional))];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Atendimentos Realizados</h1>
          <p className="text-sm text-gray-600 mt-1">
            Histórico completo de atendimentos realizados
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <Button variant="prominent">
            <CalendarDaysIcon className="w-4 h-4 mr-2" />
            Exportar Relatório
          </Button>
        </div>
      </div>

      {/* Filtros */}
      <Card className="p-4">
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <div className="relative lg:col-span-2">
            <MagnifyingGlassIcon className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Buscar paciente, profissional ou procedimento..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <Select
            options={[
              { value: '', label: 'Todas as unidades' },
              ...unidades.map(unidade => ({ value: unidade, label: unidade }))
            ]}
            value={unidadeFilter}
            onChange={(e) => setUnidadeFilter(e.target.value)}
          />
          
          <Select
            options={[
              { value: '', label: 'Todos os profissionais' },
              ...profissionais.map(prof => ({ value: prof, label: prof }))
            ]}
            value={profissionalFilter}
            onChange={(e) => setProfissionalFilter(e.target.value)}
          />
          
          <Input
            type="date"
            placeholder="Data início"
            value={dataInicioFilter}
            onChange={(e) => setDataInicioFilter(e.target.value)}
          />
          
          <Input
            type="date"
            placeholder="Data fim"
            value={dataFimFilter}
            onChange={(e) => setDataFimFilter(e.target.value)}
          />
        </div>
      </Card>

      {/* Estatísticas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-gray-900">{totalAtendimentos}</p>
            <p className="text-sm text-gray-600">Total de Atendimentos</p>
          </div>
        </Card>
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-green-600">
              R$ {valorTotal.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
            </p>
            <p className="text-sm text-gray-600">Valor Total</p>
          </div>
        </Card>
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-blue-600">
              R$ {ticketMedio.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
            </p>
            <p className="text-sm text-gray-600">Ticket Médio</p>
          </div>
        </Card>
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-purple-600">
              {Object.keys(atendimentosPorUnidade).length}
            </p>
            <p className="text-sm text-gray-600">Unidades Ativas</p>
          </div>
        </Card>
      </div>

      {/* Tabela de Atendimentos */}
      <Card className="overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Código
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Data/Hora
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Paciente
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Profissional
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Procedimento
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Forma de Pagamento
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Valor
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ações
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredAtendimentos.slice(0, 50).map((atendimento) => (
                <tr key={atendimento.codigo} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {atendimento.codigo}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>
                      <div className="font-medium">{atendimento.data}</div>
                      <div className="text-gray-500">{atendimento.hora}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>
                      <div className="font-medium">{atendimento.paciente}</div>
                      <div className="text-gray-500">{atendimento.unidade}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {atendimento.profissional}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>
                      <div className="font-medium">{atendimento.tipo_atendimento}</div>
                      <div className="text-gray-500">{atendimento.procedimento}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {atendimento.forma_pagamento}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    R$ {atendimento.valor.toFixed(2)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    <Button variant="borderless" size="sm">
                      Ver
                    </Button>
                    <Button variant="borderless" size="sm">
                      Imprimir
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        {filteredAtendimentos.length > 50 && (
          <div className="px-6 py-3 bg-gray-50 border-t border-gray-200">
            <p className="text-sm text-gray-600">
              Mostrando 50 de {filteredAtendimentos.length} atendimentos. Use os filtros para refinar a busca.
            </p>
          </div>
        )}
      </Card>
    </div>
  );
};

export default AtendimentosRealizados;

import React, { useState, useEffect } from 'react';
import { Card, Button, Input, Select } from '../../components/design-system';
import { MagnifyingGlassIcon, CurrencyDollarIcon } from '@heroicons/react/24/outline';

interface CreditoDisponivel {
  codigo: number;
  paciente: string;
  unidade: string;
  valor_original: number;
  valor_disponivel: number;
  valor_utilizado: number;
  data_criacao: string;
  data_vencimento: string;
  origem: string;
  status: string;
  observacoes?: string;
}

export const CreditosDisponiveis: React.FC = () => {
  const [creditos, setCreditos] = useState<CreditoDisponivel[]>([]);
  const [filteredCreditos, setFilteredCreditos] = useState<CreditoDisponivel[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [origemFilter, setOrigemFilter] = useState('');

  useEffect(() => {
    const loadCreditos = async () => {
      try {
        const mockCreditos: CreditoDisponivel[] = Array.from({ length: 35 }, (_, i) => {
          const valorOriginal = Math.round((Math.random() * 2000 + 100) * 100) / 100;
          const valorUtilizado = Math.round((Math.random() * valorOriginal * 0.5) * 100) / 100;
          const valorDisponivel = valorOriginal - valorUtilizado;
          
          return {
            codigo: 300000000 + i,
            paciente: [
              'AMANDA SILVA',
              'Daniel Teste',
              'Stephany Figueiredo',
              'MARINA HAZIN',
              'Claudio Lemos',
              'FABIO FELTRIM',
              'Ana Carolina Santos',
              'Pedro Henrique Lima',
              'Juliana Costa',
              'Roberto Almeida'
            ][Math.floor(Math.random() * 10)],
            unidade: ['Morumbi', 'BRASILIA', 'Recife', 'Espinheiro', 'CLÍNICA (RL)'][Math.floor(Math.random() * 5)],
            valor_original: valorOriginal,
            valor_disponivel: valorDisponivel,
            valor_utilizado: valorUtilizado,
            data_criacao: new Date(Date.now() - Math.random() * 180 * 24 * 60 * 60 * 1000).toLocaleDateString('pt-BR'),
            data_vencimento: new Date(Date.now() + Math.random() * 365 * 24 * 60 * 60 * 1000).toLocaleDateString('pt-BR'),
            origem: [
              'Devolução de Procedimento',
              'Cortesia',
              'Promoção',
              'Cancelamento',
              'Desconto Especial',
              'Programa de Fidelidade'
            ][Math.floor(Math.random() * 6)],
            status: valorDisponivel > 0 ? 'Ativo' : 'Utilizado',
            observacoes: Math.random() > 0.8 ? 'Crédito com vencimento próximo' : undefined
          };
        });

        setCreditos(mockCreditos);
        setFilteredCreditos(mockCreditos);
      } catch (error) {
        console.error('Erro ao carregar créditos disponíveis:', error);
      } finally {
        setLoading(false);
      }
    };

    loadCreditos();
  }, []);

  useEffect(() => {
    let filtered = creditos;

    if (searchTerm) {
      filtered = filtered.filter(credito =>
        credito.paciente.toLowerCase().includes(searchTerm.toLowerCase()) ||
        credito.codigo.toString().includes(searchTerm)
      );
    }

    if (statusFilter) {
      filtered = filtered.filter(credito => credito.status === statusFilter);
    }

    if (origemFilter) {
      filtered = filtered.filter(credito => credito.origem === origemFilter);
    }

    setFilteredCreditos(filtered);
  }, [searchTerm, statusFilter, origemFilter, creditos]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Ativo':
        return 'bg-green-100 text-green-800';
      case 'Utilizado':
        return 'bg-gray-100 text-gray-800';
      case 'Vencido':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const totalCreditos = filteredCreditos.reduce((sum, credito) => sum + credito.valor_disponivel, 0);
  const creditosAtivos = filteredCreditos.filter(c => c.status === 'Ativo').length;
  const creditosUtilizados = filteredCreditos.filter(c => c.status === 'Utilizado').length;
  const maiorCredito = Math.max(...filteredCreditos.map(c => c.valor_disponivel));

  const statusOptions = [...new Set(creditos.map(c => c.status))];
  const origemOptions = [...new Set(creditos.map(c => c.origem))];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Créditos Disponíveis</h1>
          <p className="text-sm text-gray-600 mt-1">Gestão de créditos de pacientes</p>
        </div>
        <Button variant="prominent">
          <CurrencyDollarIcon className="w-4 h-4 mr-2" />
          Novo Crédito
        </Button>
      </div>

      <Card className="p-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <MagnifyingGlassIcon className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Buscar paciente ou código..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select
            options={[
              { value: '', label: 'Todos os status' },
              ...statusOptions.map(status => ({ value: status, label: status }))
            ]}
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          />
          <Select
            options={[
              { value: '', label: 'Todas as origens' },
              ...origemOptions.map(origem => ({ value: origem, label: origem }))
            ]}
            value={origemFilter}
            onChange={(e) => setOrigemFilter(e.target.value)}
          />
          <Button variant="default">Exportar</Button>
        </div>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-green-600">
              R$ {totalCreditos.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
            </p>
            <p className="text-sm text-gray-600">Total Disponível</p>
          </div>
        </Card>
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-blue-600">{creditosAtivos}</p>
            <p className="text-sm text-gray-600">Créditos Ativos</p>
          </div>
        </Card>
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-gray-600">{creditosUtilizados}</p>
            <p className="text-sm text-gray-600">Créditos Utilizados</p>
          </div>
        </Card>
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-purple-600">
              R$ {maiorCredito.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
            </p>
            <p className="text-sm text-gray-600">Maior Crédito</p>
          </div>
        </Card>
      </div>

      <Card className="overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Código
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Paciente
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Valor Original
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Valor Disponível
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Origem
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Vencimento
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ações
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredCreditos.map((credito) => (
                <tr key={credito.codigo} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {credito.codigo}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>
                      <div className="font-medium">{credito.paciente}</div>
                      <div className="text-gray-500">{credito.unidade}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    R$ {credito.valor_original.toFixed(2)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>
                      <div className="font-medium text-green-600">
                        R$ {credito.valor_disponivel.toFixed(2)}
                      </div>
                      {credito.valor_utilizado > 0 && (
                        <div className="text-xs text-gray-500">
                          Utilizado: R$ {credito.valor_utilizado.toFixed(2)}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {credito.origem}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>
                      <div className="font-medium">{credito.data_vencimento}</div>
                      <div className="text-xs text-gray-500">
                        Criado: {credito.data_criacao}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(credito.status)}`}>
                      {credito.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    <Button variant="borderless" size="sm">
                      Utilizar
                    </Button>
                    <Button variant="borderless" size="sm">
                      Editar
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  );
};

export default CreditosDisponiveis;

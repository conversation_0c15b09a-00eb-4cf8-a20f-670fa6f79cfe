import React, { useState, useEffect } from 'react';
import { Card, Button, Input, Select } from '../../components/design-system';
import { MagnifyingGlassIcon, DocumentTextIcon } from '@heroicons/react/24/outline';

interface OrcamentoAberto {
  codigo: number;
  paciente: string;
  unidade: string;
  profissional: string;
  data_criacao: string;
  data_validade: string;
  procedimentos: string[];
  valor_total: number;
  desconto: number;
  valor_final: number;
  status: string;
  dias_pendente: number;
  observacoes?: string;
}

export const OrcamentosAbertos: React.FC = () => {
  const [orcamentos, setOrcamentos] = useState<OrcamentoAberto[]>([]);
  const [filteredOrcamentos, setFilteredOrcamentos] = useState<OrcamentoAberto[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [unidadeFilter, setUnidadeFilter] = useState('');

  useEffect(() => {
    const loadOrcamentos = async () => {
      try {
        const mockOrcamentos: OrcamentoAberto[] = Array.from({ length: 68 }, (_, i) => {
          const valorTotal = Math.round((Math.random() * 8000 + 500) * 100) / 100;
          const desconto = Math.round((Math.random() * valorTotal * 0.2) * 100) / 100;
          const valorFinal = valorTotal - desconto;
          const dataCriacao = new Date(Date.now() - Math.random() * 45 * 24 * 60 * 60 * 1000);
          const diasPendente = Math.floor((Date.now() - dataCriacao.getTime()) / (24 * 60 * 60 * 1000));

          return {
            codigo: 400000000 + i,
            paciente: [
              'AMANDA SILVA',
              'Daniel Teste',
              'Stephany Figueiredo',
              'MARINA HAZIN',
              'Claudio Lemos',
              'FABIO FELTRIM',
              'Ana Carolina Santos',
              'Pedro Henrique Lima',
              'Juliana Costa',
              'Roberto Almeida'
            ][Math.floor(Math.random() * 10)],
            unidade: ['Morumbi', 'BRASILIA', 'Recife', 'Espinheiro', 'CLÍNICA (RL)'][Math.floor(Math.random() * 5)],
            profissional: [
              'Dr. Bruno Lima',
              'Dr. Caio Menezes',
              'Dra. Giulia Pedrosa',
              'Dra. Ana Victoria Rocha',
              'Dr. José Pedroza'
            ][Math.floor(Math.random() * 5)],
            data_criacao: dataCriacao.toLocaleDateString('pt-BR'),
            data_validade: new Date(dataCriacao.getTime() + 30 * 24 * 60 * 60 * 1000).toLocaleDateString('pt-BR'),
            procedimentos: [
              ['BOTOX Facial', 'Preenchimento Labial'],
              ['Consulta Dermatológica', 'Limpeza de Pele'],
              ['Fisioterapia - 10 sessões'],
              ['Cirurgia Plástica', 'Pós-operatório'],
              ['Tratamento Capilar', 'Consulta Tricológica'],
              ['Psicologia - 8 sessões'],
              ['Nutrição - Consulta + Plano'],
              ['Exames Laboratoriais Completos']
            ][Math.floor(Math.random() * 8)],
            valor_total: valorTotal,
            desconto,
            valor_final: valorFinal,
            status: [
              'Aguardando Aprovação',
              'Em Análise',
              'Aguardando Documentos',
              'Pendente de Pagamento'
            ][Math.floor(Math.random() * 4)],
            dias_pendente: diasPendente,
            observacoes: diasPendente > 20 ? 'Orçamento com prazo de validade próximo' : undefined
          };
        });

        setOrcamentos(mockOrcamentos);
        setFilteredOrcamentos(mockOrcamentos);
      } catch (error) {
        console.error('Erro ao carregar orçamentos abertos:', error);
      } finally {
        setLoading(false);
      }
    };

    loadOrcamentos();
  }, []);

  useEffect(() => {
    let filtered = orcamentos;

    if (searchTerm) {
      filtered = filtered.filter(orcamento =>
        orcamento.paciente.toLowerCase().includes(searchTerm.toLowerCase()) ||
        orcamento.profissional.toLowerCase().includes(searchTerm.toLowerCase()) ||
        orcamento.codigo.toString().includes(searchTerm)
      );
    }

    if (statusFilter) {
      filtered = filtered.filter(orcamento => orcamento.status === statusFilter);
    }

    if (unidadeFilter) {
      filtered = filtered.filter(orcamento => orcamento.unidade === unidadeFilter);
    }

    setFilteredOrcamentos(filtered);
  }, [searchTerm, statusFilter, unidadeFilter, orcamentos]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Aguardando Aprovação':
        return 'bg-yellow-100 text-yellow-800';
      case 'Em Análise':
        return 'bg-blue-100 text-blue-800';
      case 'Aguardando Documentos':
        return 'bg-orange-100 text-orange-800';
      case 'Pendente de Pagamento':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPrioridadeColor = (diasPendente: number) => {
    if (diasPendente > 25) return 'bg-red-500';
    if (diasPendente > 15) return 'bg-orange-500';
    return 'bg-green-500';
  };

  const valorTotal = filteredOrcamentos.reduce((sum, orc) => sum + orc.valor_final, 0);
  const orcamentosUrgentes = filteredOrcamentos.filter(o => o.dias_pendente > 20).length;
  const ticketMedio = filteredOrcamentos.length > 0 ? valorTotal / filteredOrcamentos.length : 0;

  const statusOptions = [...new Set(orcamentos.map(o => o.status))];
  const unidades = [...new Set(orcamentos.map(o => o.unidade))];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Orçamentos Abertos</h1>
          <p className="text-sm text-gray-600 mt-1">Orçamentos aguardando aprovação</p>
        </div>
        <Button variant="prominent">
          <DocumentTextIcon className="w-4 h-4 mr-2" />
          Novo Orçamento
        </Button>
      </div>

      <Card className="p-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <MagnifyingGlassIcon className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Buscar paciente ou profissional..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select
            options={[
              { value: '', label: 'Todos os status' },
              ...statusOptions.map(status => ({ value: status, label: status }))
            ]}
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          />
          <Select
            options={[
              { value: '', label: 'Todas as unidades' },
              ...unidades.map(unidade => ({ value: unidade, label: unidade }))
            ]}
            value={unidadeFilter}
            onChange={(e) => setUnidadeFilter(e.target.value)}
          />
          <Button variant="default">Exportar</Button>
        </div>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-gray-900">{filteredOrcamentos.length}</p>
            <p className="text-sm text-gray-600">Orçamentos Abertos</p>
          </div>
        </Card>
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-green-600">
              R$ {valorTotal.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
            </p>
            <p className="text-sm text-gray-600">Valor Total</p>
          </div>
        </Card>
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-blue-600">
              R$ {ticketMedio.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
            </p>
            <p className="text-sm text-gray-600">Ticket Médio</p>
          </div>
        </Card>
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-red-600">{orcamentosUrgentes}</p>
            <p className="text-sm text-gray-600">Urgentes</p>
          </div>
        </Card>
      </div>

      <Card className="overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Código
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Paciente
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Profissional
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Procedimentos
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Valor
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Dias Pendente
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ações
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredOrcamentos.map((orcamento) => (
                <tr key={orcamento.codigo} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {orcamento.codigo}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>
                      <div className="font-medium">{orcamento.paciente}</div>
                      <div className="text-gray-500">{orcamento.unidade}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {orcamento.profissional}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900">
                    <div className="max-w-xs">
                      {orcamento.procedimentos.map((proc, index) => (
                        <div key={index} className="text-xs text-gray-600">
                          • {proc}
                        </div>
                      ))}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>
                      <div className="font-medium">R$ {orcamento.valor_final.toFixed(2)}</div>
                      {orcamento.desconto > 0 && (
                        <div className="text-xs text-gray-500">
                          Desc: R$ {orcamento.desconto.toFixed(2)}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(orcamento.status)}`}>
                      {orcamento.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="flex items-center">
                      <span className={`w-2 h-2 rounded-full mr-2 ${getPrioridadeColor(orcamento.dias_pendente)}`}></span>
                      {orcamento.dias_pendente} dias
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    <Button variant="borderless" size="sm">
                      Aprovar
                    </Button>
                    <Button variant="borderless" size="sm">
                      Editar
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  );
};

export default OrcamentosAbertos;

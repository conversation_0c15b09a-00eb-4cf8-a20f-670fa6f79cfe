import React, { useState, useEffect } from 'react';
import { Card, Button, Input, Select } from '../../components/design-system';
import { MagnifyingGlassIcon, ClipboardDocumentListIcon } from '@heroicons/react/24/outline';

interface OrcamentoFechado {
  codigo: number;
  paciente: string;
  unidade: string;
  profissional: string;
  data_criacao: string;
  data_fechamento: string;
  procedimentos: string[];
  valor_total: number;
  desconto: number;
  valor_final: number;
  status: string;
  motivo_fechamento?: string;
  observacoes?: string;
}

export const OrcamentosFechados: React.FC = () => {
  const [orcamentos, setOrcamentos] = useState<OrcamentoFechado[]>([]);
  const [filteredOrcamentos, setFilteredOrcamentos] = useState<OrcamentoFechado[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [unidadeFilter, setUnidadeFilter] = useState('');

  useEffect(() => {
    const loadOrcamentos = async () => {
      try {
        const mockOrcamentos: OrcamentoFechado[] = Array.from({ length: 156 }, (_, i) => {
          const valorTotal = Math.round((Math.random() * 6000 + 300) * 100) / 100;
          const desconto = Math.round((Math.random() * valorTotal * 0.15) * 100) / 100;
          const valorFinal = valorTotal - desconto;
          const status = ['Aprovado', 'Rejeitado', 'Cancelado'][Math.floor(Math.random() * 3)];

          return {
            codigo: 500000000 + i,
            paciente: [
              'AMANDA SILVA',
              'Daniel Teste',
              'Stephany Figueiredo',
              'MARINA HAZIN',
              'Claudio Lemos',
              'FABIO FELTRIM',
              'Ana Carolina Santos',
              'Pedro Henrique Lima',
              'Juliana Costa',
              'Roberto Almeida'
            ][Math.floor(Math.random() * 10)],
            unidade: ['Morumbi', 'BRASILIA', 'Recife', 'Espinheiro', 'CLÍNICA (RL)'][Math.floor(Math.random() * 5)],
            profissional: [
              'Dr. Bruno Lima',
              'Dr. Caio Menezes',
              'Dra. Giulia Pedrosa',
              'Dra. Ana Victoria Rocha',
              'Dr. José Pedroza'
            ][Math.floor(Math.random() * 5)],
            data_criacao: new Date(Date.now() - Math.random() * 120 * 24 * 60 * 60 * 1000).toLocaleDateString('pt-BR'),
            data_fechamento: new Date(Date.now() - Math.random() * 60 * 24 * 60 * 60 * 1000).toLocaleDateString('pt-BR'),
            procedimentos: [
              ['BOTOX Facial', 'Preenchimento Labial'],
              ['Consulta Dermatológica', 'Limpeza de Pele'],
              ['Fisioterapia - 10 sessões'],
              ['Cirurgia Plástica', 'Pós-operatório'],
              ['Tratamento Capilar', 'Consulta Tricológica'],
              ['Psicologia - 8 sessões'],
              ['Nutrição - Consulta + Plano'],
              ['Exames Laboratoriais Completos']
            ][Math.floor(Math.random() * 8)],
            valor_total: valorTotal,
            desconto,
            valor_final: valorFinal,
            status,
            motivo_fechamento: status === 'Rejeitado' 
              ? ['Valor muito alto', 'Não tem interesse', 'Vai pensar'][Math.floor(Math.random() * 3)]
              : status === 'Cancelado'
              ? ['Mudança de planos', 'Problemas financeiros'][Math.floor(Math.random() * 2)]
              : undefined,
            observacoes: status === 'Aprovado' ? 'Procedimento agendado' : undefined
          };
        });

        setOrcamentos(mockOrcamentos);
        setFilteredOrcamentos(mockOrcamentos);
      } catch (error) {
        console.error('Erro ao carregar orçamentos fechados:', error);
      } finally {
        setLoading(false);
      }
    };

    loadOrcamentos();
  }, []);

  useEffect(() => {
    let filtered = orcamentos;

    if (searchTerm) {
      filtered = filtered.filter(orcamento =>
        orcamento.paciente.toLowerCase().includes(searchTerm.toLowerCase()) ||
        orcamento.profissional.toLowerCase().includes(searchTerm.toLowerCase()) ||
        orcamento.codigo.toString().includes(searchTerm)
      );
    }

    if (statusFilter) {
      filtered = filtered.filter(orcamento => orcamento.status === statusFilter);
    }

    if (unidadeFilter) {
      filtered = filtered.filter(orcamento => orcamento.unidade === unidadeFilter);
    }

    setFilteredOrcamentos(filtered);
  }, [searchTerm, statusFilter, unidadeFilter, orcamentos]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Aprovado':
        return 'bg-green-100 text-green-800';
      case 'Rejeitado':
        return 'bg-red-100 text-red-800';
      case 'Cancelado':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const orcamentosAprovados = filteredOrcamentos.filter(o => o.status === 'Aprovado');
  const orcamentosRejeitados = filteredOrcamentos.filter(o => o.status === 'Rejeitado');
  const valorTotalAprovado = orcamentosAprovados.reduce((sum, orc) => sum + orc.valor_final, 0);
  const taxaConversao = filteredOrcamentos.length > 0 
    ? (orcamentosAprovados.length / filteredOrcamentos.length) * 100 
    : 0;

  const statusOptions = [...new Set(orcamentos.map(o => o.status))];
  const unidades = [...new Set(orcamentos.map(o => o.unidade))];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Orçamentos Fechados</h1>
          <p className="text-sm text-gray-600 mt-1">Histórico de orçamentos finalizados</p>
        </div>
        <Button variant="prominent">
          <ClipboardDocumentListIcon className="w-4 h-4 mr-2" />
          Relatório Detalhado
        </Button>
      </div>

      <Card className="p-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <MagnifyingGlassIcon className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Buscar paciente ou profissional..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select
            options={[
              { value: '', label: 'Todos os status' },
              ...statusOptions.map(status => ({ value: status, label: status }))
            ]}
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          />
          <Select
            options={[
              { value: '', label: 'Todas as unidades' },
              ...unidades.map(unidade => ({ value: unidade, label: unidade }))
            ]}
            value={unidadeFilter}
            onChange={(e) => setUnidadeFilter(e.target.value)}
          />
          <Button variant="default">Exportar</Button>
        </div>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-green-600">{orcamentosAprovados.length}</p>
            <p className="text-sm text-gray-600">Aprovados</p>
          </div>
        </Card>
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-red-600">{orcamentosRejeitados.length}</p>
            <p className="text-sm text-gray-600">Rejeitados</p>
          </div>
        </Card>
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-blue-600">
              R$ {valorTotalAprovado.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
            </p>
            <p className="text-sm text-gray-600">Valor Aprovado</p>
          </div>
        </Card>
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-purple-600">{taxaConversao.toFixed(1)}%</p>
            <p className="text-sm text-gray-600">Taxa de Conversão</p>
          </div>
        </Card>
      </div>

      <Card className="overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Código
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Paciente
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Profissional
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Procedimentos
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Valor
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Data Fechamento
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ações
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredOrcamentos.slice(0, 50).map((orcamento) => (
                <tr key={orcamento.codigo} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {orcamento.codigo}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>
                      <div className="font-medium">{orcamento.paciente}</div>
                      <div className="text-gray-500">{orcamento.unidade}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {orcamento.profissional}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900">
                    <div className="max-w-xs">
                      {orcamento.procedimentos.map((proc, index) => (
                        <div key={index} className="text-xs text-gray-600">
                          • {proc}
                        </div>
                      ))}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>
                      <div className="font-medium">R$ {orcamento.valor_final.toFixed(2)}</div>
                      {orcamento.desconto > 0 && (
                        <div className="text-xs text-gray-500">
                          Desc: R$ {orcamento.desconto.toFixed(2)}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>
                      <div className="font-medium">{orcamento.data_fechamento}</div>
                      <div className="text-xs text-gray-500">
                        Criado: {orcamento.data_criacao}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(orcamento.status)}`}>
                        {orcamento.status}
                      </span>
                      {orcamento.motivo_fechamento && (
                        <div className="text-xs text-gray-500 mt-1">
                          {orcamento.motivo_fechamento}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    <Button variant="borderless" size="sm">
                      Ver
                    </Button>
                    <Button variant="borderless" size="sm">
                      Imprimir
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        {filteredOrcamentos.length > 50 && (
          <div className="px-6 py-3 bg-gray-50 border-t border-gray-200">
            <p className="text-sm text-gray-600">
              Mostrando 50 de {filteredOrcamentos.length} orçamentos. Use os filtros para refinar a busca.
            </p>
          </div>
        )}
      </Card>
    </div>
  );
};

export default OrcamentosFechados;

import React, { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Card, KPIC<PERSON>, Button } from '../../components/design-system';
import { 
  UserIcon, 
  DocumentTextIcon, 
  CurrencyDollarIcon, 
  ClipboardDocumentListIcon,
  HeartIcon,
  CalendarDaysIcon
} from '@heroicons/react/24/outline';

interface PacienteData {
  atendimentos_realizados: any[];
  creditos_disponiveis: any[];
  orcamentos_abertos: any[];
  orcamentos_fechados: any[];
}

interface Insight {
  title: string;
  description: string;
  insight_type: string;
  category: string;
}

export const PacienteIndex: React.FC = () => {
  const [data, setData] = useState<PacienteData>({
    atendimentos_realizados: [],
    creditos_disponiveis: [],
    orcamentos_abertos: [],
    orcamentos_fechados: []
  });
  const [insights, setInsights] = useState<Insight[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadData = async () => {
      try {
        // Dados mockados para demonstração
        const mockData: PacienteData = {
          atendimentos_realizados: Array.from({ length: 245 }, (_, i) => ({ 
            id: i + 1, 
            valor: Math.random() * 800 + 100,
            data: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000).toLocaleDateString('pt-BR'),
            paciente: `Paciente ${i + 1}`,
            procedimento: ['Consulta', 'BOTOX', 'Fisioterapia', 'Exame'][Math.floor(Math.random() * 4)]
          })),
          creditos_disponiveis: Array.from({ length: 35 }, (_, i) => ({ 
            id: i + 1, 
            valor: Math.random() * 1500 + 200,
            paciente: `Paciente ${i + 1}`,
            origem: ['Devolução', 'Cortesia', 'Promoção'][Math.floor(Math.random() * 3)]
          })),
          orcamentos_abertos: Array.from({ length: 68 }, (_, i) => ({ 
            id: i + 1, 
            valor: Math.random() * 5000 + 500,
            paciente: `Paciente ${i + 1}`,
            data_criacao: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleDateString('pt-BR'),
            procedimento: ['Tratamento Estético', 'Cirurgia', 'Consulta Especializada'][Math.floor(Math.random() * 3)]
          })),
          orcamentos_fechados: Array.from({ length: 156 }, (_, i) => ({ 
            id: i + 1, 
            valor: Math.random() * 4000 + 300,
            paciente: `Paciente ${i + 1}`,
            data_fechamento: new Date(Date.now() - Math.random() * 60 * 24 * 60 * 60 * 1000).toLocaleDateString('pt-BR'),
            status: ['Aprovado', 'Rejeitado'][Math.floor(Math.random() * 2)]
          }))
        };

        const mockInsights: Insight[] = [
          {
            title: "Fidelização de Pacientes",
            description: "78% dos pacientes retornaram nos últimos 6 meses",
            insight_type: "positive",
            category: "fidelizacao"
          },
          {
            title: "Orçamentos Pendentes",
            description: "68 orçamentos aguardam aprovação há mais de 15 dias",
            insight_type: "alert",
            category: "orcamentos"
          },
          {
            title: "Créditos Disponíveis",
            description: "R$ 52.450 em créditos podem ser utilizados para novos procedimentos",
            insight_type: "opportunity",
            category: "creditos"
          }
        ];

        setData(mockData);
        setInsights(mockInsights);
      } catch (error) {
        console.error('Erro ao carregar dados de pacientes:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // Calcular estatísticas
  const totalAtendimentos = data.atendimentos_realizados.length;
  const valorTotalAtendimentos = data.atendimentos_realizados.reduce((sum, item) => sum + item.valor, 0);
  const totalCreditos = data.creditos_disponiveis.reduce((sum, item) => sum + item.valor, 0);
  const totalOrcamentosAbertos = data.orcamentos_abertos.reduce((sum, item) => sum + item.valor, 0);
  const totalOrcamentosFechados = data.orcamentos_fechados.reduce((sum, item) => sum + item.valor, 0);
  
  const ticketMedio = totalAtendimentos > 0 ? valorTotalAtendimentos / totalAtendimentos : 0;
  const taxaConversao = data.orcamentos_fechados.length > 0 
    ? (data.orcamentos_fechados.filter(o => o.status === 'Aprovado').length / data.orcamentos_fechados.length) * 100 
    : 0;

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <Card className="p-8">
        <div className="flex flex-col md:flex-row items-center">
          <div className="md:w-2/3">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Módulo Paciente</h1>
            <p className="text-lg text-gray-600 mb-6">
              Gestão completa de pacientes, atendimentos, créditos e orçamentos.
            </p>
            <div className="flex space-x-4">
              <Link to="/paciente/atendimentos-realizados">
                <Button variant="prominent">Ver Atendimentos</Button>
              </Link>
              <Link to="/paciente/orcamentos-abertos">
                <Button variant="default">Orçamentos</Button>
              </Link>
            </div>
          </div>
          <div className="md:w-1/3 mt-6 md:mt-0 flex justify-center">
            <div className="w-48 h-48 bg-purple-100 rounded-full flex items-center justify-center">
              <UserIcon className="w-24 h-24 text-purple-600" />
            </div>
          </div>
        </div>
      </Card>

      {/* KPIs Principais */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <KPICard
          title="Atendimentos Realizados"
          value={totalAtendimentos}
          delta={{
            value: "12%",
            type: "positive",
            label: "vs mês anterior"
          }}
          insight="Total de atendimentos nos últimos 90 dias"
        />
        
        <KPICard
          title="Ticket Médio"
          value={`R$ ${ticketMedio.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`}
          delta={{
            value: "8%",
            type: "positive",
            label: "vs mês anterior"
          }}
          insight="Valor médio por atendimento"
        />
        
        <KPICard
          title="Créditos Disponíveis"
          value={`R$ ${totalCreditos.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`}
          insight="Total de créditos disponíveis para uso"
        />
        
        <KPICard
          title="Taxa de Conversão"
          value={`${taxaConversao.toFixed(1)}%`}
          delta={{
            value: taxaConversao > 70 ? "Excelente" : "Boa",
            type: taxaConversao > 70 ? "positive" : "neutral"
          }}
          insight="Orçamentos aprovados vs total"
        />
      </div>

      {/* Insights com IA */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z" />
            </svg>
            <h2 className="text-base font-semibold text-gray-800">Amigo Intelligence</h2>
          </div>
          <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
            Atualizado agora
          </span>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          {insights.map((insight, index) => (
            <Card key={index} className="p-4">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  {insight.insight_type === 'positive' && (
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                      <HeartIcon className="w-4 h-4 text-green-600" />
                    </div>
                  )}
                  {insight.insight_type === 'alert' && (
                    <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                      <svg className="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                    </div>
                  )}
                  {insight.insight_type === 'opportunity' && (
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <CurrencyDollarIcon className="w-4 h-4 text-blue-600" />
                    </div>
                  )}
                </div>
                <div className="ml-3 flex-1">
                  <h3 className="text-sm font-medium text-gray-900">{insight.title}</h3>
                  <p className="text-xs text-gray-600 mt-1">{insight.description}</p>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>

      {/* Links Rápidos */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Link to="/paciente/atendimentos-realizados" className="group">
          <Card className="p-4 hover:shadow-md transition-shadow duration-200">
            <div className="flex items-center">
              <CalendarDaysIcon className="w-8 h-8 text-blue-600 group-hover:text-blue-700" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-gray-900">Atendimentos Realizados</h3>
                <p className="text-xs text-gray-600">Histórico de atendimentos</p>
              </div>
            </div>
          </Card>
        </Link>

        <Link to="/paciente/creditos-disponiveis" className="group">
          <Card className="p-4 hover:shadow-md transition-shadow duration-200">
            <div className="flex items-center">
              <CurrencyDollarIcon className="w-8 h-8 text-green-600 group-hover:text-green-700" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-gray-900">Créditos Disponíveis</h3>
                <p className="text-xs text-gray-600">Gestão de créditos</p>
              </div>
            </div>
          </Card>
        </Link>

        <Link to="/paciente/orcamentos-abertos" className="group">
          <Card className="p-4 hover:shadow-md transition-shadow duration-200">
            <div className="flex items-center">
              <DocumentTextIcon className="w-8 h-8 text-orange-600 group-hover:text-orange-700" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-gray-900">Orçamentos Abertos</h3>
                <p className="text-xs text-gray-600">Orçamentos pendentes</p>
              </div>
            </div>
          </Card>
        </Link>

        <Link to="/paciente/orcamentos-fechados" className="group">
          <Card className="p-4 hover:shadow-md transition-shadow duration-200">
            <div className="flex items-center">
              <ClipboardDocumentListIcon className="w-8 h-8 text-purple-600 group-hover:text-purple-700" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-gray-900">Orçamentos Fechados</h3>
                <p className="text-xs text-gray-600">Histórico de orçamentos</p>
              </div>
            </div>
          </Card>
        </Link>
      </div>

      {/* Resumo Financeiro */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Resumo Financeiro</h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Faturamento de Atendimentos</span>
              <span className="text-sm font-medium text-green-600">
                R$ {valorTotalAtendimentos.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Orçamentos Abertos</span>
              <span className="text-sm font-medium text-orange-600">
                R$ {totalOrcamentosAbertos.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Orçamentos Fechados</span>
              <span className="text-sm font-medium text-blue-600">
                R$ {totalOrcamentosFechados.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
              </span>
            </div>
            <div className="flex justify-between items-center pt-2 border-t border-gray-200">
              <span className="text-sm font-medium text-gray-900">Potencial Total</span>
              <span className="text-sm font-bold text-gray-900">
                R$ {(valorTotalAtendimentos + totalOrcamentosAbertos).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
              </span>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Estatísticas de Pacientes</h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm font-medium text-gray-900">Pacientes Ativos</p>
                <p className="text-xs text-gray-600">Com atendimentos nos últimos 90 dias</p>
              </div>
              <span className="text-sm font-medium text-blue-600">{totalAtendimentos}</span>
            </div>
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm font-medium text-gray-900">Pacientes com Crédito</p>
                <p className="text-xs text-gray-600">Possuem créditos disponíveis</p>
              </div>
              <span className="text-sm font-medium text-green-600">{data.creditos_disponiveis.length}</span>
            </div>
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm font-medium text-gray-900">Orçamentos Pendentes</p>
                <p className="text-xs text-gray-600">Aguardando aprovação</p>
              </div>
              <span className="text-sm font-medium text-orange-600">{data.orcamentos_abertos.length}</span>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default PacienteIndex;

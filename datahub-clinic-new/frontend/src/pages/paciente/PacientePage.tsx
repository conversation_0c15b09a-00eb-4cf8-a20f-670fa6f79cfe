import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { 
  PacienteIndex, 
  AtendimentosRealizados, 
  CreditosDisponiveis, 
  OrcamentosAbertos, 
  OrcamentosFechados 
} from './index';

export const PacientePage: React.FC = () => {
  return (
    <Routes>
      <Route index element={<PacienteIndex />} />
      <Route path="atendimentos-realizados" element={<AtendimentosRealizados />} />
      <Route path="creditos-disponiveis" element={<CreditosDisponiveis />} />
      <Route path="orcamentos-abertos" element={<OrcamentosAbertos />} />
      <Route path="orcamentos-fechados" element={<OrcamentosFechados />} />
    </Routes>
  );
};

export default PacientePage;

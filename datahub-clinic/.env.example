# Flask Configuration
SECRET_KEY=your-secret-key-here
FLASK_ENV=development
FLASK_DEBUG=True

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-4o-mini

# Redis Configuration (for sessions and cache)
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# Database Configuration (optional)
DATABASE_URL=postgresql://user:password@localhost/datahub_clinic

# Session Configuration
SESSION_TYPE=redis
SESSION_PERMANENT=False
SESSION_USE_SIGNER=True
SESSION_KEY_PREFIX=datahub:

# Application Configuration
APP_NAME=DataHub Clinic
APP_VERSION=1.0.0
APP_DEBUG=True

# Data Source Configuration
DATA_SOURCE_TYPE=mock
DATA_SOURCE_FILE_PATH=data/mock_data.json

# Streamlit Configuration
STREAMLIT_PORT=8501

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# Feature Store Configuration
FEATURE_STORE_ENABLED=True
FEATURE_STORE_PATH=services/feature_store/storage/

# Cache Configuration
CACHE_ENABLED=True
CACHE_TTL=3600

# AI Configuration
EMBEDDING_MODEL=text-embedding-ada-002
EMBEDDING_DIMENSION=1536
MAX_TOKENS=4000
TEMPERATURE=0.7

# DataHub Clinic

Uma aplicação web inteligente para gestão de clínicas médicas com recursos de análise de dados, IA e insights automatizados.

## 🚀 Funcionalidades

### Mó<PERSON><PERSON>
- **📅 Agenda**: Gestão de agendamentos, produção médica e tempo de atendimento
- **💰 Financeiro**: Controle de contas a pagar/receber, fluxo de caixa e fechamento
- **👥 Paciente**: Atendimentos realizados, créditos e orçamentos
- **🎯 AmigoCare**: Avaliação NPS, leads, campanhas e acompanhamento

- **🤖 AmigoStudio Pro**: Ambiente de desenvolvimento com IA integrada

### Recursos Avançados
- **🧠 Insights com IA**: Análises automáticas usando GPT e LangChain
- **📊 Data Mesh**: Arquitetura de dados distribuída por domínios
- **🏪 Feature Store**: Armazenamento e gestão de features para ML
- **💾 Cache Inteligente**: Sistema de cache para otimização de performance
- **🔌 Múltiplas Fontes de Dados**: Suporte a JSON, Excel, CSV e bancos de dados

## 🛠️ Tecnologias

### Backend
- **Flask**: Framework web Python
- **Pandas & NumPy**: Processamento e análise de dados
- **SQLAlchemy**: ORM para bancos de dados
- **Redis**: Cache e sessões
- **OpenAI API**: Integração com GPT
- **LangChain**: Framework para aplicações com LLM
- **FAISS**: Busca vetorial para embeddings

### Frontend
- **HTML5 & CSS3**: Interface moderna e responsiva
- **JavaScript**: Interatividade e gráficos dinâmicos
- **Chart.js**: Visualizações de dados
- **Streamlit**: Dashboards interativos

### Dados e IA
- **Feature Store**: Gestão de features para ML
- **Vector Store**: Armazenamento de embeddings
- **Data Mesh**: Arquitetura de dados descentralizada
- **Embedding Service**: Geração de embeddings para busca semântica

## 📦 Instalação

### Pré-requisitos
- Python 3.8+
- Redis (para sessões e cache)
- Git

### Passos

1. **Clone o repositório**
```bash
git clone https://github.com/brunodeabreu-art/datahub-clinic.git
cd datahub-clinic
```

2. **Crie um ambiente virtual**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# ou
venv\Scripts\activate  # Windows
```

3. **Instale as dependências**
```bash
pip install -r requirements.txt
```

4. **Configure as variáveis de ambiente**
```bash
cp .env.example .env
# Edite o arquivo .env com suas configurações
```

5. **Execute a aplicação**
```bash
python app.py
```

A aplicação estará disponível em `http://localhost:5000`

## ⚙️ Configuração

### Variáveis de Ambiente

Crie um arquivo `.env` na raiz do projeto:

```env
# Flask
SECRET_KEY=sua-chave-secreta-aqui
FLASK_ENV=development

# OpenAI
OPENAI_API_KEY=sua-chave-openai-aqui
OPENAI_MODEL=gpt-4o-mini

# Redis
REDIS_URL=redis://localhost:6379

# Banco de Dados (opcional)
DATABASE_URL=postgresql://user:password@localhost/datahub_clinic
```

### Fontes de Dados

Configure suas fontes de dados em `config/data_source.json`:

```json
{
  "type": "json",
  "file_path": "data/mock_data.json"
}
```

Tipos suportados:
- `mock`: Dados simulados (padrão)
- `json`: Arquivo JSON
- `excel`: Arquivo Excel (.xlsx)
- `csv`: Arquivos CSV
- `database`: Banco de dados SQL

## 🚀 Uso

### Interface Web
1. Acesse `http://localhost:5000`
2. Navegue pelos módulos usando o menu lateral
3. Visualize insights automáticos em cada página
4. Use o chat inteligente para fazer perguntas sobre os dados

### AmigoStudio Pro
1. Acesse `/amigostudio-pro`
2. Execute código Python interativo
3. Crie dashboards Streamlit
4. Analise dados com IA

### API
A aplicação expõe uma API REST em `/api/`:
- `GET /api/insights`: Obter insights para widgets
- `POST /api/chat`: Chat com o agente inteligente
- `POST /api/upload`: Upload de arquivos de dados

## 🏗️ Arquitetura

### Data Mesh
- **Domínios**: Cada módulo é um domínio independente
- **Data Products**: Dados estruturados por domínio
- **Self-Service**: Interface para acesso aos dados
- **Federated Governance**: Governança distribuída

### Feature Store
- **Feature Groups**: Agrupamento lógico de features
- **Feature Registry**: Catálogo de features disponíveis
- **Versioning**: Controle de versão das features
- **Serving**: API para servir features

## 🧪 Testes

Execute os testes:
```bash
pytest
```

Execute testes com cobertura:
```bash
pytest --cov=services
```

## 📝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo `LICENSE` para mais detalhes.

## 🤝 Suporte

Para suporte, abra uma issue no GitHub ou entre em contato:
- Email: <EMAIL>
- GitHub: [@brunodeabreu-art](https://github.com/brunodeabreu-art)

## 🔄 Roadmap

- [ ] Integração com mais fontes de dados
- [ ] Dashboard de administração
- [ ] Autenticação e autorização
- [ ] API GraphQL
- [ ] Deploy automatizado
- [ ] Monitoramento e alertas
- [ ] Testes automatizados
- [ ] Documentação da API

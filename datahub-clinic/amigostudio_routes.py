"""
Módulo AmigoStudio Pro - Rotas e funcionalidades
"""
import os
import sys
import json
import time
import tempfile
import subprocess
import re
from flask import Blueprint, render_template, request, jsonify, session, current_app
import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional
import openai
from dotenv import load_dotenv
from services.langchain_service import LangChainService
from services.healthcare_service import HealthcareService

# Carregar variáveis de ambiente
load_dotenv()

# Configurar a API da OpenAI (não é mais necessário configurar globalmente na versão 1.0.0+)
# A chave da API será fornecida diretamente ao criar o cliente

# Inicializar os serviços
langchain_service = LangChainService()
healthcare_service = HealthcareService()

# Variáveis globais para controle de processos
current_port = 8600  # Porta inicial para o Streamlit
streamlit_processes = {}  # Dicionário para armazenar processos do Streamlit

# Função para carregar dados mockados do arquivo JSON
def load_mock_data_from_file():
    """
    Carrega dados mockados do arquivo JSON.

    Returns:
        dict: Dados mockados estruturados.
    """
    try:
        # Caminho para o arquivo de dados mockados
        static_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'static', 'mock_data.json')

        # Verificar se o arquivo existe
        if os.path.exists(static_path):
            with open(static_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                print(f"Dados mockados carregados com sucesso de {static_path}")
                return data

        # Se o arquivo não existir, tentar o caminho relativo
        static_path = os.path.join('static', 'mock_data.json')
        if os.path.exists(static_path):
            with open(static_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                print(f"Dados mockados carregados com sucesso de {static_path}")
                return data

        # Se não encontrar o arquivo, retornar um dicionário vazio
        print("Arquivo de dados mockados não encontrado. Retornando dicionário vazio.")
        return {}

    except Exception as e:
        print(f"Erro ao carregar dados mockados: {str(e)}")
        return {}

def register_routes(app):
    """
    Registra as rotas do AmigoStudio Pro na aplicação Flask.

    Args:
        app: Aplicação Flask
    """
    global langchain_service

    # Inicializar o serviço LangChain com os dados da aplicação
    try:
        # Obter dados mockados do arquivo JSON
        mock_data = load_mock_data_from_file()

        # Inicializar o vector store
        print("Inicializando o serviço LangChain com os dados da aplicação...")
        langchain_service.initialize_vector_store(mock_data)
        print("Serviço LangChain inicializado com sucesso!")
    except Exception as e:
        print(f"Erro ao inicializar o serviço LangChain: {str(e)}")

    # Rota principal do AmigoStudio Pro
    @app.route('/amigostudio-pro')
    def amigostudio_pro():
        """
        Página principal do AmigoStudio Pro.
        """
        # Redirecionar para a rota direct
        from flask import redirect
        return redirect('/amigostudio-pro-direct' + ('?' + request.query_string.decode() if request.query_string else ''))

    # Rota para executar código Python
    @app.route('/amigostudio-pro/execute', methods=['POST'])
    def amigostudio_execute():
        """
        Executa código Python e retorna o resultado.
        """
        try:
            # Obter o código do request
            code = request.json.get('code', '')

            # Criar um arquivo temporário para o código
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(code)
                temp_file = f.name

            # Executar o código
            result = subprocess.run(
                [sys.executable, temp_file],
                capture_output=True,
                text=True
            )

            # Remover o arquivo temporário
            os.unlink(temp_file)

            # Retornar o resultado
            return jsonify({
                'stdout': result.stdout,
                'stderr': result.stderr,
                'returncode': result.returncode
            })

        except Exception as e:
            return jsonify({
                'error': str(e)
            }), 500

    # Rota para executar código Streamlit
    @app.route('/amigostudio-pro/execute_streamlit', methods=['POST'])
    def amigostudio_execute_streamlit():
        """
        Executa código Streamlit e retorna a porta onde está rodando.
        """
        global current_port, streamlit_processes

        try:
            # Obter o código do request
            code = request.json.get('code', '')

            # Verificar se o código contém importação do Streamlit
            if 'import streamlit' not in code and 'import streamlit as st' not in code:
                return jsonify({
                    'error': 'O código não parece ser um script Streamlit. Adicione "import streamlit as st" ao seu código.'
                }), 400

            # Incrementar porta para evitar conflitos (8601 a 8700)
            current_port = (current_port % 100) + 8601

            # Executar o script Streamlit
            process, temp_file = run_streamlit_script(code, current_port)

            # Armazenar o processo e o arquivo temporário
            streamlit_processes[current_port] = {
                'process': process,
                'temp_file': temp_file
            }

            # Retornar a porta
            return jsonify({
                'port': current_port
            })

        except Exception as e:
            return jsonify({
                'error': str(e)
            }), 500

    # Rota para parar o Streamlit
    @app.route('/amigostudio-pro/stop_streamlit', methods=['POST'])
    def amigostudio_stop_streamlit():
        """
        Para o processo do Streamlit.
        """
        try:
            # Obter a porta do request
            port = request.json.get('port')

            # Verificar se a porta existe
            if port not in streamlit_processes:
                return jsonify({
                    'error': f'Não há processo Streamlit rodando na porta {port}'
                }), 404

            # Obter o processo e o arquivo temporário
            process = streamlit_processes[port]['process']
            temp_file = streamlit_processes[port]['temp_file']

            # Matar o processo
            if process.poll() is None:  # Verificar se o processo ainda está rodando
                process.terminate()
                process.wait(timeout=5)  # Aguardar até 5 segundos para o processo terminar

            # Remover o arquivo temporário
            if os.path.exists(temp_file):
                os.unlink(temp_file)

            # Remover o processo da lista
            del streamlit_processes[port]

            return jsonify({
                'message': f'Processo Streamlit na porta {port} parado com sucesso'
            })

        except Exception as e:
            return jsonify({
                'error': str(e)
            }), 500

    # Rota para o chat com IA
    @app.route('/amigostudio-pro/chat', methods=['POST'])
    def amigostudio_chat():
        """
        Processa mensagens do chat e retorna respostas da IA usando o Data Mesh.
        """
        try:
            # Obter a mensagem do request
            message = request.json.get('message', '')

            # Obter o histórico de mensagens da sessão
            if 'messages' not in session:
                session['messages'] = []

            # Adicionar a mensagem do usuário ao histórico
            session['messages'].append({
                'role': 'user',
                'content': message
            })

            # Obter dados mockados do arquivo JSON
            mock_data = load_mock_data_from_file()

            # Tentar usar o Data Mesh
            try:
                # Importar o módulo de integração do Data Mesh
                from services.data_mesh.agent_integration import process_amigostudio_chat

                # Processar a mensagem usando o Data Mesh
                response = process_amigostudio_chat(message, mock_data)

                # Adicionar a resposta ao histórico
                session['messages'].append({
                    'role': 'assistant',
                    'content': response['text']
                })

                # Marcar a sessão como modificada
                session.modified = True

                # Retornar a resposta
                return jsonify(response)

            except ImportError as e:
                print(f"Erro ao importar Data Mesh: {str(e)}. Usando fallback.")
                # Continuar com o método antigo como fallback
                pass

            # FALLBACK: Se o Data Mesh não estiver disponível, usar o método antigo
            global langchain_service

            # Verificar se o LangChain está inicializado
            if langchain_service.vector_store is None:
                print("Inicializando vector store com os dados mockados...")
                mock_data = load_mock_data_from_file()
                langchain_service.initialize_vector_store(mock_data)

            # Processar a mensagem e gerar resposta
            response = process_chat_message(message, mock_data)

            # Adicionar a resposta ao histórico
            session['messages'].append({
                'role': 'assistant',
                'content': response['text']
            })

            # Marcar a sessão como modificada
            session.modified = True

            # Retornar a resposta
            return jsonify(response)

        except Exception as e:
            print(f"Erro no chat: {str(e)}")
            return jsonify({
                'error': str(e)
            }), 500

    # Rota para limpar o histórico do chat
    @app.route('/amigostudio-pro/clear_chat', methods=['POST'])
    def amigostudio_clear_chat():
        """
        Limpa o histórico de mensagens do chat.
        """
        session['messages'] = []
        session.modified = True
        return jsonify({'status': 'success'})

    # Rota para a Feature Store
    @app.route('/amigostudio-pro/feature-store')
    def amigostudio_feature_store():
        """
        Página da Feature Store do AmigoStudio Pro.
        """
        try:
            # Inicializar a Feature Store se necessário
            try:
                from services.feature_store.amigostudio_integration import initialize_feature_store_in_app
                initialize_feature_store_in_app(app)
            except Exception as e:
                print(f"Erro ao inicializar Feature Store: {str(e)}")

            # Usar o mesmo template direct para todas as rotas
            template_path = 'amigostudio_pro/index_direct.html'
            print(f"Tentando renderizar o template: {template_path}")

            # Verificar se o template existe
            template_dir = os.path.join(app.root_path, 'templates')
            full_template_path = os.path.join(template_dir, template_path)
            if os.path.exists(full_template_path):
                print(f"Template encontrado em: {full_template_path}")
            else:
                print(f"ERRO: Template não encontrado em: {full_template_path}")
                # Retornar uma mensagem de erro em vez de tentar renderizar um template inexistente
                return f"Erro: Template {template_path} não encontrado. Por favor, verifique se o arquivo existe.", 404

            # Renderizar o template com um prompt específico para a feature store
            prompt = "Mostrar lista de features disponíveis para análise de dados de saúde"
            return render_template(template_path, prompt=prompt)

        except Exception as e:
            print(f"Erro ao renderizar Feature Store: {str(e)}")
            return f"Erro ao carregar Feature Store: {str(e)}", 500

    # Rota para a Feature Store Otimizada
    @app.route('/amigostudio-pro/feature-store-otimizado')
    def amigostudio_feature_store_otimizado():
        """
        Página da Feature Store Otimizada do AmigoStudio Pro.
        """
        try:
            # Inicializar a Feature Store se necessário
            try:
                from services.feature_store.amigostudio_integration import initialize_feature_store_in_app
                initialize_feature_store_in_app(app)
            except Exception as e:
                print(f"Erro ao inicializar Feature Store: {str(e)}")

            # Usar o template correto para a feature store
            template_path = 'amigostudio/feature_store_otimizado.html'
            print(f"Tentando renderizar o template: {template_path}")

            # Verificar se o template existe
            template_dir = os.path.join(app.root_path, 'templates')
            full_template_path = os.path.join(template_dir, template_path)
            if os.path.exists(full_template_path):
                print(f"Template encontrado em: {full_template_path}")
            else:
                print(f"ERRO: Template não encontrado em: {full_template_path}")
                # Retornar uma mensagem de erro em vez de tentar renderizar um template inexistente
                return f"Erro: Template {template_path} não encontrado. Por favor, verifique se o arquivo existe.", 404

            # Renderizar o template
            return render_template(template_path)

        except Exception as e:
            print(f"Erro ao renderizar Feature Store Otimizada: {str(e)}")
            return f"Erro ao carregar Feature Store Otimizada: {str(e)}", 500

    # API para obter features
    @app.route('/amigostudio-pro/api/features', methods=['GET'])
    def amigostudio_get_features():
        """
        Retorna as features disponíveis na Feature Store.
        """
        try:
            # Obter parâmetros da query
            query = request.args.get('query')
            domain = request.args.get('domain')
            tags = request.args.get('tags')

            # Converter tags para lista se existir
            if tags:
                tags = tags.split(',')

            # Obter features
            from services.feature_store.amigostudio_integration import get_features_for_analysis
            features = get_features_for_analysis(query, domain, tags)

            return jsonify(features)

        except Exception as e:
            print(f"Erro ao obter features: {str(e)}")
            return jsonify({
                'error': str(e)
            }), 500

    # API para registrar feature personalizada
    @app.route('/amigostudio-pro/api/features', methods=['POST'])
    def amigostudio_register_feature():
        """
        Registra uma feature personalizada na Feature Store.
        """
        try:
            # Obter dados do request
            data = request.json

            # Validar dados
            required_fields = ['group_name', 'feature_name', 'description', 'data_type', 'value']
            for field in required_fields:
                if field not in data:
                    return jsonify({
                        'error': f'Campo obrigatório ausente: {field}'
                    }), 400

            # Registrar feature
            from services.feature_store.amigostudio_integration import register_custom_feature
            result = register_custom_feature(
                group_name=data['group_name'],
                feature_name=data['feature_name'],
                description=data['description'],
                data_type=data['data_type'],
                value=data['value'],
                domain=data.get('domain'),
                tags=data.get('tags')
            )

            return jsonify(result)

        except Exception as e:
            print(f"Erro ao registrar feature: {str(e)}")
            return jsonify({
                'error': str(e)
            }), 500

    # Rota para o AmigoStudio Pro Direct
    @app.route('/amigostudio-pro-direct')
    def amigostudio_pro_direct():
        """
        Página do AmigoStudio Pro Direct com maleta de ferramentas otimizada.
        """
        try:
            # Gerar insights para o AmigoStudio Pro
            insights = []

            # Verificar se há um prompt da maleta de ferramentas
            prompt = request.args.get('prompt', '')

            # Usar o template direct diretamente sem fallbacks
            template_path = 'amigostudio_pro/index_direct.html'
            print(f"Tentando renderizar o template: {template_path}")

            # Verificar se o template existe
            template_dir = os.path.join(app.root_path, 'templates')
            full_template_path = os.path.join(template_dir, template_path)
            if os.path.exists(full_template_path):
                print(f"Template encontrado em: {full_template_path}")
            else:
                print(f"ERRO: Template não encontrado em: {full_template_path}")
                # Retornar uma mensagem de erro em vez de tentar renderizar um template inexistente
                return f"Erro: Template {template_path} não encontrado. Por favor, verifique se o arquivo existe.", 404

            # Renderizar o template
            return render_template(template_path, insights=insights, prompt=prompt)

        except Exception as e:
            print(f"Erro ao renderizar AmigoStudio Pro Direct: {str(e)}")
            return f"Erro ao carregar AmigoStudio Pro Direct: {str(e)}", 500

def run_streamlit_script(code, port):
    """Executa um script Streamlit em um processo separado."""
    temp_file = None

    try:
        # Criar arquivo temporário
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as f:
            f.write(code)
            temp_file = f.name

        # Executar Streamlit
        cmd = f'streamlit run "{temp_file}" --server.port {port} --server.headless true'
        process = subprocess.Popen(
            cmd,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )

        # Aguardar um pouco para o servidor iniciar
        time.sleep(3)

        # Verificar se Streamlit iniciou corretamente
        if process.poll() is not None:
            stderr = process.stderr.read().decode('utf-8')
            raise RuntimeError(f"Falha ao iniciar o Streamlit: {stderr}")

        return process, temp_file
    except Exception as e:
        # Limpar arquivo temporário se houver erro
        if temp_file and os.path.exists(temp_file):
            try:
                os.unlink(temp_file)
            except:
                pass
        raise e

def detect_healthcare_intent(message):
    """
    Detecta intenções relacionadas à análise de dados de saúde.

    Args:
        message: Mensagem do usuário

    Returns:
        list: Lista de intenções detectadas
    """
    healthcare_intents = {
        "jornada_paciente": any(keyword in message.lower() for keyword in [
            "jornada do paciente", "fluxo do paciente", "caminho do paciente",
            "experiência do paciente", "patient journey", "etapas", "processo de atendimento"
        ]),
        "previsao_demanda": any(keyword in message.lower() for keyword in [
            "prever demanda", "previsão de agendamentos", "ocupação futura",
            "picos de atendimento", "forecast", "previsão", "agenda", "lotação"
        ]),
        "rentabilidade": any(keyword in message.lower() for keyword in [
            "rentabilidade", "lucratividade", "margem", "procedimento mais lucrativo",
            "retorno financeiro", "roi", "receita por procedimento", "faturamento"
        ]),
        "retencao_pacientes": any(keyword in message.lower() for keyword in [
            "abandono", "churn", "retenção", "pacientes inativos",
            "recuperar pacientes", "fidelização", "recorrência", "retorno de pacientes"
        ])
    }

    detected_intents = [intent for intent, detected in healthcare_intents.items() if detected]
    return detected_intents

def process_chat_message(message, data):
    """
    Processa uma mensagem do chat e gera uma resposta.

    Args:
        message: Mensagem do usuário
        data: Dados disponíveis para análise

    Returns:
        dict: Resposta contendo texto e código (se houver)
    """
    try:
        global langchain_service, healthcare_service

        # Verificar se a mensagem pede para gerar código (lista expandida)
        code_keywords = [
            # Português - Geração de código
            'gerar código', 'criar código', 'gerar um código', 'criar um código',
            'escrever código', 'desenvolver código', 'programar', 'script',
            'programa', 'algoritmo', 'função', 'método', 'classe',

            # Português - Análise e visualização
            'gerar visualização', 'criar visualização', 'mostrar gráfico', 'plotar',
            'gráfico', 'chart', 'plot', 'visualizar', 'dashboard', 'relatório',
            'análise', 'calcular', 'processar dados', 'analisar dados',

            # Português - Bibliotecas e ferramentas
            'pandas', 'matplotlib', 'seaborn', 'numpy', 'dataframe',
            'estatística', 'metrics', 'kpi', 'indicadores',

            # Inglês
            'code', 'generate code', 'create code', 'write code', 'python',
            'visualization', 'chart', 'graph', 'plot', 'analyze', 'calculate',
            'process data', 'statistics', 'report', 'dashboard'
        ]

        generate_code = any(keyword in message.lower() for keyword in code_keywords)

        # Detectar intenções relacionadas à saúde
        healthcare_intents = detect_healthcare_intent(message)
        print(f"Intenções de saúde detectadas: {healthcare_intents}")

        # Inicializar o vector store se ainda não foi feito
        if langchain_service.vector_store is None:
            print("Inicializando vector store com os dados da aplicação...")
            langchain_service.initialize_vector_store(data)

        # Executar análises especializadas com base na intenção
        specialized_analysis = None
        if healthcare_intents:
            if "jornada_paciente" in healthcare_intents:
                specialized_analysis = healthcare_service.analyze_patient_journey(data)
                print(f"Análise de jornada do paciente: {specialized_analysis['status']}")
            elif "previsao_demanda" in healthcare_intents:
                specialized_analysis = healthcare_service.predict_appointment_demand(data)
                print(f"Previsão de demanda: {specialized_analysis['status']}")
            elif "rentabilidade" in healthcare_intents:
                specialized_analysis = healthcare_service.analyze_procedure_profitability(data)
                print(f"Análise de rentabilidade: {specialized_analysis['status']}")
            elif "retencao_pacientes" in healthcare_intents:
                specialized_analysis = healthcare_service.detect_patient_churn_risk(data)
                print(f"Análise de risco de abandono: {specialized_analysis['status']}")

        # Usar o serviço LangChain para analisar os dados
        response = langchain_service.analyze_data(message, generate_code, domain="healthcare")

        # Incorporar análises especializadas na resposta, se houver
        if specialized_analysis and specialized_analysis.get('status') == 'success':
            # Adicionar contexto da análise especializada à resposta
            additional_context = "\n\n**Análise Especializada**\n\n"

            # Inicializar dados do gráfico (se necessário)
            chart_data = None

            if "jornada_paciente" in healthcare_intents:
                bottlenecks = specialized_analysis.get('bottlenecks', [])
                recommendations = specialized_analysis.get('recommendations', [])
                journey_stages = specialized_analysis.get('journey_stages', [])

                additional_context += "**Gargalos Identificados na Jornada do Paciente:**\n"
                for bottleneck in bottlenecks:
                    additional_context += f"- {bottleneck.get('stage')}: {bottleneck.get('issue')} (Impacto: {bottleneck.get('impact')})\n"

                additional_context += "\n**Recomendações para Melhorar a Jornada:**\n"
                for recommendation in recommendations:
                    additional_context += f"- {recommendation}\n"

                # Criar dados para o gráfico de jornada do paciente
                if journey_stages:
                    chart_data = {
                        "type": "bar",
                        "labels": [stage.get('stage', '').capitalize() for stage in journey_stages],
                        "datasets": [
                            {
                                "label": "Tempo Médio (min)",
                                "data": [stage.get('avg_time', 0) for stage in journey_stages],
                                "backgroundColor": "rgba(0, 122, 255, 0.7)",
                                "borderColor": "rgba(0, 122, 255, 1)",
                                "borderWidth": 1
                            },
                            {
                                "label": "Satisfação (1-5)",
                                "data": [stage.get('satisfaction', 0) for stage in journey_stages],
                                "backgroundColor": "rgba(52, 199, 89, 0.7)",
                                "borderColor": "rgba(52, 199, 89, 1)",
                                "borderWidth": 1,
                                "yAxisID": "y1"
                            }
                        ],
                        "options": {
                            "scales": {
                                "y": {
                                    "beginAtZero": true,
                                    "title": {
                                        "display": true,
                                        "text": "Tempo (min)"
                                    }
                                },
                                "y1": {
                                    "beginAtZero": true,
                                    "position": "right",
                                    "grid": {
                                        "drawOnChartArea": false
                                    },
                                    "title": {
                                        "display": true,
                                        "text": "Satisfação (1-5)"
                                    },
                                    "min": 0,
                                    "max": 5
                                }
                            },
                            "plugins": {
                                "title": {
                                    "display": true,
                                    "text": "Análise da Jornada do Paciente"
                                }
                            }
                        }
                    }

            elif "previsao_demanda" in healthcare_intents:
                forecast = specialized_analysis.get('forecast', [])[:5]  # Primeiros 5 dias
                recommendations = specialized_analysis.get('recommendations', [])

                additional_context += "**Previsão de Demanda (Próximos 5 dias):**\n"
                for day in forecast:
                    additional_context += f"- {day.get('date')} ({day.get('weekday')}): {day.get('predicted_appointments')} agendamentos previstos\n"

                additional_context += f"\n**Dia de Pico:** {specialized_analysis.get('peak_day')}\n"
                additional_context += f"**Média Diária:** {specialized_analysis.get('avg_daily_appointments')} agendamentos\n"

                additional_context += "\n**Recomendações para Gestão de Demanda:**\n"
                for recommendation in recommendations:
                    additional_context += f"- {recommendation}\n"

                # Criar dados para o gráfico de previsão de demanda
                if forecast:
                    chart_data = {
                        "type": "line",
                        "labels": [f"{day.get('date')} ({day.get('weekday')[:3]})" for day in forecast],
                        "datasets": [
                            {
                                "label": "Agendamentos Previstos",
                                "data": [day.get('predicted_appointments', 0) for day in forecast],
                                "backgroundColor": "rgba(0, 122, 255, 0.1)",
                                "borderColor": "rgba(0, 122, 255, 1)",
                                "borderWidth": 2,
                                "tension": 0.4,
                                "fill": true
                            }
                        ],
                        "options": {
                            "plugins": {
                                "title": {
                                    "display": true,
                                    "text": "Previsão de Demanda de Agendamentos"
                                }
                            }
                        }
                    }

            elif "rentabilidade" in healthcare_intents:
                procedures = specialized_analysis.get('procedures', [])[:3]  # Top 3 procedimentos
                recommendations = specialized_analysis.get('recommendations', [])

                additional_context += "**Procedimentos Mais Rentáveis:**\n"
                for proc in procedures:
                    additional_context += f"- {proc.get('tipo')}: Rentabilidade de {proc.get('rentabilidade')}% (Receita média: R${proc.get('receita_media')}, Custo médio: R${proc.get('custo_medio')})\n"

                additional_context += "\n**Recomendações para Melhorar Rentabilidade:**\n"
                for recommendation in recommendations:
                    additional_context += f"- {recommendation}\n"

                # Criar dados para o gráfico de rentabilidade
                if procedures:
                    chart_data = {
                        "type": "bar",
                        "labels": [proc.get('tipo', '') for proc in procedures],
                        "datasets": [
                            {
                                "label": "Rentabilidade (%)",
                                "data": [proc.get('rentabilidade', 0) for proc in procedures],
                                "backgroundColor": "rgba(0, 122, 255, 0.7)",
                                "borderColor": "rgba(0, 122, 255, 1)",
                                "borderWidth": 1
                            },
                            {
                                "label": "Receita Média (R$)",
                                "data": [proc.get('receita_media', 0) for proc in procedures],
                                "backgroundColor": "rgba(52, 199, 89, 0.7)",
                                "borderColor": "rgba(52, 199, 89, 1)",
                                "borderWidth": 1,
                                "yAxisID": "y1"
                            },
                            {
                                "label": "Custo Médio (R$)",
                                "data": [proc.get('custo_medio', 0) for proc in procedures],
                                "backgroundColor": "rgba(255, 59, 48, 0.7)",
                                "borderColor": "rgba(255, 59, 48, 1)",
                                "borderWidth": 1,
                                "yAxisID": "y1"
                            }
                        ],
                        "options": {
                            "scales": {
                                "y": {
                                    "beginAtZero": true,
                                    "title": {
                                        "display": true,
                                        "text": "Rentabilidade (%)"
                                    }
                                },
                                "y1": {
                                    "beginAtZero": true,
                                    "position": "right",
                                    "grid": {
                                        "drawOnChartArea": false
                                    },
                                    "title": {
                                        "display": true,
                                        "text": "Valor (R$)"
                                    }
                                }
                            },
                            "plugins": {
                                "title": {
                                    "display": true,
                                    "text": "Análise de Rentabilidade por Procedimento"
                                }
                            }
                        }
                    }

            elif "retencao_pacientes" in healthcare_intents:
                at_risk = specialized_analysis.get('at_risk_patients', [])[:3]  # Top 3 pacientes em risco
                recommendations = specialized_analysis.get('recommendations', [])
                risk_percentage = specialized_analysis.get('risk_percentage', 0)

                additional_context += "**Pacientes com Risco de Abandono:**\n"
                for patient in at_risk:
                    additional_context += f"- {patient.get('nome')}: Risco {patient.get('risco')} (Último atendimento: {patient.get('ultimo_atendimento')}, {patient.get('dias_sem_contato')} dias sem contato)\n"

                additional_context += f"\n**Percentual de Pacientes em Risco:** {risk_percentage}%\n"

                additional_context += "\n**Recomendações para Retenção de Pacientes:**\n"
                for recommendation in recommendations:
                    additional_context += f"- {recommendation}\n"

                # Criar dados para o gráfico de risco de abandono
                chart_data = {
                    "type": "doughnut",
                    "labels": ["Em Risco", "Sem Risco"],
                    "datasets": [
                        {
                            "data": [risk_percentage, 100 - risk_percentage],
                            "backgroundColor": [
                                "rgba(255, 59, 48, 0.7)",
                                "rgba(52, 199, 89, 0.7)"
                            ],
                            "borderColor": [
                                "rgba(255, 59, 48, 1)",
                                "rgba(52, 199, 89, 1)"
                            ],
                            "borderWidth": 1
                        }
                    ],
                    "options": {
                        "plugins": {
                            "title": {
                                "display": true,
                                "text": "Percentual de Pacientes em Risco de Abandono"
                            }
                        }
                    }
                }

            # Adicionar o contexto especializado à resposta
            response["text"] += additional_context

            # Adicionar dados do gráfico à resposta, se houver
            if chart_data:
                response["chart"] = chart_data

        # Se não houver resposta do LangChain, usar a API da OpenAI diretamente
        if not response or not response.get("text"):
            # Preparar o prompt para a OpenAI
            system_prompt = """Você é um assistente de análise de dados especializado em Python e visualização de dados.
Você ajuda a analisar dados e gerar insights. Quando solicitado, você pode gerar código Python para análise e visualização.
Você tem acesso aos seguintes dados:
"""

            # Adicionar informações sobre os dados disponíveis
            for key, value in data.items():
                if isinstance(value, dict) and len(value) > 0:
                    system_prompt += f"\n- {key}: {list(value.keys())}"

            # Completar o prompt
            if generate_code:
                system_prompt += """

Quando solicitado a gerar código, siga estas diretrizes:
1. Use bibliotecas como pandas, numpy, matplotlib, seaborn
2. Gere código completo e executável
3. Inclua comentários explicativos
4. Formate o código dentro de blocos de código Markdown com ```python e ```
5. Explique o que o código faz antes e depois do bloco de código
"""

            # Criar a mensagem para a API da OpenAI
            messages = [
                {"role": "system", "content": system_prompt}
            ]

            # Adicionar histórico de mensagens (limitado a 10 últimas)
            if 'messages' in session:
                messages.extend(session['messages'][-10:])
            else:
                messages.append({"role": "user", "content": message})

            # Chamar a API da OpenAI (sintaxe para openai>=1.0.0)
            client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
            openai_response = client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=messages,
                temperature=0.7,
                max_tokens=1500
            )

            # Extrair a resposta (nova sintaxe)
            ai_response = openai_response.choices[0].message.content

            # Extrair código de visualização, se houver
            visualization_code = None
            if "```python" in ai_response:
                code_start = ai_response.find("```python") + 9
                code_end = ai_response.find("```", code_start)
                if code_end != -1:
                    visualization_code = ai_response[code_start:code_end].strip()

            response = {
                "text": ai_response,
                "code": visualization_code
            }

        return response

    except Exception as e:
        print(f"Erro ao processar mensagem: {str(e)}")
        return {
            "text": f"Desculpe, ocorreu um erro ao processar sua mensagem: {str(e)}",
            "code": None
        }

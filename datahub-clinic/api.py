"""
API para fornecer insights para os widgets e respostas para o agente inteligente.
"""
from flask import Blueprint, jsonify, request, session, current_app
import os
import json
import requests
import pandas as pd
import tempfile
from werkzeug.utils import secure_filename
from services import InsightService
from dotenv import load_dotenv

# Carregar variáveis de ambiente
load_dotenv()

api = Blueprint('api', __name__)

# Configuração da OpenAI
OPENAI_API_URL = "https://api.openai.com/v1/chat/completions"
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")

# Configuração para upload de arquivos
UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
ALLOWED_EXTENSIONS = {'csv', 'xlsx', 'xls'}

# Criar pasta de uploads se não existir
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@api.route('/insights/<module_name>/<report_name>', methods=['GET', 'POST'])
def get_insights(module_name, report_name):
    """
    Obtém insights para um módulo e relatório específicos.

    Args:
        module_name (str): Nome do módulo
        report_name (str): Nome do relatório

    Returns:
        JSON: Lista de insights
    """
    # Importar a função para obter dados mockados diretamente
    from app import get_mock_data

    # Obter dados mockados da função global
    mock_data = get_mock_data()

    # Obter número de insights e tipos
    num_insights = request.args.get('num_insights', 3, type=int)
    insight_types = request.args.get('types', 'list,text,stat').split(',')

    # Obter contexto da página
    page_context = None
    if request.method == 'POST':
        page_context = request.json.get('page_context')
    elif request.args.get('page_context'):
        import json
        try:
            page_context = json.loads(request.args.get('page_context'))
        except:
            pass

    # Gerar insights
    insights = InsightService.get_insights_for_page(module_name, report_name, mock_data, num_insights, insight_types, page_context)

    return jsonify(insights)

@api.route('/insights/<module_name>/<report_name>/single', methods=['GET', 'POST'])
def get_single_insight(module_name, report_name):
    """
    Obtém um único insight para um módulo e relatório específicos.

    Args:
        module_name (str): Nome do módulo
        report_name (str): Nome do relatório

    Returns:
        JSON: Insight gerado
    """
    # Importar a função para obter dados mockados diretamente
    from app import get_mock_data

    # Obter dados mockados da função global
    mock_data = get_mock_data()

    # Obter tipo de insight
    insight_type = request.args.get('type', 'list')
    category = request.args.get('category')

    # Obter contexto da página
    page_context = None
    if request.method == 'POST':
        page_context = request.json.get('page_context')
    elif request.args.get('page_context'):
        import json
        try:
            page_context = json.loads(request.args.get('page_context'))
        except:
            pass

    # Gerar insight
    insight = InsightService.get_single_insight(module_name, report_name, mock_data, insight_type, category, page_context)

    return jsonify(insight)

@api.route('/insights/clear-cache', methods=['POST'])
def clear_cache():
    """
    Limpa o cache de insights.

    Returns:
        JSON: Mensagem de sucesso
    """
    from services.insight_cache import InsightCache

    InsightCache.clear_cache()

    return jsonify({'message': 'Cache limpo com sucesso'})

@api.route('/amigostudio/upload', methods=['POST'])
def upload_file():
    """
    Processa o upload de um arquivo para análise no AmigoStudio.

    Returns:
        JSON: Informações sobre o arquivo carregado
    """
    try:
        # Verificar se há arquivo na requisição
        if 'file' not in request.files:
            return jsonify({
                "error": "Nenhum arquivo enviado"
            }), 400

        file = request.files['file']

        # Verificar se o arquivo tem nome
        if file.filename == '':
            return jsonify({
                "error": "Nenhum arquivo selecionado"
            }), 400

        # Verificar se o arquivo é permitido
        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            file_path = os.path.join(UPLOAD_FOLDER, filename)
            file.save(file_path)

            # Armazenar o caminho do arquivo na sessão
            session['amigostudio_file'] = file_path

            # Ler o arquivo para obter informações
            data_info = {}
            try:
                if filename.endswith('.csv'):
                    df = pd.read_csv(file_path)
                else:  # xlsx ou xls
                    df = pd.read_excel(file_path)

                # Obter informações básicas sobre os dados
                data_info = {
                    "colunas": df.columns.tolist(),
                    "linhas": len(df),
                    "tipos": {col: str(dtype) for col, dtype in df.dtypes.items()},
                    "amostra": df.head(5).to_dict(orient='records')
                }

                # Armazenar informações na sessão
                session['amigostudio_data_info'] = data_info

            except Exception as e:
                return jsonify({
                    "error": f"Erro ao processar o arquivo: {str(e)}"
                }), 500

            return jsonify({
                "success": True,
                "filename": filename,
                "message": "Arquivo carregado com sucesso. Você pode agora fazer perguntas sobre os dados.",
                "data_info": data_info
            })

        return jsonify({
            "error": "Tipo de arquivo não permitido. Use CSV ou Excel."
        }), 400

    except Exception as e:
        return jsonify({
            "error": f"Erro no servidor: {str(e)}"
        }), 500

@api.route('/amigostudio/analyze', methods=['POST'])
def analyze_data():
    """
    Processa uma pergunta sobre os dados e gera uma resposta com visualização.

    Returns:
        JSON: Resposta e código de visualização
    """
    try:
        # Obter dados da requisição
        data = request.json
        question = data.get('question', '')
        conversation_history = data.get('conversation_history', [])

        # Verificar se a pergunta está vazia
        if not question.strip():
            return jsonify({
                "response": "Por favor, faça uma pergunta para que eu possa ajudar.",
                "visualization_code": None
            }), 400

        # Verificar se há dados carregados
        data_info = session.get('amigostudio_data_info', None)
        file_path = session.get('amigostudio_file', None)

        # Preparar o contexto para a IA
        context = ""
        if data_info:
            context += f"Informações sobre os dados:\n"
            context += f"- Colunas: {', '.join(data_info['colunas'])}\n"
            context += f"- Número de linhas: {data_info['linhas']}\n"
            context += f"- Tipos de dados: {data_info['tipos']}\n\n"
            context += f"Primeiras 5 linhas:\n{json.dumps(data_info['amostra'], indent=2)}\n\n"
        else:
            # Usar dados de exemplo se não houver arquivo carregado
            context += "Não há dados carregados. Usando dados de exemplo para demonstração.\n"
            context += "Os dados de exemplo contêm informações sobre vendas, agendamentos e pacientes.\n"

        # Preparar mensagens para a API da OpenAI
        messages = [
            {
                "role": "system",
                "content": """Você é um assistente especializado em análise de dados e visualização.
                Ajude o usuário a criar análises e visualizações interativas com base nos dados fornecidos.
                Quando solicitado, forneça código Python completo usando Pandas, Matplotlib, Seaborn ou Plotly.
                O código será executado em um ambiente Streamlit, então use funções como st.write(), st.dataframe(),
                st.plotly_chart() etc. para exibir resultados.

                Regras importantes:
                1. Sempre forneça uma explicação clara antes do código
                2. Sempre inclua o código de visualização quando apropriado
                3. Use 'df' como nome da variável para o DataFrame
                4. Mantenha o código simples e bem comentado
                5. Prefira visualizações interativas com Plotly quando possível
                """
            }
        ]

        # Adicionar contexto dos dados
        messages.append({
            "role": "system",
            "content": context
        })

        # Adicionar histórico de conversa
        for msg in conversation_history:
            messages.append({
                "role": msg.get("role", "user"),
                "content": msg.get("content", "")
            })

        # Adicionar a pergunta atual
        messages.append({
            "role": "user",
            "content": question
        })

        # Fazer a requisição para a API da OpenAI usando a nova sintaxe (versão 1.0.0+)
        try:
            import openai
            client = openai.OpenAI(api_key=OPENAI_API_KEY)

            response = client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=messages,
                temperature=0.7,
                max_tokens=2000
            )

            # Extrair a resposta
            ai_response = response.choices[0].message.content

        except ImportError:
            # Fallback para requests se o openai não estiver disponível
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {OPENAI_API_KEY}"
            }

            payload = {
                "model": "gpt-3.5-turbo",
                "messages": messages,
                "temperature": 0.7,
                "max_tokens": 2000
            }

            response = requests.post(OPENAI_API_URL, headers=headers, json=payload)
            response_data = response.json()

            if "choices" not in response_data:
                return jsonify({
                    "error": "Erro na resposta da API da OpenAI",
                    "details": response_data
                }), 500

            # Extrair a resposta
            ai_response = response_data["choices"][0]["message"]["content"]

        # Extrair código de visualização, se houver
        visualization_code = None
        if "```python" in ai_response:
            code_start = ai_response.find("```python") + 9
            code_end = ai_response.find("```", code_start)
            if code_end != -1:
                visualization_code = ai_response[code_start:code_end].strip()

        return jsonify({
            "response": ai_response,
            "visualization_code": visualization_code
        })

    except Exception as e:
        return jsonify({
            "error": f"Erro no servidor: {str(e)}"
        }), 500



# ===== DASHBOARD CUSTOMIZATION API =====

@api.route('/dashboard/config', methods=['GET'])
def get_dashboard_config_v2():
    """Obtém a configuração do dashboard do usuário"""
    user_id = session.get('user_id', 'default_user')
    config_key = f"dashboard_config_{user_id}"
    saved_config = session.get(config_key)

    if not saved_config:
        # Configuração padrão com 4 KPIs fixos + alguns opcionais
        saved_config = {
            "fixed_kpis": ["vendas_total", "producao_total", "agendamentos_mes_corrente", "atendimentos_mes_corrente"],
            "selected_optional_kpis": [
                "perc_conversao_leads",
                "nps_periodo_anterior",
                "ticket_medio_periodo",
                "perc_faltas_cancelamentos"
            ],
            "layout": {
                "grid_columns": 4,
                "compact_mode": False
            },
            "preferences": {
                "auto_refresh": 300,
                "show_trends": True
            }
        }
        session[config_key] = saved_config

    return jsonify(saved_config)

@api.route('/dashboard/config', methods=['POST'])
def save_dashboard_config_v2():
    """Salva a configuração do dashboard do usuário"""
    try:
        user_id = session.get('user_id', 'default_user')
        config = request.json

        if not config:
            return jsonify({"error": "Configuração inválida"}), 400

        config_key = f"dashboard_config_{user_id}"
        session[config_key] = config

        return jsonify({"message": "Configuração salva com sucesso"})

    except Exception as e:
        return jsonify({"error": f"Erro ao salvar: {str(e)}"}), 500

@api.route('/dashboard/kpis/available', methods=['GET'])
def get_available_kpis_v2():
    """Retorna todos os KPIs disponíveis"""
    from app import get_mock_data
    mock_data = get_mock_data()

    # KPIs Fixos (sempre visíveis)
    fixed_kpis = {
        "vendas_total": {
            "name": "Vendas",
            "description": "Total de vendas do período",
            "icon": "currency-dollar",
            "color": "green",
            "format": "currency",
            "value": mock_data["resumo"].get("vendas_total", 0)
        },
        "producao_total": {
            "name": "Produção",
            "description": "Total de produção do período",
            "icon": "clipboard-document",
            "color": "blue",
            "format": "currency",
            "value": mock_data["resumo"].get("producao_total", 0)
        },
        "agendamentos_mes_corrente": {
            "name": "Agendamentos",
            "description": "Slots agendados no mês",
            "icon": "calendar",
            "color": "purple",
            "format": "number",
            "value": mock_data["resumo"].get("agendamentos_mes_corrente", 0)
        },
        "atendimentos_mes_corrente": {
            "name": "Atendimentos",
            "description": "Atendimentos realizados no mês",
            "icon": "user-group",
            "color": "orange",
            "format": "number",
            "value": mock_data["resumo"].get("atendimentos_mes_corrente", 0)
        }
    }

    # KPIs Opcionais (16)
    optional_kpis = {
        "perc_faltas_cancelamentos": {
            "name": "% Faltas e Cancelamentos",
            "description": "Percentual de faltas e cancelamentos",
            "icon": "x-circle",
            "color": "blue",
            "format": "percentage",
            "value": mock_data["resumo"].get("perc_faltas_cancelamentos", 18.0)
        },
        "perc_recorrencia_finalizadas": {
            "name": "% Recorrência Finalizadas",
            "description": "Regras de recorrência concluídas",
            "icon": "arrow-path",
            "color": "blue",
            "format": "percentage",
            "value": mock_data["resumo"].get("perc_recorrencia_finalizadas", 75.3)
        },
        "perc_conversao_leads": {
            "name": "% Conversão de Leads",
            "description": "Taxa de conversão dos leads",
            "icon": "funnel",
            "color": "blue",
            "format": "percentage",
            "value": mock_data["resumo"].get("perc_conversao_leads", 24.5)
        },
        "perc_orcamentos_fechados": {
            "name": "% Orçamentos Fechados",
            "description": "Percentual de orçamentos fechados",
            "icon": "document-check",
            "color": "blue",
            "format": "percentage",
            "value": mock_data["resumo"].get("perc_orcamentos_fechados", 67.3)
        },
        "creditos_in_house": {
            "name": "Créditos In House",
            "description": "Créditos disponíveis",
            "icon": "credit-card",
            "color": "blue",
            "format": "currency",
            "value": mock_data["resumo"].get("creditos_in_house", 45678.90)
        },
        "base_pacientes_total": {
            "name": "Base de Pacientes",
            "description": "Total de pacientes cadastrados",
            "icon": "users",
            "color": "blue",
            "format": "number",
            "value": mock_data["resumo"].get("base_pacientes_total", 18333)
        },
        "perc_inativos_12m": {
            "name": "% Inativos 12m",
            "description": "Pacientes inativos há 12 meses",
            "icon": "user-minus",
            "color": "blue",
            "format": "percentage",
            "value": mock_data["resumo"].get("perc_inativos_12m", 72.0)
        },
        "nps_periodo_anterior": {
            "name": "NPS",
            "description": "Net Promoter Score",
            "icon": "star",
            "color": "blue",
            "format": "decimal",
            "value": mock_data["resumo"].get("nps_periodo_anterior", 4.92)
        },
        "novos_cadastros_periodo": {
            "name": "Novos Cadastros",
            "description": "Novos cadastros do período",
            "icon": "user-plus",
            "color": "blue",
            "format": "number",
            "value": mock_data["resumo"].get("novos_cadastros_periodo", 83)
        },
        "novos_agendamentos_periodo": {
            "name": "Novos Agendamentos",
            "description": "Agendamentos originados no período",
            "icon": "calendar-plus",
            "color": "blue",
            "format": "number",
            "value": mock_data["resumo"].get("novos_agendamentos_periodo", 444)
        },
        "perc_atendimentos_primeira_vez": {
            "name": "% Atendimentos 1ª Vez",
            "description": "Percentual de primeiros atendimentos",
            "icon": "sparkles",
            "color": "blue",
            "format": "percentage",
            "value": mock_data["resumo"].get("perc_atendimentos_primeira_vez", 18.0)
        },
        "ticket_medio_periodo": {
            "name": "Ticket Médio",
            "description": "Valor médio por atendimento",
            "icon": "banknotes",
            "color": "blue",
            "format": "currency",
            "value": mock_data["resumo"].get("ticket_medio_periodo", 333.55)
        },
        "top_3_procedimentos_vendidos": {
            "name": "Top 3 Procedimentos (Vendas)",
            "description": "Procedimentos mais vendidos",
            "icon": "chart-bar",
            "color": "blue",
            "format": "list",
            "value": mock_data["resumo"].get("top_3_procedimentos_vendidos", [
                {"nome": "Botox", "valor": 480000, "percentual": 32.0},
                {"nome": "Laser CO2", "valor": 315000, "percentual": 21.0},
                {"nome": "Consulta", "valor": 285000, "percentual": 19.0}
            ])
        },
        "top_3_vendedores": {
            "name": "Top 3 Vendedores",
            "description": "Maiores vendedores do período",
            "icon": "trophy",
            "color": "blue",
            "format": "list",
            "value": mock_data["resumo"].get("top_3_vendedores", [
                {"nome": "Dr. Silva", "valor": 480000, "percentual": 32.0},
                {"nome": "Dra. Santos", "valor": 315000, "percentual": 21.0},
                {"nome": "Dr. Oliveira", "valor": 285000, "percentual": 19.0}
            ])
        },
        "top_3_procedimentos_executados": {
            "name": "Top 3 Procedimentos (Execução)",
            "description": "Procedimentos mais executados",
            "icon": "clipboard-document-list",
            "color": "blue",
            "format": "list",
            "value": mock_data["resumo"].get("top_3_procedimentos_executados", [
                {"nome": "Consulta", "quantidade": 45, "percentual": 32.0},
                {"nome": "Botox", "quantidade": 28, "percentual": 21.0},
                {"nome": "Laser CO2", "quantidade": 25, "percentual": 19.0}
            ])
        },
        "top_3_executantes": {
            "name": "Top 3 Executantes",
            "description": "Profissionais mais produtivos",
            "icon": "user-circle",
            "color": "blue",
            "format": "list",
            "value": mock_data["resumo"].get("top_3_executantes", [
                {"nome": "Dr. Silva", "quantidade": 18, "percentual": 32.0},
                {"nome": "Dra. Santos", "quantidade": 12, "percentual": 21.0},
                {"nome": "Dr. Oliveira", "quantidade": 11, "percentual": 19.0}
            ])
        }
    }

    return jsonify({
        "fixed_kpis": fixed_kpis,
        "optional_kpis": optional_kpis
    })

@api.route('/agent/chat', methods=['POST'])
def agent_chat():
    """
    Processa uma pergunta para o agente inteligente usando o Data Mesh.

    Returns:
        JSON: Resposta do agente
    """
    try:
        # Obter dados da requisição
        data = request.json
        question = data.get('question', '')
        context = data.get('context', {})
        conversation_history = data.get('conversation_history', [])

        # Verificar se a pergunta está vazia
        if not question.strip():
            return jsonify({
                "text": "Por favor, faça uma pergunta para que eu possa ajudar.",
                "context": "Pergunta vazia"
            })

        # Importar a função para obter dados mockados diretamente
        from app import get_mock_data

        # Obter dados mockados da função global
        mock_data = get_mock_data()

        # Importar o módulo de integração do Data Mesh
        try:
            from services.data_mesh.agent_integration import process_agent_chat, initialize_agent_with_data

            # Inicializar o agente com os dados mockados
            initialize_agent_with_data(mock_data)

            # Preparar o histórico de conversas no formato esperado pelo Data Mesh
            formatted_history = []
            for msg in conversation_history:
                if msg.get('type') == 'user':
                    formatted_history.append({
                        "role": "user",
                        "content": msg.get('text', '')
                    })
                elif msg.get('type') == 'agent':
                    if isinstance(msg.get('response'), dict) and 'text' in msg.get('response', {}):
                        formatted_history.append({
                            "role": "assistant",
                            "content": msg.get('response', {}).get('text', '')
                        })
                    elif isinstance(msg.get('response'), str):
                        formatted_history.append({
                            "role": "assistant",
                            "content": msg.get('response', '')
                        })

            # Preparar o contexto da página
            page_context = {
                "current_module": context.get('page', {}).get('module', 'geral'),
                "page_title": context.get('page', {}).get('formatted_title', context.get('page', {}).get('title', 'Página')),
                "page_description": context.get('page', {}).get('page_description', ''),
                "key_metrics": context.get('page', {}).get('key_metrics', {}),
                "page_elements": context.get('page', {}).get('page_elements', [])
            }

            # Processar a mensagem usando o Data Mesh
            response = process_agent_chat(question, page_context, formatted_history)

            # Formatar a resposta para o formato esperado pelo frontend
            formatted_response = {
                "text": response.get('text', 'Erro ao gerar resposta'),
                "context": page_context.get('current_module', 'geral'),
                "chart": response.get('chart'),
                "code": response.get('code')
            }

            # Adicionar campos adicionais se existirem na resposta
            if 'markdown' in response:
                formatted_response['markdown'] = response['markdown']

            if 'list' in response:
                formatted_response['list'] = response['list']

            if 'table' in response:
                formatted_response['table'] = response['table']

            return jsonify(formatted_response)

        except ImportError as e:
            print(f"Erro ao importar Data Mesh: {str(e)}. Usando fallback.")
            # Continuar com o método antigo como fallback
            pass

        # FALLBACK: Se o Data Mesh não estiver disponível, usar o método antigo

        # Gerar embeddings para a pergunta e o contexto
        from services import EmbeddingService

        # Embedding da pergunta
        question_embedding = EmbeddingService.create_embedding(question)

        # Embedding do contexto da página
        context_embedding = EmbeddingService.create_context_embedding(context)

        # Calcular similaridade entre a pergunta e o contexto
        similarity_score = EmbeddingService.cosine_similarity(question_embedding, context_embedding)
        print(f"Similaridade entre pergunta e contexto: {similarity_score}")

        # Extrair informações relevantes do histórico de conversas
        relevant_history = []
        if conversation_history:
            # Criar embeddings para as últimas mensagens do histórico
            history_texts = []
            for msg in conversation_history[-5:]:
                if msg.get('type') == 'user':
                    history_texts.append(msg.get('text', ''))
                elif msg.get('type') == 'agent' and isinstance(msg.get('response'), dict) and 'text' in msg.get('response', {}):
                    history_texts.append(msg.get('response', {}).get('text', ''))
                elif msg.get('type') == 'agent' and isinstance(msg.get('response'), str):
                    history_texts.append(msg.get('response', ''))

            if history_texts:
                history_embeddings = EmbeddingService.create_embeddings_batch(history_texts)

                # Encontrar mensagens mais relevantes para a pergunta atual
                if history_embeddings:
                    relevant_items = EmbeddingService.find_most_similar(
                        question_embedding,
                        history_embeddings,
                        history_texts,
                        top_k=3
                    )

                    # Adicionar apenas mensagens com similaridade acima de um limiar
                    for item in relevant_items:
                        if item['similarity'] > 0.7:  # Limiar de similaridade
                            relevant_history.append(item['item'])
                            print(f"Mensagem relevante (score: {item['similarity']}): {item['item'][:50]}...")

        # Preparar mensagens para a OpenAI
        messages = [
            {"role": "system", "content": f"""Você é o Amigo Intelligence, um assistente inteligente do Amigo DataHub.
Você tem acesso ao contexto da página atual e aos dados carregados no sistema.
Responda de forma clara, direta e útil, fornecendo insights relevantes com base nos dados disponíveis.
Você pode responder em diferentes formatos: texto, listas, tabelas e gráficos.
Quando apropriado, sugira ações específicas que o usuário pode tomar com base nos dados.

Contexto da página atual:
Módulo: {context.get('page', {}).get('module', 'Não especificado')}
Página: {context.get('page', {}).get('formatted_title', context.get('page', {}).get('title', 'Não especificada'))}
Descrição: {context.get('page', {}).get('page_description', 'Não especificada')}

Métricas-chave disponíveis:
{json.dumps(context.get('page', {}).get('key_metrics', {}), indent=2, ensure_ascii=False)}

Elementos da página:
{json.dumps(context.get('page', {}).get('page_elements', []), indent=2, ensure_ascii=False)}

Dados disponíveis:
- Agendamentos
- Financeiro
- Pacientes
- AmigoCare+
- Visão 360

Informações relevantes do histórico de conversas:
{json.dumps(relevant_history, indent=2, ensure_ascii=False) if relevant_history else "Nenhuma informação relevante no histórico."}

Responda no formato JSON com os seguintes campos:
{{
  "text": "Texto principal da resposta",
  "markdown": "Conteúdo formatado em markdown (opcional)",
  "list": ["Item 1", "Item 2", "..."] (opcional),
  "table": {{"headers": ["Col1", "Col2"], "rows": [["Valor1", "Valor2"], "..."]}} (opcional),
  "chart": {{"type": "bar|line|pie", "labels": ["..."], "datasets": ["..."]}} (opcional),
  "code": "Código formatado (opcional)",
  "context": "Contexto da resposta"
}}
"""}
        ]

        # Adicionar histórico de conversa
        for msg in conversation_history[-5:]:  # Últimas 5 mensagens para contexto
            if msg.get('type') == 'user':
                messages.append({"role": "user", "content": msg.get('text', '')})
            elif msg.get('type') == 'agent' and isinstance(msg.get('response'), str):
                messages.append({"role": "assistant", "content": msg.get('response', '')})
            elif msg.get('type') == 'agent' and isinstance(msg.get('response'), dict) and 'text' in msg.get('response', {}):
                messages.append({"role": "assistant", "content": msg.get('response', {}).get('text', '')})

        # Adicionar a pergunta atual
        messages.append({"role": "user", "content": question})

        # Obter a chave da API do ambiente (mesma abordagem que o GPTService)
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            print("Chave da API OpenAI não encontrada. Usando resposta de fallback.")
            return jsonify({
                "text": "Desculpe, a chave da API não está configurada. Por favor, configure a chave da API OpenAI.",
                "context": "Erro de configuração"
            })

        # Fazer a requisição para a OpenAI usando a nova sintaxe (versão 1.0.0+)
        try:
            import openai
            client = openai.OpenAI(api_key=api_key)

            response = client.chat.completions.create(
                model="gpt-4o-mini",  # Mesmo modelo usado pelo GPTService
                messages=messages,
                temperature=0.7,
                max_tokens=1000,
                response_format={"type": "json_object"}
            )

            # Extrair a resposta
            ai_response = response.choices[0].message.content

        except ImportError:
            # Fallback para requests se o openai não estiver disponível
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {api_key}"
            }

            payload = {
                "model": "gpt-4o-mini",  # Mesmo modelo usado pelo GPTService
                "messages": messages,
                "temperature": 0.7,
                "max_tokens": 1000,
                "response_format": {"type": "json_object"}
            }

            response = requests.post(OPENAI_API_URL, headers=headers, json=payload)
            response_data = response.json()

            # Extrair a resposta
            if 'choices' in response_data and len(response_data['choices']) > 0:
                ai_response = response_data['choices'][0]['message']['content']
            else:
                return jsonify({
                    "error": "Erro na resposta da API da OpenAI",
                    "details": response_data
                }), 500

            try:
                # Tentar parsear a resposta como JSON
                parsed_response = json.loads(ai_response)

                # Adicionar informação sobre a similaridade com o contexto
                if 'context' in parsed_response:
                    parsed_response['context'] += f" (Relevância: {similarity_score:.2f})"

                return jsonify(parsed_response)
            except json.JSONDecodeError:
                # Se não for um JSON válido, retornar como texto
                return jsonify({
                    "text": ai_response,
                    "context": f"Página: {context.get('page', {}).get('formatted_title', 'DataHub')} (Relevância: {similarity_score:.2f})"
                })

        # Resposta de fallback
        return jsonify({
            "text": "Desculpe, não consegui processar sua pergunta. Por favor, tente novamente.",
            "context": "Erro de processamento"
        })

    except Exception as e:
        print(f"Erro ao processar pergunta: {str(e)}")
        return jsonify({
            "text": f"Ocorreu um erro ao processar sua pergunta: {str(e)}",
            "context": "Erro"
        }), 500

"""
Aplicação Flask para o DataHub Clinic.
"""
from flask import Flask, render_template, session, request, redirect, url_for
import json
import os
import random
import subprocess
from datetime import datetime, timedelta
import calendar
from dotenv import load_dotenv
from flask_session import Session
import redis
from services import InsightService
from services.data_service import DataService
from api import api

# Carregar variáveis de ambiente
load_dotenv()

app = Flask(__name__)
app.secret_key = os.environ.get('SECRET_KEY', 'amigo-datahub-secret-key')

# Tentar configurar o Redis para armazenamento de sessão
try:
    # Primeiro, tente sem SSL
    redis_client = redis.Redis(
        host=os.environ.get('REDIS_HOST', 'localhost'),
        port=int(os.environ.get('REDIS_PORT', 6379)),
        username=os.environ.get('REDIS_USERNAME', ''),
        password=os.environ.get('REDIS_PASSWORD', ''),
        ssl=False,
        socket_timeout=5
    )

    # Teste a conexão
    redis_client.ping()

    # Se chegou aqui, a conexão funcionou
    app.config['SESSION_TYPE'] = 'redis'
    app.config['SESSION_PERMANENT'] = False
    app.config['SESSION_USE_SIGNER'] = True
    app.config['SESSION_REDIS'] = redis_client
    app.config['SESSION_KEY_PREFIX'] = 'amigo_session:'

    print("Conexão com Redis estabelecida com sucesso!")

except Exception as e:
    # Se falhar, use o armazenamento de sessão padrão (cookie)
    print(f"Erro ao conectar ao Redis: {str(e)}")
    print("Usando armazenamento de sessão em cookie (padrão).")
    app.config['SESSION_TYPE'] = 'filesystem'
    app.config['SESSION_FILE_DIR'] = os.path.join(os.getcwd(), 'flask_session')
    app.config['SESSION_FILE_THRESHOLD'] = 100

    # Criar diretório para sessões se não existir
    if not os.path.exists(app.config['SESSION_FILE_DIR']):
        os.makedirs(app.config['SESSION_FILE_DIR'])

# Inicializar o gerenciador de sessão
Session(app)

# Registrar blueprint da API
app.register_blueprint(api, url_prefix='/api')

# Inicializar o Data Mesh
try:
    from services.data_mesh.initialize import initialize_data_mesh_in_app
    initialize_data_mesh_in_app(app)
    print("Data Mesh inicializado com sucesso!")
except Exception as e:
    print(f"Erro ao inicializar Data Mesh: {str(e)}")

# Inicializar a Feature Store
try:
    from services.feature_store.amigostudio_integration import initialize_feature_store_in_app
    initialize_feature_store_in_app(app)
    print("Feature Store inicializada com sucesso!")
except Exception as e:
    print(f"Erro ao inicializar Feature Store: {str(e)}")

# Importar e registrar as rotas do AmigoStudio Pro
try:
    from amigostudio_routes import register_routes
    register_routes(app)
    print("Rotas do AmigoStudio Pro registradas com sucesso!")
except Exception as e:
    print(f"Erro ao registrar rotas do AmigoStudio Pro: {str(e)}")

# Adicionar rotas alternativas para o AmigoStudio Pro
@app.route('/amigostudio_pro')
@app.route('/amigostudio_pro/')
@app.route('/amigostudio-pro-redirect')
def amigostudio_pro_redirect():
    print("Redirecionando para /amigostudio-pro")
    return redirect('/amigostudio-pro')

# Inicializar o serviço de dados
data_service = DataService()

# Carregar configuração de fonte de dados (se existir)
def load_data_source_config():
    config_path = os.path.join(os.path.dirname(__file__), 'config', 'data_source.json')
    if os.path.exists(config_path):
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
            data_service.set_config(config)

# Carregar dados mockados
def load_mock_data():
    data_path = os.path.join(os.path.dirname(__file__), 'data', 'mock_data.json')
    if os.path.exists(data_path):
        with open(data_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            # Garantir que a chave 'amigocare' exista
            if 'amigocare' not in data:
                data['amigocare'] = {
                    "avaliacao_nps": [],
                    "leads": [],
                    "campanhas": [],
                    "acompanhamento_pacientes": [],
                    "indicadores_qualidade": []
                }

            # Garantir que todas as chaves necessárias existam
            if 'acompanhamento_pacientes' not in data['amigocare']:
                data['amigocare']['acompanhamento_pacientes'] = []
            if 'indicadores_qualidade' not in data['amigocare']:
                data['amigocare']['indicadores_qualidade'] = []
            return data
    return {}

# Gerar dados mockados para os relatórios
def generate_mock_data():
    mock_data = {
        "agenda": {
            "agendamentos": [],
            "producao_medica": [],
            "tempo_atendimento": [],
            "cancelamentos": []
        },
        "financeiro": {
            "contas_receber": [],
            "contas_pagar": [],
            "fluxo_caixa": [],
            "fechamento_caixa": []
        },
        "paciente": {
            "atendimentos_realizados": [],
            "creditos_disponiveis": [],
            "orcamentos_fechados": [],
            "orcamentos_abertos": []
        },
        "amigocare": {
            "avaliacao_nps": [],
            "leads": [],
            "campanhas": [],
            "acompanhamento_pacientes": [],
            "indicadores_qualidade": []
        },

        "resumo": {
            "total_agendamentos": 0,
            "total_atendimentos": 0,
            "total_receita": 0,
            "total_despesa": 0,
            "taxa_ocupacao": 0,
            "tempo_medio_atendimento": 0,
            "taxa_cancelamento": 0,
            "procedimentos_populares": [],
            "medicos_produtivos": [],
            "unidades_desempenho": [],
            "total_leads": 0,
            "total_oportunidades": 0,
            "taxa_conversao": 0,
            "universidades_populares": [],
            "leads_por_mes": {},
            "distribuicao_areas": [],
            "distribuicao_origens": [],
            "etapas_funil": [],
            "cursos_populares": []
        }
    }

    # Dados comuns
    unidades = ["Morumbi", "BRASILIA", "Recife", "Espinheiro", "CLÍNICA (RL)"]
    profissionais = [
        "Equipe Amigo - BRUNO LIMA",
        "Equipe Amigo - Caio Menezes",
        "Equipe Amigo - Giulia Pedrosa 2",
        "Equipe Amigo - Ana Victoria Rocha de Almeida",
        "Rayara Toledo Souza",
        "José Pedroza"
    ]
    pacientes = [
        "AMANDA 1704",
        "Daniel Teste",
        "Stephany Figueiredo de Sousa",
        "MARINA HAZIN",
        "Claudio Lemos teste",
        "FABIO FELTRIM",
        "ADRANA TESTE SALVADOR",
        "ADOLFO STURARO TESTE",
        "LUIZ GUSTAVO",
        "DR RAFAEL TESTE",
        "ACRISIO JP TESTE"
    ]
    tipos_atendimento = [
        "Consulta - Amigo tech",
        "BOTOX",
        "Sessao de Fisioterapia",
        "PROCEDIMENTO SIMPLES - 60 MIN",
        "TERAPIA OCUPACIONAL",
        "PSICOLOGIA ABA",
        "Audiometria tonal"
    ]
    formas_pagamento = [
        "AMIL",
        "Crédito de Procedimento",
        "BRADESCO",
        "Cartão",
        "Pix",
        "Dinheiro",
        "Pendente",
        "UNIMED"
    ]
    procedimentos = [
        "Anestesia",
        "Blefaroplastia",
        "Sessao fisioterapia - teste",
        "BOTOX MALAR",
        "Ecodoppler Vertebral ou Vascular Periférico",
        "PROCEDIMENTO RAIZ FISIO",
        "Septoplastia (qualquer técnica sem vídeo)",
        "Mielograma"
    ]
    status_agendamento = ["Agendado", "Confirmado", "Cancelado", "Realizado", "Faltou"]

    # Gerar datas para os últimos 30 dias
    hoje = datetime.now()
    datas = [(hoje - timedelta(days=i)).strftime("%d/%m/%Y") for i in range(30)]

    # Gerar horários de atendimento
    horarios = [f"{h:02d}:{m:02d}" for h in range(8, 19) for m in [0, 20, 40]]

    # Gerar dados para agendamentos
    num_agendamentos = 200
    for i in range(num_agendamentos):
        data = random.choice(datas)
        hora = random.choice(horarios)
        unidade = random.choice(unidades)
        profissional = random.choice(profissionais)
        paciente = random.choice(pacientes)
        tipo_atendimento = random.choice(tipos_atendimento)
        forma_pagamento = random.choice(formas_pagamento)
        procedimento = random.choice(procedimentos)
        status = random.choice(status_agendamento)
        valor = round(random.uniform(50, 2000), 2)

        agendamento = {
            "codigo": 150000000 + i,
            "data": data,
            "hora": hora,
            "unidade": unidade,
            "profissional": profissional,
            "paciente": paciente,
            "tipo_atendimento": tipo_atendimento,
            "procedimento": procedimento,
            "forma_pagamento": forma_pagamento,
            "valor": valor,
            "status": status
        }

        mock_data["agenda"]["agendamentos"].append(agendamento)

        # Adicionar à produção médica se o status for "Realizado"
        if status == "Realizado":
            mock_data["agenda"]["producao_medica"].append({
                "codigo": agendamento["codigo"],
                "data": agendamento["data"],
                "unidade": agendamento["unidade"],
                "profissional": agendamento["profissional"],
                "paciente": agendamento["paciente"],
                "tipo_atendimento": agendamento["tipo_atendimento"],
                "procedimento": agendamento["procedimento"],
                "forma_pagamento": agendamento["forma_pagamento"],
                "valor": agendamento["valor"]
            })

            # Adicionar ao tempo de atendimento
            hora_chegada = (datetime.strptime(hora, "%H:%M") - timedelta(minutes=random.randint(0, 30))).strftime("%H:%M")
            inicio_atendimento = (datetime.strptime(hora_chegada, "%H:%M") + timedelta(minutes=random.randint(1, 20))).strftime("%H:%M")
            fim_atendimento = (datetime.strptime(inicio_atendimento, "%H:%M") + timedelta(minutes=random.randint(5, 60))).strftime("%H:%M")

            espera_minutos = (datetime.strptime(inicio_atendimento, "%H:%M") - datetime.strptime(hora_chegada, "%H:%M")).total_seconds() / 60
            tempo_atendimento = (datetime.strptime(fim_atendimento, "%H:%M") - datetime.strptime(inicio_atendimento, "%H:%M")).total_seconds() / 60

            mock_data["agenda"]["tempo_atendimento"].append({
                "codigo": agendamento["codigo"],
                "data": agendamento["data"],
                "unidade": agendamento["unidade"],
                "profissional": agendamento["profissional"],
                "paciente": agendamento["paciente"],
                "tipo_atendimento": agendamento["tipo_atendimento"],
                "hora_agendamento": agendamento["hora"],
                "hora_chegada": hora_chegada,
                "inicio_atendimento": inicio_atendimento,
                "fim_atendimento": fim_atendimento,
                "espera_minutos": int(espera_minutos),
                "tempo_atendimento": int(tempo_atendimento)
            })

            # Adicionar aos atendimentos realizados
            mock_data["paciente"]["atendimentos_realizados"].append({
                "codigo": agendamento["codigo"],
                "data": agendamento["data"],
                "unidade": agendamento["unidade"],
                "profissional": agendamento["profissional"],
                "paciente": agendamento["paciente"],
                "tipo_atendimento": agendamento["tipo_atendimento"],
                "procedimento": agendamento["procedimento"],
                "forma_pagamento": agendamento["forma_pagamento"],
                "valor": agendamento["valor"]
            })

            # Adicionar às contas a receber se não for convênio
            if forma_pagamento not in ["AMIL", "BRADESCO", "UNIMED"]:
                vencimento = (datetime.strptime(data, "%d/%m/%Y") + timedelta(days=random.randint(1, 30))).strftime("%d/%m/%Y")
                mock_data["financeiro"]["contas_receber"].append({
                    "codigo": agendamento["codigo"],
                    "data_emissao": agendamento["data"],
                    "data_vencimento": vencimento,
                    "paciente": agendamento["paciente"],
                    "descricao": f"{agendamento['tipo_atendimento']} - {agendamento['procedimento']}",
                    "forma_pagamento": agendamento["forma_pagamento"],
                    "valor": agendamento["valor"],
                    "status": random.choice(["Pago", "Pendente", "Atrasado"])
                })

        # Adicionar aos cancelamentos se o status for "Cancelado"
        if status == "Cancelado":
            mock_data["agenda"]["cancelamentos"].append({
                "codigo": agendamento["codigo"],
                "data": agendamento["data"],
                "unidade": agendamento["unidade"],
                "profissional": agendamento["profissional"],
                "paciente": agendamento["paciente"],
                "tipo_atendimento": agendamento["tipo_atendimento"],
                "procedimento": agendamento["procedimento"],
                "motivo_cancelamento": random.choice(["Solicitação do paciente", "Indisponibilidade do médico", "Reagendamento", "Outros"])
            })

    # Gerar dados para contas a pagar
    fornecedores = ["Fornecedor A", "Fornecedor B", "Fornecedor C", "Fornecedor D", "Fornecedor E"]
    categorias = ["Material Médico", "Medicamentos", "Aluguel", "Energia", "Água", "Internet", "Telefone", "Impostos", "Salários"]

    for i in range(50):
        data_emissao = random.choice(datas)
        data_vencimento = (datetime.strptime(data_emissao, "%d/%m/%Y") + timedelta(days=random.randint(15, 45))).strftime("%d/%m/%Y")
        fornecedor = random.choice(fornecedores)
        categoria = random.choice(categorias)
        valor = round(random.uniform(100, 5000), 2)

        mock_data["financeiro"]["contas_pagar"].append({
            "id": i + 1,
            "data_emissao": data_emissao,
            "data_vencimento": data_vencimento,
            "fornecedor": fornecedor,
            "categoria": categoria,
            "descricao": f"Pagamento de {categoria.lower()}",
            "valor": valor,
            "status": random.choice(["Pago", "Pendente", "Atrasado"])
        })

    # Gerar dados para fluxo de caixa
    meses = list(calendar.month_name)[1:]
    receitas_mensais = [round(random.uniform(50000, 100000), 2) for _ in range(12)]
    despesas_mensais = [round(random.uniform(30000, 70000), 2) for _ in range(12)]

    for i in range(12):
        mock_data["financeiro"]["fluxo_caixa"].append({
            "mes": meses[i],
            "receita": receitas_mensais[i],
            "despesa": despesas_mensais[i],
            "resultado": receitas_mensais[i] - despesas_mensais[i]
        })

    # Gerar dados para fechamento de caixa
    for i in range(30):
        data = datas[i]
        dinheiro = round(random.uniform(500, 3000), 2)
        cartao_credito = round(random.uniform(1000, 5000), 2)
        cartao_debito = round(random.uniform(500, 3000), 2)
        pix = round(random.uniform(1000, 4000), 2)
        cheque = round(random.uniform(0, 1000), 2)
        total = dinheiro + cartao_credito + cartao_debito + pix + cheque

        mock_data["financeiro"]["fechamento_caixa"].append({
            "data": data,
            "dinheiro": dinheiro,
            "cartao_credito": cartao_credito,
            "cartao_debito": cartao_debito,
            "pix": pix,
            "cheque": cheque,
            "total": total
        })

    # Gerar dados para créditos disponíveis
    for paciente in pacientes:
        credito = round(random.uniform(0, 5000), 2)
        if credito > 0:
            mock_data["paciente"]["creditos_disponiveis"].append({
                "paciente": paciente,
                "credito_disponivel": credito,
                "ultima_atualizacao": random.choice(datas)
            })

    # Gerar dados para orçamentos
    for i in range(30):
        paciente = random.choice(pacientes)
        data = random.choice(datas)
        profissional = random.choice(profissionais)
        valor_total = round(random.uniform(500, 10000), 2)
        desconto = round(random.uniform(0, valor_total * 0.2), 2)
        valor_final = valor_total - desconto

        # Orçamentos fechados
        if i < 15:
            mock_data["paciente"]["orcamentos_fechados"].append({
                "id": i + 1,
                "paciente": paciente,
                "data": data,
                "profissional": profissional,
                "procedimentos": [random.choice(procedimentos) for _ in range(random.randint(1, 5))],
                "valor_total": valor_total,
                "desconto": desconto,
                "valor_final": valor_final,
                "forma_pagamento": random.choice(formas_pagamento),
                "status": "Aprovado"
            })
        # Orçamentos abertos
        else:
            mock_data["paciente"]["orcamentos_abertos"].append({
                "id": i + 1,
                "paciente": paciente,
                "data": data,
                "profissional": profissional,
                "procedimentos": [random.choice(procedimentos) for _ in range(random.randint(1, 5))],
                "valor_total": valor_total,
                "desconto": desconto,
                "valor_final": valor_final,
                "validade": (datetime.strptime(data, "%d/%m/%Y") + timedelta(days=30)).strftime("%d/%m/%Y"),
                "status": random.choice(["Em análise", "Aguardando aprovação", "Enviado"])
            })

    # Calcular resumo
    mock_data["resumo"]["total_agendamentos"] = len(mock_data["agenda"]["agendamentos"])
    mock_data["resumo"]["total_atendimentos"] = len(mock_data["agenda"]["producao_medica"])
    mock_data["resumo"]["total_receita"] = sum(item["valor"] for item in mock_data["agenda"]["producao_medica"])
    mock_data["resumo"]["total_despesa"] = sum(item["valor"] for item in mock_data["financeiro"]["contas_pagar"])

    if mock_data["resumo"]["total_agendamentos"] > 0:
        mock_data["resumo"]["taxa_ocupacao"] = round((mock_data["resumo"]["total_atendimentos"] / mock_data["resumo"]["total_agendamentos"]) * 100, 2)

    if mock_data["agenda"]["tempo_atendimento"]:
        mock_data["resumo"]["tempo_medio_atendimento"] = round(sum(item["tempo_atendimento"] for item in mock_data["agenda"]["tempo_atendimento"]) / len(mock_data["agenda"]["tempo_atendimento"]), 2)

    if mock_data["resumo"]["total_agendamentos"] > 0:
        mock_data["resumo"]["taxa_cancelamento"] = round((len(mock_data["agenda"]["cancelamentos"]) / mock_data["resumo"]["total_agendamentos"]) * 100, 2)

    # Procedimentos populares
    proc_count = {}
    for item in mock_data["agenda"]["producao_medica"]:
        proc = item["procedimento"]
        if proc in proc_count:
            proc_count[proc] += 1
        else:
            proc_count[proc] = 1

    mock_data["resumo"]["procedimentos_populares"] = [{"nome": k, "quantidade": v} for k, v in sorted(proc_count.items(), key=lambda x: x[1], reverse=True)[:5]]

    # Médicos produtivos
    med_count = {}
    for item in mock_data["agenda"]["producao_medica"]:
        med = item["profissional"]
        if med in med_count:
            med_count[med] += item["valor"]
        else:
            med_count[med] = item["valor"]

    mock_data["resumo"]["medicos_produtivos"] = [{"nome": k, "valor": round(v, 2)} for k, v in sorted(med_count.items(), key=lambda x: x[1], reverse=True)[:5]]

    # Desempenho por unidade
    unid_count = {}
    for item in mock_data["agenda"]["producao_medica"]:
        unid = item["unidade"]
        if unid in unid_count:
            unid_count[unid] += item["valor"]
        else:
            unid_count[unid] = item["valor"]

    mock_data["resumo"]["unidades_desempenho"] = [{"nome": k, "valor": round(v, 2)} for k, v in sorted(unid_count.items(), key=lambda x: x[1], reverse=True)]

    # Calcular dados para a página inicial (leads, oportunidades, etc.)
    # Total de leads (usando os leads do módulo amigocare)
    mock_data["resumo"]["total_leads"] = len(mock_data["amigocare"]["leads"])

    # Total de oportunidades (usando as campanhas como oportunidades)
    mock_data["resumo"]["total_oportunidades"] = len(mock_data["amigocare"]["campanhas"])

    # Taxa de conversão
    if mock_data["resumo"]["total_oportunidades"] > 0:
        # Contar oportunidades convertidas (campanhas concluídas)
        oportunidades_convertidas = len([c for c in mock_data["amigocare"]["campanhas"] if c["status"] == "Concluída"])
        mock_data["resumo"]["taxa_conversao"] = round((oportunidades_convertidas / mock_data["resumo"]["total_oportunidades"]) * 100, 2)

    # Universidades populares (usando unidades como universidades)
    mock_data["resumo"]["universidades_populares"] = [
        {
            "id": i + 1,
            "nome": unidade,
            "sigla": unidade[:3].upper(),
            "quantidade": random.randint(5, 50),
            "percentual": round(random.uniform(5, 20), 2)
        }
        for i, unidade in enumerate(unidades)
    ]

    # Leads por mês
    meses = ["Janeiro", "Fevereiro", "Março", "Abril", "Maio", "Junho", "Julho", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro"]
    mock_data["resumo"]["leads_por_mes"] = {mes: random.randint(10, 100) for mes in meses}

    # Distribuição por áreas de formação
    areas_formacao = ["Medicina", "Enfermagem", "Fisioterapia", "Nutrição", "Psicologia", "Odontologia", "Farmácia", "Biomedicina"]
    mock_data["resumo"]["distribuicao_areas"] = [
        {"area": area, "quantidade": random.randint(5, 50), "percentual": round(random.uniform(5, 20), 2)}
        for area in areas_formacao
    ]

    # Distribuição por origens
    origens = ["Site", "Indicação", "Google", "Instagram", "Facebook", "WhatsApp", "Eventos", "Parcerias"]
    mock_data["resumo"]["distribuicao_origens"] = [
        {"origem": origem, "quantidade": random.randint(5, 50), "percentual": round(random.uniform(5, 20), 2)}
        for origem in origens
    ]

    # Etapas do funil
    etapas_funil = ["Novo Lead", "Primeiro Contato", "Apresentação", "Proposta", "Negociação", "Fechamento"]
    mock_data["resumo"]["etapas_funil"] = [
        {"etapa": etapa, "quantidade": random.randint(5, 50), "percentual": round(random.uniform(5, 20), 2)}
        for etapa in etapas_funil
    ]

    # Cursos populares
    cursos = ["Medicina", "Enfermagem", "Fisioterapia", "Nutrição", "Psicologia", "Odontologia", "Farmácia", "Biomedicina", "Educação Física", "Terapia Ocupacional"]
    mock_data["resumo"]["cursos_populares"] = [
        {"curso": curso, "quantidade": random.randint(5, 50)}
        for curso in cursos
    ]

    # Gerar dados para Amigo Care+
    # Avaliação NPS
    nps_scores = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    nps_categorias = ["Atendimento", "Instalações", "Equipe médica", "Tempo de espera", "Limpeza", "Conforto"]

    # Dados para Leads e Campanhas
    origens_lead = ["Site", "Indicação", "Google", "Instagram", "Facebook", "WhatsApp"]
    status_lead = ["Novo", "Contatado", "Em negociação", "Convertido", "Perdido"]
    tipos_campanha = ["E-mail Marketing", "SMS", "Redes Sociais", "Google Ads", "Eventos", "Parcerias"]
    status_campanha = ["Planejada", "Em andamento", "Concluída", "Cancelada"]

    for i in range(100):
        data = random.choice(datas)
        paciente = random.choice(pacientes)
        unidade = random.choice(unidades)
        categoria = random.choice(nps_categorias)
        score = random.choice(nps_scores)
        comentario = ""

        if score <= 6:
            comentario = random.choice([
                "Precisam melhorar o tempo de espera.",
                "Atendimento abaixo do esperado.",
                "Instalações poderiam ser melhores.",
                "Não fui bem atendido.",
                "Demorou muito para ser atendido."
            ])
        elif score <= 8:
            comentario = random.choice([
                "Bom atendimento, mas pode melhorar.",
                "Equipe atenciosa, mas demorou um pouco.",
                "Gostei do atendimento, mas o espaço é pequeno.",
                "Médicos bons, mas recepção poderia ser melhor."
            ])
        else:
            comentario = random.choice([
                "Excelente atendimento!",
                "Equipe muito atenciosa e profissional.",
                "Instalações impecáveis.",
                "Recomendo a todos.",
                "Melhor clínica que já fui."
            ])

        mock_data["amigocare"]["avaliacao_nps"].append({
            "id": i + 1,
            "data": data,
            "paciente": paciente,
            "unidade": unidade,
            "categoria": categoria,
            "score": score,
            "comentario": comentario
        })

    # Acompanhamento de pacientes
    status_acompanhamento = ["Em tratamento", "Concluído", "Interrompido", "Aguardando retorno"]
    tipos_tratamento = ["Fisioterapia", "Fonoaudiologia", "Terapia Ocupacional", "Psicologia", "Nutrição"]

    for i in range(50):
        paciente = random.choice(pacientes)
        profissional = random.choice(profissionais)
        tipo_tratamento = random.choice(tipos_tratamento)
        data_inicio = random.choice(datas)

        # Calcular data de término (se aplicável)
        if random.random() > 0.3:  # 70% dos tratamentos têm data de término
            dias_tratamento = random.randint(10, 90)
            data_termino = (datetime.strptime(data_inicio, "%d/%m/%Y") + timedelta(days=dias_tratamento)).strftime("%d/%m/%Y")
        else:
            data_termino = ""

        status = random.choice(status_acompanhamento)
        if data_termino and status in ["Em tratamento", "Aguardando retorno"]:
            status = random.choice(["Concluído", "Interrompido"])
        elif not data_termino and status in ["Concluído", "Interrompido"]:
            status = random.choice(["Em tratamento", "Aguardando retorno"])

        # Gerar evolução do paciente
        evolucao = []
        num_sessoes = random.randint(1, 10)
        for j in range(num_sessoes):
            data_sessao = (datetime.strptime(data_inicio, "%d/%m/%Y") + timedelta(days=j*7)).strftime("%d/%m/%Y")
            if data_termino and data_sessao > data_termino:
                continue

            evolucao.append({
                "data": data_sessao,
                "observacao": random.choice([
                    "Paciente apresentou melhora.",
                    "Mantém quadro estável.",
                    "Evolução positiva nas atividades.",
                    "Necessita continuar o tratamento.",
                    "Boa adesão às orientações."
                ])
            })

        mock_data["amigocare"]["acompanhamento_pacientes"].append({
            "id": i + 1,
            "paciente": paciente,
            "profissional": profissional,
            "tipo_tratamento": tipo_tratamento,
            "data_inicio": data_inicio,
            "data_termino": data_termino,
            "status": status,
            "evolucao": evolucao,
            "resultado": random.choice(["Excelente", "Bom", "Regular", "Insatisfatório"]) if status == "Concluído" else ""
        })

    # Gerar dados para Leads
    for i in range(80):
        data = random.choice(datas)
        nome = random.choice(pacientes)
        telefone = f"({random.randint(10, 99)}) {random.randint(90000, 99999)}-{random.randint(1000, 9999)}"
        email = f"{nome.lower().replace(' ', '.')}{random.randint(1, 999)}@email.com"
        origem = random.choice(origens_lead)
        status = random.choice(status_lead)
        interesse = random.choice(procedimentos)
        valor_potencial = round(random.uniform(500, 5000), 2) if status != "Perdido" else 0

        # Data de último contato (se aplicável)
        if status != "Novo":
            dias_desde_lead = random.randint(1, 15)
            data_ultimo_contato = (datetime.strptime(data, "%d/%m/%Y") + timedelta(days=dias_desde_lead)).strftime("%d/%m/%Y")
        else:
            data_ultimo_contato = ""

        # Probabilidade de conversão
        if status == "Convertido":
            probabilidade = 100
        elif status == "Perdido":
            probabilidade = 0
        elif status == "Em negociação":
            probabilidade = random.randint(50, 90)
        elif status == "Contatado":
            probabilidade = random.randint(20, 60)
        else:  # Novo
            probabilidade = random.randint(5, 30)

        mock_data["amigocare"]["leads"].append({
            "id": i + 1,
            "data": data,
            "nome": nome,
            "telefone": telefone,
            "email": email,
            "origem": origem,
            "status": status,
            "interesse": interesse,
            "valor_potencial": valor_potencial,
            "data_ultimo_contato": data_ultimo_contato,
            "probabilidade": probabilidade,
            "observacoes": random.choice([
                "Cliente interessado em agendar consulta",
                "Solicitou mais informações sobre preços",
                "Indicado por outro paciente",
                "Deseja conhecer a clínica antes de agendar",
                "Busca tratamento específico",
                "Comparando preços com concorrentes",
                "Tem convênio e quer saber se é aceito",
                "Não atendeu as últimas ligações",
                ""
            ])
        })

    # Gerar dados para Campanhas
    meses = ["Janeiro", "Fevereiro", "Março", "Abril", "Maio", "Junho", "Julho", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro"]

    for i in range(20):
        nome = f"Campanha {random.choice(['Promoção', 'Lançamento', 'Fidelização', 'Divulgação'])} {random.choice(procedimentos)}"
        tipo = random.choice(tipos_campanha)
        status = random.choice(status_campanha)

        # Datas da campanha
        mes_inicio = random.randint(0, 11)
        data_inicio = f"01/{mes_inicio+1:02d}/2023"
        duracao_dias = random.randint(7, 60)
        data_fim = (datetime.strptime(data_inicio, "%d/%m/%Y") + timedelta(days=duracao_dias)).strftime("%d/%m/%Y")

        # Resultados da campanha
        publico_alvo = random.randint(500, 10000)
        alcance = int(publico_alvo * random.uniform(0.3, 0.9))
        leads_gerados = int(alcance * random.uniform(0.01, 0.2))
        conversoes = int(leads_gerados * random.uniform(0.05, 0.3))
        custo = round(random.uniform(500, 10000), 2)
        receita = round(conversoes * random.uniform(200, 2000), 2) if status == "Concluída" else 0
        roi = round((receita - custo) / custo * 100, 2) if custo > 0 and status == "Concluída" else 0

        mock_data["amigocare"]["campanhas"].append({
            "id": i + 1,
            "nome": nome,
            "tipo": tipo,
            "status": status,
            "data_inicio": data_inicio,
            "data_fim": data_fim,
            "publico_alvo": publico_alvo,
            "alcance": alcance,
            "leads_gerados": leads_gerados,
            "conversoes": conversoes,
            "custo": custo,
            "receita": receita,
            "roi": roi,
            "descricao": random.choice([
                f"Campanha para divulgação de {random.choice(procedimentos)}",
                "Promoção especial para pacientes antigos",
                "Campanha de indicação de amigos",
                "Divulgação de novo tratamento",
                "Campanha sazonal de fim de ano",
                "Parceria com empresas locais",
                "Campanha de conscientização de saúde"
            ])
        })



    # Salvar dados mockados
    data_dir = os.path.join(os.path.dirname(__file__), 'data')
    os.makedirs(data_dir, exist_ok=True)

    with open(os.path.join(data_dir, 'mock_data.json'), 'w', encoding='utf-8') as f:
        json.dump(mock_data, f, ensure_ascii=False, indent=2)

    return mock_data

# Carregar ou gerar dados mockados
mock_data = load_mock_data()
if not mock_data:
    mock_data = generate_mock_data()

# Garantir que a estrutura do módulo AmigoCare+ exista
if 'amigocare' not in mock_data:
    mock_data['amigocare'] = {
        "avaliacao_nps": [],
        "leads": [],
        "campanhas": [],
        "acompanhamento_pacientes": [],
        "indicadores_qualidade": []
    }

# Gerar dados para os módulos se não existirem
if not mock_data:
    mock_data = generate_mock_data()
else:
    # Garantir que todas as chaves necessárias existam
    if 'acompanhamento_pacientes' not in mock_data['amigocare']:
        mock_data['amigocare']['acompanhamento_pacientes'] = []
    if 'indicadores_qualidade' not in mock_data['amigocare']:
        mock_data['amigocare']['indicadores_qualidade'] = []

# Rotas
@app.route('/')
def index():
    # Garantir que os dados mockados estejam atualizados
    global mock_data

    # Garantir que os campos necessários para a página inicial existam
    if "resumo" not in mock_data:
        mock_data["resumo"] = {}

    # ===== KPIs FIXOS (4) =====

    # 1. VENDAS - Total do relatório de vendas (R$)
    if "vendas_total" not in mock_data["resumo"]:
        if "agenda" in mock_data and "producao_medica" in mock_data["agenda"]:
            vendas_total = sum(item["valor"] for item in mock_data["agenda"]["producao_medica"])
            mock_data["resumo"]["vendas_total"] = round(vendas_total, 2)
        else:
            mock_data["resumo"]["vendas_total"] = 1500303.22  # Valor mockado

    # 2. PRODUÇÃO - Total do relatório de produção (R$)
    if "producao_total" not in mock_data["resumo"]:
        if "agenda" in mock_data and "producao_medica" in mock_data["agenda"]:
            # Produção é um percentual das vendas (simulando custos)
            producao_total = mock_data["resumo"]["vendas_total"] * 0.15  # 15% das vendas
            mock_data["resumo"]["producao_total"] = round(producao_total, 2)
        else:
            mock_data["resumo"]["producao_total"] = 23303.44  # Valor mockado

    # 3. AGENDAMENTOS - Total de slots agendados para o mês corrente
    if "agendamentos_mes_corrente" not in mock_data["resumo"]:
        if "agenda" in mock_data and "agendamentos" in mock_data["agenda"]:
            mes_atual = datetime.now().month
            ano_atual = datetime.now().year
            agendamentos_mes = len([a for a in mock_data["agenda"]["agendamentos"]
                                  if datetime.strptime(a["data"], "%d/%m/%Y").month == mes_atual
                                  and datetime.strptime(a["data"], "%d/%m/%Y").year == ano_atual])
            mock_data["resumo"]["agendamentos_mes_corrente"] = agendamentos_mes
        else:
            mock_data["resumo"]["agendamentos_mes_corrente"] = 457  # Valor mockado

    # 4. ATENDIMENTOS - Total de atendidos para o mês corrente
    if "atendimentos_mes_corrente" not in mock_data["resumo"]:
        if "agenda" in mock_data and "agendamentos" in mock_data["agenda"]:
            mes_atual = datetime.now().month
            ano_atual = datetime.now().year
            atendimentos_mes = len([a for a in mock_data["agenda"]["agendamentos"]
                                  if a["status"] == "Realizado"
                                  and datetime.strptime(a["data"], "%d/%m/%Y").month == mes_atual
                                  and datetime.strptime(a["data"], "%d/%m/%Y").year == ano_atual])
            mock_data["resumo"]["atendimentos_mes_corrente"] = atendimentos_mes
        else:
            mock_data["resumo"]["atendimentos_mes_corrente"] = 31  # Valor mockado

    # ===== KPIs OPCIONAIS (16) =====

    # 1. % de faltas e cancelamentos do período
    if "perc_faltas_cancelamentos" not in mock_data["resumo"]:
        if "agenda" in mock_data and "agendamentos" in mock_data["agenda"]:
            total_agendamentos = mock_data["resumo"]["agendamentos_mes_corrente"]
            faltas_cancelamentos = len([a for a in mock_data["agenda"]["agendamentos"]
                                      if a["status"] in ["Cancelado", "Faltou"]])
            if total_agendamentos > 0:
                perc = round((faltas_cancelamentos / total_agendamentos) * 100, 1)
                mock_data["resumo"]["perc_faltas_cancelamentos"] = perc
            else:
                mock_data["resumo"]["perc_faltas_cancelamentos"] = 18.0
        else:
            mock_data["resumo"]["perc_faltas_cancelamentos"] = 18.0  # Valor mockado

    # 2. % de regras de recorrência finalizadas (simulado)
    if "perc_recorrencia_finalizadas" not in mock_data["resumo"]:
        mock_data["resumo"]["perc_recorrencia_finalizadas"] = round(random.uniform(65, 85), 1)

    # 3. % de conversão dos leads do período
    if "perc_conversao_leads" not in mock_data["resumo"]:
        if "amigocare" in mock_data and "leads" in mock_data["amigocare"]:
            leads_convertidos = len([l for l in mock_data["amigocare"]["leads"] if l["status"] == "Convertido"])
            total_leads = len(mock_data["amigocare"]["leads"])
            if total_leads > 0:
                perc = round((leads_convertidos / total_leads) * 100, 1)
                mock_data["resumo"]["perc_conversao_leads"] = perc
            else:
                mock_data["resumo"]["perc_conversao_leads"] = 24.5
        else:
            mock_data["resumo"]["perc_conversao_leads"] = 24.5  # Valor mockado

    # 4. % de orçamentos fechados no período
    if "perc_orcamentos_fechados" not in mock_data["resumo"]:
        if "paciente" in mock_data:
            valor_fechados = sum(o["valor_final"] for o in mock_data["paciente"].get("orcamentos_fechados", []))
            valor_abertos = sum(o["valor_final"] for o in mock_data["paciente"].get("orcamentos_abertos", []))
            total_orcamentos = valor_fechados + valor_abertos
            if total_orcamentos > 0:
                perc = round((valor_fechados / total_orcamentos) * 100, 1)
                mock_data["resumo"]["perc_orcamentos_fechados"] = perc
            else:
                mock_data["resumo"]["perc_orcamentos_fechados"] = 67.3
        else:
            mock_data["resumo"]["perc_orcamentos_fechados"] = 67.3  # Valor mockado

    # 5. Créditos in house (R$)
    if "creditos_in_house" not in mock_data["resumo"]:
        if "paciente" in mock_data and "creditos_disponiveis" in mock_data["paciente"]:
            creditos_total = sum(c["credito_disponivel"] for c in mock_data["paciente"]["creditos_disponiveis"])
            mock_data["resumo"]["creditos_in_house"] = round(creditos_total, 2)
        else:
            mock_data["resumo"]["creditos_in_house"] = 45678.90  # Valor mockado

    # 6. Base de pacientes total
    if "base_pacientes_total" not in mock_data["resumo"]:
        if "paciente" in mock_data and "atendimentos_realizados" in mock_data["paciente"]:
            pacientes_unicos = set(item["paciente"] for item in mock_data["paciente"]["atendimentos_realizados"])
            # Simular base maior que apenas os atendimentos recentes
            mock_data["resumo"]["base_pacientes_total"] = len(pacientes_unicos) * 150  # Multiplicador para simular base histórica
        else:
            mock_data["resumo"]["base_pacientes_total"] = 18333  # Valor mockado

    # 7. % de inativos há 12m
    if "perc_inativos_12m" not in mock_data["resumo"]:
        mock_data["resumo"]["perc_inativos_12m"] = round(random.uniform(65, 75), 1)

    # 8. NPS (média do mês/trimestre anterior)
    if "nps_periodo_anterior" not in mock_data["resumo"]:
        if "amigocare" in mock_data and "avaliacao_nps" in mock_data["amigocare"]:
            avaliacoes = mock_data["amigocare"]["avaliacao_nps"]
            if avaliacoes:
                scores = [a["score"] for a in avaliacoes]
                nps_medio = round(sum(scores) / len(scores), 2)
                mock_data["resumo"]["nps_periodo_anterior"] = nps_medio
            else:
                mock_data["resumo"]["nps_periodo_anterior"] = 4.92
        else:
            mock_data["resumo"]["nps_periodo_anterior"] = 4.92  # Valor mockado

    # 9. Novos cadastros do período
    if "novos_cadastros_periodo" not in mock_data["resumo"]:
        mock_data["resumo"]["novos_cadastros_periodo"] = random.randint(75, 95)

    # 10. Novos agendamentos NO período (originados para qualquer futuro)
    if "novos_agendamentos_periodo" not in mock_data["resumo"]:
        mock_data["resumo"]["novos_agendamentos_periodo"] = random.randint(400, 500)

    # 11. % de atendimentos de 1ª vez
    if "perc_atendimentos_primeira_vez" not in mock_data["resumo"]:
        mock_data["resumo"]["perc_atendimentos_primeira_vez"] = round(random.uniform(15, 25), 1)

    # 12. Ticket médio do período
    if "ticket_medio_periodo" not in mock_data["resumo"]:
        if mock_data["resumo"]["atendimentos_mes_corrente"] > 0:
            ticket = mock_data["resumo"]["vendas_total"] / mock_data["resumo"]["atendimentos_mes_corrente"]
            mock_data["resumo"]["ticket_medio_periodo"] = round(ticket, 2)
        else:
            mock_data["resumo"]["ticket_medio_periodo"] = 333.55  # Valor mockado

    # 13. % de vendas dos 3 tipos de procedimento mais VENDIDOS
    if "top_3_procedimentos_vendidos" not in mock_data["resumo"]:
        procedimentos_vendas = {}
        if "agenda" in mock_data and "producao_medica" in mock_data["agenda"]:
            for item in mock_data["agenda"]["producao_medica"]:
                proc = item["procedimento"]
                if proc in procedimentos_vendas:
                    procedimentos_vendas[proc] += item["valor"]
                else:
                    procedimentos_vendas[proc] = item["valor"]

            # Ordenar por valor e pegar top 3
            top_3 = sorted(procedimentos_vendas.items(), key=lambda x: x[1], reverse=True)[:3]
            total_vendas = sum(procedimentos_vendas.values())

            if total_vendas > 0:
                mock_data["resumo"]["top_3_procedimentos_vendidos"] = [
                    {
                        "nome": proc,
                        "valor": valor,
                        "percentual": round((valor / total_vendas) * 100, 1)
                    }
                    for proc, valor in top_3
                ]
            else:
                mock_data["resumo"]["top_3_procedimentos_vendidos"] = [
                    {"nome": "Botox", "valor": 480000, "percentual": 32.0},
                    {"nome": "Laser CO2", "valor": 315000, "percentual": 21.0},
                    {"nome": "Consulta", "valor": 285000, "percentual": 19.0}
                ]
        else:
            mock_data["resumo"]["top_3_procedimentos_vendidos"] = [
                {"nome": "Botox", "valor": 480000, "percentual": 32.0},
                {"nome": "Laser CO2", "valor": 315000, "percentual": 21.0},
                {"nome": "Consulta", "valor": 285000, "percentual": 19.0}
            ]

    # 14. % dos 3 maiores vendedores (editor financeiro)
    if "top_3_vendedores" not in mock_data["resumo"]:
        vendedores_vendas = {}
        if "agenda" in mock_data and "producao_medica" in mock_data["agenda"]:
            for item in mock_data["agenda"]["producao_medica"]:
                vendedor = item["profissional"]
                if vendedor in vendedores_vendas:
                    vendedores_vendas[vendedor] += item["valor"]
                else:
                    vendedores_vendas[vendedor] = item["valor"]

            # Ordenar por valor e pegar top 3
            top_3 = sorted(vendedores_vendas.items(), key=lambda x: x[1], reverse=True)[:3]
            total_vendas = sum(vendedores_vendas.values())

            if total_vendas > 0:
                mock_data["resumo"]["top_3_vendedores"] = [
                    {
                        "nome": vendedor.replace("Equipe Amigo - ", ""),
                        "valor": valor,
                        "percentual": round((valor / total_vendas) * 100, 1)
                    }
                    for vendedor, valor in top_3
                ]
            else:
                mock_data["resumo"]["top_3_vendedores"] = [
                    {"nome": "Dr. Silva", "valor": 480000, "percentual": 32.0},
                    {"nome": "Dra. Santos", "valor": 315000, "percentual": 21.0},
                    {"nome": "Dr. Oliveira", "valor": 285000, "percentual": 19.0}
                ]
        else:
            mock_data["resumo"]["top_3_vendedores"] = [
                {"nome": "Dr. Silva", "valor": 480000, "percentual": 32.0},
                {"nome": "Dra. Santos", "valor": 315000, "percentual": 21.0},
                {"nome": "Dr. Oliveira", "valor": 285000, "percentual": 19.0}
            ]

    # 15. % de vendas dos 3 tipos de procedimento mais EXECUTADOS (por quantidade)
    if "top_3_procedimentos_executados" not in mock_data["resumo"]:
        procedimentos_qtd = {}
        if "agenda" in mock_data and "producao_medica" in mock_data["agenda"]:
            for item in mock_data["agenda"]["producao_medica"]:
                proc = item["procedimento"]
                if proc in procedimentos_qtd:
                    procedimentos_qtd[proc] += 1
                else:
                    procedimentos_qtd[proc] = 1

            # Ordenar por quantidade e pegar top 3
            top_3 = sorted(procedimentos_qtd.items(), key=lambda x: x[1], reverse=True)[:3]
            total_execucoes = sum(procedimentos_qtd.values())

            if total_execucoes > 0:
                mock_data["resumo"]["top_3_procedimentos_executados"] = [
                    {
                        "nome": proc,
                        "quantidade": qtd,
                        "percentual": round((qtd / total_execucoes) * 100, 1)
                    }
                    for proc, qtd in top_3
                ]
            else:
                mock_data["resumo"]["top_3_procedimentos_executados"] = [
                    {"nome": "Consulta", "quantidade": 45, "percentual": 32.0},
                    {"nome": "Botox", "quantidade": 28, "percentual": 21.0},
                    {"nome": "Laser CO2", "quantidade": 25, "percentual": 19.0}
                ]
        else:
            mock_data["resumo"]["top_3_procedimentos_executados"] = [
                {"nome": "Consulta", "quantidade": 45, "percentual": 32.0},
                {"nome": "Botox", "quantidade": 28, "percentual": 21.0},
                {"nome": "Laser CO2", "quantidade": 25, "percentual": 19.0}
            ]

    # 16. % dos 3 maiores executantes (profissional por quantidade)
    if "top_3_executantes" not in mock_data["resumo"]:
        executantes_qtd = {}
        if "agenda" in mock_data and "producao_medica" in mock_data["agenda"]:
            for item in mock_data["agenda"]["producao_medica"]:
                executante = item["profissional"]
                if executante in executantes_qtd:
                    executantes_qtd[executante] += 1
                else:
                    executantes_qtd[executante] = 1

            # Ordenar por quantidade e pegar top 3
            top_3 = sorted(executantes_qtd.items(), key=lambda x: x[1], reverse=True)[:3]
            total_execucoes = sum(executantes_qtd.values())

            if total_execucoes > 0:
                mock_data["resumo"]["top_3_executantes"] = [
                    {
                        "nome": executante.replace("Equipe Amigo - ", ""),
                        "quantidade": qtd,
                        "percentual": round((qtd / total_execucoes) * 100, 1)
                    }
                    for executante, qtd in top_3
                ]
            else:
                mock_data["resumo"]["top_3_executantes"] = [
                    {"nome": "Dr. Silva", "quantidade": 18, "percentual": 32.0},
                    {"nome": "Dra. Santos", "quantidade": 12, "percentual": 21.0},
                    {"nome": "Dr. Oliveira", "quantidade": 11, "percentual": 19.0}
                ]
        else:
            mock_data["resumo"]["top_3_executantes"] = [
                {"nome": "Dr. Silva", "quantidade": 18, "percentual": 32.0},
                {"nome": "Dra. Santos", "quantidade": 12, "percentual": 21.0},
                {"nome": "Dr. Oliveira", "quantidade": 11, "percentual": 19.0}
            ]

    # Dados para estatísticas adicionais
    # 1. Leads por Origem (manter, mas garantir que exista)
    if "distribuicao_origens" not in mock_data["resumo"]:
        origens = ["Instagram", "Google", "Indicação", "Facebook", "WhatsApp", "Site"]
        mock_data["resumo"]["distribuicao_origens"] = [
            {"origem": origem, "quantidade": random.randint(5, 50), "percentual": round(random.uniform(5, 20), 2)}
            for origem in origens
        ]

    # 2. Tempo Médio de Atendimento
    if "tempo_medio_atendimento" not in mock_data["resumo"]:
        if "agenda" in mock_data and "tempo_atendimento" in mock_data["agenda"]:
            tempos = [item["tempo_atendimento"] for item in mock_data["agenda"]["tempo_atendimento"]]
            if tempos:
                mock_data["resumo"]["tempo_medio_atendimento"] = round(sum(tempos) / len(tempos), 1)
            else:
                mock_data["resumo"]["tempo_medio_atendimento"] = 32  # Valor mockado para clínica de alto nível (consultas mais completas)
        else:
            mock_data["resumo"]["tempo_medio_atendimento"] = 32  # Valor mockado para clínica de alto nível (consultas mais completas)

    # 3. Ticket Médio
    if "ticket_medio" not in mock_data["resumo"]:
        if "financeiro" in mock_data and "contas_receber" in mock_data["financeiro"]:
            valores = [item["valor"] for item in mock_data["financeiro"]["contas_receber"] if item["status"] == "Pago"]
            if valores:
                mock_data["resumo"]["ticket_medio"] = round(sum(valores) / len(valores), 2)
            else:
                mock_data["resumo"]["ticket_medio"] = 3850  # Valor mockado para clínica de alto nível
        else:
            mock_data["resumo"]["ticket_medio"] = 3850  # Valor mockado para clínica de alto nível

    # 4. Taxa de Retorno de Pacientes
    if "taxa_retorno_pacientes" not in mock_data["resumo"]:
        if "paciente" in mock_data and "atendimentos_realizados" in mock_data["paciente"]:
            # Contar pacientes com mais de um atendimento
            pacientes = {}
            for atendimento in mock_data["paciente"]["atendimentos_realizados"]:
                paciente = atendimento["paciente"]
                if paciente in pacientes:
                    pacientes[paciente] += 1
                else:
                    pacientes[paciente] = 1

            pacientes_retorno = len([p for p, count in pacientes.items() if count > 1])
            total_pacientes = len(pacientes)

            if total_pacientes > 0:
                taxa = round((pacientes_retorno / total_pacientes) * 100, 1)
                mock_data["resumo"]["taxa_retorno_pacientes"] = taxa
            else:
                mock_data["resumo"]["taxa_retorno_pacientes"] = 92.5  # Valor mockado para clínica de alto nível
        else:
            mock_data["resumo"]["taxa_retorno_pacientes"] = 92.5  # Valor mockado para clínica de alto nível

    # Dados para os gráficos
    # 1. Evolução do Faturamento (substituindo Leads por Mês)
    if "faturamento_por_mes" not in mock_data["resumo"]:
        meses = ["Janeiro", "Fevereiro", "Março", "Abril", "Maio", "Junho", "Julho", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro"]
        mock_data["resumo"]["faturamento_por_mes"] = {mes: random.randint(300000, 600000) for mes in meses}

    # 2. Funil de Vendas (manter, mas garantir que exista)
    if "etapas_funil" not in mock_data["resumo"]:
        etapas_funil = ["Novo Lead", "Primeiro Contato", "Apresentação", "Proposta", "Negociação", "Fechamento"]
        mock_data["resumo"]["etapas_funil"] = [
            {"etapa": etapa, "quantidade": random.randint(5, 50), "percentual": round(random.uniform(5, 20), 2)}
            for etapa in etapas_funil
        ]

    # 3. Distribuição por Especialidade (substituindo Distribuição por Área de Formação)
    if "distribuicao_especialidades" not in mock_data["resumo"]:
        especialidades = ["Clínica Geral", "Ortopedia", "Fisioterapia", "Nutrição", "Psicologia", "Odontologia", "Dermatologia", "Pediatria"]
        mock_data["resumo"]["distribuicao_especialidades"] = [
            {"especialidade": esp, "quantidade": random.randint(5, 50), "percentual": round(random.uniform(5, 20), 2)}
            for esp in especialidades
        ]

    # 4. Evolução do NPS (novo gráfico)
    if "nps_por_mes" not in mock_data["resumo"]:
        meses = ["Janeiro", "Fevereiro", "Março", "Abril", "Maio", "Junho", "Julho", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro"]
        # Garantir uma tendência de crescimento realista para o NPS
        base_nps = 40
        variacao = 5
        nps_valores = {}

        for i, mes in enumerate(meses):
            # Adicionar uma tendência de crescimento suave com pequenas variações
            tendencia = min(i * 1.5, 15)  # Crescimento máximo de 15 pontos
            variacao_aleatoria = random.uniform(-variacao, variacao)
            valor_nps = base_nps + tendencia + variacao_aleatoria
            # Garantir que o valor esteja entre 30 e 85
            valor_nps = max(min(round(valor_nps, 1), 85), 30)
            nps_valores[mes] = valor_nps

        mock_data["resumo"]["nps_por_mes"] = nps_valores

    # Manter os dados originais para compatibilidade
    if "total_leads" not in mock_data["resumo"]:
        if "amigocare" in mock_data and "leads" in mock_data["amigocare"]:
            mock_data["resumo"]["total_leads"] = len(mock_data["amigocare"]["leads"])
        else:
            mock_data["resumo"]["total_leads"] = 50  # Valor mockado para demonstração

    if "total_oportunidades" not in mock_data["resumo"]:
        if "amigocare" in mock_data and "campanhas" in mock_data["amigocare"]:
            mock_data["resumo"]["total_oportunidades"] = len(mock_data["amigocare"]["campanhas"])
        else:
            mock_data["resumo"]["total_oportunidades"] = 25  # Valor mockado para demonstração

    if "taxa_conversao" not in mock_data["resumo"]:
        mock_data["resumo"]["taxa_conversao"] = 35  # Valor mockado para demonstração

    if "universidades_populares" not in mock_data["resumo"]:
        unidades = ["Morumbi", "BRASILIA", "Recife", "Espinheiro", "CLÍNICA (RL)"]
        mock_data["resumo"]["universidades_populares"] = [
            {
                "id": i + 1,
                "nome": unidade,
                "sigla": unidade[:3].upper(),
                "quantidade": random.randint(5, 50),
                "percentual": round(random.uniform(5, 20), 2)
            }
            for i, unidade in enumerate(unidades)
        ]

    if "leads_por_mes" not in mock_data["resumo"]:
        meses = ["Janeiro", "Fevereiro", "Março", "Abril", "Maio", "Junho", "Julho", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro"]
        mock_data["resumo"]["leads_por_mes"] = {mes: random.randint(10, 100) for mes in meses}

    if "distribuicao_areas" not in mock_data["resumo"]:
        areas_formacao = ["Medicina", "Enfermagem", "Fisioterapia", "Nutrição", "Psicologia", "Odontologia", "Farmácia", "Biomedicina"]
        mock_data["resumo"]["distribuicao_areas"] = [
            {"area": area, "quantidade": random.randint(5, 50), "percentual": round(random.uniform(5, 20), 2)}
            for area in areas_formacao
        ]

    if "cursos_populares" not in mock_data["resumo"]:
        cursos = ["Medicina", "Enfermagem", "Fisioterapia", "Nutrição", "Psicologia", "Odontologia", "Farmácia", "Biomedicina", "Educação Física", "Terapia Ocupacional"]
        mock_data["resumo"]["cursos_populares"] = [
            {"curso": curso, "quantidade": random.randint(5, 50)}
            for curso in cursos
        ]

    # Gerar insights para a página inicial com contexto atualizado
    page_context = {
        "page_title": "Dashboard Principal",
        "page_description": "Visão geral da clínica com métricas integradas de todos os módulos",
        "key_metrics": {
            "Vendas": f"R$ {mock_data['resumo']['vendas_total']:,.2f}",
            "Produção": f"R$ {mock_data['resumo']['producao_total']:,.2f}",
            "Agendamentos": mock_data["resumo"]["agendamentos_mes_corrente"],
            "Atendimentos": mock_data["resumo"]["atendimentos_mes_corrente"]
        },
        "analysis_focus": "Performance de vendas, produção médica, agendamentos e atendimentos realizados",
        "page_elements": ["KPIs Principais", "Métricas Opcionais", "Análises Comparativas", "Insights Estratégicos"]
    }

    insights = InsightService.get_insights_for_page('dashboard', 'resumo', mock_data, num_insights=3, page_context=page_context)

    # Armazenar na sessão para uso posterior
    session['current_insights'] = insights

    # Preparar dados para o template
    areas = [item["area"] for item in mock_data["resumo"].get("distribuicao_areas", [])]
    origens = [item["origem"] for item in mock_data["resumo"].get("distribuicao_origens", [])]
    unidades = [item["nome"] for item in mock_data["resumo"].get("universidades_populares", [])]
    especialidades = [item["especialidade"] for item in mock_data["resumo"].get("distribuicao_especialidades", [])]

    # Preparar a data atual formatada
    current_date = datetime.now().strftime('%d/%m/%Y')

    # Renderizar template com os dados atualizados
    return render_template('index.html',
                          resumo=mock_data["resumo"],
                          insights=insights,
                          areas=areas,
                          origens=origens,
                          unidades=unidades,
                          especialidades=especialidades,
                          current_date=current_date)

# Módulo Agenda
@app.route('/agenda')
def agenda():
    # Gerar insights para o módulo Agenda
    insights = InsightService.get_insights_for_page('agenda', 'resumo', mock_data, num_insights=3)

    # Armazenar na sessão para uso posterior
    session['current_insights'] = insights

    return render_template('agenda/index.html', dados=mock_data["agenda"], insights=insights)

@app.route('/agenda/agendamentos')
def agendamentos():
    # Adicionar agendamentos para o dia de hoje
    adicionar_agendamentos_hoje()

    # Gerar insights para agendamentos
    insights = InsightService.get_insights_for_page('agenda', 'agendamentos', mock_data, num_insights=3)

    # Armazenar na sessão para uso posterior
    session['current_insights'] = insights

    # Passar a data atual para o template
    now = datetime.now()

    return render_template('agenda/agendamentos.html',
                          agendamentos=mock_data["agenda"]["agendamentos"],
                          insights=insights,
                          now=now)

def adicionar_agendamentos_hoje():
    """Adiciona agendamentos para o dia de hoje se não existirem suficientes"""
    # Obter a data de hoje no formato dd/mm/yyyy
    hoje = datetime.now().strftime("%d/%m/%Y")

    # Verificar quantos agendamentos já existem para hoje
    agendamentos_hoje = [a for a in mock_data["agenda"]["agendamentos"] if a["data"] == hoje]

    # Se já existirem pelo menos 10 agendamentos para hoje, não adicionar mais
    if len(agendamentos_hoje) >= 10:
        return

    # Dados para os novos agendamentos
    unidades = ["Morumbi", "BRASILIA", "Recife", "Espinheiro", "CLÍNICA (RL)"]
    profissionais = [
        "Equipe Amigo - BRUNO LIMA",
        "Equipe Amigo - Caio Menezes",
        "Equipe Amigo - Giulia Pedrosa 2",
        "Equipe Amigo - Ana Victoria Rocha de Almeida",
        "Rayara Toledo Souza",
        "José Pedroza"
    ]
    pacientes = [
        "AMANDA 1704",
        "Daniel Teste",
        "Stephany Figueiredo de Sousa",
        "MARINA HAZIN",
        "Claudio Lemos teste",
        "FABIO FELTRIM",
        "ADRANA TESTE SALVADOR",
        "ADOLFO STURARO TESTE",
        "LUIZ GUSTAVO",
        "DR RAFAEL TESTE",
        "ACRISIO JP TESTE"
    ]
    tipos_atendimento = [
        "Consulta - Amigo tech",
        "BOTOX",
        "Sessao de Fisioterapia",
        "PROCEDIMENTO SIMPLES - 60 MIN",
        "TERAPIA OCUPACIONAL",
        "PSICOLOGIA ABA",
        "Audiometria tonal"
    ]
    formas_pagamento = [
        "AMIL",
        "Crédito de Procedimento",
        "BRADESCO",
        "Cartão",
        "Pix",
        "Dinheiro",
        "Pendente",
        "UNIMED"
    ]
    procedimentos = [
        "Anestesia",
        "Blefaroplastia",
        "Sessao fisioterapia - teste",
        "BOTOX MALAR",
        "Ecodoppler Vertebral ou Vascular Periférico",
        "PROCEDIMENTO RAIZ FISIO",
        "Septoplastia (qualquer técnica sem vídeo)",
        "Mielograma"
    ]
    status_agendamento = ["Agendado", "Confirmado", "Realizado", "Faltou"]

    # Gerar horários de atendimento para hoje
    horarios = [f"{h:02d}:{m:02d}" for h in range(8, 19) for m in [0, 30]]

    # Número de agendamentos a adicionar
    num_agendamentos_adicionar = 15 - len(agendamentos_hoje)

    # Adicionar novos agendamentos para hoje
    for i in range(num_agendamentos_adicionar):
        hora = random.choice(horarios)
        unidade = random.choice(unidades)
        profissional = random.choice(profissionais)
        paciente = random.choice(pacientes)
        tipo_atendimento = random.choice(tipos_atendimento)
        forma_pagamento = random.choice(formas_pagamento)
        procedimento = random.choice(procedimentos)
        status = random.choice(status_agendamento)
        valor = round(random.uniform(50, 2000), 2)

        agendamento = {
            "codigo": 150000000 + len(mock_data["agenda"]["agendamentos"]) + i,
            "data": hoje,
            "hora": hora,
            "unidade": unidade,
            "profissional": profissional,
            "paciente": paciente,
            "tipo_atendimento": tipo_atendimento,
            "procedimento": procedimento,
            "forma_pagamento": forma_pagamento,
            "valor": valor,
            "status": status
        }

        mock_data["agenda"]["agendamentos"].append(agendamento)

        # Adicionar à produção médica se o status for "Realizado"
        if status == "Realizado":
            mock_data["agenda"]["producao_medica"].append({
                "codigo": agendamento["codigo"],
                "data": agendamento["data"],
                "unidade": agendamento["unidade"],
                "profissional": agendamento["profissional"],
                "paciente": agendamento["paciente"],
                "tipo_atendimento": agendamento["tipo_atendimento"],
                "procedimento": agendamento["procedimento"],
                "forma_pagamento": agendamento["forma_pagamento"],
                "valor": agendamento["valor"]
            })

            # Adicionar ao tempo de atendimento
            hora_chegada = (datetime.strptime(hora, "%H:%M") - timedelta(minutes=random.randint(0, 30))).strftime("%H:%M")
            inicio_atendimento = (datetime.strptime(hora_chegada, "%H:%M") + timedelta(minutes=random.randint(1, 20))).strftime("%H:%M")
            fim_atendimento = (datetime.strptime(inicio_atendimento, "%H:%M") + timedelta(minutes=random.randint(5, 60))).strftime("%H:%M")

            espera_minutos = (datetime.strptime(inicio_atendimento, "%H:%M") - datetime.strptime(hora_chegada, "%H:%M")).total_seconds() / 60
            tempo_atendimento = (datetime.strptime(fim_atendimento, "%H:%M") - datetime.strptime(inicio_atendimento, "%H:%M")).total_seconds() / 60

            mock_data["agenda"]["tempo_atendimento"].append({
                "codigo": agendamento["codigo"],
                "data": agendamento["data"],
                "unidade": agendamento["unidade"],
                "profissional": agendamento["profissional"],
                "paciente": agendamento["paciente"],
                "tipo_atendimento": agendamento["tipo_atendimento"],
                "hora_agendamento": agendamento["hora"],
                "hora_chegada": hora_chegada,
                "inicio_atendimento": inicio_atendimento,
                "fim_atendimento": fim_atendimento,
                "espera_minutos": int(espera_minutos),
                "tempo_atendimento": int(tempo_atendimento)
            })

@app.route('/agenda/producao-medica')
def producao_medica():
    return render_template('agenda/producao_medica.html', producao=mock_data["agenda"]["producao_medica"])

@app.route('/agenda/tempo-atendimento')
def tempo_atendimento():
    # Calcular tempo médio de espera
    tempos_espera = [tempo["espera_minutos"] for tempo in mock_data["agenda"]["tempo_atendimento"] if isinstance(tempo["espera_minutos"], (int, float))]
    espera_media = 0 if not tempos_espera else round(sum(tempos_espera) / len(tempos_espera))

    # Calcular tempo médio de atendimento
    tempos_atendimento = [tempo["tempo_atendimento"] for tempo in mock_data["agenda"]["tempo_atendimento"] if isinstance(tempo["tempo_atendimento"], (int, float))]
    tempo_medio = 0 if not tempos_atendimento else round(sum(tempos_atendimento) / len(tempos_atendimento))

    # Calcular eficiência operacional
    eficiencia = 100 - ((espera_media / (tempo_medio + espera_media)) * 100) if (tempo_medio + espera_media) > 0 else 0
    eficiencia = round(eficiencia)

    # Calcular potencial de otimização
    potencial_otimizacao = round(tempo_medio * 0.15)

    # Gerar insights para a página
    insights = InsightService.get_insights_for_page('agenda', 'tempo_atendimento', mock_data, num_insights=3)

    return render_template('agenda/tempo_atendimento.html',
                          tempos=mock_data["agenda"]["tempo_atendimento"],
                          tempo_medio=tempo_medio,
                          espera_media=espera_media,
                          eficiencia=eficiencia,
                          potencial_otimizacao=potencial_otimizacao,
                          insights=insights)

@app.route('/agenda/cancelamentos')
def cancelamentos():
    # Calcular a taxa de cancelamento
    total_agendamentos = len(mock_data["agenda"]["agendamentos"])
    total_cancelamentos = len(mock_data["agenda"]["cancelamentos"])

    # Evitar divisão por zero
    if total_agendamentos > 0:
        taxa_cancelamento = round((total_cancelamentos / total_agendamentos) * 100, 1)
    else:
        taxa_cancelamento = 0

    # Gerar insights para a página
    insights = InsightService.get_insights_for_page('agenda', 'cancelamentos', mock_data, num_insights=3)

    return render_template('agenda/cancelamentos.html',
                          cancelamentos=mock_data["agenda"]["cancelamentos"],
                          taxa_cancelamento=taxa_cancelamento,
                          total_agendamentos=total_agendamentos,
                          insights=insights)

# Módulo Financeiro
@app.route('/financeiro')
def financeiro():
    return render_template('financeiro/index.html', dados=mock_data["financeiro"])

@app.route('/financeiro/contas-receber')
def contas_receber():
    return render_template('financeiro/contas_receber.html', contas=mock_data["financeiro"]["contas_receber"])

@app.route('/financeiro/contas-pagar')
def contas_pagar():
    return render_template('financeiro/contas_pagar.html', contas=mock_data["financeiro"]["contas_pagar"])

@app.route('/financeiro/fluxo-caixa')
def fluxo_caixa():
    return render_template('financeiro/fluxo_caixa.html', fluxo=mock_data["financeiro"]["fluxo_caixa"])

@app.route('/financeiro/fechamento-caixa')
def fechamento_caixa():
    return render_template('financeiro/fechamento_caixa.html', fechamentos=mock_data["financeiro"]["fechamento_caixa"])

# Módulo Paciente
@app.route('/paciente')
def paciente():
    return render_template('paciente/index.html', dados=mock_data["paciente"])

@app.route('/paciente/atendimentos-realizados')
def atendimentos_realizados():
    return render_template('paciente/atendimentos_realizados.html', atendimentos=mock_data["paciente"]["atendimentos_realizados"])

@app.route('/paciente/creditos-disponiveis')
def creditos_disponiveis():
    return render_template('paciente/creditos_disponiveis.html', creditos=mock_data["paciente"]["creditos_disponiveis"])

@app.route('/paciente/orcamentos-fechados')
def orcamentos_fechados():
    return render_template('paciente/orcamentos_fechados.html', orcamentos=mock_data["paciente"]["orcamentos_fechados"])

@app.route('/paciente/orcamentos-abertos')
def orcamentos_abertos():
    return render_template('paciente/orcamentos_abertos.html', orcamentos=mock_data["paciente"]["orcamentos_abertos"])


# Módulo Amigo Care+
@app.route('/amigocare')
def amigocare():
    return render_template('amigocare/index.html', dados=mock_data["amigocare"])

@app.route('/amigocare/avaliacao-nps')
def avaliacao_nps():
    return render_template('amigocare/avaliacao_nps.html', avaliacoes=mock_data["amigocare"]["avaliacao_nps"])

@app.route('/amigocare/leads')
def leads():
    return render_template('amigocare/leads.html', leads=mock_data["amigocare"]["leads"])

@app.route('/amigocare/campanhas')
def campanhas():
    return render_template('amigocare/campanhas.html', campanhas=mock_data["amigocare"]["campanhas"])

@app.route('/amigocare/funil-vendas')
def funil_vendas():
    return render_template('amigocare/funil_vendas.html', leads=mock_data["amigocare"]["leads"], campanhas=mock_data["amigocare"]["campanhas"])



# Módulo AmigoStudio
@app.route('/amigostudio')
def amigostudio():
    # Iniciar o servidor Streamlit se não estiver rodando
    streamlit_port = 8501
    streamlit_url = f"http://localhost:{streamlit_port}"

    # Verificar se o servidor Streamlit já está rodando
    try:
        # Tentar iniciar o servidor Streamlit em background
        streamlit_app_path = os.path.join(os.path.dirname(__file__), 'streamlit_app.py')
        subprocess.Popen(["streamlit", "run", streamlit_app_path, "--server.port", str(streamlit_port)],
                        stdout=subprocess.DEVNULL,
                        stderr=subprocess.DEVNULL)
    except Exception as e:
        print(f"Erro ao iniciar o servidor Streamlit: {str(e)}")

    # Gerar insights para o módulo AmigoStudio
    insights = InsightService.get_insights_for_page('amigostudio', 'index', mock_data, num_insights=3)

    # Armazenar na sessão para uso posterior
    session['current_insights'] = insights

    return render_template('amigostudio/index.html',
                          streamlit_url=streamlit_url,
                          insights=insights)



# Rota para configuração de fonte de dados
@app.route('/admin/data-source', methods=['GET', 'POST'])
def data_source_config():
    if request.method == 'POST':
        source_type = request.form.get('source_type')

        config = {'type': source_type}

        if source_type == 'json':
            config['file_path'] = request.form.get('file_path')
        elif source_type == 'excel':
            config['file_path'] = request.form.get('file_path')
            config['sheet_mapping'] = {
                'agenda': request.form.get('sheet_agenda', 'Agenda'),
                'financeiro': request.form.get('sheet_financeiro', 'Financeiro'),
                'paciente': request.form.get('sheet_paciente', 'Paciente'),
                'amigocare': request.form.get('sheet_amigocare', 'AmigoCare'),

                'resumo': request.form.get('sheet_resumo', 'Resumo')
            }
        elif source_type == 'csv':
            config['file_paths'] = {
                'agenda': request.form.get('csv_agenda'),
                'financeiro': request.form.get('csv_financeiro'),
                'paciente': request.form.get('csv_paciente'),
                'amigocare': request.form.get('csv_amigocare'),

            }
            config['delimiter'] = request.form.get('csv_delimiter', ',')
            config['encoding'] = request.form.get('csv_encoding', 'utf-8')
        elif source_type == 'database':
            config['connection_string'] = request.form.get('connection_string')
            # Adicionar outras configurações de banco de dados conforme necessário

        # Salvar configuração
        config_dir = os.path.join(os.path.dirname(__file__), 'config')
        os.makedirs(config_dir, exist_ok=True)
        with open(os.path.join(config_dir, 'data_source.json'), 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)

        # Atualizar serviço de dados
        data_service.set_config(config)

        # Atualizar dados
        global mock_data
        mock_data = data_service.get_data()

        return redirect(url_for('index'))

    # Carregar configuração atual
    config_path = os.path.join(os.path.dirname(__file__), 'config', 'data_source.json')
    current_config = {}
    if os.path.exists(config_path):
        with open(config_path, 'r', encoding='utf-8') as f:
            current_config = json.load(f)

    return render_template('admin/data_source.html', config=current_config)

# Rota de login
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        email = request.form.get('email')
        password = request.form.get('password')
        remember = 'remember_me' in request.form

        # Aqui você implementaria a lógica de autenticação real
        # Por enquanto, vamos apenas simular um login bem-sucedido
        if email and password:
            # Armazenar informações do usuário na sessão
            session['user'] = {
                'email': email,
                'name': 'Dr. Bruno Abreu',
                'role': 'Administrador',
                'last_login': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            return redirect(url_for('index'))

        # Se chegou aqui, houve falha no login
        # Em uma implementação real, você adicionaria uma mensagem de erro
        return render_template('login.html', error="Email ou senha inválidos")

    # Se for GET, apenas exibe a página de login
    return render_template('login.html')

# Rota de logout
@app.route('/logout')
def logout():
    session.pop('user', None)
    return redirect(url_for('login'))

# Rota do Cockpit KAM
@app.route('/cockpit-kam')
def cockpit_kam():
    """
    Cockpit KAM - Key Account Management Dashboard
    Dashboard estratégico para gestão de contas-chave com Índice Amigo
    """
    return render_template('cockpit_kam.html')

# Carregar configuração inicial
load_data_source_config()

# Obter dados
mock_data = data_service.get_data()

# Armazenar apenas referências na sessão, não os dados completos
@app.before_request
def store_session_references():
    # Verificar se o usuário está logado para rotas protegidas
    if request.path != '/login' and not request.path.startswith('/static') and 'user' not in session:
        return redirect(url_for('login'))

    # Armazenar apenas metadados e referências na sessão
    if 'page_context' not in session:
        session['page_context'] = {
            'current_module': '',
            'current_report': '',
            'last_accessed': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

    # Atualizar o contexto da página com base na URL atual
    path = request.path
    if path.startswith('/agenda'):
        session['page_context']['current_module'] = 'agenda'
    elif path.startswith('/financeiro'):
        session['page_context']['current_module'] = 'financeiro'
    elif path.startswith('/paciente'):
        session['page_context']['current_module'] = 'paciente'
    elif path.startswith('/amigocare'):
        session['page_context']['current_module'] = 'amigocare'

    elif path.startswith('/amigostudio'):
        session['page_context']['current_module'] = 'amigostudio'
    else:
        session['page_context']['current_module'] = 'dashboard'

    # Atualizar timestamp
    session['page_context']['last_accessed'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

# Função para obter dados mockados (para uso em todo o aplicativo)
def get_mock_data():
    global mock_data

    # Garantir que os campos necessários para a página inicial existam
    if "resumo" not in mock_data:
        mock_data["resumo"] = {}

    # Garantir que os campos específicos para os cards iniciais existam
    if "total_leads" not in mock_data["resumo"]:
        # Total de leads (usando os leads do módulo amigocare)
        if "amigocare" in mock_data and "leads" in mock_data["amigocare"]:
            mock_data["resumo"]["total_leads"] = len(mock_data["amigocare"]["leads"])
        else:
            mock_data["resumo"]["total_leads"] = 0

    if "total_oportunidades" not in mock_data["resumo"]:
        # Total de oportunidades (usando as campanhas como oportunidades)
        if "amigocare" in mock_data and "campanhas" in mock_data["amigocare"]:
            mock_data["resumo"]["total_oportunidades"] = len(mock_data["amigocare"]["campanhas"])
        else:
            mock_data["resumo"]["total_oportunidades"] = 0

    if "taxa_conversao" not in mock_data["resumo"]:
        # Taxa de conversão
        if mock_data["resumo"].get("total_oportunidades", 0) > 0 and "amigocare" in mock_data and "campanhas" in mock_data["amigocare"]:
            # Contar oportunidades convertidas (campanhas concluídas)
            oportunidades_convertidas = len([c for c in mock_data["amigocare"]["campanhas"] if c.get("status") == "Concluída"])
            mock_data["resumo"]["taxa_conversao"] = round((oportunidades_convertidas / mock_data["resumo"]["total_oportunidades"]) * 100, 2)
        else:
            mock_data["resumo"]["taxa_conversao"] = 0

    if "universidades_populares" not in mock_data["resumo"]:
        # Universidades populares (usando unidades como universidades)
        unidades = ["Morumbi", "BRASILIA", "Recife", "Espinheiro", "CLÍNICA (RL)"]
        mock_data["resumo"]["universidades_populares"] = [
            {
                "id": i + 1,
                "nome": unidade,
                "sigla": unidade[:3].upper(),
                "quantidade": random.randint(5, 50),
                "percentual": round(random.uniform(5, 20), 2)
            }
            for i, unidade in enumerate(unidades)
        ]

    return mock_data

if __name__ == '__main__':
    app.run(debug=True, port='5003')

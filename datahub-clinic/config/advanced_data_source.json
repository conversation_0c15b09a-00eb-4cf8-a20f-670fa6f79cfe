{"sources": {"usuarios": {"type": "excel", "file_path": "mock_data.xlsx", "sheet": "USUÁRIOS", "field_mapping": {"🔒 Row ID": "id", "Email do usuário": "email", "[N] Data do onboarding": "data_onboarding"}, "primary_key": "id"}, "contatos": {"type": "excel", "file_path": "mock_data.xlsx", "sheet": "CONTATOS", "field_mapping": {"🔒 Row ID": "id", "ID do contato": "id_contato", "Nome do contato": "nome", "Email do contato": "email", "Telefone do contato": "telefone", "Data da adição": "data_adicao", "Área de formação": "area_formacao", "ID da universidade": "id_universidade", "Contato que acionou (Id_contato)": "id_contato_acionou", "Source": "origem", "Tipo de cadastro do contato (Aba CONTATOS)": "tipo_cadastro", "Instagram preenchido": "instagram", "[id da turma]": "id_turma"}, "exclude_fields": ["Unnamed: 13"], "primary_key": "id_contato"}, "oportunidades": {"type": "excel", "file_path": "mock_data.xlsx", "sheet": "OPORTUNIDADES", "field_mapping": {"🔒 Row ID": "id", "ID da oportunidade": "id_oportunidade", "Data da adição": "data_adicao", "Data da exclusão": "data_exclusao", "ID do contato da oportunidade": "id_contato", "ID do contato do Adicionado por": "id_adicionado_por", "Etapa do funil": "etapa_funil", "Visibilidade da oportunidade": "visibilidade", "Solução": "solucao", "ID do contato do consultorcomercial/gesturmas/gestparcerias (Atual)": "id_consultor", "Tipo da oportunidade": "tipo", "Formato da oportunidade": "formato", "Data do último envio do convite": "data_ultimo_convite", "ID do cupom": "id_cupom"}, "primary_key": "id_oportunidade"}, "turmas": {"type": "excel", "file_path": "mock_data.xlsx", "sheet": "TURMAS", "field_mapping": {"ID da turma": "id", "ID da Turma sem caracteres": "id_sem_caracteres", "Nome da turma": "nome", "Semestre": "semestre", "Tamanho da turma": "ta<PERSON><PERSON>", "[N] Data da colação": "data_colacao", "[N] Data da adição": "data_adicao", "[N] Data da última indicação": "data_ultima_indicacao", "ID da Universidade": "id_universidade", "Curso da turma": "curso", "Universidade": "universidade", "Estado": "estado", "Cidade": "cidade", "ID do ECM responsável": "id_ecm_responsavel", "Etapa do funil": "etapa_funil", "ERP responsável": "erp_responsavel", "ID do GT responsável": "id_gt_responsavel", "Etapa do funil atualizada": "etapa_funil_atualizada", "ID do grupo do WhatsApp": "id_grupo_whatsapp", "Nº de indicações": "num_indicacoes"}, "primary_key": "id"}, "cupons": {"type": "excel", "file_path": "mock_data.xlsx", "sheet": "CUPONS", "field_mapping": {"🔒 Row ID": "id", "Code": "codigo", "ID do cupom": "id_cupom", "ID do produto": "id_produto", "Produto": "produto", "Descrição": "descricao", "Duração": "duracao", "Desconto": "desconto", "Cobrança da 1ª mensalidade": "cobranca_primeira_mensalidade", "Status (On/Off)": "status", "Canal": "canal"}, "primary_key": "id_cupom"}, "usuarios_especiais": {"type": "excel", "file_path": "mock_data.xlsx", "sheet": "USUARIOS ESPECIAIS", "field_mapping": {"Nome completo": "nome", "Seu melhor e-mail": "email", "WhatsApp (com DDD)": "whatsapp", "Qual a sua área de formação?": "area_formacao"}}, "universidades": {"type": "excel", "file_path": "mock_data.xlsx", "sheet": "UNIVERSIDADES", "field_mapping": {"🔒 Row ID": "id", "Sigla": "sigla", "Centro Universitário de Indaiatuba - UniMAX - Grupo UniEduK": "nome", "Vagas no 1º ano": "vagas_primeiro_ano", "Administração": "administracao", "Cidade": "cidade", "Estado": "estado", "Estado (Sigla)": "estado_sigla", "Vagas por semestre": "vagas_semestre", "Imagem da universidade": "imagem", "Status da Universidade (Ativo/Inativo)": "status", "Cursos": "cursos", "ID da universidade": "id_universidade"}, "primary_key": "id_universidade"}}, "relationships": [{"from_source": "contatos", "from_field": "id_universidade", "to_source": "universidades", "to_field": "id_universidade", "relationship_type": "many_to_one"}, {"from_source": "contatos", "from_field": "id_turma", "to_source": "turmas", "to_field": "id", "relationship_type": "many_to_one"}, {"from_source": "oportunidades", "from_field": "id_contato", "to_source": "contatos", "to_field": "id_contato", "relationship_type": "many_to_one"}, {"from_source": "oportunidades", "from_field": "id_cupom", "to_source": "cupons", "to_field": "id_cupom", "relationship_type": "many_to_one"}, {"from_source": "turmas", "from_field": "id_universidade", "to_source": "universidades", "to_field": "id_universidade", "relationship_type": "many_to_one"}], "output_structure": {"agenda": {"agendamentos": "oportunidades"}, "financeiro": {"contas_receber": "oportunidades"}, "paciente": {"atendimentos_realizados": "contatos"}, "amigocare": {"leads": "contatos", "campanhas": "oportunidades"}, "resumo": {"total_agendamentos": 0, "total_atendimentos": 0, "total_receita": 0, "total_despesa": 0, "taxa_ocupacao": 0, "tempo_medio_atendimento": 0, "taxa_cancelamento": 0, "procedimentos_populares": [], "medicos_produtivos": [], "unidades_desempenho": []}}}
# ESPECIFICAÇÃO COMPLETA DO DATA WAREHOUSE OLAP - AMIGO DATAHUB

## RESUMO EXECUTIVO

Este documento especifica a arquitetura de Data Warehouse OLAP necessária para suportar todas as análises e gráficos do Amigo DataHub, uma plataforma de gestão médica com IA proprietária.

## ESTRUTURA DIMENSIONAL

### DIMENSÕES (DIMENSION TABLES)

#### 1. DIM_TEMPO
**Propósito**: Dimensão temporal para análises históricas e sazonais
```sql
- data_id (PK): Chave primária única
- data_completa: Data completa (YYYY-MM-DD)
- ano, mes, dia: Componentes da data
- dia_semana: 1-7 (Segunda a Domingo)
- nome_mes, nome_dia_semana: Nomes por extenso
- trimestre, semestre: Agrupamentos temporais
- feriado, fim_semana: Flags booleanos
```

#### 2. DIM_PACIENTE
**Propósito**: Informações demográficas e cadastrais dos pacientes
```sql
- paciente_id (PK): Identificador único do paciente
- nome, cpf, email, telefone: Dados pessoais
- data_nascimento, idade, sexo: Demografia
- endereco, profissao: Informações complementares
- como_conheceu: Origem do paciente (marketing)
- plano_saude, matricula_plano, cns: Convênios
- vip: Flag para pacientes VIP
- data_cadastro, status: Controle
```

#### 3. DIM_PROFISSIONAL
**Propósito**: Dados dos profissionais de saúde
```sql
- profissional_id (PK): Identificador único
- nome, crm, cbo: Identificação profissional
- especialidade_id (FK): Ligação com especialidade
- unidade_id (FK): Unidade de trabalho
- data_admissao, status: Controle de RH
- salario_base, turno_trabalho: Informações trabalhistas
```

#### 4. DIM_ESPECIALIDADE
**Propósito**: Especialidades médicas e seus parâmetros
```sql
- especialidade_id (PK): Identificador único
- nome, categoria: Classificação da especialidade
- valor_consulta_base: Valor padrão de consulta
- tempo_consulta_padrao: Duração média em minutos
- descricao: Detalhes da especialidade
```

#### 5. DIM_UNIDADE
**Propósito**: Unidades/filiais da clínica
```sql
- unidade_id (PK): Identificador único
- nome, endereco, telefone, email: Dados da unidade
- cnpj, responsavel: Informações legais
- capacidade_atendimento: Número máximo de atendimentos/dia
- horario_funcionamento: Horários de operação
```

#### 6. DIM_PROCEDIMENTO
**Propósito**: Catálogo de procedimentos médicos
```sql
- procedimento_id (PK): Identificador único
- nome, codigo: Identificação do procedimento
- categoria: Classificação (consulta, exame, cirurgia, etc.)
- valor_base: Preço padrão
- tempo_estimado: Duração em minutos
- descricao: Detalhes do procedimento
- ativo: Flag de disponibilidade
```

### TABELAS DE FATOS (FACT TABLES)

#### 1. FACT_AGENDAMENTO
**Propósito**: Registro central de todos os agendamentos
**Granularidade**: Um registro por agendamento
```sql
Chaves Estrangeiras:
- data_id → DIM_TEMPO
- paciente_id → DIM_PACIENTE
- profissional_id → DIM_PROFISSIONAL
- unidade_id → DIM_UNIDADE
- procedimento_id → DIM_PROCEDIMENTO
- status_id → DIM_STATUS_AGENDAMENTO

Métricas:
- valor: Valor do agendamento
- hora_agendamento: Horário marcado
- forma_pagamento: Método de pagamento
- observacoes: Notas adicionais
```

#### 2. FACT_PRODUCAO_MEDICA
**Propósito**: Métricas de produtividade médica
**Granularidade**: Um registro por atendimento realizado
```sql
Métricas Principais:
- valor_producao: Valor gerado pelo atendimento
- quantidade_atendimentos: Número de atendimentos
- tempo_total_atendimento: Tempo total em minutos
- custo_operacional: Custos associados
- margem_contribuicao: Lucro do atendimento
```

#### 3. FACT_TEMPO_ATENDIMENTO
**Propósito**: Análise de eficiência operacional
**Granularidade**: Um registro por atendimento com tempos detalhados
```sql
Métricas de Tempo:
- hora_chegada, inicio_atendimento, fim_atendimento
- tempo_espera_minutos: Tempo de espera do paciente
- tempo_atendimento_minutos: Duração do atendimento
- tempo_total_minutos: Tempo total na clínica
```

#### 4. FACT_TRANSACAO_FINANCEIRA
**Propósito**: Movimentações financeiras gerais
**Granularidade**: Uma transação por registro
```sql
Métricas Financeiras:
- valor: Valor da transação
- tipo_transacao: Receita/Despesa
- status: Pago/Pendente/Cancelado
- data_vencimento, data_pagamento
- forma_pagamento: Método utilizado
```

## ANÁLISES POR MÓDULO

### DASHBOARD PRINCIPAL
**KPIs Calculados**:
```sql
-- Vendas Total
SELECT SUM(valor_producao) FROM FACT_PRODUCAO_MEDICA 
WHERE data_id IN (SELECT data_id FROM DIM_TEMPO WHERE ano = YEAR(GETDATE()))

-- Taxa de Ocupação
SELECT (COUNT(DISTINCT agendamento_id) * 100.0) / 
       (SELECT capacidade_total FROM capacidade_teorica)
FROM FACT_AGENDAMENTO WHERE status = 'Realizado'

-- Performance Médicos (Top 10)
SELECT p.nome, SUM(pm.valor_producao) as total_producao
FROM FACT_PRODUCAO_MEDICA pm
JOIN DIM_PROFISSIONAL p ON pm.profissional_id = p.profissional_id
GROUP BY p.profissional_id, p.nome
ORDER BY total_producao DESC
LIMIT 10
```

### MÓDULO AGENDA
**Análises Principais**:
- Distribuição de agendamentos por status
- Taxa de cancelamento por período
- Tempo médio de atendimento por especialidade
- Produtividade por profissional
- Análise de horários de pico

### MÓDULO FINANCEIRO
**Métricas Financeiras**:
- Contas a receber por aging
- Fluxo de caixa projetado
- Análise de inadimplência
- ROI por procedimento
- Margem de contribuição por unidade

### MÓDULO PACIENTE
**Análises de Paciente**:
- Lifetime Value (LTV) do paciente
- Taxa de retorno por especialidade
- Análise de créditos e utilização
- Conversão de orçamentos
- Segmentação de pacientes

### MÓDULO AMIGOCARE+
**Métricas de Qualidade**:
- NPS por período e profissional
- Funil de conversão de leads
- ROI de campanhas de marketing
- Custo de aquisição de cliente (CAC)
- Jornada do paciente

## REQUISITOS TÉCNICOS

### PERFORMANCE
- Índices em todas as chaves estrangeiras
- Particionamento por data nas tabelas de fato
- Agregações pré-calculadas para KPIs principais
- Cache de consultas frequentes

### QUALIDADE DE DADOS
- Validação de CPF e email
- Controle de duplicatas
- Auditoria de alterações
- Monitoramento de completude

### SEGURANÇA
- Criptografia de dados sensíveis (CPF, email)
- Controle de acesso por perfil
- Log de auditoria
- Backup e recovery

## IMPLEMENTAÇÃO RECOMENDADA

### FASE 1: Core Dimensional
1. Implementar dimensões básicas
2. Criar FACT_AGENDAMENTO
3. Desenvolver KPIs do dashboard

### FASE 2: Módulos Operacionais
1. Implementar módulos Agenda e Financeiro
2. Criar análises de tempo e produtividade
3. Desenvolver relatórios operacionais

### FASE 3: Analytics Avançados
1. Implementar módulo AmigoCare+
2. Criar análises preditivas
3. Desenvolver dashboards executivos

### FASE 4: IA e Machine Learning
1. Implementar modelos preditivos
2. Criar recomendações automáticas
3. Desenvolver alertas inteligentes

## MAPEAMENTO DETALHADO POR PÁGINA

### 1. DASHBOARD PRINCIPAL (/)

#### Dados Necessários:
```sql
-- KPI Vendas
SELECT SUM(valor_producao) as vendas_total
FROM FACT_PRODUCAO_MEDICA pm
JOIN DIM_TEMPO t ON pm.data_id = t.data_id
WHERE t.ano = YEAR(GETDATE())

-- KPI Agendamentos Mês Corrente
SELECT COUNT(*) as agendamentos_mes
FROM FACT_AGENDAMENTO a
JOIN DIM_TEMPO t ON a.data_id = t.data_id
WHERE t.ano = YEAR(GETDATE()) AND t.mes = MONTH(GETDATE())

-- Gráfico Evolução Y/Y
SELECT t.mes, t.ano, SUM(pm.valor_producao) as valor
FROM FACT_PRODUCAO_MEDICA pm
JOIN DIM_TEMPO t ON pm.data_id = t.data_id
WHERE t.ano IN (YEAR(GETDATE()), YEAR(GETDATE())-1)
GROUP BY t.ano, t.mes
ORDER BY t.ano, t.mes

-- Leads por Origem
SELECT ol.origem, COUNT(*) as quantidade,
       (COUNT(*) * 100.0 / SUM(COUNT(*)) OVER()) as percentual
FROM FACT_LEAD l
JOIN DIM_ORIGEM_LEAD ol ON l.origem_id = ol.origem_id
GROUP BY ol.origem_id, ol.origem

-- Performance Médicos
SELECT p.nome, SUM(pm.valor_producao) as valor_total,
       RANK() OVER (ORDER BY SUM(pm.valor_producao) DESC) as ranking
FROM FACT_PRODUCAO_MEDICA pm
JOIN DIM_PROFISSIONAL p ON pm.profissional_id = p.profissional_id
JOIN DIM_TEMPO t ON pm.data_id = t.data_id
WHERE t.ano = YEAR(GETDATE())
GROUP BY p.profissional_id, p.nome
ORDER BY valor_total DESC
LIMIT 8
```

### 2. MÓDULO AGENDA

#### 2.1 Agenda Index (/agenda)
```sql
-- Resumo Geral
SELECT
    COUNT(*) as total_agendamentos,
    COUNT(CASE WHEN sa.status = 'Realizado' THEN 1 END) as atendimentos_realizados,
    AVG(ta.tempo_atendimento_minutos) as tempo_medio,
    COUNT(CASE WHEN sa.status = 'Cancelado' THEN 1 END) as cancelamentos
FROM FACT_AGENDAMENTO a
LEFT JOIN DIM_STATUS_AGENDAMENTO sa ON a.status_id = sa.status_id
LEFT JOIN FACT_TEMPO_ATENDIMENTO ta ON a.agendamento_id = ta.agendamento_id
JOIN DIM_TEMPO t ON a.data_id = t.data_id
WHERE t.ano = YEAR(GETDATE()) AND t.mes = MONTH(GETDATE())
```

#### 2.2 Agendamentos (/agenda/agendamentos)
```sql
-- Distribuição por Status
SELECT sa.status, COUNT(*) as quantidade,
       (COUNT(*) * 100.0 / SUM(COUNT(*)) OVER()) as percentual
FROM FACT_AGENDAMENTO a
JOIN DIM_STATUS_AGENDAMENTO sa ON a.status_id = sa.status_id
JOIN DIM_TEMPO t ON a.data_id = t.data_id
WHERE t.ano = YEAR(GETDATE())
GROUP BY sa.status_id, sa.status

-- Agendamentos por Profissional
SELECT p.nome, COUNT(*) as total_agendamentos,
       COUNT(CASE WHEN sa.status = 'Realizado' THEN 1 END) as realizados,
       (COUNT(CASE WHEN sa.status = 'Realizado' THEN 1 END) * 100.0 / COUNT(*)) as taxa_realizacao
FROM FACT_AGENDAMENTO a
JOIN DIM_PROFISSIONAL p ON a.profissional_id = p.profissional_id
JOIN DIM_STATUS_AGENDAMENTO sa ON a.status_id = sa.status_id
GROUP BY p.profissional_id, p.nome
ORDER BY total_agendamentos DESC

-- Evolução Mensal
SELECT t.mes, t.nome_mes, COUNT(*) as agendamentos
FROM FACT_AGENDAMENTO a
JOIN DIM_TEMPO t ON a.data_id = t.data_id
WHERE t.ano = YEAR(GETDATE())
GROUP BY t.mes, t.nome_mes
ORDER BY t.mes

-- Horários Mais Procurados
SELECT HOUR(a.hora_agendamento) as hora, COUNT(*) as quantidade
FROM FACT_AGENDAMENTO a
JOIN DIM_TEMPO t ON a.data_id = t.data_id
WHERE t.ano = YEAR(GETDATE())
GROUP BY HOUR(a.hora_agendamento)
ORDER BY quantidade DESC
```

#### 2.3 Produção Médica (/agenda/producao-medica)
```sql
-- Produção por Profissional
SELECT p.nome, e.nome as especialidade,
       SUM(pm.valor_producao) as valor_total,
       COUNT(pm.quantidade_atendimentos) as total_atendimentos,
       AVG(pm.valor_producao) as valor_medio
FROM FACT_PRODUCAO_MEDICA pm
JOIN DIM_PROFISSIONAL p ON pm.profissional_id = p.profissional_id
JOIN DIM_ESPECIALIDADE e ON p.especialidade_id = e.especialidade_id
JOIN DIM_TEMPO t ON pm.data_id = t.data_id
WHERE t.ano = YEAR(GETDATE())
GROUP BY p.profissional_id, p.nome, e.nome
ORDER BY valor_total DESC

-- Produtividade por Dia da Semana
SELECT t.nome_dia_semana,
       SUM(pm.valor_producao) as valor_total,
       COUNT(pm.quantidade_atendimentos) as atendimentos
FROM FACT_PRODUCAO_MEDICA pm
JOIN DIM_TEMPO t ON pm.data_id = t.data_id
WHERE t.ano = YEAR(GETDATE())
GROUP BY t.dia_semana, t.nome_dia_semana
ORDER BY t.dia_semana

-- Tipos de Procedimentos
SELECT pr.nome, pr.categoria,
       COUNT(*) as quantidade,
       SUM(a.valor) as valor_total
FROM FACT_AGENDAMENTO a
JOIN DIM_PROCEDIMENTO pr ON a.procedimento_id = pr.procedimento_id
JOIN DIM_STATUS_AGENDAMENTO sa ON a.status_id = sa.status_id
WHERE sa.status = 'Realizado'
GROUP BY pr.procedimento_id, pr.nome, pr.categoria
ORDER BY quantidade DESC
```

### 3. MÓDULO FINANCEIRO

#### 3.1 Contas a Receber (/financeiro/contas-receber)
```sql
-- Resumo por Status
SELECT
    SUM(valor_original) as total_original,
    SUM(valor_recebido) as total_recebido,
    SUM(valor_pendente) as total_pendente,
    SUM(CASE WHEN dias_atraso > 0 THEN valor_pendente ELSE 0 END) as total_atrasado
FROM FACT_CONTA_RECEBER

-- Aging de Recebíveis
SELECT
    CASE
        WHEN dias_atraso <= 0 THEN 'A Vencer'
        WHEN dias_atraso <= 30 THEN '1-30 dias'
        WHEN dias_atraso <= 60 THEN '31-60 dias'
        WHEN dias_atraso <= 90 THEN '61-90 dias'
        ELSE 'Mais de 90 dias'
    END as faixa_atraso,
    COUNT(*) as quantidade,
    SUM(valor_pendente) as valor_total
FROM FACT_CONTA_RECEBER
WHERE status = 'Pendente'
GROUP BY CASE
    WHEN dias_atraso <= 0 THEN 'A Vencer'
    WHEN dias_atraso <= 30 THEN '1-30 dias'
    WHEN dias_atraso <= 60 THEN '31-60 dias'
    WHEN dias_atraso <= 90 THEN '61-90 dias'
    ELSE 'Mais de 90 dias'
END

-- Recebimentos por Mês
SELECT t.mes, t.nome_mes,
       SUM(valor_recebido) as valor_recebido,
       COUNT(*) as quantidade_recebimentos
FROM FACT_CONTA_RECEBER cr
JOIN DIM_TEMPO t ON cr.data_id = t.data_id
WHERE cr.status = 'Recebido' AND t.ano = YEAR(GETDATE())
GROUP BY t.mes, t.nome_mes
ORDER BY t.mes
```

#### 3.2 Fluxo de Caixa (/financeiro/fluxo-caixa)
```sql
-- Fluxo Mensal
SELECT t.mes, t.nome_mes,
       SUM(receita_total) as receita,
       SUM(despesa_total) as despesa,
       SUM(receita_total - despesa_total) as resultado
FROM FACT_FLUXO_CAIXA fc
JOIN DIM_TEMPO t ON fc.data_id = t.data_id
WHERE t.ano = YEAR(GETDATE())
GROUP BY t.mes, t.nome_mes
ORDER BY t.mes

-- Projeção Próximos 3 Meses
WITH receita_media AS (
    SELECT AVG(receita_total) as media_receita
    FROM FACT_FLUXO_CAIXA fc
    JOIN DIM_TEMPO t ON fc.data_id = t.data_id
    WHERE t.data_completa >= DATEADD(month, -6, GETDATE())
),
despesa_media AS (
    SELECT AVG(despesa_total) as media_despesa
    FROM FACT_FLUXO_CAIXA fc
    JOIN DIM_TEMPO t ON fc.data_id = t.data_id
    WHERE t.data_completa >= DATEADD(month, -6, GETDATE())
)
SELECT
    DATEADD(month, 1, GETDATE()) as mes_projecao,
    rm.media_receita as receita_projetada,
    dm.media_despesa as despesa_projetada,
    (rm.media_receita - dm.media_despesa) as resultado_projetado
FROM receita_media rm, despesa_media dm
```

### 4. MÓDULO PACIENTE

#### 4.1 Atendimentos Realizados (/paciente/atendimentos-realizados)
```sql
-- Resumo de Atendimentos
SELECT
    COUNT(*) as total_atendimentos,
    SUM(a.valor) as valor_total,
    AVG(a.valor) as ticket_medio,
    COUNT(DISTINCT a.paciente_id) as pacientes_unicos
FROM FACT_AGENDAMENTO a
JOIN DIM_STATUS_AGENDAMENTO sa ON a.status_id = sa.status_id
WHERE sa.status = 'Realizado'

-- Top 10 Pacientes por Valor
SELECT p.nome,
       COUNT(a.agendamento_id) as total_atendimentos,
       SUM(a.valor) as valor_total,
       AVG(a.valor) as ticket_medio,
       MAX(t.data_completa) as ultimo_atendimento
FROM FACT_AGENDAMENTO a
JOIN DIM_PACIENTE p ON a.paciente_id = p.paciente_id
JOIN DIM_STATUS_AGENDAMENTO sa ON a.status_id = sa.status_id
JOIN DIM_TEMPO t ON a.data_id = t.data_id
WHERE sa.status = 'Realizado'
GROUP BY p.paciente_id, p.nome
ORDER BY valor_total DESC
LIMIT 10

-- Atendimentos por Especialidade
SELECT e.nome as especialidade,
       COUNT(*) as quantidade,
       SUM(a.valor) as valor_total,
       AVG(a.valor) as valor_medio
FROM FACT_AGENDAMENTO a
JOIN DIM_PROFISSIONAL prof ON a.profissional_id = prof.profissional_id
JOIN DIM_ESPECIALIDADE e ON prof.especialidade_id = e.especialidade_id
JOIN DIM_STATUS_AGENDAMENTO sa ON a.status_id = sa.status_id
WHERE sa.status = 'Realizado'
GROUP BY e.especialidade_id, e.nome
ORDER BY quantidade DESC
```

#### 4.2 Créditos Disponíveis (/paciente/creditos-disponiveis)
```sql
-- Resumo de Créditos
SELECT
    COUNT(DISTINCT paciente_id) as pacientes_com_credito,
    SUM(saldo_disponivel) as total_creditos,
    AVG(saldo_disponivel) as credito_medio,
    SUM(CASE WHEN data_expiracao < GETDATE() THEN saldo_disponivel ELSE 0 END) as creditos_expirados
FROM FACT_CREDITO_PACIENTE
WHERE status = 'Ativo'

-- Top 10 Pacientes com Maior Crédito
SELECT p.nome,
       SUM(cp.saldo_disponivel) as credito_total,
       COUNT(*) as quantidade_creditos,
       MIN(cp.data_expiracao) as proxima_expiracao
FROM FACT_CREDITO_PACIENTE cp
JOIN DIM_PACIENTE p ON cp.paciente_id = p.paciente_id
WHERE cp.status = 'Ativo' AND cp.saldo_disponivel > 0
GROUP BY p.paciente_id, p.nome
ORDER BY credito_total DESC
LIMIT 10

-- Créditos por Origem
SELECT origem_credito,
       COUNT(*) as quantidade,
       SUM(valor_credito) as valor_total,
       SUM(saldo_disponivel) as saldo_disponivel
FROM FACT_CREDITO_PACIENTE
WHERE status = 'Ativo'
GROUP BY origem_credito
ORDER BY valor_total DESC
```

#### 4.3 Orçamentos Fechados (/paciente/orcamentos-fechados)
```sql
-- Resumo de Orçamentos Fechados
SELECT
    COUNT(*) as total_orcamentos,
    SUM(valor_final) as valor_total,
    AVG(valor_final) as valor_medio,
    AVG(desconto) as desconto_medio,
    COUNT(DISTINCT paciente_id) as pacientes_unicos
FROM FACT_ORCAMENTO
WHERE status = 'Fechado'

-- Orçamentos por Profissional
SELECT p.nome as profissional,
       COUNT(*) as orcamentos_fechados,
       SUM(o.valor_final) as valor_total,
       AVG(o.valor_final) as valor_medio,
       AVG(o.desconto) as desconto_medio
FROM FACT_ORCAMENTO o
JOIN DIM_PROFISSIONAL p ON o.profissional_id = p.profissional_id
WHERE o.status = 'Fechado'
GROUP BY p.profissional_id, p.nome
ORDER BY valor_total DESC

-- Procedimentos Mais Vendidos
SELECT pr.nome as procedimento,
       SUM(io.quantidade) as quantidade_vendida,
       SUM(io.valor_total) as valor_total,
       AVG(io.valor_unitario) as preco_medio
FROM FACT_ITEM_ORCAMENTO io
JOIN FACT_ORCAMENTO o ON io.orcamento_id = o.orcamento_id
JOIN DIM_PROCEDIMENTO pr ON io.procedimento_id = pr.procedimento_id
WHERE o.status = 'Fechado'
GROUP BY pr.procedimento_id, pr.nome
ORDER BY quantidade_vendida DESC
```

### 5. MÓDULO AMIGOCARE+

#### 5.1 Avaliação NPS (/amigocare/avaliacao-nps)
```sql
-- Cálculo do NPS
WITH nps_scores AS (
    SELECT score,
           CASE
               WHEN score >= 9 THEN 'Promotor'
               WHEN score >= 7 THEN 'Neutro'
               ELSE 'Detrator'
           END as categoria
    FROM FACT_AVALIACAO_NPS
    WHERE score IS NOT NULL
),
nps_summary AS (
    SELECT
        COUNT(*) as total_respostas,
        SUM(CASE WHEN categoria = 'Promotor' THEN 1 ELSE 0 END) as promotores,
        SUM(CASE WHEN categoria = 'Detrator' THEN 1 ELSE 0 END) as detratores
    FROM nps_scores
)
SELECT
    total_respostas,
    promotores,
    detratores,
    (promotores * 100.0 / total_respostas) as perc_promotores,
    (detratores * 100.0 / total_respostas) as perc_detratores,
    ((promotores - detratores) * 100.0 / total_respostas) as nps_score
FROM nps_summary

-- NPS por Profissional
SELECT p.nome as profissional,
       COUNT(nps.score) as total_avaliacoes,
       AVG(CAST(nps.score as FLOAT)) as score_medio,
       SUM(CASE WHEN nps.score >= 9 THEN 1 ELSE 0 END) as promotores,
       SUM(CASE WHEN nps.score <= 6 THEN 1 ELSE 0 END) as detratores,
       ((SUM(CASE WHEN nps.score >= 9 THEN 1 ELSE 0 END) -
         SUM(CASE WHEN nps.score <= 6 THEN 1 ELSE 0 END)) * 100.0 / COUNT(nps.score)) as nps_individual
FROM FACT_AVALIACAO_NPS nps
JOIN DIM_PROFISSIONAL p ON nps.profissional_id = p.profissional_id
WHERE nps.score IS NOT NULL
GROUP BY p.profissional_id, p.nome
HAVING COUNT(nps.score) >= 5
ORDER BY nps_individual DESC

-- Evolução do NPS por Mês
SELECT t.ano, t.mes, t.nome_mes,
       COUNT(nps.score) as total_avaliacoes,
       AVG(CAST(nps.score as FLOAT)) as score_medio,
       ((SUM(CASE WHEN nps.score >= 9 THEN 1 ELSE 0 END) -
         SUM(CASE WHEN nps.score <= 6 THEN 1 ELSE 0 END)) * 100.0 / COUNT(nps.score)) as nps_mensal
FROM FACT_AVALIACAO_NPS nps
JOIN DIM_TEMPO t ON nps.data_id = t.data_id
WHERE nps.score IS NOT NULL
GROUP BY t.ano, t.mes, t.nome_mes
ORDER BY t.ano, t.mes
```

#### 5.2 Leads (/amigocare/leads)
```sql
-- Resumo de Leads
SELECT
    COUNT(*) as total_leads,
    COUNT(CASE WHEN status = 'Convertido' THEN 1 END) as convertidos,
    COUNT(CASE WHEN status = 'Ativo' THEN 1 END) as ativos,
    COUNT(CASE WHEN status = 'Perdido' THEN 1 END) as perdidos,
    (COUNT(CASE WHEN status = 'Convertido' THEN 1 END) * 100.0 / COUNT(*)) as taxa_conversao,
    SUM(valor_potencial) as valor_potencial_total,
    AVG(valor_potencial) as valor_potencial_medio
FROM FACT_LEAD

-- Leads por Origem
SELECT ol.origem,
       COUNT(*) as quantidade,
       COUNT(CASE WHEN l.status = 'Convertido' THEN 1 END) as convertidos,
       (COUNT(CASE WHEN l.status = 'Convertido' THEN 1 END) * 100.0 / COUNT(*)) as taxa_conversao,
       SUM(l.valor_potencial) as valor_potencial,
       ol.custo_medio_lead,
       (ol.custo_medio_lead * COUNT(*)) as custo_total
FROM FACT_LEAD l
JOIN DIM_ORIGEM_LEAD ol ON l.origem_id = ol.origem_id
GROUP BY ol.origem_id, ol.origem, ol.custo_medio_lead
ORDER BY quantidade DESC

-- Funil de Conversão
SELECT status,
       COUNT(*) as quantidade,
       (COUNT(*) * 100.0 / SUM(COUNT(*)) OVER()) as percentual,
       SUM(valor_potencial) as valor_potencial
FROM FACT_LEAD
GROUP BY status
ORDER BY
    CASE status
        WHEN 'Novo' THEN 1
        WHEN 'Contato' THEN 2
        WHEN 'Qualificado' THEN 3
        WHEN 'Proposta' THEN 4
        WHEN 'Convertido' THEN 5
        WHEN 'Perdido' THEN 6
    END
```

## MÉTRICAS E KPIS CONSOLIDADOS

### KPIs PRINCIPAIS DO DASHBOARD
```sql
-- 1. Vendas Total (Anual)
SELECT SUM(valor_producao) as vendas_total
FROM FACT_PRODUCAO_MEDICA pm
JOIN DIM_TEMPO t ON pm.data_id = t.data_id
WHERE t.ano = YEAR(GETDATE())

-- 2. Produção Total (Anual)
SELECT SUM(custo_operacional) as producao_total
FROM FACT_PRODUCAO_MEDICA pm
JOIN DIM_TEMPO t ON pm.data_id = t.data_id
WHERE t.ano = YEAR(GETDATE())

-- 3. Agendamentos Mês Corrente
SELECT COUNT(*) as agendamentos_mes_corrente
FROM FACT_AGENDAMENTO a
JOIN DIM_TEMPO t ON a.data_id = t.data_id
WHERE t.ano = YEAR(GETDATE()) AND t.mes = MONTH(GETDATE())

-- 4. Atendimentos Mês Corrente
SELECT COUNT(*) as atendimentos_mes_corrente
FROM FACT_AGENDAMENTO a
JOIN DIM_TEMPO t ON a.data_id = t.data_id
JOIN DIM_STATUS_AGENDAMENTO sa ON a.status_id = sa.status_id
WHERE t.ano = YEAR(GETDATE()) AND t.mes = MONTH(GETDATE())
  AND sa.status = 'Realizado'
```

### MÉTRICAS OPERACIONAIS
```sql
-- Taxa de Ocupação
SELECT
    (COUNT(CASE WHEN sa.status = 'Realizado' THEN 1 END) * 100.0 / COUNT(*)) as taxa_ocupacao
FROM FACT_AGENDAMENTO a
JOIN DIM_STATUS_AGENDAMENTO sa ON a.status_id = sa.status_id
JOIN DIM_TEMPO t ON a.data_id = t.data_id
WHERE t.ano = YEAR(GETDATE())

-- Tempo Médio de Atendimento
SELECT AVG(tempo_atendimento_minutos) as tempo_medio_atendimento
FROM FACT_TEMPO_ATENDIMENTO ta
JOIN DIM_TEMPO t ON ta.data_id = t.data_id
WHERE t.ano = YEAR(GETDATE())

-- Taxa de Cancelamento
SELECT
    (COUNT(CASE WHEN sa.status = 'Cancelado' THEN 1 END) * 100.0 / COUNT(*)) as taxa_cancelamento
FROM FACT_AGENDAMENTO a
JOIN DIM_STATUS_AGENDAMENTO sa ON a.status_id = sa.status_id
JOIN DIM_TEMPO t ON a.data_id = t.data_id
WHERE t.ano = YEAR(GETDATE())

-- Taxa de Conversão de Leads
SELECT
    (COUNT(CASE WHEN status = 'Convertido' THEN 1 END) * 100.0 / COUNT(*)) as taxa_conversao_leads
FROM FACT_LEAD
```

### MÉTRICAS FINANCEIRAS
```sql
-- Receita Total
SELECT SUM(valor) as receita_total
FROM FACT_TRANSACAO_FINANCEIRA
WHERE tipo_transacao = 'Receita'

-- Margem de Contribuição
SELECT
    SUM(pm.valor_producao - pm.custo_operacional) as margem_total,
    (SUM(pm.valor_producao - pm.custo_operacional) * 100.0 / SUM(pm.valor_producao)) as margem_percentual
FROM FACT_PRODUCAO_MEDICA pm

-- Ticket Médio
SELECT AVG(valor) as ticket_medio
FROM FACT_AGENDAMENTO a
JOIN DIM_STATUS_AGENDAMENTO sa ON a.status_id = sa.status_id
WHERE sa.status = 'Realizado'

-- Inadimplência
SELECT
    (SUM(CASE WHEN dias_atraso > 30 THEN valor_pendente ELSE 0 END) * 100.0 / SUM(valor_original)) as taxa_inadimplencia
FROM FACT_CONTA_RECEBER
```

## ESTRUTURA DE AGREGAÇÕES PRÉ-CALCULADAS

### TABELAS DE AGREGAÇÃO RECOMENDADAS

#### AGG_VENDAS_MENSAL
```sql
CREATE TABLE AGG_VENDAS_MENSAL (
    ano INT,
    mes INT,
    unidade_id INT,
    especialidade_id INT,
    valor_total DECIMAL(15,2),
    quantidade_atendimentos INT,
    ticket_medio DECIMAL(10,2),
    data_atualizacao DATETIME
)
```

#### AGG_PERFORMANCE_PROFISSIONAL_MENSAL
```sql
CREATE TABLE AGG_PERFORMANCE_PROFISSIONAL_MENSAL (
    ano INT,
    mes INT,
    profissional_id INT,
    valor_producao DECIMAL(15,2),
    quantidade_atendimentos INT,
    tempo_medio_atendimento INT,
    nps_medio DECIMAL(3,1),
    data_atualizacao DATETIME
)
```

#### AGG_LEADS_ORIGEM_MENSAL
```sql
CREATE TABLE AGG_LEADS_ORIGEM_MENSAL (
    ano INT,
    mes INT,
    origem_id INT,
    quantidade_leads INT,
    leads_convertidos INT,
    taxa_conversao DECIMAL(5,2),
    valor_potencial DECIMAL(15,2),
    custo_total DECIMAL(15,2),
    data_atualizacao DATETIME
)
```

## ÍNDICES RECOMENDADOS

### DIMENSÕES
```sql
-- DIM_TEMPO
CREATE INDEX IX_DIM_TEMPO_ANO_MES ON DIM_TEMPO (ano, mes)
CREATE INDEX IX_DIM_TEMPO_DATA ON DIM_TEMPO (data_completa)

-- DIM_PACIENTE
CREATE INDEX IX_DIM_PACIENTE_CPF ON DIM_PACIENTE (cpf)
CREATE INDEX IX_DIM_PACIENTE_EMAIL ON DIM_PACIENTE (email)

-- DIM_PROFISSIONAL
CREATE INDEX IX_DIM_PROFISSIONAL_ESPECIALIDADE ON DIM_PROFISSIONAL (especialidade_id)
CREATE INDEX IX_DIM_PROFISSIONAL_UNIDADE ON DIM_PROFISSIONAL (unidade_id)
```

### FATOS
```sql
-- FACT_AGENDAMENTO
CREATE INDEX IX_FACT_AGENDAMENTO_DATA ON FACT_AGENDAMENTO (data_id)
CREATE INDEX IX_FACT_AGENDAMENTO_PACIENTE ON FACT_AGENDAMENTO (paciente_id)
CREATE INDEX IX_FACT_AGENDAMENTO_PROFISSIONAL ON FACT_AGENDAMENTO (profissional_id)
CREATE INDEX IX_FACT_AGENDAMENTO_STATUS ON FACT_AGENDAMENTO (status_id)

-- FACT_PRODUCAO_MEDICA
CREATE INDEX IX_FACT_PRODUCAO_DATA ON FACT_PRODUCAO_MEDICA (data_id)
CREATE INDEX IX_FACT_PRODUCAO_PROFISSIONAL ON FACT_PRODUCAO_MEDICA (profissional_id)

-- FACT_LEAD
CREATE INDEX IX_FACT_LEAD_DATA ON FACT_LEAD (data_id)
CREATE INDEX IX_FACT_LEAD_ORIGEM ON FACT_LEAD (origem_id)
CREATE INDEX IX_FACT_LEAD_STATUS ON FACT_LEAD (status)
```

## PROCESSOS ETL RECOMENDADOS

### CARGA INCREMENTAL
```sql
-- Exemplo de processo ETL para FACT_AGENDAMENTO
MERGE FACT_AGENDAMENTO AS target
USING (
    SELECT
        a.agendamento_id,
        t.data_id,
        a.paciente_id,
        a.profissional_id,
        a.unidade_id,
        a.procedimento_id,
        sa.status_id,
        a.hora_agendamento,
        a.forma_pagamento,
        a.valor,
        a.observacoes,
        a.data_criacao,
        GETDATE() as data_atualizacao
    FROM staging_agendamentos a
    JOIN DIM_TEMPO t ON CAST(a.data_agendamento AS DATE) = t.data_completa
    JOIN DIM_STATUS_AGENDAMENTO sa ON a.status = sa.status
    WHERE a.data_atualizacao > @ultima_carga
) AS source ON target.agendamento_id = source.agendamento_id
WHEN MATCHED THEN
    UPDATE SET
        status_id = source.status_id,
        valor = source.valor,
        data_atualizacao = source.data_atualizacao
WHEN NOT MATCHED THEN
    INSERT VALUES (
        source.agendamento_id,
        source.data_id,
        source.paciente_id,
        source.profissional_id,
        source.unidade_id,
        source.procedimento_id,
        source.status_id,
        source.hora_agendamento,
        source.forma_pagamento,
        source.valor,
        source.observacoes,
        source.data_criacao,
        source.data_atualizacao
    );
```

## MONITORAMENTO E QUALIDADE

### VALIDAÇÕES DE QUALIDADE
```sql
-- Verificar integridade referencial
SELECT COUNT(*) as registros_orfaos
FROM FACT_AGENDAMENTO a
LEFT JOIN DIM_PACIENTE p ON a.paciente_id = p.paciente_id
WHERE p.paciente_id IS NULL

-- Verificar completude de dados críticos
SELECT
    (COUNT(CASE WHEN valor IS NULL THEN 1 END) * 100.0 / COUNT(*)) as perc_valores_nulos
FROM FACT_AGENDAMENTO

-- Verificar consistência temporal
SELECT COUNT(*) as agendamentos_futuros_realizados
FROM FACT_AGENDAMENTO a
JOIN DIM_TEMPO t ON a.data_id = t.data_id
JOIN DIM_STATUS_AGENDAMENTO sa ON a.status_id = sa.status_id
WHERE t.data_completa > GETDATE() AND sa.status = 'Realizado'
```

### ALERTAS AUTOMÁTICOS
```sql
-- Alerta: Queda significativa em agendamentos
WITH agendamentos_hoje AS (
    SELECT COUNT(*) as total_hoje
    FROM FACT_AGENDAMENTO a
    JOIN DIM_TEMPO t ON a.data_id = t.data_id
    WHERE t.data_completa = CAST(GETDATE() AS DATE)
),
media_ultimos_30_dias AS (
    SELECT AVG(daily_count) as media_30_dias
    FROM (
        SELECT t.data_completa, COUNT(*) as daily_count
        FROM FACT_AGENDAMENTO a
        JOIN DIM_TEMPO t ON a.data_id = t.data_id
        WHERE t.data_completa BETWEEN DATEADD(day, -30, GETDATE()) AND DATEADD(day, -1, GETDATE())
        GROUP BY t.data_completa
    ) daily_stats
)
SELECT
    ah.total_hoje,
    m30.media_30_dias,
    (ah.total_hoje * 100.0 / m30.media_30_dias - 100) as variacao_percentual,
    CASE
        WHEN ah.total_hoje < m30.media_30_dias * 0.7 THEN 'CRÍTICO'
        WHEN ah.total_hoje < m30.media_30_dias * 0.8 THEN 'ATENÇÃO'
        ELSE 'NORMAL'
    END as status_alerta
FROM agendamentos_hoje ah, media_ultimos_30_dias m30
```

## CONCLUSÃO

Esta especificação fornece a base completa para implementação de um Data Warehouse OLAP robusto que suporta todas as análises e visualizações do Amigo DataHub. A arquitetura dimensional proposta permite:

1. **Escalabilidade**: Suporte a grandes volumes de dados com performance otimizada
2. **Flexibilidade**: Facilita a criação de novos relatórios e análises
3. **Qualidade**: Garante integridade e consistência dos dados
4. **Performance**: Agregações pré-calculadas para consultas rápidas
5. **Manutenibilidade**: Estrutura clara e bem documentada

A implementação deve seguir as fases propostas, priorizando os KPIs críticos do dashboard principal e expandindo gradualmente para os módulos especializados.

{"version": "1.0", "schema_name": "Amigo DataHub Global Schema", "description": "Schema global para um data hub de clínica médica com foco em relatórios integrados", "core_entities": [{"entity_name": "Paciente", "description": "Entidade central que representa um paciente da clínica", "required_fields": ["id", "nome"], "recommended_fields": ["cpf", "data_nascimento", "email", "telefone", "sexo", "como_conheceu"], "optional_fields": ["profi<PERSON><PERSON>", "endereco", "vip", "plano_saude", "matricula_plano", "cns", "cod_legado"], "primary_key": "id", "unique_constraints": ["cpf", "email"], "relationships": [{"entity": "Agendamento", "type": "one-to-many", "field": "paciente_id"}, {"entity": "TransacaoFinanceira", "type": "one-to-many", "field": "paciente_id"}, {"entity": "AvaliacaoNPS", "type": "one-to-many", "field": "paciente_id"}, {"entity": "Orcamento", "type": "one-to-many", "field": "paciente_id"}, {"entity": "Lead", "type": "one-to-one", "field": "paciente_id"}], "minimum_data_required": ["id", "nome"], "data_sources": ["relatorio_atendimentos_realizados.xlsx", "relatorio_agendamento.xlsx", "relatorio_producao_medica.xlsx"]}, {"entity_name": "Profissional", "description": "Profissional de saúde que atende na clínica", "required_fields": ["id", "nome"], "recommended_fields": ["especialidade", "cbo", "unidade_id"], "optional_fields": ["email", "telefone", "registro_profissional", "percentual_repasse"], "primary_key": "id", "relationships": [{"entity": "Agendamento", "type": "one-to-many", "field": "profissional_id"}, {"entity": "Unidade", "type": "many-to-one", "field": "unidade_id"}], "minimum_data_required": ["id", "nome"], "data_sources": ["relatorio_producao_medica.xlsx", "relatorio_agendamento.xlsx"]}, {"entity_name": "Agendamento", "description": "Agendamento de consulta ou procedimento", "required_fields": ["id", "data", "paciente_id"], "recommended_fields": ["hora", "profissional_id", "unidade_id", "procedimento_id", "status"], "optional_fields": ["tipo_atendimento", "forma_pagamento", "sala", "observacao", "hora_chegada", "hora_inicio", "hora_fim"], "primary_key": "id", "relationships": [{"entity": "Paciente", "type": "many-to-one", "field": "paciente_id"}, {"entity": "Profissional", "type": "many-to-one", "field": "profissional_id"}, {"entity": "Unidade", "type": "many-to-one", "field": "unidade_id"}, {"entity": "Procedimento", "type": "many-to-one", "field": "procedimento_id"}, {"entity": "TransacaoFinanceira", "type": "one-to-many", "field": "agendamento_id"}, {"entity": "AvaliacaoNPS", "type": "one-to-one", "field": "agendamento_id"}], "minimum_data_required": ["id", "data", "paciente_id"], "data_sources": ["relatorio_agendamento.xlsx", "relatorio_cancelamentos.xlsx", "relatorio_tempo_atendimento.xlsx"]}, {"entity_name": "TransacaoFinanceira", "description": "Transação financeira relacionada a um agendamento ou paciente", "required_fields": ["id", "valor", "data", "agendamento_id"], "recommended_fields": ["paciente_id", "tipo", "status", "forma_pagamento"], "optional_fields": ["desconto", "observacao", "categoria", "classificacao", "centro_custo", "unidade_id", "nota_fiscal", "recibo", "data_vencimento", "data_pagamento"], "primary_key": "id", "relationships": [{"entity": "Paciente", "type": "many-to-one", "field": "paciente_id"}, {"entity": "Agendamento", "type": "many-to-one", "field": "agendamento_id"}, {"entity": "Unidade", "type": "many-to-one", "field": "unidade_id"}, {"entity": "<PERSON><PERSON><PERSON>", "type": "one-to-many", "field": "transacao_id"}], "minimum_data_required": ["id", "valor", "data", "agendamento_id"], "data_sources": ["relatorio_contas_a_receber.xlsx", "relatorio_contas_a_pagar.xlsx", "relatorio_fluxo_caixa.xlsx", "relatorio_fechamento_caixa.xlsx"]}, {"entity_name": "<PERSON><PERSON><PERSON>", "description": "Parcela de uma transação financeira", "required_fields": ["id", "transacao_id", "valor", "numero_parcela"], "recommended_fields": ["data_vencimento", "status", "forma_pagamento"], "optional_fields": ["data_pagamento", "observacao"], "primary_key": "id", "relationships": [{"entity": "TransacaoFinanceira", "type": "many-to-one", "field": "transacao_id"}], "minimum_data_required": ["id", "transacao_id", "valor", "numero_parcela"], "data_sources": ["relatorio_contas_a_receber.xlsx", "relatorio_fluxo_caixa.xlsx"]}, {"entity_name": "Procedimento", "description": "Procedimento ou serviço oferecido pela clínica", "required_fields": ["id", "nome"], "recommended_fields": ["categoria", "valor_base"], "optional_fields": ["grupo", "subgrupo", "codigo_tuss", "tempo_estimado"], "primary_key": "id", "relationships": [{"entity": "Agendamento", "type": "one-to-many", "field": "procedimento_id"}], "minimum_data_required": ["id", "nome"], "data_sources": ["relatorio_producao_medica.xlsx", "relatorio_agendamento.xlsx"]}, {"entity_name": "Unidade", "description": "Unidade ou filial da clínica", "required_fields": ["id", "nome"], "recommended_fields": ["endereco", "telefone"], "optional_fields": ["email", "responsavel", "cnpj"], "primary_key": "id", "relationships": [{"entity": "Agendamento", "type": "one-to-many", "field": "unidade_id"}, {"entity": "Profissional", "type": "one-to-many", "field": "unidade_id"}, {"entity": "TransacaoFinanceira", "type": "one-to-many", "field": "unidade_id"}], "minimum_data_required": ["id", "nome"], "data_sources": ["relatorio_producao_medica.xlsx", "relatorio_agendamento.xlsx", "relatorio_contas_a_receber.xlsx"]}, {"entity_name": "AvaliacaoNPS", "description": "Avaliação de satisfação do paciente (NPS)", "required_fields": ["id", "score", "data"], "recommended_fields": ["paciente_id", "agendamento_id", "categoria"], "optional_fields": ["comentario", "profissional_id"], "primary_key": "id", "relationships": [{"entity": "Paciente", "type": "many-to-one", "field": "paciente_id"}, {"entity": "Agendamento", "type": "one-to-one", "field": "agendamento_id"}, {"entity": "Profissional", "type": "many-to-one", "field": "profissional_id"}], "minimum_data_required": ["id", "score", "data"], "data_sources": ["relatorio_avaliacao_nps.xlsx"]}, {"entity_name": "Orcamento", "description": "Orçamento de procedimentos para um paciente", "required_fields": ["id", "paciente_id", "data", "valor_total"], "recommended_fields": ["status", "validade", "profissional_id"], "optional_fields": ["observacao", "desconto", "forma_pagamento"], "primary_key": "id", "relationships": [{"entity": "Paciente", "type": "many-to-one", "field": "paciente_id"}, {"entity": "Profissional", "type": "many-to-one", "field": "profissional_id"}, {"entity": "ItemOrcamento", "type": "one-to-many", "field": "orcamento_id"}, {"entity": "TransacaoFinanceira", "type": "one-to-one", "field": "orcamento_id"}], "minimum_data_required": ["id", "paciente_id", "data", "valor_total"], "data_sources": ["relatorio_orçamentos_fechados.xlsx", "relatorio_orçamentos_abertos.xlsx"]}, {"entity_name": "ItemOrcamento", "description": "Item de um orçamento", "required_fields": ["id", "orcamento_id", "procedimento_id", "valor"], "recommended_fields": ["quantidade", "desconto"], "optional_fields": ["observacao"], "primary_key": "id", "relationships": [{"entity": "Orcamento", "type": "many-to-one", "field": "orcamento_id"}, {"entity": "Procedimento", "type": "many-to-one", "field": "procedimento_id"}], "minimum_data_required": ["id", "orcamento_id", "procedimento_id", "valor"], "data_sources": ["relatorio_orçamentos_fechados.xlsx", "relatorio_orçamentos_abertos.xlsx"]}, {"entity_name": "Lead", "description": "Lead de potencial paciente", "required_fields": ["id", "nome", "data_criacao"], "recommended_fields": ["email", "telefone", "origem", "status"], "optional_fields": ["observacao", "interesse", "campanha_id", "paciente_id"], "primary_key": "id", "relationships": [{"entity": "<PERSON>an<PERSON>", "type": "many-to-one", "field": "campanha_id"}, {"entity": "Paciente", "type": "one-to-one", "field": "paciente_id"}], "minimum_data_required": ["id", "nome", "data_criacao"], "data_sources": ["relatorio_leads.xlsx"]}, {"entity_name": "<PERSON>an<PERSON>", "description": "Campanha de marketing", "required_fields": ["id", "nome", "data_inicio"], "recommended_fields": ["data_fim", "status", "tipo"], "optional_fields": ["objetivo", "investimento", "responsavel", "canal"], "primary_key": "id", "relationships": [{"entity": "Lead", "type": "one-to-many", "field": "campanha_id"}], "minimum_data_required": ["id", "nome", "data_inicio"], "data_sources": ["relatorio_campanhas.xlsx"]}], "data_mapping": {"relatorio_producao_medica.xlsx": {"entities": ["Paciente", "Profissional", "Agendamento", "Procedimento", "TransacaoFinanceira", "Unidade"], "field_mappings": {"Paciente": {"id": "ID amigo", "nome": "Paciente", "idade": "<PERSON><PERSON>", "como_conheceu": "Como conheceu", "cod_legado": "Cod<PERSON>", "matricula_plano": "<PERSON><PERSON><PERSON><PERSON>", "cns": "CNS", "profissao": "Profissão"}, "Profissional": {"nome": "Profissional", "especialidade": "Especialidade", "cbo": "CBO"}, "Agendamento": {"data": "Data Atendimento", "hora": "<PERSON><PERSON>", "codigo": "<PERSON><PERSON><PERSON>", "hora_chegada": "<PERSON><PERSON><PERSON><PERSON>", "tipo_atendimento": "T<PERSON>o <PERSON>endimento", "forma_pagamento": "Forma de Pagamento"}, "Procedimento": {"nome": "<PERSON><PERSON>", "tipo": "Tipo", "grupo": "Grupo", "subgrupo": "Subgrupo", "quantidade": "Quantidade"}, "TransacaoFinanceira": {"valor": "Valor total do item R$", "desconto": "Desconto R$", "forma_pagamento": "Forma de Pagamento", "nota_fiscal": "Nota Emitida"}, "Unidade": {"nome": "Unidade"}}}, "relatorio_agendamento.xlsx": {"entities": ["Paciente", "Profissional", "Agendamento", "Unidade"], "field_mappings": {"Paciente": {"nome": "Paciente", "cpf": "CPF", "telefone": "Telefone", "email": "Email", "cod_legado": "Cód. Leg.", "matricula_plano": "<PERSON><PERSON><PERSON><PERSON>"}, "Profissional": {"nome": "Médico"}, "Agendamento": {"data": "Data do agendamento", "hora": "<PERSON><PERSON>", "tipo_atendimento": "Tipo de Atendimento", "status": "Status", "forma_pagamento": "<PERSON><PERSON><PERSON><PERSON>"}, "Unidade": {"nome": "Unidade"}}}, "relatorio_contas_a_receber.xlsx": {"entities": ["TransacaoFinanceira", "Unidade"], "field_mappings": {"TransacaoFinanceira": {"data_vencimento": "Data de vencimento", "competencia": "Competência", "receber_de": "<PERSON><PERSON><PERSON> de", "categoria": "Categoria", "classificacao": "Classificação", "descricao": "Descrição", "forma_pagamento": "Forma de Pagamento", "observacao": "Observação", "numero_documento": "Nº documento", "tag": "Tag", "centro_custo": "Centro de custo", "valor": "Valor Líquido R$", "nota_fiscal": "Nota Emitida", "recibo": "Recibo Emitido"}, "Unidade": {"nome": "Unidade"}}}}, "analysis_modules": {"agenda": {"description": "Análises relacionadas a agendamentos, produção médica e tempo de atendimento", "required_entities": ["Agendamento"], "recommended_entities": ["Paciente", "Profissional", "Unidade", "Procedimento"], "analyses": [{"name": "Agendamentos por período", "description": "Análise de agendamentos por período (dia, semana, mês)", "minimum_data": ["Agendamento.data", "Agendamento.status"]}, {"name": "Produção médica", "description": "Análise de produção por profissional", "minimum_data": ["Agendamento.data", "Agendamento.profissional_id", "Agendamento.status"]}, {"name": "Tempo de atendimento", "description": "Análise de tempo médio de atendimento por profissional ou procedimento", "minimum_data": ["Agendamento.hora_inicio", "Agendamento.hora_fim", "Agendamento.profissional_id"]}, {"name": "Cancelamentos", "description": "Análise de cancelamentos por motivo, profissional ou período", "minimum_data": ["Agendamento.status", "Agendamento.data"]}]}, "financeiro": {"description": "Análises financeiras como contas a receber, contas a pagar e fluxo de caixa", "required_entities": ["TransacaoFinanceira"], "recommended_entities": ["<PERSON><PERSON><PERSON>", "Paciente", "Agendamento"], "analyses": [{"name": "Contas a receber", "description": "Análise de contas a receber por período, status ou categoria", "minimum_data": ["TransacaoFinanceira.data_vencimento", "TransacaoFinanceira.valor", "TransacaoFinanceira.status"]}, {"name": "Contas a pagar", "description": "Análise de contas a pagar por período, status ou categoria", "minimum_data": ["TransacaoFinanceira.data_vencimento", "TransacaoFinanceira.valor", "TransacaoFinanceira.status", "TransacaoFinanceira.tipo"]}, {"name": "Fluxo de caixa", "description": "Análise de fluxo de caixa por período", "minimum_data": ["TransacaoFinanceira.data", "TransacaoFinanceira.valor", "TransacaoFinanceira.tipo"]}, {"name": "Fechamento de caixa", "description": "Análise de fechamento de caixa por período ou unidade", "minimum_data": ["TransacaoFinanceira.data", "TransacaoFinanceira.valor", "TransacaoFinanceira.forma_pagamento"]}]}, "paciente": {"description": "Análises relacionadas a pacientes, atendimentos e orçamentos", "required_entities": ["Paciente"], "recommended_entities": ["Agendamento", "TransacaoFinanceira", "Orcamento"], "analyses": [{"name": "Atendimentos realizados", "description": "Análise de atendimentos realizados por paciente", "minimum_data": ["Paciente.id", "Paciente.nome", "Agendamento.data", "Agendamento.status"]}, {"name": "Créditos disponíveis", "description": "Análise de créditos disponíveis por paciente", "minimum_data": ["Paciente.id", "Paciente.nome", "TransacaoFinanceira.valor", "TransacaoFinanceira.tipo"]}, {"name": "Orçamentos fechados", "description": "Análise de orçamentos fechados por paciente ou período", "minimum_data": ["Paciente.id", "Paciente.nome", "Orcamento.data", "Orcamento.valor_total", "Orcamento.status"]}, {"name": "Orçamentos abertos", "description": "Análise de orçamentos abertos por paciente ou período", "minimum_data": ["Paciente.id", "Paciente.nome", "Orcamento.data", "Orcamento.valor_total", "Orcamento.status"]}]}, "amigocare": {"description": "Análises relacionadas a satisfação do cliente, leads e campanhas", "required_entities": ["AvaliacaoNPS"], "recommended_entities": ["Paciente", "Agendamento", "Lead", "<PERSON>an<PERSON>"], "analyses": [{"name": "Avaliação NPS", "description": "Análise de satisfação do cliente por período, profissional ou procedimento", "minimum_data": ["AvaliacaoNPS.score", "AvaliacaoNPS.data", "Paciente.id"]}, {"name": "Leads", "description": "<PERSON><PERSON><PERSON><PERSON> de leads por origem, status ou período", "minimum_data": ["Lead.nome", "Lead.data_criacao", "Lead.status"]}, {"name": "<PERSON><PERSON><PERSON>", "description": "Análise de campanhas por período, tipo ou resultado", "minimum_data": ["Campanha.nome", "Campanha.data_inicio", "Campanha.data_fim"]}, {"name": "Acompanhamento de pacientes", "description": "Análise de acompanhamento de pacientes por período ou status", "minimum_data": ["Paciente.id", "Paciente.nome", "Agendamento.data"]}]}}, "data_quality": {"validation_rules": [{"entity": "Paciente", "field": "email", "rule": "format", "pattern": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", "severity": "warning"}, {"entity": "Paciente", "field": "cpf", "rule": "format", "pattern": "^\\d{3}\\.\\d{3}\\.\\d{3}\\-\\d{2}$", "severity": "warning"}, {"entity": "TransacaoFinanceira", "field": "valor", "rule": "range", "min": 0, "severity": "error"}, {"entity": "AvaliacaoNPS", "field": "score", "rule": "range", "min": 0, "max": 10, "severity": "error"}], "transformation_rules": [{"entity": "Paciente", "field": "nome", "transformation": "trim_and_capitalize"}, {"entity": "Paciente", "field": "email", "transformation": "lowercase"}, {"entity": "TransacaoFinanceira", "field": "data", "transformation": "format_date", "format": "YYYY-MM-DD"}], "deduplication_rules": [{"entity": "Paciente", "match_fields": ["cpf"], "similarity_fields": ["nome", "email", "telefone"], "threshold": 0.8}, {"entity": "Profissional", "match_fields": [], "similarity_fields": ["nome", "especialidade"], "threshold": 0.9}]}, "data_integration": {"file_types": ["xlsx", "csv", "json", "xml"], "integration_methods": [{"name": "upload_file", "description": "Upload de arquivo para processamento", "supported_file_types": ["xlsx", "csv"]}, {"name": "api_integration", "description": "Integração via API com sistemas externos", "supported_formats": ["json", "xml"]}, {"name": "database_connection", "description": "Conexão direta com banco de dados", "supported_databases": ["mysql", "postgresql", "sqlserver"]}], "mapping_strategy": {"description": "Estratégia para mapeamento de dados de diferentes fontes", "steps": ["Identificação de entidades nos arquivos de origem", "Mapeamento de campos para o esquema global", "Validação de dados conforme regras de qualidade", "Transformação de dados para formato padronizado", "Deduplicação de registros", "Integração com dados existentes"]}}, "minimum_viable_datasets": {"basic_analysis": {"description": "Conjunto mínimo de dados para análises básicas", "required_entities": ["Paciente", "Agendamento", "TransacaoFinanceira"], "required_fields": {"Paciente": ["id", "nome"], "Agendamento": ["id", "data", "paciente_id", "status"], "TransacaoFinanceira": ["id", "valor", "data", "agendamento_id", "tipo"]}}, "financial_analysis": {"description": "Conjunto mínimo de dados para análises financeiras", "required_entities": ["TransacaoFinanceira"], "required_fields": {"TransacaoFinanceira": ["id", "valor", "data", "agendamento_id", "tipo", "status"]}}, "scheduling_analysis": {"description": "Conjunto mínimo de dados para análises de agendamento", "required_entities": ["Agendamento", "Profissional"], "required_fields": {"Agendamento": ["id", "data", "hora", "status", "profissional_id"], "Profissional": ["id", "nome"]}}, "patient_analysis": {"description": "Conjunto mínimo de dados para análises de pacientes", "required_entities": ["Paciente", "Agendamento"], "required_fields": {"Paciente": ["id", "nome"], "Agendamento": ["id", "data", "paciente_id", "status"]}}, "satisfaction_analysis": {"description": "Conjunto mínimo de dados para análises de satisfação", "required_entities": ["AvaliacaoNPS", "Paciente"], "required_fields": {"AvaliacaoNPS": ["id", "score", "data", "paciente_id"], "Paciente": ["id", "nome"]}}}}
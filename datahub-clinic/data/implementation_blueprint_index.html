<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blueprint de Implementação - Amigo DataHub</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3, h4 {
            color: #007AFF;
        }
        .section {
            margin-bottom: 30px;
            border: 1px solid #e5e5ea;
            border-radius: 10px;
            padding: 20px;
            background-color: #fff;
        }
        .card {
            border: 1px solid #e5e5ea;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            background-color: #fff;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        .card-title {
            color: #007AFF;
            margin-top: 0;
            margin-bottom: 10px;
        }
        .card-description {
            color: #666;
            margin-bottom: 15px;
        }
        .button {
            display: inline-block;
            background-color: #007AFF;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            text-decoration: none;
            transition: background-color 0.3s ease;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .header {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
        }
        .logo {
            width: 60px;
            height: 60px;
            margin-right: 20px;
        }
        .header-title {
            margin: 0;
        }
        .header-subtitle {
            margin: 5px 0 0 0;
            color: #666;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <img src="/static/amigo-logo.png" alt="Amigo Logo" class="logo">
        <div>
            <h1 class="header-title">Blueprint de Implementação - Amigo DataHub</h1>
            <p class="header-subtitle">Documentação técnica para implementação do sistema</p>
        </div>
    </div>

    <div class="section">
        <h2>Visão Geral</h2>
        <p>Este blueprint de implementação fornece um guia detalhado para a criação do Amigo DataHub, um sistema de análise de dados para clínicas médicas que permite a integração de diferentes fontes de dados com tolerância para criar análises com o mínimo de dados possível.</p>
        
        <p>O documento está dividido em várias seções que abordam diferentes aspectos da implementação, desde a arquitetura técnica até estratégias para lidar com conjuntos de dados mínimos.</p>
    </div>

    <h2>Documentação</h2>
    <div class="grid">
        <div class="card">
            <h3 class="card-title">Blueprint de Implementação - Parte 1</h3>
            <p class="card-description">Visão geral do sistema e fases de implementação, incluindo preparação, processamento de dados e análise.</p>
            <a href="implementation_blueprint_part1.html" class="button">Ver Documento</a>
        </div>
        
        <div class="card">
            <h3 class="card-title">Blueprint de Implementação - Parte 2</h3>
            <p class="card-description">Estratégia de implementação com dados mínimos e sistema de upload e mapeamento de dados.</p>
            <a href="implementation_blueprint_part2.html" class="button">Ver Documento</a>
        </div>
        
        <div class="card">
            <h3 class="card-title">Blueprint de Implementação - Parte 3</h3>
            <p class="card-description">Arquitetura técnica, adaptabilidade, escalabilidade e considerações finais para implementação.</p>
            <a href="implementation_blueprint_part3.html" class="button">Ver Documento</a>
        </div>
        
        <div class="card">
            <h3 class="card-title">Schema Global</h3>
            <p class="card-description">Definição detalhada do schema global em formato JSON, incluindo entidades, campos e relacionamentos.</p>
            <a href="global_schema.json" class="button">Ver Schema</a>
        </div>
        
        <div class="card">
            <h3 class="card-title">Diagrama do Schema</h3>
            <p class="card-description">Representação visual do schema global, mostrando entidades, campos e relacionamentos.</p>
            <a href="schema_diagram.html" class="button">Ver Diagrama</a>
        </div>
    </div>

    <div class="section">
        <h2>Próximos Passos</h2>
        <p>Após revisar a documentação, recomendamos seguir estes passos para iniciar a implementação:</p>
        
        <ol>
            <li>Definir o escopo inicial do projeto com base nos requisitos específicos da clínica</li>
            <li>Configurar o ambiente de desenvolvimento conforme descrito na Parte 1</li>
            <li>Implementar o schema global no banco de dados seguindo a estrutura definida</li>
            <li>Desenvolver o sistema de upload e mapeamento de dados conforme a Parte 2</li>
            <li>Implementar os módulos de análise priorizando os mais relevantes para o cliente</li>
            <li>Testar o sistema com dados reais e ajustar conforme necessário</li>
        </ol>
        
        <p>Para qualquer dúvida ou suporte adicional, entre em contato com a equipe de desenvolvimento do Amigo DataHub.</p>
    </div>
</body>
</html>

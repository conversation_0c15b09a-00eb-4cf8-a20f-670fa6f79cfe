<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blueprint de Implementação - Amigo DataHub (Parte 2)</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3, h4 {
            color: #007AFF;
        }
        .section {
            margin-bottom: 30px;
            border: 1px solid #e5e5ea;
            border-radius: 10px;
            padding: 20px;
            background-color: #fff;
        }
        .phase {
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #e5e5ea;
        }
        .phase:last-child {
            border-bottom: none;
        }
        .step {
            margin-bottom: 15px;
        }
        .step-number {
            display: inline-block;
            width: 30px;
            height: 30px;
            background-color: #007AFF;
            color: white;
            text-align: center;
            line-height: 30px;
            border-radius: 50%;
            margin-right: 10px;
        }
        .note {
            background-color: #f2f2f7;
            padding: 15px;
            border-left: 4px solid #007AFF;
            margin: 15px 0;
        }
        .diagram {
            width: 100%;
            max-width: 800px;
            margin: 20px auto;
            border: 1px solid #e5e5ea;
            border-radius: 10px;
            padding: 10px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #e5e5ea;
            padding: 10px;
            text-align: left;
        }
        th {
            background-color: #f2f2f7;
        }
        .priority-high {
            background-color: #ffebee;
        }
        .priority-medium {
            background-color: #fff8e1;
        }
        .priority-low {
            background-color: #e8f5e9;
        }
    </style>
</head>
<body>
    <h1>Blueprint de Implementação - Amigo DataHub (Parte 2)</h1>

    <div class="section">
        <h2>3. Estratégia de Implementação com Dados Mínimos</h2>
        <p>Esta seção detalha como o sistema pode funcionar com conjuntos de dados mínimos, permitindo que clínicas com diferentes níveis de maturidade de dados possam se beneficiar da plataforma.</p>

        <h3>Abordagem de Implementação Progressiva:</h3>
        <div class="diagram">
            <pre>
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Nível Básico   │────▶│  Nível          │────▶│  Nível          │
│  (Mínimo)       │     │  Intermediário  │     │  Avançado       │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
            </pre>
        </div>

        <div class="phase">
            <h3>Nível Básico (Mínimo)</h3>
            <p>Implementação com o conjunto mínimo de dados necessários para análises básicas.</p>

            <h4>Dados Mínimos Necessários:</h4>
            <table>
                <tr>
                    <th>Entidade</th>
                    <th>Campos Obrigatórios</th>
                    <th>Fonte Potencial</th>
                </tr>
                <tr class="priority-high">
                    <td>Paciente</td>
                    <td>id, nome</td>
                    <td>Qualquer relatório com dados de pacientes</td>
                </tr>
                <tr class="priority-high">
                    <td>Agendamento</td>
                    <td>id, data, paciente_id, status</td>
                    <td>Relatório de agendamentos</td>
                </tr>
                <tr class="priority-high">
                    <td>TransacaoFinanceira</td>
                    <td>id, valor, data, agendamento_id, tipo</td>
                    <td>Relatório financeiro básico</td>
                </tr>
            </table>

            <h4>Análises Disponíveis no Nível Básico:</h4>
            <ul>
                <li>Total de agendamentos por período</li>
                <li>Status dos agendamentos (realizados, cancelados, etc.)</li>
                <li>Receita total por período</li>
                <li>Número de pacientes atendidos</li>
                <li>Ticket médio por paciente</li>
            </ul>

            <div class="note">
                <strong>Nota:</strong> Mesmo com apenas esses dados mínimos, o sistema já pode fornecer insights valiosos sobre o desempenho básico da clínica.
            </div>
        </div>

        <div class="phase">
            <h3>Nível Intermediário</h3>
            <p>Implementação com um conjunto mais amplo de dados, permitindo análises mais detalhadas.</p>

            <h4>Dados Adicionais Recomendados:</h4>
            <table>
                <tr>
                    <th>Entidade</th>
                    <th>Campos Adicionais</th>
                    <th>Fonte Potencial</th>
                </tr>
                <tr class="priority-medium">
                    <td>Paciente</td>
                    <td>email, telefone, data_nascimento, como_conheceu</td>
                    <td>Cadastro de pacientes, relatório de agendamentos</td>
                </tr>
                <tr class="priority-medium">
                    <td>Agendamento</td>
                    <td>hora, profissional_id, unidade_id, procedimento_id</td>
                    <td>Relatório detalhado de agendamentos</td>
                </tr>
                <tr class="priority-medium">
                    <td>Profissional</td>
                    <td>id, nome, especialidade</td>
                    <td>Cadastro de profissionais, relatório de produção médica</td>
                </tr>
                <tr class="priority-medium">
                    <td>TransacaoFinanceira</td>
                    <td>forma_pagamento, status</td>
                    <td>Relatório financeiro detalhado</td>
                </tr>
            </table>

            <h4>Análises Adicionais Disponíveis:</h4>
            <ul>
                <li>Produção por profissional</li>
                <li>Agendamentos por unidade</li>
                <li>Receita por forma de pagamento</li>
                <li>Análise de procedimentos mais realizados</li>
                <li>Perfil demográfico básico dos pacientes</li>
                <li>Análise de origem dos pacientes (como conheceu)</li>
            </ul>
        </div>

        <div class="phase">
            <h3>Nível Avançado</h3>
            <p>Implementação completa com todos os dados recomendados, permitindo análises avançadas e visão 360° do paciente.</p>

            <h4>Dados Adicionais para Nível Avançado:</h4>
            <table>
                <tr>
                    <th>Entidade</th>
                    <th>Campos Adicionais</th>
                    <th>Fonte Potencial</th>
                </tr>
                <tr class="priority-low">
                    <td>AvaliacaoNPS</td>
                    <td>id, score, data, paciente_id, agendamento_id</td>
                    <td>Sistema de avaliação de satisfação</td>
                </tr>
                <tr class="priority-low">
                    <td>Orcamento</td>
                    <td>id, paciente_id, data, valor_total, status</td>
                    <td>Sistema de orçamentos</td>
                </tr>
                <tr class="priority-low">
                    <td>Lead</td>
                    <td>id, nome, data_criacao, origem, status</td>
                    <td>Sistema de CRM ou gestão de leads</td>
                </tr>
                <tr class="priority-low">
                    <td>Agendamento</td>
                    <td>hora_chegada, hora_inicio, hora_fim</td>
                    <td>Sistema de gestão de atendimento</td>
                </tr>
            </table>

            <h4>Análises Avançadas Disponíveis:</h4>
            <ul>
                <li>Satisfação do cliente (NPS) por profissional, procedimento ou unidade</li>
                <li>Tempo médio de atendimento por profissional ou procedimento</li>
                <li>Análise de conversão de orçamentos</li>
                <li>Análise de leads e campanhas de marketing</li>
                <li>Jornada completa do paciente</li>
                <li>Previsão de demanda e receita</li>
                <li>Análise de retenção de pacientes</li>
                <li>Identificação de oportunidades de cross-selling</li>
            </ul>
        </div>
    </div>

    <div class="section">
        <h2>4. Implementação do Sistema de Upload e Mapeamento</h2>
        <p>Esta seção detalha a implementação do sistema de upload de arquivos e mapeamento para o esquema global.</p>

        <div class="phase">
            <h3>Interface de Upload</h3>
            <p>A interface de upload deve ser intuitiva e fornecer feedback claro sobre o processo.</p>

            <h4>Funcionalidades Principais:</h4>
            <ul>
                <li>Upload de múltiplos arquivos</li>
                <li>Suporte para formatos XLSX, CSV, JSON e XML</li>
                <li>Validação inicial de formato e estrutura</li>
                <li>Visualização prévia dos dados</li>
                <li>Detecção automática de cabeçalhos</li>
                <li>Feedback sobre problemas encontrados</li>
            </ul>

            <div class="note">
                <strong>Implementação Técnica:</strong> Utilizar Flask para o backend e JavaScript/AJAX para o frontend, permitindo upload assíncrono e feedback em tempo real.
            </div>
        </div>

        <div class="phase">
            <h3>Sistema de Mapeamento</h3>
            <p>O sistema de mapeamento permite associar as colunas dos arquivos carregados às entidades e campos do esquema global.</p>

            <h4>Funcionalidades Principais:</h4>
            <ul>
                <li>Interface de arrastar e soltar para mapeamento</li>
                <li>Sugestão automática de mapeamento com base em nomes de colunas</li>
                <li>Templates de mapeamento para formatos comuns</li>
                <li>Validação de tipos de dados</li>
                <li>Transformações básicas (formatação de data, normalização de texto)</li>
                <li>Salvamento de mapeamentos para uso futuro</li>
            </ul>

            <div class="note">
                <strong>Implementação Técnica:</strong> Utilizar um sistema de regras configuráveis para mapeamento automático e transformações, com interface interativa para ajustes manuais.
            </div>
        </div>

        <div class="phase">
            <h3>Processamento e Validação</h3>
            <p>Após o mapeamento, os dados são processados, validados e integrados ao esquema global.</p>

            <h4>Etapas de Processamento:</h4>
            <ol>
                <li>Extração dos dados conforme mapeamento</li>
                <li>Aplicação de transformações</li>
                <li>Validação conforme regras de qualidade</li>
                <li>Deduplicação de registros</li>
                <li>Resolução de conflitos com dados existentes</li>
                <li>Integração ao banco de dados</li>
                <li>Geração de relatório de processamento</li>
            </ol>

            <div class="note">
                <strong>Implementação Técnica:</strong> Utilizar processamento em background com Celery ou similar para lidar com arquivos grandes, com sistema de notificação para informar o usuário sobre o progresso e resultado.
            </div>
        </div>
    </div>
</body>
</html>

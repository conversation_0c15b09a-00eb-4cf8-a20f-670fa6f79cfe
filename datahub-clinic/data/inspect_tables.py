#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para inspecionar as tabelas que serão a base para o Amigo DataHub.
Este script analisa os arquivos Excel nos diretórios de relatórios e extrai
informações sobre sua estrutura, como colunas, tipos de dados e estatísticas básicas.
"""

import os
import json
import pandas as pd
import numpy as np
from pathlib import Path
import re
import logging
from datetime import datetime

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("inspect_tables.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Diretório base dos relatórios
REPORTS_DIR = "/Users/<USER>/Desktop/amigo-datapp/relatorios"

# Diretório para salvar os resultados
OUTPUT_DIR = "/Users/<USER>/Desktop/amigo-datapp/amigo-dataapp/data/table_analysis"

# Criar diretório de saída se não existir
os.makedirs(OUTPUT_DIR, exist_ok=True)

# Mapeamento de módulos
MODULE_MAPPING = {
    "Modulo Agenda": "agenda",
    "Módulo AmigoCare+": "amigocare",
    "Modulo Financeiro": "financeiro",
    "Modulo Paciente": "paciente"
}

# Mapeamento de tipos de dados para o schema global
TYPE_MAPPING = {
    "object": "string",
    "int64": "integer",
    "float64": "number",
    "datetime64[ns]": "date",
    "bool": "boolean"
}

# Função para normalizar nomes de colunas
def normalize_column_name(name):
    """Normaliza o nome da coluna para um formato padronizado."""
    if not isinstance(name, str):
        return str(name)
    
    # Remover caracteres especiais e espaços extras
    normalized = re.sub(r'[^\w\s]', ' ', name)
    normalized = re.sub(r'\s+', '_', normalized.strip().lower())
    
    # Remover acentos
    normalized = normalized.replace('á', 'a').replace('à', 'a').replace('ã', 'a').replace('â', 'a')
    normalized = normalized.replace('é', 'e').replace('ê', 'e').replace('è', 'e')
    normalized = normalized.replace('í', 'i').replace('ì', 'i').replace('î', 'i')
    normalized = normalized.replace('ó', 'o').replace('ò', 'o').replace('õ', 'o').replace('ô', 'o')
    normalized = normalized.replace('ú', 'u').replace('ù', 'u').replace('û', 'u')
    normalized = normalized.replace('ç', 'c')
    
    return normalized

# Função para detectar o tipo de coluna com mais precisão
def detect_column_type(series):
    """Detecta o tipo de dados de uma coluna com mais precisão."""
    # Verificar se é uma data
    if series.dtype == 'object':
        # Tentar converter para data
        try:
            pd.to_datetime(series, errors='raise')
            return "datetime64[ns]"
        except:
            pass
        
        # Verificar se é um número formatado como string
        if all(isinstance(x, str) and re.match(r'^[\d\.,]+$', x.strip()) for x in series.dropna()):
            return "float64"
        
        # Verificar se é um booleano
        if set(series.dropna().unique()) <= {'Sim', 'Não', 'sim', 'não', 'S', 'N', 's', 'n', 'True', 'False', 'true', 'false'}:
            return "bool"
    
    return str(series.dtype)

# Função para analisar um arquivo Excel
def analyze_excel_file(file_path):
    """Analisa um arquivo Excel e retorna informações sobre sua estrutura."""
    logger.info(f"Analisando arquivo: {file_path}")
    
    try:
        # Extrair nome do arquivo e módulo
        file_name = os.path.basename(file_path)
        module_dir = os.path.basename(os.path.dirname(file_path))
        module = MODULE_MAPPING.get(module_dir, module_dir)
        
        # Carregar o arquivo Excel
        df = pd.read_excel(file_path)
        
        # Informações básicas
        row_count = len(df)
        column_count = len(df.columns)
        
        # Análise de colunas
        columns = []
        column_types = {}
        column_stats = {}
        column_samples = {}
        
        for col in df.columns:
            normalized_col = normalize_column_name(col)
            columns.append({
                "original_name": col,
                "normalized_name": normalized_col
            })
            
            # Detectar tipo de dados
            col_type = detect_column_type(df[col])
            column_types[normalized_col] = col_type
            
            # Estatísticas básicas
            stats = {}
            if col_type in ['int64', 'float64']:
                stats = {
                    "min": float(df[col].min()) if not pd.isna(df[col].min()) else None,
                    "max": float(df[col].max()) if not pd.isna(df[col].max()) else None,
                    "mean": float(df[col].mean()) if not pd.isna(df[col].mean()) else None,
                    "null_count": int(df[col].isna().sum()),
                    "null_percentage": float(df[col].isna().mean() * 100)
                }
            else:
                # Para strings e outros tipos
                value_counts = df[col].value_counts(dropna=False)
                stats = {
                    "unique_values": int(df[col].nunique()),
                    "most_common": value_counts.index[0] if len(value_counts) > 0 else None,
                    "most_common_count": int(value_counts.iloc[0]) if len(value_counts) > 0 else 0,
                    "null_count": int(df[col].isna().sum()),
                    "null_percentage": float(df[col].isna().mean() * 100)
                }
            
            column_stats[normalized_col] = stats
            
            # Amostras de valores
            non_null_values = df[col].dropna()
            samples = non_null_values.sample(min(5, len(non_null_values))).tolist() if len(non_null_values) > 0 else []
            column_samples[normalized_col] = samples
        
        # Possíveis chaves primárias
        potential_keys = []
        for col in df.columns:
            normalized_col = normalize_column_name(col)
            # Verificar se a coluna tem valores únicos e não nulos
            if df[col].nunique() == row_count and df[col].isna().sum() == 0:
                potential_keys.append(normalized_col)
            # Verificar se o nome da coluna sugere que é um ID
            elif any(id_term in normalized_col.lower() for id_term in ['id', 'codigo', 'cod', 'chave']):
                potential_keys.append(normalized_col)
        
        # Possíveis relacionamentos
        potential_relationships = []
        for col in df.columns:
            normalized_col = normalize_column_name(col)
            # Verificar se o nome da coluna sugere um relacionamento
            if any(rel_term in normalized_col.lower() for rel_term in ['_id', 'id_', 'codigo_', '_codigo']):
                related_entity = re.sub(r'(_id|id_|_codigo|codigo_)', '', normalized_col)
                if related_entity:
                    potential_relationships.append({
                        "column": normalized_col,
                        "possible_related_entity": related_entity
                    })
        
        # Montar resultado
        result = {
            "file_name": file_name,
            "module": module,
            "row_count": row_count,
            "column_count": column_count,
            "columns": columns,
            "column_types": column_types,
            "column_stats": column_stats,
            "column_samples": column_samples,
            "potential_keys": potential_keys,
            "potential_relationships": potential_relationships,
            "analyzed_at": datetime.now().isoformat()
        }
        
        return result
    
    except Exception as e:
        logger.error(f"Erro ao analisar o arquivo {file_path}: {str(e)}")
        return {
            "file_name": os.path.basename(file_path),
            "error": str(e)
        }

# Função para mapear colunas para o schema global
def map_to_global_schema(analysis_results):
    """Mapeia as colunas analisadas para o schema global."""
    # Carregar o schema global
    schema_path = "/Users/<USER>/Desktop/amigo-datapp/amigo-dataapp/data/global_schema.json"
    try:
        with open(schema_path, 'r', encoding='utf-8') as f:
            global_schema = json.load(f)
    except Exception as e:
        logger.error(f"Erro ao carregar o schema global: {str(e)}")
        return None
    
    # Criar mapeamento
    mapping = {}
    
    for file_analysis in analysis_results:
        file_name = file_analysis["file_name"]
        module = file_analysis["module"]
        
        file_mapping = {
            "file_name": file_name,
            "module": module,
            "entity_mappings": []
        }
        
        # Tentar identificar a entidade principal do arquivo
        main_entity = None
        for entity in global_schema["core_entities"]:
            entity_name = entity["entity_name"].lower()
            if entity_name in file_name.lower():
                main_entity = entity["entity_name"]
                break
        
        if not main_entity:
            # Tentar inferir pela análise das colunas
            column_names = [col["normalized_name"] for col in file_analysis["columns"]]
            for entity in global_schema["core_entities"]:
                entity_fields = entity["required_fields"] + entity["recommended_fields"] + entity["optional_fields"]
                matches = sum(1 for field in entity_fields if field.lower() in column_names)
                if matches > len(entity_fields) * 0.3:  # Se mais de 30% dos campos correspondem
                    main_entity = entity["entity_name"]
                    break
        
        # Mapear colunas para entidades
        if main_entity:
            entity_mapping = {
                "entity": main_entity,
                "confidence": "high",
                "field_mappings": []
            }
            
            # Encontrar a entidade no schema global
            entity_schema = next((e for e in global_schema["core_entities"] if e["entity_name"] == main_entity), None)
            
            if entity_schema:
                all_fields = entity_schema["required_fields"] + entity_schema["recommended_fields"] + entity_schema["optional_fields"]
                
                for col in file_analysis["columns"]:
                    normalized_name = col["normalized_name"]
                    original_name = col["original_name"]
                    
                    # Verificar correspondência direta
                    direct_match = next((field for field in all_fields if field.lower() == normalized_name), None)
                    
                    if direct_match:
                        entity_mapping["field_mappings"].append({
                            "source_column": original_name,
                            "target_field": direct_match,
                            "confidence": "high"
                        })
                    else:
                        # Verificar correspondência parcial
                        partial_matches = [(field, normalized_name.lower().count(field.lower()) / len(field)) 
                                          for field in all_fields 
                                          if field.lower() in normalized_name.lower() or normalized_name.lower() in field.lower()]
                        
                        if partial_matches:
                            best_match = max(partial_matches, key=lambda x: x[1])
                            if best_match[1] > 0.5:  # Se a confiança for maior que 50%
                                entity_mapping["field_mappings"].append({
                                    "source_column": original_name,
                                    "target_field": best_match[0],
                                    "confidence": "medium"
                                })
            
            file_mapping["entity_mappings"].append(entity_mapping)
        
        mapping[file_name] = file_mapping
    
    return mapping

# Função principal
def main():
    """Função principal que coordena a análise dos arquivos."""
    logger.info("Iniciando análise das tabelas para o Amigo DataHub")
    
    # Listar todos os arquivos Excel nos diretórios de relatórios
    excel_files = []
    for root, dirs, files in os.walk(REPORTS_DIR):
        for file in files:
            if file.endswith('.xlsx'):
                excel_files.append(os.path.join(root, file))
    
    logger.info(f"Encontrados {len(excel_files)} arquivos Excel para análise")
    
    # Analisar cada arquivo
    analysis_results = []
    for file_path in excel_files:
        result = analyze_excel_file(file_path)
        if result:
            analysis_results.append(result)
    
    # Salvar resultados individuais
    for result in analysis_results:
        file_name = result["file_name"].replace('.xlsx', '')
        output_file = os.path.join(OUTPUT_DIR, f"{file_name}_analysis.json")
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
    
    # Salvar resultado consolidado
    consolidated_output = os.path.join(OUTPUT_DIR, "consolidated_analysis.json")
    with open(consolidated_output, 'w', encoding='utf-8') as f:
        json.dump(analysis_results, f, ensure_ascii=False, indent=2)
    
    # Mapear para o schema global
    schema_mapping = map_to_global_schema(analysis_results)
    if schema_mapping:
        mapping_output = os.path.join(OUTPUT_DIR, "schema_mapping.json")
        with open(mapping_output, 'w', encoding='utf-8') as f:
            json.dump(schema_mapping, f, ensure_ascii=False, indent=2)
    
    # Gerar relatório de análise
    generate_analysis_report(analysis_results, schema_mapping)
    
    logger.info(f"Análise concluída. Resultados salvos em {OUTPUT_DIR}")

# Função para gerar relatório de análise em HTML
def generate_analysis_report(analysis_results, schema_mapping):
    """Gera um relatório HTML com os resultados da análise."""
    report_path = os.path.join(OUTPUT_DIR, "analysis_report.html")
    
    html_content = """<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Relatório de Análise de Tabelas - Amigo DataHub</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3, h4 {
            color: #007AFF;
        }
        .section {
            margin-bottom: 30px;
            border: 1px solid #e5e5ea;
            border-radius: 10px;
            padding: 20px;
            background-color: #fff;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #e5e5ea;
            padding: 10px;
            text-align: left;
        }
        th {
            background-color: #f2f2f7;
        }
        .file-section {
            margin-bottom: 40px;
        }
        .confidence-high {
            color: #34C759;
        }
        .confidence-medium {
            color: #FF9500;
        }
        .confidence-low {
            color: #FF3B30;
        }
        .stats {
            font-size: 0.9em;
            color: #666;
        }
        .module-tag {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            margin-left: 10px;
        }
        .module-agenda {
            background-color: #E3F2FD;
            color: #1976D2;
        }
        .module-financeiro {
            background-color: #E8F5E9;
            color: #388E3C;
        }
        .module-paciente {
            background-color: #FFF3E0;
            color: #F57C00;
        }
        .module-amigocare {
            background-color: #F3E5F5;
            color: #7B1FA2;
        }
    </style>
</head>
<body>
    <h1>Relatório de Análise de Tabelas - Amigo DataHub</h1>
    <p>Este relatório apresenta a análise das tabelas que serão a base para o Amigo DataHub.</p>
    
    <div class="section">
        <h2>Resumo da Análise</h2>
        <p>Total de arquivos analisados: """ + str(len(analysis_results)) + """</p>
        
        <table>
            <tr>
                <th>Módulo</th>
                <th>Quantidade de Arquivos</th>
                <th>Total de Linhas</th>
                <th>Média de Colunas</th>
            </tr>"""
    
    # Agrupar por módulo
    modules = {}
    for result in analysis_results:
        module = result["module"]
        if module not in modules:
            modules[module] = {
                "count": 0,
                "total_rows": 0,
                "total_columns": 0
            }
        
        modules[module]["count"] += 1
        modules[module]["total_rows"] += result["row_count"]
        modules[module]["total_columns"] += result["column_count"]
    
    for module, stats in modules.items():
        avg_columns = stats["total_columns"] / stats["count"] if stats["count"] > 0 else 0
        html_content += f"""
            <tr>
                <td>{module}</td>
                <td>{stats["count"]}</td>
                <td>{stats["total_rows"]}</td>
                <td>{avg_columns:.1f}</td>
            </tr>"""
    
    html_content += """
        </table>
    </div>
    
    <h2>Análise Detalhada por Arquivo</h2>
    """
    
    # Detalhes de cada arquivo
    for result in analysis_results:
        file_name = result["file_name"]
        module = result["module"]
        module_class = f"module-{module}"
        
        html_content += f"""
    <div class="section file-section">
        <h3>{file_name} <span class="module-tag {module_class}">{module}</span></h3>
        <p>Linhas: {result["row_count"]} | Colunas: {result["column_count"]}</p>
        
        <h4>Possíveis Chaves Primárias</h4>
        <ul>"""
        
        for key in result.get("potential_keys", []):
            html_content += f"""
            <li>{key}</li>"""
        
        html_content += """
        </ul>
        
        <h4>Possíveis Relacionamentos</h4>
        <ul>"""
        
        for rel in result.get("potential_relationships", []):
            html_content += f"""
            <li>{rel["column"]} → {rel["possible_related_entity"]}</li>"""
        
        html_content += """
        </ul>
        
        <h4>Colunas</h4>
        <table>
            <tr>
                <th>Nome Original</th>
                <th>Nome Normalizado</th>
                <th>Tipo</th>
                <th>Estatísticas</th>
                <th>Amostras</th>
            </tr>"""
        
        for col in result["columns"]:
            original_name = col["original_name"]
            normalized_name = col["normalized_name"]
            col_type = result["column_types"].get(normalized_name, "")
            
            # Estatísticas
            stats = result["column_stats"].get(normalized_name, {})
            stats_html = "<div class='stats'>"
            if "min" in stats:
                stats_html += f"Min: {stats['min']}, Max: {stats['max']}, Média: {stats['mean']:.2f}<br>"
            if "unique_values" in stats:
                stats_html += f"Valores únicos: {stats['unique_values']}<br>"
                if stats["most_common"] is not None:
                    stats_html += f"Mais comum: {stats['most_common']} ({stats['most_common_count']} vezes)<br>"
            
            stats_html += f"Nulos: {stats.get('null_count', 0)} ({stats.get('null_percentage', 0):.1f}%)"
            stats_html += "</div>"
            
            # Amostras
            samples = result["column_samples"].get(normalized_name, [])
            samples_html = ", ".join(str(s) for s in samples[:3])
            
            html_content += f"""
            <tr>
                <td>{original_name}</td>
                <td>{normalized_name}</td>
                <td>{col_type}</td>
                <td>{stats_html}</td>
                <td>{samples_html}</td>
            </tr>"""
        
        html_content += """
        </table>"""
        
        # Adicionar mapeamento para o schema global, se disponível
        if schema_mapping and file_name in schema_mapping:
            mapping = schema_mapping[file_name]
            
            html_content += """
        <h4>Mapeamento para o Schema Global</h4>"""
            
            for entity_mapping in mapping["entity_mappings"]:
                entity = entity_mapping["entity"]
                confidence = entity_mapping["confidence"]
                confidence_class = f"confidence-{confidence}"
                
                html_content += f"""
        <p>Entidade: <strong>{entity}</strong> <span class="{confidence_class}">(Confiança: {confidence})</span></p>
        
        <table>
            <tr>
                <th>Coluna Original</th>
                <th>Campo no Schema Global</th>
                <th>Confiança</th>
            </tr>"""
                
                for field_mapping in entity_mapping["field_mappings"]:
                    source = field_mapping["source_column"]
                    target = field_mapping["target_field"]
                    field_confidence = field_mapping["confidence"]
                    field_confidence_class = f"confidence-{field_confidence}"
                    
                    html_content += f"""
            <tr>
                <td>{source}</td>
                <td>{target}</td>
                <td class="{field_confidence_class}">{field_confidence}</td>
            </tr>"""
                
                html_content += """
        </table>"""
        
        html_content += """
    </div>"""
    
    html_content += """
</body>
</html>"""
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    logger.info(f"Relatório de análise gerado em {report_path}")

if __name__ == "__main__":
    main()

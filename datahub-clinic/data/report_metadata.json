{"Modulo Agenda": [{"file_name": "relatorio_producao_medica.xlsx", "row_count": 2, "column_count": 57, "columns": ["Data Atendimento", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Indice", "Paciente", "Profissão", "<PERSON><PERSON>", "Como conheceu", "ID amigo ", "Cod<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "CNS", "Profissional", "Especialidade", "CBO", "Solicitante", "Executante", "Participação", "Hospital / Local", "Forma de Pagamento", "Lançamento", "Plano", "Número da <PERSON>a", "Qtd parcelas", "NSU(DOC/CV/ID)", "Observação financeira", "T<PERSON>o <PERSON>endimento", "Unidade", "Tipo", "Grupo", "Subgrupo", "<PERSON><PERSON>", "Quantidade", "Via de acesso", "% Red. Acrésc", "Valor do item R$", "Desconto R$", "Valor total do item R$", "Nota Emitida", "Númer<PERSON> da g<PERSON>", "Número lote", "<PERSON><PERSON> en<PERSON>u", "Data envio", "<PERSON><PERSON> bai<PERSON>", "Data pagamento", "Valor pago R$", "Valor glosado R$", "<PERSON>o ate<PERSON>", "<PERSON><PERSON>s atendi<PERSON>", "<PERSON><PERSON> en<PERSON>", "<PERSON><PERSON><PERSON> <PERSON>", "<PERSON><PERSON>aga<PERSON>", "<PERSON><PERSON><PERSON>", "Recurso", "Editor <PERSON><PERSON> (Orçamento)", "Data pagamento (Orçamento)"], "data_types": {"Data Atendimento": "object", "Hora Atendimento": "object", "Código Atendimento": "int64", "Horário de Chegada": "object", "Indice": "float64", "Paciente": "object", "Profissão": "float64", "Idade": "int64", "Como conheceu": "float64", "ID amigo ": "int64", "Cod. Legado": "float64", "Matrícula": "float64", "CNS": "float64", "Profissional": "object", "Especialidade": "object", "CBO": "object", "Solicitante": "float64", "Executante": "object", "Participação": "object", "Hospital / Local": "float64", "Forma de Pagamento": "object", "Lançamento": "int64", "Plano": "float64", "Número da parcela": "float64", "Qtd parcelas": "float64", "NSU(DOC/CV/ID)": "float64", "Observação financeira": "float64", "Tipo Atendimento": "object", "Unidade": "object", "Tipo": "object", "Grupo": "object", "Subgrupo": "object", "Item": "object", "Quantidade": "int64", "Via de acesso": "object", "% Red. Acrésc": "float64", "Valor do item R$": "int64", "Desconto R$": "int64", "Valor total do item R$": "int64", "Nota Emitida": "object", "Número da guia": "float64", "Número lote": "float64", "Quem enviou": "float64", "Data envio": "float64", "Quem baixou": "float64", "Data pagamento": "float64", "Valor pago R$": "int64", "Valor glosado R$": "float64", "Ano atendimento": "int64", "Mês atendimento": "int64", "Ano envio": "float64", "Mês envio": "float64", "Ano pagamento": "float64", "Mês pagamento": "float64", "Recurso": "object", "Editor financeiro (Orçamento)": "object", "Data pagamento (Orçamento)": "object"}, "sample_data": [{"Data Atendimento": "23/04/2025", "Hora Atendimento": "17:00", "Código Atendimento": 155601121, "Horário de Chegada": NaN, "Indice": NaN, "Paciente": "<PERSON><PERSON>", "Profissão": NaN, "Idade": 37, "Como conheceu": NaN, "ID amigo ": 48947847, "Cod. Legado": NaN, "Matrícula": 12234556.0, "CNS": NaN, "Profissional": "Rayara <PERSON> Souza", "Especialidade": "Pneumologist<PERSON>", "CBO": "225127-<PERSON><PERSON><PERSON><PERSON> pneumologista", "Solicitante": NaN, "Executante": "Rayara <PERSON> Souza", "Participação": "Clínico", "Hospital / Local": NaN, "Forma de Pagamento": "AMIL", "Lançamento": 1, "Plano": NaN, "Número da parcela": NaN, "Qtd parcelas": NaN, "NSU(DOC/CV/ID)": NaN, "Observação financeira": NaN, "Tipo Atendimento": "Sessao de Fisioterapia", "Unidade": "CLÍNICA (RL)", "Tipo": "Procedimento", "Grupo": "BOTOX", "Subgrupo": "BOTOX", "Item": "<PERSON><PERSON><PERSON> - teste", "Quantidade": 1, "Via de acesso": "Única", "% Red. Acrésc": 1.0, "Valor do item R$": 20, "Desconto R$": 0, "Valor total do item R$": 20, "Nota Emitida": "Não", "Número da guia": 421.0, "Número lote": NaN, "Quem enviou": NaN, "Data envio": NaN, "Quem baixou": NaN, "Data pagamento": NaN, "Valor pago R$": 0, "Valor glosado R$": 0.0, "Ano atendimento": 2025, "Mês atendimento": 4, "Ano envio": NaN, "Mês envio": NaN, "Ano pagamento": NaN, "Mês pagamento": NaN, "Recurso": "Não", "Editor financeiro (Orçamento)": NaN, "Data pagamento (Orçamento)": NaN}, {"Data Atendimento": "23/04/2025", "Hora Atendimento": "10:20", "Código Atendimento": 155686103, "Horário de Chegada": "13:09", "Indice": NaN, "Paciente": "<PERSON>", "Profissão": NaN, "Idade": 29, "Como conheceu": NaN, "ID amigo ": 90796209, "Cod. Legado": NaN, "Matrícula": NaN, "CNS": NaN, "Profissional": "Equipe Amigo - BRUNO LIMA", "Especialidade": "Médico otorrino<PERSON>a", "CBO": "225275-<PERSON><PERSON><PERSON><PERSON>", "Solicitante": NaN, "Executante": "Equipe Amigo - BRUNO LIMA", "Participação": NaN, "Hospital / Local": NaN, "Forma de Pagamento": "Crédito de Procedimento", "Lançamento": 1, "Plano": NaN, "Número da parcela": NaN, "Qtd parcelas": NaN, "NSU(DOC/CV/ID)": NaN, "Observação financeira": NaN, "Tipo Atendimento": "PROCEDIMENTO SIMPLES - 60 MIN", "Unidade": "Morumbi", "Tipo": "Procedimento", "Grupo": "BOTOX", "Subgrupo": "botox", "Item": "BOTOX MALAR", "Quantidade": 1, "Via de acesso": NaN, "% Red. Acrésc": NaN, "Valor do item R$": 1000, "Desconto R$": 0, "Valor total do item R$": 1000, "Nota Emitida": "Não", "Número da guia": NaN, "Número lote": NaN, "Quem enviou": NaN, "Data envio": NaN, "Quem baixou": NaN, "Data pagamento": NaN, "Valor pago R$": 0, "Valor glosado R$": NaN, "Ano atendimento": 2025, "Mês atendimento": 4, "Ano envio": NaN, "Mês envio": NaN, "Ano pagamento": NaN, "Mês pagamento": NaN, "Recurso": "Não", "Editor financeiro (Orçamento)": "Equipe Amigo - BRUNO LIMA", "Data pagamento (Orçamento)": "17/04/2025"}], "unique_values": {"Data Atendimento": ["23/04/2025"], "Hora Atendimento": ["17:00", "10:20"], "Horário de Chegada": ["13:09"], "Paciente": ["<PERSON><PERSON>", "<PERSON>"], "Profissional": ["Rayara <PERSON> Souza", "Equipe Amigo - BRUNO LIMA"], "Especialidade": ["Pneumologist<PERSON>", "Médico otorrino<PERSON>a"], "CBO": ["225127-<PERSON><PERSON><PERSON><PERSON> pneumologista", "225275-<PERSON><PERSON><PERSON><PERSON>"], "Executante": ["Rayara <PERSON> Souza", "Equipe Amigo - BRUNO LIMA"], "Participação": ["Clínico"], "Forma de Pagamento": ["AMIL", "Crédito de Procedimento"], "Tipo Atendimento": ["Sessao de Fisioterapia", "PROCEDIMENTO SIMPLES - 60 MIN"], "Unidade": ["CLÍNICA (RL)", "Morumbi"], "Tipo": ["Procedimento"], "Grupo": ["BOTOX"], "Subgrupo": ["BOTOX", "botox"], "Item": ["<PERSON><PERSON><PERSON> - teste", "BOTOX MALAR"], "Via de acesso": ["Única"], "Nota Emitida": ["Não"], "Recurso": ["Não"], "Editor financeiro (Orçamento)": ["Equipe Amigo - BRUNO LIMA"], "Data pagamento (Orçamento)": ["17/04/2025"]}, "numeric_stats": {"Código Atendimento": {"min": 155601121, "max": 155686103, "mean": 155643612.0, "median": 155643612.0}, "Indice": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "Profissão": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "Idade": {"min": 29, "max": 37, "mean": 33.0, "median": 33.0}, "Como conheceu": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "ID amigo ": {"min": 48947847, "max": 90796209, "mean": 69872028.0, "median": 69872028.0}, "Cod. Legado": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "Matrícula": {"min": 12234556.0, "max": 12234556.0, "mean": 12234556.0, "median": 12234556.0}, "CNS": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "Solicitante": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "Hospital / Local": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "Lançamento": {"min": 1, "max": 1, "mean": 1.0, "median": 1.0}, "Plano": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "Número da parcela": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "Qtd parcelas": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "NSU(DOC/CV/ID)": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "Observação financeira": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "Quantidade": {"min": 1, "max": 1, "mean": 1.0, "median": 1.0}, "% Red. Acrésc": {"min": 1.0, "max": 1.0, "mean": 1.0, "median": 1.0}, "Valor do item R$": {"min": 20, "max": 1000, "mean": 510.0, "median": 510.0}, "Desconto R$": {"min": 0, "max": 0, "mean": 0.0, "median": 0.0}, "Valor total do item R$": {"min": 20, "max": 1000, "mean": 510.0, "median": 510.0}, "Número da guia": {"min": 421.0, "max": 421.0, "mean": 421.0, "median": 421.0}, "Número lote": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "Quem enviou": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "Data envio": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "Quem baixou": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "Data pagamento": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "Valor pago R$": {"min": 0, "max": 0, "mean": 0.0, "median": 0.0}, "Valor glosado R$": {"min": 0.0, "max": 0.0, "mean": 0.0, "median": 0.0}, "Ano atendimento": {"min": 2025, "max": 2025, "mean": 2025.0, "median": 2025.0}, "Mês atendimento": {"min": 4, "max": 4, "mean": 4.0, "median": 4.0}, "Ano envio": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "Mês envio": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "Ano pagamento": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "Mês pagamento": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}}, "date_ranges": {}}, {"file_name": "relatorio_producao_medica2.xlsx", "row_count": 72, "column_count": 9, "columns": ["Data Atendimento", "<PERSON><PERSON><PERSON>", "Unidade", "Paciente", "Profissional", "Tipo de Atendimento", "Qtd/Procedimento", "Forma de Pagamento", "Valor R$"], "data_types": {"Data Atendimento": "object", "Código Atendimento": "int64", "Unidade": "object", "Paciente": "object", "Profissional": "object", "Tipo de Atendimento": "object", "Qtd/Procedimento": "object", "Forma de Pagamento": "object", "Valor R$": "float64"}, "sample_data": [{"Data Atendimento": "04/04/2025 12:20", "Código Atendimento": 153242624, "Unidade": "BRASILIA", "Paciente": "FABIO FELTRIM", "Profissional": "<PERSON>", "Tipo de Atendimento": "30501369 - Septoplastia (qualquer técnica sem vídeo)", "Qtd/Procedimento": "2 - Ecodoppler Vertebral ou Vascular Periférico, 1 - Teste Via Unica 3, 1 - LUVA CIRURGICA", "Forma de Pagamento": "BRADESCO", "Valor R$": 200.0}, {"Data Atendimento": "09/04/2025 09:40", "Código Atendimento": 154993622, "Unidade": "Morumbi", "Paciente": "ADRANA TESTE SALVADOR", "Profissional": "Equipe Amigo - Al<PERSON>", "Tipo de Atendimento": "40304892 - <PERSON><PERSON><PERSON><PERSON>", "Qtd/Procedimento": "1 - PROCEDIMENTO RAIZ FISIO", "Forma de Pagamento": "Cartão", "Valor R$": 90.0}, {"Data Atendimento": "09/04/2025 08:20", "Código Atendimento": 154987328, "Unidade": "Morumbi", "Paciente": "ADOLFO STURARO TESTE", "Profissional": "Equipe Amigo - Al<PERSON>", "Tipo de Atendimento": "30501369 - Septoplastia (qualquer técnica sem vídeo)", "Qtd/Procedimento": "1 - PROCEDIMENTO RAIZ FISIO", "Forma de Pagamento": "Cartão", "Valor R$": 100.0}], "unique_values": {"Unidade": ["BRASILIA", "Morumbi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CLÍNICA (RL)", "Recife"], "Forma de Pagamento": ["BRADESCO", "Cartão", "AMIL", "Crédito de Procedimento", "Crédito Pré-Pago", "Pix", "<PERSON><PERSON><PERSON>", "Pendente", "UNIMED"]}, "numeric_stats": {"Código Atendimento": {"min": 148577077, "max": 155741330, "mean": 153790468.91666666, "median": 153651240.5}, "Valor R$": {"min": 2.5, "max": 20000.0, "mean": 779.1138888888888, "median": 300.0}}, "date_ranges": {}}, {"file_name": "relatorio_produtividade.xlsx", "row_count": 9, "column_count": 32, "columns": ["Cód. <PERSON>end<PERSON>", "<PERSON><PERSON><PERSON>", "Agendado em", "Agendado por", "Con<PERSON>rmado em", "Confirmado por", "Data do Agendamento", "Hora do Agendamento", "Unidade", "Sala", "Profissional", "Sol. Interno", "Sol. Externo", "Paciente", "<PERSON><PERSON>", "Como conheceu", "ID Amigo", "Cod<PERSON>", "CPF", "Telefone", "E-mail", "<PERSON><PERSON><PERSON><PERSON>", "Tipo de Atendimento", "Tipo do Item", "Qtd. Item", "<PERSON><PERSON>", "Status do Agendamento", "Forma de Pagamento", "<PERSON><PERSON><PERSON>", "Acomodação", "Editor <PERSON><PERSON>", "Valor"], "data_types": {"Cód. Atendimento": "int64", "Índice": "int64", "Agendado em": "object", "Agendado por": "object", "Confirmado em": "float64", "Confirmado por": "float64", "Data do Agendamento": "object", "Hora do Agendamento": "object", "Unidade": "object", "Sala": "object", "Profissional": "object", "Sol. Interno": "object", "Sol. Externo": "object", "Paciente": "object", "Idade": "object", "Como conheceu": "float64", "ID Amigo": "int64", "Cod. Legado": "object", "CPF": "object", "Telefone": "object", "E-mail": "object", "Matrícula": "object", "Tipo de Atendimento": "object", "Tipo do Item": "object", "Qtd. Item": "int64", "Item": "object", "Status do Agendamento": "object", "Forma de Pagamento": "object", "Tipo de Guia": "object", "Acomodação": "object", "Editor Financeiro": "object", "Valor": "float64"}, "sample_data": [{"Cód. Atendimento": 155723069, "Índice": 1, "Agendado em": "17/04/2025", "Agendado por": "Equipe Amigo - Caio <PERSON>", "Confirmado em": NaN, "Confirmado por": NaN, "Data do Agendamento": "22/04/2025", "Hora do Agendamento": "08:00", "Unidade": "Morumbi", "Sala": "-", "Profissional": "Equipe Amigo - Caio <PERSON>", "Sol. Interno": "-", "Sol. Externo": "-", "Paciente": "AMANDA 1704", "Idade": NaN, "Como conheceu": NaN, "ID Amigo": 90802077, "Cod. Legado": "-", "CPF": "-", "Telefone": "-", "E-mail": "-", "Matrícula": "-", "Tipo de Atendimento": "BOTOX", "Tipo do Item": "Procedimento", "Qtd. Item": 1, "Item": "Anestesia", "Status do Agendamento": "Agendado", "Forma de Pagamento": "Crédito de Procedimento", "Tipo de Guia": "-", "Acomodação": "-", "Editor Financeiro": "Equipe Amigo - Caio <PERSON>", "Valor": 2502.36}, {"Cód. Atendimento": 155723069, "Índice": 1, "Agendado em": "17/04/2025", "Agendado por": "Equipe Amigo - Caio <PERSON>", "Confirmado em": NaN, "Confirmado por": NaN, "Data do Agendamento": "22/04/2025", "Hora do Agendamento": "08:00", "Unidade": "Morumbi", "Sala": "-", "Profissional": "Equipe Amigo - Caio <PERSON>", "Sol. Interno": "-", "Sol. Externo": "-", "Paciente": "AMANDA 1704", "Idade": NaN, "Como conheceu": NaN, "ID Amigo": 90802077, "Cod. Legado": "-", "CPF": "-", "Telefone": "-", "E-mail": "-", "Matrícula": "-", "Tipo de Atendimento": "BOTOX", "Tipo do Item": "Procedimento", "Qtd. Item": 1, "Item": "Blefaroplastia", "Status do Agendamento": "Agendado", "Forma de Pagamento": "Crédito de Procedimento", "Tipo de Guia": "-", "Acomodação": "-", "Editor Financeiro": "Equipe Amigo - Caio <PERSON>", "Valor": 6506.13}, {"Cód. Atendimento": 151314579, "Índice": 2, "Agendado em": "24/03/2025", "Agendado por": "<PERSON><PERSON>", "Confirmado em": NaN, "Confirmado por": NaN, "Data do Agendamento": "22/04/2025", "Hora do Agendamento": "10:00", "Unidade": "BRASILIA", "Sala": "-", "Profissional": "Equipe Amigo - Giulia Pedrosa 2", "Sol. Interno": "-", "Sol. Externo": "-", "Paciente": "<PERSON><PERSON>", "Idade": "30 anos", "Como conheceu": NaN, "ID Amigo": 77719863, "Cod. Legado": "-", "CPF": "11577195701", "Telefone": "(21) 99435-2590", "E-mail": "<EMAIL>", "Matrícula": "782424624646", "Tipo de Atendimento": "Sessao de Fisioterapia", "Tipo do Item": "Procedimento", "Qtd. Item": 1, "Item": "<PERSON><PERSON><PERSON> - teste", "Status do Agendamento": "Agendado", "Forma de Pagamento": "Pendente", "Tipo de Guia": "-", "Acomodação": "-", "Editor Financeiro": "<PERSON><PERSON>", "Valor": 100.0}], "unique_values": {"Agendado em": ["17/04/2025", "24/03/2025", "07/04/2025", "08/04/2025", "16/04/2025"], "Agendado por": ["Equipe Amigo - Caio <PERSON>", "<PERSON><PERSON>", "Equipe Amigo - Giulia Pedrosa 2", "Equipe Amigo - Ana Victoria <PERSON> Almeida", "Plano de Tratamento"], "Data do Agendamento": ["22/04/2025"], "Hora do Agendamento": ["08:00", "10:00", "12:00", "15:00", "16:00"], "Unidade": ["Morumbi", "BRASILIA", "Recife"], "Sala": ["-", "SL TO", "SL ABA"], "Profissional": ["Equipe Amigo - Caio <PERSON>", "Equipe Amigo - Giulia Pedrosa 2", "Equipe Amigo - Ana Victoria <PERSON> Almeida", "Plano de Tratamento"], "Sol. Interno": ["-"], "Sol. Externo": ["-"], "Paciente": ["AMANDA 1704", "<PERSON><PERSON>", "MARINA HAZIN", "testse", "MARINA HAZIN CONVÊNIO", "Victória Almeida", "ACRISIO JP TESTE"], "Idade": ["30 anos", "43 anos"], "Cod. Legado": ["-"], "CPF": ["-", "11577195701", "10880304405", "00764598457"], "Telefone": ["-", "(21) 99435-2590", "(81) 99696-4897", "(11) 94020-9917", "(81) 99797-0334", "(11) 94441-1411"], "E-mail": ["-", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "Matrícula": ["-", "782424624646", "132465", "3213213213"], "Tipo de Atendimento": ["BOTOX", "Sessao de Fisioterapia", "TERAPIA OCUPACIONAL", "PSICOLOGIA ABA"], "Tipo do Item": ["Procedimento", "-"], "Item": ["Anestesia", "Blefaroplastia", "<PERSON><PERSON><PERSON> - teste", "-"], "Status do Agendamento": ["Agendado"], "Forma de Pagamento": ["Crédito de Procedimento", "Pendente", "Pix", "BRADESCO", "Cartão"], "Tipo de Guia": ["-", "SADT"], "Acomodação": ["-"], "Editor Financeiro": ["Equipe Amigo - Caio <PERSON>", "<PERSON><PERSON>", "Equipe Amigo - Giulia Pedrosa 2", "Equipe Amigo - Ana Victoria <PERSON> Almeida", "-"]}, "numeric_stats": {"Cód. Atendimento": {"min": 151314579, "max": 155723069, "mean": 154295024.2222222, "median": 153827808.0}, "Índice": {"min": 1, "max": 8, "mean": 4.111111111111111, "median": 4.0}, "Confirmado em": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "Confirmado por": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "Como conheceu": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "ID Amigo": {"min": 9947091, "max": 90802077, "mean": 64400665.666666664, "median": 85794097.0}, "Qtd. Item": {"min": 1, "max": 1, "mean": 1.0, "median": 1.0}, "Valor": {"min": 0.0, "max": 6506.13, "mean": 1055.3877777777777, "median": 100.0}}, "date_ranges": {}}, {"file_name": "relatorio_cancelamentos_falttantes_reagendamento.xlsx", "row_count": 12, "column_count": 7, "columns": ["CONSULTORIO MEDICO", "Unnamed: 1", "Unnamed: 2", "Unnamed: 3", "Unnamed: 4", "Unnamed: 5", "Unnamed: 6"], "data_types": {"CONSULTORIO MEDICO": "object", "Unnamed: 1": "object", "Unnamed: 2": "object", "Unnamed: 3": "object", "Unnamed: 4": "object", "Unnamed: 5": "object", "Unnamed: 6": "object"}, "sample_data": [{"CONSULTORIO MEDICO": "Relatório de Cancelamentos, faltantes e reagendamentos", "Unnamed: 1": NaN, "Unnamed: 2": NaN, "Unnamed: 3": NaN, "Unnamed: 4": NaN, "Unnamed: 5": NaN, "Unnamed: 6": NaN}, {"CONSULTORIO MEDICO": "Data entre: 20/04/2025 e 26/04/2025 (2)", "Unnamed: 1": NaN, "Unnamed: 2": NaN, "Unnamed: 3": NaN, "Unnamed: 4": NaN, "Unnamed: 5": NaN, "Unnamed: 6": NaN}, {"CONSULTORIO MEDICO": NaN, "Unnamed: 1": NaN, "Unnamed: 2": NaN, "Unnamed: 3": NaN, "Unnamed: 4": NaN, "Unnamed: 5": NaN, "Unnamed: 6": NaN}], "unique_values": {"CONSULTORIO MEDICO": ["Relatório de Cancelamentos, faltantes e reagendamentos", "Data entre: 20/04/2025 e 26/04/2025 (2)", "Equipe Amigo - <PERSON> (1)", "Data", "25/04/2025", "Equipe Amigo - BRUNO LIMA (1)", "21/04/2025"], "Unnamed: 1": ["Paciente", "<PERSON>", "Junior Souza"], "Unnamed: 2": ["Telefone", "(31) 99572-0954", "(11) 97505-4988"], "Unnamed: 3": ["Tipo de Atendimento", "Fisioterapia", "Sessão Psicologia"], "Unnamed: 4": ["Unidade", "Morumbi"], "Unnamed: 5": ["Procedimento", "Sessão para assistência fisioterapêutica ambulatorial ao paciente com disfunção decorrente de alterações do sistema músculo-esquelético, Sessão para assistência fisioterapêutica ambulatorial para alterações inflamatórias e ou degenerativas do aparelho genito-urinário e reprodutor", "1ª Sessão de psicoterapia"], "Unnamed: 6": ["Status", "Reagendado para o dia 23/04/2025", "Cancelou"]}, "numeric_stats": {}, "date_ranges": {}}, {"file_name": "relatorio_tempo_de_atendimento.xlsx", "row_count": 48, "column_count": 14, "columns": ["Cód. <PERSON>end<PERSON>", "Unidade", "Profissional", "Paciente", "ID paciente", "Tipo de Atendimento", "Data do Agendamento", "Hora do Agendamento", "Hora de chegada", "Inicio do atendimento", "Fim do atendimento", "Espera em minutos", "Atraso em minutos", "Tempo de Atendimento em minutos"], "data_types": {"Cód. Atendimento": "int64", "Unidade": "object", "Profissional": "object", "Paciente": "object", "ID paciente": "int64", "Tipo de Atendimento": "object", "Data do Agendamento": "object", "Hora do Agendamento": "object", "Hora de chegada": "object", "Inicio do atendimento": "object", "Fim do atendimento": "object", "Espera em minutos": "object", "Atraso em minutos": "object", "Tempo de Atendimento em minutos": "int64"}, "sample_data": [{"Cód. Atendimento": 155741330, "Unidade": "Morumbi", "Profissional": "Equipe Amigo - Caio <PERSON>", "Paciente": "LUIZ GUSTAVO", "ID paciente": 90817169, "Tipo de Atendimento": "Consulta - Amigo tech", "Data do Agendamento": "17/04/2025", "Hora do Agendamento": "17:40", "Hora de chegada": "18:26", "Inicio do atendimento": "18:27", "Fim do atendimento": "18:35", "Espera em minutos": 1, "Atraso em minutos": 47, "Tempo de Atendimento em minutos": 7}, {"Cód. Atendimento": 155719213, "Unidade": "Morumbi", "Profissional": "Equipe Amigo - Caio <PERSON>", "Paciente": "AMANDA 1704", "ID paciente": 90802077, "Tipo de Atendimento": "Consulta - Amigo tech", "Data do Agendamento": "17/04/2025", "Hora do Agendamento": "16:30", "Hora de chegada": "16:38", "Inicio do atendimento": "16:39", "Fim do atendimento": "16:41", "Espera em minutos": "-", "Atraso em minutos": 9, "Tempo de Atendimento em minutos": 2}, {"Cód. Atendimento": 155569527, "Unidade": "Morumbi", "Profissional": "Equipe Amigo - <PERSON><PERSON>", "Paciente": "DR RAFAEL TESTE", "ID paciente": 90775793, "Tipo de Atendimento": "Audiometria tonal", "Data do Agendamento": "17/04/2025", "Hora do Agendamento": "08:20", "Hora de chegada": "14:15", "Inicio do atendimento": "14:16", "Fim do atendimento": "14:32", "Espera em minutos": "-", "Atraso em minutos": "-", "Tempo de Atendimento em minutos": 15}], "unique_values": {"Unidade": ["Morumbi", "BRASILIA", "Recife", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Espera em minutos": [1, "-", 67, 2]}, "numeric_stats": {"Cód. Atendimento": {"min": 146952664, "max": 155741330, "mean": 152826778.875, "median": 152818664.0}, "ID paciente": {"min": 393011, "max": 90817169, "mean": 77325264.1875, "median": 89162715.0}, "Tempo de Atendimento em minutos": {"min": 1, "max": 63, "mean": 11.5, "median": 8.0}}, "date_ranges": {}}, {"file_name": "relatorio_mapacirurgico.xlsx", "row_count": 14, "column_count": 28, "columns": ["Data do agendamento", "<PERSON><PERSON>", "Unidade", "Sala", "Cód. Leg.", "Paciente", "<PERSON><PERSON>", "Telefone", "Tipo de Atendimento", "Qtd/Procedimento", "Forma de Pagamento", "Acomod.", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "00 - <PERSON><PERSON><PERSON><PERSON><PERSON>", "01 - 1º Auxiliar", "02 - 2º Auxiliar", "03 - 3º Auxiliar", "04 - 4º Auxiliar", "05 - Instrument<PERSON>", "06 - <PERSON><PERSON><PERSON><PERSON>", "07 - <PERSON><PERSON><PERSON><PERSON> Anestesista", "08 - Consultor", "09 - <PERSON><PERSON><PERSON>", "10 - Pediatra na sala de parto", "11 - Auxiliar SADT", "12 - Clínico", "13 - Intensivista"], "data_types": {"Data do agendamento": "object", "Hora": "object", "Unidade": "object", "Sala": "float64", "Cód. Leg.": "float64", "Paciente": "object", "Idade": "object", "Telefone": "object", "Tipo de Atendimento": "object", "Qtd/Procedimento": "object", "Forma de Pagamento": "object", "Acomod.": "float64", "Matrícula": "float64", "Atend. Confirmado": "object", "00 - Cirurgião": "object", "01 - 1º Auxiliar": "float64", "02 - 2º Auxiliar": "float64", "03 - 3º Auxiliar": "float64", "04 - 4º Auxiliar": "float64", "05 - Instrumentador": "float64", "06 - Anestesista": "float64", "07 - Auxiliar de Anestesista": "float64", "08 - Consultor": "float64", "09 - Perfusionista": "float64", "10 - Pediatra na sala de parto": "float64", "11 - Auxiliar SADT": "float64", "12 - Clínico": "float64", "13 - Intensivista": "float64"}, "sample_data": [{"Data do agendamento": "23/04/2025", "Hora": "17:00", "Unidade": "CLÍNICA (RL)", "Sala": NaN, "Cód. Leg.": NaN, "Paciente": "<PERSON><PERSON>", "Idade": "37 anos", "Telefone": "(61) 99184-4238", "Tipo de Atendimento": "Sessao de Fisioterapia", "Qtd/Procedimento": "1 - <PERSON><PERSON><PERSON> - teste", "Forma de Pagamento": "AMIL", "Acomod.": NaN, "Matrícula": 12234556.0, "Atend. Confirmado": "<PERSON>m", "00 - Cirurgião": "Rayara <PERSON> Souza", "01 - 1º Auxiliar": NaN, "02 - 2º Auxiliar": NaN, "03 - 3º Auxiliar": NaN, "04 - 4º Auxiliar": NaN, "05 - Instrumentador": NaN, "06 - Anestesista": NaN, "07 - Auxiliar de Anestesista": NaN, "08 - Consultor": NaN, "09 - Perfusionista": NaN, "10 - Pediatra na sala de parto": NaN, "11 - Auxiliar SADT": NaN, "12 - Clínico": NaN, "13 - Intensivista": NaN}, {"Data do agendamento": "21/04/2025", "Hora": "10:00", "Unidade": "Morumbi", "Sala": NaN, "Cód. Leg.": NaN, "Paciente": "<PERSON> teste", "Idade": NaN, "Telefone": NaN, "Tipo de Atendimento": "Sessao de Fisioterapia", "Qtd/Procedimento": "1 - <PERSON><PERSON><PERSON> - teste", "Forma de Pagamento": NaN, "Acomod.": NaN, "Matrícula": 21321321.0, "Atend. Confirmado": "Não", "00 - Cirurgião": "Equipe Amigo - BRUNO LIMA", "01 - 1º Auxiliar": NaN, "02 - 2º Auxiliar": NaN, "03 - 3º Auxiliar": NaN, "04 - 4º Auxiliar": NaN, "05 - Instrumentador": NaN, "06 - Anestesista": NaN, "07 - Auxiliar de Anestesista": NaN, "08 - Consultor": NaN, "09 - Perfusionista": NaN, "10 - Pediatra na sala de parto": NaN, "11 - Auxiliar SADT": NaN, "12 - Clínico": NaN, "13 - Intensivista": NaN}, {"Data do agendamento": "25/04/2025", "Hora": "15:00", "Unidade": "Morumbi", "Sala": NaN, "Cód. Leg.": NaN, "Paciente": "<PERSON>", "Idade": NaN, "Telefone": NaN, "Tipo de Atendimento": "Sessao de Fisioterapia", "Qtd/Procedimento": "1 - <PERSON><PERSON><PERSON> - teste", "Forma de Pagamento": "Pix", "Acomod.": NaN, "Matrícula": NaN, "Atend. Confirmado": "Não", "00 - Cirurgião": "Equipe Amigo - BRUNO LIMA", "01 - 1º Auxiliar": NaN, "02 - 2º Auxiliar": NaN, "03 - 3º Auxiliar": NaN, "04 - 4º Auxiliar": NaN, "05 - Instrumentador": NaN, "06 - Anestesista": NaN, "07 - Auxiliar de Anestesista": NaN, "08 - Consultor": NaN, "09 - Perfusionista": NaN, "10 - Pediatra na sala de parto": NaN, "11 - Auxiliar SADT": NaN, "12 - Clínico": NaN, "13 - Intensivista": NaN}], "unique_values": {"Data do agendamento": ["23/04/2025", "21/04/2025", "25/04/2025", "22/04/2025"], "Hora": ["17:00", "10:00", "15:00", "16:00", "18:00", "12:00"], "Unidade": ["CLÍNICA (RL)", "Morumbi", "BRASILIA", "Recife"], "Idade": ["37 anos", "29 anos", "30 anos"], "Telefone": ["(61) 99184-4238", "(11) 97505-4988", "(21) 99435-2590", "(11) 94020-9917", "(81) 99696-4897", "(81) 99797-0334"], "Tipo de Atendimento": ["Sessao de Fisioterapia"], "Qtd/Procedimento": ["1 - <PERSON><PERSON><PERSON> - teste"], "Forma de Pagamento": ["AMIL", "Pix", "BRADESCO", "Cartão"], "Atend. Confirmado": ["<PERSON>m", "Não"], "00 - Cirurgião": ["Rayara <PERSON> Souza", "Equipe Amigo - BRUNO LIMA", "Equipe Amigo - Giulia Pedrosa 2", "Equipe Amigo - Caio <PERSON>", "Equipe Amigo - Ana Victoria <PERSON> Almeida"]}, "numeric_stats": {"Sala": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "Cód. Leg.": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "Acomod.": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "Matrícula": {"min": 132465.0, "max": 782424624646.0, "mean": 156491708860.0, "median": 12234556.0}, "01 - 1º Auxiliar": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "02 - 2º Auxiliar": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "03 - 3º Auxiliar": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "04 - 4º Auxiliar": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "05 - Instrumentador": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "06 - Anestesista": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "07 - Auxiliar de Anestesista": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "08 - Consultor": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "09 - Perfusionista": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "10 - Pediatra na sala de parto": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "11 - Auxiliar SADT": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "12 - Clínico": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "13 - Intensivista": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}}, "date_ranges": {}}, {"file_name": "relatorio_agendamento.xlsx", "row_count": 40, "column_count": 13, "columns": ["Data do agendamento", "<PERSON><PERSON>", "Unidade", "Cód. Leg.", "Paciente", "CPF", "Telefone", "Email", "Médico", "Tipo de Atendimento", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Status"], "data_types": {"Data do agendamento": "object", "Hora": "object", "Unidade": "object", "Cód. Leg.": "float64", "Paciente": "object", "CPF": "float64", "Telefone": "object", "Email": "object", "Médico": "object", "Tipo de Atendimento": "object", "Convênio": "object", "Matrícula": "float64", "Status": "object"}, "sample_data": [{"Data do agendamento": "21/04/2025", "Hora": "08:00", "Unidade": "Recife", "Cód. Leg.": NaN, "Paciente": "22115244 - ACRISIO JP TESTE", "CPF": 764598457.0, "Telefone": "(11) 94441-1411", "Email": "<EMAIL>", "Médico": "Plano de Tratamento", "Tipo de Atendimento": "TERAPIA OCUPACIONAL", "Convênio": "Particular", "Matrícula": 3213213213.0, "Status": "<PERSON>ão confirmado"}, {"Data do agendamento": "21/04/2025", "Hora": "09:00", "Unidade": "Recife", "Cód. Leg.": NaN, "Paciente": "22115244 - ACRISIO JP TESTE", "CPF": 764598457.0, "Telefone": "(11) 94441-1411", "Email": "<EMAIL>", "Médico": "Plano de Tratamento", "Tipo de Atendimento": "PSICOLOGIA ABA", "Convênio": "Particular", "Matrícula": 3213213213.0, "Status": "<PERSON>ão confirmado"}, {"Data do agendamento": "21/04/2025", "Hora": "09:00", "Unidade": "Recife", "Cód. Leg.": NaN, "Paciente": "56642251 - ADELMO TESTE", "CPF": NaN, "Telefone": "(71) 98555-6677", "Email": NaN, "Médico": "Plano de Tratamento", "Tipo de Atendimento": "TERAPIA OCUPACIONAL", "Convênio": "Particular", "Matrícula": 132132132132.0, "Status": "<PERSON>ão confirmado"}], "unique_values": {"Data do agendamento": ["21/04/2025", "22/04/2025", "23/04/2025", "24/04/2025", "25/04/2025"], "Unidade": ["Recife", "Morumbi", "BRASILIA", "CLÍNICA (RL)"], "Email": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "Convênio": ["Particular", "BRADESCO", "AMIL"], "Status": ["<PERSON>ão confirmado", "Cancelou", "<PERSON><PERSON><PERSON><PERSON>"]}, "numeric_stats": {"Cód. Leg.": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "CPF": {"min": 764598457.0, "max": 97712877025.0, "mean": 19429422031.88, "median": 5149639486.0}, "Matrícula": {"min": 132465.0, "max": 782424624646.0, "mean": 120169550571.5, "median": 3213213213.0}}, "date_ranges": {}}, {"file_name": "relatorio_agendamento_novo.xlsx", "row_count": 9, "column_count": 30, "columns": ["Cód. <PERSON>end<PERSON>", "<PERSON><PERSON><PERSON>", "Agendado em", "Agendado por", "Con<PERSON>rmado em", "Confirmado por", "Data do Agendamento", "Hora do Agendamento", "Unidade", "Sala", "Profissional", "Sol. Interno", "Sol. Externo", "Paciente", "<PERSON><PERSON>", "Como conheceu", "ID Amigo", "Cod<PERSON>", "CPF", "Telefone", "E-mail", "<PERSON><PERSON><PERSON><PERSON>", "Tipo de Atendimento", "Tipo do Item", "Qtd. Item", "<PERSON><PERSON>", "Status do Agendamento", "Forma de Pagamento", "<PERSON><PERSON><PERSON>", "Acomodação"], "data_types": {"Cód. Atendimento": "int64", "Índice": "int64", "Agendado em": "object", "Agendado por": "object", "Confirmado em": "float64", "Confirmado por": "float64", "Data do Agendamento": "object", "Hora do Agendamento": "object", "Unidade": "object", "Sala": "object", "Profissional": "object", "Sol. Interno": "object", "Sol. Externo": "object", "Paciente": "object", "Idade": "object", "Como conheceu": "float64", "ID Amigo": "int64", "Cod. Legado": "object", "CPF": "object", "Telefone": "object", "E-mail": "object", "Matrícula": "object", "Tipo de Atendimento": "object", "Tipo do Item": "object", "Qtd. Item": "int64", "Item": "object", "Status do Agendamento": "object", "Forma de Pagamento": "object", "Tipo de Guia": "object", "Acomodação": "object"}, "sample_data": [{"Cód. Atendimento": 155723069, "Índice": 1, "Agendado em": "17/04/2025", "Agendado por": "Equipe Amigo - Caio <PERSON>", "Confirmado em": NaN, "Confirmado por": NaN, "Data do Agendamento": "22/04/2025", "Hora do Agendamento": "08:00", "Unidade": "Morumbi", "Sala": "-", "Profissional": "Equipe Amigo - Caio <PERSON>", "Sol. Interno": "-", "Sol. Externo": "-", "Paciente": "AMANDA 1704", "Idade": NaN, "Como conheceu": NaN, "ID Amigo": 90802077, "Cod. Legado": "-", "CPF": "-", "Telefone": "-", "E-mail": "-", "Matrícula": "-", "Tipo de Atendimento": "BOTOX", "Tipo do Item": "Procedimento", "Qtd. Item": 1, "Item": "Anestesia", "Status do Agendamento": "Agendado", "Forma de Pagamento": "Crédito de Procedimento", "Tipo de Guia": "-", "Acomodação": "-"}, {"Cód. Atendimento": 155723069, "Índice": 1, "Agendado em": "17/04/2025", "Agendado por": "Equipe Amigo - Caio <PERSON>", "Confirmado em": NaN, "Confirmado por": NaN, "Data do Agendamento": "22/04/2025", "Hora do Agendamento": "08:00", "Unidade": "Morumbi", "Sala": "-", "Profissional": "Equipe Amigo - Caio <PERSON>", "Sol. Interno": "-", "Sol. Externo": "-", "Paciente": "AMANDA 1704", "Idade": NaN, "Como conheceu": NaN, "ID Amigo": 90802077, "Cod. Legado": "-", "CPF": "-", "Telefone": "-", "E-mail": "-", "Matrícula": "-", "Tipo de Atendimento": "BOTOX", "Tipo do Item": "Procedimento", "Qtd. Item": 1, "Item": "Blefaroplastia", "Status do Agendamento": "Agendado", "Forma de Pagamento": "Crédito de Procedimento", "Tipo de Guia": "-", "Acomodação": "-"}, {"Cód. Atendimento": 151314579, "Índice": 2, "Agendado em": "24/03/2025", "Agendado por": "<PERSON><PERSON>", "Confirmado em": NaN, "Confirmado por": NaN, "Data do Agendamento": "22/04/2025", "Hora do Agendamento": "10:00", "Unidade": "BRASILIA", "Sala": "-", "Profissional": "Equipe Amigo - Giulia Pedrosa 2", "Sol. Interno": "-", "Sol. Externo": "-", "Paciente": "<PERSON><PERSON>", "Idade": "30 anos", "Como conheceu": NaN, "ID Amigo": 77719863, "Cod. Legado": "-", "CPF": "11577195701", "Telefone": "(21) 99435-2590", "E-mail": "<EMAIL>", "Matrícula": "782424624646", "Tipo de Atendimento": "Sessao de Fisioterapia", "Tipo do Item": "Procedimento", "Qtd. Item": 1, "Item": "<PERSON><PERSON><PERSON> - teste", "Status do Agendamento": "Agendado", "Forma de Pagamento": "Pendente", "Tipo de Guia": "-", "Acomodação": "-"}], "unique_values": {"Agendado em": ["17/04/2025", "24/03/2025", "07/04/2025", "08/04/2025", "16/04/2025"], "Agendado por": ["Equipe Amigo - Caio <PERSON>", "<PERSON><PERSON>", "Equipe Amigo - Giulia Pedrosa 2", "Equipe Amigo - Ana Victoria <PERSON> Almeida", "Plano de Tratamento"], "Data do Agendamento": ["22/04/2025"], "Hora do Agendamento": ["08:00", "10:00", "12:00", "15:00", "16:00"], "Unidade": ["Morumbi", "BRASILIA", "Recife"], "Sala": ["-", "SL TO", "SL ABA"], "Profissional": ["Equipe Amigo - Caio <PERSON>", "Equipe Amigo - Giulia Pedrosa 2", "Equipe Amigo - Ana Victoria <PERSON> Almeida", "Plano de Tratamento"], "Sol. Interno": ["-"], "Sol. Externo": ["-"], "Paciente": ["AMANDA 1704", "<PERSON><PERSON>", "MARINA HAZIN", "testse", "MARINA HAZIN CONVÊNIO", "Victória Almeida", "ACRISIO JP TESTE"], "Idade": ["30 anos", "43 anos"], "Cod. Legado": ["-"], "CPF": ["-", "11577195701", "10880304405", "00764598457"], "Telefone": ["-", "(21) 99435-2590", "(81) 99696-4897", "(11) 94020-9917", "(81) 99797-0334", "(11) 94441-1411"], "E-mail": ["-", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "Matrícula": ["-", "782424624646", "132465", "3213213213"], "Tipo de Atendimento": ["BOTOX", "Sessao de Fisioterapia", "TERAPIA OCUPACIONAL", "PSICOLOGIA ABA"], "Tipo do Item": ["Procedimento", "-"], "Item": ["Anestesia", "Blefaroplastia", "<PERSON><PERSON><PERSON> - teste", "-"], "Status do Agendamento": ["Agendado"], "Forma de Pagamento": ["Crédito de Procedimento", "Pendente", "Pix", "BRADESCO", "Cartão"], "Tipo de Guia": ["-", "SADT"], "Acomodação": ["-"]}, "numeric_stats": {"Cód. Atendimento": {"min": 151314579, "max": 155723069, "mean": 154295024.2222222, "median": 153827808.0}, "Índice": {"min": 1, "max": 8, "mean": 4.111111111111111, "median": 4.0}, "Confirmado em": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "Confirmado por": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "Como conheceu": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "ID Amigo": {"min": 9947091, "max": 90802077, "mean": 64400665.666666664, "median": 85794097.0}, "Qtd. Item": {"min": 1, "max": 1, "mean": 1.0, "median": 1.0}}, "date_ranges": {}}], "Modulo Financeiro": [{"file_name": "relatorio_caixa_banco.xlsx", "row_count": 32, "column_count": 26, "columns": ["Data de vencimento", "Data de pagamento", "Competência", "Tipo", "Pago a / Recebido de", "Categoria", "Classificação", "Descrição", "Forma de Pagamento", "Observação", "Nº documento", "Nº transação", "Tag", "Centro de custo", "Unidade", "Banco", "Valor Original R$", "PIS", "COFINS", "CSLL", "IR", "ISS", "INSS", "Valor Líquido R$", "Nota Emitida", "Recibo Emitido"], "data_types": {"Data de vencimento": "object", "Data de pagamento": "object", "Competência": "object", "Tipo": "object", "Pago a / Recebido de": "object", "Categoria": "object", "Classificação": "object", "Descrição": "object", "Forma de Pagamento": "object", "Observação": "object", "Nº documento": "object", "Nº transação": "object", "Tag": "object", "Centro de custo": "object", "Unidade": "object", "Banco": "object", "Valor Original R$": "float64", "PIS": "object", "COFINS": "object", "CSLL": "object", "IR": "object", "ISS": "object", "INSS": "object", "Valor Líquido R$": "float64", "Nota Emitida": "object", "Recibo Emitido": "object"}, "sample_data": [{"Data de vencimento": "01/04/2025", "Data de pagamento": "01/04/2025", "Competência": "04/2025", "Tipo": "Entrada", "Pago a / Recebido de": "<PERSON><PERSON>", "Categoria": "RECEITA OPERACIONAL", "Classificação": "Prestação de Serviço", "Descrição": "Atendimento - CONSULTA  COM CARDIOLOGISTA", "Forma de Pagamento": "Pix", "Observação": "-", "Nº documento": "-", "Nº transação": "-", "Tag": "-", "Centro de custo": "-", "Unidade": "BRASILIA", "Banco": "SANTANDER 1-1", "Valor Original R$": 2500.0, "PIS": "-", "COFINS": "-", "CSLL": "-", "IR": "-", "ISS": "-", "INSS": "-", "Valor Líquido R$": 2500.0, "Nota Emitida": "Não", "Recibo Emitido": "Não"}, {"Data de vencimento": "01/04/2025", "Data de pagamento": "01/04/2025", "Competência": "04/2025", "Tipo": "Entrada", "Pago a / Recebido de": "<PERSON><PERSON>", "Categoria": "RECEITA OPERACIONAL", "Classificação": "Prestação de Serviço", "Descrição": "Atendimento - CONSULTA  COM CARDIOLOGISTA", "Forma de Pagamento": "Pix", "Observação": "-", "Nº documento": "-", "Nº transação": "-", "Tag": "-", "Centro de custo": "-", "Unidade": "BRASILIA", "Banco": "ITAU 456", "Valor Original R$": 500.0, "PIS": "-", "COFINS": "-", "CSLL": "-", "IR": "-", "ISS": "-", "INSS": "-", "Valor Líquido R$": 500.0, "Nota Emitida": "Não", "Recibo Emitido": "Não"}, {"Data de vencimento": "02/04/2025", "Data de pagamento": "02/04/2025", "Competência": "04/2025", "Tipo": "Entrada", "Pago a / Recebido de": "KARINE VARGAS", "Categoria": "RECEITA OPERACIONAL", "Classificação": "Prestação de Serviço", "Descrição": "Atendimento - Consulta - Amigo tech", "Forma de Pagamento": "Pix", "Observação": "-", "Nº documento": "-", "Nº transação": "-", "Tag": "-", "Centro de custo": "-", "Unidade": "BRASILIA", "Banco": "SANTANDER 1-1", "Valor Original R$": 300.0, "PIS": "-", "COFINS": "-", "CSLL": "-", "IR": "-", "ISS": "-", "INSS": "-", "Valor Líquido R$": 300.0, "Nota Emitida": "Não", "Recibo Emitido": "Não"}], "unique_values": {"Competência": ["04/2025", "-", "03/2025"], "Tipo": ["Entrada", "<PERSON><PERSON><PERSON>", "Transferência"], "Categoria": ["RECEITA OPERACIONAL", "0 - ACERTO DE CAIXA", "*******.07 - DESPESAS COM PESSOAL", "*******.18 - DESPESAS COM PESSOAL", "*******.01 - UTILIDADES E SERVIÇOS DE TERCEIROS", "-"], "Classificação": ["Prestação de Serviço", "ACERTO DE CAIXA TESTE", "Ad<PERSON><PERSON><PERSON>", "<PERSON>ass<PERSON>", "Energia elétrica", "-"], "Forma de Pagamento": ["Pix", "<PERSON><PERSON><PERSON>", "Cartão", "-"], "Observação": ["-"], "Nº documento": ["-", "1321654", "154545"], "Nº transação": ["-"], "Tag": ["-"], "Centro de custo": ["-"], "Unidade": ["BRASILIA", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "-", "Morumbi", "Recife"], "Banco": ["SANTANDER 1-1", "ITAU 456", "NEON 78799-7", "Caixa", "Caixinha 0", "BANCO SAFRA 10187840-0", "bradesco 1-1", "Pessoa física", "BANCO DO BRASIL 10329-1"], "PIS": ["-"], "COFINS": ["-"], "CSLL": ["-"], "IR": ["-"], "ISS": ["-"], "INSS": ["-"], "Nota Emitida": ["Não"], "Recibo Emitido": ["Não", "<PERSON>m"]}, "numeric_stats": {"Valor Original R$": {"min": 2.5, "max": 20000.0, "mean": 1738.3178125, "median": 428.25}, "Valor Líquido R$": {"min": -2348.0, "max": 20000.0, "mean": 1454.0678125, "median": 300.0}}, "date_ranges": {}}, {"file_name": "relatorio_fechamento_caixa_diário.xlsx", "row_count": 85, "column_count": 14, "columns": ["Data Transação", "Data Atendimento", "Paciente", "Executante", "Sol. Primário", "Sol. Secundário", "Editor financeiro", "Qtd / Procedimento", "Forma de Pagamento", "NSU(DOC/CV/ID)", "Orçamento", "Nº Orçamento", "Observação", "Valor R$"], "data_types": {"Data Transação": "object", "Data Atendimento": "object", "Paciente": "object", "Executante": "object", "Sol. Primário": "object", "Sol. Secundário": "object", "Editor financeiro": "object", "Qtd / Procedimento": "object", "Forma de Pagamento": "object", "NSU(DOC/CV/ID)": "float64", "Orçamento": "object", "Nº Orçamento": "object", "Observação": "float64", "Valor R$": "float64"}, "sample_data": [{"Data Transação": "30/04/2025", "Data Atendimento": "30/04/2025", "Paciente": "<PERSON><PERSON>", "Executante": "Rayara <PERSON> Souza", "Sol. Primário": NaN, "Sol. Secundário": NaN, "Editor financeiro": "Rayara <PERSON> Souza", "Qtd / Procedimento": "1 - <PERSON><PERSON><PERSON> - teste", "Forma de Pagamento": "AMIL", "NSU(DOC/CV/ID)": NaN, "Orçamento": "Não", "Nº Orçamento": NaN, "Observação": NaN, "Valor R$": 20.0}, {"Data Transação": "23/04/2025", "Data Atendimento": "23/04/2025", "Paciente": "<PERSON><PERSON>", "Executante": "Rayara <PERSON> Souza", "Sol. Primário": NaN, "Sol. Secundário": NaN, "Editor financeiro": "Rayara <PERSON> Souza", "Qtd / Procedimento": "1 - <PERSON><PERSON><PERSON> - teste", "Forma de Pagamento": "AMIL", "NSU(DOC/CV/ID)": NaN, "Orçamento": "Não", "Nº Orçamento": NaN, "Observação": NaN, "Valor R$": 20.0}, {"Data Transação": "23/04/2025", "Data Atendimento": "23/04/2025", "Paciente": "<PERSON>", "Executante": "Equipe Amigo - BRUNO LIMA", "Sol. Primário": NaN, "Sol. Secundário": NaN, "Editor financeiro": "Equipe Amigo - BRUNO LIMA", "Qtd / Procedimento": "1 - BOTOX MALAR", "Forma de Pagamento": "Crédito de Procedimento", "NSU(DOC/CV/ID)": NaN, "Orçamento": "Não", "Nº Orçamento": NaN, "Observação": NaN, "Valor R$": 0.0}], "unique_values": {"Sol. Primário": ["Equipe Amigo - Ana Victoria <PERSON> Almeida"], "Sol. Secundário": ["Equipe Amigo - Al<PERSON>", "Equipe Amigo - Giulia Pedrosa 2"], "Orçamento": ["Não", "<PERSON>m"]}, "numeric_stats": {"NSU(DOC/CV/ID)": {"min": 649208.0, "max": 654165465.0, "mean": 171820117.75, "median": 16232899.0}, "Observação": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "Valor R$": {"min": 0.0, "max": 20000.0, "mean": 1738.5552941176472, "median": 300.0}}, "date_ranges": {}}, {"file_name": "relatorio_caixa_banco2.xlsx", "row_count": 32, "column_count": 12, "columns": ["Data de pagamento", "Competencia", "Tipo", "Pago a / Recebido de", "Categoria", "Classificação", "Descrição", "Forma de Pagamento", "Banco", "Valor R$", "Nota emitida", "Recibo emitido"], "data_types": {"Data de pagamento": "object", "Competencia": "object", "Tipo": "object", "Pago a / Recebido de": "object", "Categoria": "object", "Classificação": "object", "Descrição": "object", "Forma de Pagamento": "object", "Banco": "object", "Valor R$": "float64", "Nota emitida": "object", "Recibo emitido": "object"}, "sample_data": [{"Data de pagamento": "01/04/2025", "Competencia": "04/2025", "Tipo": "Entrada", "Pago a / Recebido de": "<PERSON><PERSON>", "Categoria": "RECEITA OPERACIONAL", "Classificação": "Prestação de Serviço", "Descrição": "Atendimento - CONSULTA  COM CARDIOLOGISTA", "Forma de Pagamento": "Pix", "Banco": "SANTANDER 1-1", "Valor R$": 2500.0, "Nota emitida": "Não", "Recibo emitido": "Não"}, {"Data de pagamento": "01/04/2025", "Competencia": "04/2025", "Tipo": "Entrada", "Pago a / Recebido de": "<PERSON><PERSON>", "Categoria": "RECEITA OPERACIONAL", "Classificação": "Prestação de Serviço", "Descrição": "Atendimento - CONSULTA  COM CARDIOLOGISTA", "Forma de Pagamento": "Pix", "Banco": "ITAU 456", "Valor R$": 500.0, "Nota emitida": "Não", "Recibo emitido": "Não"}, {"Data de pagamento": "02/04/2025", "Competencia": "04/2025", "Tipo": "Entrada", "Pago a / Recebido de": "KARINE VARGAS", "Categoria": "RECEITA OPERACIONAL", "Classificação": "Prestação de Serviço", "Descrição": "Atendimento - Consulta - Amigo tech", "Forma de Pagamento": "Pix", "Banco": "SANTANDER 1-1", "Valor R$": 300.0, "Nota emitida": "Não", "Recibo emitido": "Não"}], "unique_values": {"Competencia": ["04/2025", "-", "03/2025"], "Tipo": ["Entrada", "<PERSON><PERSON><PERSON>", "Transferência"], "Categoria": ["RECEITA OPERACIONAL", "0 - ACERTO DE CAIXA", "*******.07 - DESPESAS COM PESSOAL", "*******.18 - DESPESAS COM PESSOAL", "*******.01 - UTILIDADES E SERVIÇOS DE TERCEIROS", "-"], "Classificação": ["Prestação de Serviço", "ACERTO DE CAIXA TESTE", "Ad<PERSON><PERSON><PERSON>", "<PERSON>ass<PERSON>", "Energia elétrica", "-"], "Forma de Pagamento": ["Pix", "<PERSON><PERSON><PERSON>", "Cartão", "-"], "Banco": ["SANTANDER 1-1", "ITAU 456", "NEON 78799-7", "Caixa", "Caixinha 0", "BANCO SAFRA 10187840-0", "bradesco 1-1", "Pessoa física", "BANCO DO BRASIL 10329-1"], "Nota emitida": ["Não"], "Recibo emitido": ["Não", "<PERSON>m"]}, "numeric_stats": {"Valor R$": {"min": -2348.0, "max": 20000.0, "mean": 1454.0678125, "median": 300.0}}, "date_ranges": {}}, {"file_name": "relatorio_contas_a_pagar.xlsx", "row_count": 113, "column_count": 22, "columns": ["Data de vencimento", "Competência", "Pagar a", "Categoria", "Classificação", "Descrição", "Forma de Pagamento", "Observação", "Nº documento", "Tag", "Centro de custo", "Unidade", "Valor Original R$", "PIS", "COFINS", "CSLL", "IR", "ISS", "INSS", "Valor Líquido R$", "Nota Emitida", "Recibo Emitido"], "data_types": {"Data de vencimento": "object", "Competência": "object", "Pagar a": "object", "Categoria": "object", "Classificação": "object", "Descrição": "object", "Forma de Pagamento": "object", "Observação": "object", "Nº documento": "object", "Tag": "object", "Centro de custo": "object", "Unidade": "object", "Valor Original R$": "float64", "PIS": "object", "COFINS": "object", "CSLL": "object", "IR": "object", "ISS": "object", "INSS": "object", "Valor Líquido R$": "float64", "Nota Emitida": "object", "Recibo Emitido": "object"}, "sample_data": [{"Data de vencimento": "02/04/2025", "Competência": "04/2025", "Pagar a": "J P CONSULTORIA EM TECNOLOGIA DA INFORMAÇÃO", "Categoria": "3.2.2.2.17 - DESPESAS ADMINISTRATIVAS", "Classificação": "Suporte Técnico", "Descrição": "SERVICO DE SUPORTE EM TI", "Forma de Pagamento": "-", "Observação": "-", "Nº documento": "-", "Tag": "-", "Centro de custo": "-", "Unidade": "Recife", "Valor Original R$": 10000.0, "PIS": "-", "COFINS": "-", "CSLL": "-", "IR": "-", "ISS": "-", "INSS": "-", "Valor Líquido R$": -10000.0, "Nota Emitida": "Não", "Recibo Emitido": "Não"}, {"Data de vencimento": "02/04/2025", "Competência": "04/2025", "Pagar a": "COPEL", "Categoria": "*******.01 - UTILIDADES E SERVIÇOS DE TERCEIROS", "Classificação": "Energia elétrica", "Descrição": "LUZ", "Forma de Pagamento": "-", "Observação": "-", "Nº documento": "-", "Tag": "-", "Centro de custo": "-", "Unidade": "Recife", "Valor Original R$": 3000.0, "PIS": "-", "COFINS": "-", "CSLL": "-", "IR": "-", "ISS": "-", "INSS": "-", "Valor Líquido R$": -3000.0, "Nota Emitida": "Não", "Recibo Emitido": "Não"}, {"Data de vencimento": "02/04/2025", "Competência": "04/2025", "Pagar a": "ENEL teste", "Categoria": "*******.01 - UTILIDADES E SERVIÇOS DE TERCEIROS", "Classificação": "Energia elétrica", "Descrição": "conta energia", "Forma de Pagamento": "-", "Observação": "-", "Nº documento": "-", "Tag": "-", "Centro de custo": "-", "Unidade": "Morumbi", "Valor Original R$": 500.0, "PIS": "-", "COFINS": "-", "CSLL": "-", "IR": "-", "ISS": "-", "INSS": "-", "Valor Líquido R$": -500.0, "Nota Emitida": "Não", "Recibo Emitido": "Não"}], "unique_values": {"Competência": ["04/2025", "05/2025", "03/2025"], "Forma de Pagamento": ["-"], "Observação": ["-"], "Tag": ["-", "Despesa Nâo dedutível"], "Unidade": ["Recife", "Morumbi", "-", "Porto Alegre"], "PIS": ["-"], "COFINS": ["-"], "CSLL": ["-"], "IR": ["-"], "ISS": ["-"], "INSS": ["-"], "Nota Emitida": ["Não"], "Recibo Emitido": ["Não"]}, "numeric_stats": {"Valor Original R$": {"min": 10.0, "max": 20000.0, "mean": 2866.4554867256634, "median": 1000.0}, "Valor Líquido R$": {"min": -20000.0, "max": -10.0, "mean": -2121.6767256637168, "median": -990.0}}, "date_ranges": {}}, {"file_name": "relatorio_fluxo_de_caixa.xlsx", "row_count": 28, "column_count": 13, "columns": ["Unnamed: 0", "jan/25", "fev/25", "mar/25", "abr/25", "mai/25", "jun/25", "jul/25", "ago/25", "set/25", "out/25", "nov/25", "dez/25"], "data_types": {"Unnamed: 0": "object", "jan/25": "float64", "fev/25": "float64", "mar/25": "float64", "abr/25": "float64", "mai/25": "float64", "jun/25": "float64", "jul/25": "float64", "ago/25": "float64", "set/25": "float64", "out/25": "float64", "nov/25": "float64", "dez/25": "float64"}, "sample_data": [{"Unnamed: 0": "Entradas operacionais de caixa", "jan/25": 42200.8, "fev/25": 106376.3, "mar/25": 67812.0, "abr/25": 49738.17, "mai/25": 0.0, "jun/25": 0.0, "jul/25": 0.0, "ago/25": 0.0, "set/25": 0.0, "out/25": 0.0, "nov/25": 0.0, "dez/25": 0.0}, {"Unnamed: 0": "-  ACERTO DE CAIXA", "jan/25": 0.0, "fev/25": 0.0, "mar/25": 0.0, "abr/25": 160.0, "mai/25": 0.0, "jun/25": 0.0, "jul/25": 0.0, "ago/25": 0.0, "set/25": 0.0, "out/25": 0.0, "nov/25": 0.0, "dez/25": 0.0}, {"Unnamed: 0": "-      ACERTO DE CAIXA TESTE", "jan/25": 0.0, "fev/25": 0.0, "mar/25": 0.0, "abr/25": 160.0, "mai/25": 0.0, "jun/25": 0.0, "jul/25": 0.0, "ago/25": 0.0, "set/25": 0.0, "out/25": 0.0, "nov/25": 0.0, "dez/25": 0.0}], "unique_values": {}, "numeric_stats": {"jan/25": {"min": -1350.0, "max": 13002138.77, "mean": 934560.0264285713, "median": 0.0}, "fev/25": {"min": -26707.0, "max": 13081808.07, "mean": 945796.1907142857, "median": 0.0}, "mar/25": {"min": -18186.0, "max": 13131434.07, "mean": 945049.005, "median": 0.0}, "abr/25": {"min": -4548.0, "max": 13176624.24, "mean": 947643.1842857143, "median": 0.0}, "mai/25": {"min": 0.0, "max": 13176624.24, "mean": 941187.4457142857, "median": 0.0}, "jun/25": {"min": 0.0, "max": 13176624.24, "mean": 941187.4457142857, "median": 0.0}, "jul/25": {"min": 0.0, "max": 13176624.24, "mean": 941187.4457142857, "median": 0.0}, "ago/25": {"min": 0.0, "max": 13176624.24, "mean": 941187.4457142857, "median": 0.0}, "set/25": {"min": 0.0, "max": 13176624.24, "mean": 941187.4457142857, "median": 0.0}, "out/25": {"min": 0.0, "max": 13176624.24, "mean": 941187.4457142857, "median": 0.0}, "nov/25": {"min": 0.0, "max": 13176624.24, "mean": 941187.4457142857, "median": 0.0}, "dez/25": {"min": 0.0, "max": 13176624.24, "mean": 941187.4457142857, "median": 0.0}}, "date_ranges": {}}, {"file_name": "relatorio_de_repasse.xlsx", "row_count": 1, "column_count": 26, "columns": ["Data Atend", "Data Pagto.", "Cód. atendimento", "Cód. financeiro", "Unidade", "Paciente", "Forma de Pagamento", "Profissional Executante", "Grau part.", "Profissional Solicitante", "Procedimento/Matmed", "Tipo", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>a", "Desconto", "Líquido", "Repasse Executante", "Percentual Repasse Executante", "Repasse <PERSON>", "Percentual Repasse Solicitante", "Regra Repasse Executante", "Regra Repasse Solicitante", "Pagto. Repasse executante", "Pagto. Repasse solicitante", "Regra de repasse", "Pagto. Repasse"], "data_types": {"Data Atend": "object", "Data Pagto.": "object", "Cód. atendimento": "int64", "Cód. financeiro": "int64", "Unidade": "object", "Paciente": "object", "Forma de Pagamento": "object", "Profissional Executante": "object", "Grau part.": "object", "Profissional Solicitante": "float64", "Procedimento/Matmed": "object", "Tipo": "object", "Faturado": "float64", "Glosa": "int64", "Desconto": "int64", "Líquido": "float64", "Repasse Executante": "int64", "Percentual Repasse Executante": "float64", "Repasse Solicitante": "int64", "Percentual Repasse Solicitante": "float64", "Regra Repasse Executante": "float64", "Regra Repasse Solicitante": "float64", "Pagto. Repasse executante": "float64", "Pagto. Repasse solicitante": "float64", "Regra de repasse": "object", "Pagto. Repasse": "float64"}, "sample_data": [{"Data Atend": "23/04/2025", "Data Pagto.": "17/04/2025", "Cód. atendimento": 155686103, "Cód. financeiro": 170684150, "Unidade": "Morumbi", "Paciente": "<PERSON>", "Forma de Pagamento": "Pix", "Profissional Executante": "Equipe Amigo - BRUNO LIMA", "Grau part.": "Executante", "Profissional Solicitante": NaN, "Procedimento/Matmed": "BOTOX MALAR", "Tipo": "Procedimento", "Faturado": 952.38, "Glosa": 0, "Desconto": 0, "Líquido": 952.38, "Repasse Executante": 0, "Percentual Repasse Executante": NaN, "Repasse Solicitante": 0, "Percentual Repasse Solicitante": NaN, "Regra Repasse Executante": NaN, "Regra Repasse Solicitante": NaN, "Pagto. Repasse executante": NaN, "Pagto. Repasse solicitante": NaN, "Regra de repasse": "Não", "Pagto. Repasse": NaN}], "unique_values": {"Data Atend": ["23/04/2025"], "Data Pagto.": ["17/04/2025"], "Unidade": ["Morumbi"], "Paciente": ["<PERSON>"], "Forma de Pagamento": ["Pix"], "Profissional Executante": ["Equipe Amigo - BRUNO LIMA"], "Grau part.": ["Executante"], "Procedimento/Matmed": ["BOTOX MALAR"], "Tipo": ["Procedimento"], "Regra de repasse": ["Não"]}, "numeric_stats": {"Cód. atendimento": {"min": 155686103, "max": 155686103, "mean": 155686103.0, "median": 155686103.0}, "Cód. financeiro": {"min": 170684150, "max": 170684150, "mean": 170684150.0, "median": 170684150.0}, "Profissional Solicitante": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "Faturado": {"min": 952.38, "max": 952.38, "mean": 952.38, "median": 952.38}, "Glosa": {"min": 0, "max": 0, "mean": 0.0, "median": 0.0}, "Desconto": {"min": 0, "max": 0, "mean": 0.0, "median": 0.0}, "Líquido": {"min": 952.38, "max": 952.38, "mean": 952.38, "median": 952.38}, "Repasse Executante": {"min": 0, "max": 0, "mean": 0.0, "median": 0.0}, "Percentual Repasse Executante": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "Repasse Solicitante": {"min": 0, "max": 0, "mean": 0.0, "median": 0.0}, "Percentual Repasse Solicitante": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "Regra Repasse Executante": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "Regra Repasse Solicitante": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "Pagto. Repasse executante": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "Pagto. Repasse solicitante": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "Pagto. Repasse": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}}, "date_ranges": {}}, {"file_name": "relatorio_fechamento_de_caixa_novo.xlsx", "row_count": 8, "column_count": 18, "columns": ["Data Transação", "Data", "Código", "Tipo", "Paciente", "Executante", "Sol. Primário", "Sol. Secundário", "Qtd - Procedimento", "Editor <PERSON><PERSON>", "Pagamento Efetuado", "Forma de Pagamento", "NSU(DOC/CV/ID)", "Observação", "Pessoa Física", "Valor R$", "N° Nota Fiscal", "Recibo Emitido"], "data_types": {"Data Transação": "object", "Data": "object", "Código": "int64", "Tipo": "object", "Paciente": "object", "Executante": "object", "Sol. Primário": "float64", "Sol. Secundário": "float64", "Qtd - Procedimento": "object", "Editor Financeiro": "object", "Pagamento Efetuado": "object", "Forma de Pagamento": "object", "NSU(DOC/CV/ID)": "float64", "Observação": "float64", "Pessoa Física": "object", "Valor R$": "float64", "N° Nota Fiscal": "float64", "Recibo Emitido": "object"}, "sample_data": [{"Data Transação": "22/04/2025", "Data": "25/04/2025", "Código": 155491953, "Tipo": "Atendimento", "Paciente": "ACRISIO JP TESTE", "Executante": "Plano de Tratamento", "Sol. Primário": NaN, "Sol. Secundário": NaN, "Qtd - Procedimento": NaN, "Editor Financeiro": "<PERSON>", "Pagamento Efetuado": "<PERSON>m", "Forma de Pagamento": "Pix", "NSU(DOC/CV/ID)": NaN, "Observação": NaN, "Pessoa Física": "Não", "Valor R$": 1000.0, "N° Nota Fiscal": NaN, "Recibo Emitido": "Não"}, {"Data Transação": "22/04/2025", "Data": "21/04/2025", "Código": 153658697, "Tipo": "Atendimento", "Paciente": "Junior Souza", "Executante": "Equipe Amigo - BRUNO LIMA", "Sol. Primário": NaN, "Sol. Secundário": NaN, "Qtd - Procedimento": "1 - 1ª Sessão de psicoterapia", "Editor Financeiro": "Equipe Amigo - BRUNO LIMA", "Pagamento Efetuado": "Não", "Forma de Pagamento": "Cancelou", "NSU(DOC/CV/ID)": NaN, "Observação": NaN, "Pessoa Física": "Não", "Valor R$": 80.0, "N° Nota Fiscal": NaN, "Recibo Emitido": "Não"}, {"Data Transação": "22/04/2025", "Data": "22/04/2025", "Código": 155491945, "Tipo": "Atendimento", "Paciente": "ACRISIO JP TESTE", "Executante": "Plano de Tratamento", "Sol. Primário": NaN, "Sol. Secundário": NaN, "Qtd - Procedimento": NaN, "Editor Financeiro": "Sem editor finance<PERSON>", "Pagamento Efetuado": "Não", "Forma de Pagamento": "Pendente", "NSU(DOC/CV/ID)": NaN, "Observação": NaN, "Pessoa Física": "Não", "Valor R$": NaN, "N° Nota Fiscal": NaN, "Recibo Emitido": "Não"}], "unique_values": {"Data Transação": ["22/04/2025"], "Data": ["25/04/2025", "21/04/2025", "22/04/2025"], "Tipo": ["Atendimento"], "Paciente": ["ACRISIO JP TESTE", "Junior Souza", "AMANDA 1704", "<PERSON><PERSON>", "MARINA HAZIN", "MARINA HAZIN CONVÊNIO"], "Executante": ["Plano de Tratamento", "Equipe Amigo - BRUNO LIMA", "Equipe Amigo - Caio <PERSON>", "Equipe Amigo - Giulia Pedrosa 2"], "Qtd - Procedimento": ["1 - 1ª Sessão de psicoterapia", "1 - <PERSON><PERSON><PERSON>, 1 - <PERSON><PERSON>faroplastia", "1 - <PERSON><PERSON><PERSON> - teste"], "Editor Financeiro": ["<PERSON>", "Equipe Amigo - BRUNO LIMA", "Sem editor finance<PERSON>", "Equipe Amigo - Caio <PERSON>", "<PERSON><PERSON>"], "Pagamento Efetuado": ["<PERSON>m", "Não"], "Forma de Pagamento": ["Pix", "Cancelou", "Pendente", "Crédito de Procedimento", "BRADESCO"], "Pessoa Física": ["Não"], "Recibo Emitido": ["Não"]}, "numeric_stats": {"Código": {"min": 151314579, "max": 155723069, "mean": 154308580.125, "median": 154575321.0}, "Sol. Primário": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "Sol. Secundário": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "NSU(DOC/CV/ID)": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "Observação": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}, "Valor R$": {"min": 80.0, "max": 9000.0, "mean": 1728.3333333333333, "median": 100.0}, "N° Nota Fiscal": {"min": NaN, "max": NaN, "mean": NaN, "median": NaN}}, "date_ranges": {}}, {"file_name": "relatorio_contas_a_receber.xlsx", "row_count": 65, "column_count": 15, "columns": ["Data de vencimento", "Competência", "<PERSON><PERSON><PERSON> de", "Categoria", "Classificação", "Descrição", "Forma de Pagamento", "Observação", "Nº documento", "Tag", "Centro de custo", "Unidade", "Valor Líquido R$", "Nota Emitida", "Recibo Emitido"], "data_types": {"Data de vencimento": "object", "Competência": "object", "Receber de": "object", "Categoria": "object", "Classificação": "object", "Descrição": "object", "Forma de Pagamento": "object", "Observação": "object", "Nº documento": "object", "Tag": "object", "Centro de custo": "object", "Unidade": "object", "Valor Líquido R$": "float64", "Nota Emitida": "object", "Recibo Emitido": "object"}, "sample_data": [{"Data de vencimento": "01/04/2025", "Competência": "04/2025", "Receber de": "UNIMED", "Categoria": "RECEITA OPERACIONAL", "Classificação": "Prestação de Serviço", "Descrição": "Lote nº 266 - UNIMED", "Forma de Pagamento": "-", "Observação": "-", "Nº documento": "-", "Tag": "-", "Centro de custo": "-", "Unidade": "-", "Valor Líquido R$": 924.2, "Nota Emitida": "Não", "Recibo Emitido": "Não"}, {"Data de vencimento": "02/04/2025", "Competência": "-", "Receber de": "<PERSON>", "Categoria": "RECEITA OPERACIONAL", "Classificação": "Prestação de Serviço", "Descrição": "Orçamento #1568251", "Forma de Pagamento": "<PERSON><PERSON><PERSON>", "Observação": "-", "Nº documento": "-", "Tag": "-", "Centro de custo": "-", "Unidade": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Valor Líquido R$": 5000.0, "Nota Emitida": "Não", "Recibo Emitido": "Não"}, {"Data de vencimento": "03/04/2025", "Competência": "-", "Receber de": "<PERSON> <PERSON> - visita", "Categoria": "3.1.1.1.01 - RECEITA OPERACIONAL", "Classificação": "Prestação de Serviço", "Descrição": "ALUGUEL - 1.000", "Forma de Pagamento": "-", "Observação": "-", "Nº documento": "-", "Tag": "-", "Centro de custo": "-", "Unidade": "-", "Valor Líquido R$": 1212.0, "Nota Emitida": "Não", "Recibo Emitido": "Não"}], "unique_values": {"Competência": ["04/2025", "-", "03/2025", "02/2025"], "Categoria": ["RECEITA OPERACIONAL", "3.1.1.1.01 - RECEITA OPERACIONAL", "3.1.1.1.05 - RECEITA OPERACIONAL", "3.1.1.10.1 - OUTRAS RECEITAS"], "Classificação": ["Prestação de Serviço", "Venda de Produtos médicos", "<PERSON><PERSON><PERSON> de aluguel"], "Forma de Pagamento": ["-", "<PERSON><PERSON><PERSON>", "Pix", "Cartão", "Nota Promissória", "Cheque"], "Observação": ["-"], "Nº documento": ["-", "sderfgt", "123456", "wedsgf"], "Tag": ["-"], "Centro de custo": ["-", "Centro Cirurgico (30%)", "Centro de Processamento de Dados (70%)"], "Unidade": ["-", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BRASILIA", "Morumbi"], "Nota Emitida": ["Não"], "Recibo Emitido": ["Não", "<PERSON>m"]}, "numeric_stats": {"Valor Líquido R$": {"min": 0.0, "max": 11500.0, "mean": 1638.3886153846156, "median": 450.0}}, "date_ranges": {}}], "Modulo Paciente": [{"file_name": "relatório_orçamentos_em_aberto.xlsx", "row_count": 10, "column_count": 10, "columns": ["ID Amigo", "Paciente", "CPF", "Data Nasc.", "Sexo", "Vip", "E-mail", "<PERSON><PERSON><PERSON>", "Orçamentos", "Total R$"], "data_types": {"ID Amigo": "int64", "Paciente": "object", "CPF": "int64", "Data Nasc.": "object", "Sexo": "object", "Vip": "object", "E-mail": "object", "Celular": "object", "Orçamentos": "int64", "Total R$": "int64"}, "sample_data": [{"ID Amigo": 89863347, "Paciente": "<PERSON><PERSON><PERSON>", "CPF": 6820047070, "Data Nasc.": "21/09/1995", "Sexo": NaN, "Vip": "Não", "E-mail": "<EMAIL>", "Celular": "(11) 97505-4988", "Orçamentos": 3, "Total R$": 26350}, {"ID Amigo": 89900347, "Paciente": "<PERSON><PERSON>", "CPF": 84472975009, "Data Nasc.": "21/09/1995", "Sexo": NaN, "Vip": "Não", "E-mail": "<EMAIL>", "Celular": "(11) 97505-4988", "Orçamentos": 2, "Total R$": 21500}, {"ID Amigo": 26015293, "Paciente": "<PERSON> ", "CPF": 93070730744, "Data Nasc.": "10/01/2022", "Sexo": "<PERSON><PERSON><PERSON><PERSON>", "Vip": "<PERSON>m", "E-mail": "claudio<PERSON><PERSON><PERSON>@gmail.com", "Celular": "(71) 98852-3298", "Orçamentos": 1, "Total R$": 10500}], "unique_values": {"Data Nasc.": ["21/09/1995", "10/01/2022", "29/05/1975", "23/03/1981", "29/05/1997"], "Sexo": ["<PERSON><PERSON><PERSON><PERSON>"], "Vip": ["Não", "<PERSON>m"], "E-mail": ["<EMAIL>", "claudio<PERSON><PERSON><PERSON>@gmail.com", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "Celular": ["(11) 97505-4988", "(71) 98852-3298", "(27) 99650-3322", "(27) 99693-2233", "(31) 99572-0954", "(81) 99797-0334", "(19) 98118-7676"]}, "numeric_stats": {"ID Amigo": {"min": 3962189, "max": 90775793, "mean": 66140328.5, "median": 87828722.0}, "CPF": {"min": 3718245086, "max": 93070730744, "mean": 43913747793.6, "median": 28932382954.0}, "Orçamentos": {"min": 1, "max": 3, "mean": 1.3, "median": 1.0}, "Total R$": {"min": 720, "max": 26350, "mean": 9797.0, "median": 8100.0}}, "date_ranges": {}}, {"file_name": "relatorio_atendimentos_realizados.xlsx", "row_count": 47, "column_count": 11, "columns": ["ID Amigo", "Paciente", "CPF", "Data Nasc.", "Sexo", "Vip", "E-mail", "<PERSON><PERSON><PERSON>", "Atendimentos", "Ticket médio R$", "Total R$"], "data_types": {"ID Amigo": "int64", "Paciente": "object", "CPF": "float64", "Data Nasc.": "object", "Sexo": "object", "Vip": "object", "E-mail": "object", "Celular": "object", "Atendimentos": "int64", "Ticket médio R$": "float64", "Total R$": "float64"}, "sample_data": [{"ID Amigo": 89900347, "Paciente": "<PERSON><PERSON>", "CPF": 84472975009.0, "Data Nasc.": "21/09/1995", "Sexo": NaN, "Vip": "Não", "E-mail": "<EMAIL>", "Celular": "(11) 97505-4988", "Atendimentos": 1, "Ticket médio R$": 25000.0, "Total R$": 25000.0}, {"ID Amigo": 38204852, "Paciente": "<PERSON><PERSON><PERSON>", "CPF": 5149639486.0, "Data Nasc.": "09/09/1993", "Sexo": "<PERSON><PERSON><PERSON><PERSON>", "Vip": "Não", "E-mail": "<EMAIL>", "Celular": "(81) 99730-4365", "Atendimentos": 3, "Ticket médio R$": 1535.5, "Total R$": 4606.5}, {"ID Amigo": 58369419, "Paciente": "thais horst capistrano", "CPF": 2437170081.0, "Data Nasc.": "13/03/1997", "Sexo": "Feminino", "Vip": "Não", "E-mail": "<EMAIL>", "Celular": "(11) 99015-7988", "Atendimentos": 3, "Ticket médio R$": 1466.67, "Total R$": 4400.0}], "unique_values": {"Sexo": ["<PERSON><PERSON><PERSON><PERSON>", "Feminino"], "Vip": ["Não", "<PERSON>m"]}, "numeric_stats": {"ID Amigo": {"min": 27050798, "max": 90817169, "mean": 84139991.9787234, "median": 90101577.0}, "CPF": {"min": 195423135.0, "max": 84472975009.0, "mean": 26671393898.944443, "median": 11403389235.5}, "Atendimentos": {"min": 1, "max": 4, "mean": 1.2765957446808511, "median": 1.0}, "Ticket médio R$": {"min": 2.5, "max": 25000.0, "mean": 936.6265957446809, "median": 300.0}, "Total R$": {"min": 2.5, "max": 25000.0, "mean": 1162.6851063829788, "median": 300.0}}, "date_ranges": {}}, {"file_name": "relatorio_orçamentos_fechados.xlsx", "row_count": 14, "column_count": 10, "columns": ["ID Amigo", "Paciente", "CPF", "Data Nasc.", "Sexo", "Vip", "E-mail", "<PERSON><PERSON><PERSON>", "Orçamentos", "Total R$"], "data_types": {"ID Amigo": "int64", "Paciente": "object", "CPF": "float64", "Data Nasc.": "object", "Sexo": "object", "Vip": "object", "E-mail": "object", "Celular": "object", "Orçamentos": "int64", "Total R$": "int64"}, "sample_data": [{"ID Amigo": 90157170, "Paciente": "Junior Souza", "CPF": 3718245086.0, "Data Nasc.": "21/09/1995", "Sexo": NaN, "Vip": "Não", "E-mail": "<EMAIL>", "Celular": "(11) 97505-4988", "Orçamentos": 2, "Total R$": 20500}, {"ID Amigo": 89863347, "Paciente": "<PERSON><PERSON><PERSON>", "CPF": 6820047070.0, "Data Nasc.": "21/09/1995", "Sexo": NaN, "Vip": "Não", "E-mail": "<EMAIL>", "Celular": "(11) 97505-4988", "Orçamentos": 1, "Total R$": 17500}, {"ID Amigo": 38204852, "Paciente": "<PERSON><PERSON><PERSON>", "CPF": 5149639486.0, "Data Nasc.": "09/09/1993", "Sexo": "<PERSON><PERSON><PERSON><PERSON>", "Vip": "Não", "E-mail": "<EMAIL>", "Celular": "(81) 99730-4365", "Orçamentos": 2, "Total R$": 14200}], "unique_values": {"Data Nasc.": ["21/09/1995", "09/09/1993", "23/03/1981", "10/05/1996", "29/05/1997"], "Sexo": ["<PERSON><PERSON><PERSON><PERSON>"], "Vip": ["Não", "<PERSON>m"], "E-mail": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]}, "numeric_stats": {"ID Amigo": {"min": 19085497, "max": 90802077, "mean": 76271349.21428572, "median": 89995358.5}, "CPF": {"min": 195423135.0, "max": 75731649120.0, "mean": 22922115628.2, "median": 10589554111.5}, "Orçamentos": {"min": 1, "max": 2, "mean": 1.3571428571428572, "median": 1.0}, "Total R$": {"min": 300, "max": 20500, "mean": 9040.42857142857, "median": 9000.0}}, "date_ranges": {}}, {"file_name": "relatorio_creditos_disponiveis.xlsx", "row_count": 794, "column_count": 11, "columns": ["ID Amigo", "Paciente", "CPF", "Data Nasc.", "Sexo", "Vip", "E-mail", "<PERSON><PERSON><PERSON>", "Crédito Pré-pago R$", "Crédito Orçamento R$", "Total R$"], "data_types": {"ID Amigo": "int64", "Paciente": "object", "CPF": "float64", "Data Nasc.": "object", "Sexo": "object", "Vip": "object", "E-mail": "object", "Celular": "object", "Crédito Pré-pago R$": "int64", "Crédito Orçamento R$": "int64", "Total R$": "float64"}, "sample_data": [{"ID Amigo": 58369419, "Paciente": "thais horst capistrano", "CPF": 2437170081.0, "Data Nasc.": "13/03/1997", "Sexo": "Feminino", "Vip": "Não", "E-mail": "<EMAIL>", "Celular": "(11) 99015-7988", "Crédito Pré-pago R$": 0, "Crédito Orçamento R$": 108700, "Total R$": 108700.01}, {"ID Amigo": 24948063, "Paciente": "<PERSON><PERSON>", "CPF": 80680185062.0, "Data Nasc.": "29/10/1992", "Sexo": NaN, "Vip": "<PERSON>m", "E-mail": "<EMAIL>", "Celular": "(11) 99423-4069", "Crédito Pré-pago R$": 5429, "Crédito Orçamento R$": 94470, "Total R$": 99899.0}, {"ID Amigo": 77719863, "Paciente": "<PERSON><PERSON>", "CPF": 11577195701.0, "Data Nasc.": "25/02/1995", "Sexo": "Feminino", "Vip": "<PERSON>m", "E-mail": "<EMAIL>", "Celular": "(21) 99435-2590", "Crédito Pré-pago R$": 4000, "Crédito Orçamento R$": 87780, "Total R$": 91780.0}], "unique_values": {"Sexo": ["Feminino", "<PERSON><PERSON><PERSON><PERSON>"], "Vip": ["Não", "<PERSON>m"]}, "numeric_stats": {"ID Amigo": {"min": 39306, "max": 90796209, "mean": 54967410.96095718, "median": 62103074.0}, "CPF": {"min": 15053008.0, "max": 99440761040.0, "mean": 32534537397.564102, "median": 20361188352.5}, "Crédito Pré-pago R$": {"min": -1500, "max": 50000, "mean": 1468.3639798488664, "median": 0.0}, "Crédito Orçamento R$": {"min": 0, "max": 108700, "mean": 6591.8753148614605, "median": 4223.0}, "Total R$": {"min": -250.0, "max": 108700.01, "mean": 8060.280793450881, "median": 5000.0}}, "date_ranges": {}}]}
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagrama do Schema Global - Amigo DataHub</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3 {
            color: #007AFF;
        }
        .diagram-container {
            overflow-x: auto;
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e5e5ea;
            border-radius: 10px;
            background-color: #fff;
        }
        .entity {
            fill: #f2f2f7;
            stroke: #8E8E93;
            stroke-width: 1;
            rx: 5;
            ry: 5;
        }
        .entity-title {
            font-weight: bold;
            font-size: 14px;
            fill: #007AFF;
        }
        .entity-field {
            font-size: 12px;
            fill: #333;
        }
        .required-field {
            font-weight: bold;
        }
        .relationship {
            stroke: #8E8E93;
            stroke-width: 1;
            fill: none;
        }
        .relationship-label {
            font-size: 10px;
            fill: #8E8E93;
        }
        .legend {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #e5e5ea;
            border-radius: 10px;
            background-color: #f2f2f7;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .legend-color {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            border: 1px solid #8E8E93;
            border-radius: 3px;
        }
        .legend-required {
            font-weight: bold;
        }
        .module-section {
            margin-top: 40px;
            padding: 20px;
            border: 1px solid #e5e5ea;
            border-radius: 10px;
            background-color: #fff;
        }
        .module-title {
            color: #007AFF;
            margin-bottom: 15px;
        }
        .module-description {
            margin-bottom: 15px;
        }
        .module-entities {
            margin-bottom: 15px;
        }
        .module-analyses {
            margin-left: 20px;
        }
        .analysis-item {
            margin-bottom: 10px;
        }
        .analysis-name {
            font-weight: bold;
        }
        .analysis-description {
            font-size: 14px;
            color: #666;
        }
        .analysis-data {
            font-size: 12px;
            color: #8E8E93;
            font-family: monospace;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <h1>Diagrama do Schema Global - Amigo DataHub</h1>
    <p>Este diagrama representa o schema global do Amigo DataHub, mostrando as principais entidades, seus campos e relacionamentos.</p>

    <div class="diagram-container">
        <svg width="1100" height="800" xmlns="http://www.w3.org/2000/svg">
            <!-- Entidade Paciente -->
            <rect x="50" y="50" width="200" height="220" class="entity" />
            <text x="150" y="75" text-anchor="middle" class="entity-title">Paciente</text>
            <line x1="50" y1="85" x2="250" y2="85" stroke="#8E8E93" stroke-width="1" />
            <text x="60" y="105" class="entity-field required-field">id</text>
            <text x="60" y="125" class="entity-field required-field">nome</text>
            <text x="60" y="145" class="entity-field">cpf</text>
            <text x="60" y="165" class="entity-field">data_nascimento</text>
            <text x="60" y="185" class="entity-field">email</text>
            <text x="60" y="205" class="entity-field">telefone</text>
            <text x="60" y="225" class="entity-field">como_conheceu</text>
            <text x="60" y="245" class="entity-field">...</text>

            <!-- Entidade Agendamento -->
            <rect x="350" y="50" width="200" height="220" class="entity" />
            <text x="450" y="75" text-anchor="middle" class="entity-title">Agendamento</text>
            <line x1="350" y1="85" x2="550" y2="85" stroke="#8E8E93" stroke-width="1" />
            <text x="360" y="105" class="entity-field required-field">id</text>
            <text x="360" y="125" class="entity-field required-field">data</text>
            <text x="360" y="145" class="entity-field required-field">paciente_id</text>
            <text x="360" y="165" class="entity-field">hora</text>
            <text x="360" y="185" class="entity-field">profissional_id</text>
            <text x="360" y="205" class="entity-field">unidade_id</text>
            <text x="360" y="225" class="entity-field">procedimento_id</text>
            <text x="360" y="245" class="entity-field">status</text>

            <!-- Entidade Profissional -->
            <rect x="650" y="50" width="200" height="180" class="entity" />
            <text x="750" y="75" text-anchor="middle" class="entity-title">Profissional</text>
            <line x1="650" y1="85" x2="850" y2="85" stroke="#8E8E93" stroke-width="1" />
            <text x="660" y="105" class="entity-field required-field">id</text>
            <text x="660" y="125" class="entity-field required-field">nome</text>
            <text x="660" y="145" class="entity-field">especialidade</text>
            <text x="660" y="165" class="entity-field">cbo</text>
            <text x="660" y="185" class="entity-field">unidade_id</text>
            <text x="660" y="205" class="entity-field">...</text>

            <!-- Entidade TransacaoFinanceira -->
            <rect x="350" y="350" width="200" height="220" class="entity" />
            <text x="450" y="375" text-anchor="middle" class="entity-title">TransacaoFinanceira</text>
            <line x1="350" y1="385" x2="550" y2="385" stroke="#8E8E93" stroke-width="1" />
            <text x="360" y="405" class="entity-field required-field">id</text>
            <text x="360" y="425" class="entity-field required-field">valor</text>
            <text x="360" y="445" class="entity-field required-field">data</text>
            <text x="360" y="465" class="entity-field required-field">agendamento_id</text>
            <text x="360" y="485" class="entity-field">paciente_id</text>
            <text x="360" y="505" class="entity-field">tipo</text>
            <text x="360" y="525" class="entity-field">status</text>
            <text x="360" y="545" class="entity-field">...</text>

            <!-- Entidade AvaliacaoNPS -->
            <rect x="50" y="350" width="200" height="180" class="entity" />
            <text x="150" y="375" text-anchor="middle" class="entity-title">AvaliacaoNPS</text>
            <line x1="50" y1="385" x2="250" y2="385" stroke="#8E8E93" stroke-width="1" />
            <text x="60" y="405" class="entity-field required-field">id</text>
            <text x="60" y="425" class="entity-field required-field">score</text>
            <text x="60" y="445" class="entity-field required-field">data</text>
            <text x="60" y="465" class="entity-field">paciente_id</text>
            <text x="60" y="485" class="entity-field">agendamento_id</text>
            <text x="60" y="505" class="entity-field">categoria</text>
            <text x="60" y="525" class="entity-field">...</text>

            <!-- Entidade Procedimento -->
            <rect x="650" y="350" width="200" height="180" class="entity" />
            <text x="750" y="375" text-anchor="middle" class="entity-title">Procedimento</text>
            <line x1="650" y1="385" x2="850" y2="385" stroke="#8E8E93" stroke-width="1" />
            <text x="660" y="405" class="entity-field required-field">id</text>
            <text x="660" y="425" class="entity-field required-field">nome</text>
            <text x="660" y="445" class="entity-field">categoria</text>
            <text x="660" y="465" class="entity-field">valor_base</text>
            <text x="660" y="485" class="entity-field">grupo</text>
            <text x="660" y="505" class="entity-field">...</text>

            <!-- Entidade Unidade -->
            <rect x="850" y="200" width="200" height="160" class="entity" />
            <text x="950" y="225" text-anchor="middle" class="entity-title">Unidade</text>
            <line x1="850" y1="235" x2="1050" y2="235" stroke="#8E8E93" stroke-width="1" />
            <text x="860" y="255" class="entity-field required-field">id</text>
            <text x="860" y="275" class="entity-field required-field">nome</text>
            <text x="860" y="295" class="entity-field">endereco</text>
            <text x="860" y="315" class="entity-field">telefone</text>
            <text x="860" y="335" class="entity-field">...</text>

            <!-- Entidade Orcamento -->
            <rect x="50" y="600" width="200" height="180" class="entity" />
            <text x="150" y="625" text-anchor="middle" class="entity-title">Orcamento</text>
            <line x1="50" y1="635" x2="250" y2="635" stroke="#8E8E93" stroke-width="1" />
            <text x="60" y="655" class="entity-field required-field">id</text>
            <text x="60" y="675" class="entity-field required-field">paciente_id</text>
            <text x="60" y="695" class="entity-field required-field">data</text>
            <text x="60" y="715" class="entity-field required-field">valor_total</text>
            <text x="60" y="735" class="entity-field">status</text>
            <text x="60" y="755" class="entity-field">...</text>

            <!-- Entidade Lead -->
            <rect x="350" y="600" width="200" height="180" class="entity" />
            <text x="450" y="625" text-anchor="middle" class="entity-title">Lead</text>
            <line x1="350" y1="635" x2="550" y2="635" stroke="#8E8E93" stroke-width="1" />
            <text x="360" y="655" class="entity-field required-field">id</text>
            <text x="360" y="675" class="entity-field required-field">nome</text>
            <text x="360" y="695" class="entity-field required-field">data_criacao</text>
            <text x="360" y="715" class="entity-field">email</text>
            <text x="360" y="735" class="entity-field">telefone</text>
            <text x="360" y="755" class="entity-field">...</text>

            <!-- Entidade Campanha -->
            <rect x="650" y="600" width="200" height="160" class="entity" />
            <text x="750" y="625" text-anchor="middle" class="entity-title">Campanha</text>
            <line x1="650" y1="635" x2="850" y2="635" stroke="#8E8E93" stroke-width="1" />
            <text x="660" y="655" class="entity-field required-field">id</text>
            <text x="660" y="675" class="entity-field required-field">nome</text>
            <text x="660" y="695" class="entity-field required-field">data_inicio</text>
            <text x="660" y="715" class="entity-field">data_fim</text>
            <text x="660" y="735" class="entity-field">...</text>

            <!-- Relacionamentos -->
            <!-- Paciente -> Agendamento -->
            <path d="M250,150 H300 Q325,150 325,175 V175 Q325,200 350,200" class="relationship" marker-end="url(#arrow)" />
            <text x="300" y="140" class="relationship-label">1:N</text>

            <!-- Paciente -> TransacaoFinanceira -->
            <path d="M150,270 V310 H300 Q325,310 325,335 V335 Q325,360 350,360" class="relationship" marker-end="url(#arrow)" />
            <text x="200" y="300" class="relationship-label">1:N</text>

            <!-- Paciente -> AvaliacaoNPS -->
            <path d="M150,270 V310 Q150,335 150,360" class="relationship" marker-end="url(#arrow)" />
            <text x="160" y="320" class="relationship-label">1:N</text>

            <!-- Paciente -> Orcamento -->
            <path d="M150,530 V565 Q150,585 150,600" class="relationship" marker-end="url(#arrow)" />
            <text x="160" y="570" class="relationship-label">1:N</text>

            <!-- Profissional -> Agendamento -->
            <path d="M650,150 H600 Q575,150 575,175 V175 Q575,200 550,200" class="relationship" marker-end="url(#arrow)" />
            <text x="600" y="140" class="relationship-label">1:N</text>

            <!-- Unidade -> Agendamento -->
            <path d="M850,250 H700 Q675,250 675,225 V225 Q675,200 550,200" class="relationship" marker-end="url(#arrow)" />
            <text x="700" y="240" class="relationship-label">1:N</text>

            <!-- Unidade -> Profissional -->
            <path d="M850,250 H800 Q775,250 775,225 V225 Q775,200 750,175" class="relationship" marker-end="url(#arrow)" />
            <text x="775" y="220" class="relationship-label">1:N</text>

            <!-- Procedimento -> Agendamento -->
            <path d="M750,350 V300 H600 Q575,300 575,275 V275 Q575,250 550,225" class="relationship" marker-end="url(#arrow)" />
            <text x="650" y="290" class="relationship-label">1:N</text>

            <!-- Agendamento -> TransacaoFinanceira -->
            <path d="M450,270 V310 Q450,335 450,350" class="relationship" marker-end="url(#arrow)" />
            <text x="460" y="320" class="relationship-label">1:N</text>

            <!-- Agendamento -> AvaliacaoNPS -->
            <path d="M350,200 H300 Q275,200 275,225 V335 Q275,360 250,400" class="relationship" marker-end="url(#arrow)" />
            <text x="275" y="350" class="relationship-label">1:1</text>

            <!-- Lead -> Campanha -->
            <path d="M550,700 H600 Q625,700 650,700" class="relationship" marker-end="url(#arrow)" />
            <text x="600" y="690" class="relationship-label">N:1</text>

            <!-- Definições de marcadores -->
            <defs>
                <marker id="arrow" viewBox="0 0 10 10" refX="5" refY="5"
                    markerWidth="6" markerHeight="6"
                    orient="auto-start-reverse">
                    <path d="M 0 0 L 10 5 L 0 10 z" fill="#8E8E93"/>
                </marker>
            </defs>
        </svg>
    </div>

    <div class="legend">
        <h3>Legenda</h3>
        <div class="legend-item">
            <div class="legend-color" style="background-color: #f2f2f7;"></div>
            <div>Entidade</div>
        </div>
        <div class="legend-item">
            <div class="legend-required">Campo em negrito</div>
            <div>Campo obrigatório</div>
        </div>
        <div class="legend-item">
            <div>1:N</div>
            <div>Relacionamento um para muitos</div>
        </div>
        <div class="legend-item">
            <div>1:1</div>
            <div>Relacionamento um para um</div>
        </div>
        <div class="legend-item">
            <div>N:1</div>
            <div>Relacionamento muitos para um</div>
        </div>
    </div>

    <h2>Módulos de Análise</h2>
    <p>O Amigo DataHub organiza suas análises em módulos, cada um focado em um aspecto específico da operação da clínica.</p>

    <div class="module-section">
        <h3 class="module-title">Módulo Agenda</h3>
        <p class="module-description">Análises relacionadas a agendamentos, produção médica e tempo de atendimento.</p>

        <div class="module-entities">
            <strong>Entidades Requeridas:</strong> Agendamento<br>
            <strong>Entidades Recomendadas:</strong> Paciente, Profissional, Unidade, Procedimento
        </div>

        <h4>Análises Disponíveis:</h4>
        <div class="module-analyses">
            <div class="analysis-item">
                <div class="analysis-name">Agendamentos por período</div>
                <div class="analysis-description">Análise de agendamentos por período (dia, semana, mês)</div>
                <div class="analysis-data">Dados mínimos: Agendamento.data, Agendamento.status</div>
            </div>
            <div class="analysis-item">
                <div class="analysis-name">Produção médica</div>
                <div class="analysis-description">Análise de produção por profissional</div>
                <div class="analysis-data">Dados mínimos: Agendamento.data, Agendamento.profissional_id, Agendamento.status</div>
            </div>
            <div class="analysis-item">
                <div class="analysis-name">Tempo de atendimento</div>
                <div class="analysis-description">Análise de tempo médio de atendimento por profissional ou procedimento</div>
                <div class="analysis-data">Dados mínimos: Agendamento.hora_inicio, Agendamento.hora_fim, Agendamento.profissional_id</div>
            </div>
            <div class="analysis-item">
                <div class="analysis-name">Cancelamentos</div>
                <div class="analysis-description">Análise de cancelamentos por motivo, profissional ou período</div>
                <div class="analysis-data">Dados mínimos: Agendamento.status, Agendamento.data</div>
            </div>
        </div>
    </div>

    <div class="module-section">
        <h3 class="module-title">Módulo Financeiro</h3>
        <p class="module-description">Análises financeiras como contas a receber, contas a pagar e fluxo de caixa.</p>

        <div class="module-entities">
            <strong>Entidades Requeridas:</strong> TransacaoFinanceira<br>
            <strong>Entidades Recomendadas:</strong> Parcela, Paciente, Agendamento
        </div>

        <h4>Análises Disponíveis:</h4>
        <div class="module-analyses">
            <div class="analysis-item">
                <div class="analysis-name">Contas a receber</div>
                <div class="analysis-description">Análise de contas a receber por período, status ou categoria</div>
                <div class="analysis-data">Dados mínimos: TransacaoFinanceira.data_vencimento, TransacaoFinanceira.valor, TransacaoFinanceira.agendamento_id, TransacaoFinanceira.status</div>
            </div>
            <div class="analysis-item">
                <div class="analysis-name">Contas a pagar</div>
                <div class="analysis-description">Análise de contas a pagar por período, status ou categoria</div>
                <div class="analysis-data">Dados mínimos: TransacaoFinanceira.data_vencimento, TransacaoFinanceira.valor, TransacaoFinanceira.agendamento_id, TransacaoFinanceira.status, TransacaoFinanceira.tipo</div>
            </div>
            <div class="analysis-item">
                <div class="analysis-name">Fluxo de caixa</div>
                <div class="analysis-description">Análise de fluxo de caixa por período</div>
                <div class="analysis-data">Dados mínimos: TransacaoFinanceira.data, TransacaoFinanceira.valor, TransacaoFinanceira.agendamento_id, TransacaoFinanceira.tipo</div>
            </div>
            <div class="analysis-item">
                <div class="analysis-name">Fechamento de caixa</div>
                <div class="analysis-description">Análise de fechamento de caixa por período ou unidade</div>
                <div class="analysis-data">Dados mínimos: TransacaoFinanceira.data, TransacaoFinanceira.valor, TransacaoFinanceira.agendamento_id, TransacaoFinanceira.forma_pagamento</div>
            </div>
        </div>
    </div>

    <div class="module-section">
        <h3 class="module-title">Módulo AmigoCare</h3>
        <p class="module-description">Análises relacionadas a satisfação do cliente, leads e campanhas.</p>

        <div class="module-entities">
            <strong>Entidades Requeridas:</strong> AvaliacaoNPS<br>
            <strong>Entidades Recomendadas:</strong> Paciente, Agendamento, Lead, Campanha
        </div>

        <h4>Análises Disponíveis:</h4>
        <div class="module-analyses">
            <div class="analysis-item">
                <div class="analysis-name">Avaliação NPS</div>
                <div class="analysis-description">Análise de satisfação do cliente por período, profissional ou procedimento</div>
                <div class="analysis-data">Dados mínimos: AvaliacaoNPS.score, AvaliacaoNPS.data, Paciente.id</div>
            </div>
            <div class="analysis-item">
                <div class="analysis-name">Leads</div>
                <div class="analysis-description">Análise de leads por origem, status ou período</div>
                <div class="analysis-data">Dados mínimos: Lead.nome, Lead.data_criacao, Lead.status</div>
            </div>
            <div class="analysis-item">
                <div class="analysis-name">Campanhas</div>
                <div class="analysis-description">Análise de campanhas por período, tipo ou resultado</div>
                <div class="analysis-data">Dados mínimos: Campanha.nome, Campanha.data_inicio, Campanha.data_fim</div>
            </div>
        </div>
    </div>

    <div class="module-section">
        <h3 class="module-title">Módulo Visão 360</h3>
        <p class="module-description">Visão integrada de todas as informações do paciente.</p>

        <div class="module-entities">
            <strong>Entidades Requeridas:</strong> Paciente<br>
            <strong>Entidades Recomendadas:</strong> Agendamento, TransacaoFinanceira, AvaliacaoNPS, Orcamento
        </div>

        <h4>Análises Disponíveis:</h4>
        <div class="module-analyses">
            <div class="analysis-item">
                <div class="analysis-name">Pacientes integrados</div>
                <div class="analysis-description">Visão completa do paciente com todas as suas interações</div>
                <div class="analysis-data">Dados mínimos: Paciente.id, Paciente.nome</div>
            </div>
            <div class="analysis-item">
                <div class="analysis-name">Agendamentos integrados</div>
                <div class="analysis-description">Visão integrada de agendamentos com informações financeiras e de satisfação</div>
                <div class="analysis-data">Dados mínimos: Agendamento.id, Agendamento.data, Paciente.id</div>
            </div>
            <div class="analysis-item">
                <div class="analysis-name">Transações integradas</div>
                <div class="analysis-description">Visão integrada de transações financeiras com informações de agendamentos e pacientes</div>
                <div class="analysis-data">Dados mínimos: TransacaoFinanceira.id, TransacaoFinanceira.valor, TransacaoFinanceira.data, TransacaoFinanceira.agendamento_id</div>
            </div>
            <div class="analysis-item">
                <div class="analysis-name">Jornadas do paciente</div>
                <div class="analysis-description">Análise da jornada completa do paciente na clínica</div>
                <div class="analysis-data">Dados mínimos: Paciente.id, Paciente.nome, Agendamento.data</div>
            </div>
        </div>
    </div>
</body>
</html>

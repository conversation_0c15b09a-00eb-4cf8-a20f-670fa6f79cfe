{"file_name": "Relatorio_Avalicao_NPS.xlsx", "module": "amigocare", "row_count": 4, "column_count": 36, "columns": [{"original_name": "ID do paciente", "normalized_name": "id_do_paciente"}, {"original_name": "Código do Atendimento", "normalized_name": "codigo_do_atendimento"}, {"original_name": "Código", "normalized_name": "codigo"}, {"original_name": "Nome", "normalized_name": "nome"}, {"original_name": "VIP", "normalized_name": "vip"}, {"original_name": "NPS", "normalized_name": "nps"}, {"original_name": "<PERSON><PERSON><PERSON><PERSON>", "normalized_name": "comentario"}, {"original_name": "Data do atendimento", "normalized_name": "data_do_atendimento"}, {"original_name": "Data de envio do NPS", "normalized_name": "data_de_envio_do_nps"}, {"original_name": "Data da avaliação do NPS", "normalized_name": "data_da_avaliacao_do_nps"}, {"original_name": "CPF", "normalized_name": "cpf"}, {"original_name": "RG", "normalized_name": "rg"}, {"original_name": "CNH", "normalized_name": "cnh"}, {"original_name": "Nome do responsável", "normalized_name": "nome_do_responsavel"}, {"original_name": "CPF do responsável", "normalized_name": "cpf_do_responsavel"}, {"original_name": "Data de Nascimento", "normalized_name": "data_de_nascimento"}, {"original_name": "Email", "normalized_name": "email"}, {"original_name": "Sexo", "normalized_name": "sexo"}, {"original_name": "Observação", "normalized_name": "observacao"}, {"original_name": "Origem", "normalized_name": "origem"}, {"original_name": "<PERSON><PERSON><PERSON>", "normalized_name": "tipo_sanguineo"}, {"original_name": "<PERSON><PERSON><PERSON>", "normalized_name": "celular"}, {"original_name": "Telefone Residencial", "normalized_name": "telefone_residencial"}, {"original_name": "Telefone Comercial", "normalized_name": "telefone_comercial"}, {"original_name": "CEP", "normalized_name": "cep"}, {"original_name": "Endereço", "normalized_name": "endereco"}, {"original_name": "N°", "normalized_name": "n"}, {"original_name": "Complemento", "normalized_name": "complemento"}, {"original_name": "Bairro", "normalized_name": "bairro"}, {"original_name": "Cidade", "normalized_name": "cidade"}, {"original_name": "Estado", "normalized_name": "estado"}, {"original_name": "<PERSON><PERSON><PERSON><PERSON>", "normalized_name": "convenio"}, {"original_name": "Plano", "normalized_name": "plano"}, {"original_name": "N° matrícula", "normalized_name": "n_matricula"}, {"original_name": "Validade", "normalized_name": "validade"}, {"original_name": "Acomodação", "normalized_name": "acomodacao"}], "column_types": {"id_do_paciente": "int64", "codigo_do_atendimento": "int64", "codigo": "float64", "nome": "object", "vip": "bool", "nps": "int64", "comentario": "float64", "data_do_atendimento": "object", "data_de_envio_do_nps": "object", "data_da_avaliacao_do_nps": "object", "cpf": "float64", "rg": "float64", "cnh": "float64", "nome_do_responsavel": "float64", "cpf_do_responsavel": "float64", "data_de_nascimento": "datetime64[ns]", "email": "float64", "sexo": "float64", "observacao": "float64", "origem": "float64", "tipo_sanguineo": "float64", "celular": "object", "telefone_residencial": "float64", "telefone_comercial": "float64", "cep": "float64", "endereco": "float64", "n": "float64", "complemento": "float64", "bairro": "float64", "cidade": "float64", "estado": "float64", "convenio": "float64", "plano": "float64", "n_matricula": "float64", "validade": "datetime64[ns]", "acomodacao": "float64"}, "column_stats": {"id_do_paciente": {"min": 89130181.0, "max": 90070974.0, "mean": 89394817.0, "null_count": 0, "null_percentage": 0.0}, "codigo_do_atendimento": {"min": 151342058.0, "max": 153075414.0, "mean": 151895337.25, "null_count": 0, "null_percentage": 0.0}, "codigo": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "nome": {"unique_values": 4, "most_common": "Dr. <PERSON>", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "vip": {"unique_values": 1, "most_common": "Não", "most_common_count": 4, "null_count": 0, "null_percentage": 0.0}, "nps": {"min": 5.0, "max": 5.0, "mean": 5.0, "null_count": 0, "null_percentage": 0.0}, "comentario": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "data_do_atendimento": {"unique_values": 4, "most_common": "24/03/2025 às 18:20", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "data_de_envio_do_nps": {"unique_values": 1, "most_common": "22/04/2025 às 19:59", "most_common_count": 4, "null_count": 0, "null_percentage": 0.0}, "data_da_avaliacao_do_nps": {"unique_values": 3, "most_common": "25/03/2025 às 00:00", "most_common_count": 1, "null_count": 1, "null_percentage": 25.0}, "cpf": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "rg": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "cnh": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "nome_do_responsavel": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "cpf_do_responsavel": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "data_de_nascimento": {"unique_values": 1, "most_common": "22/04/2025", "most_common_count": 4, "null_count": 0, "null_percentage": 0.0}, "email": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "sexo": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "observacao": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "origem": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "tipo_sanguineo": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "celular": {"unique_values": 4, "most_common": "(46) 99940-2000", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "telefone_residencial": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "telefone_comercial": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "cep": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "endereco": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "n": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "complemento": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "bairro": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "cidade": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "estado": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "convenio": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "plano": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "n_matricula": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "validade": {"unique_values": 1, "most_common": "22/04/2025", "most_common_count": 4, "null_count": 0, "null_percentage": 0.0}, "acomodacao": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}}, "column_samples": {"id_do_paciente": [89224729, 89153384, 89130181, 90070974], "codigo_do_atendimento": [151342058, 153075414, 151721228, 151442649], "codigo": [], "nome": ["DR AMILTON TESTE", "MONIZE", "DRA FABIANA TESTE", "Dr. <PERSON>"], "vip": ["Não", "Não", "Não", "Não"], "nps": [5, 5, 5, 5], "comentario": [], "data_do_atendimento": ["24/03/2025 às 18:20", "28/03/2025 às 10:40", "03/04/2025 às 16:30", "27/03/2025 às 11:00"], "data_de_envio_do_nps": ["22/04/2025 às 19:59", "22/04/2025 às 19:59", "22/04/2025 às 19:59", "22/04/2025 às 19:59"], "data_da_avaliacao_do_nps": ["25/03/2025 às 00:00", "28/03/2025 às 00:00", "29/03/2025 às 00:00"], "cpf": [], "rg": [], "cnh": [], "nome_do_responsavel": [], "cpf_do_responsavel": [], "data_de_nascimento": ["22/04/2025", "22/04/2025", "22/04/2025", "22/04/2025"], "email": [], "sexo": [], "observacao": [], "origem": [], "tipo_sanguineo": [], "celular": ["(46) 99940-2000", "(18) 99790-3599", "(11) 97436-5636", "(11) 99739-1665"], "telefone_residencial": [], "telefone_comercial": [], "cep": [], "endereco": [], "n": [], "complemento": [], "bairro": [], "cidade": [], "estado": [], "convenio": [], "plano": [], "n_matricula": [], "validade": ["22/04/2025", "22/04/2025", "22/04/2025", "22/04/2025"], "acomodacao": []}, "potential_keys": ["id_do_paciente", "codigo_do_atendimento", "codigo", "nome", "data_do_atendimento", "celular", "telefone_residencial", "cidade", "validade"], "potential_relationships": [{"column": "id_do_paciente", "possible_related_entity": "do_paciente"}, {"column": "codigo_do_atendimento", "possible_related_entity": "do_atendimento"}], "analyzed_at": "2025-04-23T23:41:59.908602"}
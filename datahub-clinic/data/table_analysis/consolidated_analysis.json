[{"file_name": "relatorio_caixa_banco.xlsx", "module": "financeiro", "row_count": 32, "column_count": 26, "columns": [{"original_name": "Data de vencimento", "normalized_name": "data_de_vencimento"}, {"original_name": "Data de pagamento", "normalized_name": "data_de_pagamento"}, {"original_name": "Competência", "normalized_name": "competencia"}, {"original_name": "Tipo", "normalized_name": "tipo"}, {"original_name": "Pago a / Recebido de", "normalized_name": "pago_a_recebido_de"}, {"original_name": "Categoria", "normalized_name": "categoria"}, {"original_name": "Classificação", "normalized_name": "classificacao"}, {"original_name": "Descrição", "normalized_name": "descricao"}, {"original_name": "Forma de Pagamento", "normalized_name": "forma_de_pagamento"}, {"original_name": "Observação", "normalized_name": "observacao"}, {"original_name": "Nº documento", "normalized_name": "nº_documento"}, {"original_name": "Nº transação", "normalized_name": "nº_transacao"}, {"original_name": "Tag", "normalized_name": "tag"}, {"original_name": "Centro de custo", "normalized_name": "centro_de_custo"}, {"original_name": "Unidade", "normalized_name": "unidade"}, {"original_name": "Banco", "normalized_name": "banco"}, {"original_name": "Valor Original R$", "normalized_name": "valor_original_r"}, {"original_name": "PIS", "normalized_name": "pis"}, {"original_name": "COFINS", "normalized_name": "cofins"}, {"original_name": "CSLL", "normalized_name": "csll"}, {"original_name": "IR", "normalized_name": "ir"}, {"original_name": "ISS", "normalized_name": "iss"}, {"original_name": "INSS", "normalized_name": "inss"}, {"original_name": "Valor Líquido R$", "normalized_name": "valor_liquido_r"}, {"original_name": "Nota Emitida", "normalized_name": "nota_emitida"}, {"original_name": "Recibo Emitido", "normalized_name": "recibo_emitido"}], "column_types": {"data_de_vencimento": "object", "data_de_pagamento": "object", "competencia": "object", "tipo": "object", "pago_a_recebido_de": "object", "categoria": "object", "classificacao": "object", "descricao": "object", "forma_de_pagamento": "object", "observacao": "object", "nº_documento": "object", "nº_transacao": "object", "tag": "object", "centro_de_custo": "object", "unidade": "object", "banco": "object", "valor_original_r": "float64", "pis": "object", "cofins": "object", "csll": "object", "ir": "object", "iss": "object", "inss": "object", "valor_liquido_r": "float64", "nota_emitida": "bool", "recibo_emitido": "bool"}, "column_stats": {"data_de_vencimento": {"unique_values": 10, "most_common": "02/04/2025", "most_common_count": 7, "null_count": 0, "null_percentage": 0.0}, "data_de_pagamento": {"unique_values": 10, "most_common": "03/04/2025", "most_common_count": 5, "null_count": 0, "null_percentage": 0.0}, "competencia": {"unique_values": 3, "most_common": "04/2025", "most_common_count": 21, "null_count": 0, "null_percentage": 0.0}, "tipo": {"unique_values": 3, "most_common": "Entrada", "most_common_count": 27, "null_count": 0, "null_percentage": 0.0}, "pago_a_recebido_de": {"unique_values": 25, "most_common": "<PERSON><PERSON>", "most_common_count": 4, "null_count": 0, "null_percentage": 0.0}, "categoria": {"unique_values": 6, "most_common": "RECEITA OPERACIONAL", "most_common_count": 26, "null_count": 0, "null_percentage": 0.0}, "classificacao": {"unique_values": 6, "most_common": "Prestação de Serviço", "most_common_count": 26, "null_count": 0, "null_percentage": 0.0}, "descricao": {"unique_values": 20, "most_common": "Atendimento - Consulta - Amigo tech", "most_common_count": 7, "null_count": 0, "null_percentage": 0.0}, "forma_de_pagamento": {"unique_values": 4, "most_common": "Pix", "most_common_count": 20, "null_count": 0, "null_percentage": 0.0}, "observacao": {"unique_values": 1, "most_common": "-", "most_common_count": 32, "null_count": 0, "null_percentage": 0.0}, "nº_documento": {"unique_values": 3, "most_common": "-", "most_common_count": 30, "null_count": 0, "null_percentage": 0.0}, "nº_transacao": {"unique_values": 1, "most_common": "-", "most_common_count": 32, "null_count": 0, "null_percentage": 0.0}, "tag": {"unique_values": 1, "most_common": "-", "most_common_count": 32, "null_count": 0, "null_percentage": 0.0}, "centro_de_custo": {"unique_values": 1, "most_common": "-", "most_common_count": 32, "null_count": 0, "null_percentage": 0.0}, "unidade": {"unique_values": 5, "most_common": "BRASILIA", "most_common_count": 15, "null_count": 0, "null_percentage": 0.0}, "banco": {"unique_values": 9, "most_common": "SANTANDER 1-1", "most_common_count": 13, "null_count": 0, "null_percentage": 0.0}, "valor_original_r": {"min": 2.5, "max": 20000.0, "mean": 1738.3178125, "null_count": 0, "null_percentage": 0.0}, "pis": {"unique_values": 1, "most_common": "-", "most_common_count": 32, "null_count": 0, "null_percentage": 0.0}, "cofins": {"unique_values": 1, "most_common": "-", "most_common_count": 32, "null_count": 0, "null_percentage": 0.0}, "csll": {"unique_values": 1, "most_common": "-", "most_common_count": 32, "null_count": 0, "null_percentage": 0.0}, "ir": {"unique_values": 1, "most_common": "-", "most_common_count": 32, "null_count": 0, "null_percentage": 0.0}, "iss": {"unique_values": 1, "most_common": "-", "most_common_count": 32, "null_count": 0, "null_percentage": 0.0}, "inss": {"unique_values": 1, "most_common": "-", "most_common_count": 32, "null_count": 0, "null_percentage": 0.0}, "valor_liquido_r": {"min": -2348.0, "max": 20000.0, "mean": 1454.0678125, "null_count": 0, "null_percentage": 0.0}, "nota_emitida": {"unique_values": 1, "most_common": "Não", "most_common_count": 32, "null_count": 0, "null_percentage": 0.0}, "recibo_emitido": {"unique_values": 2, "most_common": "Não", "most_common_count": 30, "null_count": 0, "null_percentage": 0.0}}, "column_samples": {"data_de_vencimento": ["07/04/2025", "01/04/2025", "02/04/2025", "02/04/2025", "01/04/2025"], "data_de_pagamento": ["16/04/2025", "08/04/2025", "10/04/2025", "07/04/2025", "03/04/2025"], "competencia": ["04/2025", "04/2025", "-", "03/2025", "04/2025"], "tipo": ["Entrada", "Entrada", "Entrada", "Entrada", "Entrada"], "pago_a_recebido_de": ["EDP", "DRA CAROLINA TESTE", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "categoria": ["RECEITA OPERACIONAL", "RECEITA OPERACIONAL", "RECEITA OPERACIONAL", "RECEITA OPERACIONAL", "RECEITA OPERACIONAL"], "classificacao": ["Prestação de Serviço", "Prestação de Serviço", "Prestação de Serviço", "-", "Ad<PERSON><PERSON><PERSON>"], "descricao": ["Atendimento - CONSULTA  COM CARDIOLOGISTA", "Atendimento (Sessão) - Fisioterapia", "<PERSON><PERSON> inicial", "Orçamento #1578045", "Orçamento #1568236"], "forma_de_pagamento": ["Pix", "Pix", "Pix", "Cartão", "Pix"], "observacao": ["-", "-", "-", "-", "-"], "nº_documento": ["-", "-", "-", "-", "-"], "nº_transacao": ["-", "-", "-", "-", "-"], "tag": ["-", "-", "-", "-", "-"], "centro_de_custo": ["-", "-", "-", "-", "-"], "unidade": ["-", "BRASILIA", "BRASILIA", "BRASILIA", "BRASILIA"], "banco": ["SANTANDER 1-1", "Caixinha 0", "SANTANDER 1-1", "BANCO SAFRA 10187840-0", "NEON 78799-7"], "valor_original_r": [2348.0, 300.0, 20000.0, 1100.0, 300.0], "pis": ["-", "-", "-", "-", "-"], "cofins": ["-", "-", "-", "-", "-"], "csll": ["-", "-", "-", "-", "-"], "ir": ["-", "-", "-", "-", "-"], "iss": ["-", "-", "-", "-", "-"], "inss": ["-", "-", "-", "-", "-"], "valor_liquido_r": [160.0, 20000.0, 300.0, 4000.0, -1100.0], "nota_emitida": ["Não", "Não", "Não", "Não", "Não"], "recibo_emitido": ["Não", "Não", "Não", "Não", "Não"]}, "potential_keys": ["pago_a_recebido_de", "unidade", "valor_liquido_r", "nota_emitida", "recibo_emitido"], "potential_relationships": [], "analyzed_at": "2025-04-23T23:41:59.640309"}, {"file_name": "relatorio_fechamento_caixa_diário.xlsx", "module": "financeiro", "row_count": 85, "column_count": 14, "columns": [{"original_name": "Data Transação", "normalized_name": "data_transacao"}, {"original_name": "Data Atendimento", "normalized_name": "data_atendimento"}, {"original_name": "Paciente", "normalized_name": "paciente"}, {"original_name": "Executante", "normalized_name": "executante"}, {"original_name": "Sol. Primário", "normalized_name": "sol_primario"}, {"original_name": "Sol. Secundário", "normalized_name": "sol_secundario"}, {"original_name": "Editor financeiro", "normalized_name": "editor_financeiro"}, {"original_name": "Qtd / Procedimento", "normalized_name": "qtd_procedimento"}, {"original_name": "Forma de Pagamento", "normalized_name": "forma_de_pagamento"}, {"original_name": "NSU(DOC/CV/ID)", "normalized_name": "nsu_doc_cv_id"}, {"original_name": "Orçamento", "normalized_name": "orcamento"}, {"original_name": "Nº Orçamento", "normalized_name": "nº_orcamento"}, {"original_name": "Observação", "normalized_name": "observacao"}, {"original_name": "Valor R$", "normalized_name": "valor_r"}], "column_types": {"data_transacao": "object", "data_atendimento": "datetime64[ns]", "paciente": "object", "executante": "object", "sol_primario": "object", "sol_secundario": "object", "editor_financeiro": "object", "qtd_procedimento": "object", "forma_de_pagamento": "object", "nsu_doc_cv_id": "float64", "orcamento": "bool", "nº_orcamento": "object", "observacao": "float64", "valor_r": "float64"}, "column_stats": {"data_transacao": {"unique_values": 18, "most_common": "07/04/2025", "most_common_count": 15, "null_count": 0, "null_percentage": 0.0}, "data_atendimento": {"unique_values": 18, "most_common": "07/04/2025", "most_common_count": 15, "null_count": 0, "null_percentage": 0.0}, "paciente": {"unique_values": 51, "most_common": "<PERSON><PERSON>", "most_common_count": 5, "null_count": 0, "null_percentage": 0.0}, "executante": {"unique_values": 18, "most_common": "Orçamentos Fechados", "most_common_count": 27, "null_count": 0, "null_percentage": 0.0}, "sol_primario": {"unique_values": 1, "most_common": NaN, "most_common_count": 83, "null_count": 83, "null_percentage": 97.6470588235294}, "sol_secundario": {"unique_values": 2, "most_common": NaN, "most_common_count": 83, "null_count": 83, "null_percentage": 97.6470588235294}, "editor_financeiro": {"unique_values": 17, "most_common": "Equipe Amigo - Caio <PERSON>", "most_common_count": 23, "null_count": 0, "null_percentage": 0.0}, "qtd_procedimento": {"unique_values": 26, "most_common": "1 - Consulta amigo tech - teste", "most_common_count": 14, "null_count": 10, "null_percentage": 11.76470588235294}, "forma_de_pagamento": {"unique_values": 17, "most_common": "Pix", "most_common_count": 28, "null_count": 0, "null_percentage": 0.0}, "nsu_doc_cv_id": {"min": 649208.0, "max": 654165465.0, "mean": 171820117.75, "null_count": 81, "null_percentage": 95.29411764705881}, "orcamento": {"unique_values": 2, "most_common": "Não", "most_common_count": 58, "null_count": 0, "null_percentage": 0.0}, "nº_orcamento": {"unique_values": 22, "most_common": NaN, "most_common_count": 58, "null_count": 58, "null_percentage": 68.23529411764706}, "observacao": {"min": null, "max": null, "mean": null, "null_count": 85, "null_percentage": 100.0}, "valor_r": {"min": 0.0, "max": 20000.0, "mean": 1738.5552941176472, "null_count": 0, "null_percentage": 0.0}}, "column_samples": {"data_transacao": ["01/04/2025", "16/04/2025", "21/04/2025", "10/04/2025", "01/04/2025"], "data_atendimento": ["08/04/2025", "01/04/2025", "21/04/2025", "07/04/2025", "14/04/2025"], "paciente": ["<PERSON>", "caio menezes", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "executante": ["Equipe Amigo - Caio <PERSON>", "Orçamentos Fechados", "Equipe Amigo - Ana Victoria <PERSON> Almeida", "Orçamentos Fechados", "Equipe Amigo - BRUNO LIMA"], "sol_primario": ["Equipe Amigo - Ana Victoria <PERSON> Almeida", "Equipe Amigo - Ana Victoria <PERSON> Almeida"], "sol_secundario": ["Equipe Amigo - Al<PERSON>", "Equipe Amigo - Giulia Pedrosa 2"], "editor_financeiro": ["Equipe Amigo - BRUNO LIMA", "Equipe AMIGO - Ayrton Neto", "Equipe <PERSON>igo - <PERSON>", "EQUIPE AMIGO - <PERSON><PERSON><PERSON><PERSON>", "Equipe Amigo - Caio <PERSON>"], "qtd_procedimento": ["1 - Sessão para assistência fisioterapêutica ambulatorial ao paciente com disfunção decorrente de alterações do sistema músculo-esquelético, 1 - Sessão para assistência fisioterapêutica ambulatorial para alterações inflamatórias e ou degenerativas do aparelho genito-urinário e reprodutor", "1 - FIO PDO - <PERSON><PERSON><PERSON>, 1 - <PERSON><PERSON><PERSON> MALAR, 1 - <PERSON><PERSON>", "1 - Consulta - 10101012", "1 - Consulta - 10101012", "1 - 1ª Sessão de psicoterapia"], "forma_de_pagamento": ["<PERSON><PERSON><PERSON>", "Cartão 1x", "Pix", "BRADESCO", "Pendente"], "nsu_doc_cv_id": [649208.0, 31231231.0, 654165465.0, 1234567.0], "orcamento": ["Não", "Não", "Não", "Não", "Não"], "nº_orcamento": ["OR1600015", "OR1577012", "OR1601790", "OR1579959", "OR1576357"], "observacao": [], "valor_r": [300.0, 60.0, 90.0, 80.0, 500.0]}, "potential_keys": ["nsu_doc_cv_id"], "potential_relationships": [{"column": "nsu_doc_cv_id", "possible_related_entity": "nsu_doc_cv"}], "analyzed_at": "2025-04-23T23:41:59.665055"}, {"file_name": "relatorio_caixa_banco2.xlsx", "module": "financeiro", "row_count": 32, "column_count": 12, "columns": [{"original_name": "Data de pagamento", "normalized_name": "data_de_pagamento"}, {"original_name": "Competencia", "normalized_name": "competencia"}, {"original_name": "Tipo", "normalized_name": "tipo"}, {"original_name": "Pago a / Recebido de", "normalized_name": "pago_a_recebido_de"}, {"original_name": "Categoria", "normalized_name": "categoria"}, {"original_name": "Classificação", "normalized_name": "classificacao"}, {"original_name": "Descrição", "normalized_name": "descricao"}, {"original_name": "Forma de Pagamento", "normalized_name": "forma_de_pagamento"}, {"original_name": "Banco", "normalized_name": "banco"}, {"original_name": "Valor R$", "normalized_name": "valor_r"}, {"original_name": "Nota emitida", "normalized_name": "nota_emitida"}, {"original_name": "Recibo emitido", "normalized_name": "recibo_emitido"}], "column_types": {"data_de_pagamento": "object", "competencia": "object", "tipo": "object", "pago_a_recebido_de": "object", "categoria": "object", "classificacao": "object", "descricao": "object", "forma_de_pagamento": "object", "banco": "object", "valor_r": "float64", "nota_emitida": "bool", "recibo_emitido": "bool"}, "column_stats": {"data_de_pagamento": {"unique_values": 10, "most_common": "03/04/2025", "most_common_count": 5, "null_count": 0, "null_percentage": 0.0}, "competencia": {"unique_values": 3, "most_common": "04/2025", "most_common_count": 21, "null_count": 0, "null_percentage": 0.0}, "tipo": {"unique_values": 3, "most_common": "Entrada", "most_common_count": 27, "null_count": 0, "null_percentage": 0.0}, "pago_a_recebido_de": {"unique_values": 25, "most_common": "<PERSON><PERSON>", "most_common_count": 4, "null_count": 0, "null_percentage": 0.0}, "categoria": {"unique_values": 6, "most_common": "RECEITA OPERACIONAL", "most_common_count": 26, "null_count": 0, "null_percentage": 0.0}, "classificacao": {"unique_values": 6, "most_common": "Prestação de Serviço", "most_common_count": 26, "null_count": 0, "null_percentage": 0.0}, "descricao": {"unique_values": 20, "most_common": "Atendimento - Consulta - Amigo tech", "most_common_count": 7, "null_count": 0, "null_percentage": 0.0}, "forma_de_pagamento": {"unique_values": 4, "most_common": "Pix", "most_common_count": 20, "null_count": 0, "null_percentage": 0.0}, "banco": {"unique_values": 9, "most_common": "SANTANDER 1-1", "most_common_count": 13, "null_count": 0, "null_percentage": 0.0}, "valor_r": {"min": -2348.0, "max": 20000.0, "mean": 1454.0678125, "null_count": 0, "null_percentage": 0.0}, "nota_emitida": {"unique_values": 1, "most_common": "Não", "most_common_count": 32, "null_count": 0, "null_percentage": 0.0}, "recibo_emitido": {"unique_values": 2, "most_common": "Não", "most_common_count": 30, "null_count": 0, "null_percentage": 0.0}}, "column_samples": {"data_de_pagamento": ["08/04/2025", "03/04/2025", "16/04/2025", "03/04/2025", "03/04/2025"], "competencia": ["04/2025", "-", "-", "04/2025", "04/2025"], "tipo": ["Entrada", "Entrada", "Entrada", "Entrada", "<PERSON><PERSON><PERSON>"], "pago_a_recebido_de": ["bradesco 1-1", "MARINA HAZIN", "<PERSON>", "BANCO DO BRASIL 103291", "EDP"], "categoria": ["RECEITA OPERACIONAL", "RECEITA OPERACIONAL", "RECEITA OPERACIONAL", "*******.01 - UTILIDADES E SERVIÇOS DE TERCEIROS", "*******.07 - DESPESAS COM PESSOAL"], "classificacao": ["Prestação de Serviço", "ACERTO DE CAIXA TESTE", "Prestação de Serviço", "Prestação de Serviço", "Prestação de Serviço"], "descricao": ["Atendimento (Sessão) - Sessao de Fisioterapia", "Atendimento (Sessão) - Fisioterapia", "Pré-Pagamento #1568232", "Pré-Pagamento #1568236", "Atendimento - CONSULTA  COM CARDIOLOGISTA"], "forma_de_pagamento": ["<PERSON><PERSON><PERSON>", "Pix", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Pix"], "banco": ["Caixa", "SANTANDER 1-1", "NEON 78799-7", "SANTANDER 1-1", "SANTANDER 1-1"], "valor_r": [300.0, 500.0, -100.0, 100.0, 356.5], "nota_emitida": ["Não", "Não", "Não", "Não", "Não"], "recibo_emitido": ["Não", "Não", "Não", "Não", "Não"]}, "potential_keys": ["pago_a_recebido_de", "nota_emitida", "recibo_emitido"], "potential_relationships": [], "analyzed_at": "2025-04-23T23:41:59.679045"}, {"file_name": "relatorio_contas_a_pagar.xlsx", "module": "financeiro", "row_count": 113, "column_count": 22, "columns": [{"original_name": "Data de vencimento", "normalized_name": "data_de_vencimento"}, {"original_name": "Competência", "normalized_name": "competencia"}, {"original_name": "Pagar a", "normalized_name": "pagar_a"}, {"original_name": "Categoria", "normalized_name": "categoria"}, {"original_name": "Classificação", "normalized_name": "classificacao"}, {"original_name": "Descrição", "normalized_name": "descricao"}, {"original_name": "Forma de Pagamento", "normalized_name": "forma_de_pagamento"}, {"original_name": "Observação", "normalized_name": "observacao"}, {"original_name": "Nº documento", "normalized_name": "nº_documento"}, {"original_name": "Tag", "normalized_name": "tag"}, {"original_name": "Centro de custo", "normalized_name": "centro_de_custo"}, {"original_name": "Unidade", "normalized_name": "unidade"}, {"original_name": "Valor Original R$", "normalized_name": "valor_original_r"}, {"original_name": "PIS", "normalized_name": "pis"}, {"original_name": "COFINS", "normalized_name": "cofins"}, {"original_name": "CSLL", "normalized_name": "csll"}, {"original_name": "IR", "normalized_name": "ir"}, {"original_name": "ISS", "normalized_name": "iss"}, {"original_name": "INSS", "normalized_name": "inss"}, {"original_name": "Valor Líquido R$", "normalized_name": "valor_liquido_r"}, {"original_name": "Nota Emitida", "normalized_name": "nota_emitida"}, {"original_name": "Recibo Emitido", "normalized_name": "recibo_emitido"}], "column_types": {"data_de_vencimento": "object", "competencia": "datetime64[ns]", "pagar_a": "object", "categoria": "object", "classificacao": "object", "descricao": "object", "forma_de_pagamento": "object", "observacao": "object", "nº_documento": "object", "tag": "object", "centro_de_custo": "object", "unidade": "object", "valor_original_r": "float64", "pis": "object", "cofins": "object", "csll": "object", "ir": "object", "iss": "object", "inss": "object", "valor_liquido_r": "float64", "nota_emitida": "bool", "recibo_emitido": "bool"}, "column_stats": {"data_de_vencimento": {"unique_values": 24, "most_common": "10/04/2025", "most_common_count": 29, "null_count": 0, "null_percentage": 0.0}, "competencia": {"unique_values": 3, "most_common": "04/2025", "most_common_count": 101, "null_count": 0, "null_percentage": 0.0}, "pagar_a": {"unique_values": 79, "most_common": "Caesb", "most_common_count": 8, "null_count": 0, "null_percentage": 0.0}, "categoria": {"unique_values": 26, "most_common": "*******.01 - UTILIDADES E SERVIÇOS DE TERCEIROS", "most_common_count": 25, "null_count": 0, "null_percentage": 0.0}, "classificacao": {"unique_values": 25, "most_common": "Energia elétrica", "most_common_count": 25, "null_count": 0, "null_percentage": 0.0}, "descricao": {"unique_values": 72, "most_common": "Pagamento de agua", "most_common_count": 8, "null_count": 0, "null_percentage": 0.0}, "forma_de_pagamento": {"unique_values": 1, "most_common": "-", "most_common_count": 113, "null_count": 0, "null_percentage": 0.0}, "observacao": {"unique_values": 1, "most_common": "-", "most_common_count": 113, "null_count": 0, "null_percentage": 0.0}, "nº_documento": {"unique_values": 18, "most_common": "-", "most_common_count": 89, "null_count": 0, "null_percentage": 0.0}, "tag": {"unique_values": 2, "most_common": "-", "most_common_count": 112, "null_count": 0, "null_percentage": 0.0}, "centro_de_custo": {"unique_values": 28, "most_common": "-", "most_common_count": 81, "null_count": 0, "null_percentage": 0.0}, "unidade": {"unique_values": 4, "most_common": "Morumbi", "most_common_count": 61, "null_count": 0, "null_percentage": 0.0}, "valor_original_r": {"min": 10.0, "max": 20000.0, "mean": 2866.4554867256634, "null_count": 0, "null_percentage": 0.0}, "pis": {"unique_values": 1, "most_common": "-", "most_common_count": 113, "null_count": 0, "null_percentage": 0.0}, "cofins": {"unique_values": 1, "most_common": "-", "most_common_count": 113, "null_count": 0, "null_percentage": 0.0}, "csll": {"unique_values": 1, "most_common": "-", "most_common_count": 113, "null_count": 0, "null_percentage": 0.0}, "ir": {"unique_values": 1, "most_common": "-", "most_common_count": 113, "null_count": 0, "null_percentage": 0.0}, "iss": {"unique_values": 1, "most_common": "-", "most_common_count": 113, "null_count": 0, "null_percentage": 0.0}, "inss": {"unique_values": 1, "most_common": "-", "most_common_count": 113, "null_count": 0, "null_percentage": 0.0}, "valor_liquido_r": {"min": -20000.0, "max": -10.0, "mean": -2121.6767256637168, "null_count": 0, "null_percentage": 0.0}, "nota_emitida": {"unique_values": 1, "most_common": "Não", "most_common_count": 113, "null_count": 0, "null_percentage": 0.0}, "recibo_emitido": {"unique_values": 1, "most_common": "Não", "most_common_count": 113, "null_count": 0, "null_percentage": 0.0}}, "column_samples": {"data_de_vencimento": ["15/04/2025", "02/04/2025", "26/04/2025", "18/04/2025", "28/04/2025"], "competencia": ["04/2025", "04/2025", "04/2025", "04/2025", "04/2025"], "pagar_a": ["Condominio", "Condominio", "NET", "Equipe Amigo - Caio <PERSON>", "ALGAR"], "categoria": ["*******.05 - DESPESAS ADMINISTRATIVAS", "*******.02 - UTILIDADES E SERVIÇOS DE TERCEIROS", "*******.17 - DESPESAS ADMINISTRATIVAS", "*******.02 - UTILIDADES E SERVIÇOS DE TERCEIROS", "*******.06 - DESPESAS ADMINISTRATIVAS"], "classificacao": ["Suporte Técnico", "<PERSON>ass<PERSON>", "GEL", "Material de escritório", "Con<PERSON><PERSON><PERSON>"], "descricao": ["PGT LUZ", "condominio", "condominio dsbkfds", "Pagamento de agua", "CURATIVO"], "forma_de_pagamento": ["-", "-", "-", "-", "-"], "observacao": ["-", "-", "-", "-", "-"], "nº_documento": ["-", "-", "SDAS", "-", "-"], "tag": ["-", "-", "-", "-", "-"], "centro_de_custo": ["-", "-", "-", "Centro de Diagnósticos (40%)", "-"], "unidade": ["Morumbi", "-", "Morumbi", "Morumbi", "Morumbi"], "valor_original_r": [200.0, 500.0, 6000.0, 10000.0, 113.0], "pis": ["-", "-", "-", "-", "-"], "cofins": ["-", "-", "-", "-", "-"], "csll": ["-", "-", "-", "-", "-"], "ir": ["-", "-", "-", "-", "-"], "iss": ["-", "-", "-", "-", "-"], "inss": ["-", "-", "-", "-", "-"], "valor_liquido_r": [-500.0, -150.0, -833.34, -1000.0, -3000.0], "nota_emitida": ["Não", "Não", "Não", "Não", "Não"], "recibo_emitido": ["Não", "Não", "Não", "Não", "Não"]}, "potential_keys": ["unidade", "valor_liquido_r", "nota_emitida", "recibo_emitido"], "potential_relationships": [], "analyzed_at": "2025-04-23T23:41:59.710036"}, {"file_name": "relatorio_fluxo_de_caixa.xlsx", "module": "financeiro", "row_count": 28, "column_count": 13, "columns": [{"original_name": "Unnamed: 0", "normalized_name": "unnamed_0"}, {"original_name": "jan/25", "normalized_name": "jan_25"}, {"original_name": "fev/25", "normalized_name": "fev_25"}, {"original_name": "mar/25", "normalized_name": "mar_25"}, {"original_name": "abr/25", "normalized_name": "abr_25"}, {"original_name": "mai/25", "normalized_name": "mai_25"}, {"original_name": "jun/25", "normalized_name": "jun_25"}, {"original_name": "jul/25", "normalized_name": "jul_25"}, {"original_name": "ago/25", "normalized_name": "ago_25"}, {"original_name": "set/25", "normalized_name": "set_25"}, {"original_name": "out/25", "normalized_name": "out_25"}, {"original_name": "nov/25", "normalized_name": "nov_25"}, {"original_name": "dez/25", "normalized_name": "dez_25"}], "column_types": {"unnamed_0": "object", "jan_25": "float64", "fev_25": "float64", "mar_25": "float64", "abr_25": "float64", "mai_25": "float64", "jun_25": "float64", "jul_25": "float64", "ago_25": "float64", "set_25": "float64", "out_25": "float64", "nov_25": "float64", "dez_25": "float64"}, "column_stats": {"unnamed_0": {"unique_values": 28, "most_common": "Entradas operacionais de caixa", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "jan_25": {"min": -1350.0, "max": 13002138.77, "mean": 934560.0264285713, "null_count": 0, "null_percentage": 0.0}, "fev_25": {"min": -26707.0, "max": 13081808.07, "mean": 945796.1907142857, "null_count": 0, "null_percentage": 0.0}, "mar_25": {"min": -18186.0, "max": 13131434.07, "mean": 945049.005, "null_count": 0, "null_percentage": 0.0}, "abr_25": {"min": -4548.0, "max": 13176624.24, "mean": 947643.1842857143, "null_count": 0, "null_percentage": 0.0}, "mai_25": {"min": 0.0, "max": 13176624.24, "mean": 941187.4457142857, "null_count": 0, "null_percentage": 0.0}, "jun_25": {"min": 0.0, "max": 13176624.24, "mean": 941187.4457142857, "null_count": 0, "null_percentage": 0.0}, "jul_25": {"min": 0.0, "max": 13176624.24, "mean": 941187.4457142857, "null_count": 0, "null_percentage": 0.0}, "ago_25": {"min": 0.0, "max": 13176624.24, "mean": 941187.4457142857, "null_count": 0, "null_percentage": 0.0}, "set_25": {"min": 0.0, "max": 13176624.24, "mean": 941187.4457142857, "null_count": 0, "null_percentage": 0.0}, "out_25": {"min": 0.0, "max": 13176624.24, "mean": 941187.4457142857, "null_count": 0, "null_percentage": 0.0}, "nov_25": {"min": 0.0, "max": 13176624.24, "mean": 941187.4457142857, "null_count": 0, "null_percentage": 0.0}, "dez_25": {"min": 0.0, "max": 13176624.24, "mean": 941187.4457142857, "null_count": 0, "null_percentage": 0.0}}, "column_samples": {"unnamed_0": ["-      PROVISÃO DE RECEITA FUTURA", "-  DESPESAS TRIBUTÁRIAS", "Saldo final", "-      Outras despesas", "-  DESPESAS ADMINISTRATIVAS"], "jan_25": [0.0, 0.0, 12961287.969999999, 3600.0, 38100.8], "fev_25": [79669.3, -10150.0, 8100.0, 79669.3, 0.0], "mar_25": [-454.0, 17812.0, 0.0, 0.0, 13131434.07], "abr_25": [49578.17, -100.0, 0.0, -2448.0, -2348.0], "mai_25": [0.0, 0.0, 0.0, 0.0, 0.0], "jun_25": [13176624.24, 0.0, 0.0, 0.0, 0.0], "jul_25": [13176624.24, 0.0, 0.0, 0.0, 0.0], "ago_25": [0.0, 0.0, 0.0, 0.0, 0.0], "set_25": [0.0, 0.0, 0.0, 0.0, 0.0], "out_25": [0.0, 13176624.24, 0.0, 0.0, 0.0], "nov_25": [0.0, 0.0, 0.0, 0.0, 0.0], "dez_25": [0.0, 0.0, 0.0, 0.0, 0.0]}, "potential_keys": ["unnamed_0"], "potential_relationships": [], "analyzed_at": "2025-04-23T23:41:59.722667"}, {"file_name": "relatorio_de_repasse.xlsx", "module": "financeiro", "row_count": 1, "column_count": 26, "columns": [{"original_name": "Data Atend", "normalized_name": "data_atend"}, {"original_name": "Data Pagto.", "normalized_name": "data_pagto"}, {"original_name": "Cód. atendimento", "normalized_name": "cod_atendimento"}, {"original_name": "Cód. financeiro", "normalized_name": "cod_financeiro"}, {"original_name": "Unidade", "normalized_name": "unidade"}, {"original_name": "Paciente", "normalized_name": "paciente"}, {"original_name": "Forma de Pagamento", "normalized_name": "forma_de_pagamento"}, {"original_name": "Profissional Executante", "normalized_name": "profissional_executante"}, {"original_name": "Grau part.", "normalized_name": "grau_part"}, {"original_name": "Profissional Solicitante", "normalized_name": "profissional_solicitante"}, {"original_name": "Procedimento/Matmed", "normalized_name": "procedimento_matmed"}, {"original_name": "Tipo", "normalized_name": "tipo"}, {"original_name": "<PERSON><PERSON><PERSON>", "normalized_name": "<PERSON><PERSON><PERSON>"}, {"original_name": "<PERSON><PERSON>a", "normalized_name": "glosa"}, {"original_name": "Desconto", "normalized_name": "desconto"}, {"original_name": "Líquido", "normalized_name": "liquido"}, {"original_name": "Repasse Executante", "normalized_name": "repasse_executante"}, {"original_name": "Percentual Repasse Executante", "normalized_name": "percentual_repasse_executante"}, {"original_name": "Repasse <PERSON>", "normalized_name": "repasse_solicitante"}, {"original_name": "Percentual Repasse Solicitante", "normalized_name": "percentual_repasse_solicitante"}, {"original_name": "Regra Repasse Executante", "normalized_name": "regra_repasse_executante"}, {"original_name": "Regra Repasse Solicitante", "normalized_name": "regra_repasse_solicitante"}, {"original_name": "Pagto. Repasse executante", "normalized_name": "pagto_repasse_executante"}, {"original_name": "Pagto. Repasse solicitante", "normalized_name": "pagto_repasse_solicitante"}, {"original_name": "Regra de repasse", "normalized_name": "regra_de_repasse"}, {"original_name": "Pagto. Repasse", "normalized_name": "pagto_repasse"}], "column_types": {"data_atend": "datetime64[ns]", "data_pagto": "datetime64[ns]", "cod_atendimento": "int64", "cod_financeiro": "int64", "unidade": "object", "paciente": "object", "forma_de_pagamento": "object", "profissional_executante": "object", "grau_part": "object", "profissional_solicitante": "float64", "procedimento_matmed": "object", "tipo": "object", "faturado": "float64", "glosa": "int64", "desconto": "int64", "liquido": "float64", "repasse_executante": "int64", "percentual_repasse_executante": "float64", "repasse_solicitante": "int64", "percentual_repasse_solicitante": "float64", "regra_repasse_executante": "float64", "regra_repasse_solicitante": "float64", "pagto_repasse_executante": "float64", "pagto_repasse_solicitante": "float64", "regra_de_repasse": "bool", "pagto_repasse": "float64"}, "column_stats": {"data_atend": {"unique_values": 1, "most_common": "23/04/2025", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "data_pagto": {"unique_values": 1, "most_common": "17/04/2025", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "cod_atendimento": {"min": 155686103.0, "max": 155686103.0, "mean": 155686103.0, "null_count": 0, "null_percentage": 0.0}, "cod_financeiro": {"min": 170684150.0, "max": 170684150.0, "mean": 170684150.0, "null_count": 0, "null_percentage": 0.0}, "unidade": {"unique_values": 1, "most_common": "Morumbi", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "paciente": {"unique_values": 1, "most_common": "<PERSON>", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "forma_de_pagamento": {"unique_values": 1, "most_common": "Pix", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "profissional_executante": {"unique_values": 1, "most_common": "Equipe Amigo - BRUNO LIMA", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "grau_part": {"unique_values": 1, "most_common": "Executante", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "profissional_solicitante": {"min": null, "max": null, "mean": null, "null_count": 1, "null_percentage": 100.0}, "procedimento_matmed": {"unique_values": 1, "most_common": "BOTOX MALAR", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "tipo": {"unique_values": 1, "most_common": "Procedimento", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "faturado": {"min": 952.38, "max": 952.38, "mean": 952.38, "null_count": 0, "null_percentage": 0.0}, "glosa": {"min": 0.0, "max": 0.0, "mean": 0.0, "null_count": 0, "null_percentage": 0.0}, "desconto": {"min": 0.0, "max": 0.0, "mean": 0.0, "null_count": 0, "null_percentage": 0.0}, "liquido": {"min": 952.38, "max": 952.38, "mean": 952.38, "null_count": 0, "null_percentage": 0.0}, "repasse_executante": {"min": 0.0, "max": 0.0, "mean": 0.0, "null_count": 0, "null_percentage": 0.0}, "percentual_repasse_executante": {"min": null, "max": null, "mean": null, "null_count": 1, "null_percentage": 100.0}, "repasse_solicitante": {"min": 0.0, "max": 0.0, "mean": 0.0, "null_count": 0, "null_percentage": 0.0}, "percentual_repasse_solicitante": {"min": null, "max": null, "mean": null, "null_count": 1, "null_percentage": 100.0}, "regra_repasse_executante": {"min": null, "max": null, "mean": null, "null_count": 1, "null_percentage": 100.0}, "regra_repasse_solicitante": {"min": null, "max": null, "mean": null, "null_count": 1, "null_percentage": 100.0}, "pagto_repasse_executante": {"min": null, "max": null, "mean": null, "null_count": 1, "null_percentage": 100.0}, "pagto_repasse_solicitante": {"min": null, "max": null, "mean": null, "null_count": 1, "null_percentage": 100.0}, "regra_de_repasse": {"unique_values": 1, "most_common": "Não", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "pagto_repasse": {"min": null, "max": null, "mean": null, "null_count": 1, "null_percentage": 100.0}}, "column_samples": {"data_atend": ["23/04/2025"], "data_pagto": ["17/04/2025"], "cod_atendimento": [155686103], "cod_financeiro": [170684150], "unidade": ["Morumbi"], "paciente": ["<PERSON>"], "forma_de_pagamento": ["Pix"], "profissional_executante": ["Equipe Amigo - BRUNO LIMA"], "grau_part": ["Executante"], "profissional_solicitante": [], "procedimento_matmed": ["BOTOX MALAR"], "tipo": ["Procedimento"], "faturado": [952.38], "glosa": [0], "desconto": [0], "liquido": [952.38], "repasse_executante": [0], "percentual_repasse_executante": [], "repasse_solicitante": [0], "percentual_repasse_solicitante": [], "regra_repasse_executante": [], "regra_repasse_solicitante": [], "pagto_repasse_executante": [], "pagto_repasse_solicitante": [], "regra_de_repasse": ["Não"], "pagto_repasse": []}, "potential_keys": ["data_atend", "data_pagto", "cod_atendimento", "cod_financeiro", "unidade", "paciente", "forma_de_pagamento", "profissional_executante", "grau_part", "procedimento_matmed", "tipo", "<PERSON><PERSON><PERSON>", "glosa", "desconto", "liquido", "repasse_executante", "repasse_solicitante", "regra_de_repasse"], "potential_relationships": [], "analyzed_at": "2025-04-23T23:41:59.736963"}, {"file_name": "relatorio_fechamento_de_caixa_novo.xlsx", "module": "financeiro", "row_count": 8, "column_count": 18, "columns": [{"original_name": "Data Transação", "normalized_name": "data_transacao"}, {"original_name": "Data", "normalized_name": "data"}, {"original_name": "Código", "normalized_name": "codigo"}, {"original_name": "Tipo", "normalized_name": "tipo"}, {"original_name": "Paciente", "normalized_name": "paciente"}, {"original_name": "Executante", "normalized_name": "executante"}, {"original_name": "Sol. Primário", "normalized_name": "sol_primario"}, {"original_name": "Sol. Secundário", "normalized_name": "sol_secundario"}, {"original_name": "Qtd - Procedimento", "normalized_name": "qtd_procedimento"}, {"original_name": "Editor <PERSON><PERSON>", "normalized_name": "editor_financeiro"}, {"original_name": "Pagamento Efetuado", "normalized_name": "pagamento_efetuado"}, {"original_name": "Forma de Pagamento", "normalized_name": "forma_de_pagamento"}, {"original_name": "NSU(DOC/CV/ID)", "normalized_name": "nsu_doc_cv_id"}, {"original_name": "Observação", "normalized_name": "observacao"}, {"original_name": "Pessoa Física", "normalized_name": "pessoa_fisica"}, {"original_name": "Valor R$", "normalized_name": "valor_r"}, {"original_name": "N° Nota Fiscal", "normalized_name": "n_nota_fiscal"}, {"original_name": "Recibo Emitido", "normalized_name": "recibo_emitido"}], "column_types": {"data_transacao": "datetime64[ns]", "data": "datetime64[ns]", "codigo": "int64", "tipo": "object", "paciente": "object", "executante": "object", "sol_primario": "float64", "sol_secundario": "float64", "qtd_procedimento": "object", "editor_financeiro": "object", "pagamento_efetuado": "bool", "forma_de_pagamento": "object", "nsu_doc_cv_id": "float64", "observacao": "float64", "pessoa_fisica": "bool", "valor_r": "float64", "n_nota_fiscal": "float64", "recibo_emitido": "bool"}, "column_stats": {"data_transacao": {"unique_values": 1, "most_common": "22/04/2025", "most_common_count": 8, "null_count": 0, "null_percentage": 0.0}, "data": {"unique_values": 3, "most_common": "22/04/2025", "most_common_count": 6, "null_count": 0, "null_percentage": 0.0}, "codigo": {"min": 151314579.0, "max": 155723069.0, "mean": 154308580.125, "null_count": 0, "null_percentage": 0.0}, "tipo": {"unique_values": 1, "most_common": "Atendimento", "most_common_count": 8, "null_count": 0, "null_percentage": 0.0}, "paciente": {"unique_values": 6, "most_common": "ACRISIO JP TESTE", "most_common_count": 3, "null_count": 0, "null_percentage": 0.0}, "executante": {"unique_values": 4, "most_common": "Plano de Tratamento", "most_common_count": 3, "null_count": 0, "null_percentage": 0.0}, "sol_primario": {"min": null, "max": null, "mean": null, "null_count": 8, "null_percentage": 100.0}, "sol_secundario": {"min": null, "max": null, "mean": null, "null_count": 8, "null_percentage": 100.0}, "qtd_procedimento": {"unique_values": 3, "most_common": NaN, "most_common_count": 3, "null_count": 3, "null_percentage": 37.5}, "editor_financeiro": {"unique_values": 5, "most_common": "Equipe Amigo - Caio <PERSON>", "most_common_count": 3, "null_count": 0, "null_percentage": 0.0}, "pagamento_efetuado": {"unique_values": 2, "most_common": "Não", "most_common_count": 7, "null_count": 0, "null_percentage": 0.0}, "forma_de_pagamento": {"unique_values": 5, "most_common": "Pendente", "most_common_count": 4, "null_count": 0, "null_percentage": 0.0}, "nsu_doc_cv_id": {"min": null, "max": null, "mean": null, "null_count": 8, "null_percentage": 100.0}, "observacao": {"min": null, "max": null, "mean": null, "null_count": 8, "null_percentage": 100.0}, "pessoa_fisica": {"unique_values": 1, "most_common": "Não", "most_common_count": 8, "null_count": 0, "null_percentage": 0.0}, "valor_r": {"min": 80.0, "max": 9000.0, "mean": 1728.3333333333333, "null_count": 2, "null_percentage": 25.0}, "n_nota_fiscal": {"min": null, "max": null, "mean": null, "null_count": 8, "null_percentage": 100.0}, "recibo_emitido": {"unique_values": 1, "most_common": "Não", "most_common_count": 8, "null_count": 0, "null_percentage": 0.0}}, "column_samples": {"data_transacao": ["22/04/2025", "22/04/2025", "22/04/2025", "22/04/2025", "22/04/2025"], "data": ["22/04/2025", "22/04/2025", "22/04/2025", "22/04/2025", "22/04/2025"], "codigo": [153651062, 153658697, 155723069, 151314579, 155491950], "tipo": ["Atendimento", "Atendimento", "Atendimento", "Atendimento", "Atendimento"], "paciente": ["ACRISIO JP TESTE", "ACRISIO JP TESTE", "<PERSON><PERSON>", "Junior Souza", "MARINA HAZIN CONVÊNIO"], "executante": ["Plano de Tratamento", "Equipe Amigo - Caio <PERSON>", "Plano de Tratamento", "Equipe Amigo - Caio <PERSON>", "Plano de Tratamento"], "sol_primario": [], "sol_secundario": [], "qtd_procedimento": ["1 - <PERSON><PERSON><PERSON> - teste", "1 - <PERSON><PERSON><PERSON> - teste", "1 - <PERSON><PERSON><PERSON> - teste", "1 - <PERSON><PERSON><PERSON>, 1 - <PERSON><PERSON>faroplastia", "1 - 1ª Sessão de psicoterapia"], "editor_financeiro": ["Equipe Amigo - BRUNO LIMA", "<PERSON>", "<PERSON><PERSON>", "Sem editor finance<PERSON>", "Equipe Amigo - Caio <PERSON>"], "pagamento_efetuado": ["<PERSON>m", "Não", "Não", "Não", "Não"], "forma_de_pagamento": ["Cancelou", "Pendente", "Pendente", "Pendente", "BRADESCO"], "nsu_doc_cv_id": [], "observacao": [], "pessoa_fisica": ["Não", "Não", "Não", "Não", "Não"], "valor_r": [1000.0, 80.0, 9000.0, 100.0, 90.0], "n_nota_fiscal": [], "recibo_emitido": ["Não", "Não", "Não", "Não", "Não"]}, "potential_keys": ["codigo", "nsu_doc_cv_id", "recibo_emitido"], "potential_relationships": [{"column": "nsu_doc_cv_id", "possible_related_entity": "nsu_doc_cv"}], "analyzed_at": "2025-04-23T23:41:59.753072"}, {"file_name": "relatorio_contas_a_receber.xlsx", "module": "financeiro", "row_count": 65, "column_count": 15, "columns": [{"original_name": "Data de vencimento", "normalized_name": "data_de_vencimento"}, {"original_name": "Competência", "normalized_name": "competencia"}, {"original_name": "<PERSON><PERSON><PERSON> de", "normalized_name": "receber_de"}, {"original_name": "Categoria", "normalized_name": "categoria"}, {"original_name": "Classificação", "normalized_name": "classificacao"}, {"original_name": "Descrição", "normalized_name": "descricao"}, {"original_name": "Forma de Pagamento", "normalized_name": "forma_de_pagamento"}, {"original_name": "Observação", "normalized_name": "observacao"}, {"original_name": "Nº documento", "normalized_name": "nº_documento"}, {"original_name": "Tag", "normalized_name": "tag"}, {"original_name": "Centro de custo", "normalized_name": "centro_de_custo"}, {"original_name": "Unidade", "normalized_name": "unidade"}, {"original_name": "Valor Líquido R$", "normalized_name": "valor_liquido_r"}, {"original_name": "Nota Emitida", "normalized_name": "nota_emitida"}, {"original_name": "Recibo Emitido", "normalized_name": "recibo_emitido"}], "column_types": {"data_de_vencimento": "object", "competencia": "object", "receber_de": "object", "categoria": "object", "classificacao": "object", "descricao": "object", "forma_de_pagamento": "object", "observacao": "object", "nº_documento": "object", "tag": "object", "centro_de_custo": "object", "unidade": "object", "valor_liquido_r": "float64", "nota_emitida": "bool", "recibo_emitido": "bool"}, "column_stats": {"data_de_vencimento": {"unique_values": 26, "most_common": "17/04/2025", "most_common_count": 8, "null_count": 0, "null_percentage": 0.0}, "competencia": {"unique_values": 4, "most_common": "-", "most_common_count": 47, "null_count": 0, "null_percentage": 0.0}, "receber_de": {"unique_values": 30, "most_common": "BANCO DO BRASIL 103291", "most_common_count": 13, "null_count": 0, "null_percentage": 0.0}, "categoria": {"unique_values": 4, "most_common": "RECEITA OPERACIONAL", "most_common_count": 54, "null_count": 0, "null_percentage": 0.0}, "classificacao": {"unique_values": 3, "most_common": "Prestação de Serviço", "most_common_count": 57, "null_count": 0, "null_percentage": 0.0}, "descricao": {"unique_values": 40, "most_common": "Lote - Cartão (GetNET)", "most_common_count": 9, "null_count": 0, "null_percentage": 0.0}, "forma_de_pagamento": {"unique_values": 6, "most_common": "Cartão", "most_common_count": 24, "null_count": 0, "null_percentage": 0.0}, "observacao": {"unique_values": 1, "most_common": "-", "most_common_count": 65, "null_count": 0, "null_percentage": 0.0}, "nº_documento": {"unique_values": 4, "most_common": "-", "most_common_count": 62, "null_count": 0, "null_percentage": 0.0}, "tag": {"unique_values": 1, "most_common": "-", "most_common_count": 65, "null_count": 0, "null_percentage": 0.0}, "centro_de_custo": {"unique_values": 3, "most_common": "-", "most_common_count": 63, "null_count": 0, "null_percentage": 0.0}, "unidade": {"unique_values": 4, "most_common": "-", "most_common_count": 33, "null_count": 0, "null_percentage": 0.0}, "valor_liquido_r": {"min": 0.0, "max": 11500.0, "mean": 1638.3886153846156, "null_count": 0, "null_percentage": 0.0}, "nota_emitida": {"unique_values": 1, "most_common": "Não", "most_common_count": 65, "null_count": 0, "null_percentage": 0.0}, "recibo_emitido": {"unique_values": 2, "most_common": "Não", "most_common_count": 63, "null_count": 0, "null_percentage": 0.0}}, "column_samples": {"data_de_vencimento": ["03/04/2025", "14/04/2025", "28/04/2025", "03/04/2025", "17/04/2025"], "competencia": ["-", "04/2025", "-", "04/2025", "04/2025"], "receber_de": ["BRADESCO 000000", "<PERSON>", "BANCO DO BRASIL 103291", "testse", "Junior Souza"], "categoria": ["RECEITA OPERACIONAL", "RECEITA OPERACIONAL", "RECEITA OPERACIONAL", "RECEITA OPERACIONAL", "RECEITA OPERACIONAL"], "classificacao": ["Prestação de Serviço", "Prestação de Serviço", "Prestação de Serviço", "Prestação de Serviço", "Prestação de Serviço"], "descricao": ["Lote - Cartão (DÉBITO - Banricompras)", "Orçamento #1601790", "Lote - Cartão (CIELO TESTE FABI)", "Lote - Cartão (CRÉDITO - Cielo)", "Orçamento #1597553"], "forma_de_pagamento": ["Pix", "Nota Promissória", "<PERSON><PERSON><PERSON>", "Cartão", "<PERSON><PERSON><PERSON>"], "observacao": ["-", "-", "-", "-", "-"], "nº_documento": ["-", "-", "-", "-", "-"], "tag": ["-", "-", "-", "-", "-"], "centro_de_custo": ["-", "-", "-", "-", "-"], "unidade": ["-", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "-", "-", "Morumbi"], "valor_liquido_r": [300.0, 203.0, 450.0, 2.5, 2800.0], "nota_emitida": ["Não", "Não", "Não", "Não", "Não"], "recibo_emitido": ["<PERSON>m", "Não", "Não", "Não", "Não"]}, "potential_keys": ["unidade", "valor_liquido_r", "nota_emitida", "recibo_emitido"], "potential_relationships": [], "analyzed_at": "2025-04-23T23:41:59.773324"}, {"file_name": "relatório_orçamentos_em_aberto.xlsx", "module": "paciente", "row_count": 10, "column_count": 10, "columns": [{"original_name": "ID Amigo", "normalized_name": "id_amigo"}, {"original_name": "Paciente", "normalized_name": "paciente"}, {"original_name": "CPF", "normalized_name": "cpf"}, {"original_name": "Data Nasc.", "normalized_name": "data_nasc"}, {"original_name": "Sexo", "normalized_name": "sexo"}, {"original_name": "Vip", "normalized_name": "vip"}, {"original_name": "E-mail", "normalized_name": "e_mail"}, {"original_name": "<PERSON><PERSON><PERSON>", "normalized_name": "celular"}, {"original_name": "Orçamentos", "normalized_name": "orcamentos"}, {"original_name": "Total R$", "normalized_name": "total_r"}], "column_types": {"id_amigo": "int64", "paciente": "object", "cpf": "int64", "data_nasc": "datetime64[ns]", "sexo": "object", "vip": "bool", "e_mail": "object", "celular": "object", "orcamentos": "int64", "total_r": "int64"}, "column_stats": {"id_amigo": {"min": 3962189.0, "max": 90775793.0, "mean": 66140328.5, "null_count": 0, "null_percentage": 0.0}, "paciente": {"unique_values": 10, "most_common": "<PERSON><PERSON><PERSON>", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "cpf": {"min": 3718245086.0, "max": 93070730744.0, "mean": 43913747793.6, "null_count": 0, "null_percentage": 0.0}, "data_nasc": {"unique_values": 5, "most_common": "21/09/1995", "most_common_count": 4, "null_count": 2, "null_percentage": 20.0}, "sexo": {"unique_values": 1, "most_common": NaN, "most_common_count": 7, "null_count": 7, "null_percentage": 70.0}, "vip": {"unique_values": 2, "most_common": "Não", "most_common_count": 7, "null_count": 0, "null_percentage": 0.0}, "e_mail": {"unique_values": 5, "most_common": "<EMAIL>", "most_common_count": 4, "null_count": 2, "null_percentage": 20.0}, "celular": {"unique_values": 7, "most_common": "(11) 97505-4988", "most_common_count": 4, "null_count": 0, "null_percentage": 0.0}, "orcamentos": {"min": 1.0, "max": 3.0, "mean": 1.3, "null_count": 0, "null_percentage": 0.0}, "total_r": {"min": 720.0, "max": 26350.0, "mean": 9797.0, "null_count": 0, "null_percentage": 0.0}}, "column_samples": {"id_amigo": [90157170, 89900347, 75656153, 26015293, 3962189], "paciente": ["<PERSON><PERSON><PERSON>", "Junior Souza", "<PERSON>", "<PERSON><PERSON>", "<PERSON> "], "cpf": [9704130767, 80351218041, 84472975009, 92255060906, 26907698838], "data_nasc": ["21/09/1995", "23/03/1981", "21/09/1995", "21/09/1995", "21/09/1995"], "sexo": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "vip": ["Não", "Não", "Não", "<PERSON>m", "Não"], "e_mail": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "claudio<PERSON><PERSON><PERSON>@gmail.com", "<EMAIL>"], "celular": ["(11) 97505-4988", "(71) 98852-3298", "(27) 99650-3322", "(11) 97505-4988", "(31) 99572-0954"], "orcamentos": [1, 1, 1, 2, 1], "total_r": [26350, 4900, 9000, 10500, 2800]}, "potential_keys": ["id_amigo", "paciente", "cpf"], "potential_relationships": [{"column": "id_amigo", "possible_related_entity": "amigo"}], "analyzed_at": "2025-04-23T23:41:59.787296"}, {"file_name": "relatorio_atendimentos_realizados.xlsx", "module": "paciente", "row_count": 47, "column_count": 11, "columns": [{"original_name": "ID Amigo", "normalized_name": "id_amigo"}, {"original_name": "Paciente", "normalized_name": "paciente"}, {"original_name": "CPF", "normalized_name": "cpf"}, {"original_name": "Data Nasc.", "normalized_name": "data_nasc"}, {"original_name": "Sexo", "normalized_name": "sexo"}, {"original_name": "Vip", "normalized_name": "vip"}, {"original_name": "E-mail", "normalized_name": "e_mail"}, {"original_name": "<PERSON><PERSON><PERSON>", "normalized_name": "celular"}, {"original_name": "Atendimentos", "normalized_name": "atendimentos"}, {"original_name": "Ticket médio R$", "normalized_name": "ticket_medio_r"}, {"original_name": "Total R$", "normalized_name": "total_r"}], "column_types": {"id_amigo": "int64", "paciente": "object", "cpf": "float64", "data_nasc": "datetime64[ns]", "sexo": "object", "vip": "bool", "e_mail": "object", "celular": "object", "atendimentos": "int64", "ticket_medio_r": "float64", "total_r": "float64"}, "column_stats": {"id_amigo": {"min": 27050798.0, "max": 90817169.0, "mean": 84139991.9787234, "null_count": 0, "null_percentage": 0.0}, "paciente": {"unique_values": 46, "most_common": "YGOR PAIVA", "most_common_count": 2, "null_count": 0, "null_percentage": 0.0}, "cpf": {"min": 195423135.0, "max": 84472975009.0, "mean": 26671393898.944443, "null_count": 29, "null_percentage": 61.702127659574465}, "data_nasc": {"unique_values": 13, "most_common": NaN, "most_common_count": 32, "null_count": 32, "null_percentage": 68.08510638297872}, "sexo": {"unique_values": 2, "most_common": NaN, "most_common_count": 42, "null_count": 42, "null_percentage": 89.36170212765957}, "vip": {"unique_values": 2, "most_common": "Não", "most_common_count": 44, "null_count": 0, "null_percentage": 0.0}, "e_mail": {"unique_values": 16, "most_common": NaN, "most_common_count": 29, "null_count": 29, "null_percentage": 61.702127659574465}, "celular": {"unique_values": 30, "most_common": NaN, "most_common_count": 13, "null_count": 13, "null_percentage": 27.659574468085108}, "atendimentos": {"min": 1.0, "max": 4.0, "mean": 1.2765957446808511, "null_count": 0, "null_percentage": 0.0}, "ticket_medio_r": {"min": 2.5, "max": 25000.0, "mean": 936.6265957446809, "null_count": 0, "null_percentage": 0.0}, "total_r": {"min": 2.5, "max": 25000.0, "mean": 1162.6851063829788, "null_count": 0, "null_percentage": 0.0}}, "column_samples": {"id_amigo": [90071073, 90193399, 81199677, 89894844, 90164643], "paciente": ["FABIO FELTRIM", "LUIZ GUSTAVO", "DIEGO PATERNOSTRO", "IGOR DOURADO", "<PERSON>"], "cpf": [80351218041.0, 3718245086.0, 30957067070.0, 2437170081.0, 32585940829.0], "data_nasc": ["30/05/1990", "09/09/1993", "12/12/2000", "21/09/1995", "21/09/1995"], "sexo": ["Feminino", "Feminino", "Feminino", "Feminino", "<PERSON><PERSON><PERSON><PERSON>"], "vip": ["Não", "Não", "Não", "Não", "Não"], "e_mail": ["<EMAIL>", "fabi<PERSON><PERSON><PERSON><PERSON>@me.com", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "celular": ["(66) 99654-2070", "(34) 93300-1813", "(81) 99730-4365", "(11) 98270-5254", "(41) 99252-5610"], "atendimentos": [1, 1, 1, 1, 1], "ticket_medio_r": [1535.5, 300.0, 300.0, 213.33, 1084.4], "total_r": [149.0, 300.0, 4400.0, 500.0, 4606.5]}, "potential_keys": ["id_amigo"], "potential_relationships": [{"column": "id_amigo", "possible_related_entity": "amigo"}], "analyzed_at": "2025-04-23T23:41:59.801231"}, {"file_name": "relatorio_orçamentos_fechados.xlsx", "module": "paciente", "row_count": 14, "column_count": 10, "columns": [{"original_name": "ID Amigo", "normalized_name": "id_amigo"}, {"original_name": "Paciente", "normalized_name": "paciente"}, {"original_name": "CPF", "normalized_name": "cpf"}, {"original_name": "Data Nasc.", "normalized_name": "data_nasc"}, {"original_name": "Sexo", "normalized_name": "sexo"}, {"original_name": "Vip", "normalized_name": "vip"}, {"original_name": "E-mail", "normalized_name": "e_mail"}, {"original_name": "<PERSON><PERSON><PERSON>", "normalized_name": "celular"}, {"original_name": "Orçamentos", "normalized_name": "orcamentos"}, {"original_name": "Total R$", "normalized_name": "total_r"}], "column_types": {"id_amigo": "int64", "paciente": "object", "cpf": "float64", "data_nasc": "datetime64[ns]", "sexo": "object", "vip": "bool", "e_mail": "object", "celular": "object", "orcamentos": "int64", "total_r": "int64"}, "column_stats": {"id_amigo": {"min": 19085497.0, "max": 90802077.0, "mean": 76271349.21428572, "null_count": 0, "null_percentage": 0.0}, "paciente": {"unique_values": 14, "most_common": "Junior Souza", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "cpf": {"min": 195423135.0, "max": 75731649120.0, "mean": 22922115628.2, "null_count": 4, "null_percentage": 28.57142857142857}, "data_nasc": {"unique_values": 5, "most_common": NaN, "most_common_count": 7, "null_count": 7, "null_percentage": 50.0}, "sexo": {"unique_values": 1, "most_common": NaN, "most_common_count": 10, "null_count": 10, "null_percentage": 71.42857142857143}, "vip": {"unique_values": 2, "most_common": "Não", "most_common_count": 13, "null_count": 0, "null_percentage": 0.0}, "e_mail": {"unique_values": 9, "most_common": "<EMAIL>", "most_common_count": 3, "null_count": 3, "null_percentage": 21.428571428571427}, "celular": {"unique_values": 10, "most_common": "(11) 97505-4988", "most_common_count": 3, "null_count": 2, "null_percentage": 14.285714285714285}, "orcamentos": {"min": 1.0, "max": 2.0, "mean": 1.3571428571428572, "null_count": 0, "null_percentage": 0.0}, "total_r": {"min": 300.0, "max": 20500.0, "mean": 9040.42857142857, "null_count": 0, "null_percentage": 0.0}}, "column_samples": {"id_amigo": [75656153, 90643157, 72562209, 64447608, 38204852], "paciente": ["<PERSON>", "Junior Souza", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "JONAS CASTRO FILHO"], "cpf": [75731649120.0, 22587999022.0, 195423135.0, 5149639486.0, 9704130767.0], "data_nasc": ["23/03/1981", "21/09/1995", "21/09/1995", "29/05/1997", "21/09/1995"], "sexo": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "vip": ["Não", "Não", "Não", "Não", "<PERSON>m"], "e_mail": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "celular": ["(11) 97505-4988", "(11) 97645-8283", "(11) 97155-6404", "(27) 99693-2233", "(31) 99572-0954"], "orcamentos": [1, 1, 1, 2, 2], "total_r": [20500, 300, 9000, 5000, 14000]}, "potential_keys": ["id_amigo", "paciente"], "potential_relationships": [{"column": "id_amigo", "possible_related_entity": "amigo"}], "analyzed_at": "2025-04-23T23:41:59.814304"}, {"file_name": "relatorio_creditos_disponiveis.xlsx", "module": "paciente", "row_count": 794, "column_count": 11, "columns": [{"original_name": "ID Amigo", "normalized_name": "id_amigo"}, {"original_name": "Paciente", "normalized_name": "paciente"}, {"original_name": "CPF", "normalized_name": "cpf"}, {"original_name": "Data Nasc.", "normalized_name": "data_nasc"}, {"original_name": "Sexo", "normalized_name": "sexo"}, {"original_name": "Vip", "normalized_name": "vip"}, {"original_name": "E-mail", "normalized_name": "e_mail"}, {"original_name": "<PERSON><PERSON><PERSON>", "normalized_name": "celular"}, {"original_name": "Crédito Pré-pago R$", "normalized_name": "credito_pre_pago_r"}, {"original_name": "Crédito Orçamento R$", "normalized_name": "credito_orcamento_r"}, {"original_name": "Total R$", "normalized_name": "total_r"}], "column_types": {"id_amigo": "int64", "paciente": "object", "cpf": "float64", "data_nasc": "object", "sexo": "object", "vip": "bool", "e_mail": "object", "celular": "object", "credito_pre_pago_r": "int64", "credito_orcamento_r": "int64", "total_r": "float64"}, "column_stats": {"id_amigo": {"min": 39306.0, "max": 90796209.0, "mean": 54967410.96095718, "null_count": 0, "null_percentage": 0.0}, "paciente": {"unique_values": 771, "most_common": "<PERSON>", "most_common_count": 6, "null_count": 0, "null_percentage": 0.0}, "cpf": {"min": 15053008.0, "max": 99440761040.0, "mean": 32534537397.564102, "null_count": 560, "null_percentage": 70.52896725440806}, "data_nasc": {"unique_values": 157, "most_common": NaN, "most_common_count": 610, "null_count": 610, "null_percentage": 76.82619647355163}, "sexo": {"unique_values": 2, "most_common": NaN, "most_common_count": 745, "null_count": 745, "null_percentage": 93.82871536523929}, "vip": {"unique_values": 2, "most_common": "Não", "most_common_count": 745, "null_count": 0, "null_percentage": 0.0}, "e_mail": {"unique_values": 373, "most_common": NaN, "most_common_count": 232, "null_count": 232, "null_percentage": 29.219143576826195}, "celular": {"unique_values": 486, "most_common": NaN, "most_common_count": 142, "null_count": 142, "null_percentage": 17.884130982367758}, "credito_pre_pago_r": {"min": -1500.0, "max": 50000.0, "mean": 1468.3639798488664, "null_count": 0, "null_percentage": 0.0}, "credito_orcamento_r": {"min": 0.0, "max": 108700.0, "mean": 6591.8753148614605, "null_count": 0, "null_percentage": 0.0}, "total_r": {"min": -250.0, "max": 108700.01, "mean": 8060.280793450881, "null_count": 0, "null_percentage": 0.0}}, "column_samples": {"id_amigo": [73061217, 23996049, 26714595, 85921930, 79707174], "paciente": ["Marissol Teste", "FERNANDO FONTES", "<PERSON> alexa<PERSON> teste", "<PERSON><PERSON><PERSON>", "Raiane - <PERSON><PERSON><PERSON><PERSON>"], "cpf": [97534161304.0, 25559552089.0, 24590696851.0, 49664245852.0, 195423135.0], "data_nasc": ["14/01/1985", "11/11/2021", "21/07/1993", "23/03/1981", "11/09/1987"], "sexo": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Feminino", "Feminino", "Feminino"], "vip": ["Não", "Não", "Não", "Não", "Não"], "e_mail": ["<EMAIL>", "leandr<PERSON><PERSON><PERSON>@icloud.com", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "celular": ["(11) 97505-4988", "(61) 99251-2333", "(61) 98402-4170", "(66) 99614-0022", "(11) 97505-4988"], "credito_pre_pago_r": [300, 0, 0, 0, 0], "credito_orcamento_r": [400, 4000, 900, 11500, 900], "total_r": [9000.0, 26623.45, 9000.0, 16000.0, 800.0]}, "potential_keys": ["id_amigo"], "potential_relationships": [{"column": "id_amigo", "possible_related_entity": "amigo"}], "analyzed_at": "2025-04-23T23:41:59.890672"}, {"file_name": "Relatorio_Avalicao_NPS.xlsx", "module": "amigocare", "row_count": 4, "column_count": 36, "columns": [{"original_name": "ID do paciente", "normalized_name": "id_do_paciente"}, {"original_name": "Código do Atendimento", "normalized_name": "codigo_do_atendimento"}, {"original_name": "Código", "normalized_name": "codigo"}, {"original_name": "Nome", "normalized_name": "nome"}, {"original_name": "VIP", "normalized_name": "vip"}, {"original_name": "NPS", "normalized_name": "nps"}, {"original_name": "<PERSON><PERSON><PERSON><PERSON>", "normalized_name": "comentario"}, {"original_name": "Data do atendimento", "normalized_name": "data_do_atendimento"}, {"original_name": "Data de envio do NPS", "normalized_name": "data_de_envio_do_nps"}, {"original_name": "Data da avaliação do NPS", "normalized_name": "data_da_avaliacao_do_nps"}, {"original_name": "CPF", "normalized_name": "cpf"}, {"original_name": "RG", "normalized_name": "rg"}, {"original_name": "CNH", "normalized_name": "cnh"}, {"original_name": "Nome do responsável", "normalized_name": "nome_do_responsavel"}, {"original_name": "CPF do responsável", "normalized_name": "cpf_do_responsavel"}, {"original_name": "Data de Nascimento", "normalized_name": "data_de_nascimento"}, {"original_name": "Email", "normalized_name": "email"}, {"original_name": "Sexo", "normalized_name": "sexo"}, {"original_name": "Observação", "normalized_name": "observacao"}, {"original_name": "Origem", "normalized_name": "origem"}, {"original_name": "<PERSON><PERSON><PERSON>", "normalized_name": "tipo_sanguineo"}, {"original_name": "<PERSON><PERSON><PERSON>", "normalized_name": "celular"}, {"original_name": "Telefone Residencial", "normalized_name": "telefone_residencial"}, {"original_name": "Telefone Comercial", "normalized_name": "telefone_comercial"}, {"original_name": "CEP", "normalized_name": "cep"}, {"original_name": "Endereço", "normalized_name": "endereco"}, {"original_name": "N°", "normalized_name": "n"}, {"original_name": "Complemento", "normalized_name": "complemento"}, {"original_name": "Bairro", "normalized_name": "bairro"}, {"original_name": "Cidade", "normalized_name": "cidade"}, {"original_name": "Estado", "normalized_name": "estado"}, {"original_name": "<PERSON><PERSON><PERSON><PERSON>", "normalized_name": "convenio"}, {"original_name": "Plano", "normalized_name": "plano"}, {"original_name": "N° matrícula", "normalized_name": "n_matricula"}, {"original_name": "Validade", "normalized_name": "validade"}, {"original_name": "Acomodação", "normalized_name": "acomodacao"}], "column_types": {"id_do_paciente": "int64", "codigo_do_atendimento": "int64", "codigo": "float64", "nome": "object", "vip": "bool", "nps": "int64", "comentario": "float64", "data_do_atendimento": "object", "data_de_envio_do_nps": "object", "data_da_avaliacao_do_nps": "object", "cpf": "float64", "rg": "float64", "cnh": "float64", "nome_do_responsavel": "float64", "cpf_do_responsavel": "float64", "data_de_nascimento": "datetime64[ns]", "email": "float64", "sexo": "float64", "observacao": "float64", "origem": "float64", "tipo_sanguineo": "float64", "celular": "object", "telefone_residencial": "float64", "telefone_comercial": "float64", "cep": "float64", "endereco": "float64", "n": "float64", "complemento": "float64", "bairro": "float64", "cidade": "float64", "estado": "float64", "convenio": "float64", "plano": "float64", "n_matricula": "float64", "validade": "datetime64[ns]", "acomodacao": "float64"}, "column_stats": {"id_do_paciente": {"min": 89130181.0, "max": 90070974.0, "mean": 89394817.0, "null_count": 0, "null_percentage": 0.0}, "codigo_do_atendimento": {"min": 151342058.0, "max": 153075414.0, "mean": 151895337.25, "null_count": 0, "null_percentage": 0.0}, "codigo": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "nome": {"unique_values": 4, "most_common": "Dr. <PERSON>", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "vip": {"unique_values": 1, "most_common": "Não", "most_common_count": 4, "null_count": 0, "null_percentage": 0.0}, "nps": {"min": 5.0, "max": 5.0, "mean": 5.0, "null_count": 0, "null_percentage": 0.0}, "comentario": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "data_do_atendimento": {"unique_values": 4, "most_common": "24/03/2025 às 18:20", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "data_de_envio_do_nps": {"unique_values": 1, "most_common": "22/04/2025 às 19:59", "most_common_count": 4, "null_count": 0, "null_percentage": 0.0}, "data_da_avaliacao_do_nps": {"unique_values": 3, "most_common": "25/03/2025 às 00:00", "most_common_count": 1, "null_count": 1, "null_percentage": 25.0}, "cpf": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "rg": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "cnh": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "nome_do_responsavel": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "cpf_do_responsavel": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "data_de_nascimento": {"unique_values": 1, "most_common": "22/04/2025", "most_common_count": 4, "null_count": 0, "null_percentage": 0.0}, "email": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "sexo": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "observacao": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "origem": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "tipo_sanguineo": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "celular": {"unique_values": 4, "most_common": "(46) 99940-2000", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "telefone_residencial": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "telefone_comercial": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "cep": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "endereco": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "n": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "complemento": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "bairro": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "cidade": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "estado": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "convenio": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "plano": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "n_matricula": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}, "validade": {"unique_values": 1, "most_common": "22/04/2025", "most_common_count": 4, "null_count": 0, "null_percentage": 0.0}, "acomodacao": {"min": null, "max": null, "mean": null, "null_count": 4, "null_percentage": 100.0}}, "column_samples": {"id_do_paciente": [89224729, 89153384, 89130181, 90070974], "codigo_do_atendimento": [151342058, 153075414, 151721228, 151442649], "codigo": [], "nome": ["DR AMILTON TESTE", "MONIZE", "DRA FABIANA TESTE", "Dr. <PERSON>"], "vip": ["Não", "Não", "Não", "Não"], "nps": [5, 5, 5, 5], "comentario": [], "data_do_atendimento": ["24/03/2025 às 18:20", "28/03/2025 às 10:40", "03/04/2025 às 16:30", "27/03/2025 às 11:00"], "data_de_envio_do_nps": ["22/04/2025 às 19:59", "22/04/2025 às 19:59", "22/04/2025 às 19:59", "22/04/2025 às 19:59"], "data_da_avaliacao_do_nps": ["25/03/2025 às 00:00", "28/03/2025 às 00:00", "29/03/2025 às 00:00"], "cpf": [], "rg": [], "cnh": [], "nome_do_responsavel": [], "cpf_do_responsavel": [], "data_de_nascimento": ["22/04/2025", "22/04/2025", "22/04/2025", "22/04/2025"], "email": [], "sexo": [], "observacao": [], "origem": [], "tipo_sanguineo": [], "celular": ["(46) 99940-2000", "(18) 99790-3599", "(11) 97436-5636", "(11) 99739-1665"], "telefone_residencial": [], "telefone_comercial": [], "cep": [], "endereco": [], "n": [], "complemento": [], "bairro": [], "cidade": [], "estado": [], "convenio": [], "plano": [], "n_matricula": [], "validade": ["22/04/2025", "22/04/2025", "22/04/2025", "22/04/2025"], "acomodacao": []}, "potential_keys": ["id_do_paciente", "codigo_do_atendimento", "codigo", "nome", "data_do_atendimento", "celular", "telefone_residencial", "cidade", "validade"], "potential_relationships": [{"column": "id_do_paciente", "possible_related_entity": "do_paciente"}, {"column": "codigo_do_atendimento", "possible_related_entity": "do_atendimento"}], "analyzed_at": "2025-04-23T23:41:59.908602"}, {"file_name": "relatorio_producao_medica.xlsx", "module": "agenda", "row_count": 2, "column_count": 57, "columns": [{"original_name": "Data Atendimento", "normalized_name": "data_atendimento"}, {"original_name": "<PERSON><PERSON>", "normalized_name": "hora_atendimento"}, {"original_name": "<PERSON><PERSON><PERSON>", "normalized_name": "codigo_atendimento"}, {"original_name": "<PERSON><PERSON><PERSON><PERSON>", "normalized_name": "horario_de_chegada"}, {"original_name": "Indice", "normalized_name": "indice"}, {"original_name": "Paciente", "normalized_name": "paciente"}, {"original_name": "Profissão", "normalized_name": "profi<PERSON><PERSON>"}, {"original_name": "<PERSON><PERSON>", "normalized_name": "idade"}, {"original_name": "Como conheceu", "normalized_name": "como_conheceu"}, {"original_name": "ID amigo ", "normalized_name": "id_amigo"}, {"original_name": "Cod<PERSON>", "normalized_name": "cod_legado"}, {"original_name": "<PERSON><PERSON><PERSON><PERSON>", "normalized_name": "matricula"}, {"original_name": "CNS", "normalized_name": "cns"}, {"original_name": "Profissional", "normalized_name": "profissional"}, {"original_name": "Especialidade", "normalized_name": "especialidade"}, {"original_name": "CBO", "normalized_name": "cbo"}, {"original_name": "Solicitante", "normalized_name": "solicitante"}, {"original_name": "Executante", "normalized_name": "executante"}, {"original_name": "Participação", "normalized_name": "participacao"}, {"original_name": "Hospital / Local", "normalized_name": "hospital_local"}, {"original_name": "Forma de Pagamento", "normalized_name": "forma_de_pagamento"}, {"original_name": "Lançamento", "normalized_name": "lancamento"}, {"original_name": "Plano", "normalized_name": "plano"}, {"original_name": "Número da <PERSON>a", "normalized_name": "numero_da_parcela"}, {"original_name": "Qtd parcelas", "normalized_name": "qtd_parcelas"}, {"original_name": "NSU(DOC/CV/ID)", "normalized_name": "nsu_doc_cv_id"}, {"original_name": "Observação financeira", "normalized_name": "observacao_financeira"}, {"original_name": "T<PERSON>o <PERSON>endimento", "normalized_name": "tipo_atendimento"}, {"original_name": "Unidade", "normalized_name": "unidade"}, {"original_name": "Tipo", "normalized_name": "tipo"}, {"original_name": "Grupo", "normalized_name": "grupo"}, {"original_name": "Subgrupo", "normalized_name": "subgrupo"}, {"original_name": "<PERSON><PERSON>", "normalized_name": "item"}, {"original_name": "Quantidade", "normalized_name": "quantidade"}, {"original_name": "Via de acesso", "normalized_name": "via_de_acesso"}, {"original_name": "% Red. Acrésc", "normalized_name": "red_acresc"}, {"original_name": "Valor do item R$", "normalized_name": "valor_do_item_r"}, {"original_name": "Desconto R$", "normalized_name": "desconto_r"}, {"original_name": "Valor total do item R$", "normalized_name": "valor_total_do_item_r"}, {"original_name": "Nota Emitida", "normalized_name": "nota_emitida"}, {"original_name": "Númer<PERSON> da g<PERSON>", "normalized_name": "numero_da_guia"}, {"original_name": "Número lote", "normalized_name": "numero_lote"}, {"original_name": "<PERSON><PERSON> en<PERSON>u", "normalized_name": "quem_enviou"}, {"original_name": "Data envio", "normalized_name": "data_envio"}, {"original_name": "<PERSON><PERSON> bai<PERSON>", "normalized_name": "quem_baixou"}, {"original_name": "Data pagamento", "normalized_name": "data_pagamento"}, {"original_name": "Valor pago R$", "normalized_name": "valor_pago_r"}, {"original_name": "Valor glosado R$", "normalized_name": "valor_glosado_r"}, {"original_name": "<PERSON>o ate<PERSON>", "normalized_name": "ano_atendimento"}, {"original_name": "<PERSON><PERSON>s atendi<PERSON>", "normalized_name": "mes_atendimento"}, {"original_name": "<PERSON><PERSON> en<PERSON>", "normalized_name": "ano_envio"}, {"original_name": "<PERSON><PERSON><PERSON> <PERSON>", "normalized_name": "mes_envio"}, {"original_name": "<PERSON><PERSON>aga<PERSON>", "normalized_name": "ano_pagamento"}, {"original_name": "<PERSON><PERSON><PERSON>", "normalized_name": "mes_pagamento"}, {"original_name": "Recurso", "normalized_name": "recurso"}, {"original_name": "Editor <PERSON><PERSON> (Orçamento)", "normalized_name": "editor_finance<PERSON>_orcamento"}, {"original_name": "Data pagamento (Orçamento)", "normalized_name": "data_pagamento_orcamento"}], "column_types": {"data_atendimento": "datetime64[ns]", "hora_atendimento": "datetime64[ns]", "codigo_atendimento": "int64", "horario_de_chegada": "datetime64[ns]", "indice": "float64", "paciente": "object", "profissao": "float64", "idade": "int64", "como_conheceu": "float64", "id_amigo": "int64", "cod_legado": "float64", "matricula": "float64", "cns": "float64", "profissional": "object", "especialidade": "object", "cbo": "object", "solicitante": "float64", "executante": "object", "participacao": "object", "hospital_local": "float64", "forma_de_pagamento": "object", "lancamento": "int64", "plano": "float64", "numero_da_parcela": "float64", "qtd_parcelas": "float64", "nsu_doc_cv_id": "float64", "observacao_financeira": "float64", "tipo_atendimento": "object", "unidade": "object", "tipo": "object", "grupo": "object", "subgrupo": "object", "item": "object", "quantidade": "int64", "via_de_acesso": "object", "red_acresc": "float64", "valor_do_item_r": "int64", "desconto_r": "int64", "valor_total_do_item_r": "int64", "nota_emitida": "bool", "numero_da_guia": "float64", "numero_lote": "float64", "quem_enviou": "float64", "data_envio": "float64", "quem_baixou": "float64", "data_pagamento": "float64", "valor_pago_r": "int64", "valor_glosado_r": "float64", "ano_atendimento": "int64", "mes_atendimento": "int64", "ano_envio": "float64", "mes_envio": "float64", "ano_pagamento": "float64", "mes_pagamento": "float64", "recurso": "bool", "editor_financeiro_orcamento": "object", "data_pagamento_orcamento": "datetime64[ns]"}, "column_stats": {"data_atendimento": {"unique_values": 1, "most_common": "23/04/2025", "most_common_count": 2, "null_count": 0, "null_percentage": 0.0}, "hora_atendimento": {"unique_values": 2, "most_common": "17:00", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "codigo_atendimento": {"min": 155601121.0, "max": 155686103.0, "mean": 155643612.0, "null_count": 0, "null_percentage": 0.0}, "horario_de_chegada": {"unique_values": 1, "most_common": NaN, "most_common_count": 1, "null_count": 1, "null_percentage": 50.0}, "indice": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "paciente": {"unique_values": 2, "most_common": "<PERSON><PERSON>", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "profissao": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "idade": {"min": 29.0, "max": 37.0, "mean": 33.0, "null_count": 0, "null_percentage": 0.0}, "como_conheceu": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "id_amigo": {"min": 48947847.0, "max": 90796209.0, "mean": 69872028.0, "null_count": 0, "null_percentage": 0.0}, "cod_legado": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "matricula": {"min": 12234556.0, "max": 12234556.0, "mean": 12234556.0, "null_count": 1, "null_percentage": 50.0}, "cns": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "profissional": {"unique_values": 2, "most_common": "Rayara <PERSON> Souza", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "especialidade": {"unique_values": 2, "most_common": "Pneumologist<PERSON>", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "cbo": {"unique_values": 2, "most_common": "225127-<PERSON><PERSON><PERSON><PERSON> pneumologista", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "solicitante": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "executante": {"unique_values": 2, "most_common": "Rayara <PERSON> Souza", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "participacao": {"unique_values": 1, "most_common": "Clínico", "most_common_count": 1, "null_count": 1, "null_percentage": 50.0}, "hospital_local": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "forma_de_pagamento": {"unique_values": 2, "most_common": "AMIL", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "lancamento": {"min": 1.0, "max": 1.0, "mean": 1.0, "null_count": 0, "null_percentage": 0.0}, "plano": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "numero_da_parcela": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "qtd_parcelas": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "nsu_doc_cv_id": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "observacao_financeira": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "tipo_atendimento": {"unique_values": 2, "most_common": "Sessao de Fisioterapia", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "unidade": {"unique_values": 2, "most_common": "CLÍNICA (RL)", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "tipo": {"unique_values": 1, "most_common": "Procedimento", "most_common_count": 2, "null_count": 0, "null_percentage": 0.0}, "grupo": {"unique_values": 1, "most_common": "BOTOX", "most_common_count": 2, "null_count": 0, "null_percentage": 0.0}, "subgrupo": {"unique_values": 2, "most_common": "BOTOX", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "item": {"unique_values": 2, "most_common": "<PERSON><PERSON><PERSON> - teste", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "quantidade": {"min": 1.0, "max": 1.0, "mean": 1.0, "null_count": 0, "null_percentage": 0.0}, "via_de_acesso": {"unique_values": 1, "most_common": "Única", "most_common_count": 1, "null_count": 1, "null_percentage": 50.0}, "red_acresc": {"min": 1.0, "max": 1.0, "mean": 1.0, "null_count": 1, "null_percentage": 50.0}, "valor_do_item_r": {"min": 20.0, "max": 1000.0, "mean": 510.0, "null_count": 0, "null_percentage": 0.0}, "desconto_r": {"min": 0.0, "max": 0.0, "mean": 0.0, "null_count": 0, "null_percentage": 0.0}, "valor_total_do_item_r": {"min": 20.0, "max": 1000.0, "mean": 510.0, "null_count": 0, "null_percentage": 0.0}, "nota_emitida": {"unique_values": 1, "most_common": "Não", "most_common_count": 2, "null_count": 0, "null_percentage": 0.0}, "numero_da_guia": {"min": 421.0, "max": 421.0, "mean": 421.0, "null_count": 1, "null_percentage": 50.0}, "numero_lote": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "quem_enviou": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "data_envio": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "quem_baixou": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "data_pagamento": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "valor_pago_r": {"min": 0.0, "max": 0.0, "mean": 0.0, "null_count": 0, "null_percentage": 0.0}, "valor_glosado_r": {"min": 0.0, "max": 0.0, "mean": 0.0, "null_count": 1, "null_percentage": 50.0}, "ano_atendimento": {"min": 2025.0, "max": 2025.0, "mean": 2025.0, "null_count": 0, "null_percentage": 0.0}, "mes_atendimento": {"min": 4.0, "max": 4.0, "mean": 4.0, "null_count": 0, "null_percentage": 0.0}, "ano_envio": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "mes_envio": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "ano_pagamento": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "mes_pagamento": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "recurso": {"unique_values": 1, "most_common": "Não", "most_common_count": 2, "null_count": 0, "null_percentage": 0.0}, "editor_financeiro_orcamento": {"unique_values": 1, "most_common": NaN, "most_common_count": 1, "null_count": 1, "null_percentage": 50.0}, "data_pagamento_orcamento": {"unique_values": 1, "most_common": NaN, "most_common_count": 1, "null_count": 1, "null_percentage": 50.0}}, "column_samples": {"data_atendimento": ["23/04/2025", "23/04/2025"], "hora_atendimento": ["10:20", "17:00"], "codigo_atendimento": [155601121, 155686103], "horario_de_chegada": ["13:09"], "indice": [], "paciente": ["<PERSON><PERSON>", "<PERSON>"], "profissao": [], "idade": [37, 29], "como_conheceu": [], "id_amigo": [90796209, 48947847], "cod_legado": [], "matricula": [12234556.0], "cns": [], "profissional": ["Equipe Amigo - BRUNO LIMA", "Rayara <PERSON> Souza"], "especialidade": ["Médico otorrino<PERSON>a", "Pneumologist<PERSON>"], "cbo": ["225275-<PERSON><PERSON><PERSON><PERSON>", "225127-<PERSON><PERSON><PERSON><PERSON> pneumologista"], "solicitante": [], "executante": ["Rayara <PERSON> Souza", "Equipe Amigo - BRUNO LIMA"], "participacao": ["Clínico"], "hospital_local": [], "forma_de_pagamento": ["Crédito de Procedimento", "AMIL"], "lancamento": [1, 1], "plano": [], "numero_da_parcela": [], "qtd_parcelas": [], "nsu_doc_cv_id": [], "observacao_financeira": [], "tipo_atendimento": ["PROCEDIMENTO SIMPLES - 60 MIN", "Sessao de Fisioterapia"], "unidade": ["Morumbi", "CLÍNICA (RL)"], "tipo": ["Procedimento", "Procedimento"], "grupo": ["BOTOX", "BOTOX"], "subgrupo": ["BOTOX", "botox"], "item": ["<PERSON><PERSON><PERSON> - teste", "BOTOX MALAR"], "quantidade": [1, 1], "via_de_acesso": ["Única"], "red_acresc": [1.0], "valor_do_item_r": [20, 1000], "desconto_r": [0, 0], "valor_total_do_item_r": [20, 1000], "nota_emitida": ["Não", "Não"], "numero_da_guia": [421.0], "numero_lote": [], "quem_enviou": [], "data_envio": [], "quem_baixou": [], "data_pagamento": [], "valor_pago_r": [0, 0], "valor_glosado_r": [0.0], "ano_atendimento": [2025, 2025], "mes_atendimento": [4, 4], "ano_envio": [], "mes_envio": [], "ano_pagamento": [], "mes_pagamento": [], "recurso": ["Não", "Não"], "editor_financeiro_orcamento": ["Equipe Amigo - BRUNO LIMA"], "data_pagamento_orcamento": ["17/04/2025"]}, "potential_keys": ["hora_atendimento", "codigo_atendimento", "paciente", "idade", "id_amigo", "cod_legado", "profissional", "especialidade", "cbo", "executante", "forma_de_pagamento", "nsu_doc_cv_id", "tipo_atendimento", "unidade", "subgrupo", "item", "quantidade", "valor_do_item_r", "valor_total_do_item_r", "nota_emitida"], "potential_relationships": [{"column": "codigo_atendimento", "possible_related_entity": "atendimento"}, {"column": "id_amigo", "possible_related_entity": "amigo"}, {"column": "nsu_doc_cv_id", "possible_related_entity": "nsu_doc_cv"}], "analyzed_at": "2025-04-23T23:41:59.957820"}, {"file_name": "relatorio_producao_medica2.xlsx", "module": "agenda", "row_count": 72, "column_count": 9, "columns": [{"original_name": "Data Atendimento", "normalized_name": "data_atendimento"}, {"original_name": "<PERSON><PERSON><PERSON>", "normalized_name": "codigo_atendimento"}, {"original_name": "Unidade", "normalized_name": "unidade"}, {"original_name": "Paciente", "normalized_name": "paciente"}, {"original_name": "Profissional", "normalized_name": "profissional"}, {"original_name": "Tipo de Atendimento", "normalized_name": "tipo_de_atendimento"}, {"original_name": "Qtd/Procedimento", "normalized_name": "qtd_procedimento"}, {"original_name": "Forma de Pagamento", "normalized_name": "forma_de_pagamento"}, {"original_name": "Valor R$", "normalized_name": "valor_r"}], "column_types": {"data_atendimento": "object", "codigo_atendimento": "int64", "unidade": "object", "paciente": "object", "profissional": "object", "tipo_de_atendimento": "object", "qtd_procedimento": "object", "forma_de_pagamento": "object", "valor_r": "float64"}, "column_stats": {"data_atendimento": {"unique_values": 61, "most_common": "01/04/2025 12:00", "most_common_count": 5, "null_count": 0, "null_percentage": 0.0}, "codigo_atendimento": {"min": 148577077.0, "max": 155741330.0, "mean": 153790468.91666666, "null_count": 0, "null_percentage": 0.0}, "unidade": {"unique_values": 5, "most_common": "BRASILIA", "most_common_count": 37, "null_count": 0, "null_percentage": 0.0}, "paciente": {"unique_values": 50, "most_common": "<PERSON><PERSON>", "most_common_count": 5, "null_count": 0, "null_percentage": 0.0}, "profissional": {"unique_values": 17, "most_common": "Equipe Amigo - Caio <PERSON>", "most_common_count": 23, "null_count": 0, "null_percentage": 0.0}, "tipo_de_atendimento": {"unique_values": 18, "most_common": "CONSULTA  COM CARDIOLOGISTA", "most_common_count": 23, "null_count": 0, "null_percentage": 0.0}, "qtd_procedimento": {"unique_values": 26, "most_common": "1 - Consulta - 10101012", "most_common_count": 21, "null_count": 0, "null_percentage": 0.0}, "forma_de_pagamento": {"unique_values": 9, "most_common": "Pix", "most_common_count": 20, "null_count": 0, "null_percentage": 0.0}, "valor_r": {"min": 2.5, "max": 20000.0, "mean": 779.1138888888888, "null_count": 0, "null_percentage": 0.0}}, "column_samples": {"data_atendimento": ["09/04/2025 13:30", "08/04/2025 08:00", "07/04/2025 12:00", "07/04/2025 08:00", "07/04/2025 09:00"], "codigo_atendimento": [154993622, 153612551, 153618725, 152628845, 155571565], "unidade": ["BRASILIA", "BRASILIA", "Morumbi", "BRASILIA", "BRASILIA"], "paciente": ["thais horst capistrano", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "taciana camelo da cosra", "teste marco felipe <PERSON>"], "profissional": ["Equipe Amigo - Caio <PERSON>", "Equipe Amigo - <PERSON>", "Equipe Amigo - Al<PERSON>", "Equipe Amigo - <PERSON>", "Equipe Amigo - <PERSON><PERSON>"], "tipo_de_atendimento": ["30501369 - Septoplastia (qualquer técnica sem vídeo)", "Consulta - Amigo tech", "Consulta - Amigo tech", "CONSULTA  COM CARDIOLOGISTA", "Consulta - Amigo tech"], "qtd_procedimento": ["1 - Consulta amigo tech - teste", "1 - Consulta - 10101012, 1 - Audiometria tonal limiar infantil condicionada (qualquer técnica) - Peep-show", "1 - Consulta - 10101012", "1 - <PERSON><PERSON><PERSON> - teste", "1 - <PERSON><PERSON><PERSON> - teste"], "forma_de_pagamento": ["Pix", "Pendente", "AMIL", "BRADESCO", "Pix"], "valor_r": [800.0, 80.0, 300.0, 149.0, 20000.0]}, "potential_keys": ["codigo_atendimento", "unidade"], "potential_relationships": [{"column": "codigo_atendimento", "possible_related_entity": "atendimento"}], "analyzed_at": "2025-04-23T23:41:59.972403"}, {"file_name": "relatorio_produtividade.xlsx", "module": "agenda", "row_count": 9, "column_count": 32, "columns": [{"original_name": "Cód. <PERSON>end<PERSON>", "normalized_name": "cod_atendimento"}, {"original_name": "<PERSON><PERSON><PERSON>", "normalized_name": "indice"}, {"original_name": "Agendado em", "normalized_name": "agendado_em"}, {"original_name": "Agendado por", "normalized_name": "agendado_por"}, {"original_name": "Con<PERSON>rmado em", "normalized_name": "confirmado_em"}, {"original_name": "Confirmado por", "normalized_name": "confirmado_por"}, {"original_name": "Data do Agendamento", "normalized_name": "data_do_agendamento"}, {"original_name": "Hora do Agendamento", "normalized_name": "hora_do_agendamento"}, {"original_name": "Unidade", "normalized_name": "unidade"}, {"original_name": "Sala", "normalized_name": "sala"}, {"original_name": "Profissional", "normalized_name": "profissional"}, {"original_name": "Sol. Interno", "normalized_name": "sol_interno"}, {"original_name": "Sol. Externo", "normalized_name": "sol_externo"}, {"original_name": "Paciente", "normalized_name": "paciente"}, {"original_name": "<PERSON><PERSON>", "normalized_name": "idade"}, {"original_name": "Como conheceu", "normalized_name": "como_conheceu"}, {"original_name": "ID Amigo", "normalized_name": "id_amigo"}, {"original_name": "Cod<PERSON>", "normalized_name": "cod_legado"}, {"original_name": "CPF", "normalized_name": "cpf"}, {"original_name": "Telefone", "normalized_name": "telefone"}, {"original_name": "E-mail", "normalized_name": "e_mail"}, {"original_name": "<PERSON><PERSON><PERSON><PERSON>", "normalized_name": "matricula"}, {"original_name": "Tipo de Atendimento", "normalized_name": "tipo_de_atendimento"}, {"original_name": "Tipo do Item", "normalized_name": "tipo_do_item"}, {"original_name": "Qtd. Item", "normalized_name": "qtd_item"}, {"original_name": "<PERSON><PERSON>", "normalized_name": "item"}, {"original_name": "Status do Agendamento", "normalized_name": "status_do_agendamento"}, {"original_name": "Forma de Pagamento", "normalized_name": "forma_de_pagamento"}, {"original_name": "<PERSON><PERSON><PERSON>", "normalized_name": "tipo_de_guia"}, {"original_name": "Acomodação", "normalized_name": "acomodacao"}, {"original_name": "Editor <PERSON><PERSON>", "normalized_name": "editor_financeiro"}, {"original_name": "Valor", "normalized_name": "valor"}], "column_types": {"cod_atendimento": "int64", "indice": "int64", "agendado_em": "datetime64[ns]", "agendado_por": "object", "confirmado_em": "float64", "confirmado_por": "float64", "data_do_agendamento": "datetime64[ns]", "hora_do_agendamento": "datetime64[ns]", "unidade": "object", "sala": "object", "profissional": "object", "sol_interno": "object", "sol_externo": "object", "paciente": "object", "idade": "object", "como_conheceu": "float64", "id_amigo": "int64", "cod_legado": "object", "cpf": "object", "telefone": "object", "e_mail": "object", "matricula": "object", "tipo_de_atendimento": "object", "tipo_do_item": "object", "qtd_item": "int64", "item": "object", "status_do_agendamento": "object", "forma_de_pagamento": "object", "tipo_de_guia": "object", "acomodacao": "object", "editor_financeiro": "object", "valor": "float64"}, "column_stats": {"cod_atendimento": {"min": 151314579.0, "max": 155723069.0, "mean": 154295024.2222222, "null_count": 0, "null_percentage": 0.0}, "indice": {"min": 1.0, "max": 8.0, "mean": 4.111111111111111, "null_count": 0, "null_percentage": 0.0}, "agendado_em": {"unique_values": 5, "most_common": "17/04/2025", "most_common_count": 2, "null_count": 0, "null_percentage": 0.0}, "agendado_por": {"unique_values": 5, "most_common": "Equipe Amigo - Caio <PERSON>", "most_common_count": 4, "null_count": 0, "null_percentage": 0.0}, "confirmado_em": {"min": null, "max": null, "mean": null, "null_count": 9, "null_percentage": 100.0}, "confirmado_por": {"min": null, "max": null, "mean": null, "null_count": 9, "null_percentage": 100.0}, "data_do_agendamento": {"unique_values": 1, "most_common": "22/04/2025", "most_common_count": 9, "null_count": 0, "null_percentage": 0.0}, "hora_do_agendamento": {"unique_values": 5, "most_common": "10:00", "most_common_count": 3, "null_count": 0, "null_percentage": 0.0}, "unidade": {"unique_values": 3, "most_common": "Morumbi", "most_common_count": 4, "null_count": 0, "null_percentage": 0.0}, "sala": {"unique_values": 3, "most_common": "-", "most_common_count": 7, "null_count": 0, "null_percentage": 0.0}, "profissional": {"unique_values": 4, "most_common": "Equipe Amigo - Caio <PERSON>", "most_common_count": 4, "null_count": 0, "null_percentage": 0.0}, "sol_interno": {"unique_values": 1, "most_common": "-", "most_common_count": 9, "null_count": 0, "null_percentage": 0.0}, "sol_externo": {"unique_values": 1, "most_common": "-", "most_common_count": 9, "null_count": 0, "null_percentage": 0.0}, "paciente": {"unique_values": 7, "most_common": "AMANDA 1704", "most_common_count": 2, "null_count": 0, "null_percentage": 0.0}, "idade": {"unique_values": 2, "most_common": NaN, "most_common_count": 6, "null_count": 6, "null_percentage": 66.66666666666666}, "como_conheceu": {"min": null, "max": null, "mean": null, "null_count": 9, "null_percentage": 100.0}, "id_amigo": {"min": 9947091.0, "max": 90802077.0, "mean": 64400665.666666664, "null_count": 0, "null_percentage": 0.0}, "cod_legado": {"unique_values": 1, "most_common": "-", "most_common_count": 9, "null_count": 0, "null_percentage": 0.0}, "cpf": {"unique_values": 4, "most_common": "-", "most_common_count": 5, "null_count": 0, "null_percentage": 0.0}, "telefone": {"unique_values": 6, "most_common": "-", "most_common_count": 2, "null_count": 0, "null_percentage": 0.0}, "e_mail": {"unique_values": 4, "most_common": "-", "most_common_count": 4, "null_count": 0, "null_percentage": 0.0}, "matricula": {"unique_values": 4, "most_common": "-", "most_common_count": 5, "null_count": 0, "null_percentage": 0.0}, "tipo_de_atendimento": {"unique_values": 4, "most_common": "Sessao de Fisioterapia", "most_common_count": 5, "null_count": 0, "null_percentage": 0.0}, "tipo_do_item": {"unique_values": 2, "most_common": "Procedimento", "most_common_count": 7, "null_count": 0, "null_percentage": 0.0}, "qtd_item": {"min": 1.0, "max": 1.0, "mean": 1.0, "null_count": 0, "null_percentage": 0.0}, "item": {"unique_values": 4, "most_common": "<PERSON><PERSON><PERSON> - teste", "most_common_count": 5, "null_count": 0, "null_percentage": 0.0}, "status_do_agendamento": {"unique_values": 1, "most_common": "Agendado", "most_common_count": 9, "null_count": 0, "null_percentage": 0.0}, "forma_de_pagamento": {"unique_values": 5, "most_common": "Pendente", "most_common_count": 4, "null_count": 0, "null_percentage": 0.0}, "tipo_de_guia": {"unique_values": 2, "most_common": "-", "most_common_count": 8, "null_count": 0, "null_percentage": 0.0}, "acomodacao": {"unique_values": 1, "most_common": "-", "most_common_count": 9, "null_count": 0, "null_percentage": 0.0}, "editor_financeiro": {"unique_values": 5, "most_common": "Equipe Amigo - Caio <PERSON>", "most_common_count": 4, "null_count": 0, "null_percentage": 0.0}, "valor": {"min": 0.0, "max": 6506.13, "mean": 1055.3877777777777, "null_count": 0, "null_percentage": 0.0}}, "column_samples": {"cod_atendimento": [155723069, 153827808, 153786350, 155491950, 155723069], "indice": [1, 5, 8, 7, 2], "agendado_em": ["17/04/2025", "07/04/2025", "17/04/2025", "16/04/2025", "08/04/2025"], "agendado_por": ["Plano de Tratamento", "Plano de Tratamento", "<PERSON><PERSON>", "Equipe Amigo - Caio <PERSON>", "Equipe Amigo - Caio <PERSON>"], "confirmado_em": [], "confirmado_por": [], "data_do_agendamento": ["22/04/2025", "22/04/2025", "22/04/2025", "22/04/2025", "22/04/2025"], "hora_do_agendamento": ["12:00", "10:00", "15:00", "16:00", "15:00"], "unidade": ["Recife", "Recife", "BRASILIA", "Morumbi", "Morumbi"], "sala": ["-", "-", "SL TO", "-", "SL ABA"], "profissional": ["Equipe Amigo - Caio <PERSON>", "Equipe Amigo - Caio <PERSON>", "Equipe Amigo - Giulia Pedrosa 2", "Equipe Amigo - Caio <PERSON>", "Equipe Amigo - Ana Victoria <PERSON> Almeida"], "sol_interno": ["-", "-", "-", "-", "-"], "sol_externo": ["-", "-", "-", "-", "-"], "paciente": ["ACRISIO JP TESTE", "<PERSON><PERSON>", "Victória Almeida", "AMANDA 1704", "testse"], "idade": ["30 anos", "43 anos", "43 anos"], "como_conheceu": [], "id_amigo": [90802077, 9947091, 90154592, 90155706, 77719863], "cod_legado": ["-", "-", "-", "-", "-"], "cpf": ["10880304405", "-", "-", "-", "-"], "telefone": ["-", "(11) 94020-9917", "-", "(11) 94441-1411", "(81) 99797-0334"], "e_mail": ["-", "-", "-", "<EMAIL>", "-"], "matricula": ["-", "3213213213", "-", "3213213213", "-"], "tipo_de_atendimento": ["PSICOLOGIA ABA", "Sessao de Fisioterapia", "Sessao de Fisioterapia", "Sessao de Fisioterapia", "Sessao de Fisioterapia"], "tipo_do_item": ["Procedimento", "Procedimento", "Procedimento", "-", "Procedimento"], "qtd_item": [1, 1, 1, 1, 1], "item": ["<PERSON><PERSON><PERSON> - teste", "<PERSON><PERSON><PERSON> - teste", "<PERSON><PERSON><PERSON> - teste", "-", "Anestesia"], "status_do_agendamento": ["Agendado", "Agendado", "Agendado", "Agendado", "Agendado"], "forma_de_pagamento": ["Pendente", "Crédito de Procedimento", "Cartão", "Pendente", "Pendente"], "tipo_de_guia": ["-", "SADT", "-", "-", "-"], "acomodacao": ["-", "-", "-", "-", "-"], "editor_financeiro": ["<PERSON><PERSON>", "Equipe Amigo - Caio <PERSON>", "-", "-", "Equipe Amigo - Caio <PERSON>"], "valor": [100.0, 2502.36, 100.0, 6506.13, 0.0]}, "potential_keys": ["cod_atendimento", "unidade", "idade", "id_amigo", "cod_legado"], "potential_relationships": [{"column": "id_amigo", "possible_related_entity": "amigo"}], "analyzed_at": "2025-04-23T23:41:59.992280"}, {"file_name": "relatorio_cancelamentos_falttantes_reagendamento.xlsx", "module": "agenda", "row_count": 12, "column_count": 7, "columns": [{"original_name": "CONSULTORIO MEDICO", "normalized_name": "consultorio_medico"}, {"original_name": "Unnamed: 1", "normalized_name": "unnamed_1"}, {"original_name": "Unnamed: 2", "normalized_name": "unnamed_2"}, {"original_name": "Unnamed: 3", "normalized_name": "unnamed_3"}, {"original_name": "Unnamed: 4", "normalized_name": "unnamed_4"}, {"original_name": "Unnamed: 5", "normalized_name": "unnamed_5"}, {"original_name": "Unnamed: 6", "normalized_name": "unnamed_6"}], "column_types": {"consultorio_medico": "object", "unnamed_1": "object", "unnamed_2": "object", "unnamed_3": "object", "unnamed_4": "object", "unnamed_5": "object", "unnamed_6": "object"}, "column_stats": {"consultorio_medico": {"unique_values": 7, "most_common": NaN, "most_common_count": 4, "null_count": 4, "null_percentage": 33.33333333333333}, "unnamed_1": {"unique_values": 3, "most_common": NaN, "most_common_count": 8, "null_count": 8, "null_percentage": 66.66666666666666}, "unnamed_2": {"unique_values": 3, "most_common": NaN, "most_common_count": 8, "null_count": 8, "null_percentage": 66.66666666666666}, "unnamed_3": {"unique_values": 3, "most_common": NaN, "most_common_count": 8, "null_count": 8, "null_percentage": 66.66666666666666}, "unnamed_4": {"unique_values": 2, "most_common": NaN, "most_common_count": 8, "null_count": 8, "null_percentage": 66.66666666666666}, "unnamed_5": {"unique_values": 3, "most_common": NaN, "most_common_count": 8, "null_count": 8, "null_percentage": 66.66666666666666}, "unnamed_6": {"unique_values": 3, "most_common": NaN, "most_common_count": 8, "null_count": 8, "null_percentage": 66.66666666666666}}, "column_samples": {"consultorio_medico": ["Data entre: 20/04/2025 e 26/04/2025 (2)", "Data", "Data", "Equipe Amigo - <PERSON> (1)", "Relatório de Cancelamentos, faltantes e reagendamentos"], "unnamed_1": ["Paciente", "<PERSON>", "Paciente", "Junior Souza"], "unnamed_2": ["Telefone", "Telefone", "(11) 97505-4988", "(31) 99572-0954"], "unnamed_3": ["Tipo de Atendimento", "Fisioterapia", "Tipo de Atendimento", "Sessão Psicologia"], "unnamed_4": ["Unidade", "Morumbi", "Unidade", "Morumbi"], "unnamed_5": ["1ª Sessão de psicoterapia", "Procedimento", "Sessão para assistência fisioterapêutica ambulatorial ao paciente com disfunção decorrente de alterações do sistema músculo-esquelético, Sessão para assistência fisioterapêutica ambulatorial para alterações inflamatórias e ou degenerativas do aparelho genito-urinário e reprodutor", "Procedimento"], "unnamed_6": ["Status", "Status", "Cancelou", "Reagendado para o dia 23/04/2025"]}, "potential_keys": [], "potential_relationships": [], "analyzed_at": "2025-04-23T23:42:00.017987"}, {"file_name": "relatorio_tempo_de_atendimento.xlsx", "module": "agenda", "row_count": 48, "column_count": 14, "columns": [{"original_name": "Cód. <PERSON>end<PERSON>", "normalized_name": "cod_atendimento"}, {"original_name": "Unidade", "normalized_name": "unidade"}, {"original_name": "Profissional", "normalized_name": "profissional"}, {"original_name": "Paciente", "normalized_name": "paciente"}, {"original_name": "ID paciente", "normalized_name": "id_paciente"}, {"original_name": "Tipo de Atendimento", "normalized_name": "tipo_de_atendimento"}, {"original_name": "Data do Agendamento", "normalized_name": "data_do_agendamento"}, {"original_name": "Hora do Agendamento", "normalized_name": "hora_do_agendamento"}, {"original_name": "Hora de chegada", "normalized_name": "hora_de_chegada"}, {"original_name": "Inicio do atendimento", "normalized_name": "inicio_do_atendimento"}, {"original_name": "Fim do atendimento", "normalized_name": "fim_do_atendimento"}, {"original_name": "Espera em minutos", "normalized_name": "espera_em_minutos"}, {"original_name": "Atraso em minutos", "normalized_name": "atraso_em_minutos"}, {"original_name": "Tempo de Atendimento em minutos", "normalized_name": "tempo_de_atendimento_em_minutos"}], "column_types": {"cod_atendimento": "int64", "unidade": "object", "profissional": "object", "paciente": "object", "id_paciente": "int64", "tipo_de_atendimento": "object", "data_do_agendamento": "datetime64[ns]", "hora_do_agendamento": "datetime64[ns]", "hora_de_chegada": "object", "inicio_do_atendimento": "datetime64[ns]", "fim_do_atendimento": "datetime64[ns]", "espera_em_minutos": "object", "atraso_em_minutos": "object", "tempo_de_atendimento_em_minutos": "int64"}, "column_stats": {"cod_atendimento": {"min": 146952664.0, "max": 155741330.0, "mean": 152826778.875, "null_count": 0, "null_percentage": 0.0}, "unidade": {"unique_values": 4, "most_common": "BRASILIA", "most_common_count": 33, "null_count": 0, "null_percentage": 0.0}, "profissional": {"unique_values": 11, "most_common": "Equipe Amigo - Caio <PERSON>", "most_common_count": 17, "null_count": 0, "null_percentage": 0.0}, "paciente": {"unique_values": 41, "most_common": "<PERSON><PERSON>", "most_common_count": 3, "null_count": 0, "null_percentage": 0.0}, "id_paciente": {"min": 393011.0, "max": 90817169.0, "mean": 77325264.1875, "null_count": 0, "null_percentage": 0.0}, "tipo_de_atendimento": {"unique_values": 14, "most_common": "CONSULTA  COM CARDIOLOGISTA", "most_common_count": 16, "null_count": 0, "null_percentage": 0.0}, "data_do_agendamento": {"unique_values": 19, "most_common": "27/03/2025", "most_common_count": 6, "null_count": 0, "null_percentage": 0.0}, "hora_do_agendamento": {"unique_values": 23, "most_common": "08:00", "most_common_count": 7, "null_count": 0, "null_percentage": 0.0}, "hora_de_chegada": {"unique_values": 29, "most_common": "-", "most_common_count": 18, "null_count": 0, "null_percentage": 0.0}, "inicio_do_atendimento": {"unique_values": 48, "most_common": "18:27", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "fim_do_atendimento": {"unique_values": 48, "most_common": "18:35", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "espera_em_minutos": {"unique_values": 4, "most_common": "-", "most_common_count": 36, "null_count": 0, "null_percentage": 0.0}, "atraso_em_minutos": {"unique_values": 20, "most_common": "-", "most_common_count": 28, "null_count": 0, "null_percentage": 0.0}, "tempo_de_atendimento_em_minutos": {"min": 1.0, "max": 63.0, "mean": 11.5, "null_count": 0, "null_percentage": 0.0}}, "column_samples": {"cod_atendimento": [153715242, 152421869, 151442649, 151775779, 151777985], "unidade": ["BRASILIA", "Recife", "Morumbi", "Morumbi", "BRASILIA"], "profissional": ["Equipe Amigo - <PERSON>", "Equipe Amigo - Caio <PERSON>", "Equipe Amigo - <PERSON>", "Equipe Amigo - <PERSON><PERSON>", "Equipe Amigo - Caio <PERSON>"], "paciente": ["JULIA EYER", "thais horst capistrano", "FERNANDA GOMES", "Dr. <PERSON><PERSON>", "<PERSON><PERSON>"], "id_paciente": [90090896, 48947847, 90212027, 89260601, 89225796], "tipo_de_atendimento": ["CONSULTA  COM CARDIOLOGISTA", "Consulta - Amigo tech", "Consulta - Amigo tech", "Consulta - Amigo tech", "CONSULTA  COM CARDIOLOGISTA"], "data_do_agendamento": ["27/03/2025", "24/03/2025", "02/04/2025", "07/04/2025", "24/03/2025"], "hora_do_agendamento": ["15:00", "08:20", "14:00", "13:40", "08:00"], "hora_de_chegada": ["09:45", "13:24", "12:04", "17:24", "14:15"], "inicio_do_atendimento": ["19:47", "12:33", "14:35", "18:53", "11:20"], "fim_do_atendimento": ["16:49", "17:43", "12:05", "18:16", "12:50"], "espera_em_minutos": ["-", 1, "-", 2, "-"], "atraso_em_minutos": ["-", "-", 9, "-", 559], "tempo_de_atendimento_em_minutos": [7, 28, 8, 26, 63]}, "potential_keys": ["cod_atendimento", "unidade", "id_paciente", "inicio_do_atendimento", "fim_do_atendimento"], "potential_relationships": [{"column": "id_paciente", "possible_related_entity": "paciente"}], "analyzed_at": "2025-04-23T23:42:00.044055"}, {"file_name": "relatorio_mapacirurgico.xlsx", "module": "agenda", "row_count": 14, "column_count": 28, "columns": [{"original_name": "Data do agendamento", "normalized_name": "data_do_agendamento"}, {"original_name": "<PERSON><PERSON>", "normalized_name": "hora"}, {"original_name": "Unidade", "normalized_name": "unidade"}, {"original_name": "Sala", "normalized_name": "sala"}, {"original_name": "Cód. Leg.", "normalized_name": "cod_leg"}, {"original_name": "Paciente", "normalized_name": "paciente"}, {"original_name": "<PERSON><PERSON>", "normalized_name": "idade"}, {"original_name": "Telefone", "normalized_name": "telefone"}, {"original_name": "Tipo de Atendimento", "normalized_name": "tipo_de_atendimento"}, {"original_name": "Qtd/Procedimento", "normalized_name": "qtd_procedimento"}, {"original_name": "Forma de Pagamento", "normalized_name": "forma_de_pagamento"}, {"original_name": "Acomod.", "normalized_name": "acomod"}, {"original_name": "<PERSON><PERSON><PERSON><PERSON>", "normalized_name": "matricula"}, {"original_name": "<PERSON><PERSON><PERSON>", "normalized_name": "atend_confirmado"}, {"original_name": "00 - <PERSON><PERSON><PERSON><PERSON><PERSON>", "normalized_name": "00_cirurgiao"}, {"original_name": "01 - 1º Auxiliar", "normalized_name": "01_1º_auxiliar"}, {"original_name": "02 - 2º Auxiliar", "normalized_name": "02_2º_auxiliar"}, {"original_name": "03 - 3º Auxiliar", "normalized_name": "03_3º_auxiliar"}, {"original_name": "04 - 4º Auxiliar", "normalized_name": "04_4º_auxiliar"}, {"original_name": "05 - Instrument<PERSON>", "normalized_name": "05_instrumentador"}, {"original_name": "06 - <PERSON><PERSON><PERSON><PERSON>", "normalized_name": "06_anestesista"}, {"original_name": "07 - <PERSON><PERSON><PERSON><PERSON> Anestesista", "normalized_name": "07_auxiliar_de_anestesista"}, {"original_name": "08 - Consultor", "normalized_name": "08_consultor"}, {"original_name": "09 - <PERSON><PERSON><PERSON>", "normalized_name": "09_perfusionista"}, {"original_name": "10 - Pediatra na sala de parto", "normalized_name": "10_pediatra_na_sala_de_parto"}, {"original_name": "11 - Auxiliar SADT", "normalized_name": "11_auxiliar_sadt"}, {"original_name": "12 - Clínico", "normalized_name": "12_clinico"}, {"original_name": "13 - Intensivista", "normalized_name": "13_intensivista"}], "column_types": {"data_do_agendamento": "datetime64[ns]", "hora": "datetime64[ns]", "unidade": "object", "sala": "float64", "cod_leg": "float64", "paciente": "object", "idade": "object", "telefone": "object", "tipo_de_atendimento": "object", "qtd_procedimento": "object", "forma_de_pagamento": "object", "acomod": "float64", "matricula": "float64", "atend_confirmado": "bool", "00_cirurgiao": "object", "01_1º_auxiliar": "float64", "02_2º_auxiliar": "float64", "03_3º_auxiliar": "float64", "04_4º_auxiliar": "float64", "05_instrumentador": "float64", "06_anestesista": "float64", "07_auxiliar_de_anestesista": "float64", "08_consultor": "float64", "09_perfusionista": "float64", "10_pediatra_na_sala_de_parto": "float64", "11_auxiliar_sadt": "float64", "12_clinico": "float64", "13_intensivista": "float64"}, "column_stats": {"data_do_agendamento": {"unique_values": 4, "most_common": "25/04/2025", "most_common_count": 5, "null_count": 0, "null_percentage": 0.0}, "hora": {"unique_values": 6, "most_common": "10:00", "most_common_count": 5, "null_count": 0, "null_percentage": 0.0}, "unidade": {"unique_values": 4, "most_common": "Morumbi", "most_common_count": 8, "null_count": 0, "null_percentage": 0.0}, "sala": {"min": null, "max": null, "mean": null, "null_count": 14, "null_percentage": 100.0}, "cod_leg": {"min": null, "max": null, "mean": null, "null_count": 14, "null_percentage": 100.0}, "paciente": {"unique_values": 13, "most_common": "MARINA HAZIN", "most_common_count": 2, "null_count": 0, "null_percentage": 0.0}, "idade": {"unique_values": 3, "most_common": NaN, "most_common_count": 11, "null_count": 11, "null_percentage": 78.57142857142857}, "telefone": {"unique_values": 6, "most_common": NaN, "most_common_count": 6, "null_count": 6, "null_percentage": 42.857142857142854}, "tipo_de_atendimento": {"unique_values": 1, "most_common": "Sessao de Fisioterapia", "most_common_count": 14, "null_count": 0, "null_percentage": 0.0}, "qtd_procedimento": {"unique_values": 1, "most_common": "1 - <PERSON><PERSON><PERSON> - teste", "most_common_count": 14, "null_count": 0, "null_percentage": 0.0}, "forma_de_pagamento": {"unique_values": 4, "most_common": NaN, "most_common_count": 7, "null_count": 7, "null_percentage": 50.0}, "acomod": {"min": null, "max": null, "mean": null, "null_count": 14, "null_percentage": 100.0}, "matricula": {"min": 132465.0, "max": 782424624646.0, "mean": 156491708860.0, "null_count": 9, "null_percentage": 64.28571428571429}, "atend_confirmado": {"unique_values": 2, "most_common": "Não", "most_common_count": 13, "null_count": 0, "null_percentage": 0.0}, "00_cirurgiao": {"unique_values": 5, "most_common": "Equipe Amigo - BRUNO LIMA", "most_common_count": 6, "null_count": 0, "null_percentage": 0.0}, "01_1º_auxiliar": {"min": null, "max": null, "mean": null, "null_count": 14, "null_percentage": 100.0}, "02_2º_auxiliar": {"min": null, "max": null, "mean": null, "null_count": 14, "null_percentage": 100.0}, "03_3º_auxiliar": {"min": null, "max": null, "mean": null, "null_count": 14, "null_percentage": 100.0}, "04_4º_auxiliar": {"min": null, "max": null, "mean": null, "null_count": 14, "null_percentage": 100.0}, "05_instrumentador": {"min": null, "max": null, "mean": null, "null_count": 14, "null_percentage": 100.0}, "06_anestesista": {"min": null, "max": null, "mean": null, "null_count": 14, "null_percentage": 100.0}, "07_auxiliar_de_anestesista": {"min": null, "max": null, "mean": null, "null_count": 14, "null_percentage": 100.0}, "08_consultor": {"min": null, "max": null, "mean": null, "null_count": 14, "null_percentage": 100.0}, "09_perfusionista": {"min": null, "max": null, "mean": null, "null_count": 14, "null_percentage": 100.0}, "10_pediatra_na_sala_de_parto": {"min": null, "max": null, "mean": null, "null_count": 14, "null_percentage": 100.0}, "11_auxiliar_sadt": {"min": null, "max": null, "mean": null, "null_count": 14, "null_percentage": 100.0}, "12_clinico": {"min": null, "max": null, "mean": null, "null_count": 14, "null_percentage": 100.0}, "13_intensivista": {"min": null, "max": null, "mean": null, "null_count": 14, "null_percentage": 100.0}}, "column_samples": {"data_do_agendamento": ["25/04/2025", "21/04/2025", "23/04/2025", "21/04/2025", "22/04/2025"], "hora": ["10:00", "10:00", "17:00", "15:00", "17:00"], "unidade": ["Morumbi", "Morumbi", "BRASILIA", "CLÍNICA (RL)", "Recife"], "sala": [], "cod_leg": [], "paciente": ["<PERSON> teste DF", "MARINA HAZIN CONVÊNIO", "Victória Almeida", "<PERSON> teste", "<PERSON><PERSON>"], "idade": ["29 anos", "30 anos", "37 anos"], "telefone": ["(11) 94020-9917", "(81) 99696-4897", "(11) 97505-4988", "(81) 99696-4897", "(21) 99435-2590"], "tipo_de_atendimento": ["Sessao de Fisioterapia", "Sessao de Fisioterapia", "Sessao de Fisioterapia", "Sessao de Fisioterapia", "Sessao de Fisioterapia"], "qtd_procedimento": ["1 - <PERSON><PERSON><PERSON> - teste", "1 - <PERSON><PERSON><PERSON> - teste", "1 - <PERSON><PERSON><PERSON> - teste", "1 - <PERSON><PERSON><PERSON> - teste", "1 - <PERSON><PERSON><PERSON> - teste"], "forma_de_pagamento": ["Pix", "BRADESCO", "Cartão", "BRADESCO", "AMIL"], "acomod": [], "matricula": [21321321.0, 231312.0, 12234556.0, 782424624646.0, 132465.0], "atend_confirmado": ["Não", "Não", "Não", "Não", "Não"], "00_cirurgiao": ["Equipe Amigo - BRUNO LIMA", "Equipe Amigo - BRUNO LIMA", "Rayara <PERSON> Souza", "Equipe Amigo - Giulia Pedrosa 2", "Equipe Amigo - Caio <PERSON>"], "01_1º_auxiliar": [], "02_2º_auxiliar": [], "03_3º_auxiliar": [], "04_4º_auxiliar": [], "05_instrumentador": [], "06_anestesista": [], "07_auxiliar_de_anestesista": [], "08_consultor": [], "09_perfusionista": [], "10_pediatra_na_sala_de_parto": [], "11_auxiliar_sadt": [], "12_clinico": [], "13_intensivista": []}, "potential_keys": ["unidade", "cod_leg", "idade"], "potential_relationships": [], "analyzed_at": "2025-04-23T23:42:00.060745"}, {"file_name": "relatorio_agendamento.xlsx", "module": "agenda", "row_count": 40, "column_count": 13, "columns": [{"original_name": "Data do agendamento", "normalized_name": "data_do_agendamento"}, {"original_name": "<PERSON><PERSON>", "normalized_name": "hora"}, {"original_name": "Unidade", "normalized_name": "unidade"}, {"original_name": "Cód. Leg.", "normalized_name": "cod_leg"}, {"original_name": "Paciente", "normalized_name": "paciente"}, {"original_name": "CPF", "normalized_name": "cpf"}, {"original_name": "Telefone", "normalized_name": "telefone"}, {"original_name": "Email", "normalized_name": "email"}, {"original_name": "Médico", "normalized_name": "medico"}, {"original_name": "Tipo de Atendimento", "normalized_name": "tipo_de_atendimento"}, {"original_name": "<PERSON><PERSON><PERSON><PERSON>", "normalized_name": "convenio"}, {"original_name": "<PERSON><PERSON><PERSON><PERSON>", "normalized_name": "matricula"}, {"original_name": "Status", "normalized_name": "status"}], "column_types": {"data_do_agendamento": "datetime64[ns]", "hora": "datetime64[ns]", "unidade": "object", "cod_leg": "float64", "paciente": "object", "cpf": "float64", "telefone": "object", "email": "object", "medico": "object", "tipo_de_atendimento": "object", "convenio": "object", "matricula": "float64", "status": "object"}, "column_stats": {"data_do_agendamento": {"unique_values": 5, "most_common": "21/04/2025", "most_common_count": 13, "null_count": 0, "null_percentage": 0.0}, "hora": {"unique_values": 13, "most_common": "10:00", "most_common_count": 12, "null_count": 0, "null_percentage": 0.0}, "unidade": {"unique_values": 4, "most_common": "Morumbi", "most_common_count": 20, "null_count": 0, "null_percentage": 0.0}, "cod_leg": {"min": null, "max": null, "mean": null, "null_count": 40, "null_percentage": 100.0}, "paciente": {"unique_values": 26, "most_common": "22115244 - ACRISIO JP TESTE", "most_common_count": 10, "null_count": 0, "null_percentage": 0.0}, "cpf": {"min": 764598457.0, "max": 97712877025.0, "mean": 19429422031.88, "null_count": 15, "null_percentage": 37.5}, "telefone": {"unique_values": 14, "most_common": "(11) 94441-1411", "most_common_count": 10, "null_count": 8, "null_percentage": 20.0}, "email": {"unique_values": 9, "most_common": NaN, "most_common_count": 14, "null_count": 14, "null_percentage": 35.0}, "medico": {"unique_values": 10, "most_common": "Plano de Tratamento", "most_common_count": 12, "null_count": 0, "null_percentage": 0.0}, "tipo_de_atendimento": {"unique_values": 10, "most_common": "Sessao de Fisioterapia", "most_common_count": 14, "null_count": 0, "null_percentage": 0.0}, "convenio": {"unique_values": 3, "most_common": "Particular", "most_common_count": 36, "null_count": 0, "null_percentage": 0.0}, "matricula": {"min": 132465.0, "max": 782424624646.0, "mean": 120169550571.5, "null_count": 18, "null_percentage": 45.0}, "status": {"unique_values": 3, "most_common": "<PERSON>ão confirmado", "most_common_count": 37, "null_count": 0, "null_percentage": 0.0}}, "column_samples": {"data_do_agendamento": ["23/04/2025", "22/04/2025", "21/04/2025", "25/04/2025", "22/04/2025"], "hora": ["14:00", "10:00", "18:00", "16:00", "12:00"], "unidade": ["Morumbi", "Morumbi", "Recife", "Recife", "Recife"], "cod_leg": [], "paciente": ["88801630 - <PERSON>", "56642251 - ADELMO TESTE", "56642251 - ADELMO TESTE", "22115244 - ACRISIO JP TESTE", "90829899 - <PERSON> teste DF"], "cpf": [764598457.0, 11577195701.0, 764598457.0, 62881978070.0, 764598457.0], "telefone": ["(81) 99797-0334", "(11) 94441-1411", "(81) 99730-4365", "(11) 94441-1411", "(11) 97505-4988"], "email": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "medico": ["Equipe Amigo - Giulia Pedrosa 2", "Equipe Amigo - Caio <PERSON>", "Equipe Amigo - BRUNO LIMA", "Plano de Tratamento", "Equipe Amigo - Giulia Pedrosa 2"], "tipo_de_atendimento": ["TERAPIA OCUPACIONAL", "Fisioterapia", "TERAPIA OCUPACIONAL", "CONSULTA COM ORTOPEDISTA", "BOTOX"], "convenio": ["Particular", "Particular", "Particular", "Particular", "Particular"], "matricula": [12234556.0, 132132132132.0, 3213213213.0, 3213213213.0, 1231231.0], "status": ["<PERSON>ão confirmado", "<PERSON>ão confirmado", "<PERSON>ão confirmado", "<PERSON>ão confirmado", "<PERSON>ão confirmado"]}, "potential_keys": ["unidade", "cod_leg"], "potential_relationships": [], "analyzed_at": "2025-04-23T23:42:00.076356"}, {"file_name": "relatorio_agendamento_novo.xlsx", "module": "agenda", "row_count": 9, "column_count": 30, "columns": [{"original_name": "Cód. <PERSON>end<PERSON>", "normalized_name": "cod_atendimento"}, {"original_name": "<PERSON><PERSON><PERSON>", "normalized_name": "indice"}, {"original_name": "Agendado em", "normalized_name": "agendado_em"}, {"original_name": "Agendado por", "normalized_name": "agendado_por"}, {"original_name": "Con<PERSON>rmado em", "normalized_name": "confirmado_em"}, {"original_name": "Confirmado por", "normalized_name": "confirmado_por"}, {"original_name": "Data do Agendamento", "normalized_name": "data_do_agendamento"}, {"original_name": "Hora do Agendamento", "normalized_name": "hora_do_agendamento"}, {"original_name": "Unidade", "normalized_name": "unidade"}, {"original_name": "Sala", "normalized_name": "sala"}, {"original_name": "Profissional", "normalized_name": "profissional"}, {"original_name": "Sol. Interno", "normalized_name": "sol_interno"}, {"original_name": "Sol. Externo", "normalized_name": "sol_externo"}, {"original_name": "Paciente", "normalized_name": "paciente"}, {"original_name": "<PERSON><PERSON>", "normalized_name": "idade"}, {"original_name": "Como conheceu", "normalized_name": "como_conheceu"}, {"original_name": "ID Amigo", "normalized_name": "id_amigo"}, {"original_name": "Cod<PERSON>", "normalized_name": "cod_legado"}, {"original_name": "CPF", "normalized_name": "cpf"}, {"original_name": "Telefone", "normalized_name": "telefone"}, {"original_name": "E-mail", "normalized_name": "e_mail"}, {"original_name": "<PERSON><PERSON><PERSON><PERSON>", "normalized_name": "matricula"}, {"original_name": "Tipo de Atendimento", "normalized_name": "tipo_de_atendimento"}, {"original_name": "Tipo do Item", "normalized_name": "tipo_do_item"}, {"original_name": "Qtd. Item", "normalized_name": "qtd_item"}, {"original_name": "<PERSON><PERSON>", "normalized_name": "item"}, {"original_name": "Status do Agendamento", "normalized_name": "status_do_agendamento"}, {"original_name": "Forma de Pagamento", "normalized_name": "forma_de_pagamento"}, {"original_name": "<PERSON><PERSON><PERSON>", "normalized_name": "tipo_de_guia"}, {"original_name": "Acomodação", "normalized_name": "acomodacao"}], "column_types": {"cod_atendimento": "int64", "indice": "int64", "agendado_em": "datetime64[ns]", "agendado_por": "object", "confirmado_em": "float64", "confirmado_por": "float64", "data_do_agendamento": "datetime64[ns]", "hora_do_agendamento": "datetime64[ns]", "unidade": "object", "sala": "object", "profissional": "object", "sol_interno": "object", "sol_externo": "object", "paciente": "object", "idade": "object", "como_conheceu": "float64", "id_amigo": "int64", "cod_legado": "object", "cpf": "object", "telefone": "object", "e_mail": "object", "matricula": "object", "tipo_de_atendimento": "object", "tipo_do_item": "object", "qtd_item": "int64", "item": "object", "status_do_agendamento": "object", "forma_de_pagamento": "object", "tipo_de_guia": "object", "acomodacao": "object"}, "column_stats": {"cod_atendimento": {"min": 151314579.0, "max": 155723069.0, "mean": 154295024.2222222, "null_count": 0, "null_percentage": 0.0}, "indice": {"min": 1.0, "max": 8.0, "mean": 4.111111111111111, "null_count": 0, "null_percentage": 0.0}, "agendado_em": {"unique_values": 5, "most_common": "17/04/2025", "most_common_count": 2, "null_count": 0, "null_percentage": 0.0}, "agendado_por": {"unique_values": 5, "most_common": "Equipe Amigo - Caio <PERSON>", "most_common_count": 4, "null_count": 0, "null_percentage": 0.0}, "confirmado_em": {"min": null, "max": null, "mean": null, "null_count": 9, "null_percentage": 100.0}, "confirmado_por": {"min": null, "max": null, "mean": null, "null_count": 9, "null_percentage": 100.0}, "data_do_agendamento": {"unique_values": 1, "most_common": "22/04/2025", "most_common_count": 9, "null_count": 0, "null_percentage": 0.0}, "hora_do_agendamento": {"unique_values": 5, "most_common": "10:00", "most_common_count": 3, "null_count": 0, "null_percentage": 0.0}, "unidade": {"unique_values": 3, "most_common": "Morumbi", "most_common_count": 4, "null_count": 0, "null_percentage": 0.0}, "sala": {"unique_values": 3, "most_common": "-", "most_common_count": 7, "null_count": 0, "null_percentage": 0.0}, "profissional": {"unique_values": 4, "most_common": "Equipe Amigo - Caio <PERSON>", "most_common_count": 4, "null_count": 0, "null_percentage": 0.0}, "sol_interno": {"unique_values": 1, "most_common": "-", "most_common_count": 9, "null_count": 0, "null_percentage": 0.0}, "sol_externo": {"unique_values": 1, "most_common": "-", "most_common_count": 9, "null_count": 0, "null_percentage": 0.0}, "paciente": {"unique_values": 7, "most_common": "AMANDA 1704", "most_common_count": 2, "null_count": 0, "null_percentage": 0.0}, "idade": {"unique_values": 2, "most_common": NaN, "most_common_count": 6, "null_count": 6, "null_percentage": 66.66666666666666}, "como_conheceu": {"min": null, "max": null, "mean": null, "null_count": 9, "null_percentage": 100.0}, "id_amigo": {"min": 9947091.0, "max": 90802077.0, "mean": 64400665.666666664, "null_count": 0, "null_percentage": 0.0}, "cod_legado": {"unique_values": 1, "most_common": "-", "most_common_count": 9, "null_count": 0, "null_percentage": 0.0}, "cpf": {"unique_values": 4, "most_common": "-", "most_common_count": 5, "null_count": 0, "null_percentage": 0.0}, "telefone": {"unique_values": 6, "most_common": "-", "most_common_count": 2, "null_count": 0, "null_percentage": 0.0}, "e_mail": {"unique_values": 4, "most_common": "-", "most_common_count": 4, "null_count": 0, "null_percentage": 0.0}, "matricula": {"unique_values": 4, "most_common": "-", "most_common_count": 5, "null_count": 0, "null_percentage": 0.0}, "tipo_de_atendimento": {"unique_values": 4, "most_common": "Sessao de Fisioterapia", "most_common_count": 5, "null_count": 0, "null_percentage": 0.0}, "tipo_do_item": {"unique_values": 2, "most_common": "Procedimento", "most_common_count": 7, "null_count": 0, "null_percentage": 0.0}, "qtd_item": {"min": 1.0, "max": 1.0, "mean": 1.0, "null_count": 0, "null_percentage": 0.0}, "item": {"unique_values": 4, "most_common": "<PERSON><PERSON><PERSON> - teste", "most_common_count": 5, "null_count": 0, "null_percentage": 0.0}, "status_do_agendamento": {"unique_values": 1, "most_common": "Agendado", "most_common_count": 9, "null_count": 0, "null_percentage": 0.0}, "forma_de_pagamento": {"unique_values": 5, "most_common": "Pendente", "most_common_count": 4, "null_count": 0, "null_percentage": 0.0}, "tipo_de_guia": {"unique_values": 2, "most_common": "-", "most_common_count": 8, "null_count": 0, "null_percentage": 0.0}, "acomodacao": {"unique_values": 1, "most_common": "-", "most_common_count": 9, "null_count": 0, "null_percentage": 0.0}}, "column_samples": {"cod_atendimento": [151314579, 155491950, 153786350, 153827808, 153651062], "indice": [3, 5, 1, 4, 8], "agendado_em": ["08/04/2025", "07/04/2025", "08/04/2025", "16/04/2025", "17/04/2025"], "agendado_por": ["Equipe Amigo - Caio <PERSON>", "Equipe Amigo - Caio <PERSON>", "Plano de Tratamento", "Equipe Amigo - Caio <PERSON>", "<PERSON><PERSON>"], "confirmado_em": [], "confirmado_por": [], "data_do_agendamento": ["22/04/2025", "22/04/2025", "22/04/2025", "22/04/2025", "22/04/2025"], "hora_do_agendamento": ["15:00", "10:00", "08:00", "15:00", "12:00"], "unidade": ["Recife", "Recife", "Recife", "Morumbi", "Morumbi"], "sala": ["SL TO", "-", "-", "-", "-"], "profissional": ["Plano de Tratamento", "Equipe Amigo - Ana Victoria <PERSON> Almeida", "Plano de Tratamento", "Equipe Amigo - Giulia Pedrosa 2", "Equipe Amigo - Giulia Pedrosa 2"], "sol_interno": ["-", "-", "-", "-", "-"], "sol_externo": ["-", "-", "-", "-", "-"], "paciente": ["testse", "ACRISIO JP TESTE", "<PERSON><PERSON>", "ACRISIO JP TESTE", "MARINA HAZIN CONVÊNIO"], "idade": ["30 anos", "43 anos", "43 anos"], "como_conheceu": [], "id_amigo": [90155706, 9947091, 90802077, 90154592, 85794097], "cod_legado": ["-", "-", "-", "-", "-"], "cpf": ["-", "-", "10880304405", "00764598457", "-"], "telefone": ["(11) 94441-1411", "-", "(81) 99797-0334", "(21) 99435-2590", "-"], "e_mail": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "-"], "matricula": ["-", "-", "-", "782424624646", "-"], "tipo_de_atendimento": ["Sessao de Fisioterapia", "TERAPIA OCUPACIONAL", "BOTOX", "Sessao de Fisioterapia", "Sessao de Fisioterapia"], "tipo_do_item": ["Procedimento", "Procedimento", "Procedimento", "-", "Procedimento"], "qtd_item": [1, 1, 1, 1, 1], "item": ["Blefaroplastia", "<PERSON><PERSON><PERSON> - teste", "<PERSON><PERSON><PERSON> - teste", "-", "<PERSON><PERSON><PERSON> - teste"], "status_do_agendamento": ["Agendado", "Agendado", "Agendado", "Agendado", "Agendado"], "forma_de_pagamento": ["Pendente", "Pendente", "Pendente", "Crédito de Procedimento", "Pendente"], "tipo_de_guia": ["-", "-", "-", "-", "-"], "acomodacao": ["-", "-", "-", "-", "-"]}, "potential_keys": ["cod_atendimento", "unidade", "idade", "id_amigo", "cod_legado"], "potential_relationships": [{"column": "id_amigo", "possible_related_entity": "amigo"}], "analyzed_at": "2025-04-23T23:42:00.095946"}]
{"file_name": "relatorio_agendamento.xlsx", "module": "agenda", "row_count": 40, "column_count": 13, "columns": [{"original_name": "Data do agendamento", "normalized_name": "data_do_agendamento"}, {"original_name": "<PERSON><PERSON>", "normalized_name": "hora"}, {"original_name": "Unidade", "normalized_name": "unidade"}, {"original_name": "Cód. Leg.", "normalized_name": "cod_leg"}, {"original_name": "Paciente", "normalized_name": "paciente"}, {"original_name": "CPF", "normalized_name": "cpf"}, {"original_name": "Telefone", "normalized_name": "telefone"}, {"original_name": "Email", "normalized_name": "email"}, {"original_name": "Médico", "normalized_name": "medico"}, {"original_name": "Tipo de Atendimento", "normalized_name": "tipo_de_atendimento"}, {"original_name": "<PERSON><PERSON><PERSON><PERSON>", "normalized_name": "convenio"}, {"original_name": "<PERSON><PERSON><PERSON><PERSON>", "normalized_name": "matricula"}, {"original_name": "Status", "normalized_name": "status"}], "column_types": {"data_do_agendamento": "datetime64[ns]", "hora": "datetime64[ns]", "unidade": "object", "cod_leg": "float64", "paciente": "object", "cpf": "float64", "telefone": "object", "email": "object", "medico": "object", "tipo_de_atendimento": "object", "convenio": "object", "matricula": "float64", "status": "object"}, "column_stats": {"data_do_agendamento": {"unique_values": 5, "most_common": "21/04/2025", "most_common_count": 13, "null_count": 0, "null_percentage": 0.0}, "hora": {"unique_values": 13, "most_common": "10:00", "most_common_count": 12, "null_count": 0, "null_percentage": 0.0}, "unidade": {"unique_values": 4, "most_common": "Morumbi", "most_common_count": 20, "null_count": 0, "null_percentage": 0.0}, "cod_leg": {"min": null, "max": null, "mean": null, "null_count": 40, "null_percentage": 100.0}, "paciente": {"unique_values": 26, "most_common": "22115244 - ACRISIO JP TESTE", "most_common_count": 10, "null_count": 0, "null_percentage": 0.0}, "cpf": {"min": 764598457.0, "max": 97712877025.0, "mean": 19429422031.88, "null_count": 15, "null_percentage": 37.5}, "telefone": {"unique_values": 14, "most_common": "(11) 94441-1411", "most_common_count": 10, "null_count": 8, "null_percentage": 20.0}, "email": {"unique_values": 9, "most_common": NaN, "most_common_count": 14, "null_count": 14, "null_percentage": 35.0}, "medico": {"unique_values": 10, "most_common": "Plano de Tratamento", "most_common_count": 12, "null_count": 0, "null_percentage": 0.0}, "tipo_de_atendimento": {"unique_values": 10, "most_common": "Sessao de Fisioterapia", "most_common_count": 14, "null_count": 0, "null_percentage": 0.0}, "convenio": {"unique_values": 3, "most_common": "Particular", "most_common_count": 36, "null_count": 0, "null_percentage": 0.0}, "matricula": {"min": 132465.0, "max": 782424624646.0, "mean": 120169550571.5, "null_count": 18, "null_percentage": 45.0}, "status": {"unique_values": 3, "most_common": "<PERSON>ão confirmado", "most_common_count": 37, "null_count": 0, "null_percentage": 0.0}}, "column_samples": {"data_do_agendamento": ["23/04/2025", "22/04/2025", "21/04/2025", "25/04/2025", "22/04/2025"], "hora": ["14:00", "10:00", "18:00", "16:00", "12:00"], "unidade": ["Morumbi", "Morumbi", "Recife", "Recife", "Recife"], "cod_leg": [], "paciente": ["88801630 - <PERSON>", "56642251 - ADELMO TESTE", "56642251 - ADELMO TESTE", "22115244 - ACRISIO JP TESTE", "90829899 - <PERSON> teste DF"], "cpf": [764598457.0, 11577195701.0, 764598457.0, 62881978070.0, 764598457.0], "telefone": ["(81) 99797-0334", "(11) 94441-1411", "(81) 99730-4365", "(11) 94441-1411", "(11) 97505-4988"], "email": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "medico": ["Equipe Amigo - Giulia Pedrosa 2", "Equipe Amigo - Caio <PERSON>", "Equipe Amigo - BRUNO LIMA", "Plano de Tratamento", "Equipe Amigo - Giulia Pedrosa 2"], "tipo_de_atendimento": ["TERAPIA OCUPACIONAL", "Fisioterapia", "TERAPIA OCUPACIONAL", "CONSULTA COM ORTOPEDISTA", "BOTOX"], "convenio": ["Particular", "Particular", "Particular", "Particular", "Particular"], "matricula": [12234556.0, 132132132132.0, 3213213213.0, 3213213213.0, 1231231.0], "status": ["<PERSON>ão confirmado", "<PERSON>ão confirmado", "<PERSON>ão confirmado", "<PERSON>ão confirmado", "<PERSON>ão confirmado"]}, "potential_keys": ["unidade", "cod_leg"], "potential_relationships": [], "analyzed_at": "2025-04-23T23:42:00.076356"}
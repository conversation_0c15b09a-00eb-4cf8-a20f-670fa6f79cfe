{"file_name": "relatorio_agendamento_novo.xlsx", "module": "agenda", "row_count": 9, "column_count": 30, "columns": [{"original_name": "Cód. <PERSON>end<PERSON>", "normalized_name": "cod_atendimento"}, {"original_name": "<PERSON><PERSON><PERSON>", "normalized_name": "indice"}, {"original_name": "Agendado em", "normalized_name": "agendado_em"}, {"original_name": "Agendado por", "normalized_name": "agendado_por"}, {"original_name": "Con<PERSON>rmado em", "normalized_name": "confirmado_em"}, {"original_name": "Confirmado por", "normalized_name": "confirmado_por"}, {"original_name": "Data do Agendamento", "normalized_name": "data_do_agendamento"}, {"original_name": "Hora do Agendamento", "normalized_name": "hora_do_agendamento"}, {"original_name": "Unidade", "normalized_name": "unidade"}, {"original_name": "Sala", "normalized_name": "sala"}, {"original_name": "Profissional", "normalized_name": "profissional"}, {"original_name": "Sol. Interno", "normalized_name": "sol_interno"}, {"original_name": "Sol. Externo", "normalized_name": "sol_externo"}, {"original_name": "Paciente", "normalized_name": "paciente"}, {"original_name": "<PERSON><PERSON>", "normalized_name": "idade"}, {"original_name": "Como conheceu", "normalized_name": "como_conheceu"}, {"original_name": "ID Amigo", "normalized_name": "id_amigo"}, {"original_name": "Cod<PERSON>", "normalized_name": "cod_legado"}, {"original_name": "CPF", "normalized_name": "cpf"}, {"original_name": "Telefone", "normalized_name": "telefone"}, {"original_name": "E-mail", "normalized_name": "e_mail"}, {"original_name": "<PERSON><PERSON><PERSON><PERSON>", "normalized_name": "matricula"}, {"original_name": "Tipo de Atendimento", "normalized_name": "tipo_de_atendimento"}, {"original_name": "Tipo do Item", "normalized_name": "tipo_do_item"}, {"original_name": "Qtd. Item", "normalized_name": "qtd_item"}, {"original_name": "<PERSON><PERSON>", "normalized_name": "item"}, {"original_name": "Status do Agendamento", "normalized_name": "status_do_agendamento"}, {"original_name": "Forma de Pagamento", "normalized_name": "forma_de_pagamento"}, {"original_name": "<PERSON><PERSON><PERSON>", "normalized_name": "tipo_de_guia"}, {"original_name": "Acomodação", "normalized_name": "acomodacao"}], "column_types": {"cod_atendimento": "int64", "indice": "int64", "agendado_em": "datetime64[ns]", "agendado_por": "object", "confirmado_em": "float64", "confirmado_por": "float64", "data_do_agendamento": "datetime64[ns]", "hora_do_agendamento": "datetime64[ns]", "unidade": "object", "sala": "object", "profissional": "object", "sol_interno": "object", "sol_externo": "object", "paciente": "object", "idade": "object", "como_conheceu": "float64", "id_amigo": "int64", "cod_legado": "object", "cpf": "object", "telefone": "object", "e_mail": "object", "matricula": "object", "tipo_de_atendimento": "object", "tipo_do_item": "object", "qtd_item": "int64", "item": "object", "status_do_agendamento": "object", "forma_de_pagamento": "object", "tipo_de_guia": "object", "acomodacao": "object"}, "column_stats": {"cod_atendimento": {"min": 151314579.0, "max": 155723069.0, "mean": 154295024.2222222, "null_count": 0, "null_percentage": 0.0}, "indice": {"min": 1.0, "max": 8.0, "mean": 4.111111111111111, "null_count": 0, "null_percentage": 0.0}, "agendado_em": {"unique_values": 5, "most_common": "17/04/2025", "most_common_count": 2, "null_count": 0, "null_percentage": 0.0}, "agendado_por": {"unique_values": 5, "most_common": "Equipe Amigo - Caio <PERSON>", "most_common_count": 4, "null_count": 0, "null_percentage": 0.0}, "confirmado_em": {"min": null, "max": null, "mean": null, "null_count": 9, "null_percentage": 100.0}, "confirmado_por": {"min": null, "max": null, "mean": null, "null_count": 9, "null_percentage": 100.0}, "data_do_agendamento": {"unique_values": 1, "most_common": "22/04/2025", "most_common_count": 9, "null_count": 0, "null_percentage": 0.0}, "hora_do_agendamento": {"unique_values": 5, "most_common": "10:00", "most_common_count": 3, "null_count": 0, "null_percentage": 0.0}, "unidade": {"unique_values": 3, "most_common": "Morumbi", "most_common_count": 4, "null_count": 0, "null_percentage": 0.0}, "sala": {"unique_values": 3, "most_common": "-", "most_common_count": 7, "null_count": 0, "null_percentage": 0.0}, "profissional": {"unique_values": 4, "most_common": "Equipe Amigo - Caio <PERSON>", "most_common_count": 4, "null_count": 0, "null_percentage": 0.0}, "sol_interno": {"unique_values": 1, "most_common": "-", "most_common_count": 9, "null_count": 0, "null_percentage": 0.0}, "sol_externo": {"unique_values": 1, "most_common": "-", "most_common_count": 9, "null_count": 0, "null_percentage": 0.0}, "paciente": {"unique_values": 7, "most_common": "AMANDA 1704", "most_common_count": 2, "null_count": 0, "null_percentage": 0.0}, "idade": {"unique_values": 2, "most_common": NaN, "most_common_count": 6, "null_count": 6, "null_percentage": 66.66666666666666}, "como_conheceu": {"min": null, "max": null, "mean": null, "null_count": 9, "null_percentage": 100.0}, "id_amigo": {"min": 9947091.0, "max": 90802077.0, "mean": 64400665.666666664, "null_count": 0, "null_percentage": 0.0}, "cod_legado": {"unique_values": 1, "most_common": "-", "most_common_count": 9, "null_count": 0, "null_percentage": 0.0}, "cpf": {"unique_values": 4, "most_common": "-", "most_common_count": 5, "null_count": 0, "null_percentage": 0.0}, "telefone": {"unique_values": 6, "most_common": "-", "most_common_count": 2, "null_count": 0, "null_percentage": 0.0}, "e_mail": {"unique_values": 4, "most_common": "-", "most_common_count": 4, "null_count": 0, "null_percentage": 0.0}, "matricula": {"unique_values": 4, "most_common": "-", "most_common_count": 5, "null_count": 0, "null_percentage": 0.0}, "tipo_de_atendimento": {"unique_values": 4, "most_common": "Sessao de Fisioterapia", "most_common_count": 5, "null_count": 0, "null_percentage": 0.0}, "tipo_do_item": {"unique_values": 2, "most_common": "Procedimento", "most_common_count": 7, "null_count": 0, "null_percentage": 0.0}, "qtd_item": {"min": 1.0, "max": 1.0, "mean": 1.0, "null_count": 0, "null_percentage": 0.0}, "item": {"unique_values": 4, "most_common": "<PERSON><PERSON><PERSON> - teste", "most_common_count": 5, "null_count": 0, "null_percentage": 0.0}, "status_do_agendamento": {"unique_values": 1, "most_common": "Agendado", "most_common_count": 9, "null_count": 0, "null_percentage": 0.0}, "forma_de_pagamento": {"unique_values": 5, "most_common": "Pendente", "most_common_count": 4, "null_count": 0, "null_percentage": 0.0}, "tipo_de_guia": {"unique_values": 2, "most_common": "-", "most_common_count": 8, "null_count": 0, "null_percentage": 0.0}, "acomodacao": {"unique_values": 1, "most_common": "-", "most_common_count": 9, "null_count": 0, "null_percentage": 0.0}}, "column_samples": {"cod_atendimento": [151314579, 155491950, 153786350, 153827808, 153651062], "indice": [3, 5, 1, 4, 8], "agendado_em": ["08/04/2025", "07/04/2025", "08/04/2025", "16/04/2025", "17/04/2025"], "agendado_por": ["Equipe Amigo - Caio <PERSON>", "Equipe Amigo - Caio <PERSON>", "Plano de Tratamento", "Equipe Amigo - Caio <PERSON>", "<PERSON><PERSON>"], "confirmado_em": [], "confirmado_por": [], "data_do_agendamento": ["22/04/2025", "22/04/2025", "22/04/2025", "22/04/2025", "22/04/2025"], "hora_do_agendamento": ["15:00", "10:00", "08:00", "15:00", "12:00"], "unidade": ["Recife", "Recife", "Recife", "Morumbi", "Morumbi"], "sala": ["SL TO", "-", "-", "-", "-"], "profissional": ["Plano de Tratamento", "Equipe Amigo - Ana Victoria <PERSON> Almeida", "Plano de Tratamento", "Equipe Amigo - Giulia Pedrosa 2", "Equipe Amigo - Giulia Pedrosa 2"], "sol_interno": ["-", "-", "-", "-", "-"], "sol_externo": ["-", "-", "-", "-", "-"], "paciente": ["testse", "ACRISIO JP TESTE", "<PERSON><PERSON>", "ACRISIO JP TESTE", "MARINA HAZIN CONVÊNIO"], "idade": ["30 anos", "43 anos", "43 anos"], "como_conheceu": [], "id_amigo": [90155706, 9947091, 90802077, 90154592, 85794097], "cod_legado": ["-", "-", "-", "-", "-"], "cpf": ["-", "-", "10880304405", "00764598457", "-"], "telefone": ["(11) 94441-1411", "-", "(81) 99797-0334", "(21) 99435-2590", "-"], "e_mail": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "-"], "matricula": ["-", "-", "-", "782424624646", "-"], "tipo_de_atendimento": ["Sessao de Fisioterapia", "TERAPIA OCUPACIONAL", "BOTOX", "Sessao de Fisioterapia", "Sessao de Fisioterapia"], "tipo_do_item": ["Procedimento", "Procedimento", "Procedimento", "-", "Procedimento"], "qtd_item": [1, 1, 1, 1, 1], "item": ["Blefaroplastia", "<PERSON><PERSON><PERSON> - teste", "<PERSON><PERSON><PERSON> - teste", "-", "<PERSON><PERSON><PERSON> - teste"], "status_do_agendamento": ["Agendado", "Agendado", "Agendado", "Agendado", "Agendado"], "forma_de_pagamento": ["Pendente", "Pendente", "Pendente", "Crédito de Procedimento", "Pendente"], "tipo_de_guia": ["-", "-", "-", "-", "-"], "acomodacao": ["-", "-", "-", "-", "-"]}, "potential_keys": ["cod_atendimento", "unidade", "idade", "id_amigo", "cod_legado"], "potential_relationships": [{"column": "id_amigo", "possible_related_entity": "amigo"}], "analyzed_at": "2025-04-23T23:42:00.095946"}
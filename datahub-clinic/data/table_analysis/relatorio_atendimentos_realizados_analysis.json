{"file_name": "relatorio_atendimentos_realizados.xlsx", "module": "paciente", "row_count": 47, "column_count": 11, "columns": [{"original_name": "ID Amigo", "normalized_name": "id_amigo"}, {"original_name": "Paciente", "normalized_name": "paciente"}, {"original_name": "CPF", "normalized_name": "cpf"}, {"original_name": "Data Nasc.", "normalized_name": "data_nasc"}, {"original_name": "Sexo", "normalized_name": "sexo"}, {"original_name": "Vip", "normalized_name": "vip"}, {"original_name": "E-mail", "normalized_name": "e_mail"}, {"original_name": "<PERSON><PERSON><PERSON>", "normalized_name": "celular"}, {"original_name": "Atendimentos", "normalized_name": "atendimentos"}, {"original_name": "Ticket médio R$", "normalized_name": "ticket_medio_r"}, {"original_name": "Total R$", "normalized_name": "total_r"}], "column_types": {"id_amigo": "int64", "paciente": "object", "cpf": "float64", "data_nasc": "datetime64[ns]", "sexo": "object", "vip": "bool", "e_mail": "object", "celular": "object", "atendimentos": "int64", "ticket_medio_r": "float64", "total_r": "float64"}, "column_stats": {"id_amigo": {"min": 27050798.0, "max": 90817169.0, "mean": 84139991.9787234, "null_count": 0, "null_percentage": 0.0}, "paciente": {"unique_values": 46, "most_common": "YGOR PAIVA", "most_common_count": 2, "null_count": 0, "null_percentage": 0.0}, "cpf": {"min": 195423135.0, "max": 84472975009.0, "mean": 26671393898.944443, "null_count": 29, "null_percentage": 61.702127659574465}, "data_nasc": {"unique_values": 13, "most_common": NaN, "most_common_count": 32, "null_count": 32, "null_percentage": 68.08510638297872}, "sexo": {"unique_values": 2, "most_common": NaN, "most_common_count": 42, "null_count": 42, "null_percentage": 89.36170212765957}, "vip": {"unique_values": 2, "most_common": "Não", "most_common_count": 44, "null_count": 0, "null_percentage": 0.0}, "e_mail": {"unique_values": 16, "most_common": NaN, "most_common_count": 29, "null_count": 29, "null_percentage": 61.702127659574465}, "celular": {"unique_values": 30, "most_common": NaN, "most_common_count": 13, "null_count": 13, "null_percentage": 27.659574468085108}, "atendimentos": {"min": 1.0, "max": 4.0, "mean": 1.2765957446808511, "null_count": 0, "null_percentage": 0.0}, "ticket_medio_r": {"min": 2.5, "max": 25000.0, "mean": 936.6265957446809, "null_count": 0, "null_percentage": 0.0}, "total_r": {"min": 2.5, "max": 25000.0, "mean": 1162.6851063829788, "null_count": 0, "null_percentage": 0.0}}, "column_samples": {"id_amigo": [90071073, 90193399, 81199677, 89894844, 90164643], "paciente": ["FABIO FELTRIM", "LUIZ GUSTAVO", "DIEGO PATERNOSTRO", "IGOR DOURADO", "<PERSON>"], "cpf": [80351218041.0, 3718245086.0, 30957067070.0, 2437170081.0, 32585940829.0], "data_nasc": ["30/05/1990", "09/09/1993", "12/12/2000", "21/09/1995", "21/09/1995"], "sexo": ["Feminino", "Feminino", "Feminino", "Feminino", "<PERSON><PERSON><PERSON><PERSON>"], "vip": ["Não", "Não", "Não", "Não", "Não"], "e_mail": ["<EMAIL>", "fabi<PERSON><PERSON><PERSON><PERSON>@me.com", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "celular": ["(66) 99654-2070", "(34) 93300-1813", "(81) 99730-4365", "(11) 98270-5254", "(41) 99252-5610"], "atendimentos": [1, 1, 1, 1, 1], "ticket_medio_r": [1535.5, 300.0, 300.0, 213.33, 1084.4], "total_r": [149.0, 300.0, 4400.0, 500.0, 4606.5]}, "potential_keys": ["id_amigo"], "potential_relationships": [{"column": "id_amigo", "possible_related_entity": "amigo"}], "analyzed_at": "2025-04-23T23:41:59.801231"}
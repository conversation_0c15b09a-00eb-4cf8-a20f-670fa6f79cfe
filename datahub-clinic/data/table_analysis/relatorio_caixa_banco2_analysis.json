{"file_name": "relatorio_caixa_banco2.xlsx", "module": "financeiro", "row_count": 32, "column_count": 12, "columns": [{"original_name": "Data de pagamento", "normalized_name": "data_de_pagamento"}, {"original_name": "Competencia", "normalized_name": "competencia"}, {"original_name": "Tipo", "normalized_name": "tipo"}, {"original_name": "Pago a / Recebido de", "normalized_name": "pago_a_recebido_de"}, {"original_name": "Categoria", "normalized_name": "categoria"}, {"original_name": "Classificação", "normalized_name": "classificacao"}, {"original_name": "Descrição", "normalized_name": "descricao"}, {"original_name": "Forma de Pagamento", "normalized_name": "forma_de_pagamento"}, {"original_name": "Banco", "normalized_name": "banco"}, {"original_name": "Valor R$", "normalized_name": "valor_r"}, {"original_name": "Nota emitida", "normalized_name": "nota_emitida"}, {"original_name": "Recibo emitido", "normalized_name": "recibo_emitido"}], "column_types": {"data_de_pagamento": "object", "competencia": "object", "tipo": "object", "pago_a_recebido_de": "object", "categoria": "object", "classificacao": "object", "descricao": "object", "forma_de_pagamento": "object", "banco": "object", "valor_r": "float64", "nota_emitida": "bool", "recibo_emitido": "bool"}, "column_stats": {"data_de_pagamento": {"unique_values": 10, "most_common": "03/04/2025", "most_common_count": 5, "null_count": 0, "null_percentage": 0.0}, "competencia": {"unique_values": 3, "most_common": "04/2025", "most_common_count": 21, "null_count": 0, "null_percentage": 0.0}, "tipo": {"unique_values": 3, "most_common": "Entrada", "most_common_count": 27, "null_count": 0, "null_percentage": 0.0}, "pago_a_recebido_de": {"unique_values": 25, "most_common": "<PERSON><PERSON>", "most_common_count": 4, "null_count": 0, "null_percentage": 0.0}, "categoria": {"unique_values": 6, "most_common": "RECEITA OPERACIONAL", "most_common_count": 26, "null_count": 0, "null_percentage": 0.0}, "classificacao": {"unique_values": 6, "most_common": "Prestação de Serviço", "most_common_count": 26, "null_count": 0, "null_percentage": 0.0}, "descricao": {"unique_values": 20, "most_common": "Atendimento - Consulta - Amigo tech", "most_common_count": 7, "null_count": 0, "null_percentage": 0.0}, "forma_de_pagamento": {"unique_values": 4, "most_common": "Pix", "most_common_count": 20, "null_count": 0, "null_percentage": 0.0}, "banco": {"unique_values": 9, "most_common": "SANTANDER 1-1", "most_common_count": 13, "null_count": 0, "null_percentage": 0.0}, "valor_r": {"min": -2348.0, "max": 20000.0, "mean": 1454.0678125, "null_count": 0, "null_percentage": 0.0}, "nota_emitida": {"unique_values": 1, "most_common": "Não", "most_common_count": 32, "null_count": 0, "null_percentage": 0.0}, "recibo_emitido": {"unique_values": 2, "most_common": "Não", "most_common_count": 30, "null_count": 0, "null_percentage": 0.0}}, "column_samples": {"data_de_pagamento": ["08/04/2025", "03/04/2025", "16/04/2025", "03/04/2025", "03/04/2025"], "competencia": ["04/2025", "-", "-", "04/2025", "04/2025"], "tipo": ["Entrada", "Entrada", "Entrada", "Entrada", "<PERSON><PERSON><PERSON>"], "pago_a_recebido_de": ["bradesco 1-1", "MARINA HAZIN", "<PERSON>", "BANCO DO BRASIL 103291", "EDP"], "categoria": ["RECEITA OPERACIONAL", "RECEITA OPERACIONAL", "RECEITA OPERACIONAL", "*******.01 - UTILIDADES E SERVIÇOS DE TERCEIROS", "*******.07 - DESPESAS COM PESSOAL"], "classificacao": ["Prestação de Serviço", "ACERTO DE CAIXA TESTE", "Prestação de Serviço", "Prestação de Serviço", "Prestação de Serviço"], "descricao": ["Atendimento (Sessão) - Sessao de Fisioterapia", "Atendimento (Sessão) - Fisioterapia", "Pré-Pagamento #1568232", "Pré-Pagamento #1568236", "Atendimento - CONSULTA  COM CARDIOLOGISTA"], "forma_de_pagamento": ["<PERSON><PERSON><PERSON>", "Pix", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Pix"], "banco": ["Caixa", "SANTANDER 1-1", "NEON 78799-7", "SANTANDER 1-1", "SANTANDER 1-1"], "valor_r": [300.0, 500.0, -100.0, 100.0, 356.5], "nota_emitida": ["Não", "Não", "Não", "Não", "Não"], "recibo_emitido": ["Não", "Não", "Não", "Não", "Não"]}, "potential_keys": ["pago_a_recebido_de", "nota_emitida", "recibo_emitido"], "potential_relationships": [], "analyzed_at": "2025-04-23T23:41:59.679045"}
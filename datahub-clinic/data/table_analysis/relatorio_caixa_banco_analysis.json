{"file_name": "relatorio_caixa_banco.xlsx", "module": "financeiro", "row_count": 32, "column_count": 26, "columns": [{"original_name": "Data de vencimento", "normalized_name": "data_de_vencimento"}, {"original_name": "Data de pagamento", "normalized_name": "data_de_pagamento"}, {"original_name": "Competência", "normalized_name": "competencia"}, {"original_name": "Tipo", "normalized_name": "tipo"}, {"original_name": "Pago a / Recebido de", "normalized_name": "pago_a_recebido_de"}, {"original_name": "Categoria", "normalized_name": "categoria"}, {"original_name": "Classificação", "normalized_name": "classificacao"}, {"original_name": "Descrição", "normalized_name": "descricao"}, {"original_name": "Forma de Pagamento", "normalized_name": "forma_de_pagamento"}, {"original_name": "Observação", "normalized_name": "observacao"}, {"original_name": "Nº documento", "normalized_name": "nº_documento"}, {"original_name": "Nº transação", "normalized_name": "nº_transacao"}, {"original_name": "Tag", "normalized_name": "tag"}, {"original_name": "Centro de custo", "normalized_name": "centro_de_custo"}, {"original_name": "Unidade", "normalized_name": "unidade"}, {"original_name": "Banco", "normalized_name": "banco"}, {"original_name": "Valor Original R$", "normalized_name": "valor_original_r"}, {"original_name": "PIS", "normalized_name": "pis"}, {"original_name": "COFINS", "normalized_name": "cofins"}, {"original_name": "CSLL", "normalized_name": "csll"}, {"original_name": "IR", "normalized_name": "ir"}, {"original_name": "ISS", "normalized_name": "iss"}, {"original_name": "INSS", "normalized_name": "inss"}, {"original_name": "Valor Líquido R$", "normalized_name": "valor_liquido_r"}, {"original_name": "Nota Emitida", "normalized_name": "nota_emitida"}, {"original_name": "Recibo Emitido", "normalized_name": "recibo_emitido"}], "column_types": {"data_de_vencimento": "object", "data_de_pagamento": "object", "competencia": "object", "tipo": "object", "pago_a_recebido_de": "object", "categoria": "object", "classificacao": "object", "descricao": "object", "forma_de_pagamento": "object", "observacao": "object", "nº_documento": "object", "nº_transacao": "object", "tag": "object", "centro_de_custo": "object", "unidade": "object", "banco": "object", "valor_original_r": "float64", "pis": "object", "cofins": "object", "csll": "object", "ir": "object", "iss": "object", "inss": "object", "valor_liquido_r": "float64", "nota_emitida": "bool", "recibo_emitido": "bool"}, "column_stats": {"data_de_vencimento": {"unique_values": 10, "most_common": "02/04/2025", "most_common_count": 7, "null_count": 0, "null_percentage": 0.0}, "data_de_pagamento": {"unique_values": 10, "most_common": "03/04/2025", "most_common_count": 5, "null_count": 0, "null_percentage": 0.0}, "competencia": {"unique_values": 3, "most_common": "04/2025", "most_common_count": 21, "null_count": 0, "null_percentage": 0.0}, "tipo": {"unique_values": 3, "most_common": "Entrada", "most_common_count": 27, "null_count": 0, "null_percentage": 0.0}, "pago_a_recebido_de": {"unique_values": 25, "most_common": "<PERSON><PERSON>", "most_common_count": 4, "null_count": 0, "null_percentage": 0.0}, "categoria": {"unique_values": 6, "most_common": "RECEITA OPERACIONAL", "most_common_count": 26, "null_count": 0, "null_percentage": 0.0}, "classificacao": {"unique_values": 6, "most_common": "Prestação de Serviço", "most_common_count": 26, "null_count": 0, "null_percentage": 0.0}, "descricao": {"unique_values": 20, "most_common": "Atendimento - Consulta - Amigo tech", "most_common_count": 7, "null_count": 0, "null_percentage": 0.0}, "forma_de_pagamento": {"unique_values": 4, "most_common": "Pix", "most_common_count": 20, "null_count": 0, "null_percentage": 0.0}, "observacao": {"unique_values": 1, "most_common": "-", "most_common_count": 32, "null_count": 0, "null_percentage": 0.0}, "nº_documento": {"unique_values": 3, "most_common": "-", "most_common_count": 30, "null_count": 0, "null_percentage": 0.0}, "nº_transacao": {"unique_values": 1, "most_common": "-", "most_common_count": 32, "null_count": 0, "null_percentage": 0.0}, "tag": {"unique_values": 1, "most_common": "-", "most_common_count": 32, "null_count": 0, "null_percentage": 0.0}, "centro_de_custo": {"unique_values": 1, "most_common": "-", "most_common_count": 32, "null_count": 0, "null_percentage": 0.0}, "unidade": {"unique_values": 5, "most_common": "BRASILIA", "most_common_count": 15, "null_count": 0, "null_percentage": 0.0}, "banco": {"unique_values": 9, "most_common": "SANTANDER 1-1", "most_common_count": 13, "null_count": 0, "null_percentage": 0.0}, "valor_original_r": {"min": 2.5, "max": 20000.0, "mean": 1738.3178125, "null_count": 0, "null_percentage": 0.0}, "pis": {"unique_values": 1, "most_common": "-", "most_common_count": 32, "null_count": 0, "null_percentage": 0.0}, "cofins": {"unique_values": 1, "most_common": "-", "most_common_count": 32, "null_count": 0, "null_percentage": 0.0}, "csll": {"unique_values": 1, "most_common": "-", "most_common_count": 32, "null_count": 0, "null_percentage": 0.0}, "ir": {"unique_values": 1, "most_common": "-", "most_common_count": 32, "null_count": 0, "null_percentage": 0.0}, "iss": {"unique_values": 1, "most_common": "-", "most_common_count": 32, "null_count": 0, "null_percentage": 0.0}, "inss": {"unique_values": 1, "most_common": "-", "most_common_count": 32, "null_count": 0, "null_percentage": 0.0}, "valor_liquido_r": {"min": -2348.0, "max": 20000.0, "mean": 1454.0678125, "null_count": 0, "null_percentage": 0.0}, "nota_emitida": {"unique_values": 1, "most_common": "Não", "most_common_count": 32, "null_count": 0, "null_percentage": 0.0}, "recibo_emitido": {"unique_values": 2, "most_common": "Não", "most_common_count": 30, "null_count": 0, "null_percentage": 0.0}}, "column_samples": {"data_de_vencimento": ["07/04/2025", "01/04/2025", "02/04/2025", "02/04/2025", "01/04/2025"], "data_de_pagamento": ["16/04/2025", "08/04/2025", "10/04/2025", "07/04/2025", "03/04/2025"], "competencia": ["04/2025", "04/2025", "-", "03/2025", "04/2025"], "tipo": ["Entrada", "Entrada", "Entrada", "Entrada", "Entrada"], "pago_a_recebido_de": ["EDP", "DRA CAROLINA TESTE", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "categoria": ["RECEITA OPERACIONAL", "RECEITA OPERACIONAL", "RECEITA OPERACIONAL", "RECEITA OPERACIONAL", "RECEITA OPERACIONAL"], "classificacao": ["Prestação de Serviço", "Prestação de Serviço", "Prestação de Serviço", "-", "Ad<PERSON><PERSON><PERSON>"], "descricao": ["Atendimento - CONSULTA  COM CARDIOLOGISTA", "Atendimento (Sessão) - Fisioterapia", "<PERSON><PERSON> inicial", "Orçamento #1578045", "Orçamento #1568236"], "forma_de_pagamento": ["Pix", "Pix", "Pix", "Cartão", "Pix"], "observacao": ["-", "-", "-", "-", "-"], "nº_documento": ["-", "-", "-", "-", "-"], "nº_transacao": ["-", "-", "-", "-", "-"], "tag": ["-", "-", "-", "-", "-"], "centro_de_custo": ["-", "-", "-", "-", "-"], "unidade": ["-", "BRASILIA", "BRASILIA", "BRASILIA", "BRASILIA"], "banco": ["SANTANDER 1-1", "Caixinha 0", "SANTANDER 1-1", "BANCO SAFRA 10187840-0", "NEON 78799-7"], "valor_original_r": [2348.0, 300.0, 20000.0, 1100.0, 300.0], "pis": ["-", "-", "-", "-", "-"], "cofins": ["-", "-", "-", "-", "-"], "csll": ["-", "-", "-", "-", "-"], "ir": ["-", "-", "-", "-", "-"], "iss": ["-", "-", "-", "-", "-"], "inss": ["-", "-", "-", "-", "-"], "valor_liquido_r": [160.0, 20000.0, 300.0, 4000.0, -1100.0], "nota_emitida": ["Não", "Não", "Não", "Não", "Não"], "recibo_emitido": ["Não", "Não", "Não", "Não", "Não"]}, "potential_keys": ["pago_a_recebido_de", "unidade", "valor_liquido_r", "nota_emitida", "recibo_emitido"], "potential_relationships": [], "analyzed_at": "2025-04-23T23:41:59.640309"}
{"file_name": "relatorio_contas_a_pagar.xlsx", "module": "financeiro", "row_count": 113, "column_count": 22, "columns": [{"original_name": "Data de vencimento", "normalized_name": "data_de_vencimento"}, {"original_name": "Competência", "normalized_name": "competencia"}, {"original_name": "Pagar a", "normalized_name": "pagar_a"}, {"original_name": "Categoria", "normalized_name": "categoria"}, {"original_name": "Classificação", "normalized_name": "classificacao"}, {"original_name": "Descrição", "normalized_name": "descricao"}, {"original_name": "Forma de Pagamento", "normalized_name": "forma_de_pagamento"}, {"original_name": "Observação", "normalized_name": "observacao"}, {"original_name": "Nº documento", "normalized_name": "nº_documento"}, {"original_name": "Tag", "normalized_name": "tag"}, {"original_name": "Centro de custo", "normalized_name": "centro_de_custo"}, {"original_name": "Unidade", "normalized_name": "unidade"}, {"original_name": "Valor Original R$", "normalized_name": "valor_original_r"}, {"original_name": "PIS", "normalized_name": "pis"}, {"original_name": "COFINS", "normalized_name": "cofins"}, {"original_name": "CSLL", "normalized_name": "csll"}, {"original_name": "IR", "normalized_name": "ir"}, {"original_name": "ISS", "normalized_name": "iss"}, {"original_name": "INSS", "normalized_name": "inss"}, {"original_name": "Valor Líquido R$", "normalized_name": "valor_liquido_r"}, {"original_name": "Nota Emitida", "normalized_name": "nota_emitida"}, {"original_name": "Recibo Emitido", "normalized_name": "recibo_emitido"}], "column_types": {"data_de_vencimento": "object", "competencia": "datetime64[ns]", "pagar_a": "object", "categoria": "object", "classificacao": "object", "descricao": "object", "forma_de_pagamento": "object", "observacao": "object", "nº_documento": "object", "tag": "object", "centro_de_custo": "object", "unidade": "object", "valor_original_r": "float64", "pis": "object", "cofins": "object", "csll": "object", "ir": "object", "iss": "object", "inss": "object", "valor_liquido_r": "float64", "nota_emitida": "bool", "recibo_emitido": "bool"}, "column_stats": {"data_de_vencimento": {"unique_values": 24, "most_common": "10/04/2025", "most_common_count": 29, "null_count": 0, "null_percentage": 0.0}, "competencia": {"unique_values": 3, "most_common": "04/2025", "most_common_count": 101, "null_count": 0, "null_percentage": 0.0}, "pagar_a": {"unique_values": 79, "most_common": "Caesb", "most_common_count": 8, "null_count": 0, "null_percentage": 0.0}, "categoria": {"unique_values": 26, "most_common": "*******.01 - UTILIDADES E SERVIÇOS DE TERCEIROS", "most_common_count": 25, "null_count": 0, "null_percentage": 0.0}, "classificacao": {"unique_values": 25, "most_common": "Energia elétrica", "most_common_count": 25, "null_count": 0, "null_percentage": 0.0}, "descricao": {"unique_values": 72, "most_common": "Pagamento de agua", "most_common_count": 8, "null_count": 0, "null_percentage": 0.0}, "forma_de_pagamento": {"unique_values": 1, "most_common": "-", "most_common_count": 113, "null_count": 0, "null_percentage": 0.0}, "observacao": {"unique_values": 1, "most_common": "-", "most_common_count": 113, "null_count": 0, "null_percentage": 0.0}, "nº_documento": {"unique_values": 18, "most_common": "-", "most_common_count": 89, "null_count": 0, "null_percentage": 0.0}, "tag": {"unique_values": 2, "most_common": "-", "most_common_count": 112, "null_count": 0, "null_percentage": 0.0}, "centro_de_custo": {"unique_values": 28, "most_common": "-", "most_common_count": 81, "null_count": 0, "null_percentage": 0.0}, "unidade": {"unique_values": 4, "most_common": "Morumbi", "most_common_count": 61, "null_count": 0, "null_percentage": 0.0}, "valor_original_r": {"min": 10.0, "max": 20000.0, "mean": 2866.4554867256634, "null_count": 0, "null_percentage": 0.0}, "pis": {"unique_values": 1, "most_common": "-", "most_common_count": 113, "null_count": 0, "null_percentage": 0.0}, "cofins": {"unique_values": 1, "most_common": "-", "most_common_count": 113, "null_count": 0, "null_percentage": 0.0}, "csll": {"unique_values": 1, "most_common": "-", "most_common_count": 113, "null_count": 0, "null_percentage": 0.0}, "ir": {"unique_values": 1, "most_common": "-", "most_common_count": 113, "null_count": 0, "null_percentage": 0.0}, "iss": {"unique_values": 1, "most_common": "-", "most_common_count": 113, "null_count": 0, "null_percentage": 0.0}, "inss": {"unique_values": 1, "most_common": "-", "most_common_count": 113, "null_count": 0, "null_percentage": 0.0}, "valor_liquido_r": {"min": -20000.0, "max": -10.0, "mean": -2121.6767256637168, "null_count": 0, "null_percentage": 0.0}, "nota_emitida": {"unique_values": 1, "most_common": "Não", "most_common_count": 113, "null_count": 0, "null_percentage": 0.0}, "recibo_emitido": {"unique_values": 1, "most_common": "Não", "most_common_count": 113, "null_count": 0, "null_percentage": 0.0}}, "column_samples": {"data_de_vencimento": ["15/04/2025", "02/04/2025", "26/04/2025", "18/04/2025", "28/04/2025"], "competencia": ["04/2025", "04/2025", "04/2025", "04/2025", "04/2025"], "pagar_a": ["Condominio", "Condominio", "NET", "Equipe Amigo - Caio <PERSON>", "ALGAR"], "categoria": ["*******.05 - DESPESAS ADMINISTRATIVAS", "*******.02 - UTILIDADES E SERVIÇOS DE TERCEIROS", "*******.17 - DESPESAS ADMINISTRATIVAS", "*******.02 - UTILIDADES E SERVIÇOS DE TERCEIROS", "*******.06 - DESPESAS ADMINISTRATIVAS"], "classificacao": ["Suporte Técnico", "<PERSON>ass<PERSON>", "GEL", "Material de escritório", "Con<PERSON><PERSON><PERSON>"], "descricao": ["PGT LUZ", "condominio", "condominio dsbkfds", "Pagamento de agua", "CURATIVO"], "forma_de_pagamento": ["-", "-", "-", "-", "-"], "observacao": ["-", "-", "-", "-", "-"], "nº_documento": ["-", "-", "SDAS", "-", "-"], "tag": ["-", "-", "-", "-", "-"], "centro_de_custo": ["-", "-", "-", "Centro de Diagnósticos (40%)", "-"], "unidade": ["Morumbi", "-", "Morumbi", "Morumbi", "Morumbi"], "valor_original_r": [200.0, 500.0, 6000.0, 10000.0, 113.0], "pis": ["-", "-", "-", "-", "-"], "cofins": ["-", "-", "-", "-", "-"], "csll": ["-", "-", "-", "-", "-"], "ir": ["-", "-", "-", "-", "-"], "iss": ["-", "-", "-", "-", "-"], "inss": ["-", "-", "-", "-", "-"], "valor_liquido_r": [-500.0, -150.0, -833.34, -1000.0, -3000.0], "nota_emitida": ["Não", "Não", "Não", "Não", "Não"], "recibo_emitido": ["Não", "Não", "Não", "Não", "Não"]}, "potential_keys": ["unidade", "valor_liquido_r", "nota_emitida", "recibo_emitido"], "potential_relationships": [], "analyzed_at": "2025-04-23T23:41:59.710036"}
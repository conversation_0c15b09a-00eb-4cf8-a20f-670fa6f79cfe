{"file_name": "relatorio_contas_a_receber.xlsx", "module": "financeiro", "row_count": 65, "column_count": 15, "columns": [{"original_name": "Data de vencimento", "normalized_name": "data_de_vencimento"}, {"original_name": "Competência", "normalized_name": "competencia"}, {"original_name": "<PERSON><PERSON><PERSON> de", "normalized_name": "receber_de"}, {"original_name": "Categoria", "normalized_name": "categoria"}, {"original_name": "Classificação", "normalized_name": "classificacao"}, {"original_name": "Descrição", "normalized_name": "descricao"}, {"original_name": "Forma de Pagamento", "normalized_name": "forma_de_pagamento"}, {"original_name": "Observação", "normalized_name": "observacao"}, {"original_name": "Nº documento", "normalized_name": "nº_documento"}, {"original_name": "Tag", "normalized_name": "tag"}, {"original_name": "Centro de custo", "normalized_name": "centro_de_custo"}, {"original_name": "Unidade", "normalized_name": "unidade"}, {"original_name": "Valor Líquido R$", "normalized_name": "valor_liquido_r"}, {"original_name": "Nota Emitida", "normalized_name": "nota_emitida"}, {"original_name": "Recibo Emitido", "normalized_name": "recibo_emitido"}], "column_types": {"data_de_vencimento": "object", "competencia": "object", "receber_de": "object", "categoria": "object", "classificacao": "object", "descricao": "object", "forma_de_pagamento": "object", "observacao": "object", "nº_documento": "object", "tag": "object", "centro_de_custo": "object", "unidade": "object", "valor_liquido_r": "float64", "nota_emitida": "bool", "recibo_emitido": "bool"}, "column_stats": {"data_de_vencimento": {"unique_values": 26, "most_common": "17/04/2025", "most_common_count": 8, "null_count": 0, "null_percentage": 0.0}, "competencia": {"unique_values": 4, "most_common": "-", "most_common_count": 47, "null_count": 0, "null_percentage": 0.0}, "receber_de": {"unique_values": 30, "most_common": "BANCO DO BRASIL 103291", "most_common_count": 13, "null_count": 0, "null_percentage": 0.0}, "categoria": {"unique_values": 4, "most_common": "RECEITA OPERACIONAL", "most_common_count": 54, "null_count": 0, "null_percentage": 0.0}, "classificacao": {"unique_values": 3, "most_common": "Prestação de Serviço", "most_common_count": 57, "null_count": 0, "null_percentage": 0.0}, "descricao": {"unique_values": 40, "most_common": "Lote - Cartão (GetNET)", "most_common_count": 9, "null_count": 0, "null_percentage": 0.0}, "forma_de_pagamento": {"unique_values": 6, "most_common": "Cartão", "most_common_count": 24, "null_count": 0, "null_percentage": 0.0}, "observacao": {"unique_values": 1, "most_common": "-", "most_common_count": 65, "null_count": 0, "null_percentage": 0.0}, "nº_documento": {"unique_values": 4, "most_common": "-", "most_common_count": 62, "null_count": 0, "null_percentage": 0.0}, "tag": {"unique_values": 1, "most_common": "-", "most_common_count": 65, "null_count": 0, "null_percentage": 0.0}, "centro_de_custo": {"unique_values": 3, "most_common": "-", "most_common_count": 63, "null_count": 0, "null_percentage": 0.0}, "unidade": {"unique_values": 4, "most_common": "-", "most_common_count": 33, "null_count": 0, "null_percentage": 0.0}, "valor_liquido_r": {"min": 0.0, "max": 11500.0, "mean": 1638.3886153846156, "null_count": 0, "null_percentage": 0.0}, "nota_emitida": {"unique_values": 1, "most_common": "Não", "most_common_count": 65, "null_count": 0, "null_percentage": 0.0}, "recibo_emitido": {"unique_values": 2, "most_common": "Não", "most_common_count": 63, "null_count": 0, "null_percentage": 0.0}}, "column_samples": {"data_de_vencimento": ["03/04/2025", "14/04/2025", "28/04/2025", "03/04/2025", "17/04/2025"], "competencia": ["-", "04/2025", "-", "04/2025", "04/2025"], "receber_de": ["BRADESCO 000000", "<PERSON>", "BANCO DO BRASIL 103291", "testse", "Junior Souza"], "categoria": ["RECEITA OPERACIONAL", "RECEITA OPERACIONAL", "RECEITA OPERACIONAL", "RECEITA OPERACIONAL", "RECEITA OPERACIONAL"], "classificacao": ["Prestação de Serviço", "Prestação de Serviço", "Prestação de Serviço", "Prestação de Serviço", "Prestação de Serviço"], "descricao": ["Lote - Cartão (DÉBITO - Banricompras)", "Orçamento #1601790", "Lote - Cartão (CIELO TESTE FABI)", "Lote - Cartão (CRÉDITO - Cielo)", "Orçamento #1597553"], "forma_de_pagamento": ["Pix", "Nota Promissória", "<PERSON><PERSON><PERSON>", "Cartão", "<PERSON><PERSON><PERSON>"], "observacao": ["-", "-", "-", "-", "-"], "nº_documento": ["-", "-", "-", "-", "-"], "tag": ["-", "-", "-", "-", "-"], "centro_de_custo": ["-", "-", "-", "-", "-"], "unidade": ["-", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "-", "-", "Morumbi"], "valor_liquido_r": [300.0, 203.0, 450.0, 2.5, 2800.0], "nota_emitida": ["Não", "Não", "Não", "Não", "Não"], "recibo_emitido": ["<PERSON>m", "Não", "Não", "Não", "Não"]}, "potential_keys": ["unidade", "valor_liquido_r", "nota_emitida", "recibo_emitido"], "potential_relationships": [], "analyzed_at": "2025-04-23T23:41:59.773324"}
{"file_name": "relatorio_creditos_disponiveis.xlsx", "module": "paciente", "row_count": 794, "column_count": 11, "columns": [{"original_name": "ID Amigo", "normalized_name": "id_amigo"}, {"original_name": "Paciente", "normalized_name": "paciente"}, {"original_name": "CPF", "normalized_name": "cpf"}, {"original_name": "Data Nasc.", "normalized_name": "data_nasc"}, {"original_name": "Sexo", "normalized_name": "sexo"}, {"original_name": "Vip", "normalized_name": "vip"}, {"original_name": "E-mail", "normalized_name": "e_mail"}, {"original_name": "<PERSON><PERSON><PERSON>", "normalized_name": "celular"}, {"original_name": "Crédito Pré-pago R$", "normalized_name": "credito_pre_pago_r"}, {"original_name": "Crédito Orçamento R$", "normalized_name": "credito_orcamento_r"}, {"original_name": "Total R$", "normalized_name": "total_r"}], "column_types": {"id_amigo": "int64", "paciente": "object", "cpf": "float64", "data_nasc": "object", "sexo": "object", "vip": "bool", "e_mail": "object", "celular": "object", "credito_pre_pago_r": "int64", "credito_orcamento_r": "int64", "total_r": "float64"}, "column_stats": {"id_amigo": {"min": 39306.0, "max": 90796209.0, "mean": 54967410.96095718, "null_count": 0, "null_percentage": 0.0}, "paciente": {"unique_values": 771, "most_common": "<PERSON>", "most_common_count": 6, "null_count": 0, "null_percentage": 0.0}, "cpf": {"min": 15053008.0, "max": 99440761040.0, "mean": 32534537397.564102, "null_count": 560, "null_percentage": 70.52896725440806}, "data_nasc": {"unique_values": 157, "most_common": NaN, "most_common_count": 610, "null_count": 610, "null_percentage": 76.82619647355163}, "sexo": {"unique_values": 2, "most_common": NaN, "most_common_count": 745, "null_count": 745, "null_percentage": 93.82871536523929}, "vip": {"unique_values": 2, "most_common": "Não", "most_common_count": 745, "null_count": 0, "null_percentage": 0.0}, "e_mail": {"unique_values": 373, "most_common": NaN, "most_common_count": 232, "null_count": 232, "null_percentage": 29.219143576826195}, "celular": {"unique_values": 486, "most_common": NaN, "most_common_count": 142, "null_count": 142, "null_percentage": 17.884130982367758}, "credito_pre_pago_r": {"min": -1500.0, "max": 50000.0, "mean": 1468.3639798488664, "null_count": 0, "null_percentage": 0.0}, "credito_orcamento_r": {"min": 0.0, "max": 108700.0, "mean": 6591.8753148614605, "null_count": 0, "null_percentage": 0.0}, "total_r": {"min": -250.0, "max": 108700.01, "mean": 8060.280793450881, "null_count": 0, "null_percentage": 0.0}}, "column_samples": {"id_amigo": [73061217, 23996049, 26714595, 85921930, 79707174], "paciente": ["Marissol Teste", "FERNANDO FONTES", "<PERSON> alexa<PERSON> teste", "<PERSON><PERSON><PERSON>", "Raiane - <PERSON><PERSON><PERSON><PERSON>"], "cpf": [97534161304.0, 25559552089.0, 24590696851.0, 49664245852.0, 195423135.0], "data_nasc": ["14/01/1985", "11/11/2021", "21/07/1993", "23/03/1981", "11/09/1987"], "sexo": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Feminino", "Feminino", "Feminino"], "vip": ["Não", "Não", "Não", "Não", "Não"], "e_mail": ["<EMAIL>", "leandr<PERSON><PERSON><PERSON>@icloud.com", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "celular": ["(11) 97505-4988", "(61) 99251-2333", "(61) 98402-4170", "(66) 99614-0022", "(11) 97505-4988"], "credito_pre_pago_r": [300, 0, 0, 0, 0], "credito_orcamento_r": [400, 4000, 900, 11500, 900], "total_r": [9000.0, 26623.45, 9000.0, 16000.0, 800.0]}, "potential_keys": ["id_amigo"], "potential_relationships": [{"column": "id_amigo", "possible_related_entity": "amigo"}], "analyzed_at": "2025-04-23T23:41:59.890672"}
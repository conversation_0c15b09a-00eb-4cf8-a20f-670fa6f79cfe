{"file_name": "relatorio_de_repasse.xlsx", "module": "financeiro", "row_count": 1, "column_count": 26, "columns": [{"original_name": "Data Atend", "normalized_name": "data_atend"}, {"original_name": "Data Pagto.", "normalized_name": "data_pagto"}, {"original_name": "Cód. atendimento", "normalized_name": "cod_atendimento"}, {"original_name": "Cód. financeiro", "normalized_name": "cod_financeiro"}, {"original_name": "Unidade", "normalized_name": "unidade"}, {"original_name": "Paciente", "normalized_name": "paciente"}, {"original_name": "Forma de Pagamento", "normalized_name": "forma_de_pagamento"}, {"original_name": "Profissional Executante", "normalized_name": "profissional_executante"}, {"original_name": "Grau part.", "normalized_name": "grau_part"}, {"original_name": "Profissional Solicitante", "normalized_name": "profissional_solicitante"}, {"original_name": "Procedimento/Matmed", "normalized_name": "procedimento_matmed"}, {"original_name": "Tipo", "normalized_name": "tipo"}, {"original_name": "<PERSON><PERSON><PERSON>", "normalized_name": "<PERSON><PERSON><PERSON>"}, {"original_name": "<PERSON><PERSON>a", "normalized_name": "glosa"}, {"original_name": "Desconto", "normalized_name": "desconto"}, {"original_name": "Líquido", "normalized_name": "liquido"}, {"original_name": "Repasse Executante", "normalized_name": "repasse_executante"}, {"original_name": "Percentual Repasse Executante", "normalized_name": "percentual_repasse_executante"}, {"original_name": "Repasse <PERSON>", "normalized_name": "repasse_solicitante"}, {"original_name": "Percentual Repasse Solicitante", "normalized_name": "percentual_repasse_solicitante"}, {"original_name": "Regra Repasse Executante", "normalized_name": "regra_repasse_executante"}, {"original_name": "Regra Repasse Solicitante", "normalized_name": "regra_repasse_solicitante"}, {"original_name": "Pagto. Repasse executante", "normalized_name": "pagto_repasse_executante"}, {"original_name": "Pagto. Repasse solicitante", "normalized_name": "pagto_repasse_solicitante"}, {"original_name": "Regra de repasse", "normalized_name": "regra_de_repasse"}, {"original_name": "Pagto. Repasse", "normalized_name": "pagto_repasse"}], "column_types": {"data_atend": "datetime64[ns]", "data_pagto": "datetime64[ns]", "cod_atendimento": "int64", "cod_financeiro": "int64", "unidade": "object", "paciente": "object", "forma_de_pagamento": "object", "profissional_executante": "object", "grau_part": "object", "profissional_solicitante": "float64", "procedimento_matmed": "object", "tipo": "object", "faturado": "float64", "glosa": "int64", "desconto": "int64", "liquido": "float64", "repasse_executante": "int64", "percentual_repasse_executante": "float64", "repasse_solicitante": "int64", "percentual_repasse_solicitante": "float64", "regra_repasse_executante": "float64", "regra_repasse_solicitante": "float64", "pagto_repasse_executante": "float64", "pagto_repasse_solicitante": "float64", "regra_de_repasse": "bool", "pagto_repasse": "float64"}, "column_stats": {"data_atend": {"unique_values": 1, "most_common": "23/04/2025", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "data_pagto": {"unique_values": 1, "most_common": "17/04/2025", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "cod_atendimento": {"min": 155686103.0, "max": 155686103.0, "mean": 155686103.0, "null_count": 0, "null_percentage": 0.0}, "cod_financeiro": {"min": 170684150.0, "max": 170684150.0, "mean": 170684150.0, "null_count": 0, "null_percentage": 0.0}, "unidade": {"unique_values": 1, "most_common": "Morumbi", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "paciente": {"unique_values": 1, "most_common": "<PERSON>", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "forma_de_pagamento": {"unique_values": 1, "most_common": "Pix", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "profissional_executante": {"unique_values": 1, "most_common": "Equipe Amigo - BRUNO LIMA", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "grau_part": {"unique_values": 1, "most_common": "Executante", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "profissional_solicitante": {"min": null, "max": null, "mean": null, "null_count": 1, "null_percentage": 100.0}, "procedimento_matmed": {"unique_values": 1, "most_common": "BOTOX MALAR", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "tipo": {"unique_values": 1, "most_common": "Procedimento", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "faturado": {"min": 952.38, "max": 952.38, "mean": 952.38, "null_count": 0, "null_percentage": 0.0}, "glosa": {"min": 0.0, "max": 0.0, "mean": 0.0, "null_count": 0, "null_percentage": 0.0}, "desconto": {"min": 0.0, "max": 0.0, "mean": 0.0, "null_count": 0, "null_percentage": 0.0}, "liquido": {"min": 952.38, "max": 952.38, "mean": 952.38, "null_count": 0, "null_percentage": 0.0}, "repasse_executante": {"min": 0.0, "max": 0.0, "mean": 0.0, "null_count": 0, "null_percentage": 0.0}, "percentual_repasse_executante": {"min": null, "max": null, "mean": null, "null_count": 1, "null_percentage": 100.0}, "repasse_solicitante": {"min": 0.0, "max": 0.0, "mean": 0.0, "null_count": 0, "null_percentage": 0.0}, "percentual_repasse_solicitante": {"min": null, "max": null, "mean": null, "null_count": 1, "null_percentage": 100.0}, "regra_repasse_executante": {"min": null, "max": null, "mean": null, "null_count": 1, "null_percentage": 100.0}, "regra_repasse_solicitante": {"min": null, "max": null, "mean": null, "null_count": 1, "null_percentage": 100.0}, "pagto_repasse_executante": {"min": null, "max": null, "mean": null, "null_count": 1, "null_percentage": 100.0}, "pagto_repasse_solicitante": {"min": null, "max": null, "mean": null, "null_count": 1, "null_percentage": 100.0}, "regra_de_repasse": {"unique_values": 1, "most_common": "Não", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "pagto_repasse": {"min": null, "max": null, "mean": null, "null_count": 1, "null_percentage": 100.0}}, "column_samples": {"data_atend": ["23/04/2025"], "data_pagto": ["17/04/2025"], "cod_atendimento": [155686103], "cod_financeiro": [170684150], "unidade": ["Morumbi"], "paciente": ["<PERSON>"], "forma_de_pagamento": ["Pix"], "profissional_executante": ["Equipe Amigo - BRUNO LIMA"], "grau_part": ["Executante"], "profissional_solicitante": [], "procedimento_matmed": ["BOTOX MALAR"], "tipo": ["Procedimento"], "faturado": [952.38], "glosa": [0], "desconto": [0], "liquido": [952.38], "repasse_executante": [0], "percentual_repasse_executante": [], "repasse_solicitante": [0], "percentual_repasse_solicitante": [], "regra_repasse_executante": [], "regra_repasse_solicitante": [], "pagto_repasse_executante": [], "pagto_repasse_solicitante": [], "regra_de_repasse": ["Não"], "pagto_repasse": []}, "potential_keys": ["data_atend", "data_pagto", "cod_atendimento", "cod_financeiro", "unidade", "paciente", "forma_de_pagamento", "profissional_executante", "grau_part", "procedimento_matmed", "tipo", "<PERSON><PERSON><PERSON>", "glosa", "desconto", "liquido", "repasse_executante", "repasse_solicitante", "regra_de_repasse"], "potential_relationships": [], "analyzed_at": "2025-04-23T23:41:59.736963"}
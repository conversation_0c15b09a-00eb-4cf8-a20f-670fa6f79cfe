{"file_name": "relatorio_fechamento_caixa_diário.xlsx", "module": "financeiro", "row_count": 85, "column_count": 14, "columns": [{"original_name": "Data Transação", "normalized_name": "data_transacao"}, {"original_name": "Data Atendimento", "normalized_name": "data_atendimento"}, {"original_name": "Paciente", "normalized_name": "paciente"}, {"original_name": "Executante", "normalized_name": "executante"}, {"original_name": "Sol. Primário", "normalized_name": "sol_primario"}, {"original_name": "Sol. Secundário", "normalized_name": "sol_secundario"}, {"original_name": "Editor financeiro", "normalized_name": "editor_financeiro"}, {"original_name": "Qtd / Procedimento", "normalized_name": "qtd_procedimento"}, {"original_name": "Forma de Pagamento", "normalized_name": "forma_de_pagamento"}, {"original_name": "NSU(DOC/CV/ID)", "normalized_name": "nsu_doc_cv_id"}, {"original_name": "Orçamento", "normalized_name": "orcamento"}, {"original_name": "Nº Orçamento", "normalized_name": "nº_orcamento"}, {"original_name": "Observação", "normalized_name": "observacao"}, {"original_name": "Valor R$", "normalized_name": "valor_r"}], "column_types": {"data_transacao": "object", "data_atendimento": "datetime64[ns]", "paciente": "object", "executante": "object", "sol_primario": "object", "sol_secundario": "object", "editor_financeiro": "object", "qtd_procedimento": "object", "forma_de_pagamento": "object", "nsu_doc_cv_id": "float64", "orcamento": "bool", "nº_orcamento": "object", "observacao": "float64", "valor_r": "float64"}, "column_stats": {"data_transacao": {"unique_values": 18, "most_common": "07/04/2025", "most_common_count": 15, "null_count": 0, "null_percentage": 0.0}, "data_atendimento": {"unique_values": 18, "most_common": "07/04/2025", "most_common_count": 15, "null_count": 0, "null_percentage": 0.0}, "paciente": {"unique_values": 51, "most_common": "<PERSON><PERSON>", "most_common_count": 5, "null_count": 0, "null_percentage": 0.0}, "executante": {"unique_values": 18, "most_common": "Orçamentos Fechados", "most_common_count": 27, "null_count": 0, "null_percentage": 0.0}, "sol_primario": {"unique_values": 1, "most_common": NaN, "most_common_count": 83, "null_count": 83, "null_percentage": 97.6470588235294}, "sol_secundario": {"unique_values": 2, "most_common": NaN, "most_common_count": 83, "null_count": 83, "null_percentage": 97.6470588235294}, "editor_financeiro": {"unique_values": 17, "most_common": "Equipe Amigo - Caio <PERSON>", "most_common_count": 23, "null_count": 0, "null_percentage": 0.0}, "qtd_procedimento": {"unique_values": 26, "most_common": "1 - Consulta amigo tech - teste", "most_common_count": 14, "null_count": 10, "null_percentage": 11.76470588235294}, "forma_de_pagamento": {"unique_values": 17, "most_common": "Pix", "most_common_count": 28, "null_count": 0, "null_percentage": 0.0}, "nsu_doc_cv_id": {"min": 649208.0, "max": 654165465.0, "mean": 171820117.75, "null_count": 81, "null_percentage": 95.29411764705881}, "orcamento": {"unique_values": 2, "most_common": "Não", "most_common_count": 58, "null_count": 0, "null_percentage": 0.0}, "nº_orcamento": {"unique_values": 22, "most_common": NaN, "most_common_count": 58, "null_count": 58, "null_percentage": 68.23529411764706}, "observacao": {"min": null, "max": null, "mean": null, "null_count": 85, "null_percentage": 100.0}, "valor_r": {"min": 0.0, "max": 20000.0, "mean": 1738.5552941176472, "null_count": 0, "null_percentage": 0.0}}, "column_samples": {"data_transacao": ["01/04/2025", "16/04/2025", "21/04/2025", "10/04/2025", "01/04/2025"], "data_atendimento": ["08/04/2025", "01/04/2025", "21/04/2025", "07/04/2025", "14/04/2025"], "paciente": ["<PERSON>", "caio menezes", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "executante": ["Equipe Amigo - Caio <PERSON>", "Orçamentos Fechados", "Equipe Amigo - Ana Victoria <PERSON> Almeida", "Orçamentos Fechados", "Equipe Amigo - BRUNO LIMA"], "sol_primario": ["Equipe Amigo - Ana Victoria <PERSON> Almeida", "Equipe Amigo - Ana Victoria <PERSON> Almeida"], "sol_secundario": ["Equipe Amigo - Al<PERSON>", "Equipe Amigo - Giulia Pedrosa 2"], "editor_financeiro": ["Equipe Amigo - BRUNO LIMA", "Equipe AMIGO - Ayrton Neto", "Equipe <PERSON>igo - <PERSON>", "EQUIPE AMIGO - <PERSON><PERSON><PERSON><PERSON>", "Equipe Amigo - Caio <PERSON>"], "qtd_procedimento": ["1 - Sessão para assistência fisioterapêutica ambulatorial ao paciente com disfunção decorrente de alterações do sistema músculo-esquelético, 1 - Sessão para assistência fisioterapêutica ambulatorial para alterações inflamatórias e ou degenerativas do aparelho genito-urinário e reprodutor", "1 - FIO PDO - <PERSON><PERSON><PERSON>, 1 - <PERSON><PERSON><PERSON> MALAR, 1 - <PERSON><PERSON>", "1 - Consulta - 10101012", "1 - Consulta - 10101012", "1 - 1ª Sessão de psicoterapia"], "forma_de_pagamento": ["<PERSON><PERSON><PERSON>", "Cartão 1x", "Pix", "BRADESCO", "Pendente"], "nsu_doc_cv_id": [649208.0, 31231231.0, 654165465.0, 1234567.0], "orcamento": ["Não", "Não", "Não", "Não", "Não"], "nº_orcamento": ["OR1600015", "OR1577012", "OR1601790", "OR1579959", "OR1576357"], "observacao": [], "valor_r": [300.0, 60.0, 90.0, 80.0, 500.0]}, "potential_keys": ["nsu_doc_cv_id"], "potential_relationships": [{"column": "nsu_doc_cv_id", "possible_related_entity": "nsu_doc_cv"}], "analyzed_at": "2025-04-23T23:41:59.665055"}
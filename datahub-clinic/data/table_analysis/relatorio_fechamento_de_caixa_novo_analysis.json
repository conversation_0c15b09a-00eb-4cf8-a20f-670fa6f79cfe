{"file_name": "relatorio_fechamento_de_caixa_novo.xlsx", "module": "financeiro", "row_count": 8, "column_count": 18, "columns": [{"original_name": "Data Transação", "normalized_name": "data_transacao"}, {"original_name": "Data", "normalized_name": "data"}, {"original_name": "Código", "normalized_name": "codigo"}, {"original_name": "Tipo", "normalized_name": "tipo"}, {"original_name": "Paciente", "normalized_name": "paciente"}, {"original_name": "Executante", "normalized_name": "executante"}, {"original_name": "Sol. Primário", "normalized_name": "sol_primario"}, {"original_name": "Sol. Secundário", "normalized_name": "sol_secundario"}, {"original_name": "Qtd - Procedimento", "normalized_name": "qtd_procedimento"}, {"original_name": "Editor <PERSON><PERSON>", "normalized_name": "editor_financeiro"}, {"original_name": "Pagamento Efetuado", "normalized_name": "pagamento_efetuado"}, {"original_name": "Forma de Pagamento", "normalized_name": "forma_de_pagamento"}, {"original_name": "NSU(DOC/CV/ID)", "normalized_name": "nsu_doc_cv_id"}, {"original_name": "Observação", "normalized_name": "observacao"}, {"original_name": "Pessoa Física", "normalized_name": "pessoa_fisica"}, {"original_name": "Valor R$", "normalized_name": "valor_r"}, {"original_name": "N° Nota Fiscal", "normalized_name": "n_nota_fiscal"}, {"original_name": "Recibo Emitido", "normalized_name": "recibo_emitido"}], "column_types": {"data_transacao": "datetime64[ns]", "data": "datetime64[ns]", "codigo": "int64", "tipo": "object", "paciente": "object", "executante": "object", "sol_primario": "float64", "sol_secundario": "float64", "qtd_procedimento": "object", "editor_financeiro": "object", "pagamento_efetuado": "bool", "forma_de_pagamento": "object", "nsu_doc_cv_id": "float64", "observacao": "float64", "pessoa_fisica": "bool", "valor_r": "float64", "n_nota_fiscal": "float64", "recibo_emitido": "bool"}, "column_stats": {"data_transacao": {"unique_values": 1, "most_common": "22/04/2025", "most_common_count": 8, "null_count": 0, "null_percentage": 0.0}, "data": {"unique_values": 3, "most_common": "22/04/2025", "most_common_count": 6, "null_count": 0, "null_percentage": 0.0}, "codigo": {"min": 151314579.0, "max": 155723069.0, "mean": 154308580.125, "null_count": 0, "null_percentage": 0.0}, "tipo": {"unique_values": 1, "most_common": "Atendimento", "most_common_count": 8, "null_count": 0, "null_percentage": 0.0}, "paciente": {"unique_values": 6, "most_common": "ACRISIO JP TESTE", "most_common_count": 3, "null_count": 0, "null_percentage": 0.0}, "executante": {"unique_values": 4, "most_common": "Plano de Tratamento", "most_common_count": 3, "null_count": 0, "null_percentage": 0.0}, "sol_primario": {"min": null, "max": null, "mean": null, "null_count": 8, "null_percentage": 100.0}, "sol_secundario": {"min": null, "max": null, "mean": null, "null_count": 8, "null_percentage": 100.0}, "qtd_procedimento": {"unique_values": 3, "most_common": NaN, "most_common_count": 3, "null_count": 3, "null_percentage": 37.5}, "editor_financeiro": {"unique_values": 5, "most_common": "Equipe Amigo - Caio <PERSON>", "most_common_count": 3, "null_count": 0, "null_percentage": 0.0}, "pagamento_efetuado": {"unique_values": 2, "most_common": "Não", "most_common_count": 7, "null_count": 0, "null_percentage": 0.0}, "forma_de_pagamento": {"unique_values": 5, "most_common": "Pendente", "most_common_count": 4, "null_count": 0, "null_percentage": 0.0}, "nsu_doc_cv_id": {"min": null, "max": null, "mean": null, "null_count": 8, "null_percentage": 100.0}, "observacao": {"min": null, "max": null, "mean": null, "null_count": 8, "null_percentage": 100.0}, "pessoa_fisica": {"unique_values": 1, "most_common": "Não", "most_common_count": 8, "null_count": 0, "null_percentage": 0.0}, "valor_r": {"min": 80.0, "max": 9000.0, "mean": 1728.3333333333333, "null_count": 2, "null_percentage": 25.0}, "n_nota_fiscal": {"min": null, "max": null, "mean": null, "null_count": 8, "null_percentage": 100.0}, "recibo_emitido": {"unique_values": 1, "most_common": "Não", "most_common_count": 8, "null_count": 0, "null_percentage": 0.0}}, "column_samples": {"data_transacao": ["22/04/2025", "22/04/2025", "22/04/2025", "22/04/2025", "22/04/2025"], "data": ["22/04/2025", "22/04/2025", "22/04/2025", "22/04/2025", "22/04/2025"], "codigo": [153651062, 153658697, 155723069, 151314579, 155491950], "tipo": ["Atendimento", "Atendimento", "Atendimento", "Atendimento", "Atendimento"], "paciente": ["ACRISIO JP TESTE", "ACRISIO JP TESTE", "<PERSON><PERSON>", "Junior Souza", "MARINA HAZIN CONVÊNIO"], "executante": ["Plano de Tratamento", "Equipe Amigo - Caio <PERSON>", "Plano de Tratamento", "Equipe Amigo - Caio <PERSON>", "Plano de Tratamento"], "sol_primario": [], "sol_secundario": [], "qtd_procedimento": ["1 - <PERSON><PERSON><PERSON> - teste", "1 - <PERSON><PERSON><PERSON> - teste", "1 - <PERSON><PERSON><PERSON> - teste", "1 - <PERSON><PERSON><PERSON>, 1 - <PERSON><PERSON>faroplastia", "1 - 1ª Sessão de psicoterapia"], "editor_financeiro": ["Equipe Amigo - BRUNO LIMA", "<PERSON>", "<PERSON><PERSON>", "Sem editor finance<PERSON>", "Equipe Amigo - Caio <PERSON>"], "pagamento_efetuado": ["<PERSON>m", "Não", "Não", "Não", "Não"], "forma_de_pagamento": ["Cancelou", "Pendente", "Pendente", "Pendente", "BRADESCO"], "nsu_doc_cv_id": [], "observacao": [], "pessoa_fisica": ["Não", "Não", "Não", "Não", "Não"], "valor_r": [1000.0, 80.0, 9000.0, 100.0, 90.0], "n_nota_fiscal": [], "recibo_emitido": ["Não", "Não", "Não", "Não", "Não"]}, "potential_keys": ["codigo", "nsu_doc_cv_id", "recibo_emitido"], "potential_relationships": [{"column": "nsu_doc_cv_id", "possible_related_entity": "nsu_doc_cv"}], "analyzed_at": "2025-04-23T23:41:59.753072"}
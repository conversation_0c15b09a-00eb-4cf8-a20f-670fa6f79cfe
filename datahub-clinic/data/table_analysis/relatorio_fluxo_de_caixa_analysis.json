{"file_name": "relatorio_fluxo_de_caixa.xlsx", "module": "financeiro", "row_count": 28, "column_count": 13, "columns": [{"original_name": "Unnamed: 0", "normalized_name": "unnamed_0"}, {"original_name": "jan/25", "normalized_name": "jan_25"}, {"original_name": "fev/25", "normalized_name": "fev_25"}, {"original_name": "mar/25", "normalized_name": "mar_25"}, {"original_name": "abr/25", "normalized_name": "abr_25"}, {"original_name": "mai/25", "normalized_name": "mai_25"}, {"original_name": "jun/25", "normalized_name": "jun_25"}, {"original_name": "jul/25", "normalized_name": "jul_25"}, {"original_name": "ago/25", "normalized_name": "ago_25"}, {"original_name": "set/25", "normalized_name": "set_25"}, {"original_name": "out/25", "normalized_name": "out_25"}, {"original_name": "nov/25", "normalized_name": "nov_25"}, {"original_name": "dez/25", "normalized_name": "dez_25"}], "column_types": {"unnamed_0": "object", "jan_25": "float64", "fev_25": "float64", "mar_25": "float64", "abr_25": "float64", "mai_25": "float64", "jun_25": "float64", "jul_25": "float64", "ago_25": "float64", "set_25": "float64", "out_25": "float64", "nov_25": "float64", "dez_25": "float64"}, "column_stats": {"unnamed_0": {"unique_values": 28, "most_common": "Entradas operacionais de caixa", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "jan_25": {"min": -1350.0, "max": 13002138.77, "mean": 934560.0264285713, "null_count": 0, "null_percentage": 0.0}, "fev_25": {"min": -26707.0, "max": 13081808.07, "mean": 945796.1907142857, "null_count": 0, "null_percentage": 0.0}, "mar_25": {"min": -18186.0, "max": 13131434.07, "mean": 945049.005, "null_count": 0, "null_percentage": 0.0}, "abr_25": {"min": -4548.0, "max": 13176624.24, "mean": 947643.1842857143, "null_count": 0, "null_percentage": 0.0}, "mai_25": {"min": 0.0, "max": 13176624.24, "mean": 941187.4457142857, "null_count": 0, "null_percentage": 0.0}, "jun_25": {"min": 0.0, "max": 13176624.24, "mean": 941187.4457142857, "null_count": 0, "null_percentage": 0.0}, "jul_25": {"min": 0.0, "max": 13176624.24, "mean": 941187.4457142857, "null_count": 0, "null_percentage": 0.0}, "ago_25": {"min": 0.0, "max": 13176624.24, "mean": 941187.4457142857, "null_count": 0, "null_percentage": 0.0}, "set_25": {"min": 0.0, "max": 13176624.24, "mean": 941187.4457142857, "null_count": 0, "null_percentage": 0.0}, "out_25": {"min": 0.0, "max": 13176624.24, "mean": 941187.4457142857, "null_count": 0, "null_percentage": 0.0}, "nov_25": {"min": 0.0, "max": 13176624.24, "mean": 941187.4457142857, "null_count": 0, "null_percentage": 0.0}, "dez_25": {"min": 0.0, "max": 13176624.24, "mean": 941187.4457142857, "null_count": 0, "null_percentage": 0.0}}, "column_samples": {"unnamed_0": ["-      PROVISÃO DE RECEITA FUTURA", "-  DESPESAS TRIBUTÁRIAS", "Saldo final", "-      Outras despesas", "-  DESPESAS ADMINISTRATIVAS"], "jan_25": [0.0, 0.0, 12961287.969999999, 3600.0, 38100.8], "fev_25": [79669.3, -10150.0, 8100.0, 79669.3, 0.0], "mar_25": [-454.0, 17812.0, 0.0, 0.0, 13131434.07], "abr_25": [49578.17, -100.0, 0.0, -2448.0, -2348.0], "mai_25": [0.0, 0.0, 0.0, 0.0, 0.0], "jun_25": [13176624.24, 0.0, 0.0, 0.0, 0.0], "jul_25": [13176624.24, 0.0, 0.0, 0.0, 0.0], "ago_25": [0.0, 0.0, 0.0, 0.0, 0.0], "set_25": [0.0, 0.0, 0.0, 0.0, 0.0], "out_25": [0.0, 13176624.24, 0.0, 0.0, 0.0], "nov_25": [0.0, 0.0, 0.0, 0.0, 0.0], "dez_25": [0.0, 0.0, 0.0, 0.0, 0.0]}, "potential_keys": ["unnamed_0"], "potential_relationships": [], "analyzed_at": "2025-04-23T23:41:59.722667"}
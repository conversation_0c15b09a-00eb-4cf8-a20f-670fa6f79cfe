{"file_name": "relatorio_mapacirurgico.xlsx", "module": "agenda", "row_count": 14, "column_count": 28, "columns": [{"original_name": "Data do agendamento", "normalized_name": "data_do_agendamento"}, {"original_name": "<PERSON><PERSON>", "normalized_name": "hora"}, {"original_name": "Unidade", "normalized_name": "unidade"}, {"original_name": "Sala", "normalized_name": "sala"}, {"original_name": "Cód. Leg.", "normalized_name": "cod_leg"}, {"original_name": "Paciente", "normalized_name": "paciente"}, {"original_name": "<PERSON><PERSON>", "normalized_name": "idade"}, {"original_name": "Telefone", "normalized_name": "telefone"}, {"original_name": "Tipo de Atendimento", "normalized_name": "tipo_de_atendimento"}, {"original_name": "Qtd/Procedimento", "normalized_name": "qtd_procedimento"}, {"original_name": "Forma de Pagamento", "normalized_name": "forma_de_pagamento"}, {"original_name": "Acomod.", "normalized_name": "acomod"}, {"original_name": "<PERSON><PERSON><PERSON><PERSON>", "normalized_name": "matricula"}, {"original_name": "<PERSON><PERSON><PERSON>", "normalized_name": "atend_confirmado"}, {"original_name": "00 - <PERSON><PERSON><PERSON><PERSON><PERSON>", "normalized_name": "00_cirurgiao"}, {"original_name": "01 - 1º Auxiliar", "normalized_name": "01_1º_auxiliar"}, {"original_name": "02 - 2º Auxiliar", "normalized_name": "02_2º_auxiliar"}, {"original_name": "03 - 3º Auxiliar", "normalized_name": "03_3º_auxiliar"}, {"original_name": "04 - 4º Auxiliar", "normalized_name": "04_4º_auxiliar"}, {"original_name": "05 - Instrument<PERSON>", "normalized_name": "05_instrumentador"}, {"original_name": "06 - <PERSON><PERSON><PERSON><PERSON>", "normalized_name": "06_anestesista"}, {"original_name": "07 - <PERSON><PERSON><PERSON><PERSON> Anestesista", "normalized_name": "07_auxiliar_de_anestesista"}, {"original_name": "08 - Consultor", "normalized_name": "08_consultor"}, {"original_name": "09 - <PERSON><PERSON><PERSON>", "normalized_name": "09_perfusionista"}, {"original_name": "10 - Pediatra na sala de parto", "normalized_name": "10_pediatra_na_sala_de_parto"}, {"original_name": "11 - Auxiliar SADT", "normalized_name": "11_auxiliar_sadt"}, {"original_name": "12 - Clínico", "normalized_name": "12_clinico"}, {"original_name": "13 - Intensivista", "normalized_name": "13_intensivista"}], "column_types": {"data_do_agendamento": "datetime64[ns]", "hora": "datetime64[ns]", "unidade": "object", "sala": "float64", "cod_leg": "float64", "paciente": "object", "idade": "object", "telefone": "object", "tipo_de_atendimento": "object", "qtd_procedimento": "object", "forma_de_pagamento": "object", "acomod": "float64", "matricula": "float64", "atend_confirmado": "bool", "00_cirurgiao": "object", "01_1º_auxiliar": "float64", "02_2º_auxiliar": "float64", "03_3º_auxiliar": "float64", "04_4º_auxiliar": "float64", "05_instrumentador": "float64", "06_anestesista": "float64", "07_auxiliar_de_anestesista": "float64", "08_consultor": "float64", "09_perfusionista": "float64", "10_pediatra_na_sala_de_parto": "float64", "11_auxiliar_sadt": "float64", "12_clinico": "float64", "13_intensivista": "float64"}, "column_stats": {"data_do_agendamento": {"unique_values": 4, "most_common": "25/04/2025", "most_common_count": 5, "null_count": 0, "null_percentage": 0.0}, "hora": {"unique_values": 6, "most_common": "10:00", "most_common_count": 5, "null_count": 0, "null_percentage": 0.0}, "unidade": {"unique_values": 4, "most_common": "Morumbi", "most_common_count": 8, "null_count": 0, "null_percentage": 0.0}, "sala": {"min": null, "max": null, "mean": null, "null_count": 14, "null_percentage": 100.0}, "cod_leg": {"min": null, "max": null, "mean": null, "null_count": 14, "null_percentage": 100.0}, "paciente": {"unique_values": 13, "most_common": "MARINA HAZIN", "most_common_count": 2, "null_count": 0, "null_percentage": 0.0}, "idade": {"unique_values": 3, "most_common": NaN, "most_common_count": 11, "null_count": 11, "null_percentage": 78.57142857142857}, "telefone": {"unique_values": 6, "most_common": NaN, "most_common_count": 6, "null_count": 6, "null_percentage": 42.857142857142854}, "tipo_de_atendimento": {"unique_values": 1, "most_common": "Sessao de Fisioterapia", "most_common_count": 14, "null_count": 0, "null_percentage": 0.0}, "qtd_procedimento": {"unique_values": 1, "most_common": "1 - <PERSON><PERSON><PERSON> - teste", "most_common_count": 14, "null_count": 0, "null_percentage": 0.0}, "forma_de_pagamento": {"unique_values": 4, "most_common": NaN, "most_common_count": 7, "null_count": 7, "null_percentage": 50.0}, "acomod": {"min": null, "max": null, "mean": null, "null_count": 14, "null_percentage": 100.0}, "matricula": {"min": 132465.0, "max": 782424624646.0, "mean": 156491708860.0, "null_count": 9, "null_percentage": 64.28571428571429}, "atend_confirmado": {"unique_values": 2, "most_common": "Não", "most_common_count": 13, "null_count": 0, "null_percentage": 0.0}, "00_cirurgiao": {"unique_values": 5, "most_common": "Equipe Amigo - BRUNO LIMA", "most_common_count": 6, "null_count": 0, "null_percentage": 0.0}, "01_1º_auxiliar": {"min": null, "max": null, "mean": null, "null_count": 14, "null_percentage": 100.0}, "02_2º_auxiliar": {"min": null, "max": null, "mean": null, "null_count": 14, "null_percentage": 100.0}, "03_3º_auxiliar": {"min": null, "max": null, "mean": null, "null_count": 14, "null_percentage": 100.0}, "04_4º_auxiliar": {"min": null, "max": null, "mean": null, "null_count": 14, "null_percentage": 100.0}, "05_instrumentador": {"min": null, "max": null, "mean": null, "null_count": 14, "null_percentage": 100.0}, "06_anestesista": {"min": null, "max": null, "mean": null, "null_count": 14, "null_percentage": 100.0}, "07_auxiliar_de_anestesista": {"min": null, "max": null, "mean": null, "null_count": 14, "null_percentage": 100.0}, "08_consultor": {"min": null, "max": null, "mean": null, "null_count": 14, "null_percentage": 100.0}, "09_perfusionista": {"min": null, "max": null, "mean": null, "null_count": 14, "null_percentage": 100.0}, "10_pediatra_na_sala_de_parto": {"min": null, "max": null, "mean": null, "null_count": 14, "null_percentage": 100.0}, "11_auxiliar_sadt": {"min": null, "max": null, "mean": null, "null_count": 14, "null_percentage": 100.0}, "12_clinico": {"min": null, "max": null, "mean": null, "null_count": 14, "null_percentage": 100.0}, "13_intensivista": {"min": null, "max": null, "mean": null, "null_count": 14, "null_percentage": 100.0}}, "column_samples": {"data_do_agendamento": ["25/04/2025", "21/04/2025", "23/04/2025", "21/04/2025", "22/04/2025"], "hora": ["10:00", "10:00", "17:00", "15:00", "17:00"], "unidade": ["Morumbi", "Morumbi", "BRASILIA", "CLÍNICA (RL)", "Recife"], "sala": [], "cod_leg": [], "paciente": ["<PERSON> teste DF", "MARINA HAZIN CONVÊNIO", "Victória Almeida", "<PERSON> teste", "<PERSON><PERSON>"], "idade": ["29 anos", "30 anos", "37 anos"], "telefone": ["(11) 94020-9917", "(81) 99696-4897", "(11) 97505-4988", "(81) 99696-4897", "(21) 99435-2590"], "tipo_de_atendimento": ["Sessao de Fisioterapia", "Sessao de Fisioterapia", "Sessao de Fisioterapia", "Sessao de Fisioterapia", "Sessao de Fisioterapia"], "qtd_procedimento": ["1 - <PERSON><PERSON><PERSON> - teste", "1 - <PERSON><PERSON><PERSON> - teste", "1 - <PERSON><PERSON><PERSON> - teste", "1 - <PERSON><PERSON><PERSON> - teste", "1 - <PERSON><PERSON><PERSON> - teste"], "forma_de_pagamento": ["Pix", "BRADESCO", "Cartão", "BRADESCO", "AMIL"], "acomod": [], "matricula": [21321321.0, 231312.0, 12234556.0, 782424624646.0, 132465.0], "atend_confirmado": ["Não", "Não", "Não", "Não", "Não"], "00_cirurgiao": ["Equipe Amigo - BRUNO LIMA", "Equipe Amigo - BRUNO LIMA", "Rayara <PERSON> Souza", "Equipe Amigo - Giulia Pedrosa 2", "Equipe Amigo - Caio <PERSON>"], "01_1º_auxiliar": [], "02_2º_auxiliar": [], "03_3º_auxiliar": [], "04_4º_auxiliar": [], "05_instrumentador": [], "06_anestesista": [], "07_auxiliar_de_anestesista": [], "08_consultor": [], "09_perfusionista": [], "10_pediatra_na_sala_de_parto": [], "11_auxiliar_sadt": [], "12_clinico": [], "13_intensivista": []}, "potential_keys": ["unidade", "cod_leg", "idade"], "potential_relationships": [], "analyzed_at": "2025-04-23T23:42:00.060745"}
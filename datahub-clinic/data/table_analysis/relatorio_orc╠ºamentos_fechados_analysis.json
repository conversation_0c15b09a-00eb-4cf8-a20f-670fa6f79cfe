{"file_name": "relatorio_orçamentos_fechados.xlsx", "module": "paciente", "row_count": 14, "column_count": 10, "columns": [{"original_name": "ID Amigo", "normalized_name": "id_amigo"}, {"original_name": "Paciente", "normalized_name": "paciente"}, {"original_name": "CPF", "normalized_name": "cpf"}, {"original_name": "Data Nasc.", "normalized_name": "data_nasc"}, {"original_name": "Sexo", "normalized_name": "sexo"}, {"original_name": "Vip", "normalized_name": "vip"}, {"original_name": "E-mail", "normalized_name": "e_mail"}, {"original_name": "<PERSON><PERSON><PERSON>", "normalized_name": "celular"}, {"original_name": "Orçamentos", "normalized_name": "orcamentos"}, {"original_name": "Total R$", "normalized_name": "total_r"}], "column_types": {"id_amigo": "int64", "paciente": "object", "cpf": "float64", "data_nasc": "datetime64[ns]", "sexo": "object", "vip": "bool", "e_mail": "object", "celular": "object", "orcamentos": "int64", "total_r": "int64"}, "column_stats": {"id_amigo": {"min": 19085497.0, "max": 90802077.0, "mean": 76271349.21428572, "null_count": 0, "null_percentage": 0.0}, "paciente": {"unique_values": 14, "most_common": "Junior Souza", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "cpf": {"min": 195423135.0, "max": 75731649120.0, "mean": 22922115628.2, "null_count": 4, "null_percentage": 28.57142857142857}, "data_nasc": {"unique_values": 5, "most_common": NaN, "most_common_count": 7, "null_count": 7, "null_percentage": 50.0}, "sexo": {"unique_values": 1, "most_common": NaN, "most_common_count": 10, "null_count": 10, "null_percentage": 71.42857142857143}, "vip": {"unique_values": 2, "most_common": "Não", "most_common_count": 13, "null_count": 0, "null_percentage": 0.0}, "e_mail": {"unique_values": 9, "most_common": "<EMAIL>", "most_common_count": 3, "null_count": 3, "null_percentage": 21.428571428571427}, "celular": {"unique_values": 10, "most_common": "(11) 97505-4988", "most_common_count": 3, "null_count": 2, "null_percentage": 14.285714285714285}, "orcamentos": {"min": 1.0, "max": 2.0, "mean": 1.3571428571428572, "null_count": 0, "null_percentage": 0.0}, "total_r": {"min": 300.0, "max": 20500.0, "mean": 9040.42857142857, "null_count": 0, "null_percentage": 0.0}}, "column_samples": {"id_amigo": [75656153, 90643157, 72562209, 64447608, 38204852], "paciente": ["<PERSON>", "Junior Souza", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "JONAS CASTRO FILHO"], "cpf": [75731649120.0, 22587999022.0, 195423135.0, 5149639486.0, 9704130767.0], "data_nasc": ["23/03/1981", "21/09/1995", "21/09/1995", "29/05/1997", "21/09/1995"], "sexo": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "vip": ["Não", "Não", "Não", "Não", "<PERSON>m"], "e_mail": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "celular": ["(11) 97505-4988", "(11) 97645-8283", "(11) 97155-6404", "(27) 99693-2233", "(31) 99572-0954"], "orcamentos": [1, 1, 1, 2, 2], "total_r": [20500, 300, 9000, 5000, 14000]}, "potential_keys": ["id_amigo", "paciente"], "potential_relationships": [{"column": "id_amigo", "possible_related_entity": "amigo"}], "analyzed_at": "2025-04-23T23:41:59.814304"}
{"file_name": "relatorio_producao_medica2.xlsx", "module": "agenda", "row_count": 72, "column_count": 9, "columns": [{"original_name": "Data Atendimento", "normalized_name": "data_atendimento"}, {"original_name": "<PERSON><PERSON><PERSON>", "normalized_name": "codigo_atendimento"}, {"original_name": "Unidade", "normalized_name": "unidade"}, {"original_name": "Paciente", "normalized_name": "paciente"}, {"original_name": "Profissional", "normalized_name": "profissional"}, {"original_name": "Tipo de Atendimento", "normalized_name": "tipo_de_atendimento"}, {"original_name": "Qtd/Procedimento", "normalized_name": "qtd_procedimento"}, {"original_name": "Forma de Pagamento", "normalized_name": "forma_de_pagamento"}, {"original_name": "Valor R$", "normalized_name": "valor_r"}], "column_types": {"data_atendimento": "object", "codigo_atendimento": "int64", "unidade": "object", "paciente": "object", "profissional": "object", "tipo_de_atendimento": "object", "qtd_procedimento": "object", "forma_de_pagamento": "object", "valor_r": "float64"}, "column_stats": {"data_atendimento": {"unique_values": 61, "most_common": "01/04/2025 12:00", "most_common_count": 5, "null_count": 0, "null_percentage": 0.0}, "codigo_atendimento": {"min": 148577077.0, "max": 155741330.0, "mean": 153790468.91666666, "null_count": 0, "null_percentage": 0.0}, "unidade": {"unique_values": 5, "most_common": "BRASILIA", "most_common_count": 37, "null_count": 0, "null_percentage": 0.0}, "paciente": {"unique_values": 50, "most_common": "<PERSON><PERSON>", "most_common_count": 5, "null_count": 0, "null_percentage": 0.0}, "profissional": {"unique_values": 17, "most_common": "Equipe Amigo - Caio <PERSON>", "most_common_count": 23, "null_count": 0, "null_percentage": 0.0}, "tipo_de_atendimento": {"unique_values": 18, "most_common": "CONSULTA  COM CARDIOLOGISTA", "most_common_count": 23, "null_count": 0, "null_percentage": 0.0}, "qtd_procedimento": {"unique_values": 26, "most_common": "1 - Consulta - 10101012", "most_common_count": 21, "null_count": 0, "null_percentage": 0.0}, "forma_de_pagamento": {"unique_values": 9, "most_common": "Pix", "most_common_count": 20, "null_count": 0, "null_percentage": 0.0}, "valor_r": {"min": 2.5, "max": 20000.0, "mean": 779.1138888888888, "null_count": 0, "null_percentage": 0.0}}, "column_samples": {"data_atendimento": ["09/04/2025 13:30", "08/04/2025 08:00", "07/04/2025 12:00", "07/04/2025 08:00", "07/04/2025 09:00"], "codigo_atendimento": [154993622, 153612551, 153618725, 152628845, 155571565], "unidade": ["BRASILIA", "BRASILIA", "Morumbi", "BRASILIA", "BRASILIA"], "paciente": ["thais horst capistrano", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "taciana camelo da cosra", "teste marco felipe <PERSON>"], "profissional": ["Equipe Amigo - Caio <PERSON>", "Equipe Amigo - <PERSON>", "Equipe Amigo - Al<PERSON>", "Equipe Amigo - <PERSON>", "Equipe Amigo - <PERSON><PERSON>"], "tipo_de_atendimento": ["30501369 - Septoplastia (qualquer técnica sem vídeo)", "Consulta - Amigo tech", "Consulta - Amigo tech", "CONSULTA  COM CARDIOLOGISTA", "Consulta - Amigo tech"], "qtd_procedimento": ["1 - Consulta amigo tech - teste", "1 - Consulta - 10101012, 1 - Audiometria tonal limiar infantil condicionada (qualquer técnica) - Peep-show", "1 - Consulta - 10101012", "1 - <PERSON><PERSON><PERSON> - teste", "1 - <PERSON><PERSON><PERSON> - teste"], "forma_de_pagamento": ["Pix", "Pendente", "AMIL", "BRADESCO", "Pix"], "valor_r": [800.0, 80.0, 300.0, 149.0, 20000.0]}, "potential_keys": ["codigo_atendimento", "unidade"], "potential_relationships": [{"column": "codigo_atendimento", "possible_related_entity": "atendimento"}], "analyzed_at": "2025-04-23T23:41:59.972403"}
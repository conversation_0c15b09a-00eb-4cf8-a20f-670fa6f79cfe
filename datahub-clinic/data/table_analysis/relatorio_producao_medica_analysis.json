{"file_name": "relatorio_producao_medica.xlsx", "module": "agenda", "row_count": 2, "column_count": 57, "columns": [{"original_name": "Data Atendimento", "normalized_name": "data_atendimento"}, {"original_name": "<PERSON><PERSON>", "normalized_name": "hora_atendimento"}, {"original_name": "<PERSON><PERSON><PERSON>", "normalized_name": "codigo_atendimento"}, {"original_name": "<PERSON><PERSON><PERSON><PERSON>", "normalized_name": "horario_de_chegada"}, {"original_name": "Indice", "normalized_name": "indice"}, {"original_name": "Paciente", "normalized_name": "paciente"}, {"original_name": "Profissão", "normalized_name": "profi<PERSON><PERSON>"}, {"original_name": "<PERSON><PERSON>", "normalized_name": "idade"}, {"original_name": "Como conheceu", "normalized_name": "como_conheceu"}, {"original_name": "ID amigo ", "normalized_name": "id_amigo"}, {"original_name": "Cod<PERSON>", "normalized_name": "cod_legado"}, {"original_name": "<PERSON><PERSON><PERSON><PERSON>", "normalized_name": "matricula"}, {"original_name": "CNS", "normalized_name": "cns"}, {"original_name": "Profissional", "normalized_name": "profissional"}, {"original_name": "Especialidade", "normalized_name": "especialidade"}, {"original_name": "CBO", "normalized_name": "cbo"}, {"original_name": "Solicitante", "normalized_name": "solicitante"}, {"original_name": "Executante", "normalized_name": "executante"}, {"original_name": "Participação", "normalized_name": "participacao"}, {"original_name": "Hospital / Local", "normalized_name": "hospital_local"}, {"original_name": "Forma de Pagamento", "normalized_name": "forma_de_pagamento"}, {"original_name": "Lançamento", "normalized_name": "lancamento"}, {"original_name": "Plano", "normalized_name": "plano"}, {"original_name": "Número da <PERSON>a", "normalized_name": "numero_da_parcela"}, {"original_name": "Qtd parcelas", "normalized_name": "qtd_parcelas"}, {"original_name": "NSU(DOC/CV/ID)", "normalized_name": "nsu_doc_cv_id"}, {"original_name": "Observação financeira", "normalized_name": "observacao_financeira"}, {"original_name": "T<PERSON>o <PERSON>endimento", "normalized_name": "tipo_atendimento"}, {"original_name": "Unidade", "normalized_name": "unidade"}, {"original_name": "Tipo", "normalized_name": "tipo"}, {"original_name": "Grupo", "normalized_name": "grupo"}, {"original_name": "Subgrupo", "normalized_name": "subgrupo"}, {"original_name": "<PERSON><PERSON>", "normalized_name": "item"}, {"original_name": "Quantidade", "normalized_name": "quantidade"}, {"original_name": "Via de acesso", "normalized_name": "via_de_acesso"}, {"original_name": "% Red. Acrésc", "normalized_name": "red_acresc"}, {"original_name": "Valor do item R$", "normalized_name": "valor_do_item_r"}, {"original_name": "Desconto R$", "normalized_name": "desconto_r"}, {"original_name": "Valor total do item R$", "normalized_name": "valor_total_do_item_r"}, {"original_name": "Nota Emitida", "normalized_name": "nota_emitida"}, {"original_name": "Númer<PERSON> da g<PERSON>", "normalized_name": "numero_da_guia"}, {"original_name": "Número lote", "normalized_name": "numero_lote"}, {"original_name": "<PERSON><PERSON> en<PERSON>u", "normalized_name": "quem_enviou"}, {"original_name": "Data envio", "normalized_name": "data_envio"}, {"original_name": "<PERSON><PERSON> bai<PERSON>", "normalized_name": "quem_baixou"}, {"original_name": "Data pagamento", "normalized_name": "data_pagamento"}, {"original_name": "Valor pago R$", "normalized_name": "valor_pago_r"}, {"original_name": "Valor glosado R$", "normalized_name": "valor_glosado_r"}, {"original_name": "<PERSON>o ate<PERSON>", "normalized_name": "ano_atendimento"}, {"original_name": "<PERSON><PERSON>s atendi<PERSON>", "normalized_name": "mes_atendimento"}, {"original_name": "<PERSON><PERSON> en<PERSON>", "normalized_name": "ano_envio"}, {"original_name": "<PERSON><PERSON><PERSON> <PERSON>", "normalized_name": "mes_envio"}, {"original_name": "<PERSON><PERSON>aga<PERSON>", "normalized_name": "ano_pagamento"}, {"original_name": "<PERSON><PERSON><PERSON>", "normalized_name": "mes_pagamento"}, {"original_name": "Recurso", "normalized_name": "recurso"}, {"original_name": "Editor <PERSON><PERSON> (Orçamento)", "normalized_name": "editor_finance<PERSON>_orcamento"}, {"original_name": "Data pagamento (Orçamento)", "normalized_name": "data_pagamento_orcamento"}], "column_types": {"data_atendimento": "datetime64[ns]", "hora_atendimento": "datetime64[ns]", "codigo_atendimento": "int64", "horario_de_chegada": "datetime64[ns]", "indice": "float64", "paciente": "object", "profissao": "float64", "idade": "int64", "como_conheceu": "float64", "id_amigo": "int64", "cod_legado": "float64", "matricula": "float64", "cns": "float64", "profissional": "object", "especialidade": "object", "cbo": "object", "solicitante": "float64", "executante": "object", "participacao": "object", "hospital_local": "float64", "forma_de_pagamento": "object", "lancamento": "int64", "plano": "float64", "numero_da_parcela": "float64", "qtd_parcelas": "float64", "nsu_doc_cv_id": "float64", "observacao_financeira": "float64", "tipo_atendimento": "object", "unidade": "object", "tipo": "object", "grupo": "object", "subgrupo": "object", "item": "object", "quantidade": "int64", "via_de_acesso": "object", "red_acresc": "float64", "valor_do_item_r": "int64", "desconto_r": "int64", "valor_total_do_item_r": "int64", "nota_emitida": "bool", "numero_da_guia": "float64", "numero_lote": "float64", "quem_enviou": "float64", "data_envio": "float64", "quem_baixou": "float64", "data_pagamento": "float64", "valor_pago_r": "int64", "valor_glosado_r": "float64", "ano_atendimento": "int64", "mes_atendimento": "int64", "ano_envio": "float64", "mes_envio": "float64", "ano_pagamento": "float64", "mes_pagamento": "float64", "recurso": "bool", "editor_financeiro_orcamento": "object", "data_pagamento_orcamento": "datetime64[ns]"}, "column_stats": {"data_atendimento": {"unique_values": 1, "most_common": "23/04/2025", "most_common_count": 2, "null_count": 0, "null_percentage": 0.0}, "hora_atendimento": {"unique_values": 2, "most_common": "17:00", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "codigo_atendimento": {"min": 155601121.0, "max": 155686103.0, "mean": 155643612.0, "null_count": 0, "null_percentage": 0.0}, "horario_de_chegada": {"unique_values": 1, "most_common": NaN, "most_common_count": 1, "null_count": 1, "null_percentage": 50.0}, "indice": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "paciente": {"unique_values": 2, "most_common": "<PERSON><PERSON>", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "profissao": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "idade": {"min": 29.0, "max": 37.0, "mean": 33.0, "null_count": 0, "null_percentage": 0.0}, "como_conheceu": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "id_amigo": {"min": 48947847.0, "max": 90796209.0, "mean": 69872028.0, "null_count": 0, "null_percentage": 0.0}, "cod_legado": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "matricula": {"min": 12234556.0, "max": 12234556.0, "mean": 12234556.0, "null_count": 1, "null_percentage": 50.0}, "cns": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "profissional": {"unique_values": 2, "most_common": "Rayara <PERSON> Souza", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "especialidade": {"unique_values": 2, "most_common": "Pneumologist<PERSON>", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "cbo": {"unique_values": 2, "most_common": "225127-<PERSON><PERSON><PERSON><PERSON> pneumologista", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "solicitante": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "executante": {"unique_values": 2, "most_common": "Rayara <PERSON> Souza", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "participacao": {"unique_values": 1, "most_common": "Clínico", "most_common_count": 1, "null_count": 1, "null_percentage": 50.0}, "hospital_local": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "forma_de_pagamento": {"unique_values": 2, "most_common": "AMIL", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "lancamento": {"min": 1.0, "max": 1.0, "mean": 1.0, "null_count": 0, "null_percentage": 0.0}, "plano": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "numero_da_parcela": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "qtd_parcelas": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "nsu_doc_cv_id": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "observacao_financeira": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "tipo_atendimento": {"unique_values": 2, "most_common": "Sessao de Fisioterapia", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "unidade": {"unique_values": 2, "most_common": "CLÍNICA (RL)", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "tipo": {"unique_values": 1, "most_common": "Procedimento", "most_common_count": 2, "null_count": 0, "null_percentage": 0.0}, "grupo": {"unique_values": 1, "most_common": "BOTOX", "most_common_count": 2, "null_count": 0, "null_percentage": 0.0}, "subgrupo": {"unique_values": 2, "most_common": "BOTOX", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "item": {"unique_values": 2, "most_common": "<PERSON><PERSON><PERSON> - teste", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "quantidade": {"min": 1.0, "max": 1.0, "mean": 1.0, "null_count": 0, "null_percentage": 0.0}, "via_de_acesso": {"unique_values": 1, "most_common": "Única", "most_common_count": 1, "null_count": 1, "null_percentage": 50.0}, "red_acresc": {"min": 1.0, "max": 1.0, "mean": 1.0, "null_count": 1, "null_percentage": 50.0}, "valor_do_item_r": {"min": 20.0, "max": 1000.0, "mean": 510.0, "null_count": 0, "null_percentage": 0.0}, "desconto_r": {"min": 0.0, "max": 0.0, "mean": 0.0, "null_count": 0, "null_percentage": 0.0}, "valor_total_do_item_r": {"min": 20.0, "max": 1000.0, "mean": 510.0, "null_count": 0, "null_percentage": 0.0}, "nota_emitida": {"unique_values": 1, "most_common": "Não", "most_common_count": 2, "null_count": 0, "null_percentage": 0.0}, "numero_da_guia": {"min": 421.0, "max": 421.0, "mean": 421.0, "null_count": 1, "null_percentage": 50.0}, "numero_lote": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "quem_enviou": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "data_envio": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "quem_baixou": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "data_pagamento": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "valor_pago_r": {"min": 0.0, "max": 0.0, "mean": 0.0, "null_count": 0, "null_percentage": 0.0}, "valor_glosado_r": {"min": 0.0, "max": 0.0, "mean": 0.0, "null_count": 1, "null_percentage": 50.0}, "ano_atendimento": {"min": 2025.0, "max": 2025.0, "mean": 2025.0, "null_count": 0, "null_percentage": 0.0}, "mes_atendimento": {"min": 4.0, "max": 4.0, "mean": 4.0, "null_count": 0, "null_percentage": 0.0}, "ano_envio": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "mes_envio": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "ano_pagamento": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "mes_pagamento": {"min": null, "max": null, "mean": null, "null_count": 2, "null_percentage": 100.0}, "recurso": {"unique_values": 1, "most_common": "Não", "most_common_count": 2, "null_count": 0, "null_percentage": 0.0}, "editor_financeiro_orcamento": {"unique_values": 1, "most_common": NaN, "most_common_count": 1, "null_count": 1, "null_percentage": 50.0}, "data_pagamento_orcamento": {"unique_values": 1, "most_common": NaN, "most_common_count": 1, "null_count": 1, "null_percentage": 50.0}}, "column_samples": {"data_atendimento": ["23/04/2025", "23/04/2025"], "hora_atendimento": ["10:20", "17:00"], "codigo_atendimento": [155601121, 155686103], "horario_de_chegada": ["13:09"], "indice": [], "paciente": ["<PERSON><PERSON>", "<PERSON>"], "profissao": [], "idade": [37, 29], "como_conheceu": [], "id_amigo": [90796209, 48947847], "cod_legado": [], "matricula": [12234556.0], "cns": [], "profissional": ["Equipe Amigo - BRUNO LIMA", "Rayara <PERSON> Souza"], "especialidade": ["Médico otorrino<PERSON>a", "Pneumologist<PERSON>"], "cbo": ["225275-<PERSON><PERSON><PERSON><PERSON>", "225127-<PERSON><PERSON><PERSON><PERSON> pneumologista"], "solicitante": [], "executante": ["Rayara <PERSON> Souza", "Equipe Amigo - BRUNO LIMA"], "participacao": ["Clínico"], "hospital_local": [], "forma_de_pagamento": ["Crédito de Procedimento", "AMIL"], "lancamento": [1, 1], "plano": [], "numero_da_parcela": [], "qtd_parcelas": [], "nsu_doc_cv_id": [], "observacao_financeira": [], "tipo_atendimento": ["PROCEDIMENTO SIMPLES - 60 MIN", "Sessao de Fisioterapia"], "unidade": ["Morumbi", "CLÍNICA (RL)"], "tipo": ["Procedimento", "Procedimento"], "grupo": ["BOTOX", "BOTOX"], "subgrupo": ["BOTOX", "botox"], "item": ["<PERSON><PERSON><PERSON> - teste", "BOTOX MALAR"], "quantidade": [1, 1], "via_de_acesso": ["Única"], "red_acresc": [1.0], "valor_do_item_r": [20, 1000], "desconto_r": [0, 0], "valor_total_do_item_r": [20, 1000], "nota_emitida": ["Não", "Não"], "numero_da_guia": [421.0], "numero_lote": [], "quem_enviou": [], "data_envio": [], "quem_baixou": [], "data_pagamento": [], "valor_pago_r": [0, 0], "valor_glosado_r": [0.0], "ano_atendimento": [2025, 2025], "mes_atendimento": [4, 4], "ano_envio": [], "mes_envio": [], "ano_pagamento": [], "mes_pagamento": [], "recurso": ["Não", "Não"], "editor_financeiro_orcamento": ["Equipe Amigo - BRUNO LIMA"], "data_pagamento_orcamento": ["17/04/2025"]}, "potential_keys": ["hora_atendimento", "codigo_atendimento", "paciente", "idade", "id_amigo", "cod_legado", "profissional", "especialidade", "cbo", "executante", "forma_de_pagamento", "nsu_doc_cv_id", "tipo_atendimento", "unidade", "subgrupo", "item", "quantidade", "valor_do_item_r", "valor_total_do_item_r", "nota_emitida"], "potential_relationships": [{"column": "codigo_atendimento", "possible_related_entity": "atendimento"}, {"column": "id_amigo", "possible_related_entity": "amigo"}, {"column": "nsu_doc_cv_id", "possible_related_entity": "nsu_doc_cv"}], "analyzed_at": "2025-04-23T23:41:59.957820"}
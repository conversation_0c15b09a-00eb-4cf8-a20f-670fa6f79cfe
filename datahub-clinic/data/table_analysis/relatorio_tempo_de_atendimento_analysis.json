{"file_name": "relatorio_tempo_de_atendimento.xlsx", "module": "agenda", "row_count": 48, "column_count": 14, "columns": [{"original_name": "Cód. <PERSON>end<PERSON>", "normalized_name": "cod_atendimento"}, {"original_name": "Unidade", "normalized_name": "unidade"}, {"original_name": "Profissional", "normalized_name": "profissional"}, {"original_name": "Paciente", "normalized_name": "paciente"}, {"original_name": "ID paciente", "normalized_name": "id_paciente"}, {"original_name": "Tipo de Atendimento", "normalized_name": "tipo_de_atendimento"}, {"original_name": "Data do Agendamento", "normalized_name": "data_do_agendamento"}, {"original_name": "Hora do Agendamento", "normalized_name": "hora_do_agendamento"}, {"original_name": "Hora de chegada", "normalized_name": "hora_de_chegada"}, {"original_name": "Inicio do atendimento", "normalized_name": "inicio_do_atendimento"}, {"original_name": "Fim do atendimento", "normalized_name": "fim_do_atendimento"}, {"original_name": "Espera em minutos", "normalized_name": "espera_em_minutos"}, {"original_name": "Atraso em minutos", "normalized_name": "atraso_em_minutos"}, {"original_name": "Tempo de Atendimento em minutos", "normalized_name": "tempo_de_atendimento_em_minutos"}], "column_types": {"cod_atendimento": "int64", "unidade": "object", "profissional": "object", "paciente": "object", "id_paciente": "int64", "tipo_de_atendimento": "object", "data_do_agendamento": "datetime64[ns]", "hora_do_agendamento": "datetime64[ns]", "hora_de_chegada": "object", "inicio_do_atendimento": "datetime64[ns]", "fim_do_atendimento": "datetime64[ns]", "espera_em_minutos": "object", "atraso_em_minutos": "object", "tempo_de_atendimento_em_minutos": "int64"}, "column_stats": {"cod_atendimento": {"min": 146952664.0, "max": 155741330.0, "mean": 152826778.875, "null_count": 0, "null_percentage": 0.0}, "unidade": {"unique_values": 4, "most_common": "BRASILIA", "most_common_count": 33, "null_count": 0, "null_percentage": 0.0}, "profissional": {"unique_values": 11, "most_common": "Equipe Amigo - Caio <PERSON>", "most_common_count": 17, "null_count": 0, "null_percentage": 0.0}, "paciente": {"unique_values": 41, "most_common": "<PERSON><PERSON>", "most_common_count": 3, "null_count": 0, "null_percentage": 0.0}, "id_paciente": {"min": 393011.0, "max": 90817169.0, "mean": 77325264.1875, "null_count": 0, "null_percentage": 0.0}, "tipo_de_atendimento": {"unique_values": 14, "most_common": "CONSULTA  COM CARDIOLOGISTA", "most_common_count": 16, "null_count": 0, "null_percentage": 0.0}, "data_do_agendamento": {"unique_values": 19, "most_common": "27/03/2025", "most_common_count": 6, "null_count": 0, "null_percentage": 0.0}, "hora_do_agendamento": {"unique_values": 23, "most_common": "08:00", "most_common_count": 7, "null_count": 0, "null_percentage": 0.0}, "hora_de_chegada": {"unique_values": 29, "most_common": "-", "most_common_count": 18, "null_count": 0, "null_percentage": 0.0}, "inicio_do_atendimento": {"unique_values": 48, "most_common": "18:27", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "fim_do_atendimento": {"unique_values": 48, "most_common": "18:35", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "espera_em_minutos": {"unique_values": 4, "most_common": "-", "most_common_count": 36, "null_count": 0, "null_percentage": 0.0}, "atraso_em_minutos": {"unique_values": 20, "most_common": "-", "most_common_count": 28, "null_count": 0, "null_percentage": 0.0}, "tempo_de_atendimento_em_minutos": {"min": 1.0, "max": 63.0, "mean": 11.5, "null_count": 0, "null_percentage": 0.0}}, "column_samples": {"cod_atendimento": [153715242, 152421869, 151442649, 151775779, 151777985], "unidade": ["BRASILIA", "Recife", "Morumbi", "Morumbi", "BRASILIA"], "profissional": ["Equipe Amigo - <PERSON>", "Equipe Amigo - Caio <PERSON>", "Equipe Amigo - <PERSON>", "Equipe Amigo - <PERSON><PERSON>", "Equipe Amigo - Caio <PERSON>"], "paciente": ["JULIA EYER", "thais horst capistrano", "FERNANDA GOMES", "Dr. <PERSON><PERSON>", "<PERSON><PERSON>"], "id_paciente": [90090896, 48947847, 90212027, 89260601, 89225796], "tipo_de_atendimento": ["CONSULTA  COM CARDIOLOGISTA", "Consulta - Amigo tech", "Consulta - Amigo tech", "Consulta - Amigo tech", "CONSULTA  COM CARDIOLOGISTA"], "data_do_agendamento": ["27/03/2025", "24/03/2025", "02/04/2025", "07/04/2025", "24/03/2025"], "hora_do_agendamento": ["15:00", "08:20", "14:00", "13:40", "08:00"], "hora_de_chegada": ["09:45", "13:24", "12:04", "17:24", "14:15"], "inicio_do_atendimento": ["19:47", "12:33", "14:35", "18:53", "11:20"], "fim_do_atendimento": ["16:49", "17:43", "12:05", "18:16", "12:50"], "espera_em_minutos": ["-", 1, "-", 2, "-"], "atraso_em_minutos": ["-", "-", 9, "-", 559], "tempo_de_atendimento_em_minutos": [7, 28, 8, 26, 63]}, "potential_keys": ["cod_atendimento", "unidade", "id_paciente", "inicio_do_atendimento", "fim_do_atendimento"], "potential_relationships": [{"column": "id_paciente", "possible_related_entity": "paciente"}], "analyzed_at": "2025-04-23T23:42:00.044055"}
{"file_name": "relatório_orçamentos_em_aberto.xlsx", "module": "paciente", "row_count": 10, "column_count": 10, "columns": [{"original_name": "ID Amigo", "normalized_name": "id_amigo"}, {"original_name": "Paciente", "normalized_name": "paciente"}, {"original_name": "CPF", "normalized_name": "cpf"}, {"original_name": "Data Nasc.", "normalized_name": "data_nasc"}, {"original_name": "Sexo", "normalized_name": "sexo"}, {"original_name": "Vip", "normalized_name": "vip"}, {"original_name": "E-mail", "normalized_name": "e_mail"}, {"original_name": "<PERSON><PERSON><PERSON>", "normalized_name": "celular"}, {"original_name": "Orçamentos", "normalized_name": "orcamentos"}, {"original_name": "Total R$", "normalized_name": "total_r"}], "column_types": {"id_amigo": "int64", "paciente": "object", "cpf": "int64", "data_nasc": "datetime64[ns]", "sexo": "object", "vip": "bool", "e_mail": "object", "celular": "object", "orcamentos": "int64", "total_r": "int64"}, "column_stats": {"id_amigo": {"min": 3962189.0, "max": 90775793.0, "mean": 66140328.5, "null_count": 0, "null_percentage": 0.0}, "paciente": {"unique_values": 10, "most_common": "<PERSON><PERSON><PERSON>", "most_common_count": 1, "null_count": 0, "null_percentage": 0.0}, "cpf": {"min": 3718245086.0, "max": 93070730744.0, "mean": 43913747793.6, "null_count": 0, "null_percentage": 0.0}, "data_nasc": {"unique_values": 5, "most_common": "21/09/1995", "most_common_count": 4, "null_count": 2, "null_percentage": 20.0}, "sexo": {"unique_values": 1, "most_common": NaN, "most_common_count": 7, "null_count": 7, "null_percentage": 70.0}, "vip": {"unique_values": 2, "most_common": "Não", "most_common_count": 7, "null_count": 0, "null_percentage": 0.0}, "e_mail": {"unique_values": 5, "most_common": "<EMAIL>", "most_common_count": 4, "null_count": 2, "null_percentage": 20.0}, "celular": {"unique_values": 7, "most_common": "(11) 97505-4988", "most_common_count": 4, "null_count": 0, "null_percentage": 0.0}, "orcamentos": {"min": 1.0, "max": 3.0, "mean": 1.3, "null_count": 0, "null_percentage": 0.0}, "total_r": {"min": 720.0, "max": 26350.0, "mean": 9797.0, "null_count": 0, "null_percentage": 0.0}}, "column_samples": {"id_amigo": [90157170, 89900347, 75656153, 26015293, 3962189], "paciente": ["<PERSON><PERSON><PERSON>", "Junior Souza", "<PERSON>", "<PERSON><PERSON>", "<PERSON> "], "cpf": [9704130767, 80351218041, 84472975009, 92255060906, 26907698838], "data_nasc": ["21/09/1995", "23/03/1981", "21/09/1995", "21/09/1995", "21/09/1995"], "sexo": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "vip": ["Não", "Não", "Não", "<PERSON>m", "Não"], "e_mail": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "claudio<PERSON><PERSON><PERSON>@gmail.com", "<EMAIL>"], "celular": ["(11) 97505-4988", "(71) 98852-3298", "(27) 99650-3322", "(11) 97505-4988", "(31) 99572-0954"], "orcamentos": [1, 1, 1, 2, 1], "total_r": [26350, 4900, 9000, 10500, 2800]}, "potential_keys": ["id_amigo", "paciente", "cpf"], "potential_relationships": [{"column": "id_amigo", "possible_related_entity": "amigo"}], "analyzed_at": "2025-04-23T23:41:59.787296"}
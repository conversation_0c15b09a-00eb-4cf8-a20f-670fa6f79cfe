{"relatorio_caixa_banco.xlsx": {"file_name": "relatorio_caixa_banco.xlsx", "module": "financeiro", "entity_mappings": []}, "relatorio_fechamento_caixa_diário.xlsx": {"file_name": "relatorio_fechamento_caixa_diário.xlsx", "module": "financeiro", "entity_mappings": []}, "relatorio_caixa_banco2.xlsx": {"file_name": "relatorio_caixa_banco2.xlsx", "module": "financeiro", "entity_mappings": []}, "relatorio_contas_a_pagar.xlsx": {"file_name": "relatorio_contas_a_pagar.xlsx", "module": "financeiro", "entity_mappings": []}, "relatorio_fluxo_de_caixa.xlsx": {"file_name": "relatorio_fluxo_de_caixa.xlsx", "module": "financeiro", "entity_mappings": []}, "relatorio_de_repasse.xlsx": {"file_name": "relatorio_de_repasse.xlsx", "module": "financeiro", "entity_mappings": []}, "relatorio_fechamento_de_caixa_novo.xlsx": {"file_name": "relatorio_fechamento_de_caixa_novo.xlsx", "module": "financeiro", "entity_mappings": []}, "relatorio_contas_a_receber.xlsx": {"file_name": "relatorio_contas_a_receber.xlsx", "module": "financeiro", "entity_mappings": []}, "relatório_orçamentos_em_aberto.xlsx": {"file_name": "relatório_orçamentos_em_aberto.xlsx", "module": "paciente", "entity_mappings": []}, "relatorio_atendimentos_realizados.xlsx": {"file_name": "relatorio_atendimentos_realizados.xlsx", "module": "paciente", "entity_mappings": []}, "relatorio_orçamentos_fechados.xlsx": {"file_name": "relatorio_orçamentos_fechados.xlsx", "module": "paciente", "entity_mappings": []}, "relatorio_creditos_disponiveis.xlsx": {"file_name": "relatorio_creditos_disponiveis.xlsx", "module": "paciente", "entity_mappings": []}, "Relatorio_Avalicao_NPS.xlsx": {"file_name": "Relatorio_Avalicao_NPS.xlsx", "module": "amigocare", "entity_mappings": [{"entity": "Paciente", "confidence": "high", "field_mappings": [{"source_column": "Nome", "target_field": "nome", "confidence": "high"}, {"source_column": "VIP", "target_field": "vip", "confidence": "high"}, {"source_column": "CPF", "target_field": "cpf", "confidence": "high"}, {"source_column": "Email", "target_field": "email", "confidence": "high"}, {"source_column": "Sexo", "target_field": "sexo", "confidence": "high"}, {"source_column": "Endereço", "target_field": "endereco", "confidence": "high"}]}]}, "relatorio_producao_medica.xlsx": {"file_name": "relatorio_producao_medica.xlsx", "module": "agenda", "entity_mappings": []}, "relatorio_producao_medica2.xlsx": {"file_name": "relatorio_producao_medica2.xlsx", "module": "agenda", "entity_mappings": []}, "relatorio_produtividade.xlsx": {"file_name": "relatorio_produtividade.xlsx", "module": "agenda", "entity_mappings": []}, "relatorio_cancelamentos_falttantes_reagendamento.xlsx": {"file_name": "relatorio_cancelamentos_falttantes_reagendamento.xlsx", "module": "agenda", "entity_mappings": [{"entity": "Agendamento", "confidence": "high", "field_mappings": []}]}, "relatorio_tempo_de_atendimento.xlsx": {"file_name": "relatorio_tempo_de_atendimento.xlsx", "module": "agenda", "entity_mappings": []}, "relatorio_mapacirurgico.xlsx": {"file_name": "relatorio_mapacirurgico.xlsx", "module": "agenda", "entity_mappings": []}, "relatorio_agendamento.xlsx": {"file_name": "relatorio_agendamento.xlsx", "module": "agenda", "entity_mappings": [{"entity": "Agendamento", "confidence": "high", "field_mappings": [{"source_column": "<PERSON><PERSON>", "target_field": "hora", "confidence": "high"}, {"source_column": "Status", "target_field": "status", "confidence": "high"}]}]}, "relatorio_agendamento_novo.xlsx": {"file_name": "relatorio_agendamento_novo.xlsx", "module": "agenda", "entity_mappings": [{"entity": "Agendamento", "confidence": "high", "field_mappings": [{"source_column": "Sala", "target_field": "sala", "confidence": "high"}]}]}}
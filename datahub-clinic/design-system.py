ue from flask import Flask, render_template_string

app = Flask(__name__)

# Main HTML template with Tailwind CSS classes
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tailwind HIG Inspired Design System (Expanded + AI Insights)</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
          theme: {
            extend: {
              colors: {
                systemBlue: '#007AFF',
                systemGreen: '#34C759',
                systemGray: {
                  DEFAULT: '#8E8E93',
                  light: '#AEAEB2',
                  lighter: '#C7C7CC',
                  lightest: '#D1D1D6',
                  extralight: '#E5E5EA',
                  ultralight: '#F2F2F7'
                },
                label: {
                    DEFAULT: '#000000',
                    secondary: 'rgba(60, 60, 67, 0.6)',
                    tertiary: 'rgba(60, 60, 67, 0.3)',
                    quaternary: 'rgba(60, 60, 67, 0.18)'
                },
                chartPurple: '#7B61FF',
              },
              borderRadius: {
                  'view': '10px',
                  'control': '7px'
              },
              boxShadow: {
                  none: 'none',
              },
               borderColor: theme => ({
                   ...theme('colors'),
                   DEFAULT: theme('colors.gray.200', 'currentColor'),
               })
            }
          }
        }
    </script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
        }
        .text-label-secondary { color: rgba(60, 60, 67, 0.6); }
        .text-label-tertiary { color: rgba(60, 60, 67, 0.3); }
        .text-label-quaternary { color: rgba(60, 60, 67, 0.18); }
        .btn-subtle-hover:hover { background-color: rgba(0, 122, 255, 0.05); }
        .btn-subtle-active:active { background-color: rgba(0, 122, 255, 0.1); }
        .btn-gray-subtle-hover:hover { background-color: rgba(100, 100, 100, 0.05); }
        .btn-gray-subtle-active:active { background-color: rgba(100, 100, 100, 0.1); }

        /* Chart Styles - Thinner lines, subtler grid */
        .chart-placeholder .dot { width: 7px; height: 7px; background-color: #007AFF; border-radius: 50%; position: absolute; }
        .chart-placeholder .area { fill: rgba(0, 122, 255, 0.15); stroke: none; }
        .chart-placeholder .line { stroke: #007AFF; stroke-width: 1; fill: none; } /* Standardized thinner line (1px) */
        .chart-placeholder .grid-line { display: none; } /* Hide grid lines */
        .chart-placeholder .axis-label { font-size: 3.5px; fill: rgba(60, 60, 67, 0.6); font-family: inherit; } /* Set axis label size to 2px */
        .chart-placeholder .donut-hole { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 50%; height: 50%; background-color: #F2F2F7; border-radius: 50%; }
        .chart-placeholder .bg-conic-blue-gray { background: conic-gradient(#007AFF 0% 40%, #AEAEB2 40% 75%, #E5E5EA 75% 100%); }

        /* Example Dashboard Card Chart Styles - Thinner lines, subtler grid */
        .image-card-chart .line { stroke: #007AFF; stroke-width: 1; fill: none; } /* Standardized thinner line (1px) */
        .image-card-chart .area { fill: url(#area-gradient-blue); stroke: none; }
        .image-card-chart .grid-line { display: none; } /* Hide grid lines */
        .image-card-chart .axis-label { font-size: 3.5px; fill: rgba(60, 60, 67, 0.6); font-family: inherit; } /* Set axis label size to 2px */
    </style>
</head>
<body class="bg-gray-100 text-gray-900">

    <header class="w-full bg-white border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <h1 class="text-3xl font-bold">Design System - DataApps </h1>
        </div>
    </header>

    <main class="w-full">

        <!-- Section 1: Colors - Each color swatch is enumerated from 1.1 to 1.8 -->
        <section class="w-full py-10 px-4 sm:px-6 lg:px-8">
             <div>
                <!-- Main section title -->
                <h2 class="text-2xl font-bold mb-6">1. Colors</h2>
                 <div class="flex flex-wrap gap-4">
                    <!-- 1.1: Primary Blue -->
                    <div class="text-center relative">
                        <span class="absolute -top-2 -left-2 text-xs text-label-secondary">1.1</span>
                        <div class="w-16 h-16 rounded-lg bg-systemBlue border border-gray-200"></div>
                        <p class="text-xs mt-1 text-label-secondary">systemBlue</p>
                    </div>
                    <!-- 1.2: Secondary Blue -->
                    <div class="text-center relative">
                        <span class="absolute -top-2 -left-2 text-xs text-label-secondary">1.2</span>
                        <div class="w-16 h-16 rounded-lg bg-blue-500 border border-gray-200"></div>
                        <p class="text-xs mt-1 text-label-secondary">blue-500</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 rounded-lg bg-blue-200 border border-gray-200"></div>
                        <p class="text-xs mt-1 text-label-secondary">blue-200</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 rounded-lg bg-gray-700 border border-gray-200"></div>
                        <p class="text-xs mt-1 text-label-secondary">gray-700</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 rounded-lg bg-systemGray border border-gray-200"></div>
                        <p class="text-xs mt-1 text-label-secondary">systemGray</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 rounded-lg bg-gray-400 border border-gray-200"></div>
                        <p class="text-xs mt-1 text-label-secondary">gray-400</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 rounded-lg bg-gray-100 border border-gray-200"></div>
                        <p class="text-xs mt-1 text-label-secondary">gray-100</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 rounded-lg bg-white border border-gray-200"></div>
                        <p class="text-xs mt-1 text-label-secondary">white</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 2: Typography - Text styles from 2.1 to 2.11 -->
        <section class="w-full py-10 px-4 sm:px-6 lg:px-8">
            <div>
                <h2 class="text-2xl font-bold mb-6">2. Typography</h2>
                <div class="bg-white rounded-view p-6 space-y-4">
                    <!-- 2.1: Large Title -->
                    <div class="relative border-b border-gray-100 pb-3">
                        <span class="absolute -left-8 text-xs text-label-secondary">2.1</span>
                        <p class="text-3xl font-normal tracking-wide">Large Title</p>
                        <p class="text-[11px] text-label-secondary mt-1">SF Pro Display • 34px • Regular • Letter-spacing: 0.37px</p>
                    </div>

                    <!-- 2.2: Title 1 -->
                    <div class="relative border-b border-gray-100 pb-3">
                        <span class="absolute -left-8 text-xs text-label-secondary">2.2</span>
                        <p class="text-2xl font-normal tracking-wide">Title 1</p>
                        <p class="text-[11px] text-label-secondary mt-1">SF Pro Display • 28px • Regular • Letter-spacing: 0.36px</p>
                    </div>

                    <!-- 2.3: Title 2 -->
                    <div class="relative border-b border-gray-100 pb-3">
                        <span class="absolute -left-8 text-xs text-label-secondary">2.3</span>
                        <p class="text-xl font-normal tracking-wide">Title 2</p>
                        <p class="text-[11px] text-label-secondary mt-1">SF Pro Display • 22px • Regular • Letter-spacing: 0.35px</p>
                    </div>

                    <!-- 2.4: Title 3 -->
                    <div class="relative border-b border-gray-100 pb-3">
                        <span class="absolute -left-8 text-xs text-label-secondary">2.4</span>
                        <p class="text-lg font-normal tracking-wide">Title 3</p>
                        <p class="text-[11px] text-label-secondary mt-1">SF Pro Display • 20px • Regular • Letter-spacing: 0.38px</p>
                    </div>

                    <!-- 2.5: Headline -->
                    <div class="relative border-b border-gray-100 pb-3">
                        <span class="absolute -left-8 text-xs text-label-secondary">2.5</span>
                        <p class="text-base font-semibold tracking-tight">Headline</p>
                        <p class="text-[11px] text-label-secondary mt-1">SF Pro Text • 17px • Semibold • Letter-spacing: -0.41px</p>
                    </div>

                    <!-- 2.6: Body -->
                    <div class="relative border-b border-gray-100 pb-3">
                        <span class="absolute -left-8 text-xs text-label-secondary">2.6</span>
                        <p class="text-base font-normal tracking-tight">Body: The quick brown fox jumps over the lazy dog.</p>
                        <p class="text-[11px] text-label-secondary mt-1">SF Pro Text • 17px • Regular • Letter-spacing: -0.41px</p>
                    </div>

                    <!-- 2.7: Callout -->
                    <div class="relative border-b border-gray-100 pb-3">
                        <span class="absolute -left-8 text-xs text-label-secondary">2.7</span>
                        <p class="text-sm font-normal tracking-tight text-label-secondary">Callout: Use for prominent annotations.</p>
                        <p class="text-[11px] text-label-secondary mt-1">SF Pro Text • 16px • Regular • Letter-spacing: -0.32px</p>
                    </div>

                    <!-- 2.8: Subhead -->
                    <div class="relative border-b border-gray-100 pb-3">
                        <span class="absolute -left-8 text-xs text-label-secondary">2.8</span>
                        <p class="text-sm font-normal tracking-tight text-label-secondary">Subhead: Often used for sections.</p>
                        <p class="text-[11px] text-label-secondary mt-1">SF Pro Text • 15px • Regular • Letter-spacing: -0.24px</p>
                    </div>

                    <!-- 2.9: Footnote -->
                    <div class="relative border-b border-gray-100 pb-3">
                        <span class="absolute -left-8 text-xs text-label-secondary">2.9</span>
                        <p class="text-xs font-normal tracking-tight text-label-secondary">Footnote: Extra information.</p>
                        <p class="text-[11px] text-label-secondary mt-1">SF Pro Text • 13px • Regular • Letter-spacing: -0.08px</p>
                    </div>

                    <!-- 2.10: Caption 1 -->
                    <div class="relative border-b border-gray-100 pb-3">
                        <span class="absolute -left-8 text-xs text-label-secondary">2.10</span>
                        <p class="text-xs font-normal tracking-normal text-label-tertiary">Caption 1</p>
                        <p class="text-[11px] text-label-secondary mt-1">SF Pro Text • 12px • Regular • Letter-spacing: 0px</p>
                    </div>

                    <!-- 2.11: Caption 2 -->
                    <div class="relative border-b border-gray-100 pb-3">
                        <span class="absolute -left-8 text-xs text-label-secondary">2.11</span>
                        <p class="text-[11px] font-normal tracking-wide text-label-tertiary">Caption 2</p>
                        <p class="text-[11px] text-label-secondary mt-1">SF Pro Text • 11px • Regular • Letter-spacing: 0.06px</p>
                    </div>

                    <!-- Font Stack Information -->
                    <div class="mt-8 pt-4 border-t border-gray-200">
                        <h3 class="text-sm font-semibold mb-4">System Font Stack</h3>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <code class="text-xs text-label-secondary break-words">
                                -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji"
                            </code>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 3: UI Controls - Grouped controls with sub-enumeration -->
        <section class="w-full py-10 px-4 sm:px-6 lg:px-8">
             <div>
                <h2 class="text-2xl font-bold mb-6">3. UI Controls & Views</h2>

                <!-- 3.1: Button Group -->
                <h3 class="text-xl font-semibold mb-4 relative">
                    <span class="absolute -left-8 text-xs text-label-secondary">3.1</span>
                    Buttons
                </h3>
                 <div class="flex flex-wrap gap-4 items-center mb-8">
                    <!-- 3.1.1: Prominent Button -->
                    <button class="bg-blue-700 hover:opacity-90 active:opacity-80 text-white text-xs font-medium py-2 px-4 rounded-full transition duration-150 ease-in-out relative">
                        <span class="absolute -top-4 -left-2 text-xs text-label-secondary">3.1.1</span>
                        Prominent
                    </button>
                    <button class="bg-systemGray-ultralight hover:bg-systemGray-extralight active:bg-systemGray-lightest text-gray-800 text-xs font-medium py-2 px-4 rounded-full transition duration-150 ease-in-out border border-systemGray-lightest">Default</button>
                    <button class="bg-transparent text-systemBlue hover:bg-blue-50 active:bg-blue-100 text-xs font-medium py-2 px-3 rounded-full transition duration-150 ease-in-out">Borderless</button>
                    <button class="bg-transparent text-red-600 hover:bg-red-50 active:bg-red-100 text-xs font-medium py-2 px-4 rounded-full transition duration-150 ease-in-out">Destructive</button>
                    <button class="bg-systemGray-ultralight text-systemGray-light text-xs font-medium py-2 px-4 rounded-full cursor-not-allowed opacity-70 border border-systemGray-lightest">Disabled</button>
                    <!-- 3.1.6: File Upload Button -->
                    <label class="relative inline-flex items-center gap-2 bg-systemGray-ultralight hover:bg-systemGray-extralight active:bg-systemGray-lightest text-gray-800 text-xs font-medium py-2 px-4 rounded-full transition duration-150 ease-in-out border border-systemGray-lightest cursor-pointer">
                        <span class="absolute -top-4 -left-2 text-xs text-label-secondary">3.1.6</span>
                        <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                        </svg>
                        Upload File
                        <input type="file" class="hidden" />
                    </label>
                 </div>

                <!-- 3.2: Selection Controls -->
                <div class="bg-white rounded-view p-6 border border-gray-200 mb-8">
                    <h3 class="text-xl font-semibold mb-4 relative">
                        <span class="absolute -left-8 text-xs text-label-secondary">3.2</span>
                        Selection & Toggles
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-8 items-start">
                        <div class="space-y-3">
                            <h4 class="text-sm font-medium text-label-secondary mb-2">Checkboxes</h4>
                            <label class="flex items-center space-x-2 cursor-pointer"><input type="checkbox" class="rounded border-systemGray-lighter text-systemBlue focus:ring-systemBlue focus:ring-opacity-50 focus:ring-offset-0 focus:ring-1" checked><span class="text-sm text-gray-800">Option 1 (Checked)</span></label>
                            <label class="flex items-center space-x-2 cursor-pointer"><input type="checkbox" class="rounded border-systemGray-lighter text-systemBlue focus:ring-systemBlue focus:ring-opacity-50 focus:ring-offset-0 focus:ring-1"><span class="text-sm text-gray-800">Option 2</span></label>
                            <label class="flex items-center space-x-2 cursor-not-allowed"><input type="checkbox" class="rounded border-systemGray-lighter text-systemGray-lightest" disabled><span class="text-sm text-systemGray-light">Option 3 (Disabled)</span></label>
                        </div>
                        <div class="space-y-3">
                            <h4 class="text-sm font-medium text-label-secondary mb-2">Radio Buttons</h4>
                            <label class="flex items-center space-x-2 cursor-pointer"><input type="radio" name="radio-group" class="text-systemBlue focus:ring-systemBlue focus:ring-opacity-50 focus:ring-offset-0 focus:ring-1" checked><span class="text-sm text-gray-800">Choice A (Selected)</span></label>
                            <label class="flex items-center space-x-2 cursor-pointer"><input type="radio" name="radio-group" class="text-systemBlue focus:ring-systemBlue focus:ring-opacity-50 focus:ring-offset-0 focus:ring-1"><span class="text-sm text-gray-800">Choice B</span></label>
                            <label class="flex items-center space-x-2 cursor-not-allowed"><input type="radio" name="radio-group-disabled" class="text-systemGray-lightest" disabled><span class="text-sm text-systemGray-light">Choice C (Disabled)</span></label>
                        </div>
                        <div class="space-y-4">
                            <h4 class="text-sm font-medium text-label-secondary mb-2">Toggles (Switches)</h4>
                            <button type="button" class="relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-systemBlue bg-systemGreen" role="switch" aria-checked="true">
                              <span class="sr-only">Use setting</span>
                              <span aria-hidden="true" class="inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200 translate-x-5"></span>
                            </button>
                            <button type="button" class="relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-systemBlue bg-gray-200" role="switch" aria-checked="false">
                              <span class="sr-only">Use setting</span>
                              <span aria-hidden="true" class="inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200 translate-x-0"></span>
                            </button>
                              <button type="button" class="relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-not-allowed transition-colors ease-in-out duration-200 bg-gray-200 opacity-50" role="switch" aria-checked="false" disabled>
                              <span class="sr-only">Use setting</span>
                              <span aria-hidden="true" class="inline-block h-5 w-5 rounded-full bg-gray-100 shadow transform ring-0 transition ease-in-out duration-200 translate-x-0"></span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Other Controls Card -->
                <div class="bg-white rounded-view p-6 border border-gray-200 mb-8">
                    <h3 class="text-xl font-semibold mb-4">Other Controls</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-8 items-center">
                        <div>
                            <label for="location" class="block text-sm font-medium text-label-secondary mb-1">Dropdown</label>
                            <select id="location" name="location" class="block w-full pl-3 pr-10 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
                              <option>Option 1</option>
                              <option selected>Option 2</option>
                              <option>Option 3</option>
                            </select>
                        </div>
                             <div>
                                <label for="price" class="block text-sm font-medium text-label-secondary mb-1">Slider</label>
                                <input type="range" id="price" name="price" min="0" max="100" value="50" class="w-full h-2 bg-systemGray-extralight rounded-lg appearance-none cursor-pointer accent-systemBlue"></input>
                            </div>
                            <div>
                                <label for="disabled-dropdown" class="block text-sm font-medium text-systemGray-light mb-1">Disabled Dropdown</label>
                                <select id="disabled-dropdown" name="disabled-dropdown" class="block w-full pl-3 pr-10 py-2 text-sm border-systemGray-lightest bg-systemGray-ultralight text-systemGray-light rounded-control transition duration-150 ease-in-out cursor-not-allowed" disabled>
                                  <option>Option A</option>
                                </select>
                            </div>
                    </div>
                </div>

                <!-- Views -->
                 <h3 class="text-xl font-semibold mb-4 mt-10">Views</h3>
                 <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    <div class="bg-white rounded-view p-6 border border-gray-200">
                        <h4 class="text-base font-semibold mb-1">Standard View</h4>
                        <p class="text-base mb-1">Standard container for content sections.</p>
                        <p class="text-xs text-label-secondary">White background, subtle border.</p>
                     </div>
                     <div class="bg-gray-100 border border-gray-200 rounded-view p-6">
                        <h4 class="text-base font-semibold mb-1">Grouped View</h4>
                        <p class="text-base mb-1">Used for secondary content or grouped elements.</p>
                        <p class="text-sm text-systemBlue">Can contain differently styled elements.</p>
                     </div>
                 </div>

                 <!-- Segmented Control -->
                 <h3 class="text-xl font-semibold mb-4">Segmented Control</h3>
                 <div class="inline-flex bg-gray-200 rounded-control p-0.5">
                     <button class="text-sm font-medium px-4 py-1 rounded-[6px] bg-white text-gray-900 shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-systemBlue">Overview</button>
                     <button class="text-sm font-medium px-4 py-1 rounded-[6px] text-label-secondary hover:text-gray-900 focus:outline-none">Details</button>
                     <button class="text-sm font-medium px-4 py-1 rounded-[6px] text-label-secondary hover:text-gray-900 focus:outline-none">Settings</button>
                 </div>
            </div>
        </section>

        <!-- Section 4: KPI Cards - Each card enumerated from 4.1 to 4.6 -->
        <section class="w-full bg-white py-10 px-4 sm:px-6 lg:px-8">
            <div>
                <h2 class="text-2xl font-bold mb-6">4. Advanced KPI Cards</h2>
                <div class="max-w-5xl mx-auto">
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
                        <!-- 4.1: Conversion Rate Card -->
                    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200 relative">
                        <span class="absolute -top-3 -left-3 text-xs text-label-secondary">4.1</span>
                        <div class="flex items-center mb-2">
                                <p class="text-sm font-semibold text-gray-900">Conversion Rate</p>
                        </div>
                        <p class="text-3xl font-semibold text-gray-900 mb-1">5.8%</p>
                        <div class="flex items-baseline mb-2">
                                <p class="text-sm font-medium text-systemGreen mr-2">+0.3%</p>
                            <p class="text-xs text-label-secondary">vs 5.5% Target</p>
                        </div>
                        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Slightly above target due to recent campaign.</p>
                    </div>

                        <!-- 4.2: Negative Delta & Comment -->
                    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200 relative">
                        <span class="absolute -top-3 -left-3 text-xs text-label-secondary">4.2</span>
                        <div class="flex items-center mb-2">
                                <p class="text-sm font-semibold text-gray-900">Avg. Session Duration</p>
                        </div>
                        <p class="text-3xl font-semibold text-gray-900 mb-1">2m 15s</p>
                        <div class="flex items-baseline mb-2">
                                <p class="text-sm font-medium text-red-600 mr-2">-12s</p>
                            <p class="text-xs text-label-secondary">vs Last Period</p>
                        </div>
                        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Drop potentially linked to homepage bounce rate increase.</p>
                    </div>

                         <!-- 4.3: Simple Value & Comment -->
                    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200 relative">
                        <span class="absolute -top-3 -left-3 text-xs text-label-secondary">4.3</span>
                        <div class="flex items-center mb-2">
                                <p class="text-sm font-semibold text-gray-900">Reports Generated</p>
                        </div>
                        <p class="text-3xl font-semibold text-gray-900 mb-1">1,450</p>
                        <div class="flex items-baseline mb-2">
                                <p class="text-sm font-medium text-systemGray mr-2">+0</p>
                            <p class="text-xs text-label-secondary">vs Last Month</p>
                        </div>
                        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Usage consistent month-over-month.</p>
                    </div>

                        <!-- 4.4: Wide with AI Insight -->
                    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200 lg:col-span-2 relative">
                        <span class="absolute -top-3 -left-3 text-xs text-label-secondary">4.4</span>
                        <div class="flex items-center mb-2">
                                <p class="text-sm font-semibold text-gray-900">Customer Satisfaction (CSAT)</p>
                        </div>
                        <p class="text-3xl font-semibold text-gray-900 mb-1">92%</p>
                        <div class="flex items-baseline mb-2">
                                <p class="text-sm font-medium text-systemGreen mr-2">+3%</p>
                            <p class="text-xs text-label-secondary">vs Last Quarter</p>
                        </div>
                        <div class="mt-3 p-2 bg-green-50 text-green-800 rounded-md text-[11px] flex items-start">
                            <span class="mr-2 text-base leading-none text-systemGreen">✨</span>
                            <span>Recent support improvements correlate with score increase.</span>
                        </div>
                        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Consistently high satisfaction.</p>
                    </div>

                        <!-- 4.5: Taller Style -->
                    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200 relative">
                        <span class="absolute -top-3 -left-3 text-xs text-label-secondary">4.5</span>
                        <div class="flex items-center mb-2">
                                <p class="text-sm font-semibold text-gray-900">Bounce Rate</p>
                        </div>
                        <p class="text-3xl font-semibold text-gray-900 mb-1">45.5%</p>
                        <div class="flex items-baseline mb-2">
                                <p class="text-sm font-medium text-red-600 mr-2">+2.1%</p>
                            <p class="text-xs text-label-secondary">vs Benchmark</p>
                        </div>
                        <p class="text-xs text-label-secondary mb-3">Higher than desired. Key landing pages to review:</p>
                        <ul class="list-disc list-inside text-xs text-label-secondary space-y-1 mb-3">
                            <li>/landing/feature-a</li>
                            <li>/blog/popular-post</li>
                        </ul>
                        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Action plan needed to reduce bounce.</p>
                    </div>

                        <!-- 4.6: Standard with AI Insight -->
                        <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200 relative">
                            <span class="absolute -top-3 -left-3 text-xs text-label-secondary">4.6</span>
                            <div class="flex items-center mb-2">
                                    <p class="text-sm font-semibold text-gray-900">Server Uptime</p>
                            </div>
                            <p class="text-3xl font-semibold text-gray-900 mb-1">99.98%</p>
                            <div class="flex items-baseline mb-2">
                                    <p class="text-sm font-medium text-systemGray mr-2">-0.01%</p>
                                <p class="text-xs text-label-secondary">vs Last 30 days</p>
                            </div>
                            <div class="mt-3 p-2 bg-green-50 text-green-800 rounded-md text-[11px] flex items-start">
                                <span class="mr-2 text-base leading-none text-systemGreen">✨</span>
                                <span>Minor dip due to scheduled maintenance window.</span>
                            </div>
                            <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Overall performance stable.</p>
                        </div>

                        <!-- 4.7: System Status Card -->
                        <div class="bg-white p-6 rounded-view border border-gray-200 relative">
                            <span class="absolute -top-3 -left-3 text-xs text-label-secondary">4.7</span>
                            <div class="flex justify-between items-start">
                                <div>
                                    <h3 class="text-base font-semibold">System Status</h3>
                                    <p class="text-3xl font-bold mt-2">Healthy</p>
                                    <p class="text-sm text-label-secondary mt-1">All systems operational</p>
                                </div>
                                <span class="h-3 w-3 bg-systemGreen rounded-full"></span>
                            </div>
                            <div class="mt-6 space-y-2">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-label-secondary">API</span>
                                    <span class="text-sm text-systemGreen">99.9%</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-label-secondary">CDN</span>
                                    <span class="text-sm text-systemGreen">100%</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-label-secondary">Database</span>
                                    <span class="text-sm text-systemGreen">99.95%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                </div>
            </div>
        </section>

        <!-- Section 5: AI Insights - Cards with enumeration -->
        <section class="w-full bg-white py-10 px-4 sm:px-6 lg:px-8">
             <div>
                <h2 class="text-2xl font-bold mb-6">5. AI Insights</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <!-- 5.1: Campaign Optimization -->
                    <div class="bg-white rounded-view p-5 border border-gray-200 flex flex-col relative">
                        <span class="absolute -top-3 -left-3 text-xs text-label-secondary">5.1</span>
                        <div class="flex items-center mb-1">
                            <svg class="w-4 h-4 text-systemBlue mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path></svg>
                            <span class="text-base font-semibold text-gray-900">Campaign Optimization</span>
                        </div>
                        <p class="text-sm text-label-secondary mb-3">High engagement from users in the 25-34 age group on mobile. Consider adjusting bids upwards for this segment.</p>
                        <div class="flex justify-between items-center mb-4">
                            <span class="inline-block bg-blue-100 text-blue-700 text-xs font-medium px-2.5 py-0.5 rounded-full">Marketing</span>
                        </div>
                        <div class="flex items-center space-x-4 text-xs text-label-secondary pt-3 border-t border-gray-100 mt-auto">
                            <div class="flex items-center">
                                <svg class="h-4 w-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"></path></svg>
                                <span>3 Recommendations</span>'
                            </div>
                            <div class="flex items-center">
                                 <svg class="h-4 w-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                                <span>High Priority</span>
                            </div>
                        </div>
                    </div>

                    <!-- 5.2: Risk -->
                     <div class="bg-white rounded-view p-5 border border-gray-200 flex flex-col">
                        <div class="flex items-center mb-1">
                            <svg class="w-4 h-4 text-systemBlue mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path></svg>
                            <span class="text-base font-semibold text-gray-900">Traffic Anomaly</span>
                        </div>
                        <p class="text-sm text-label-secondary mb-3">Recent algorithm update correlates with a slight dip in organic traffic. Monitor related keywords closely.</p>
                         <div class="flex justify-between items-center mb-4">
                            <span class="inline-block bg-orange-100 text-orange-700 text-xs font-medium px-2.5 py-0.5 rounded-full">SEO</span>
                         </div>
                        <div class="flex items-center space-x-4 text-xs text-label-secondary pt-3 border-t border-gray-100 mt-auto">
                            <div class="flex items-center">
                                <svg class="h-4 w-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                                <span>Monitor Trend</span>
                            </div>
                             <div class="flex items-center">
                                 <svg class="h-4 w-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg>
                                <span>Review Weekly</span>
                            </div>
                        </div>
                    </div>

                    <!-- 5.3: Performance -->
                    <div class="bg-white rounded-view p-5 border border-gray-200 flex flex-col">
                        <div class="flex items-center mb-1">
                            <svg class="w-4 h-4 text-systemBlue mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path></svg>
                            <span class="text-base font-semibold text-gray-900">Feature Adoption</span>
                        </div>
                        <p class="text-sm text-label-secondary mb-3">Feature X adoption rate increased by 25% after the latest UI improvements. Potential for further promotion.</p>
                        <div class="flex justify-between items-center mb-4">
                            <span class="inline-block bg-green-100 text-green-700 text-xs font-medium px-2.5 py-0.5 rounded-full">Product</span>
                        </div>
                        <div class="flex items-center space-x-4 text-xs text-label-secondary pt-3 border-t border-gray-100 mt-auto">
                           <div class="flex items-center">
                                <svg class="h-4 w-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122"></path></svg>
                                <span>Explore Drivers</span>
                            </div>
                            <div class="flex items-center">
                                <svg class="h-4 w-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                                <span>Verified Positive</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

         <!-- Section 6: Charts - Each chart type enumerated -->
         <section class="w-full py-10 px-4 sm:px-6 lg:px-8">
            <div>
                <h2 class="text-2xl font-bold mb-6">6. Card & Chart Examples</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-8">
                    <!-- 6.1: Bar Chart -->
                    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-100 relative">
                        <span class="absolute -top-3 -left-3 text-xs text-label-secondary">6.1</span>
                        <h3 class="text-xs font-semibold mb-3 text-label-secondary">Distribution (Bar)</h3>
                        <div class="chart-placeholder bg-gray-50 rounded-lg h-48 flex items-end justify-around px-2 pt-2 gap-2 flex-grow">
                            <div class="w-1/5 h-[70%] bg-systemBlue rounded-t-md"></div>
                            <div class="w-1/5 h-[45%] bg-blue-300 rounded-t-md"></div>
                            <div class="w-1/5 h-[85%] bg-gray-400 rounded-t-md"></div>
                            <div class="w-1/5 h-[60%] bg-gray-200 rounded-t-md"></div>
                        </div>
                        <div class="mt-3 p-2 bg-green-50 text-green-800 rounded-md text-[11px] flex items-start">
                            <span class="mr-2 text-base leading-none text-systemGreen">✨</span>
                            <span>Category C shows significantly higher values...</span>
                        </div>
                    </div>

                    <!-- 6.2: Area Chart -->
                    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-100 relative">
                        <span class="absolute -top-3 -left-3 text-xs text-label-secondary">6.2</span>
                        <h3 class="text-xs font-semibold mb-3 text-label-secondary">Revenue Trend (Area)</h3>
                        <div class="chart-placeholder bg-gray-50 rounded-lg h-48 relative overflow-hidden">
                            <svg class="w-full h-full" viewBox="0 0 100 60" preserveAspectRatio="none">
                                <defs>
                                    <linearGradient id="area-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                        <stop offset="0%" style="stop-color:#007AFF;stop-opacity:0.1"/>
                                        <stop offset="100%" style="stop-color:#007AFF;stop-opacity:0"/>
                                    </linearGradient>
                                </defs>
                                <path d="M0 50 L10 45 L20 48 L30 40 L40 42 L50 35 L60 38 L70 30 L80 32 L90 25 L100 28 L100 60 L0 60 Z" 
                                      fill="url(#area-gradient)" stroke="none"/>
                                <path d="M0 50 L10 45 L20 48 L30 40 L40 42 L50 35 L60 38 L70 30 L80 32 L90 25 L100 28" 
                                      fill="none" stroke="#007AFF" stroke-width="0.75"/>
                            </svg>
                        </div>
                        <div class="mt-3 p-2 bg-green-50 text-green-800 rounded-md text-[11px] flex items-start">
                            <span class="mr-2 text-base leading-none text-systemGreen">✨</span>
                            <span>Consistent upward trend with 15% growth</span>
                        </div>
                    </div>

                    <!-- 6.3: Table Chart -->
                    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-100 relative">
                        <span class="absolute -top-3 -left-3 text-xs text-label-secondary">6.3</span>
                        <h3 class="text-xs font-semibold mb-3 text-label-secondary">Financial Summary (Table)</h3>
                        <div class="overflow-x-auto">
                            <table class="w-full text-xs">
                                <thead>
                                    <tr class="border-b border-gray-200">
                                        <th class="text-left py-2 text-label-secondary font-medium">Metric</th>
                                        <th class="text-right py-2 text-label-secondary font-medium">Value</th>
                                        <th class="text-right py-2 text-label-secondary font-medium">Change</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr class="border-b border-gray-100">
                                        <td class="py-2">Revenue</td>
                                        <td class="text-right">$52.5K</td>
                                        <td class="text-right text-systemGreen">+12%</td>
                                    </tr>
                                    <tr class="border-b border-gray-100">
                                        <td class="py-2">Expenses</td>
                                        <td class="text-right">$31.2K</td>
                                        <td class="text-right text-red-500">+8%</td>
                                    </tr>
                                    <tr>
                                        <td class="py-2">Margin</td>
                                        <td class="text-right">40.6%</td>
                                        <td class="text-right text-systemGreen">+2%</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="mt-3 p-2 bg-green-50 text-green-800 rounded-md text-[11px] flex items-start">
                            <span class="mr-2 text-base leading-none text-systemGreen">✨</span>
                            <span>Profit margins improving despite rising costs</span>
                        </div>
                    </div>

                    <!-- 6.4: Anomaly Detection -->
                    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-100 relative">
                        <span class="absolute -top-3 -left-3 text-xs text-label-secondary">6.4</span>
                        <h3 class="text-xs font-semibold mb-3 text-label-secondary">Server Response Time</h3>
                        <div class="chart-placeholder bg-gray-50 rounded-lg h-48 relative overflow-hidden">
                            <svg class="w-full h-full" viewBox="0 0 100 60" preserveAspectRatio="none">
                                <!-- Normal trend line -->
                                <path d="M0 30 L20 32 L40 28 L60 30 L80 29 L100 31" 
                                      fill="none" stroke="#007AFF" stroke-width="0.75"/>
                                <!-- Anomaly section -->
                                <path d="M60 30 L70 45 L80 29" 
                                      fill="none" stroke="#FF3B30" stroke-width="0.75" stroke-dasharray="2,2"/>
                                <!-- Anomaly point -->
                                <circle cx="70" cy="45" r="1.5" fill="#FF3B30"/>
                            </svg>
                        </div>
                        <div class="mt-3 p-2 bg-red-50 text-red-800 rounded-md text-[11px] flex items-start">
                            <span class="mr-2 text-base leading-none text-red-500">⚠️</span>
                            <span>Detected response time spike at 14:30</span>
                        </div>
                    </div>

                    <!-- 6.5: Scatter Plot -->
                    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-100 relative">
                        <span class="absolute -top-3 -left-3 text-xs text-label-secondary">6.5</span>
                        <h3 class="text-xs font-semibold mb-3 text-label-secondary">Customer Behavior Analysis</h3>
                        <div class="chart-placeholder bg-gray-50 rounded-lg h-48 relative">
                            <svg class="w-full h-full" viewBox="0 0 100 60">
                                <!-- Grid Lines -->
                                <defs>
                                    <linearGradient id="scatter-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                        <stop offset="0%" style="stop-color:#007AFF;stop-opacity:0.1"/>
                                        <stop offset="100%" style="stop-color:#007AFF;stop-opacity:0"/>
                                    </linearGradient>
                                </defs>
                                <!-- Background Grid -->
                                <line x1="10" y1="10" x2="90" y2="10" stroke="#E5E5EA" stroke-width="0.25" stroke-dasharray="1,2"/>
                                <line x1="10" y1="20" x2="90" y2="20" stroke="#E5E5EA" stroke-width="0.25" stroke-dasharray="1,2"/>
                                <line x1="10" y1="30" x2="90" y2="30" stroke="#E5E5EA" stroke-width="0.25" stroke-dasharray="1,2"/>
                                <line x1="10" y1="40" x2="90" y2="40" stroke="#E5E5EA" stroke-width="0.25" stroke-dasharray="1,2"/>
                                <line x1="10" y1="50" x2="90" y2="50" stroke="#E5E5EA" stroke-width="0.25"/>
                                
                                <line x1="10" y1="10" x2="10" y2="50" stroke="#E5E5EA" stroke-width="0.25"/>
                                <line x1="30" y1="10" x2="30" y2="50" stroke="#E5E5EA" stroke-width="0.25" stroke-dasharray="1,2"/>
                                <line x1="50" y1="10" x2="50" y2="50" stroke="#E5E5EA" stroke-width="0.25" stroke-dasharray="1,2"/>
                                <line x1="70" y1="10" x2="70" y2="50" stroke="#E5E5EA" stroke-width="0.25" stroke-dasharray="1,2"/>
                                <line x1="90" y1="10" x2="90" y2="50" stroke="#E5E5EA" stroke-width="0.25"/>

                                <!-- Trend Line -->
                                <path d="M20 35 Q50 25 85 15" fill="none" stroke="#007AFF" stroke-width="0.75" stroke-dasharray="2,2"/>

                                <!-- Data Points -->
                                <circle cx="20" cy="30" r="2.5" fill="#007AFF" opacity="0.8">
                                    <animate attributeName="opacity" values="0.8;1;0.8" dur="2s" repeatCount="indefinite"/>
                                </circle>
                                <circle cx="35" cy="25" r="2.5" fill="#007AFF" opacity="0.8"/>
                                <circle cx="45" cy="35" r="2.5" fill="#007AFF" opacity="0.8"/>
                                <circle cx="60" cy="20" r="2.5" fill="#007AFF" opacity="0.8"/>
                                <circle cx="75" cy="40" r="2.5" fill="#007AFF" opacity="0.8"/>
                                <circle cx="85" cy="15" r="2.5" fill="#007AFF" opacity="0.8">
                                    <animate attributeName="opacity" values="0.8;1;0.8" dur="2s" repeatCount="indefinite"/>
                                </circle>
                            </svg>
                            <!-- Axis Labels -->
                            <div class="absolute -bottom-1 left-10 right-10 flex justify-between text-[10px] text-label-secondary">
                                <span>Low</span>
                                <span>Purchase Frequency</span>
                                <span>High</span>
                            </div>
                            <div class="absolute top-10 bottom-10 left-0 flex flex-col justify-between items-start">
                                <span class="text-[10px] text-label-secondary transform -rotate-90">Spend Amount</span>
                                <div class="flex flex-col items-start text-[10px] text-label-secondary">
                                    <span>Low</span>
                                    <span class="mt-auto">High</span>
                                </div>
                            </div>
                            <!-- Legend -->
                            <div class="absolute top-2 right-2 bg-white bg-opacity-90 p-2 rounded-md text-[10px] space-y-1">
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-systemBlue mr-1 opacity-80"></div>
                                    <span>Customer Data Point</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-3 h-0.5 border-t border-dashed border-systemBlue mr-1"></div>
                                    <span>Trend Line</span>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3 p-2 bg-blue-50 text-blue-800 rounded-md text-[11px] flex items-start">
                            <span class="mr-2 text-base leading-none text-systemBlue">🔍</span>
                            <span>Strong positive correlation between purchase frequency and spend amount</span>
                        </div>
                    </div>

                    <!-- 6.6: Sparkline Table -->
                    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-100 relative">
                        <span class="absolute -top-3 -left-3 text-xs text-label-secondary">6.6</span>
                        <h3 class="text-xs font-semibold mb-3 text-label-secondary">Traffic Sources (Stacked Area)</h3>
                        <div class="chart-placeholder bg-gray-50 rounded-lg h-48 relative overflow-hidden">
                            <svg class="w-full h-full" viewBox="0 0 100 60" preserveAspectRatio="none">
                                <defs>
                                    <linearGradient id="gradient1" x1="0%" y1="0%" x2="0%" y2="100%">
                                        <stop offset="0%" style="stop-color:#007AFF;stop-opacity:0.3"/>
                                        <stop offset="100%" style="stop-color:#007AFF;stop-opacity:0.1"/>
                                    </linearGradient>
                                    <linearGradient id="gradient2" x1="0%" y1="0%" x2="0%" y2="100%">
                                        <stop offset="0%" style="stop-color:#34C759;stop-opacity:0.3"/>
                                        <stop offset="100%" style="stop-color:#34C759;stop-opacity:0.1"/>
                                    </linearGradient>
                                </defs>
                                <!-- Bottom Area -->
                                <path d="M0 60 L0 45 L20 42 L40 44 L60 40 L80 38 L100 35 L100 60 Z" 
                                      fill="url(#gradient2)"/>
                                <!-- Top Area -->
                                <path d="M0 45 L20 42 L40 44 L60 40 L80 38 L100 35 L100 20 L80 25 L60 30 L40 28 L20 32 L0 30 Z" 
                                      fill="url(#gradient1)"/>
                            </svg>
                            <!-- Legend -->
                            <div class="absolute top-2 right-2 bg-white bg-opacity-90 p-2 rounded-md text-[10px] space-y-1">
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-systemBlue mr-1"></div>
                                    <span>Organic</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-systemGreen mr-1"></div>
                                    <span>Direct</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 6.7: Treemap -->
                    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-100 relative">
                        <span class="absolute -top-3 -left-3 text-xs text-label-secondary">6.7</span>
                        <h3 class="text-xs font-semibold mb-3 text-label-secondary">Budget Allocation (Treemap)</h3>
                        <div class="chart-placeholder bg-gray-50 rounded-lg h-48 p-2 grid grid-cols-3 grid-rows-2 gap-1">
                            <div class="bg-systemBlue col-span-2 row-span-2 rounded p-2">
                                <span class="text-[10px] text-white">Operations 60%</span>
                            </div>
                            <div class="bg-blue-400 rounded p-2">
                                <span class="text-[10px] text-white">Marketing 20%</span>
                            </div>
                            <div class="bg-blue-300 rounded p-2">
                                <span class="text-[10px] text-white">R&D 20%</span>
                            </div>
                        </div>
                        <!-- AI Insight -->
                        <div class="mt-3 p-2 bg-green-50 text-green-800 rounded-md text-[11px] flex items-start">
                            <span class="mr-2 text-base leading-none text-systemGreen">✨</span>
                            <span>Budget distribution follows industry standards</span>
                        </div>
                    </div>

                    <!-- 6.8: Heatmap -->
                    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-100 relative">
                        <span class="absolute -top-3 -left-3 text-xs text-label-secondary">6.8</span>
                        <h3 class="text-xs font-semibold mb-3 text-label-secondary">Monthly Performance (Heatmap)</h3>
                        <div class="chart-placeholder bg-gray-50 rounded-lg h-48 p-2 grid grid-cols-6 gap-1">
                            <div class="bg-blue-100 rounded"></div>
                            <div class="bg-blue-200 rounded"></div>
                            <div class="bg-blue-300 rounded"></div>
                            <div class="bg-blue-400 rounded"></div>
                            <div class="bg-blue-500 rounded"></div>
                            <div class="bg-blue-600 rounded"></div>
                            <div class="bg-blue-200 rounded"></div>
                            <div class="bg-blue-300 rounded"></div>
                            <div class="bg-blue-400 rounded"></div>
                            <div class="bg-blue-500 rounded"></div>
                            <div class="bg-blue-600 rounded"></div>
                            <div class="bg-blue-700 rounded"></div>
                        </div>
                        <!-- AI Insight -->
                        <div class="mt-3 p-2 bg-green-50 text-green-800 rounded-md text-[11px] flex items-start">
                            <span class="mr-2 text-base leading-none text-systemGreen">✨</span>
                            <span>Performance peaks detected in Q3</span>
                        </div>
                    </div>

                    <!-- 6.9: Multi-line Chart -->
                    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-100 relative">
                        <span class="absolute -top-3 -left-3 text-xs text-label-secondary">6.9</span>
                        <h3 class="text-xs font-semibold mb-3 text-label-secondary">Multi-line Comparison</h3>
                        <div class="chart-placeholder bg-gray-50 rounded-lg h-48 relative overflow-hidden">
                            <svg class="w-full h-full" viewBox="0 0 100 60" preserveAspectRatio="none">
                                <!-- Line 1: Primary Metric -->
                                <path d="M0 40 L20 35 L40 38 L60 30 L80 25 L100 20" 
                                      fill="none" stroke="#007AFF" stroke-width="0.75"/>
                                <!-- Line 2: Secondary Metric -->
                                <path d="M0 45 L20 42 L40 44 L60 40 L80 38 L100 35" 
                                      fill="none" stroke="#34C759" stroke-width="0.75" stroke-dasharray="2,2"/>
                                <!-- Line 3: Tertiary Metric -->
                                <path d="M0 30 L20 32 L40 28 L60 35 L80 30 L100 28" 
                                      fill="none" stroke="#FF9500" stroke-width="0.75"/>
                            </svg>
                            <!-- Legend -->
                            <div class="absolute bottom-2 right-2 bg-white bg-opacity-90 p-2 rounded-md text-[10px] space-y-1">
                                <div class="flex items-center">
                                    <div class="w-3 h-0.5 bg-systemBlue mr-1"></div>
                                    <span>Revenue</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-3 h-0.5 bg-systemGreen mr-1"></div>
                                    <span>Profit</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-3 h-0.5 bg-[#FF9500] mr-1"></div>
                                    <span>Orders</span>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3 p-2 bg-blue-50 text-blue-800 rounded-md text-[11px] flex items-start">
                            <span class="mr-2 text-base leading-none text-systemBlue">📊</span>
                            <span>Revenue growth outpacing order volume</span>
                        </div>
                    </div>

                    <!-- 6.10: Stacked Area Chart -->
                    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-100 relative">
                        <span class="absolute -top-3 -left-3 text-xs text-label-secondary">6.10</span>
                        <h3 class="text-xs font-semibold mb-3 text-label-secondary">Traffic Sources (Stacked Area)</h3>
                        <div class="chart-placeholder bg-gray-50 rounded-lg h-48 relative overflow-hidden">
                            <svg class="w-full h-full" viewBox="0 0 100 60" preserveAspectRatio="none">
                                <defs>
                                    <linearGradient id="gradient1" x1="0%" y1="0%" x2="0%" y2="100%">
                                        <stop offset="0%" style="stop-color:#007AFF;stop-opacity:0.3"/>
                                        <stop offset="100%" style="stop-color:#007AFF;stop-opacity:0.1"/>
                                    </linearGradient>
                                    <linearGradient id="gradient2" x1="0%" y1="0%" x2="0%" y2="100%">
                                        <stop offset="0%" style="stop-color:#34C759;stop-opacity:0.3"/>
                                        <stop offset="100%" style="stop-color:#34C759;stop-opacity:0.1"/>
                                    </linearGradient>
                                </defs>
                                <!-- Bottom Area -->
                                <path d="M0 60 L0 45 L20 42 L40 44 L60 40 L80 38 L100 35 L100 60 Z" 
                                      fill="url(#gradient2)"/>
                                <!-- Top Area -->
                                <path d="M0 45 L20 42 L40 44 L60 40 L80 38 L100 35 L100 20 L80 25 L60 30 L40 28 L20 32 L0 30 Z" 
                                      fill="url(#gradient1)"/>
                            </svg>
                            <!-- Legend -->
                            <div class="absolute top-2 right-2 bg-white bg-opacity-90 p-2 rounded-md text-[10px] space-y-1">
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-systemBlue mr-1"></div>
                                    <span>Organic</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-systemGreen mr-1"></div>
                                    <span>Direct</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 6.11: Revenue Forecast Chart -->
                    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-100 relative">
                        <span class="absolute -top-3 -left-3 text-xs text-label-secondary">6.11</span>
                        <h3 class="text-xs font-semibold mb-3 text-label-secondary">Revenue Forecast</h3>
                        <div class="chart-placeholder bg-gray-50 rounded-lg h-48 relative overflow-hidden">
                            <svg class="w-full h-full" viewBox="0 0 100 60" preserveAspectRatio="none">
                                <!-- Confidence Band -->
                                <path d="M0 35 L20 32 L40 30 L60 28 L80 25 L100 23 L100 38 L80 35 L60 33 L40 35 L20 37 L0 40 Z" 
                                      fill="rgba(0, 122, 255, 0.1)"/>
                                <!-- Historical Line -->
                                <path d="M0 38 L20 35 L40 33 L50 31" 
                                      fill="none" stroke="#007AFF" stroke-width="0.75"/>
                                <!-- Forecast Line (dashed) -->
                                <path d="M50 31 L60 30 L80 28 L100 25" 
                                      fill="none" stroke="#007AFF" stroke-width="0.75" stroke-dasharray="2,2"/>
                                <!-- Divider -->
                                <line x1="50" y1="20" x2="50" y2="40" stroke="#E5E5EA" stroke-width="0.5" stroke-dasharray="2,2"/>
                            </svg>
                            <!-- Legend -->
                            <div class="absolute top-2 right-2 bg-white bg-opacity-90 p-2 rounded-md text-[10px] space-y-1">
                                <div class="flex items-center">
                                    <div class="w-3 h-0.5 bg-systemBlue mr-1"></div>
                                    <span>Historical</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-3 h-0.5 border-t border-dashed border-systemBlue mr-1"></div>
                                    <span>Forecast</span>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3 p-2 bg-blue-50 text-blue-800 rounded-md text-[11px] flex items-start">
                            <span class="mr-2 text-base leading-none text-systemBlue">📈</span>
                            <span>Projected 15% growth with 90% confidence</span>
                        </div>
                    </div>

                    <!-- 6.12: Radial Progress Chart -->
                    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-100 relative">
                        <span class="absolute -top-3 -left-3 text-xs text-label-secondary">6.12</span>
                        <h3 class="text-xs font-semibold mb-3 text-label-secondary">Goal Progress</h3>
                        <div class="chart-placeholder bg-gray-50 rounded-lg h-48 flex items-center justify-center">
                            <div class="relative w-32 h-32">
                                <svg class="w-full h-full transform -rotate-90">
                                    <circle cx="64" cy="64" r="60" fill="none" stroke="#E5E5EA" stroke-width="4"/>
                                    <circle cx="64" cy="64" r="60" fill="none" stroke="#007AFF" stroke-width="4" 
                                            stroke-dasharray="377" stroke-dashoffset="94"/>
                                </svg>
                                <div class="absolute inset-0 flex flex-col items-center justify-center">
                                    <span class="text-2xl font-semibold text-systemBlue">75%</span>
                                    <span class="text-[10px] text-label-secondary">Complete</span>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3 p-2 bg-blue-50 text-blue-800 rounded-md text-[11px] flex items-start">
                            <span class="mr-2 text-base leading-none text-systemBlue">🎯</span>
                            <span>On track to meet Q4 targets</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 7: Filter Components -->
        <section class="w-full py-10 px-4 sm:px-6 lg:px-8">
            <div>
                <h2 class="text-2xl font-bold mb-6">7. Filter Components</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    
                    <!-- 7.1: Range Slider with Numeric Input -->
                    <div class="bg-white rounded-view p-5 border border-gray-200 relative">
                        <span class="absolute -top-3 -left-3 text-xs text-label-secondary">7.1</span>
                        <h3 class="text-sm font-semibold mb-4">Price Range Filter</h3>
                        <div class="space-y-4">
                            <div class="flex items-center gap-4">
                                <div class="flex-1">
                                    <label class="block text-xs text-label-secondary mb-1">Min</label>
                                    <input type="number" value="0" class="w-full px-3 py-1.5 text-sm border border-systemGray-lighter rounded-control"/>
                                </div>
                                <div class="flex-1">
                                    <label class="block text-xs text-label-secondary mb-1">Max</label>
                                    <input type="number" value="1000" class="w-full px-3 py-1.5 text-sm border border-systemGray-lighter rounded-control"/>
                                </div>
                            </div>
                            <div class="px-2">
                                <input type="range" class="w-full h-2 bg-systemGray-extralight rounded-lg appearance-none cursor-pointer accent-systemBlue"/>
                            </div>
                            <div class="flex justify-between text-xs text-label-secondary">
                                <span>$0</span>
                                <span>$1000</span>
                            </div>
                        </div>
                    </div>

                    <!-- 7.2: Date Range Picker -->
                    <div class="bg-white rounded-view p-5 border border-gray-200 relative">
                        <span class="absolute -top-3 -left-3 text-xs text-label-secondary">7.2</span>
                        <h3 class="text-sm font-semibold mb-4">Date Range</h3>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-xs text-label-secondary mb-1">Start Date</label>
                                <input type="date" class="w-full px-3 py-1.5 text-sm border border-systemGray-lighter rounded-control"/>
                            </div>
                            <div>
                                <label class="block text-xs text-label-secondary mb-1">End Date</label>
                                <input type="date" class="w-full px-3 py-1.5 text-sm border border-systemGray-lighter rounded-control"/>
                            </div>
                        </div>
                        <div class="flex gap-2 mt-3">
                            <button class="text-xs px-3 py-1 rounded-full bg-systemGray-ultralight text-label-secondary hover:bg-systemGray-extralight">Last 7 days</button>
                            <button class="text-xs px-3 py-1 rounded-full bg-systemGray-ultralight text-label-secondary hover:bg-systemGray-extralight">Last 30 days</button>
                            <button class="text-xs px-3 py-1 rounded-full bg-systemBlue text-white">Custom</button>
                        </div>
                    </div>

                    <!-- 7.3: Multi-select Dropdown with Search -->
                    <div class="bg-white rounded-view p-5 border border-gray-200 relative">
                        <span class="absolute -top-3 -left-3 text-xs text-label-secondary">7.3</span>
                        <h3 class="text-sm font-semibold mb-4">Categories</h3>
                        <div class="relative">
                            <input type="text" placeholder="Search categories..." class="w-full px-3 py-2 text-sm border border-systemGray-lighter rounded-control mb-2"/>
                            <div class="border border-systemGray-lighter rounded-control p-2 max-h-40 overflow-y-auto">
                                <label class="flex items-center p-1 hover:bg-systemGray-ultralight rounded cursor-pointer">
                                    <input type="checkbox" class="rounded border-systemGray-lighter text-systemBlue mr-2"/>
                                    <span class="text-sm">Electronics</span>
                                </label>
                                <label class="flex items-center p-1 hover:bg-systemGray-ultralight rounded cursor-pointer">
                                    <input type="checkbox" checked class="rounded border-systemGray-lighter text-systemBlue mr-2"/>
                                    <span class="text-sm">Clothing</span>
                                </label>
                                <label class="flex items-center p-1 hover:bg-systemGray-ultralight rounded cursor-pointer">
                                    <input type="checkbox" class="rounded border-systemGray-lighter text-systemBlue mr-2"/>
                                    <span class="text-sm">Home & Garden</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- 7.4: Cascading Filters -->
                    <div class="bg-white rounded-view p-5 border border-gray-200 relative">
                        <span class="absolute -top-3 -left-3 text-xs text-label-secondary">7.4</span>
                        <h3 class="text-sm font-semibold mb-4">Location Filter</h3>
                        <div class="space-y-3">
                            <div>
                                <label class="block text-xs text-label-secondary mb-1">Country</label>
                                <select class="w-full px-3 py-1.5 text-sm border border-systemGray-lighter rounded-control">
                                    <option>United States</option>
                                    <option>Canada</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-xs text-label-secondary mb-1">State</label>
                                <select class="w-full px-3 py-1.5 text-sm border border-systemGray-lighter rounded-control">
                                    <option>California</option>
                                    <option>New York</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-xs text-label-secondary mb-1">City</label>
                                <select class="w-full px-3 py-1.5 text-sm border border-systemGray-lighter rounded-control">
                                    <option>San Francisco</option>
                                    <option>Los Angeles</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- 7.5: Active Filter Tags -->
                    <div class="bg-white rounded-view p-5 border border-gray-200 relative col-span-full">
                        <span class="absolute -top-3 -left-3 text-xs text-label-secondary">7.5</span>
                        <h3 class="text-sm font-semibold mb-4">Active Filters</h3>
                        <div class="flex flex-wrap gap-2">
                            <div class="inline-flex items-center bg-systemGray-ultralight px-3 py-1 rounded-full text-sm">
                                <span class="text-label-secondary mr-2">Price: $0 - $1000</span>
                                <button class="text-systemGray hover:text-gray-700">×</button>
                            </div>
                            <div class="inline-flex items-center bg-systemGray-ultralight px-3 py-1 rounded-full text-sm">
                                <span class="text-label-secondary mr-2">Date: Last 7 days</span>
                                <button class="text-systemGray hover:text-gray-700">×</button>
                            </div>
                            <div class="inline-flex items-center bg-systemGray-ultralight px-3 py-1 rounded-full text-sm">
                                <span class="text-label-secondary mr-2">Category: Clothing</span>
                                <button class="text-systemGray hover:text-gray-700">×</button>
                            </div>
                            <div class="inline-flex items-center bg-systemGray-ultralight px-3 py-1 rounded-full text-sm">
                                <span class="text-label-secondary mr-2">Location: San Francisco, CA</span>
                                <button class="text-systemGray hover:text-gray-700">×</button>
                            </div>
                            <button class="text-sm text-systemBlue hover:underline">Clear all filters</button>
                        </div>
                    </div>

                </div>
            </div>
        </section>

        <!-- Section 8: Alerts & Notifications -->
        <section class="w-full py-10 px-4 sm:px-6 lg:px-8">
            <div>
                <h2 class="text-2xl font-bold mb-6">8. Alerts & Notifications</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <!-- 8.1: Success Alert (Sidebar Style) -->
                    <div class="bg-systemGreen-ultralight border-l-4 border-systemGreen p-3 rounded-r-md relative">
                        <span class="absolute -top-3 -left-3 text-xs text-label-secondary">8.1</span>
                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-systemGreen mt-0.5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                </svg>
                            <div>
                                <h3 class="text-sm font-medium text-systemGreen">Sucesso!</h3>
                                <p class="text-xs text-systemGreen-dark mt-1">Suas alterações foram salvas com sucesso.</p>
                            </div>
                        </div>
                    </div>

                    <!-- 8.2: Info Alert (Top Banner Style) -->
                    <div class="bg-systemBlue-ultralight p-3 rounded-md relative">
                        <span class="absolute -top-3 -left-3 text-xs text-label-secondary">8.2</span>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-systemBlue mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                <p class="text-xs text-systemBlue">Nova atualização disponível</p>
                            </div>
                            <button class="text-xs text-systemBlue hover:text-systemBlue-dark">Atualizar</button>
                        </div>
                    </div>

                    <!-- 8.3: Warning Alert (Compact Style) -->
                    <div class="bg-systemYellow-ultralight border border-systemYellow p-3 rounded-md relative">
                        <span class="absolute -top-3 -left-3 text-xs text-label-secondary">8.3</span>
                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-systemYellow mt-0.5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                                </svg>
                            <p class="text-xs text-systemYellow-dark">Seu plano expira em 3 dias</p>
                            </div>
                        </div>
                    </div>

                <!-- Mantendo 8.4 original para abordagem comercial -->
                <div class="mt-6">
                    <!-- 8.4: Premium Feature Banner (Original - Purple) -->
                    <div class="bg-gradient-to-r from-purple-600 to-indigo-600 rounded-view p-6 relative">
                        <span class="absolute -top-3 -left-3 text-xs text-label-secondary">8.4</span>
                        <div class="flex flex-col sm:flex-row sm:items-start sm:justify-between">
                            <div class="flex-1">
                                <h3 class="text-xl font-semibold text-white">Unlock Advanced Analytics</h3>
                                <p class="mt-2 text-sm text-purple-100">Get access to AI-powered insights, custom reports, and real-time analytics.</p>
                                <div class="mt-4 space-y-2">
                                    <div class="flex items-center text-sm text-purple-100">
                                        <svg class="h-5 w-5 text-purple-300 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                        </svg>
                                        Advanced AI Analysis
                                    </div>
                                    <div class="flex items-center text-sm text-purple-100">
                                        <svg class="h-5 w-5 text-purple-300 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                        </svg>
                                        Custom Report Builder
                                    </div>
                                    <div class="flex items-center text-sm text-purple-100">
                                        <svg class="h-5 w-5 text-purple-300 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                        </svg>
                                        24/7 Priority Support
                                    </div>
                                </div>
                            </div>
                            <div class="mt-6 sm:mt-0 sm:ml-8 flex-shrink-0">
                                <button class="w-full sm:w-auto inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-indigo-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 shadow-sm">
                                    Upgrade Now
                                    <svg class="ml-2 -mr-1 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 8.4.1: Blue Premium Feature Banner -->
                    <div class="bg-gradient-to-r from-systemBlue to-blue-500 rounded-view p-6 relative">
                        <span class="absolute -top-3 -left-3 text-xs text-label-secondary">8.4.1</span>
                        <div class="flex flex-col sm:flex-row sm:items-start sm:justify-between">
                            <div class="flex-1">
                                <h3 class="text-xl font-semibold text-white">Unlock Pro Features</h3>
                                <p class="mt-2 text-sm text-blue-100">Access premium tools, advanced reporting, and exclusive content.</p>
                                <div class="mt-4 space-y-2">
                                    <div class="flex items-center text-sm text-blue-100">
                                        <svg class="h-5 w-5 text-blue-300 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                        </svg>
                                        Premium Templates
                                    </div>
                                    <div class="flex items-center text-sm text-blue-100">
                                        <svg class="h-5 w-5 text-blue-300 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                        </svg>
                                        Advanced Analytics
                                    </div>
                                    <div class="flex items-center text-sm text-blue-100">
                                        <svg class="h-5 w-5 text-blue-300 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                        </svg>
                                        Priority Support
                                    </div>
                                </div>
                            </div>
                            <div class="mt-6 sm:mt-0 sm:ml-8 flex-shrink-0">
                                <button class="w-full sm:w-auto inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-systemBlue bg-white hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-systemBlue shadow-sm">
                                    Get Started
                                    <svg class="ml-2 -mr-1 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 8.4.2: Green Premium Feature Banner -->
                    <div class="bg-gradient-to-r from-systemGreen to-green-400 rounded-view p-6 relative">
                        <span class="absolute -top-3 -left-3 text-xs text-label-secondary">8.4.2</span>
                        <div class="flex flex-col sm:flex-row sm:items-start sm:justify-between">
                            <div class="flex-1">
                                <h3 class="text-xl font-semibold text-white">Go Premium Today</h3>
                                <p class="mt-2 text-sm text-green-100">Enhance your workflow with premium features and tools.</p>
                                <div class="mt-4 space-y-2">
                                    <div class="flex items-center text-sm text-green-100">
                                        <svg class="h-5 w-5 text-green-300 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                        </svg>
                                        Unlimited Projects
                                    </div>
                                    <div class="flex items-center text-sm text-green-100">
                                        <svg class="h-5 w-5 text-green-300 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                        </svg>
                                        Team Collaboration
                                    </div>
                                    <div class="flex items-center text-sm text-green-100">
                                        <svg class="h-5 w-5 text-green-300 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                        </svg>
                                        Expert Support
                                    </div>
                                </div>
                            </div>
                            <div class="mt-6 sm:mt-0 sm:ml-8 flex-shrink-0">
                                <button class="w-full sm:w-auto inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-systemGreen bg-white hover:bg-green-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-systemGreen shadow-sm">
                                    Upgrade Now
                                    <svg class="ml-2 -mr-1 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 8.4.3: Gray Premium Feature Banner -->
                    <div class="bg-gradient-to-r from-systemGray to-gray-500 rounded-view p-6 relative">
                        <span class="absolute -top-3 -left-3 text-xs text-label-secondary">8.4.3</span>
                        <div class="flex flex-col sm:flex-row sm:items-start sm:justify-between">
                            <div class="flex-1">
                                <h3 class="text-xl font-semibold text-white">Enterprise Features</h3>
                                <p class="mt-2 text-sm text-gray-100">Scale your business with enterprise-grade tools and support.</p>
                                <div class="mt-4 space-y-2">
                                    <div class="flex items-center text-sm text-gray-100">
                                        <svg class="h-5 w-5 text-gray-300 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                        </svg>
                                        Enterprise Security
                                    </div>
                                    <div class="flex items-center text-sm text-gray-100">
                                        <svg class="h-5 w-5 text-gray-300 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                        </svg>
                                        Dedicated Manager
                                    </div>
                                    <div class="flex items-center text-sm text-gray-100">
                                        <svg class="h-5 w-5 text-gray-300 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                        </svg>
                                        Custom Integration
                                    </div>
                                </div>
                            </div>
                            <div class="mt-6 sm:mt-0 sm:ml-8 flex-shrink-0">
                                <button class="w-full sm:w-auto inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 shadow-sm">
                                    Contact Sales
                                    <svg class="ml-2 -mr-1 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 8.4.2: Wide Premium Banner (Based on 4.4 style) -->
                    <div class="bg-gradient-to-r from-chartPurple to-purple-500 rounded-view p-5 lg:col-span-2 relative">
                        <span class="absolute -top-3 -left-3 text-xs text-label-secondary">8.4.2</span>
                        <div class="flex flex-col sm:flex-row sm:items-start sm:justify-between">
                            <div class="flex-1">
                                <h3 class="text-xl font-semibold text-white">Analytics Suite Pro</h3>
                                <p class="mt-2 text-sm text-purple-100">Unlock the full potential of your data with our comprehensive analytics suite.</p>
                                <div class="grid grid-cols-2 gap-4 mt-4">
                                    <div class="flex items-center text-sm text-purple-100">
                                        <svg class="h-5 w-5 text-purple-300 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                        </svg>
                                        Advanced Reporting
                                    </div>
                                    <div class="flex items-center text-sm text-purple-100">
                                        <svg class="h-5 w-5 text-purple-300 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                        </svg>
                                        Custom Dashboards
                                    </div>
                                    <div class="flex items-center text-sm text-purple-100">
                                        <svg class="h-5 w-5 text-purple-300 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                        </svg>
                                        AI Predictions
                                    </div>
                                    <div class="flex items-center text-sm text-purple-100">
                                        <svg class="h-5 w-5 text-purple-300 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                        </svg>
                                        Export Tools
                                    </div>
                                </div>
                                <div class="mt-3 p-2 bg-purple-900 bg-opacity-30 text-purple-100 rounded-md text-[11px] flex items-start">
                                    <span class="mr-2 text-base leading-none">✨</span>
                                    <span>Special launch pricing available for early adopters!</span>
                                </div>
                            </div>
                            <div class="mt-6 sm:mt-0 sm:ml-8 flex-shrink-0">
                                <button class="w-full sm:w-auto inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-purple-600 bg-white hover:bg-purple-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 shadow-sm">
                                    Start Free Trial
                                    <svg class="ml-2 -mr-1 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 8.4.3: Compact Premium Banner (Based on 4.3 style) -->
                    <div class="bg-gradient-to-r from-systemBlue to-blue-600 rounded-view p-4 relative">
                        <span class="absolute -top-3 -left-3 text-xs text-label-secondary">8.4.3</span>
                        <div class="flex flex-col">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-semibold text-white">Quick Upgrade</h3>
                                <span class="text-xs bg-blue-900 bg-opacity-30 text-blue-100 px-2 py-1 rounded-full">Limited Time</span>
                            </div>
                            <p class="text-3xl font-bold text-white mb-2">$29<span class="text-sm">/mo</span></p>
                            <p class="text-sm text-blue-100 mb-4">Save 40% with annual billing</p>
                            <button class="w-full bg-white text-systemBlue font-medium py-2 px-4 rounded-md hover:bg-blue-50 transition-colors">
                                Upgrade Now
                            </button>
                        </div>
                    </div>

                    <!-- 8.4.4: Tall Premium Banner (Based on 4.5 style) -->
                    <div class="bg-gradient-to-r from-gray-800 to-gray-900 rounded-view p-5 relative">
                        <span class="absolute -top-3 -left-3 text-xs text-label-secondary">8.4.4</span>
                        <div class="flex flex-col h-full">
                            <h3 class="text-lg font-semibold text-white mb-2">Enterprise Plan</h3>
                            <div class="flex items-baseline mb-4">
                                <p class="text-3xl font-bold text-white">Custom</p>
                                <p class="text-sm text-gray-300 ml-2">/ organization</p>
                            </div>
                            <div class="space-y-3 mb-6">
                                <div class="flex items-center text-sm text-gray-300">
                                    <svg class="h-5 w-5 text-gray-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                    </svg>
                                    Unlimited Users
                                </div>
                                <div class="flex items-center text-sm text-gray-300">
                                    <svg class="h-5 w-5 text-gray-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                    </svg>
                                    Dedicated Support
                                </div>
                                <div class="flex items-center text-sm text-gray-300">
                                    <svg class="h-5 w-5 text-gray-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                    </svg>
                                    Custom Integration
                                </div>
                            </div>
                            <div class="mt-auto">
                                <button class="w-full bg-white text-gray-900 font-medium py-2 px-4 rounded-md hover:bg-gray-100 transition-colors mb-2">
                                    Contact Sales
                                </button>
                                <p class="text-xs text-gray-400 text-center">Enterprise-grade security & support</p>
                            </div>
                        </div>
                    </div>

                    <!-- 8.4.5: Stats Premium Banner (Based on 4.7 style) -->
                    <div class="bg-gradient-to-r from-systemBlue to-chartPurple rounded-view p-5 relative">
                        <span class="absolute -top-3 -left-3 text-xs text-label-secondary">8.4.5</span>
                        <div class="flex flex-col">
                            <div class="flex justify-between items-start mb-6">
                                <div>
                                    <h3 class="text-lg font-semibold text-white">Pro Analytics</h3>
                                    <p class="text-sm text-blue-100 mt-1">Unlock full potential</p>
                                </div>
                                <span class="h-3 w-3 bg-green-400 rounded-full"></span>
                            </div>
                            <div class="grid grid-cols-2 gap-4 mb-6">
                                <div class="bg-white bg-opacity-10 rounded-lg p-3">
                                    <p class="text-2xl font-bold text-white">10x</p>
                                    <p class="text-sm text-blue-100">Faster Analysis</p>
                                </div>
                                <div class="bg-white bg-opacity-10 rounded-lg p-3">
                                    <p class="text-2xl font-bold text-white">100%</p>
                                    <p class="text-sm text-blue-100">Data Access</p>
                                </div>
                            </div>
                            <button class="w-full bg-white text-systemBlue font-medium py-2 px-4 rounded-md hover:bg-blue-50 transition-colors">
                                Upgrade to Pro
                            </button>
                        </div>
                    </div>

                    <!-- 8.5: Toast Notification (Floating Style) -->
                    <div class="mt-6 flex justify-end">
                        <div class="bg-systemGray-dark text-white p-3 rounded-lg shadow-lg w-64 relative">
                            <span class="absolute -top-3 -left-3 text-xs text-label-secondary">8.5</span>
                            <div class="flex items-start space-x-2">
                                <div class="flex-shrink-0">
                                    <svg class="h-4 w-4 text-systemBlue" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"/>
                                    </svg>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900">Report Generated</p>
                                    <p class="mt-1 text-xs text-label-secondary">Your monthly analytics report is ready.</p>
                                    <div class="mt-2 flex space-x-3">
                                        <button class="text-xs font-medium text-systemBlue hover:text-blue-600">Download</button>
                                        <button class="text-xs font-medium text-label-secondary hover:text-gray-500">Dismiss</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </section>

        <!-- Section 9: Iconography -->
        <section class="w-full py-10 px-4 sm:px-6 lg:px-8">
            <div>
                <h2 class="text-2xl font-bold mb-6">9. Iconography</h2>
                
                <!-- 9.1: Data & Analytics Icons -->
                <div class="bg-white rounded-view p-6 mb-8">
                    <h3 class="text-base font-semibold mb-4 relative">
                        <span class="absolute -left-8 text-xs text-label-secondary">9.1</span>
                        Data & Analytics Icons
                    </h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6">
                        <!-- Charts -->
                        <div class="flex flex-col items-center">
                            <div class="w-12 h-12 bg-systemGray-ultralight rounded-lg flex items-center justify-center mb-2">
                                <svg class="w-6 h-6 text-systemBlue" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                </svg>
                            </div>
                            <span class="text-xs text-label-secondary">Bar Chart</span>
                        </div>

                        <!-- Line Chart -->
                        <div class="flex flex-col items-center">
                            <div class="w-12 h-12 bg-systemGray-ultralight rounded-lg flex items-center justify-center mb-2">
                                <svg class="w-6 h-6 text-systemBlue" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                                </svg>
                            </div>
                            <span class="text-xs text-label-secondary">Line Chart</span>
                        </div>

                        <!-- Pie Chart -->
                        <div class="flex flex-col items-center">
                            <div class="w-12 h-12 bg-systemGray-ultralight rounded-lg flex items-center justify-center mb-2">
                                <svg class="w-6 h-6 text-systemBlue" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z" />
                                </svg>
                            </div>
                            <span class="text-xs text-label-secondary">Pie Chart</span>
                        </div>

                        <!-- Analytics -->
                        <div class="flex flex-col items-center">
                            <div class="w-12 h-12 bg-systemGray-ultralight rounded-lg flex items-center justify-center mb-2">
                                <svg class="w-6 h-6 text-systemBlue" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                </svg>
                            </div>
                            <span class="text-xs text-label-secondary">Analytics</span>
                        </div>

                        <!-- Dashboard -->
                        <div class="flex flex-col items-center">
                            <div class="w-12 h-12 bg-systemGray-ultralight rounded-lg flex items-center justify-center mb-2">
                                <svg class="w-6 h-6 text-systemBlue" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
                                </svg>
                            </div>
                            <span class="text-xs text-label-secondary">Dashboard</span>
                        </div>

                        <!-- Data Processing -->
                        <div class="flex flex-col items-center">
                            <div class="w-12 h-12 bg-systemGray-ultralight rounded-lg flex items-center justify-center mb-2">
                                <svg class="w-6 h-6 text-systemBlue" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                                </svg>
                            </div>
                            <span class="text-xs text-label-secondary">Data Processing</span>
                        </div>
                    </div>
                </div>

                <!-- 9.2: Business & Management Icons -->
                <div class="bg-white rounded-view p-6 mb-8">
                    <h3 class="text-base font-semibold mb-4 relative">
                        <span class="absolute -left-8 text-xs text-label-secondary">9.2</span>
                        Business & Management Icons
                    </h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6">
                        <!-- Team -->
                        <div class="flex flex-col items-center">
                            <div class="w-12 h-12 bg-systemGray-ultralight rounded-lg flex items-center justify-center mb-2">
                                <svg class="w-6 h-6 text-systemBlue" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                                </svg>
                            </div>
                            <span class="text-xs text-label-secondary">Team</span>
                        </div>

                        <!-- Reports -->
                        <div class="flex flex-col items-center">
                            <div class="w-12 h-12 bg-systemGray-ultralight rounded-lg flex items-center justify-center mb-2">
                                <svg class="w-6 h-6 text-systemBlue" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                            </div>
                            <span class="text-xs text-label-secondary">Reports</span>
                        </div>

                        <!-- Tasks -->
                        <div class="flex flex-col items-center">
                            <div class="w-12 h-12 bg-systemGray-ultralight rounded-lg flex items-center justify-center mb-2">
                                <svg class="w-6 h-6 text-systemBlue" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                                </svg>
                            </div>
                            <span class="text-xs text-label-secondary">Tasks</span>
                        </div>

                        <!-- Calendar -->
                        <div class="flex flex-col items-center">
                            <div class="w-12 h-12 bg-systemGray-ultralight rounded-lg flex items-center justify-center mb-2">
                                <svg class="w-6 h-6 text-systemBlue" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                            </div>
                            <span class="text-xs text-label-secondary">Calendar</span>
                        </div>

                        <!-- Settings -->
                        <div class="flex flex-col items-center">
                            <div class="w-12 h-12 bg-systemGray-ultralight rounded-lg flex items-center justify-center mb-2">
                                <svg class="w-6 h-6 text-systemBlue" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                            </div>
                            <span class="text-xs text-label-secondary">Settings</span>
                        </div>

                        <!-- Notifications -->
                        <div class="flex flex-col items-center">
                            <div class="w-12 h-12 bg-systemGray-ultralight rounded-lg flex items-center justify-center mb-2">
                                <svg class="w-6 h-6 text-systemBlue" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"/>
                                </svg>
                            </div>
                            <span class="text-xs text-label-secondary">Notifications</span>
                        </div>
                    </div>
                </div>

                <!-- 9.3: Data Security & Integration Icons -->
                <div class="bg-white rounded-view p-6">
                    <h3 class="text-base font-semibold mb-4 relative">
                        <span class="absolute -left-8 text-xs text-label-secondary">9.3</span>
                        Security & Integration Icons
                    </h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6">
                        <!-- Security -->
                        <div class="flex flex-col items-center">
                            <div class="w-12 h-12 bg-systemGray-ultralight rounded-lg flex items-center justify-center mb-2">
                                <svg class="w-6 h-6 text-systemBlue" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                </svg>
                            </div>
                            <span class="text-xs text-label-secondary">Security</span>
                        </div>

                        <!-- API -->
                        <div class="flex flex-col items-center">
                            <div class="w-12 h-12 bg-systemGray-ultralight rounded-lg flex items-center justify-center mb-2">
                                <svg class="w-6 h-6 text-systemBlue" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                            </div>
                            <span class="text-xs text-label-secondary">API</span>
                        </div>

                        <!-- Database -->
                        <div class="flex flex-col items-center">
                            <div class="w-12 h-12 bg-systemGray-ultralight rounded-lg flex items-center justify-center mb-2">
                                <svg class="w-6 h-6 text-systemBlue" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4" />
                                </svg>
                            </div>
                            <span class="text-xs text-label-secondary">Database</span>
                        </div>

                        <!-- Cloud -->
                        <div class="flex flex-col items-center">
                            <div class="w-12 h-12 bg-systemGray-ultralight rounded-lg flex items-center justify-center mb-2">
                                <svg class="w-6 h-6 text-systemBlue" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z" />
                                </svg>
                            </div>
                            <span class="text-xs text-label-secondary">Cloud</span>
                        </div>

                        <!-- Sync -->
                        <div class="flex flex-col items-center">
                            <div class="w-12 h-12 bg-systemGray-ultralight rounded-lg flex items-center justify-center mb-2">
                                <svg class="w-6 h-6 text-systemBlue" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                </svg>
                            </div>
                            <span class="text-xs text-label-secondary">Sync</span>
                        </div>

                        <!-- Integration -->
                        <div class="flex flex-col items-center">
                            <div class="w-12 h-12 bg-systemGray-ultralight rounded-lg flex items-center justify-center mb-2">
                                <svg class="w-6 h-6 text-systemBlue" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 110-4h1a1 1 0 001-1V7a1 1 0 011-1h3a1 1 0 001-1V4z" />
                                </svg>
                            </div>
                            <span class="text-xs text-label-secondary">Integration</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <footer class="w-full mt-12 border-t border-gray-200 bg-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 text-center">
                <p class="text-xs text-label-secondary">Design System v3.0 (Flask/Tailwind - Renumbered Sections) - Inspired by Apple HIG</p>
            </div>
        </footer>

    </main>

</body>
</html>
"""

@app.route('/')
def home():
    return render_template_string(HTML_TEMPLATE)

if __name__ == '__main__':
    app.run(debug=True)

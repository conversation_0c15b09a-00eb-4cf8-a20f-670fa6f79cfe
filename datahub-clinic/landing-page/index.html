<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Amigo DataHub - Transforme dados médicos em decisões inteligentes</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: #1a1a1a;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            z-index: 1000;
            padding: 1rem 0;
            border-bottom: 1px solid rgba(0, 122, 255, 0.1);
        }

        .nav {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .logo img {
            height: 56px;
            width: auto;
        }

        .logo-text {
            font-size: 1.75rem;
            font-weight: 700;
            color: #007AFF;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            text-decoration: none;
            color: #1a1a1a;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover {
            color: #007AFF;
        }

        .nav-links a.active {
            color: #007AFF;
            font-weight: 600;
            position: relative;
        }

        .nav-links a.active::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 0;
            right: 0;
            height: 2px;
            background: #007AFF;
            border-radius: 1px;
        }

        /* Mobile Menu */
        .mobile-menu-toggle {
            display: none;
            flex-direction: column;
            cursor: pointer;
            padding: 0.5rem;
            gap: 4px;
        }

        .mobile-menu-toggle span {
            width: 24px;
            height: 2px;
            background: #1a1a1a;
            transition: all 0.3s ease;
            border-radius: 1px;
        }

        .mobile-menu-toggle.active span:nth-child(1) {
            transform: rotate(45deg) translate(5px, 5px);
        }

        .mobile-menu-toggle.active span:nth-child(2) {
            opacity: 0;
        }

        .mobile-menu-toggle.active span:nth-child(3) {
            transform: rotate(-45deg) translate(7px, -6px);
        }

        .mobile-menu {
            display: none;
            position: fixed;
            top: 80px;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 122, 255, 0.1);
            z-index: 999;
            padding: 1rem 0;
        }

        .mobile-menu.active {
            display: block;
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .mobile-nav-links {
            display: flex;
            flex-direction: column;
            list-style: none;
            padding: 0 2rem;
            gap: 1rem;
        }

        .mobile-nav-links a {
            text-decoration: none;
            color: #1a1a1a;
            font-weight: 500;
            padding: 0.75rem 0;
            border-bottom: 1px solid #f0f0f0;
            transition: color 0.3s ease;
        }

        .mobile-nav-links a:hover {
            color: #007AFF;
        }

        .cta-button {
            background: #007AFF;
            color: white;
            padding: 1rem 2rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 500;
            font-size: 0.95rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: none;
            cursor: pointer;
            box-shadow: 0 4px 20px rgba(0, 122, 255, 0.3);
            position: relative;
            overflow: hidden;
        }

        .cta-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.6s;
        }

        .cta-button:hover::before {
            left: 100%;
        }

        .cta-button:hover {
            background: #0056CC;
            transform: translateY(-3px);
            box-shadow: 0 8px 30px rgba(0, 122, 255, 0.4);
        }

        /* Hero Section */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            position: relative;
            overflow: hidden;
            padding-top: 80px;
        }

        .hero-container {
            max-width: 1400px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 45fr 55fr;
            gap: 2rem;
            align-items: center;
            padding: 0 2rem;
            position: relative;
            z-index: 2;
            min-height: 100vh;
        }

        .hero-content h1 {
            font-size: 2.75rem;
            font-weight: 800;
            line-height: 1.1;
            margin-bottom: 1.5rem;
            color: #1a1a1a;
        }

        .hero-title-stacked {
            text-align: left !important;
            margin-bottom: 2rem;
        }

        .title-word {
            font-size: 4.5rem !important;
            font-weight: 900;
            line-height: 0.9;
            margin-bottom: 0.75rem;
            color: #007AFF;
            text-shadow: 2px 2px 4px rgba(0, 122, 255, 0.1);
            letter-spacing: -0.03em;
            display: block;
            transition: all 0.3s ease;
        }

        .title-word:hover {
            transform: translateX(10px);
            text-shadow: 4px 4px 8px rgba(0, 122, 255, 0.2);
        }

        .product-branding {
            margin-bottom: 1rem;
            text-align: left;
        }

        .product-name {
            font-size: 1.5rem;
            font-weight: 600;
            color: #007AFF;
            margin-bottom: 0.25rem;
            letter-spacing: -0.01em;
        }

        .ecosystem-badge {
            font-size: 0.75rem;
            color: #64748b;
            font-weight: 500;
            margin: 0;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .title-word:nth-child(1) {
            color: #007AFF; /* Azul principal */
        }

        .title-word:nth-child(2) {
            color: #0056CC; /* Azul mais escuro */
        }

        .title-word:nth-child(3) {
            color: #003D99; /* Azul mais escuro ainda */
        }

        .hero-content .highlight {
            color: #007AFF;
        }

        .hero-content p {
            font-size: 1.125rem;
            color: #475569;
            margin-bottom: 1.5rem;
            font-weight: 500;
            line-height: 1.6;
        }

        .value-proposition {
            font-size: 1.25rem !important;
            color: #1e293b !important;
            font-weight: 600 !important;
            margin-bottom: 1rem !important;
            line-height: 1.4;
        }

        .social-proof {
            display: flex;
            align-items: center;
            gap: 2rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .proof-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            min-width: 120px;
        }

        .proof-number {
            font-size: 1.75rem;
            font-weight: 800;
            color: #007AFF;
            line-height: 1;
            margin-bottom: 0.25rem;
        }

        .proof-label {
            font-size: 0.8rem;
            color: #64748b;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .hero-buttons {
            display: flex;
            gap: 1rem;
            margin-bottom: 3rem;
        }

        .btn-secondary {
            background: rgba(0, 122, 255, 0.08);
            color: #007AFF;
            border: 1px solid rgba(0, 122, 255, 0.2);
            padding: 1rem 2rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 500;
            font-size: 0.95rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
        }

        .btn-secondary:hover {
            background: rgba(0, 122, 255, 0.15);
            border-color: rgba(0, 122, 255, 0.4);
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0, 122, 255, 0.2);
        }

        .hero-stats {
            display: flex;
            gap: 2rem;
        }

        .stat {
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #007AFF;
            display: block;
        }

        .stat-label {
            font-size: 0.875rem;
            color: #666;
            font-weight: 500;
        }

        /* Hero Visual */
        .hero-visual {
            position: relative;
            height: 100vh;
            min-height: 600px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .main-illustration {
            width: 100%;
            height: 100%;
            max-width: 900px; /* Increased from 800px */
            max-height: 800px; /* Increased from 700px */
            position: relative;
            z-index: 3;
            overflow: visible; /* Ensure cube is not clipped */
        }

        #d3-visualization {
            width: 100%;
            height: 100%;
            overflow: visible; /* Prevent clipping of the cube */
        }

        /* Floating Elements */
        .floating-element {
            position: absolute;
            border-radius: 8px;
            animation: float 6s ease-in-out infinite;
        }

        .floating-element.square {
            width: 40px;
            height: 40px;
            background: rgba(0, 122, 255, 0.2);
        }

        .floating-element.rectangle {
            width: 60px;
            height: 30px;
            background: rgba(0, 122, 255, 0.15);
        }

        .floating-element.circle {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: rgba(0, 122, 255, 0.25);
        }

        .floating-element:nth-child(1) {
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-element:nth-child(2) {
            top: 20%;
            right: 15%;
            animation-delay: 1s;
        }

        .floating-element:nth-child(3) {
            bottom: 30%;
            left: 5%;
            animation-delay: 2s;
        }

        .floating-element:nth-child(4) {
            bottom: 15%;
            right: 10%;
            animation-delay: 3s;
        }

        .floating-element:nth-child(5) {
            top: 50%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
            }
            50% {
                transform: translateY(-20px) rotate(5deg);
            }
        }

        /* Common Styles */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        @media (max-width: 1240px) {
            .container {
                padding: 0 1.5rem;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 0 1rem;
            }
        }

        .section-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .section-header h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 1rem;
            letter-spacing: -0.02em;
            line-height: 1.2;
        }

        .section-header p {
            font-size: 1.125rem;
            color: #64748b;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
            font-weight: 400;
        }

        /* Value Banners Slider Section */
        .value-banners {
            padding: 0;
            background: #ffffff;
            position: relative;
            height: 75vh;
            overflow: hidden;
        }

        .banner-slider {
            position: relative;
            width: 100%;
            height: 100%;
        }

        .banner-slides {
            display: flex;
            width: 300%;
            height: 100%;
            transition: transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .banner {
            width: 33.333%;
            height: 75vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
            background-size: cover;
            background-position: center;
            background-attachment: scroll;
            flex-shrink: 0;
        }

        .banner::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.3) 100%);
            z-index: 1;
        }

        .banner-content {
            position: relative;
            z-index: 2;
            color: white;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            text-align: left;
        }

        .banner-content h2 {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            line-height: 1.2;
        }

        .banner-content p {
            font-size: 1.25rem;
            margin-bottom: 2rem;
            max-width: 600px;
            line-height: 1.6;
            opacity: 0.95;
        }

        .banner-cta {
            display: inline-flex;
            align-items: center;
            gap: 1rem;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 1rem 2rem;
            border-radius: 50px;
            color: white;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .banner-cta:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .banner-1 {
            background-image: url('https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80');
        }

        .banner-2 {
            background-image: url('https://images.unsplash.com/photo-1553877522-43269d4ea984?ixlib=rb-4.0.3&auto=format&fit=crop&w=2069&q=80');
        }

        .banner-3 {
            background-image: url('https://images.unsplash.com/photo-1677442136019-21780ecad995?ixlib=rb-4.0.3&auto=format&fit=crop&w=2071&q=80');
        }

        /* Slider Controls */
        .slider-controls {
            position: absolute;
            bottom: 2rem;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 1rem;
            z-index: 10;
        }

        .slider-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.4);
            border: 2px solid rgba(255, 255, 255, 0.6);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .slider-dot.active {
            background: rgba(255, 255, 255, 0.9);
            border-color: rgba(255, 255, 255, 1);
            transform: scale(1.2);
        }

        .slider-arrows {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            z-index: 10;
        }

        .slider-arrow {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .slider-arrow:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: scale(1.1);
        }

        .slider-arrow.prev {
            left: 2rem;
        }

        .slider-arrow.next {
            right: 2rem;
        }

        /* Progress bar */
        .slider-progress {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 4px;
            background: rgba(0, 122, 255, 0.8);
            transition: width 0.1s linear;
            z-index: 10;
        }

        /* Features Section */
        .features {
            padding: 6rem 0;
            background: #f8f9fa;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
        }

        .feature-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            padding: 2.5rem;
            border-radius: 20px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(0, 122, 255, 0.08);
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #007AFF, #0056CC);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 12px 40px rgba(0, 122, 255, 0.12);
            border-color: rgba(0, 122, 255, 0.2);
        }

        .feature-card:hover::before {
            opacity: 1;
        }

        .feature-icon {
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: flex-start;
        }

        .feature-card h3 {
            font-size: 1.375rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 1.25rem;
            letter-spacing: -0.01em;
        }

        .feature-card p {
            color: #64748b;
            line-height: 1.7;
            font-size: 0.95rem;
            font-weight: 400;
            margin-bottom: 1.5rem;
        }

        .feature-cta {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: #007AFF;
            text-decoration: none;
            font-weight: 600;
            font-size: 0.875rem;
            transition: all 0.3s ease;
            opacity: 0;
            transform: translateY(10px);
        }

        .feature-card:hover .feature-cta {
            opacity: 1;
            transform: translateY(0);
        }

        .feature-cta:hover {
            color: #0056CC;
            gap: 0.75rem;
        }

        .feature-priority {
            position: relative;
        }

        .feature-priority::after {
            content: 'Mais Usado';
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: linear-gradient(135deg, #007AFF, #0056CC);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Geometric Shapes */
        .geometric-shape {
            border-radius: 8px;
        }

        .circle-blue {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(0, 122, 255, 0.2);
        }

        .square-blue {
            width: 40px;
            height: 40px;
            background: rgba(0, 122, 255, 0.25);
        }

        .rectangle-blue {
            width: 50px;
            height: 30px;
            background: rgba(0, 122, 255, 0.2);
        }

        .triangle-blue {
            width: 0;
            height: 0;
            border-left: 20px solid transparent;
            border-right: 20px solid transparent;
            border-bottom: 35px solid rgba(0, 122, 255, 0.25);
            border-radius: 0;
        }

        .hexagon-blue {
            width: 40px;
            height: 35px;
            background: rgba(0, 122, 255, 0.2);
            position: relative;
            border-radius: 4px;
        }

        .hexagon-blue:before,
        .hexagon-blue:after {
            content: "";
            position: absolute;
            width: 0;
            border-left: 20px solid transparent;
            border-right: 20px solid transparent;
        }

        .hexagon-blue:before {
            bottom: 100%;
            border-bottom: 10px solid rgba(0, 122, 255, 0.2);
        }

        .hexagon-blue:after {
            top: 100%;
            border-top: 10px solid rgba(0, 122, 255, 0.2);
        }

        .diamond-blue {
            width: 35px;
            height: 35px;
            background: rgba(0, 122, 255, 0.25);
            transform: rotate(45deg);
        }

        /* Pricing Section */
        .pricing {
            padding: 8rem 0;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            position: relative;
        }

        .pricing::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 80%, rgba(0, 122, 255, 0.03) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(0, 122, 255, 0.03) 0%, transparent 50%);
            pointer-events: none;
        }

        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 3rem;
            margin-top: 4rem;
            margin-bottom: 4rem;
            position: relative;
            z-index: 2;
        }

        .community-plan {
            grid-column: 1 / -1;
            margin-top: 4rem;
            position: relative;
        }

        .community-plan .pricing-card {
            background: linear-gradient(135deg,
                rgba(0, 122, 255, 0.03) 0%,
                rgba(255, 255, 255, 0.95) 50%,
                rgba(0, 122, 255, 0.03) 100%);
            border: 2px solid transparent;
            background-clip: padding-box;
            position: relative;
            overflow: hidden;
        }

        .community-plan .pricing-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #007AFF, #4A90E2, #007AFF);
            margin: -2px;
            border-radius: inherit;
            z-index: -1;
        }

        .community-plan .pricing-card::after {
            content: '🎉 GRÁTIS';
            position: absolute;
            top: 1rem;
            right: 2rem;
            background: linear-gradient(135deg, #27AE60, #2ECC71);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 700;
            letter-spacing: 0.5px;
            text-transform: uppercase;
            box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .community-plan .pricing-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 2rem;
            position: relative;
        }

        .community-plan .plan-info {
            display: flex;
            align-items: center;
            gap: 2rem;
        }

        .community-plan h3 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            color: #1a1a1a;
            font-weight: 700;
        }

        .community-plan .plan-description {
            margin: 0;
            color: #666;
            font-size: 1rem;
            font-weight: 500;
        }

        .free-badge {
            background: linear-gradient(135deg, rgba(0, 122, 255, 0.1), rgba(0, 122, 255, 0.05));
            color: #007AFF;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-size: 0.875rem;
            font-weight: 600;
            white-space: nowrap;
            border: 2px solid rgba(0, 122, 255, 0.2);
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 20px rgba(0, 122, 255, 0.1);
        }

        .pricing-card {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 24px;
            padding: 3rem 2.5rem;
            text-align: left;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
            height: 100%;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .pricing-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #007AFF, #4A90E2);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .pricing-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 60px rgba(0, 122, 255, 0.15);
            border-color: rgba(0, 122, 255, 0.3);
        }

        .pricing-card:hover::before {
            opacity: 1;
        }

        .pricing-card.featured {
            background: linear-gradient(135deg, rgba(0, 122, 255, 0.05) 0%, rgba(255, 255, 255, 0.9) 100%);
            border: 2px solid rgba(0, 122, 255, 0.2);
            transform: scale(1.05);
            position: relative;
        }

        .pricing-card.featured::after {
            content: 'Mais Popular';
            position: absolute;
            top: -1px;
            right: 2rem;
            background: linear-gradient(135deg, #007AFF, #0056CC);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 0 0 16px 16px;
            font-size: 0.75rem;
            font-weight: 600;
            letter-spacing: 0.5px;
            text-transform: uppercase;
            box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
        }

        .pricing-card.featured:hover {
            transform: translateY(-8px) scale(1.07);
            box-shadow: 0 25px 80px rgba(0, 122, 255, 0.25);
        }

        .pricing-header {
            margin-bottom: 1.5rem;
        }

        .plan-name {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 0.5rem;
        }

        .plan-description {
            color: #666;
            font-size: 0.875rem;
            margin-bottom: 1.5rem;
            line-height: 1.5;
        }

        .price-section {
            margin-bottom: 2rem;
        }

        .price-display {
            display: flex;
            align-items: baseline;
            gap: 0.25rem;
            margin-bottom: 0.5rem;
        }

        .currency {
            font-size: 1.125rem;
            color: #666;
            font-weight: 500;
        }

        .amount {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1a1a1a;
            line-height: 1;
        }

        .period {
            font-size: 0.875rem;
            color: #666;
            font-weight: 500;
        }

        .price-note {
            font-size: 0.75rem;
            color: #999;
        }

        .features-section {
            flex: 1;
            margin-bottom: 2rem;
        }

        .features-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 1rem;
        }

        .features-list {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .features-list li {
            padding: 0.75rem 0;
            color: #666;
            position: relative;
            padding-left: 1.5rem;
            font-size: 0.875rem;
            line-height: 1.4;
            border-bottom: 1px solid #f5f5f5;
        }

        .features-list li:last-child {
            border-bottom: none;
        }

        .features-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #007AFF;
            font-weight: 600;
            font-size: 0.875rem;
        }

        .plan-button {
            width: 100%;
            text-align: center;
            display: block;
            margin-top: auto;
        }

        .cta-primary {
            background: #007AFF;
            color: white;
            border: none;
        }

        .cta-primary:hover {
            background: #0056CC;
        }

        /* Contact Section */
        .contact-section {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 5rem 0;
        }

        .contact-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: start;
        }

        .contact-info h2 {
            font-size: 2.5rem;
            font-weight: 800;
            color: #1e293b;
            margin-bottom: 1.5rem;
            line-height: 1.2;
        }

        .contact-info p {
            font-size: 1.125rem;
            color: #64748b;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .contact-stats {
            display: flex;
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .contact-stat {
            text-align: center;
        }

        .stat-number {
            display: block;
            font-size: 1.75rem;
            font-weight: 800;
            color: #007AFF;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.875rem;
            color: #64748b;
            font-weight: 500;
        }

        .contact-methods {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .contact-method {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .contact-method strong {
            color: #1e293b;
            font-weight: 600;
            margin-bottom: 0.25rem;
            display: block;
        }

        .contact-method p {
            color: #64748b;
            margin: 0;
            font-size: 0.95rem;
        }

        .contact-form {
            background: white;
            padding: 2.5rem;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .demo-form h3 {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .form-group {
            margin-bottom: 1.25rem;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 0.875rem 1rem;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            background: #f8fafc;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #007AFF;
            background: white;
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
        }

        .form-note {
            text-align: center;
            font-size: 0.8rem;
            color: #007AFF;
            margin-top: 1rem;
            font-weight: 500;
        }

        /* Footer */
        .footer {
            background: #ffffff;
            color: #1e293b;
            padding: 3rem 0 1rem;
            border-top: 1px solid #e2e8f0;
        }

        .footer-content {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 3rem;
            margin-bottom: 2rem;
        }

        .footer-brand .logo {
            color: #007AFF;
            margin-bottom: 1rem;
        }

        .footer-brand p {
            color: #64748b;
            margin-bottom: 2rem;
        }

        .newsletter-signup {
            margin-bottom: 2rem;
        }

        .newsletter-signup h4 {
            color: #1e293b;
            font-weight: 600;
            margin-bottom: 1rem;
            font-size: 1rem;
        }

        .newsletter-form {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1.5rem;
        }

        .newsletter-form input {
            flex: 1;
            padding: 0.75rem 1rem;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 0.9rem;
            transition: border-color 0.3s ease;
        }

        .newsletter-form input:focus {
            outline: none;
            border-color: #007AFF;
        }

        .btn-newsletter {
            padding: 0.75rem 1.5rem;
            background: #007AFF;
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 0.9rem;
            cursor: pointer;
            transition: background 0.3s ease;
            white-space: nowrap;
        }

        .btn-newsletter:hover {
            background: #0056CC;
        }

        .social-links {
            display: flex;
            gap: 1rem;
        }

        .social-links a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: #f8fafc;
            color: #64748b;
            border-radius: 8px;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .social-links a:hover {
            background: #007AFF;
            color: white;
            transform: translateY(-2px);
        }

        .footer-links {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 2rem;
        }

        .link-group h4 {
            color: #1e293b;
            margin-bottom: 1rem;
            font-weight: 600;
        }

        .link-group a {
            display: block;
            color: #64748b;
            text-decoration: none;
            margin-bottom: 0.5rem;
            transition: color 0.3s ease;
        }

        .link-group a:hover {
            color: #007AFF;
        }

        .footer-bottom {
            border-top: 1px solid #e2e8f0;
            padding-top: 1rem;
            text-align: center;
            color: #64748b;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .mobile-menu-toggle {
                display: flex;
            }

            .hero-container {
                grid-template-columns: 1fr;
                text-align: center;
                gap: 2rem;
                min-height: auto;
                padding: 1rem;
            }

            .hero-content h1 {
                font-size: 2rem;
            }

            .title-word {
                font-size: 3rem !important;
                margin-bottom: 0.5rem;
            }

            .value-proposition {
                font-size: 1.125rem !important;
            }

            .social-proof {
                justify-content: center;
                gap: 1rem;
            }

            .proof-item {
                min-width: 100px;
            }

            .proof-number {
                font-size: 1.5rem;
            }

            .hero-visual {
                height: 50vh;
                min-height: 300px;
                padding: 1rem;
            }

            .main-illustration {
                max-width: 280px;
                max-height: 280px;
            }

            .banner {
                height: 60vh;
                background-attachment: scroll;
            }

            .banner-content h2 {
                font-size: 2rem;
            }

            .banner-content p {
                font-size: 1rem;
            }

            .hero-buttons {
                flex-direction: column;
                align-items: center;
                gap: 0.75rem;
            }

            .features-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .pricing-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .pricing-card.featured {
                transform: scale(1);
            }

            .pricing-card.featured:hover {
                transform: translateY(-4px) scale(1.02);
            }

            .community-plan .pricing-header {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }

            .community-features-grid {
                grid-template-columns: 1fr !important;
                gap: 0.5rem;
            }

            .community-header-premium {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }

            .community-brand {
                justify-content: center;
            }

            .footer-content {
                grid-template-columns: 1fr;
                text-align: center;
            }

            .footer-links {
                grid-template-columns: 1fr;
            }

            .contact-content {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .contact-info h2 {
                font-size: 2rem;
            }

            .contact-stats {
                justify-content: center;
                gap: 1rem;
            }

            .contact-form {
                padding: 1.5rem;
            }
        }

        @media (max-width: 480px) {
            .hero-visual {
                height: 50vh;
                min-height: 300px;
            }

            .main-illustration {
                max-width: 280px;
                max-height: 280px;
            }

            .hero-content h1 {
                font-size: 2rem;
            }

            .title-word {
                font-size: 3.5rem !important; /* Ainda menor em mobile pequeno */
            }

            .banner {
                height: 70vh;
            }

            .banner-content h2 {
                font-size: 2rem;
            }

            .banner-content p {
                font-size: 1rem;
            }

            .banner-cta {
                padding: 0.75rem 1.5rem;
                font-size: 0.875rem;
            }
        }

        /* Community Plan Premium Design */
        .community-card-premium {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid #e2e8f0;
            border-radius: 16px;
            padding: 2rem;
            position: relative;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            height: auto;
            max-height: 280px;
        }

        .community-card-premium::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #007AFF, #0056CC);
            border-radius: 16px 16px 0 0;
        }

        .community-card-premium:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 122, 255, 0.08);
            border-color: #007AFF;
        }

        .community-header-premium {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1.5rem;
        }

        .community-brand {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .amigo-logo-premium {
            flex-shrink: 0;
        }

        .community-title h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1e293b;
            margin: 0 0 0.25rem 0;
            line-height: 1.2;
        }

        .community-subtitle {
            font-size: 0.75rem;
            color: #64748b;
            font-weight: 500;
        }

        .community-badge-premium {
            background: #f1f5f9;
            color: #475569;
            padding: 0.375rem 0.75rem;
            border-radius: 8px;
            font-size: 0.75rem;
            font-weight: 600;
            border: 1px solid #e2e8f0;
        }

        .community-content {
            height: auto;
        }

        .community-description {
            color: #64748b;
            font-size: 0.875rem;
            margin: 0 0 1.25rem 0;
            line-height: 1.4;
        }

        .community-features-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.75rem;
            margin-bottom: 1.5rem;
        }

        .feature-item {
            display: flex;
            align-items: center;
            padding: 0.5rem 0;
        }

        .feature-item::before {
            content: '';
            width: 4px;
            height: 4px;
            background: #007AFF;
            border-radius: 50%;
            margin-right: 0.5rem;
            flex-shrink: 0;
        }

        .feature-label {
            font-size: 0.8rem;
            color: #475569;
            font-weight: 500;
            line-height: 1.3;
        }

        .community-cta-premium {
            width: 100%;
            background: #007AFF;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-size: 0.875rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            font-family: inherit;
        }

        .community-cta-premium:hover {
            background: #0056CC;
            transform: translateY(-1px);
        }

        .community-cta-premium:active {
            transform: translateY(0);
        }

        .google-recognition {
            font-size: 0.95rem;
            color: #64748b;
            font-style: italic;
            margin-top: 1rem;
            margin-bottom: 2rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 1rem 1.25rem;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            border: 1px solid rgba(0, 0, 0, 0.05);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
            backdrop-filter: blur(10px);
            max-width: fit-content;
            transition: all 0.3s ease;
        }

        .google-recognition:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
        }

        .google-icon {
            flex-shrink: 0;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }

        .hero-description {
            font-size: 1.125rem !important;
            color: #475569 !important;
            margin-bottom: 1rem !important;
            font-weight: 500;
            line-height: 1.5;
        }

        .demo-button {
            background: #f1f5f9;
            color: #64748b;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            font-size: 0.875rem;
            transition: all 0.2s ease;
            border: 1px solid #e2e8f0;
        }

        .demo-button:hover {
            background: #e2e8f0;
            color: #475569;
            transform: translateY(-1px);
        }

        /* Amigo Ecosystem Banner */
        .amigo-ecosystem-banner {
            background: #ffffff;
            padding: 5rem 0;
            position: relative;
            overflow: hidden;
            border-top: 1px solid #e2e8f0;
            border-bottom: 1px solid #e2e8f0;
        }

        .ecosystem-background {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            opacity: 0.1;
        }

        .ecosystem-pattern {
            background-image:
                radial-gradient(circle at 25% 25%, rgba(255,255,255,0.2) 2px, transparent 2px),
                radial-gradient(circle at 75% 75%, rgba(255,255,255,0.15) 1px, transparent 1px);
            background-size: 50px 50px, 30px 30px;
            width: 100%;
            height: 100%;
            animation: patternMove 20s linear infinite;
        }

        @keyframes patternMove {
            0% { transform: translate(0, 0); }
            100% { transform: translate(50px, 50px); }
        }

        .ecosystem-content {
            position: relative;
            z-index: 2;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 4rem;
            align-items: center;
        }

        .ecosystem-logos {
            text-align: center;
        }

        .main-logo {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .logo-container {
            background: white;
            border-radius: 50%;
            padding: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .logo-container-small {
            background: white;
            border-radius: 50%;
            padding: 0.25rem;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .logo-text-large {
            font-size: 3rem;
            font-weight: 800;
            color: #1e293b;
            letter-spacing: -0.02em;
        }

        .ecosystem-products {
            display: flex;
            justify-content: center;
            gap: 2rem;
            flex-wrap: wrap;
        }

        .product-logo {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
            color: #1e293b;
            font-weight: 600;
            font-size: 0.875rem;
        }

        .ecosystem-message h2 {
            font-size: 2.25rem;
            font-weight: 800;
            color: #1e293b;
            margin-bottom: 1.5rem;
            line-height: 1.2;
        }

        .ecosystem-description {
            font-size: 1.125rem;
            color: #64748b;
            line-height: 1.6;
            margin-bottom: 2.5rem;
        }

        .ecosystem-description strong {
            color: #1e293b;
            font-weight: 700;
        }

        .ecosystem-ctas {
            display: flex;
            gap: 1rem;
            margin-bottom: 3rem;
            flex-wrap: wrap;
        }

        .btn-ecosystem-primary {
            background: linear-gradient(135deg, #007AFF, #0056CC);
            border: 2px solid #007AFF;
            color: white;
            padding: 1rem 2rem;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            transition: all 0.3s ease;
            white-space: nowrap;
            box-shadow: 0 4px 20px rgba(0, 122, 255, 0.3);
        }

        .btn-ecosystem-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0, 122, 255, 0.4);
        }

        .btn-ecosystem-secondary {
            background: transparent;
            border: 2px solid #007AFF;
            color: #007AFF;
            padding: 1rem 2rem;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .btn-ecosystem-secondary:hover {
            background: #007AFF;
            color: white;
        }

        .ecosystem-stats {
            display: flex;
            gap: 2rem;
            flex-wrap: wrap;
        }

        .ecosystem-stat-item {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }

        .ecosystem-stat-number {
            font-size: 1.75rem;
            font-weight: 800;
            color: #007AFF;
            line-height: 1;
        }

        .ecosystem-stat-label {
            font-size: 0.875rem;
            color: #64748b;
            font-weight: 500;
        }

        /* Blog CTA Section */
        .blog-cta {
            background: linear-gradient(135deg, #64748b 0%, #475569 100%);
            padding: 3rem 0;
            margin: 2rem 0;
        }

        .blog-cta-content {
            max-width: 800px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 2rem;
            padding: 0 2rem;
        }

        .blog-cta-text h3 {
            font-size: 1.75rem;
            font-weight: 700;
            color: white;
            margin-bottom: 0.5rem;
        }

        .blog-cta-text p {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.125rem;
            line-height: 1.5;
        }

        .blog-cta-button {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 1rem 2rem;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .blog-cta-button:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        @media (max-width: 768px) {
            .blog-cta-content {
                flex-direction: column;
                text-align: center;
            }

            .blog-cta-text h3 {
                font-size: 1.5rem;
            }

            .ecosystem-content {
                grid-template-columns: 1fr;
                gap: 2rem;
                text-align: center;
            }

            .ecosystem-message h2 {
                font-size: 1.75rem;
            }

            .ecosystem-ctas {
                justify-content: center;
            }
        }

        /* Community Plan Elegant */
        .community-plan-elegant {
            margin-top: 3rem;
            width: 100%;
            grid-column: 1 / -1;
        }

        .community-card-elegant {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 3px solid #e2e8f0;
            border-radius: 24px;
            padding: 3rem;
            position: relative;
            overflow: hidden;
            transition: all 0.4s ease;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
            display: grid;
            grid-template-columns: auto 1fr auto;
            gap: 3rem;
            align-items: flex-start;
        }

        .community-card-elegant::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, #007AFF, #0056CC, #007AFF);
            background-size: 200% 100%;
            animation: shimmer 3s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% { background-position: 200% 0; }
            50% { background-position: -200% 0; }
        }

        .community-card-elegant:hover {
            transform: translateY(-8px);
            box-shadow: 0 24px 64px rgba(0, 122, 255, 0.25);
            border-color: rgba(0, 122, 255, 0.4);
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        }



        .community-header-elegant {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .community-title-elegant h3 {
            font-size: 2rem;
            font-weight: 800;
            color: #1e293b;
            margin: 0 0 0.5rem 0;
            line-height: 1.1;
            letter-spacing: -0.02em;
        }

        .community-subtitle-elegant {
            font-size: 0.95rem;
            color: #007AFF;
            font-weight: 700;
            background: linear-gradient(135deg, rgba(0, 122, 255, 0.12), rgba(0, 86, 204, 0.18));
            padding: 0.5rem 1rem;
            border-radius: 16px;
            margin-top: 0.5rem;
            display: inline-block;
            border: 1px solid rgba(0, 122, 255, 0.2);
            box-shadow: 0 2px 8px rgba(0, 122, 255, 0.1);
        }

        .community-content-elegant {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .community-description-elegant p {
            color: #64748b;
            font-size: 1rem;
            line-height: 1.6;
            margin: 0;
        }

        .community-features-elegant {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            align-items: flex-start;
        }

        .feature-elegant {
            color: #475569;
            font-size: 0.875rem;
            line-height: 1.4;
            padding-left: 1.25rem;
            position: relative;
            text-align: left;
        }

        .feature-elegant::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0.4rem;
            width: 5px;
            height: 5px;
            background: #007AFF;
            border-radius: 50%;
        }

        .community-cta-elegant {
            display: flex;
            justify-content: center;
        }

        .btn-community-elegant {
            background: linear-gradient(135deg, #007AFF, #0056CC);
            color: white;
            padding: 1rem 2rem;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 0.75rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 20px rgba(0, 122, 255, 0.3);
        }

        .btn-community-elegant:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0, 122, 255, 0.4);
        }

        /* Client Carousel Section */
        .clients-carousel {
            background: #f8fafc;
            padding: 2rem 0;
            border-top: 1px solid #e2e8f0;
            border-bottom: 1px solid #e2e8f0;
            overflow: hidden;
            width: 100vw;
            margin-left: calc(-50vw + 50%);
        }

        .clients-carousel-content {
            text-align: center;
            margin-bottom: 1.5rem;
        }

        .clients-carousel h4 {
            font-size: 0.875rem;
            font-weight: 600;
            color: #64748b;
            margin: 0 0 1rem 0;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .carousel-container {
            position: relative;
            overflow: hidden;
            width: 100%;
        }

        .carousel-track {
            display: flex;
            animation: scroll 30s linear infinite;
            transition: animation-duration 0.3s ease;
        }

        .carousel-track:hover {
            animation-duration: 60s;
        }

        .client-item {
            flex: 0 0 auto;
            margin: 0 2rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
            opacity: 0.7;
            transition: opacity 0.3s ease;
        }

        .client-item:hover {
            opacity: 1;
        }

        .client-logo {
            width: 80px;
            height: 60px;
            object-fit: contain;
            filter: grayscale(100%);
            transition: filter 0.3s ease;
        }

        .client-item:hover .client-logo {
            filter: grayscale(0%);
        }

        .client-name {
            font-size: 0.75rem;
            font-weight: 500;
            color: #64748b;
            text-align: center;
            white-space: nowrap;
        }

        @keyframes scroll {
            0% {
                transform: translateX(0);
            }
            100% {
                transform: translateX(-50%);
            }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .client-item {
                margin: 0 1rem;
            }

            .client-logo {
                width: 60px;
                height: 45px;
            }

            .client-name {
                font-size: 0.7rem;
            }

            .hero-container {
                grid-template-columns: 1fr;
                gap: 3rem;
                text-align: center;
            }

            .title-word {
                font-size: 4rem !important;
            }

            .google-recognition {
                justify-content: center;
                margin: 1rem auto 2rem;
            }

            .hero-buttons {
                justify-content: center;
            }

            .pricing-grid {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .community-card-elegant {
                grid-template-columns: 1fr;
                gap: 2rem;
                text-align: center;
            }

            .community-features-elegant {
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav">
            <div class="logo">
                <img src="assets/amigo-logo.png" alt="Amigo Logo">
                <span class="logo-text">DataHub</span>
            </div>
            <ul class="nav-links">
                <li><a href="#home" class="active">Início</a></li>
                <li><a href="#features">Recursos</a></li>
                <li><a href="#pricing">Planos</a></li>
                <li><a href="blog.html">Blog</a></li>
                <li><a href="#contact">Contato</a></li>
            </ul>
            <div class="mobile-menu-toggle" id="mobileMenuToggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
            <a href="#demo" class="cta-button">Agendar Demo Gratuita</a>
        </nav>
        <div class="mobile-menu" id="mobileMenu">
            <ul class="mobile-nav-links">
                <li><a href="#home">Início</a></li>
                <li><a href="#features">Recursos</a></li>
                <li><a href="#pricing">Planos</a></li>
                <li><a href="blog.html">Blog</a></li>
                <li><a href="#contact">Contato</a></li>
                <li><a href="#demo" class="cta-button">Agendar Demo Gratuita</a></li>
            </ul>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-container">
            <div class="hero-content">
                <div class="product-branding">
                    <h2 class="product-name">Amigo DataHub</h2>
                    <p class="ecosystem-badge">Ecossistema Amigo</p>
                </div>
                <h1 class="hero-title-stacked">
                    <div class="title-word">Inteligência</div>
                    <div class="title-word">Gestão</div>
                    <div class="title-word">Resultados</div>
                </h1>
                <p class="value-proposition">
                    A primeira plataforma de gestão médica com IA proprietária que transforma dados em decisões práticas e lucrativas.
                </p>

                <div class="social-proof">
                    <div class="proof-item">
                        <span class="proof-number">500+</span>
                        <span class="proof-label">Clínicas Ativas</span>
                    </div>
                    <div class="proof-item">
                        <span class="proof-number">+25%</span>
                        <span class="proof-label">Aumento Receita</span>
                    </div>
                    <div class="proof-item">
                        <span class="proof-number">580%</span>
                        <span class="proof-label">ROI Médio</span>
                    </div>
                    <div class="proof-item">
                        <span class="proof-number">15 dias</span>
                        <span class="proof-label">Implementação</span>
                    </div>
                </div>

                <p class="google-recognition">
                    <svg class="google-icon" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="#4285F4"/>
                        <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="#34A853"/>
                        <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="#FBBC05"/>
                        <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="#EA4335"/>
                    </svg>
                    <em>Google Partner oficial - "Excelência em transformação digital na área da saúde"</em>
                </p>
                <div class="hero-buttons">
                    <a href="#demo" class="cta-button">Começar Teste Gratuito</a>
                    <a href="#features" class="btn-secondary">Ver Como Funciona</a>
                </div>
            </div>
            <div class="hero-visual">
                <div class="floating-element square"></div>
                <div class="floating-element rectangle"></div>
                <div class="floating-element circle"></div>
                <div class="floating-element square"></div>
                <div class="floating-element rectangle"></div>
                <div class="main-illustration">
                    <svg id="d3-visualization"></svg>
                </div>
            </div>
        </div>
    </section>

    <!-- Client Carousel Section -->
    <section class="clients-carousel">
        <div class="clients-carousel-content">
            <h4>Confiado por clínicas líderes</h4>
        </div>
        <div class="carousel-container">
            <div class="carousel-track">
                <!-- First set of clients -->
                <div class="client-item">
                    <img src="assets/clientes/botocenter.png" alt="Botocenter" class="client-logo">
                    <span class="client-name">Botocenter</span>
                </div>
                <div class="client-item">
                    <img src="assets/clientes/clinicamundos.png" alt="Clínica Mundos" class="client-logo">
                    <span class="client-name">Clínica Mundos</span>
                </div>
                <div class="client-item">
                    <img src="assets/clientes/ior-recife.png" alt="IOR Recife" class="client-logo">
                    <span class="client-name">IOR Recife</span>
                </div>
                <!-- Duplicate set for seamless loop -->
                <div class="client-item">
                    <img src="assets/clientes/botocenter.png" alt="Botocenter" class="client-logo">
                    <span class="client-name">Botocenter</span>
                </div>
                <div class="client-item">
                    <img src="assets/clientes/clinicamundos.png" alt="Clínica Mundos" class="client-logo">
                    <span class="client-name">Clínica Mundos</span>
                </div>
                <div class="client-item">
                    <img src="assets/clientes/ior-recife.png" alt="IOR Recife" class="client-logo">
                    <span class="client-name">IOR Recife</span>
                </div>
                <!-- Third set for smooth infinite scroll -->
                <div class="client-item">
                    <img src="assets/clientes/botocenter.png" alt="Botocenter" class="client-logo">
                    <span class="client-name">Botocenter</span>
                </div>
                <div class="client-item">
                    <img src="assets/clientes/clinicamundos.png" alt="Clínica Mundos" class="client-logo">
                    <span class="client-name">Clínica Mundos</span>
                </div>
                <div class="client-item">
                    <img src="assets/clientes/ior-recife.png" alt="IOR Recife" class="client-logo">
                    <span class="client-name">IOR Recife</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Value Banners Slider Section -->
    <section class="value-banners">
        <div class="banner-slider">
            <div class="banner-slides" id="bannerSlides">
                <!-- Banner 1: Gestão de Saúde + DataHub -->
                <div class="banner banner-1">
                    <div class="banner-content">
                        <h2>Gestão médica inteligente com dados que geram valor</h2>
                        <p>
                            Conecte todos os dados do seu Amigo Clinic com análises avançadas do DataHub.
                            Transforme consultas, exames e receitas em insights que aumentam sua receita e otimizam operações.
                        </p>
                        <a href="#demo" class="banner-cta">
                            <span>Ver demonstração</span>
                            <span>→</span>
                        </a>
                    </div>
                </div>

                <!-- Banner 2: Amigo Intelligence Connection -->
                <div class="banner banner-2">
                    <div class="banner-content">
                        <h2>Powered by Amigo Intelligence</h2>
                        <p>
                            Nossa IA proprietária analisa padrões em tempo real, prevê tendências e automatiza decisões.
                            O DataHub se conecta perfeitamente com todo ecossistema Amigo para máxima eficiência.
                        </p>
                        <a href="#features" class="banner-cta">
                            <span>Conhecer IA</span>
                            <span>→</span>
                        </a>
                    </div>
                </div>

                <!-- Banner 3: Google Recognition -->
                <div class="banner banner-3">
                    <div class="banner-content">
                        <h2>Reconhecida pelo Google como inovação em HealthTech</h2>
                        <p>
                            A Amigo Tech foi destacada pelo Google como referência em transformação digital na saúde.
                            Essa expertise agora potencializa o DataHub para gerar valor real no seu negócio médico.
                        </p>
                        <a href="#about" class="banner-cta">
                            <span>Saber mais</span>
                            <span>→</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Slider Controls -->
            <div class="slider-controls">
                <div class="slider-dot active" data-slide="0"></div>
                <div class="slider-dot" data-slide="1"></div>
                <div class="slider-dot" data-slide="2"></div>
            </div>

            <!-- Slider Arrows -->
            <div class="slider-arrow prev" id="prevSlide">‹</div>
            <div class="slider-arrow next" id="nextSlide">›</div>

            <!-- Progress Bar -->
            <div class="slider-progress" id="sliderProgress"></div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features">
        <div class="container">
            <div class="section-header">
                <h2>Conheça o Amigo DataHub</h2>
                <p>Tecnologia de ponta para otimizar cada aspecto da sua clínica</p>
            </div>
            <div class="features-grid">
                <div class="feature-card feature-priority">
                    <div class="feature-icon">
                        <svg width="40" height="40" viewBox="0 0 24 24" fill="none">
                            <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="#007AFF"/>
                            <circle cx="12" cy="12" r="3" fill="#0056CC" opacity="0.6"/>
                        </svg>
                    </div>
                    <h3>IA Proprietária Brasileira</h3>
                    <p>Primeira IA desenvolvida especificamente para o contexto médico brasileiro, com análise preditiva que aumenta receita em 25% e reduz custos em 29%.</p>
                    <a href="#demo" class="feature-cta">
                        Ver IA em Ação
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M8.59 16.59L13.17 12L8.59 7.41L10 6L16 12L10 18L8.59 16.59Z"/>
                        </svg>
                    </a>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg width="40" height="40" viewBox="0 0 24 24" fill="none">
                            <rect x="3" y="3" width="18" height="18" rx="2" stroke="#007AFF" stroke-width="2" fill="none"/>
                            <path d="M9 9H15V15H9V9Z" fill="#007AFF" opacity="0.6"/>
                            <path d="M7 7H17V17H7V7Z" stroke="#0056CC" stroke-width="1" fill="none"/>
                        </svg>
                    </div>
                    <h3>Dashboards 360°</h3>
                    <p>Visualizações em tempo real que transformam dados complexos em decisões práticas, com ROI médio de 580% no primeiro ano.</p>
                    <a href="#pricing" class="feature-cta">
                        Explorar Dashboards
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M8.59 16.59L13.17 12L8.59 7.41L10 6L16 12L10 18L8.59 16.59Z"/>
                        </svg>
                    </a>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg width="40" height="40" viewBox="0 0 24 24" fill="none">
                            <path d="M12 2L2 7L12 12L22 7L12 2Z" fill="#007AFF"/>
                            <path d="M2 17L12 22L22 17" stroke="#0056CC" stroke-width="2" fill="none"/>
                            <path d="M2 12L12 17L22 12" stroke="#007AFF" stroke-width="2" fill="none"/>
                        </svg>
                    </div>
                    <h3>Widgets Inteligentes</h3>
                    <p>Componentes que apresentam insights automáticos em tempo real, identificando oportunidades de crescimento e otimização.</p>
                    <a href="#features" class="feature-cta">
                        Ver Widgets
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M8.59 16.59L13.17 12L8.59 7.41L10 6L16 12L10 18L8.59 16.59Z"/>
                        </svg>
                    </a>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg width="40" height="40" viewBox="0 0 24 24" fill="none">
                            <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1Z" fill="#007AFF" opacity="0.8"/>
                            <path d="M9 12L11 14L15 10" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <h3>Segurança LGPD</h3>
                    <p>Conformidade total com LGPD e padrões internacionais de segurança, garantindo proteção máxima dos dados médicos.</p>
                    <a href="#contact" class="feature-cta">
                        Saber Mais
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M8.59 16.59L13.17 12L8.59 7.41L10 6L16 12L10 18L8.59 16.59Z"/>
                        </svg>
                    </a>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg width="40" height="40" viewBox="0 0 24 24" fill="none">
                            <circle cx="12" cy="12" r="10" stroke="#007AFF" stroke-width="2" fill="none"/>
                            <path d="M12 6V12L16 14" stroke="#0056CC" stroke-width="2" stroke-linecap="round"/>
                            <circle cx="12" cy="12" r="2" fill="#007AFF"/>
                        </svg>
                    </div>
                    <h3>Visibilidade Total</h3>
                    <p>Visão completa e unificada de todos os aspectos da clínica, desde operações até resultados financeiros em uma única plataforma.</p>
                    <a href="#demo" class="feature-cta">
                        Ver Demo
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M8.59 16.59L13.17 12L8.59 7.41L10 6L16 12L10 18L8.59 16.59Z"/>
                        </svg>
                    </a>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg width="40" height="40" viewBox="0 0 24 24" fill="none">
                            <path d="M17 21V19C17 17.9 16.1 17 15 17H9C7.9 17 7 17.9 7 19V21" stroke="#007AFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <circle cx="12" cy="7" r="4" stroke="#007AFF" stroke-width="2" fill="#0056CC" opacity="0.6"/>
                            <path d="M22 21V19C22 18.1 21.3 17.4 20.4 17.1" stroke="#007AFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M16 3.1C16.9 3.4 17.6 4.1 17.6 5S16.9 5.6 16 5.9" stroke="#007AFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <h3>Suporte 24/7</h3>
                    <p>Equipe especializada em gestão médica disponível 24/7, com implementação em 15 dias e garantia de ROI.</p>
                    <a href="#contact" class="feature-cta">
                        Falar com Especialista
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M8.59 16.59L13.17 12L8.59 7.41L10 6L16 12L10 18L8.59 16.59Z"/>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </section>



    <!-- Blog CTA Section -->
    <section class="blog-cta">
        <div class="container">
            <div class="blog-cta-content">
                <div class="blog-cta-text">
                    <h3>Acesse nosso DataBlog</h3>
                    <p>Conheça o Amigo DataBlog: nossa plataforma especializada em artigos técnicos, publicações acadêmicas e insights avançados sobre análise de dados médicos e gestão hospitalar</p>
                </div>
                <a href="blog.html" class="blog-cta-button">
                    <span>Acesse nosso DataBlog</span>
                    <span>→</span>
                </a>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="pricing">
        <div class="container">
            <div class="section-header">
                <h2>Planos para cada necessidade</h2>
                <p>Escolha o plano ideal para o tamanho e complexidade da sua operação</p>
            </div>
            <div class="pricing-grid">
                <div class="pricing-card">
                    <div class="pricing-header">
                        <h3 class="plan-name">Analyst</h3>
                        <p class="plan-description">Ideal para equipes dedicadas de análise de dados médicos</p>
                    </div>
                    <div class="price-section">
                        <div class="price-display">
                            <span class="currency">R$</span>
                            <span class="amount">399</span>
                            <span class="period">/mês</span>
                        </div>
                        <p class="price-note">Para insights mais profundos</p>
                    </div>
                    <div class="features-section">
                        <h4 class="features-title">Tudo do Community, mais</h4>
                        <ul class="features-list">
                            <li>Dashboards avançados e personalizáveis</li>
                            <li>Suporte prioritário</li>
                            <li>Visibilidade 360 graus</li>
                            <li>Análise de padrões médicos</li>
                            <li>Insights automáticos</li>
                            <li>Módulos para todas as especialidades</li>
                        </ul>
                    </div>
                    <div style="margin-top: 1rem; padding: 1rem; background: rgba(0, 122, 255, 0.05); border-radius: 12px; border-left: 3px solid #007AFF;">
                        <p style="font-size: 0.8rem; color: #007AFF; font-weight: 600; margin: 0;">✓ 14 dias grátis • ✓ Sem cartão • ✓ Suporte incluído</p>
                    </div>
                    <a href="#demo" class="plan-button btn-secondary">Começar Teste Gratuito</a>
                </div>

                <div class="pricing-card featured">
                    <div class="pricing-header">
                        <h3 class="plan-name">Business</h3>
                        <p class="plan-description">Gerar Valor com Dados no Piloto Automático</p>
                    </div>
                    <div class="price-section">
                        <div class="price-display">
                            <span class="currency">R$</span>
                            <span class="amount">799</span>
                            <span class="period">/mês</span>
                        </div>
                        <p class="price-note">ROI médio de 580% • Mais Popular</p>
                    </div>
                    <div class="features-section">
                        <h4 class="features-title">Tudo do Analyst, mais</h4>
                        <ul class="features-list">
                            <li>Automação completa de análise de dados e geração de insights</li>
                            <li>IA avançada com machine learning</li>
                            <li>Suporte 24/7</li>
                            <li>Consultoria mensal humanizada</li>
                            <li>Análise preditiva avançada</li>
                            <li>Automação de relatórios</li>
                            <li>Workflows inteligentes de acompanhamento mensal</li>
                        </ul>
                    </div>
                    <div style="margin-top: 1rem; padding: 1rem; background: linear-gradient(135deg, rgba(0, 122, 255, 0.1), rgba(0, 86, 204, 0.05)); border-radius: 12px; border: 1px solid rgba(0, 122, 255, 0.2);">
                        <p style="font-size: 0.8rem; color: #007AFF; font-weight: 600; margin: 0;">🚀 Garantia de ROI ou dinheiro de volta em 90 dias</p>
                    </div>
                    <a href="#demo" class="plan-button cta-button cta-primary">Começar Agora - 50% OFF</a>
                </div>

                <div class="pricing-card">
                    <div class="pricing-header">
                        <h3 class="plan-name">Enterprise</h3>
                        <p class="plan-description">Solução customizada para grandes organizações de saúde</p>
                    </div>
                    <div class="price-section">
                        <div class="price-display">
                            <span class="amount" style="font-size: 1.5rem; color: #666;">Preço customizado</span>
                        </div>
                        <p class="price-note">Baseado nas necessidades específicas</p>
                    </div>
                    <div class="features-section">
                        <h4 class="features-title">Tudo do Business, mais</h4>
                        <ul class="features-list">
                            <li>Desenvolvimento customizado</li>
                            <li>ML especializado para casos específicos</li>
                            <li>Gerente de conta dedicado</li>
                            <li>SLA garantido</li>
                            <li>Implementação assistida</li>
                            <li>Suporte white-glove</li>
                            <li>Treinamento completo da equipe</li>
                            <li>Consultoria estratégica</li>
                            <li>Módulos personalizados</li>
                            <li>Compliance específico (HIPAA, LGPD)</li>
                        </ul>
                    </div>
                    <div style="margin-top: 1rem; padding: 1rem; background: rgba(0, 122, 255, 0.05); border-radius: 12px; border-left: 3px solid #007AFF;">
                        <p style="font-size: 0.8rem; color: #007AFF; font-weight: 600; margin: 0;">📞 Consultoria gratuita • 🎯 Proposta em 48h</p>
                    </div>
                    <a href="#contact" class="plan-button btn-secondary">Agendar Consultoria</a>
                </div>
            </div>

            <!-- Community Plan Card -->
            <div class="community-plan-elegant">
                <div class="community-card-elegant">
                    <div class="community-header-elegant">
                        <div class="community-title-elegant">
                            <h3>Community</h3>
                            <span class="community-subtitle-elegant">Gratuito para clientes Amigo</span>
                        </div>
                    </div>

                    <div class="community-content-elegant">
                        <div class="community-description-elegant">
                            <p>Para clínicas iniciando análise de dados médicos com ferramentas profissionais de alta qualidade.</p>
                        </div>

                        <div class="community-features-elegant">
                            <div class="feature-elegant">Dashboard profissional com métricas essenciais</div>
                            <div class="feature-elegant">Relatórios inteligentes automatizados</div>
                            <div class="feature-elegant">Visualizações interativas e insights acionáveis</div>
                        </div>
                    </div>

                    <div class="community-cta-elegant">
                        <a href="#contact" class="btn-community-elegant">
                            Ativar Community
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact-section">
        <div class="container">
            <div class="contact-content">
                <div class="contact-info">
                    <h2>Pronto para transformar sua clínica?</h2>
                    <p>Fale com nossos especialistas e descubra como o Amigo DataHub pode revolucionar sua gestão médica.</p>

                    <div class="contact-stats">
                        <div class="contact-stat">
                            <span class="stat-number">15 dias</span>
                            <span class="stat-label">Implementação</span>
                        </div>
                        <div class="contact-stat">
                            <span class="stat-number">24/7</span>
                            <span class="stat-label">Suporte</span>
                        </div>
                        <div class="contact-stat">
                            <span class="stat-number">580%</span>
                            <span class="stat-label">ROI Médio</span>
                        </div>
                    </div>

                    <div class="contact-methods">
                        <div class="contact-method">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="#007AFF">
                                <path d="M20 4H4C2.9 4 2.01 4.9 2.01 6L2 18C2 19.1 2.9 20 4 20H20C21.1 20 22 19.1 22 18V6C22 4.9 21.1 4 20 4ZM20 8L12 13L4 8V6L12 11L20 6V8Z"/>
                            </svg>
                            <div>
                                <strong>Email</strong>
                                <p><EMAIL></p>
                            </div>
                        </div>
                        <div class="contact-method">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="#007AFF">
                                <path d="M6.62 10.79C8.06 13.62 10.38 15.94 13.21 17.38L15.41 15.18C15.69 14.9 16.08 14.82 16.43 14.93C17.55 15.3 18.75 15.5 20 15.5C20.55 15.5 21 15.95 21 16.5V20C21 20.55 20.55 21 20 21C10.61 21 3 13.39 3 4C3 3.45 3.45 3 4 3H7.5C8.05 3 8.5 3.45 8.5 4C8.5 5.25 8.7 6.45 9.07 7.57C9.18 7.92 9.1 8.31 8.82 8.59L6.62 10.79Z"/>
                            </svg>
                            <div>
                                <strong>Telefone</strong>
                                <p>(11) 99999-9999</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="contact-form">
                    <form class="demo-form">
                        <h3>Agende sua demonstração gratuita</h3>
                        <div class="form-group">
                            <input type="text" placeholder="Nome completo" required>
                        </div>
                        <div class="form-group">
                            <input type="email" placeholder="Email profissional" required>
                        </div>
                        <div class="form-group">
                            <input type="tel" placeholder="Telefone" required>
                        </div>
                        <div class="form-group">
                            <input type="text" placeholder="Nome da clínica" required>
                        </div>
                        <div class="form-group">
                            <select required>
                                <option value="">Tamanho da clínica</option>
                                <option value="pequena">1-5 médicos</option>
                                <option value="media">6-20 médicos</option>
                                <option value="grande">21+ médicos</option>
                            </select>
                        </div>
                        <button type="submit" class="cta-button">
                            Agendar Demo Gratuita
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M8.59 16.59L13.17 12L8.59 7.41L10 6L16 12L10 18L8.59 16.59Z"/>
                            </svg>
                        </button>
                        <p class="form-note">✓ Demo personalizada ✓ Sem compromisso ✓ Resposta em 2h</p>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-brand">
                    <div class="logo">
                        <img src="assets/amigo-logo.png" alt="Amigo Logo">
                        <span class="logo-text">DataHub</span>
                    </div>
                    <p>Transformando dados médicos em decisões inteligentes para o futuro da saúde brasileira.</p>

                    <div class="newsletter-signup">
                        <h4>Receba insights exclusivos</h4>
                        <form class="newsletter-form">
                            <input type="email" placeholder="Seu email profissional" required>
                            <button type="submit" class="btn-newsletter">Inscrever</button>
                        </form>
                    </div>

                    <div class="social-links">
                        <a href="#" aria-label="LinkedIn">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M20.447 20.452H16.893V14.883C16.893 13.555 16.866 11.846 15.041 11.846C13.188 11.846 12.905 13.291 12.905 14.785V20.452H9.351V9H12.765V10.561H12.811C13.288 9.661 14.448 8.711 16.181 8.711C19.782 8.711 20.448 11.081 20.448 14.166V20.452H20.447ZM5.337 7.433C4.193 7.433 3.274 6.507 3.274 5.368C3.274 4.23 4.194 3.305 5.337 3.305C6.477 3.305 7.401 4.23 7.401 5.368C7.401 6.507 6.476 7.433 5.337 7.433ZM7.119 20.452H3.555V9H7.119V20.452ZM22.225 0H1.771C0.792 0 0 0.774 0 1.729V22.271C0 23.227 0.792 24 1.771 24H22.222C23.2 24 24 23.227 24 22.271V1.729C24 0.774 23.2 0 22.222 0H22.225Z"/>
                            </svg>
                        </a>
                        <a href="#" aria-label="Twitter">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M23.953 4.57C23.054 4.95 22.097 5.207 21.097 5.328C22.119 4.724 22.911 3.766 23.285 2.625C22.326 3.188 21.253 3.594 20.122 3.813C19.204 2.863 17.896 2.25 16.461 2.25C13.691 2.25 11.445 4.496 11.445 7.266C11.445 7.64 11.489 8.003 11.574 8.352C7.565 8.148 4.045 6.135 1.64 3.161C1.227 3.884 0.994 4.724 0.994 5.625C0.994 7.334 1.875 8.845 3.183 9.723C2.371 9.697 1.608 9.486 0.934 9.133V9.197C0.934 11.647 2.665 13.681 4.959 14.124C4.559 14.234 4.138 14.293 3.703 14.293C3.394 14.293 3.094 14.263 2.803 14.209C3.411 16.207 5.267 17.652 7.472 17.693C5.754 19.026 3.609 19.818 1.265 19.818C0.881 19.818 0.503 19.795 0.131 19.752C2.364 21.164 5.046 22 7.919 22C16.45 22 21.173 14.379 21.173 7.816C21.173 7.612 21.168 7.408 21.159 7.206C22.123 6.518 22.97 5.65 23.653 4.657L23.953 4.57Z"/>
                            </svg>
                        </a>
                        <a href="#" aria-label="Instagram">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2.163C15.204 2.163 15.584 2.175 16.85 2.233C20.102 2.381 21.621 3.924 21.769 7.152C21.827 8.417 21.838 8.797 21.838 12.001C21.838 15.206 21.826 15.585 21.769 16.85C21.62 20.075 20.105 21.621 16.85 21.769C15.584 21.827 15.206 21.839 12 21.839C8.796 21.839 8.416 21.827 7.151 21.769C3.891 21.62 2.38 20.07 2.232 16.849C2.174 15.584 2.162 15.205 2.162 12C2.162 8.796 2.175 8.417 2.232 7.151C2.381 3.924 3.896 2.38 7.151 2.232C8.417 2.175 8.796 2.163 12 2.163ZM12 0C8.741 0 8.333 0.014 7.053 0.072C2.695 0.272 0.273 2.69 0.073 7.052C0.014 8.333 0 8.741 0 12C0 15.259 0.014 15.668 0.072 16.948C0.272 21.306 2.69 23.728 7.052 23.928C8.333 23.986 8.741 24 12 24C15.259 24 15.668 23.986 16.948 23.928C21.302 23.728 23.73 21.31 23.927 16.948C23.986 15.668 24 15.259 24 12C24 8.741 23.986 8.333 23.928 7.053C23.732 2.699 21.311 0.273 16.949 0.073C15.668 0.014 15.259 0 12 0ZM12 5.838C8.597 5.838 5.838 8.597 5.838 12C5.838 15.403 8.597 18.162 12 18.162C15.403 18.162 18.162 15.403 18.162 12C18.162 8.597 15.403 5.838 12 5.838ZM12 16C9.791 16 8 14.209 8 12C8 9.791 9.791 8 12 8C14.209 8 16 9.791 16 12C16 14.209 14.209 16 12 16ZM18.406 4.155C18.406 5.052 17.68 5.778 16.783 5.778C15.886 5.778 15.16 5.052 15.16 4.155C15.16 3.258 15.886 2.532 16.783 2.532C17.68 2.532 18.406 3.258 18.406 4.155Z"/>
                            </svg>
                        </a>
                    </div>
                </div>
                <div class="footer-links">
                    <div class="link-group">
                        <h4>Produto</h4>
                        <a href="#features">Recursos</a>
                        <a href="#pricing">Planos</a>
                        <a href="blog.html">Blog</a>
                        <a href="#demo">Demonstração</a>
                    </div>
                    <div class="link-group">
                        <h4>Empresa</h4>
                        <a href="#about">Sobre</a>
                        <a href="#contact">Contato</a>
                        <a href="#support">Suporte</a>
                    </div>
                    <div class="link-group">
                        <h4>Legal</h4>
                        <a href="#privacy">Privacidade</a>
                        <a href="#terms">Termos</a>
                        <a href="#security">Segurança</a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Amigo DataHub. Todos os direitos reservados.</p>
            </div>
        </div>
    </footer>

    <script>
        // Super Dynamic Blue Cube Animation
        function createDataVisualization() {
            const svg = d3.select("#d3-visualization");
            const container = svg.node().parentElement;
            const width = Math.min(container.offsetWidth, 800);
            const height = Math.min(container.offsetHeight, 700);
            const centerX = width / 2;
            const centerY = height / 2;

            svg.attr("width", width)
               .attr("height", height)
               .attr("viewBox", `${-width*0.3} ${-height*0.3} ${width*1.6} ${height*1.6}`) // Expanded viewBox to prevent clipping
               .style("overflow", "visible");

            // Clear any existing content
            svg.selectAll("*").remove();

            // Create sophisticated gradients and filters
            const defs = svg.append("defs");

            // Blue cube gradients
            const cubeGradient = defs.append("linearGradient")
                .attr("id", "cubeGradient")
                .attr("x1", "0%").attr("y1", "0%")
                .attr("x2", "100%").attr("y2", "100%");

            cubeGradient.append("stop")
                .attr("offset", "0%")
                .attr("stop-color", "#007AFF")
                .attr("stop-opacity", 1);

            cubeGradient.append("stop")
                .attr("offset", "50%")
                .attr("stop-color", "#0056CC")
                .attr("stop-opacity", 0.9);

            cubeGradient.append("stop")
                .attr("offset", "100%")
                .attr("stop-color", "#003D99")
                .attr("stop-opacity", 0.8);

            // Cube face gradients
            const faceGradient1 = defs.append("linearGradient")
                .attr("id", "faceGradient1")
                .attr("x1", "0%").attr("y1", "0%")
                .attr("x2", "100%").attr("y2", "0%");

            faceGradient1.append("stop")
                .attr("offset", "0%")
                .attr("stop-color", "#007AFF")
                .attr("stop-opacity", 0.9);

            faceGradient1.append("stop")
                .attr("offset", "100%")
                .attr("stop-color", "#0056CC")
                .attr("stop-opacity", 0.7);

            const faceGradient2 = defs.append("linearGradient")
                .attr("id", "faceGradient2")
                .attr("x1", "0%").attr("y1", "0%")
                .attr("x2", "0%").attr("y2", "100%");

            faceGradient2.append("stop")
                .attr("offset", "0%")
                .attr("stop-color", "#0056CC")
                .attr("stop-opacity", 0.8);

            faceGradient2.append("stop")
                .attr("offset", "100%")
                .attr("stop-color", "#003D99")
                .attr("stop-opacity", 0.6);

            // Clean highlight filter - minimal and precise
            const cleanHighlight = defs.append("filter")
                .attr("id", "cleanHighlight")
                .attr("x", "-10%").attr("y", "-10%")
                .attr("width", "120%").attr("height", "120%");

            cleanHighlight.append("feGaussianBlur")
                .attr("stdDeviation", "1")
                .attr("result", "softBlur");

            const cleanMerge = cleanHighlight.append("feMerge");
            cleanMerge.append("feMergeNode").attr("in", "softBlur");
            cleanMerge.append("feMergeNode").attr("in", "SourceGraphic");

            // Main container
            const g = svg.append("g")
                .attr("transform", `translate(${centerX}, ${centerY})`);

            // Cube parameters - scaled to container size (increased by 70%)
            const cubeSize = Math.min(width, height) * 0.6; // Increased from 0.35 to 0.6 (70% increase)
            let rotationX = 0;
            let rotationY = 0;
            let rotationZ = 0;

            // Create Advanced Analytics Cube composed of smaller data cubes
            function create3DDataCube() {
                const cubeGroup = g.append("g").attr("class", "analytics-cube-3d");

                // Parameters for the multi-cube structure
                const gridSize = 4; // 4x4x4 cube of smaller cubes
                const smallCubeSize = cubeSize / (gridSize + 0.3); // Larger individual cube size
                const spacing = smallCubeSize * 0.25; // Much more spacing for better individual definition
                const totalSize = (smallCubeSize + spacing) * gridSize - spacing;

                // Create container for all small cubes
                const smallCubesGroup = cubeGroup.append("g").attr("class", "small-cubes");
                const cubesData = [];

                // Generate 3D grid of small cubes
                for (let x = 0; x < gridSize; x++) {
                    for (let y = 0; y < gridSize; y++) {
                        for (let z = 0; z < gridSize; z++) {
                            const cubeId = `cube_${x}_${y}_${z}`;
                            const posX = -totalSize/2 + x * (smallCubeSize + spacing) + smallCubeSize/2;
                            const posY = -totalSize/2 + y * (smallCubeSize + spacing) + smallCubeSize/2;
                            const posZ = z; // Z-depth for layering

                            // Data categories for different cube types
                            const dataTypes = ['patients', 'consultations', 'exams', 'revenue'];
                            const dataType = dataTypes[(x + y + z) % dataTypes.length];

                            // Blue color scheme with better visibility
                            const hue = 210 + (x * 5) + (y * 3) + (z * 2); // Keep in blue range (210-240)
                            const saturation = 70 + (z * 5); // Higher saturation for better visibility
                            const lightness = 45 + (y * 6) + (z * 3); // Better contrast

                            cubesData.push({
                                id: cubeId,
                                x: x, y: y, z: z,
                                posX: posX, posY: posY, posZ: posZ,
                                size: smallCubeSize,
                                dataType: dataType,
                                color: `hsl(${hue}, ${saturation}%, ${lightness}%)`,
                                visible: true,
                                selected: false,
                                drilled: false,
                                sliced: false
                            });
                        }
                    }
                }

                // Create visual representation of each small cube
                const cubeElements = smallCubesGroup.selectAll(".data-cube")
                    .data(cubesData)
                    .enter()
                    .append("g")
                    .attr("class", "data-cube")
                    .attr("data-cube-id", d => d.id);

                // Draw each small cube with 3D effect
                cubeElements.each(function(d) {
                    const cubeElement = d3.select(this);

                    // Calculate 3D position with isometric projection
                    const isoX = d.posX + d.posZ * smallCubeSize * 0.3;
                    const isoY = d.posY - d.posZ * smallCubeSize * 0.2;

                    cubeElement.attr("transform", `translate(${isoX}, ${isoY})`);

                    // Front face with subtle definition
                    cubeElement.append("rect")
                        .attr("class", "cube-front")
                        .attr("x", -d.size/2)
                        .attr("y", -d.size/2)
                        .attr("width", d.size)
                        .attr("height", d.size)
                        .attr("fill", d.color)
                        .attr("stroke", "rgba(255, 255, 255, 0.3)") // Subtle white border
                        .attr("stroke-width", 0.8) // Thin border
                        .attr("rx", 2);

                    // Top face (isometric) with subtle definition
                    const topOffset = d.size * 0.3;
                    cubeElement.append("polygon")
                        .attr("class", "cube-top")
                        .attr("points", [
                            [-d.size/2, -d.size/2],
                            [d.size/2, -d.size/2],
                            [d.size/2 + topOffset, -d.size/2 - topOffset],
                            [-d.size/2 + topOffset, -d.size/2 - topOffset]
                        ].map(p => p.join(",")).join(" "))
                        .attr("fill", d3.color(d.color).brighter(0.3)) // Subtle brighter top
                        .attr("stroke", "rgba(255, 255, 255, 0.4)") // Subtle border
                        .attr("stroke-width", 0.8);

                    // Right face (isometric) with subtle definition
                    cubeElement.append("polygon")
                        .attr("class", "cube-right")
                        .attr("points", [
                            [d.size/2, -d.size/2],
                            [d.size/2, d.size/2],
                            [d.size/2 + topOffset, d.size/2 - topOffset],
                            [d.size/2 + topOffset, -d.size/2 - topOffset]
                        ].map(p => p.join(",")).join(" "))
                        .attr("fill", d3.color(d.color).darker(0.2)) // Subtle darker side
                        .attr("stroke", "rgba(255, 255, 255, 0.3)") // Subtle border
                        .attr("stroke-width", 0.8);

                    // Remove data indicators - no white dots needed
                });

                return { cubeGroup, smallCubesGroup, cubesData, cubeElements };
            }

            /*
            PLANEJAMENTO DE ANIMAÇÕES OLAP PARA CUBO ANALÍTICO

            1. SLICE - Remove 1 eixo (fixa valor em dimensão)
               Animação: Cubos de uma camada específica se movem para fora e desaparecem

            2. DICE - Reduz todos os eixos (subconjunto em 2+ dimensões)
               Animação: Cubos se reorganizam em padrão menor, outros se afastam

            3. DRILL DOWN - Aumenta granularidade (Ano → Mês)
               Animação: Camada se expande, cubos se subdividem em mais detalhes

            4. ROLL UP - Reduz granularidade (Mês → Ano)
               Animação: Múltiplos cubos se combinam em cubos maiores

            5. PIVOT - Troca 2 eixos (Produto ↔ Região)
               Animação: Cubo gira 90° trocando orientação dos eixos

            6. DRILL THROUGH - Sai dos eixos (dados transacionais)
               Animação: Cubo se "abre" revelando dados internos detalhados

            7. ZOOM IN/OUT - Foco em faixa de valores
               Animação: Cubo se aproxima/afasta com efeito de zoom

            8. FILTERING - Reduz valores sem alterar estrutura
               Animação: Cubos específicos ficam destacados, outros desbotam
            */

            // SYNCHRONIZED CHOREOGRAPHED OPERATIONS

            function performSynchronizedDrillDown(cubesData, cubeElements, targetLayer) {
                // DRILL DOWN: Cascading wave effect with perfect timing
                cubeElements.each(function(d, i) {
                    const cube = d3.select(this);
                    const delay = (d.x + d.y) * 80; // Diagonal wave timing

                    if (d.z === targetLayer) {
                        // Target layer: Synchronized expansion in wave pattern
                        d.drilled = true;
                        const expandedScale = 1.4;
                        const separationDistance = d.size * 0.7;

                        cube.transition()
                            .delay(delay)
                            .duration(800)
                            .ease(d3.easeBackOut)
                            .attr("transform", `translate(${d.posX + d.posZ * d.size * 0.3 + (d.x - 1.5) * separationDistance}, ${d.posY - d.posZ * d.size * 0.2 + (d.y - 1.5) * separationDistance}) scale(${expandedScale})`)
                            .style("opacity", 1)
                            .style("filter", "url(#cleanHighlight)");
                    } else {
                        // Non-target layers: Synchronized fade to invisibility
                        d.drilled = false;
                        cube.transition()
                            .delay(delay * 0.5)
                            .duration(600)
                            .ease(d3.easeQuadIn)
                            .style("opacity", 0);
                    }
                });
            }

            function performChoreographedSlice(cubesData, cubeElements, axis, position) {
                // SLICE: Synchronized separation like synchronized swimming formation
                cubeElements.each(function(d, i) {
                    const cube = d3.select(this);
                    let shouldSlice = false;
                    let moveDirection = { x: 0, y: 0 };
                    let sequenceDelay = 0;

                    switch(axis) {
                        case 'x':
                            shouldSlice = d.x >= position;
                            moveDirection.x = 150;
                            sequenceDelay = d.x * 100; // Sequential by X coordinate
                            break;
                        case 'y':
                            shouldSlice = d.y >= position;
                            moveDirection.y = -150;
                            sequenceDelay = d.y * 100; // Sequential by Y coordinate
                            break;
                        case 'z':
                            shouldSlice = d.z >= position;
                            moveDirection.x = 100;
                            moveDirection.y = -100;
                            sequenceDelay = d.z * 120; // Sequential by Z coordinate
                            break;
                    }

                    if (shouldSlice) {
                        d.sliced = true;
                        // Choreographed exit: cubes leave in perfect sequence
                        cube.transition()
                            .delay(sequenceDelay)
                            .duration(1000)
                            .ease(d3.easeBackIn)
                            .attr("transform", `translate(${d.posX + d.posZ * d.size * 0.3 + moveDirection.x}, ${d.posY - d.posZ * d.size * 0.2 + moveDirection.y}) scale(0.3)`)
                            .style("opacity", 0);
                    } else {
                        d.sliced = false;
                        // Remaining cubes: synchronized highlight wave
                        const highlightDelay = (d.x + d.y + d.z) * 60;
                        cube.transition()
                            .delay(highlightDelay)
                            .duration(800)
                            .ease(d3.easeQuadOut)
                            .style("opacity", 1)
                            .style("filter", "url(#cleanHighlight)");
                    }
                });
            }

            function performAdvancedOLAPChoreography(cubesData, cubeElements, operationType) {
                // ADVANCED OLAP CHOREOGRAPHY: Precise synchronized movements
                cubeElements.each(function(d, i) {
                    const cube = d3.select(this);
                    let targetX = d.posX;
                    let targetY = d.posY;
                    let targetScale = 1;
                    let isVisible = true;
                    let delay = 0;
                    let rotation = 0;

                    switch(operationType) {
                        case 'pivot_rotation':
                            // PIVOT: Synchronized 90-degree rotation in waves
                            delay = (d.x + d.y) * 80;
                            rotation = 90;
                            targetX = d.posY; // Swap coordinates
                            targetY = -d.posX;
                            break;

                        case 'drill_through':
                            // DRILL THROUGH: Cubes dive deep in sequence
                            delay = d.z * 150;
                            targetScale = d.z === 1 ? 1.5 : 0.3;
                            isVisible = d.z <= 2;
                            targetY = d.posY + (d.z * 50);
                            break;

                        case 'roll_up_cascade':
                            // ROLL UP: Cubes merge in cascading groups
                            const groupX = Math.floor(d.x / 2);
                            const groupY = Math.floor(d.y / 2);
                            targetX = groupX * 60 - 30;
                            targetY = groupY * 60 - 30;
                            delay = (groupX + groupY) * 200;
                            targetScale = 0.8;
                            break;

                        case 'dice_scatter':
                            // DICE: Precise scatter then regroup
                            const scatterPhase = i % 2;
                            if (scatterPhase === 0) {
                                targetX = d.posX + (Math.sin(i) * 80);
                                targetY = d.posY + (Math.cos(i) * 80);
                                delay = i * 50;
                            } else {
                                targetX = d.posX * 0.7;
                                targetY = d.posY * 0.7;
                                delay = i * 50 + 600;
                            }
                            break;

                        case 'filter_highlight':
                            // FILTERING: Sequential highlighting like dominoes
                            const filterCondition = (d.x + d.y + d.z) % 3 === 0;
                            delay = (d.x * 100) + (d.y * 80) + (d.z * 60);
                            isVisible = filterCondition;
                            targetScale = filterCondition ? 1.3 : 0.5;
                            break;

                        case 'zoom_focus':
                            // ZOOM: Concentric focus with perfect timing
                            const centerDist = Math.sqrt((d.x - 1.5) ** 2 + (d.y - 1.5) ** 2);
                            delay = centerDist * 120;
                            if (centerDist <= 1) {
                                targetScale = 1.6;
                                targetX = d.posX * 1.2;
                                targetY = d.posY * 1.2;
                            } else {
                                isVisible = false;
                            }
                            break;

                        case 'trend_analysis':
                            // TREND ANALYSIS: Time-based wave motion
                            targetY = d.posY + Math.sin((d.x + d.z) * 0.8) * 40;
                            targetX = d.posX + Math.cos(d.y * 0.6) * 20;
                            delay = d.z * 100;
                            rotation = Math.sin(d.x + d.y) * 15;
                            break;

                        case 'brushing_linking':
                            // BRUSHING & LINKING: Connected movement patterns
                            const linkedGroup = Math.floor((d.x + d.y) / 2);
                            targetX = d.posX + Math.sin(linkedGroup) * 30;
                            targetY = d.posY + Math.cos(linkedGroup) * 30;
                            delay = linkedGroup * 150;
                            targetScale = 1.1;
                            break;
                    }

                    if (isVisible) {
                        cube.transition()
                            .delay(delay)
                            .duration(1000)
                            .ease(d3.easeBackOut)
                            .attr("transform", `translate(${targetX + d.posZ * d.size * 0.3}, ${targetY - d.posZ * d.size * 0.2}) scale(${targetScale}) rotate(${rotation})`)
                            .style("opacity", 1)
                            .style("filter", "url(#cleanHighlight)");
                    } else {
                        cube.transition()
                            .delay(delay * 0.3)
                            .duration(600)
                            .ease(d3.easeQuadIn)
                            .style("opacity", 0);
                    }
                });
            }

            function performPivot(cubesData, cubeElements) {
                // PIVOT: Troca 2 eixos - rotação do cubo inteiro
                cubeElements.each(function(d) {
                    const cube = d3.select(this);

                    // Swap X and Y coordinates for pivot effect
                    const pivotX = d.posY; // Swap X with Y
                    const pivotY = d.posX; // Swap Y with X

                    cube.transition()
                        .duration(1600)
                        .ease(d3.easeBackInOut)
                        .attr("transform", `translate(${pivotX + d.posZ * d.size * 0.3}, ${pivotY - d.posZ * d.size * 0.2}) rotate(90) scale(1.05)`)
                        .style("filter", "url(#subtleGlow)")
                        .transition()
                        .duration(400)
                        .attr("transform", `translate(${pivotX + d.posZ * d.size * 0.3}, ${pivotY - d.posZ * d.size * 0.2}) rotate(0) scale(1)`);
                });
            }

            function performZoomIn(cubesData, cubeElements) {
                // ZOOM IN: Foco em faixa de valores - aproxima cubos centrais
                cubeElements.each(function(d) {
                    const cube = d3.select(this);
                    const isCentral = (d.x >= 1 && d.x <= 2 && d.y >= 1 && d.y <= 2);

                    if (isCentral) {
                        // Central cubes zoom in
                        cube.transition()
                            .duration(1200)
                            .ease(d3.easeQuadOut)
                            .attr("transform", `translate(${d.posX + d.posZ * d.size * 0.3}, ${d.posY - d.posZ * d.size * 0.2}) scale(1.4)`)
                            .style("filter", "url(#focusGlow)")
                            .style("opacity", 1);
                    } else {
                        // Peripheral cubes fade and shrink
                        cube.transition()
                            .duration(1200)
                            .ease(d3.easeQuadIn)
                            .attr("transform", `translate(${d.posX + d.posZ * d.size * 0.3}, ${d.posY - d.posZ * d.size * 0.2}) scale(0.7)`)
                            .style("opacity", 0.25);
                    }
                });
            }

            function performRollUp(cubesData, cubeElements) {
                // ROLL UP: Reduz granularidade - agrupa cubos
                cubeElements.each(function(d) {
                    const cube = d3.select(this);

                    // Group cubes by quadrants
                    const quadrantX = Math.floor(d.x / 2);
                    const quadrantY = Math.floor(d.y / 2);
                    const centerX = (quadrantX * 2 + 0.5) * (d.size + d.size * 0.25) - (d.size + d.size * 0.25) * 2;
                    const centerY = (quadrantY * 2 + 0.5) * (d.size + d.size * 0.25) - (d.size + d.size * 0.25) * 2;

                    cube.transition()
                        .duration(1300)
                        .ease(d3.easeCircleInOut)
                        .attr("transform", `translate(${centerX + d.posZ * d.size * 0.3}, ${centerY - d.posZ * d.size * 0.2}) scale(0.9)`)
                        .style("filter", "url(#subtleGlow)")
                        .style("opacity", 0.8);
                });
            }

            function performSynchronizedReset(cubesData, cubeElements) {
                // SYNCHRONIZED RESET: Choreographed return to original positions
                cubeElements.each(function(d, i) {
                    const cube = d3.select(this);
                    d.drilled = false;
                    d.sliced = false;
                    d.selected = false;

                    // Cascading reset from center outward
                    const centerDistance = Math.sqrt((d.x - 1.5) ** 2 + (d.y - 1.5) ** 2 + (d.z - 1.5) ** 2);
                    const resetDelay = centerDistance * 80;

                    cube.transition()
                        .delay(resetDelay)
                        .duration(1000)
                        .ease(d3.easeElasticOut.amplitude(1).period(0.3))
                        .attr("transform", `translate(${d.posX + d.posZ * d.size * 0.3}, ${d.posY - d.posZ * d.size * 0.2}) scale(1)`)
                        .style("opacity", 1)
                        .style("filter", "none");
                });
            }

            // Create data streams
            function createDataStreams() {
                const streamsGroup = g.append("g").attr("class", "data-streams");
                const streams = [];

                // Create 4 data streams from corners
                for (let i = 0; i < 4; i++) {
                    const angle = (i / 4) * Math.PI * 2;
                    const startX = Math.cos(angle) * cubeSize * 2;
                    const startY = Math.sin(angle) * cubeSize * 2;

                    const stream = streamsGroup.append("line")
                        .attr("x1", startX)
                        .attr("y1", startY)
                        .attr("x2", 0)
                        .attr("y2", 0)
                        .attr("stroke", "url(#faceGradient1)")
                        .attr("stroke-width", 2)
                        .attr("opacity", 0.6)
                        .attr("stroke-dasharray", "5,5");

                    streams.push(stream);
                }

                return { streamsGroup, streams };
            }

            // Initialize 3D Data Cube
            const { cubeGroup, smallCubesGroup, cubesData, cubeElements } = create3DDataCube();
            const { streamsGroup, streams } = createDataStreams();

            // Advanced Analytics Cube Animation with Real Operations
            function startDataAnalysisAnimation() {
                let time = 0;
                let operationPhase = -1; // Initialize to -1 to trigger first operation
                const animationDuration = 36; // 36 seconds total cycle for 12 detailed OLAP operations

                function animate() {
                    time += 0.02; // Slower speed for longer animation

                    // Enhanced 3D rotation on multiple axes (slower)
                    rotationX += 0.3;
                    rotationY += 0.25;
                    rotationZ += 0.2;

                    // Smooth loop transition - normalize time for seamless loop
                    const normalizedTime = (time % animationDuration) / animationDuration;
                    const smoothTime = normalizedTime * Math.PI * 2;

                    // Apply complex 3D rotation with smooth transitions
                    const scaleEffect = 1 + Math.sin(smoothTime * 0.6) * 0.1;
                    const skewXEffect = Math.sin(smoothTime * 0.8) * 3;
                    const skewYEffect = Math.cos(smoothTime * 0.6) * 2;

                    // Main cube rotation
                    cubeGroup.attr("transform", `
                        rotate(${rotationY + Math.sin(smoothTime * 0.5) * 15})
                        skewX(${skewXEffect})
                        skewY(${skewYEffect})
                        scale(${scaleEffect})
                    `);

                    // Execute ADVANCED OLAP CHOREOGRAPHED Operations
                    const operationCycle = Math.floor(normalizedTime * 12); // 12 operations per cycle for more variety
                    const currentOperation = operationCycle % 12;

                    // Trigger operations at specific intervals
                    if (operationCycle !== operationPhase) {
                        operationPhase = operationCycle;

                        switch(currentOperation) {
                            case 0: // Synchronized reset - cascading return
                                performSynchronizedReset(cubesData, cubeElements);
                                break;
                            case 1: // Drill down with wave effect
                                performSynchronizedDrillDown(cubesData, cubeElements, 0);
                                break;
                            case 2: // PIVOT rotation choreography
                                performAdvancedOLAPChoreography(cubesData, cubeElements, 'pivot_rotation');
                                break;
                            case 3: // Choreographed slice X
                                performChoreographedSlice(cubesData, cubeElements, 'x', 2);
                                break;
                            case 4: // DRILL THROUGH deep dive
                                performAdvancedOLAPChoreography(cubesData, cubeElements, 'drill_through');
                                break;
                            case 5: // FILTER highlighting dominoes
                                performAdvancedOLAPChoreography(cubesData, cubeElements, 'filter_highlight');
                                break;
                            case 6: // ROLL UP cascading merge
                                performAdvancedOLAPChoreography(cubesData, cubeElements, 'roll_up_cascade');
                                break;
                            case 7: // ZOOM focus concentric
                                performAdvancedOLAPChoreography(cubesData, cubeElements, 'zoom_focus');
                                break;
                            case 8: // DICE scatter and regroup
                                performAdvancedOLAPChoreography(cubesData, cubeElements, 'dice_scatter');
                                break;
                            case 9: // TREND ANALYSIS wave motion
                                performAdvancedOLAPChoreography(cubesData, cubeElements, 'trend_analysis');
                                break;
                            case 10: // BRUSHING & LINKING connected patterns
                                performAdvancedOLAPChoreography(cubesData, cubeElements, 'brushing_linking');
                                break;
                            case 11: // Final drill down
                                performSynchronizedDrillDown(cubesData, cubeElements, 2);
                                break;
                        }
                    }

                    // NO individual animations during choreographed sequences
                    // Only the main cube rotation continues for context

                    // Animate data streams (representing data flow)
                    streams.forEach((stream, i) => {
                        const opacity = 0.4 + Math.sin(time * 2 + i * 0.8) * 0.3;
                        const strokeWidth = 2 + Math.sin(time * 3 + i) * 1;
                        stream.attr("opacity", opacity)
                              .attr("stroke-width", strokeWidth);
                    });

                    // Smooth loop reset - ensure seamless transition at cycle end
                    if (normalizedTime > 0.95) {
                        // Prepare for smooth loop restart
                        const resetTransition = (normalizedTime - 0.95) / 0.05; // 0 to 1 over last 5%
                        const smoothReset = d3.easeQuadInOut(resetTransition);

                        // Gradually return to starting position for seamless loop
                        const resetRotationY = rotationY * (1 - smoothReset * 0.1);
                        const resetScale = scaleEffect * (1 - smoothReset * 0.05);

                        cubeGroup.attr("transform", `
                            rotate(${resetRotationY})
                            skewX(${skewXEffect * (1 - smoothReset * 0.3)})
                            skewY(${skewYEffect * (1 - smoothReset * 0.3)})
                            scale(${resetScale})
                        `);
                    }

                    requestAnimationFrame(animate);
                }

                animate();
            }

            // Initialize the 3D data analysis animation
            startDataAnalysisAnimation();
        }

        // Banner Slider Controller
        class BannerSlider {
            constructor() {
                this.currentSlide = 0;
                this.totalSlides = 3;
                this.autoPlayInterval = 8000; // 8 seconds
                this.autoPlayTimer = null;
                this.progressTimer = null;

                this.slides = document.getElementById('bannerSlides');
                this.dots = document.querySelectorAll('.slider-dot');
                this.prevBtn = document.getElementById('prevSlide');
                this.nextBtn = document.getElementById('nextSlide');
                this.progressBar = document.getElementById('sliderProgress');

                this.init();
            }

            init() {
                // Add event listeners
                this.prevBtn.addEventListener('click', () => this.prevSlide());
                this.nextBtn.addEventListener('click', () => this.nextSlide());

                this.dots.forEach((dot, index) => {
                    dot.addEventListener('click', () => this.goToSlide(index));
                });

                // Start auto-play
                this.startAutoPlay();

                // Pause on hover
                const slider = document.querySelector('.banner-slider');
                slider.addEventListener('mouseenter', () => this.pauseAutoPlay());
                slider.addEventListener('mouseleave', () => this.startAutoPlay());
            }

            goToSlide(slideIndex) {
                this.currentSlide = slideIndex;
                this.updateSlider();
                this.resetAutoPlay();
            }

            nextSlide() {
                this.currentSlide = (this.currentSlide + 1) % this.totalSlides;
                this.updateSlider();
                this.resetAutoPlay();
            }

            prevSlide() {
                this.currentSlide = (this.currentSlide - 1 + this.totalSlides) % this.totalSlides;
                this.updateSlider();
                this.resetAutoPlay();
            }

            updateSlider() {
                // Move slides
                const translateX = -this.currentSlide * (100 / this.totalSlides);
                this.slides.style.transform = `translateX(${translateX}%)`;

                // Update dots
                this.dots.forEach((dot, index) => {
                    dot.classList.toggle('active', index === this.currentSlide);
                });
            }

            startAutoPlay() {
                this.pauseAutoPlay(); // Clear any existing timer

                this.autoPlayTimer = setInterval(() => {
                    this.nextSlide();
                }, this.autoPlayInterval);

                this.startProgressBar();
            }

            pauseAutoPlay() {
                if (this.autoPlayTimer) {
                    clearInterval(this.autoPlayTimer);
                    this.autoPlayTimer = null;
                }

                if (this.progressTimer) {
                    clearInterval(this.progressTimer);
                    this.progressTimer = null;
                }

                this.progressBar.style.width = '0%';
            }

            resetAutoPlay() {
                this.startAutoPlay();
            }

            startProgressBar() {
                let progress = 0;
                const increment = 100 / (this.autoPlayInterval / 100);

                this.progressBar.style.width = '0%';

                this.progressTimer = setInterval(() => {
                    progress += increment;
                    this.progressBar.style.width = `${Math.min(progress, 100)}%`;

                    if (progress >= 100) {
                        clearInterval(this.progressTimer);
                    }
                }, 100);
            }
        }

        // Mobile Menu Functionality
        function initMobileMenu() {
            const mobileMenuToggle = document.getElementById('mobileMenuToggle');
            const mobileMenu = document.getElementById('mobileMenu');

            if (mobileMenuToggle && mobileMenu) {
                mobileMenuToggle.addEventListener('click', function() {
                    mobileMenuToggle.classList.toggle('active');
                    mobileMenu.classList.toggle('active');
                });

                // Close menu when clicking on links
                const mobileLinks = mobileMenu.querySelectorAll('a');
                mobileLinks.forEach(link => {
                    link.addEventListener('click', function() {
                        mobileMenuToggle.classList.remove('active');
                        mobileMenu.classList.remove('active');
                    });
                });

                // Close menu when clicking outside
                document.addEventListener('click', function(event) {
                    if (!mobileMenuToggle.contains(event.target) && !mobileMenu.contains(event.target)) {
                        mobileMenuToggle.classList.remove('active');
                        mobileMenu.classList.remove('active');
                    }
                });
            }
        }

        // Enhanced Banner Slider with pause on hover
        class EnhancedBannerSlider {
            constructor() {
                this.currentSlide = 0;
                this.slides = document.querySelectorAll('.banner');
                this.totalSlides = this.slides.length;
                this.slidesContainer = document.querySelector('.banner-slides');
                this.dots = document.querySelectorAll('.slider-dot');
                this.progressBar = document.querySelector('.slider-progress');
                this.autoSlideInterval = null;
                this.progressInterval = null;
                this.slideInterval = 8000; // 8 seconds
                this.isPaused = false;

                this.init();
            }

            init() {
                this.setupEventListeners();
                this.startAutoSlide();
                this.updateProgress();
            }

            setupEventListeners() {
                // Dot navigation
                this.dots.forEach((dot, index) => {
                    dot.addEventListener('click', () => {
                        this.goToSlide(index);
                    });
                });

                // Arrow navigation
                const prevArrow = document.querySelector('.slider-arrow.prev');
                const nextArrow = document.querySelector('.slider-arrow.next');

                if (prevArrow) {
                    prevArrow.addEventListener('click', () => {
                        this.previousSlide();
                    });
                }

                if (nextArrow) {
                    nextArrow.addEventListener('click', () => {
                        this.nextSlide();
                    });
                }

                // Pause on hover
                const bannerSlider = document.querySelector('.banner-slider');
                if (bannerSlider) {
                    bannerSlider.addEventListener('mouseenter', () => {
                        this.pauseSlider();
                    });

                    bannerSlider.addEventListener('mouseleave', () => {
                        this.resumeSlider();
                    });
                }
            }

            goToSlide(index) {
                this.currentSlide = index;
                this.updateSlider();
                this.resetAutoSlide();
            }

            nextSlide() {
                this.currentSlide = (this.currentSlide + 1) % this.totalSlides;
                this.updateSlider();
                this.resetAutoSlide();
            }

            previousSlide() {
                this.currentSlide = (this.currentSlide - 1 + this.totalSlides) % this.totalSlides;
                this.updateSlider();
                this.resetAutoSlide();
            }

            updateSlider() {
                const translateX = -this.currentSlide * (100 / this.totalSlides);
                this.slidesContainer.style.transform = `translateX(${translateX}%)`;

                // Update dots
                this.dots.forEach((dot, index) => {
                    dot.classList.toggle('active', index === this.currentSlide);
                });

                this.updateProgress();
            }

            startAutoSlide() {
                this.autoSlideInterval = setInterval(() => {
                    if (!this.isPaused) {
                        this.nextSlide();
                    }
                }, this.slideInterval);
            }

            resetAutoSlide() {
                clearInterval(this.autoSlideInterval);
                clearInterval(this.progressInterval);
                this.startAutoSlide();
                this.updateProgress();
            }

            pauseSlider() {
                this.isPaused = true;
                clearInterval(this.progressInterval);
            }

            resumeSlider() {
                this.isPaused = false;
                this.updateProgress();
            }

            updateProgress() {
                if (this.progressBar) {
                    this.progressBar.style.width = '0%';

                    let progress = 0;
                    this.progressInterval = setInterval(() => {
                        if (!this.isPaused) {
                            progress += 100 / (this.slideInterval / 100);
                            this.progressBar.style.width = `${Math.min(progress, 100)}%`;

                            if (progress >= 100) {
                                clearInterval(this.progressInterval);
                            }
                        }
                    }, 100);
                }
            }
        }

        // Initialize everything when page loads
        document.addEventListener('DOMContentLoaded', function() {
            createDataVisualization();
            initMobileMenu();
            new EnhancedBannerSlider();
        });
    </script>
</body>
</html>

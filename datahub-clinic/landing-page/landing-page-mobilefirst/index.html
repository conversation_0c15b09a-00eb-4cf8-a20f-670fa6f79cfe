<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Amigo DataHub - Gestão Médica Inteligente</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    
    <style>
        /* Mobile-First CSS Reset */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: #1a1a1a;
            overflow-x: hidden;
            background: #ffffff;
        }

        .container {
            max-width: 100%;
            margin: 0 auto;
            padding: 0 1rem;
        }

        /* Mobile Header */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 122, 255, 0.1);
            z-index: 1000;
            padding: 0.75rem 0;
        }

        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 1rem;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 700;
            color: #007AFF;
            text-decoration: none;
        }

        .logo img {
            width: 32px;
            height: 32px;
            border-radius: 6px;
        }

        .logo-text {
            font-size: 1.1rem;
            font-weight: 700;
        }

        .mobile-menu-toggle {
            display: flex;
            flex-direction: column;
            cursor: pointer;
            padding: 0.5rem;
            gap: 4px;
            background: none;
            border: none;
        }

        .mobile-menu-toggle span {
            width: 24px;
            height: 2px;
            background: #1a1a1a;
            transition: all 0.3s ease;
            border-radius: 1px;
        }

        .mobile-menu-toggle.active span:nth-child(1) {
            transform: rotate(45deg) translate(5px, 5px);
        }

        .mobile-menu-toggle.active span:nth-child(2) {
            opacity: 0;
        }

        .mobile-menu-toggle.active span:nth-child(3) {
            transform: rotate(-45deg) translate(7px, -6px);
        }

        .mobile-menu {
            display: none;
            position: fixed;
            top: 70px;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 122, 255, 0.1);
            z-index: 999;
            padding: 1rem 0;
        }

        .mobile-menu.active {
            display: block;
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .mobile-nav-links {
            display: flex;
            flex-direction: column;
            list-style: none;
            padding: 0 1rem;
            gap: 0.5rem;
        }

        .mobile-nav-links a {
            text-decoration: none;
            color: #1a1a1a;
            font-weight: 500;
            padding: 1rem;
            border-radius: 12px;
            transition: all 0.3s ease;
            text-align: center;
        }

        .mobile-nav-links a:hover,
        .mobile-nav-links a.active {
            background: rgba(0, 122, 255, 0.1);
            color: #007AFF;
        }

        /* Mobile Hero Section */
        .hero {
            padding: 100px 0 3rem;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }

        .hero-container {
            text-align: center;
            width: 100%;
        }

        .hero-visual {
            margin-bottom: 2rem;
            height: 250px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .main-illustration {
            width: 200px;
            height: 200px;
        }

        .title-word {
            font-size: 2.5rem;
            font-weight: 900;
            line-height: 0.9;
            margin-bottom: 0.5rem;
            color: #007AFF;
            text-shadow: 2px 2px 4px rgba(0, 122, 255, 0.1);
            letter-spacing: -0.03em;
            display: block;
        }

        .title-word:nth-child(1) { color: #007AFF; }
        .title-word:nth-child(2) { color: #0056CC; }
        .title-word:nth-child(3) { color: #003D99; }

        .product-branding {
            margin-bottom: 1rem;
            text-align: center;
        }

        .product-name {
            font-size: 1.25rem;
            font-weight: 600;
            color: #007AFF;
            margin-bottom: 0.25rem;
            letter-spacing: -0.01em;
        }

        .ecosystem-badge {
            font-size: 0.7rem;
            color: #64748b;
            font-weight: 500;
            margin: 0;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .value-proposition {
            font-size: 1.125rem;
            color: #1e293b;
            font-weight: 600;
            margin: 1.5rem 0;
            line-height: 1.4;
        }

        .social-proof {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            margin: 2rem 0;
        }

        .proof-item {
            text-align: center;
            padding: 1rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .proof-number {
            display: block;
            font-size: 1.5rem;
            font-weight: 800;
            color: #007AFF;
            margin-bottom: 0.25rem;
        }

        .proof-label {
            font-size: 0.8rem;
            color: #64748b;
            font-weight: 500;
        }

        .google-recognition {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            margin: 1.5rem 0;
            padding: 1rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .google-recognition em {
            font-size: 0.9rem;
            color: #64748b;
            font-style: normal;
            font-weight: 500;
        }

        .hero-buttons {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            margin-top: 2rem;
        }

        .cta-button {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 1rem 2rem;
            background: linear-gradient(135deg, #007AFF, #0056CC);
            color: white;
            text-decoration: none;
            border-radius: 12px;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 16px rgba(0, 122, 255, 0.3);
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 122, 255, 0.4);
        }

        .btn-secondary {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 1rem 2rem;
            background: white;
            color: #007AFF;
            text-decoration: none;
            border: 2px solid #007AFF;
            border-radius: 12px;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: #007AFF;
            color: white;
        }

        /* Mobile Sections */
        .features, .pricing, .contact {
            padding: 3rem 0;
        }

        .features {
            background: #ffffff;
        }

        .pricing {
            background: #f8fafc;
        }

        .contact {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        .section-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .section-header h2 {
            font-size: 2rem;
            font-weight: 800;
            color: #1e293b;
            margin-bottom: 0.5rem;
        }

        .section-header p {
            color: #64748b;
            font-size: 1rem;
        }

        /* Mobile Features */
        .features-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }

        .feature-card {
            background: white;
            padding: 1.5rem;
            border-radius: 16px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
        }

        .feature-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
        }

        .feature-card.priority::after {
            content: 'Mais Usado';
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: linear-gradient(135deg, #007AFF, #0056CC);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .feature-icon {
            margin-bottom: 1rem;
        }

        .feature-card h3 {
            font-size: 1.25rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 0.75rem;
        }

        .feature-card p {
            color: #64748b;
            line-height: 1.6;
            margin-bottom: 1rem;
            font-size: 0.95rem;
        }

        .feature-cta {
            color: #007AFF;
            text-decoration: none;
            font-weight: 600;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }

        .feature-cta:hover {
            color: #0056CC;
        }

        /* Mobile Pricing */
        .pricing-cards {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }

        .pricing-card {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
            position: relative;
            transition: all 0.3s ease;
        }

        .pricing-card.featured {
            border: 2px solid #007AFF;
            transform: scale(1.02);
        }

        .popular-badge {
            position: absolute;
            top: -12px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, #007AFF, #0056CC);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .plan-header {
            text-align: center;
            margin-bottom: 1.5rem;
        }

        .plan-header h3 {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 0.5rem;
        }

        .price {
            margin: 1rem 0;
        }

        .currency {
            font-size: 1rem;
            color: #64748b;
        }

        .amount {
            font-size: 2.5rem;
            font-weight: 800;
            color: #007AFF;
        }

        .amount.custom {
            font-size: 1.25rem;
            color: #64748b;
        }

        .period {
            font-size: 1rem;
            color: #64748b;
        }

        .plan-description {
            color: #64748b;
            font-size: 0.9rem;
        }

        .plan-features {
            margin-bottom: 1.5rem;
        }

        .feature-item {
            padding: 0.5rem 0;
            color: #1e293b;
            font-size: 0.9rem;
        }

        .plan-guarantee {
            background: rgba(0, 122, 255, 0.05);
            padding: 1rem;
            border-radius: 12px;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .plan-guarantee.featured {
            background: linear-gradient(135deg, rgba(0, 122, 255, 0.1), rgba(0, 86, 204, 0.05));
            border: 1px solid rgba(0, 122, 255, 0.2);
        }

        .plan-guarantee p {
            font-size: 0.8rem;
            color: #007AFF;
            font-weight: 600;
            margin: 0;
        }

        .plan-button {
            display: block;
            width: 100%;
            padding: 1rem;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 600;
            text-align: center;
            transition: all 0.3s ease;
            border: none;
            font-size: 1rem;
        }

        .plan-button.primary {
            background: linear-gradient(135deg, #007AFF, #0056CC);
            color: white;
        }

        .plan-button.secondary {
            background: white;
            color: #007AFF;
            border: 2px solid #007AFF;
        }

        .plan-button:hover {
            transform: translateY(-2px);
        }

        /* Mobile Contact */
        .contact-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .contact-header h2 {
            font-size: 2rem;
            font-weight: 800;
            color: #1e293b;
            margin-bottom: 1rem;
        }

        .contact-header p {
            color: #64748b;
            line-height: 1.6;
        }

        .contact-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-item {
            text-align: center;
            padding: 1rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .stat-number {
            display: block;
            font-size: 1.25rem;
            font-weight: 800;
            color: #007AFF;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.75rem;
            color: #64748b;
            font-weight: 500;
        }

        .contact-form {
            background: white;
            padding: 2rem;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }

        .contact-form h3 {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f8fafc;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #007AFF;
            background: white;
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
        }

        .form-note {
            text-align: center;
            font-size: 0.8rem;
            color: #007AFF;
            margin-top: 1rem;
            font-weight: 500;
        }

        .contact-methods {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .contact-method {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .contact-method strong {
            color: #1e293b;
            font-weight: 600;
            margin-bottom: 0.25rem;
            display: block;
        }

        .contact-method p {
            color: #64748b;
            margin: 0;
            font-size: 0.9rem;
        }

        /* Mobile Footer */
        .footer {
            background: #ffffff;
            color: #1e293b;
            padding: 2rem 0 1rem;
            border-top: 1px solid #e2e8f0;
        }

        .footer-brand {
            text-align: center;
            margin-bottom: 2rem;
        }

        .footer-brand .logo {
            justify-content: center;
            margin-bottom: 1rem;
        }

        .footer-brand p {
            color: #64748b;
            font-size: 0.9rem;
            line-height: 1.6;
        }

        .footer-links {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .link-group h4 {
            color: #1e293b;
            margin-bottom: 1rem;
            font-weight: 600;
            font-size: 1rem;
        }

        .link-group a {
            display: block;
            color: #64748b;
            text-decoration: none;
            margin-bottom: 0.75rem;
            transition: color 0.3s ease;
            font-size: 0.9rem;
        }

        .link-group a:hover {
            color: #007AFF;
        }

        .newsletter {
            text-align: center;
            margin-bottom: 2rem;
        }

        .newsletter h4 {
            color: #1e293b;
            font-weight: 600;
            margin-bottom: 1rem;
            font-size: 1rem;
        }

        .newsletter-form {
            display: flex;
            gap: 0.5rem;
            max-width: 300px;
            margin: 0 auto;
        }

        .newsletter-form input {
            flex: 1;
            padding: 0.75rem;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 0.9rem;
        }

        .newsletter-form button {
            padding: 0.75rem 1rem;
            background: #007AFF;
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 0.9rem;
            cursor: pointer;
            white-space: nowrap;
        }

        .social-links {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .social-links a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: #f8fafc;
            color: #64748b;
            border-radius: 8px;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .social-links a:hover {
            background: #007AFF;
            color: white;
            transform: translateY(-2px);
        }

        .footer-bottom {
            text-align: center;
            padding-top: 1rem;
            border-top: 1px solid #e2e8f0;
            color: #64748b;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <!-- Mobile Header -->
    <header class="header">
        <nav class="nav">
            <a href="#home" class="logo">
                <img src="assets/amigo-logo.png" alt="Amigo Logo">
                <span class="logo-text">DataHub</span>
            </a>
            <button class="mobile-menu-toggle" id="mobileMenuToggle">
                <span></span>
                <span></span>
                <span></span>
            </button>
        </nav>
        <div class="mobile-menu" id="mobileMenu">
            <ul class="mobile-nav-links">
                <li><a href="#home" class="active">Início</a></li>
                <li><a href="#features">Recursos</a></li>
                <li><a href="#pricing">Planos</a></li>
                <li><a href="#contact">Contato</a></li>
                <li><a href="#demo" class="cta-button">Demo Gratuita</a></li>
            </ul>
        </div>
    </header>

    <!-- Mobile Hero Section -->
    <section id="home" class="hero">
        <div class="container">
            <div class="hero-container">
                <div class="hero-visual">
                    <svg class="main-illustration" id="d3-visualization" viewBox="0 0 200 200"></svg>
                </div>

                <div class="product-branding">
                    <h2 class="product-name">Amigo DataHub</h2>
                    <p class="ecosystem-badge">Ecossistema Amigo</p>
                </div>

                <h1 class="hero-title-stacked">
                    <div class="title-word">Inteligência</div>
                    <div class="title-word">Gestão</div>
                    <div class="title-word">Resultados</div>
                </h1>
                
                <p class="value-proposition">
                    A primeira plataforma de gestão médica com IA proprietária que transforma dados em decisões práticas e lucrativas.
                </p>
                
                <div class="social-proof">
                    <div class="proof-item">
                        <span class="proof-number">500+</span>
                        <span class="proof-label">Clínicas Ativas</span>
                    </div>
                    <div class="proof-item">
                        <span class="proof-number">+25%</span>
                        <span class="proof-label">Aumento Receita</span>
                    </div>
                    <div class="proof-item">
                        <span class="proof-number">580%</span>
                        <span class="proof-label">ROI Médio</span>
                    </div>
                    <div class="proof-item">
                        <span class="proof-number">15 dias</span>
                        <span class="proof-label">Implementação</span>
                    </div>
                </div>

                <div class="google-recognition">
                    <svg class="google-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="#4285F4"/>
                        <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="#34A853"/>
                        <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="#FBBC05"/>
                        <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="#EA4335"/>
                    </svg>
                    <em>Google Partner oficial - "Excelência em transformação digital na área da saúde"</em>
                </div>
                
                <div class="hero-buttons">
                    <a href="#demo" class="cta-button">
                        Começar Teste Gratuito
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M8.59 16.59L13.17 12L8.59 7.41L10 6L16 12L10 18L8.59 16.59Z"/>
                        </svg>
                    </a>
                    <a href="#features" class="btn-secondary">
                        Ver Como Funciona
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M8.59 16.59L13.17 12L8.59 7.41L10 6L16 12L10 18L8.59 16.59Z"/>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Mobile Features Section -->
    <section id="features" class="features">
        <div class="container">
            <div class="section-header">
                <h2>Recursos Principais</h2>
                <p>Tecnologia avançada para gestão médica moderna</p>
            </div>

            <div class="features-grid">
                <div class="feature-card priority">
                    <div class="feature-icon">
                        <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                            <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="#007AFF"/>
                            <circle cx="12" cy="12" r="3" fill="#0056CC" opacity="0.6"/>
                        </svg>
                    </div>
                    <h3>IA Proprietária</h3>
                    <p>Primeira IA desenvolvida para o contexto médico brasileiro, com análise preditiva que aumenta receita em 25%.</p>
                    <a href="#demo" class="feature-cta">Ver IA em Ação →</a>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                            <rect x="3" y="3" width="18" height="18" rx="2" stroke="#007AFF" stroke-width="2" fill="none"/>
                            <path d="M9 9H15V15H9V9Z" fill="#007AFF" opacity="0.6"/>
                        </svg>
                    </div>
                    <h3>Dashboards 360°</h3>
                    <p>Visualizações em tempo real que transformam dados complexos em decisões práticas.</p>
                    <a href="#pricing" class="feature-cta">Explorar →</a>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                            <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1Z" fill="#007AFF" opacity="0.8"/>
                            <path d="M9 12L11 14L15 10" stroke="white" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                    </div>
                    <h3>Segurança LGPD</h3>
                    <p>Conformidade total com LGPD e padrões internacionais de segurança para dados médicos.</p>
                    <a href="#contact" class="feature-cta">Saber Mais →</a>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                            <path d="M17 21V19C17 17.9 16.1 17 15 17H9C7.9 17 7 17.9 7 19V21" stroke="#007AFF" stroke-width="2"/>
                            <circle cx="12" cy="7" r="4" stroke="#007AFF" stroke-width="2" fill="#0056CC" opacity="0.6"/>
                        </svg>
                    </div>
                    <h3>Suporte 24/7</h3>
                    <p>Equipe especializada disponível 24/7, com implementação em 15 dias e garantia de ROI.</p>
                    <a href="#contact" class="feature-cta">Falar com Especialista →</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Mobile Pricing Section -->
    <section id="pricing" class="pricing">
        <div class="container">
            <div class="section-header">
                <h2>Planos e Preços</h2>
                <p>Escolha o plano ideal para sua clínica</p>
            </div>

            <div class="pricing-cards">
                <div class="pricing-card">
                    <div class="plan-header">
                        <h3>Analyst</h3>
                        <div class="price">
                            <span class="currency">R$</span>
                            <span class="amount">399</span>
                            <span class="period">,90/mês</span>
                        </div>
                        <p class="plan-description">Para equipes que querem insights automáticos</p>
                    </div>
                    <div class="plan-features">
                        <div class="feature-item">✓ Dashboards inteligentes</div>
                        <div class="feature-item">✓ Relatórios automáticos</div>
                        <div class="feature-item">✓ Análise de tendências</div>
                        <div class="feature-item">✓ Suporte por email</div>
                    </div>
                    <div class="plan-guarantee">
                        <p>✓ 14 dias grátis • ✓ Sem cartão • ✓ Suporte incluído</p>
                    </div>
                    <a href="#demo" class="plan-button secondary">Começar Teste Gratuito</a>
                </div>

                <div class="pricing-card featured">
                    <div class="popular-badge">Mais Popular</div>
                    <div class="plan-header">
                        <h3>Business</h3>
                        <div class="price">
                            <span class="currency">R$</span>
                            <span class="amount">799</span>
                            <span class="period">/mês</span>
                        </div>
                        <p class="plan-description">Gerar valor com dados no piloto automático</p>
                    </div>
                    <div class="plan-features">
                        <div class="feature-item">✓ Tudo do Analyst</div>
                        <div class="feature-item">✓ IA avançada com ML</div>
                        <div class="feature-item">✓ Automação completa</div>
                        <div class="feature-item">✓ Suporte 24/7</div>
                        <div class="feature-item">✓ Consultoria mensal</div>
                    </div>
                    <div class="plan-guarantee featured">
                        <p>🚀 Garantia de ROI ou dinheiro de volta em 90 dias</p>
                    </div>
                    <a href="#demo" class="plan-button primary">Começar Agora - 50% OFF</a>
                </div>

                <div class="pricing-card">
                    <div class="plan-header">
                        <h3>Enterprise</h3>
                        <div class="price">
                            <span class="amount custom">Preço customizado</span>
                        </div>
                        <p class="plan-description">Solução customizada para grandes organizações</p>
                    </div>
                    <div class="plan-features">
                        <div class="feature-item">✓ Tudo do Business</div>
                        <div class="feature-item">✓ Desenvolvimento customizado</div>
                        <div class="feature-item">✓ ML especializado</div>
                        <div class="feature-item">✓ Gerente dedicado</div>
                        <div class="feature-item">✓ SLA garantido</div>
                    </div>
                    <div class="plan-guarantee">
                        <p>📞 Consultoria gratuita • 🎯 Proposta em 48h</p>
                    </div>
                    <a href="#contact" class="plan-button secondary">Agendar Consultoria</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Mobile Contact Section -->
    <section id="contact" class="contact">
        <div class="container">
            <div class="contact-header">
                <h2>Pronto para transformar sua clínica?</h2>
                <p>Fale com nossos especialistas e descubra como o Amigo DataHub pode revolucionar sua gestão médica.</p>
            </div>

            <div class="contact-stats">
                <div class="stat-item">
                    <span class="stat-number">15 dias</span>
                    <span class="stat-label">Implementação</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">24/7</span>
                    <span class="stat-label">Suporte</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">580%</span>
                    <span class="stat-label">ROI Médio</span>
                </div>
            </div>

            <div class="contact-form">
                <h3>Agende sua demonstração gratuita</h3>
                <form class="demo-form">
                    <div class="form-group">
                        <input type="text" placeholder="Nome completo" required>
                    </div>
                    <div class="form-group">
                        <input type="email" placeholder="Email profissional" required>
                    </div>
                    <div class="form-group">
                        <input type="tel" placeholder="Telefone" required>
                    </div>
                    <div class="form-group">
                        <input type="text" placeholder="Nome da clínica" required>
                    </div>
                    <div class="form-group">
                        <select required>
                            <option value="">Tamanho da clínica</option>
                            <option value="pequena">1-5 médicos</option>
                            <option value="media">6-20 médicos</option>
                            <option value="grande">21+ médicos</option>
                        </select>
                    </div>
                    <button type="submit" class="cta-button">
                        Agendar Demo Gratuita
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M8.59 16.59L13.17 12L8.59 7.41L10 6L16 12L10 18L8.59 16.59Z"/>
                        </svg>
                    </button>
                    <p class="form-note">✓ Demo personalizada ✓ Sem compromisso ✓ Resposta em 2h</p>
                </form>
            </div>

            <div class="contact-methods">
                <div class="contact-method">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="#007AFF">
                        <path d="M20 4H4C2.9 4 2.01 4.9 2.01 6L2 18C2 19.1 2.9 20 4 20H20C21.1 20 22 19.1 22 18V6C22 4.9 21.1 4 20 4ZM20 8L12 13L4 8V6L12 11L20 6V8Z"/>
                    </svg>
                    <div>
                        <strong>Email</strong>
                        <p><EMAIL></p>
                    </div>
                </div>
                <div class="contact-method">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="#007AFF">
                        <path d="M6.62 10.79C8.06 13.62 10.38 15.94 13.21 17.38L15.41 15.18C15.69 14.9 16.08 14.82 16.43 14.93C17.55 15.3 18.75 15.5 20 15.5C20.55 15.5 21 15.95 21 16.5V20C21 20.55 20.55 21 20 21C10.61 21 3 13.39 3 4C3 3.45 3.45 3 4 3H7.5C8.05 3 8.5 3.45 8.5 4C8.5 5.25 8.7 6.45 9.07 7.57C9.18 7.92 9.1 8.31 8.82 8.59L6.62 10.79Z"/>
                    </svg>
                    <div>
                        <strong>Telefone</strong>
                        <p>(11) 99999-9999</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Mobile Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-brand">
                <div class="logo">
                    <img src="assets/amigo-logo.png" alt="Amigo Logo">
                    <span class="logo-text">DataHub</span>
                </div>
                <p>Transformando dados médicos em decisões inteligentes para o futuro da saúde brasileira.</p>
            </div>

            <div class="footer-links">
                <div class="link-group">
                    <h4>Produto</h4>
                    <a href="#features">Recursos</a>
                    <a href="#pricing">Planos</a>
                    <a href="#demo">Demonstração</a>
                </div>
                <div class="link-group">
                    <h4>Empresa</h4>
                    <a href="#contact">Contato</a>
                    <a href="#support">Suporte</a>
                </div>
            </div>

            <div class="newsletter">
                <h4>Receba insights exclusivos</h4>
                <form class="newsletter-form">
                    <input type="email" placeholder="Seu email profissional" required>
                    <button type="submit">Inscrever</button>
                </form>
            </div>

            <div class="social-links">
                <a href="#" aria-label="LinkedIn">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M20.447 20.452H16.893V14.883C16.893 13.555 16.866 11.846 15.041 11.846C13.188 11.846 12.905 13.291 12.905 14.785V20.452H9.351V9H12.765V10.561H12.811C13.288 9.661 14.448 8.711 16.181 8.711C19.782 8.711 20.448 11.081 20.448 14.166V20.452H20.447ZM5.337 7.433C4.193 7.433 3.274 6.507 3.274 5.368C3.274 4.23 4.194 3.305 5.337 3.305C6.477 3.305 7.401 4.23 7.401 5.368C7.401 6.507 6.476 7.433 5.337 7.433ZM7.119 20.452H3.555V9H7.119V20.452ZM22.225 0H1.771C0.792 0 0 0.774 0 1.729V22.271C0 23.227 0.792 24 1.771 24H22.222C23.2 24 24 23.227 24 22.271V1.729C24 0.774 23.2 0 22.222 0H22.225Z"/>
                    </svg>
                </a>
                <a href="#" aria-label="Instagram">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2.163C15.204 2.163 15.584 2.175 16.85 2.233C20.102 2.381 21.621 3.924 21.769 7.152C21.827 8.417 21.838 8.797 21.838 12.001C21.838 15.206 21.826 15.585 21.769 16.85C21.62 20.075 20.105 21.621 16.85 21.769C15.584 21.827 15.206 21.839 12 21.839C8.796 21.839 8.416 21.827 7.151 21.769C3.891 21.62 2.38 20.07 2.232 16.849C2.174 15.584 2.162 15.205 2.162 12C2.162 8.796 2.175 8.417 2.232 7.151C2.381 3.924 3.896 2.38 7.151 2.232C8.417 2.175 8.796 2.163 12 2.163Z"/>
                    </svg>
                </a>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2024 Amigo DataHub. Todos os direitos reservados.</p>
            </div>
        </div>
    </footer>

    <script>
        // Mobile Menu Functionality
        function initMobileMenu() {
            const mobileMenuToggle = document.getElementById('mobileMenuToggle');
            const mobileMenu = document.getElementById('mobileMenu');

            if (mobileMenuToggle && mobileMenu) {
                mobileMenuToggle.addEventListener('click', function() {
                    mobileMenuToggle.classList.toggle('active');
                    mobileMenu.classList.toggle('active');
                });

                // Close menu when clicking on links
                const mobileLinks = mobileMenu.querySelectorAll('a');
                mobileLinks.forEach(link => {
                    link.addEventListener('click', function() {
                        mobileMenuToggle.classList.remove('active');
                        mobileMenu.classList.remove('active');
                    });
                });

                // Close menu when clicking outside
                document.addEventListener('click', function(event) {
                    if (!mobileMenuToggle.contains(event.target) && !mobileMenu.contains(event.target)) {
                        mobileMenuToggle.classList.remove('active');
                        mobileMenu.classList.remove('active');
                    }
                });
            }
        }

        // Simple Mobile D3 Visualization
        function createMobileVisualization() {
            const svg = d3.select("#d3-visualization");
            const width = 200;
            const height = 200;

            svg.attr("width", width)
               .attr("height", height);

            // Clear any existing content
            svg.selectAll("*").remove();

            // Create simple animated cubes for mobile
            const cubesData = [];
            for (let i = 0; i < 9; i++) {
                cubesData.push({
                    x: (i % 3) * 60 + 40,
                    y: Math.floor(i / 3) * 60 + 40,
                    size: 20
                });
            }

            const cubes = svg.selectAll('.mobile-cube')
                .data(cubesData)
                .enter()
                .append('rect')
                .attr('class', 'mobile-cube')
                .attr('x', d => d.x)
                .attr('y', d => d.y)
                .attr('width', d => d.size)
                .attr('height', d => d.size)
                .attr('rx', 4)
                .attr('fill', '#007AFF')
                .attr('opacity', 0.8);

            // Simple animation
            function animateCubes() {
                cubes.transition()
                    .duration(2000)
                    .attr('opacity', 0.3)
                    .attr('transform', 'scale(0.8)')
                    .transition()
                    .duration(2000)
                    .attr('opacity', 0.8)
                    .attr('transform', 'scale(1)')
                    .on('end', animateCubes);
            }

            animateCubes();
        }

        // Initialize everything when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initMobileMenu();
            createMobileVisualization();
        });
    </script>
</body>
</html>

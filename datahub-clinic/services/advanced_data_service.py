"""
Serviço avançado para gerenciamento de múltiplas fontes de dados com relacionamentos.
"""
import os
import json
import copy
import logging
import pandas as pd

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AdvancedDataService:
    """Serviço avançado para gerenciamento de múltiplas fontes de dados com relacionamentos."""

    def __init__(self, config=None):
        """
        Inicializa o serviço de dados avançado.

        Args:
            config (dict, optional): Configuração das fontes de dados.
        """
        self.config = config or {}
        self.sources = {}
        self.raw_data = {}
        self.processed_data = {}
        self._initialize_sources()

    def _initialize_sources(self):
        """Inicializa todas as fontes de dados configuradas."""
        sources_config = self.config.get('sources', {})

        for source_name, source_config in sources_config.items():
            try:
                source_type = source_config.get('type', 'mock')

                if source_type == 'mock':
                    from .data_sources.mock_data_source import MockDataSource
                    self.sources[source_name] = MockDataSource()
                    logger.info(f"Fonte de dados mockados inicializada: {source_name}")

                elif source_type == 'json':
                    from .data_sources.json_file_source import JsonFileDataSource
                    file_path = source_config.get('file_path')
                    self.sources[source_name] = JsonFileDataSource(file_path)
                    logger.info(f"Fonte de dados JSON inicializada: {source_name} - {file_path}")

                elif source_type == 'excel':
                    from .data_sources.excel_source import ExcelDataSource
                    file_path = source_config.get('file_path')
                    sheet = source_config.get('sheet')
                    self.sources[source_name] = ExcelDataSource(file_path, {source_name: sheet} if sheet else None)
                    logger.info(f"Fonte de dados Excel inicializada: {source_name} - {file_path}")

                elif source_type == 'csv':
                    from .data_sources.csv_source import CsvDataSource
                    file_path = source_config.get('file_path')
                    delimiter = source_config.get('delimiter', ',')
                    encoding = source_config.get('encoding', 'utf-8')
                    self.sources[source_name] = CsvDataSource({source_name: file_path}, delimiter, encoding)
                    logger.info(f"Fonte de dados CSV inicializada: {source_name} - {file_path}")

                elif source_type == 'database':
                    from .data_sources.database_source import DatabaseDataSource
                    connection_string = source_config.get('connection_string')
                    query = source_config.get('query')
                    query_mapping = {source_name: query} if query else {}
                    self.sources[source_name] = DatabaseDataSource(connection_string, query_mapping)
                    logger.info(f"Fonte de dados de banco de dados inicializada: {source_name}")

                else:
                    logger.warning(f"Tipo de fonte de dados desconhecido: {source_type} para {source_name}")

            except Exception as e:
                logger.error(f"Erro ao inicializar fonte de dados {source_name}: {e}")

    def set_config(self, config):
        """
        Atualiza a configuração e reinicializa as fontes de dados.

        Args:
            config (dict): Nova configuração.
        """
        self.config = config
        self.sources = {}
        self.raw_data = {}
        self.processed_data = {}
        self._initialize_sources()

    def get_data(self):
        """
        Obtém dados de todas as fontes, aplica transformações, relacionamentos
        e estrutura no formato esperado pelo aplicativo.

        Returns:
            dict: Dados estruturados no formato esperado pelo aplicativo.
        """
        try:
            # 1. Obter dados brutos de cada fonte
            self._load_raw_data()

            # 2. Aplicar transformações (mapeamento de campos, exclusão de campos)
            self._apply_transformations()

            # 3. Aplicar relacionamentos entre fontes
            self._apply_relationships()

            # 4. Estruturar dados no formato esperado pelo aplicativo
            final_data = self._structure_output()

            return final_data

        except Exception as e:
            logger.error(f"Erro ao processar dados: {e}")
            # Fallback para dados mockados em caso de erro
            from .data_sources.mock_data_source import MockDataSource
            return MockDataSource().get_data()

    def _load_raw_data(self):
        """Carrega dados brutos de todas as fontes configuradas."""
        self.raw_data = {}

        for source_name, source in self.sources.items():
            try:
                source_data = source.get_data()

                # Extrair os dados relevantes para esta fonte
                # Dependendo da implementação da fonte, pode ser necessário ajustar esta lógica
                if isinstance(source_data, dict):
                    # Se a fonte retorna um dicionário com múltiplos conjuntos de dados
                    # (como a implementação atual), extraímos apenas o que precisamos
                    for key, value in source_data.items():
                        if isinstance(value, list) and value:
                            self.raw_data[source_name] = value
                            break

                    # Se não encontramos dados em listas, usamos o primeiro conjunto de dados disponível
                    if source_name not in self.raw_data:
                        for key, value in source_data.items():
                            if isinstance(value, dict) and value:
                                self.raw_data[source_name] = [value]
                                break

                # Se ainda não temos dados, tentamos usar o retorno diretamente
                if source_name not in self.raw_data:
                    self.raw_data[source_name] = source_data

                logger.info(f"Dados carregados da fonte {source_name}")

            except Exception as e:
                logger.error(f"Erro ao carregar dados da fonte {source_name}: {e}")

    def _apply_transformations(self):
        """Aplica transformações aos dados brutos (mapeamento e exclusão de campos)."""
        self.processed_data = {}
        sources_config = self.config.get('sources', {})

        for source_name, data in self.raw_data.items():
            if source_name not in sources_config:
                self.processed_data[source_name] = data
                continue

            source_config = sources_config[source_name]
            field_mapping = source_config.get('field_mapping', {})
            exclude_fields = source_config.get('exclude_fields', [])

            # Converter para DataFrame para facilitar as transformações
            if isinstance(data, list) and data:
                df = pd.DataFrame(data)
            elif isinstance(data, dict):
                df = pd.DataFrame([data])
            else:
                self.processed_data[source_name] = data
                continue

            # Aplicar mapeamento de campos
            if field_mapping:
                df = df.rename(columns=field_mapping)

            # Excluir campos
            if exclude_fields:
                df = df.drop(columns=[col for col in exclude_fields if col in df.columns])

            # Converter de volta para lista de dicionários
            self.processed_data[source_name] = df.to_dict('records')

            logger.info(f"Transformações aplicadas à fonte {source_name}")

    def _apply_relationships(self):
        """Aplica relacionamentos entre fontes de dados."""
        relationships = self.config.get('relationships', [])

        for rel in relationships:
            try:
                from_source = rel.get('from_source')
                from_field = rel.get('from_field')
                to_source = rel.get('to_source')
                to_field = rel.get('to_field')
                rel_type = rel.get('relationship_type', 'one_to_many')

                if not all([from_source, from_field, to_source, to_field]):
                    logger.warning(f"Relacionamento incompleto: {rel}")
                    continue

                if from_source not in self.processed_data or to_source not in self.processed_data:
                    logger.warning(f"Fonte de dados não encontrada para relacionamento: {rel}")
                    continue

                # Criar índice para a fonte de destino
                to_index = {}
                for item in self.processed_data[to_source]:
                    key = item.get(to_field)
                    if key is not None:
                        if rel_type == 'one_to_many':
                            if key not in to_index:
                                to_index[key] = []
                            to_index[key].append(item)
                        else:  # one_to_one ou many_to_one
                            to_index[key] = item

                # Aplicar relacionamento
                if rel_type == 'one_to_many':
                    for item in self.processed_data[from_source]:
                        key = item.get(from_field)
                        if key is not None and key in to_index:
                            item[f"{to_source}_items"] = to_index[key]

                elif rel_type == 'many_to_one' or rel_type == 'one_to_one':
                    for item in self.processed_data[from_source]:
                        key = item.get(from_field)
                        if key is not None and key in to_index:
                            item[to_source] = to_index[key]

                logger.info(f"Relacionamento aplicado: {from_source}.{from_field} -> {to_source}.{to_field}")

            except Exception as e:
                logger.error(f"Erro ao aplicar relacionamento: {e}")

    def _structure_output(self):
        """Estrutura os dados processados no formato esperado pelo aplicativo."""
        output_structure = self.config.get('output_structure', {})

        # Se não houver estrutura de saída definida, criar uma estrutura padrão
        if not output_structure:
            return self._create_default_structure()

        # Criar estrutura de saída conforme configuração
        result = {}

        for module_name, module_mapping in output_structure.items():
            result[module_name] = {}

            if isinstance(module_mapping, dict):
                for submodule_name, source_name in module_mapping.items():
                    if source_name in self.processed_data:
                        result[module_name][submodule_name] = self.processed_data[source_name]
                    else:
                        result[module_name][submodule_name] = []
            elif isinstance(module_mapping, str) and module_mapping in self.processed_data:
                # Se o mapeamento for uma string, usar diretamente como fonte
                result[module_name] = self.processed_data[module_mapping]

        # Garantir que a estrutura básica exista
        self._ensure_basic_structure(result)

        return result

    def _create_default_structure(self):
        """Cria uma estrutura padrão com base nos dados processados."""
        result = {
            "agenda": {
                "agendamentos": [],
                "producao_medica": [],
                "tempo_atendimento": [],
                "cancelamentos": []
            },
            "financeiro": {
                "contas_receber": [],
                "contas_pagar": [],
                "fluxo_caixa": [],
                "fechamento_caixa": []
            },
            "paciente": {
                "atendimentos_realizados": [],
                "creditos_disponiveis": [],
                "orcamentos_fechados": [],
                "orcamentos_abertos": []
            },
            "amigocare": {
                "avaliacao_nps": [],
                "leads": [],
                "campanhas": [],
                "acompanhamento_pacientes": [],
                "indicadores_qualidade": []
            },
            "visao360": {
                "pacientes_integrados": [],
                "agendamentos_integrados": [],
                "transacoes_integradas": [],
                "jornadas_paciente": [],
                "recomendacoes_estrutura": []
            },
            "resumo": {
                "total_agendamentos": 0,
                "total_atendimentos": 0,
                "total_receita": 0,
                "total_despesa": 0,
                "taxa_ocupacao": 0,
                "tempo_medio_atendimento": 0,
                "taxa_cancelamento": 0,
                "procedimentos_populares": [],
                "medicos_produtivos": [],
                "unidades_desempenho": []
            }
        }

        # Tentar mapear fontes para a estrutura padrão com base nos nomes
        for source_name, data in self.processed_data.items():
            source_name_lower = source_name.lower()

            # Mapear para agenda
            if 'agenda' in source_name_lower:
                if 'agendamento' in source_name_lower:
                    result["agenda"]["agendamentos"] = data
                elif 'producao' in source_name_lower:
                    result["agenda"]["producao_medica"] = data
                elif 'tempo' in source_name_lower:
                    result["agenda"]["tempo_atendimento"] = data
                elif 'cancelamento' in source_name_lower:
                    result["agenda"]["cancelamentos"] = data

            # Mapear para financeiro
            elif 'financeiro' in source_name_lower or 'finance' in source_name_lower:
                if 'receber' in source_name_lower:
                    result["financeiro"]["contas_receber"] = data
                elif 'pagar' in source_name_lower:
                    result["financeiro"]["contas_pagar"] = data
                elif 'fluxo' in source_name_lower:
                    result["financeiro"]["fluxo_caixa"] = data
                elif 'fechamento' in source_name_lower or 'caixa' in source_name_lower:
                    result["financeiro"]["fechamento_caixa"] = data

            # Mapear para paciente
            elif 'paciente' in source_name_lower or 'patient' in source_name_lower:
                if 'atendimento' in source_name_lower:
                    result["paciente"]["atendimentos_realizados"] = data
                elif 'credito' in source_name_lower:
                    result["paciente"]["creditos_disponiveis"] = data
                elif 'orcamento' in source_name_lower and 'fechado' in source_name_lower:
                    result["paciente"]["orcamentos_fechados"] = data
                elif 'orcamento' in source_name_lower and 'aberto' in source_name_lower:
                    result["paciente"]["orcamentos_abertos"] = data
                else:
                    # Se não houver correspondência específica, colocar em atendimentos
                    result["paciente"]["atendimentos_realizados"] = data

            # Mapear para amigocare
            elif 'amigocare' in source_name_lower or 'amigo_care' in source_name_lower:
                if 'nps' in source_name_lower or 'avaliacao' in source_name_lower:
                    result["amigocare"]["avaliacao_nps"] = data
                elif 'lead' in source_name_lower:
                    result["amigocare"]["leads"] = data
                elif 'campanha' in source_name_lower:
                    result["amigocare"]["campanhas"] = data
                elif 'acompanhamento' in source_name_lower:
                    result["amigocare"]["acompanhamento_pacientes"] = data
                elif 'indicador' in source_name_lower or 'qualidade' in source_name_lower:
                    result["amigocare"]["indicadores_qualidade"] = data



            # Mapear para resumo
            elif 'resumo' in source_name_lower or 'summary' in source_name_lower:
                # Tentar extrair métricas do resumo
                if isinstance(data, list) and data:
                    for item in data:
                        for key, value in item.items():
                            if key in result["resumo"]:
                                result["resumo"][key] = value
                elif isinstance(data, dict):
                    for key, value in data.items():
                        if key in result["resumo"]:
                            result["resumo"][key] = value

        return result

    def _ensure_basic_structure(self, data):
        """Garante que a estrutura básica exista no resultado."""
        basic_structure = {
            "agenda": {
                "agendamentos": [],
                "producao_medica": [],
                "tempo_atendimento": [],
                "cancelamentos": []
            },
            "financeiro": {
                "contas_receber": [],
                "contas_pagar": [],
                "fluxo_caixa": [],
                "fechamento_caixa": []
            },
            "paciente": {
                "atendimentos_realizados": [],
                "creditos_disponiveis": [],
                "orcamentos_fechados": [],
                "orcamentos_abertos": []
            },
            "amigocare": {
                "avaliacao_nps": [],
                "leads": [],
                "campanhas": [],
                "acompanhamento_pacientes": [],
                "indicadores_qualidade": []
            },
            "visao360": {
                "pacientes_integrados": [],
                "agendamentos_integrados": [],
                "transacoes_integradas": [],
                "jornadas_paciente": [],
                "recomendacoes_estrutura": []
            },
            "resumo": {
                "total_agendamentos": 0,
                "total_atendimentos": 0,
                "total_receita": 0,
                "total_despesa": 0,
                "taxa_ocupacao": 0,
                "tempo_medio_atendimento": 0,
                "taxa_cancelamento": 0,
                "procedimentos_populares": [],
                "medicos_produtivos": [],
                "unidades_desempenho": []
            }
        }

        # Garantir que todos os módulos existam
        for module_name, module_structure in basic_structure.items():
            if module_name not in data:
                data[module_name] = {}

            # Se o módulo for um dicionário, garantir que todos os submódulos existam
            if isinstance(data[module_name], dict):
                for submodule_name, default_value in module_structure.items():
                    if submodule_name not in data[module_name]:
                        data[module_name][submodule_name] = default_value
            # Se o módulo não for um dicionário, substituir pelo padrão
            elif not isinstance(data[module_name], dict):
                data[module_name] = module_structure

    def save_config(self, config_path=None):
        """
        Salva a configuração atual em um arquivo JSON.

        Args:
            config_path (str, optional): Caminho para o arquivo de configuração.
                Se não for fornecido, usa o caminho padrão.
        """
        if not config_path:
            # Caminho padrão para o arquivo de configuração
            config_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config')
            os.makedirs(config_dir, exist_ok=True)
            config_path = os.path.join(config_dir, 'advanced_data_source.json')

        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, ensure_ascii=False, indent=2)

        logger.info(f"Configuração avançada salva em: {config_path}")

    def load_config(self, config_path=None):
        """
        Carrega a configuração de um arquivo JSON.

        Args:
            config_path (str, optional): Caminho para o arquivo de configuração.
                Se não for fornecido, usa o caminho padrão.
        """
        if not config_path:
            # Caminho padrão para o arquivo de configuração
            config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', 'advanced_data_source.json')

        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                self.config = json.load(f)

            self.sources = {}
            self._initialize_sources()
            logger.info(f"Configuração avançada carregada de: {config_path}")
            return True
        else:
            logger.warning(f"Arquivo de configuração avançada não encontrado: {config_path}")
            return False

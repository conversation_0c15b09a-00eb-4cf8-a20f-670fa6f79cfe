"""
Serviço de agente inteligente que utiliza a arquitetura Data Mesh.

Este serviço fornece uma interface para o agente inteligente acessar dados
de forma contextual e anônima através da arquitetura Data Mesh.
"""
from typing import Dict, List, Any, Optional
import logging
import json
import os
from datetime import datetime
import openai
from .data_mesh import DomainRegistry, initialize_data_mesh

# Configurar logging
logger = logging.getLogger(__name__)

class AgentService:
    """Serviço de agente inteligente que utiliza a arquitetura Data Mesh."""

    _instance = None

    def __new__(cls):
        """Implementação de Singleton."""
        if cls._instance is None:
            cls._instance = super(AgentService, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """Inicializa o serviço de agente."""
        if self._initialized:
            return

        # Inicializar o Data Mesh
        self.domain_registry = initialize_data_mesh()

        # Configurar a API da OpenAI (não é mais necessário configurar globalmente na versão 1.0.0+)
        # A chave da API será fornecida diretamente ao criar o cliente

        # Histórico de consultas
        self.query_history = []

        # Modelo de linguagem a ser usado
        self.model = os.getenv("OPENAI_MODEL", "gpt-4o-mini")

        # Marcar como inicializado
        self._initialized = True

        logger.info("Serviço de agente inicializado com Data Mesh")

    def load_data(self, raw_data: Dict[str, Any]) -> None:
        """
        Carrega dados brutos em todos os domínios do Data Mesh.

        Args:
            raw_data: Dados brutos da aplicação
        """
        self.domain_registry.load_data_to_all_domains(raw_data)
        logger.info("Dados carregados em todos os domínios do Data Mesh")

    def get_all_metrics(self) -> Dict[str, Dict[str, Any]]:
        """
        Obtém métricas de todos os domínios.

        Returns:
            Dict[str, Dict[str, Any]]: Métricas de todos os domínios
        """
        return self.domain_registry.get_all_metrics()

    def get_domain_metrics(self, domain_name: str) -> Dict[str, Any]:
        """
        Obtém métricas de um domínio específico.

        Args:
            domain_name: Nome do domínio

        Returns:
            Dict[str, Any]: Métricas do domínio
        """
        domain = self.domain_registry.get_domain(domain_name)
        if domain:
            return domain.get_metrics()
        return {}

    def get_contextual_data(self, domain_name: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Obtém dados contextuais de um domínio específico.

        Args:
            domain_name: Nome do domínio
            context: Contexto da consulta

        Returns:
            Dict[str, Any]: Dados contextuais
        """
        domain = self.domain_registry.get_domain(domain_name)
        if domain:
            return domain.get_contextual_data(context)
        return {}

    def process_query(self, query: str, page_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Processa uma consulta do usuário e retorna uma resposta contextualizada.

        Args:
            query: Consulta do usuário
            page_context: Contexto da página atual

        Returns:
            Dict[str, Any]: Resposta contextualizada
        """
        try:
            # Registrar a consulta no histórico
            query_entry = {
                "query": query,
                "timestamp": datetime.now().isoformat(),
                "page_context": page_context
            }
            self.query_history.append(query_entry)

            # Determinar o domínio relevante com base no contexto da página
            relevant_domain = self._determine_relevant_domain(query, page_context)

            # Obter métricas do domínio relevante
            domain_metrics = self.get_domain_metrics(relevant_domain) if relevant_domain else {}

            # Determinar o contexto específico para a consulta
            query_context = self._determine_query_context(query, page_context)

            # Obter dados contextuais do domínio relevante
            contextual_data = self.get_contextual_data(relevant_domain, query_context) if relevant_domain else {}

            # Preparar o prompt para o modelo de linguagem
            prompt = self._prepare_prompt(query, page_context, domain_metrics, contextual_data)

            # Chamar o modelo de linguagem
            response = self._call_language_model(prompt)

            # Processar a resposta
            processed_response = self._process_response(response, query, relevant_domain)

            return processed_response

        except Exception as e:
            logger.error(f"Erro ao processar consulta: {str(e)}")
            return {
                "text": f"Desculpe, ocorreu um erro ao processar sua consulta: {str(e)}",
                "error": str(e)
            }

    def _determine_relevant_domain(self, query: str, page_context: Dict[str, Any]) -> Optional[str]:
        """
        Determina o domínio mais relevante para a consulta.

        Args:
            query: Consulta do usuário
            page_context: Contexto da página atual

        Returns:
            Optional[str]: Nome do domínio relevante ou None
        """
        # Se o contexto da página especificar um módulo, usar esse domínio
        if page_context and "current_module" in page_context:
            module = page_context["current_module"]
            # Mapear módulo para domínio
            domain_mapping = {
                "agenda": "agenda",
                "financeiro": "financeiro",
                "paciente": "paciente",
                "amigocare": "amigocare",
                "visao360": "visao360",
                "amigostudio": "amigostudio",
                "dashboard": None  # Página inicial não tem domínio específico
            }
            return domain_mapping.get(module)

        # Caso contrário, tentar determinar o domínio a partir da consulta
        query_lower = query.lower()

        # Palavras-chave para cada domínio
        domain_keywords = {
            "agenda": ["agenda", "agendamento", "consulta", "horário", "médico", "profissional",
                      "cancelamento", "remarcação", "disponibilidade", "ocupação"],
            "financeiro": ["financeiro", "pagamento", "recebimento", "fatura", "conta", "caixa",
                          "receita", "despesa", "lucro", "margem", "inadimplência"],
            "paciente": ["paciente", "atendimento", "prontuário", "histórico", "tratamento",
                        "orçamento", "crédito", "procedimento"],
            "amigocare": ["satisfação", "nps", "lead", "campanha", "marketing", "captação",
                         "qualidade", "avaliação", "feedback"],
            "visao360": ["visão", "integrado", "jornada", "retenção", "lifetime", "valor",
                        "segmentação", "comportamento", "previsão"],
            "amigostudio": ["análise", "visualização", "gráfico", "dashboard", "relatório",
                           "inteligência", "ia", "modelo", "previsão", "tendência"]
        }

        # Contar ocorrências de palavras-chave
        domain_scores = {domain: 0 for domain in domain_keywords}

        for domain, keywords in domain_keywords.items():
            for keyword in keywords:
                if keyword in query_lower:
                    domain_scores[domain] += 1

        # Retornar o domínio com maior pontuação
        max_score = max(domain_scores.values())
        if max_score > 0:
            # Em caso de empate, priorizar o domínio do contexto da página
            if page_context and "current_module" in page_context:
                module = page_context["current_module"]
                if module in domain_scores and domain_scores[module] == max_score:
                    return module

            # Caso contrário, retornar o primeiro domínio com pontuação máxima
            for domain, score in domain_scores.items():
                if score == max_score:
                    return domain

        # Se não for possível determinar o domínio, retornar None
        return None

    def _determine_query_context(self, query: str, page_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Determina o contexto específico para a consulta.

        Args:
            query: Consulta do usuário
            page_context: Contexto da página atual

        Returns:
            Dict[str, Any]: Contexto da consulta
        """
        query_context = {}

        # Extrair período da consulta
        if "hoje" in query.lower() or "atual" in query.lower():
            query_context["periodo"] = "hoje"
        elif "semana" in query.lower():
            query_context["periodo"] = "semana"
        elif "mês" in query.lower():
            query_context["periodo"] = "mes_atual"
        elif "trimestre" in query.lower():
            query_context["periodo"] = "trimestre"
        elif "ano" in query.lower():
            query_context["periodo"] = "ano"

        # Adicionar outros contextos específicos conforme necessário
        # ...

        # Incorporar contexto da página
        if page_context:
            # Adicionar filtros específicos da página
            if "filters" in page_context:
                query_context.update(page_context["filters"])

            # Adicionar seleções específicas da página
            if "selections" in page_context:
                query_context.update(page_context["selections"])

        return query_context

    def _prepare_prompt(self, query: str, page_context: Dict[str, Any],
                       domain_metrics: Dict[str, Any], contextual_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Prepara o prompt para o modelo de linguagem.

        Args:
            query: Consulta do usuário
            page_context: Contexto da página atual
            domain_metrics: Métricas do domínio relevante
            contextual_data: Dados contextuais

        Returns:
            List[Dict[str, Any]]: Prompt formatado para o modelo
        """
        # Formatar o contexto da página
        page_context_str = ""
        if page_context:
            page_context_str = "Contexto da página:\n"
            for key, value in page_context.items():
                page_context_str += f"- {key}: {value}\n"

        # Formatar métricas do domínio
        metrics_str = ""
        if domain_metrics:
            metrics_str = "Métricas relevantes:\n"
            for key, value in domain_metrics.items():
                # Limitar a complexidade para métricas simples
                if isinstance(value, (int, float, str, bool)):
                    metrics_str += f"- {key}: {value}\n"
                elif isinstance(value, dict) and len(value) <= 5:
                    metrics_str += f"- {key}:\n"
                    for subkey, subvalue in value.items():
                        if isinstance(subvalue, (int, float, str, bool)):
                            metrics_str += f"  - {subkey}: {subvalue}\n"

        # Formatar dados contextuais
        contextual_str = ""
        if "metricas" in contextual_data:
            # Já incluímos as métricas acima, então podemos pular
            pass

        # Histórico de consultas recentes (últimas 3)
        history_str = ""
        if len(self.query_history) > 1:  # Mais de 1 porque a consulta atual já foi adicionada
            history_str = "Consultas recentes:\n"
            for entry in self.query_history[-4:-1]:  # Últimas 3 consultas excluindo a atual
                history_str += f"- {entry['query']}\n"

        # Montar o prompt completo
        system_message = f"""
        Você é um assistente especializado em análise de dados de saúde para clínicas médicas.
        Você tem acesso a métricas anônimas e dados contextuais, mas não a dados individuais de pacientes.

        Ao responder:
        1. Foque em insights relevantes para clínicas de saúde
        2. Considere métricas importantes como: taxa de ocupação, tempo médio de atendimento, satisfação do paciente, taxa de retorno, e rentabilidade por procedimento
        3. Sugira correlações entre dados que possam não ser óbvias
        4. Quando relevante, mencione benchmarks do setor de saúde
        5. Considere a jornada completa do paciente na clínica
        6. Relacione os dados com impacto financeiro e operacional para a clínica
        7. Sugira ações práticas baseadas nos insights

        {page_context_str}

        {metrics_str}

        {contextual_str}

        {history_str}
        """

        # Formatar como mensagens para a API da OpenAI
        messages = [
            {"role": "system", "content": system_message},
            {"role": "user", "content": query}
        ]

        return messages

    def _call_language_model(self, prompt: List[Dict[str, Any]]) -> str:
        """
        Chama o modelo de linguagem com o prompt fornecido.

        Args:
            prompt: Prompt formatado para o modelo

        Returns:
            str: Resposta do modelo
        """
        try:
            # Chamar a API da OpenAI (sintaxe para openai>=1.0.0)
            client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
            response = client.chat.completions.create(
                model=self.model,
                messages=prompt,
                temperature=0.7,
                max_tokens=1000
            )

            # Extrair a resposta (nova sintaxe)
            return response.choices[0].message.content

        except Exception as e:
            logger.error(f"Erro ao chamar o modelo de linguagem: {str(e)}")
            return f"Erro ao gerar resposta: {str(e)}"

    def _process_response(self, response: str, query: str, domain: Optional[str]) -> Dict[str, Any]:
        """
        Processa a resposta do modelo de linguagem.

        Args:
            response: Resposta do modelo
            query: Consulta original
            domain: Domínio relevante

        Returns:
            Dict[str, Any]: Resposta processada
        """
        # Extrair código Python, se houver
        code = None
        if "```python" in response:
            code_start = response.find("```python") + 9
            code_end = response.find("```", code_start)
            if code_end != -1:
                code = response[code_start:code_end].strip()

        # Extrair dados para gráfico, se houver
        chart_data = None
        if "```chart" in response:
            chart_start = response.find("```chart") + 8
            chart_end = response.find("```", chart_start)
            if chart_end != -1:
                try:
                    chart_json = response[chart_start:chart_end].strip()

                    # Verificar se o JSON começa com { e termina com }
                    if not chart_json.startswith('{') or not chart_json.endswith('}'):
                        logger.warning('Formato JSON inválido, tentando corrigir...')

                        # Tentar extrair apenas a parte JSON válida
                        json_start_index = chart_json.find('{')
                        json_end_index = chart_json.rfind('}') + 1

                        if json_start_index >= 0 and json_end_index > json_start_index:
                            chart_json = chart_json[json_start_index:json_end_index]
                        else:
                            raise ValueError('Não foi possível encontrar um objeto JSON válido')

                    # Analisar o JSON
                    chart_data = json.loads(chart_json)

                    # Garantir que o gráfico tenha as propriedades necessárias
                    if 'type' not in chart_data:
                        chart_data['type'] = 'bar'  # Tipo padrão

                    if 'labels' not in chart_data or not chart_data['labels']:
                        chart_data['labels'] = []

                    if 'datasets' not in chart_data or not chart_data['datasets']:
                        chart_data['datasets'] = []

                    # Garantir que cada dataset tenha as propriedades necessárias
                    for dataset in chart_data.get('datasets', []):
                        if 'data' not in dataset or not dataset['data']:
                            dataset['data'] = []

                except Exception as e:
                    logger.error(f"Erro ao processar dados do gráfico: {str(e)}")

        # Montar resposta final
        processed_response = {
            "text": response,
            "code": code,
            "chart": chart_data,
            "domain": domain,
            "query": query,
            "timestamp": datetime.now().isoformat()
        }

        return processed_response

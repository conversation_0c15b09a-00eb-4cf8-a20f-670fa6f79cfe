"""
Serviço de agregação de dados para alimentar o GPT.
Coleta dados de diferentes fontes e os formata para uso com o GPT.
"""
import json
from flask import session

class DataAggregator:
    """
    Classe responsável por agregar dados de diferentes fontes
    e formatá-los para uso com o GPT.
    """

    @staticmethod
    def aggregate_module_data(module_name, mock_data):
        """
        Agrega dados de um módulo específico.

        Args:
            module_name (str): Nome do módulo (agenda, financeiro, etc.)
            mock_data (dict): Dados mockados carregados na aplicação

        Returns:
            dict: Dados agregados do módulo
        """
        if module_name not in mock_data:
            return {}

        return mock_data[module_name]

    @staticmethod
    def aggregate_session_data():
        """
        Agrega dados da sessão atual.

        Returns:
            dict: Dados da sessão
        """
        session_data = {}

        # Coletar dados relevantes da sessão
        if 'user' in session:
            session_data['user'] = session['user']

        # Adicionar outros dados da sessão conforme necessário

        return session_data

    @staticmethod
    def format_for_gpt(data, context=None):
        """
        Formata os dados para uso com o GPT.

        Args:
            data (dict): Dados a serem formatados
            context (str, optional): Contexto adicional para o GPT

        Returns:
            dict: Dados formatados para o GPT
        """
        # Simplificar dados complexos e limitar o tamanho
        formatted_data = {}

        # Processar cada chave no dicionário de dados
        for key, value in data.items():
            # Se for uma lista, pegar apenas os primeiros 5 itens para não sobrecarregar
            if isinstance(value, list):
                formatted_data[key] = value[:5]
            # Se for um dicionário, incluir diretamente
            elif isinstance(value, dict):
                formatted_data[key] = value
            # Outros tipos de dados, incluir diretamente
            else:
                formatted_data[key] = value

        # Adicionar contexto se fornecido
        if context:
            formatted_data['context'] = context

        return formatted_data

    @staticmethod
    def get_data_for_insight(module_name, report_name, mock_data, page_context=None):
        """
        Obtém dados específicos para gerar insights.

        Args:
            module_name (str): Nome do módulo (agenda, financeiro, etc.)
            report_name (str): Nome do relatório específico
            mock_data (dict): Dados mockados carregados na aplicação
            page_context (dict, optional): Contexto específico da página

        Returns:
            dict: Dados formatados para gerar insights
        """
        # Obter dados do módulo
        module_data = DataAggregator.aggregate_module_data(module_name, mock_data)

        # Obter dados específicos do relatório
        report_data = {}
        if report_name in module_data:
            report_data = module_data[report_name]

        # Obter dados de resumo
        summary_data = mock_data.get('resumo', {})

        # Combinar dados
        combined_data = {
            'report': report_data,
            'summary': summary_data
        }

        # Criar contexto detalhado para o GPT
        context_parts = [f"Dados do módulo {module_name}, relatório {report_name}"]

        # Adicionar contexto específico da página se fornecido
        if page_context:
            # Adicionar título da página
            if 'page_title' in page_context:
                context_parts.append(f"Título da página: {page_context['page_title']}")

            # Adicionar descrição da página
            if 'page_description' in page_context:
                context_parts.append(f"Descrição: {page_context['page_description']}")

            # Adicionar métricas principais
            if 'key_metrics' in page_context:
                metrics_str = ", ".join([f"{k}: {v}" for k, v in page_context['key_metrics'].items()])
                context_parts.append(f"Métricas principais: {metrics_str}")

            # Adicionar foco da análise
            if 'analysis_focus' in page_context:
                context_parts.append(f"Foco da análise: {page_context['analysis_focus']}")

            # Adicionar elementos da página
            if 'page_elements' in page_context:
                elements_str = ", ".join(map(str, page_context['page_elements']))
                context_parts.append(f"Elementos da página: {elements_str}")

            # Adicionar o contexto completo aos dados combinados
            combined_data['page_context'] = page_context

        # Juntar todas as partes do contexto
        context = "; ".join(context_parts)

        return DataAggregator.format_for_gpt(combined_data, context)

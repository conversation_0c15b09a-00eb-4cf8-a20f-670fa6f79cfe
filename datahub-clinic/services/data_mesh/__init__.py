"""
Pacote Data Mesh para a aplicação.

Este pacote implementa uma arquitetura de Data Mesh com domínios de negócio autônomos.
"""
from .data_mesh_domains import DataMeshDomain, DomainRegistry
from .domains.agenda_domain import AgendaDomain
from .domains.financeiro_domain import FinanceiroDomain
from .domains.paciente_domain import PacienteDomain
from .domains.amigocare_domain import AmigoCareDomain
from .domains.visao360_domain import Visao360Domain
from .domains.amigostudio_domain import AmigoStudioDomain

__all__ = [
    'DataMeshDomain',
    'DomainRegistry',
    'AgendaDomain',
    'FinanceiroDomain',
    'PacienteDomain',
    'AmigoCareDomain',
    'Visao360Domain',
    'AmigoStudioDomain',
    'initialize_data_mesh'
]

def initialize_data_mesh():
    """
    Inicializa o Data Mesh registrando todos os domínios.
    
    Returns:
        DomainRegistry: Registro de domínios inicializado
    """
    # Obter o registro de domínios
    registry = DomainRegistry()
    
    # Registrar domínios
    registry.register_domain(AgendaDomain())
    registry.register_domain(FinanceiroDomain())
    registry.register_domain(PacienteDomain())
    registry.register_domain(AmigoCareDomain())
    registry.register_domain(Visao360Domain())
    registry.register_domain(AmigoStudioDomain())
    
    return registry

"""
Integração do Data Mesh com o agente existente.

Este módulo fornece funções para integrar a arquitetura Data Mesh com o agente
existente na aplicação.
"""
from typing import Dict, List, Any, Optional
import logging
from flask import session
from ..agent_service import AgentService

# Configurar logging
logger = logging.getLogger(__name__)

def process_agent_chat(message: str, page_context: Dict[str, Any], history: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Processa uma mensagem do chat do agente usando o Data Mesh.
    
    Args:
        message: Mensagem do usuário
        page_context: Contexto da página atual
        history: Histórico de conversas
        
    Returns:
        Dict[str, Any]: Resposta processada
    """
    try:
        # Obter o serviço de agente
        agent_service = AgentService()
        
        # Processar a consulta
        response = agent_service.process_query(message, page_context)
        
        # Adicionar a resposta ao histórico
        if history is not None:
            history.append({
                "role": "user",
                "content": message
            })
            history.append({
                "role": "assistant",
                "content": response["text"]
            })
        
        return response
        
    except Exception as e:
        logger.error(f"Erro ao processar mensagem do agente: {str(e)}")
        return {
            "text": f"Desculpe, ocorreu um erro ao processar sua mensagem: {str(e)}",
            "error": str(e)
        }

def process_amigostudio_chat(message: str, data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Processa uma mensagem do chat do AmigoStudio Pro usando o Data Mesh.
    
    Args:
        message: Mensagem do usuário
        data: Dados disponíveis para análise
        
    Returns:
        Dict[str, Any]: Resposta processada
    """
    try:
        # Obter o serviço de agente
        agent_service = AgentService()
        
        # Carregar dados no Data Mesh, se ainda não foram carregados
        agent_service.load_data(data)
        
        # Obter o histórico de mensagens da sessão
        if 'messages' not in session:
            session['messages'] = []
        
        # Adicionar a mensagem do usuário ao histórico
        session['messages'].append({
            'role': 'user',
            'content': message
        })
        
        # Preparar o contexto da página
        page_context = {
            "current_module": "amigostudio",
            "page_title": "AmigoStudio Pro",
            "page_description": "Análise de dados com IA"
        }
        
        # Processar a consulta
        response = agent_service.process_query(message, page_context)
        
        # Adicionar a resposta ao histórico
        session['messages'].append({
            'role': 'assistant',
            'content': response["text"]
        })
        
        # Marcar a sessão como modificada
        session.modified = True
        
        return response
        
    except Exception as e:
        logger.error(f"Erro ao processar mensagem do AmigoStudio Pro: {str(e)}")
        return {
            "text": f"Desculpe, ocorreu um erro ao processar sua mensagem: {str(e)}",
            "error": str(e),
            "code": None
        }

def initialize_agent_with_data(data: Dict[str, Any]) -> None:
    """
    Inicializa o agente com os dados da aplicação.
    
    Args:
        data: Dados da aplicação
    """
    try:
        # Obter o serviço de agente
        agent_service = AgentService()
        
        # Carregar dados no Data Mesh
        agent_service.load_data(data)
        
        logger.info("Agente inicializado com dados da aplicação")
        
    except Exception as e:
        logger.error(f"Erro ao inicializar agente com dados: {str(e)}")

def get_domain_metrics(domain_name: str) -> Dict[str, Any]:
    """
    Obtém métricas de um domínio específico.
    
    Args:
        domain_name: Nome do domínio
        
    Returns:
        Dict[str, Any]: Métricas do domínio
    """
    try:
        # Obter o serviço de agente
        agent_service = AgentService()
        
        # Obter métricas do domínio
        return agent_service.get_domain_metrics(domain_name)
        
    except Exception as e:
        logger.error(f"Erro ao obter métricas do domínio {domain_name}: {str(e)}")
        return {}

def get_all_domain_metrics() -> Dict[str, Dict[str, Any]]:
    """
    Obtém métricas de todos os domínios.
    
    Returns:
        Dict[str, Dict[str, Any]]: Métricas de todos os domínios
    """
    try:
        # Obter o serviço de agente
        agent_service = AgentService()
        
        # Obter métricas de todos os domínios
        return agent_service.get_all_metrics()
        
    except Exception as e:
        logger.error(f"Erro ao obter métricas de todos os domínios: {str(e)}")
        return {}

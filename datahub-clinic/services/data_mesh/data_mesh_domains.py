"""
Definição dos domínios de dados para a arquitetura Data Mesh.

Este módulo define os domínios de negócio autônomos que compõem a arquitetura
Data Mesh da aplicação. Cada domínio é responsável por seus próprios dados e
fornece interfaces padronizadas para acesso.
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import logging

# Configurar logging
logger = logging.getLogger(__name__)

class DataMeshDomain(ABC):
    """Classe base para domínios de dados no Data Mesh."""
    
    def __init__(self, name: str, description: str):
        """
        Inicializa um domínio de dados.
        
        Args:
            name: Nome do domínio
            description: Descrição do domínio
        """
        self.name = name
        self.description = description
        self.metrics_cache = {}
        self.last_update = None
        self.update_interval = timedelta(hours=1)  # Atualizar métricas a cada hora
    
    @abstractmethod
    def load_data(self, raw_data: Dict[str, Any]) -> None:
        """
        Carrega dados brutos no domínio.
        
        Args:
            raw_data: Dados brutos da aplicação
        """
        pass
    
    @abstractmethod
    def get_metrics(self) -> Dict[str, Any]:
        """
        Retorna métricas anônimas do domínio.
        
        Returns:
            Dict[str, Any]: Métricas anônimas
        """
        pass
    
    @abstractmethod
    def get_contextual_data(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Retorna dados contextuais com base no contexto fornecido.
        
        Args:
            context: Contexto da consulta
            
        Returns:
            Dict[str, Any]: Dados contextuais anônimos
        """
        pass
    
    def should_update_metrics(self) -> bool:
        """
        Verifica se as métricas devem ser atualizadas.
        
        Returns:
            bool: True se as métricas devem ser atualizadas
        """
        if not self.last_update:
            return True
        
        return datetime.now() - self.last_update > self.update_interval
    
    def update_metrics_cache(self) -> None:
        """Atualiza o cache de métricas."""
        if self.should_update_metrics():
            self.metrics_cache = self._calculate_metrics()
            self.last_update = datetime.now()
            logger.info(f"Métricas do domínio {self.name} atualizadas")
    
    @abstractmethod
    def _calculate_metrics(self) -> Dict[str, Any]:
        """
        Calcula métricas do domínio.
        
        Returns:
            Dict[str, Any]: Métricas calculadas
        """
        pass
    
    def get_domain_schema(self) -> Dict[str, Any]:
        """
        Retorna o schema do domínio.
        
        Returns:
            Dict[str, Any]: Schema do domínio
        """
        return {
            "name": self.name,
            "description": self.description,
            "metrics": list(self.metrics_cache.keys()) if self.metrics_cache else [],
            "last_update": self.last_update.isoformat() if self.last_update else None
        }
    
    def anonymize_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Anonimiza dados para garantir privacidade.
        
        Args:
            data: Dados a serem anonimizados
            
        Returns:
            Dict[str, Any]: Dados anonimizados
        """
        # Implementação básica de anonimização
        anonymized = {}
        
        for key, value in data.items():
            # Remover campos sensíveis
            if key.lower() in ["nome", "paciente", "email", "telefone", "cpf", "rg", "endereco", 
                              "data_nascimento", "cns", "matricula_plano"]:
                continue
                
            # Anonimizar IDs
            if key.lower() in ["id", "paciente_id", "codigo"]:
                if isinstance(value, list):
                    anonymized[key] = [f"id_{i}" for i in range(len(value))]
                else:
                    anonymized[key] = f"id_{hash(str(value)) % 10000}"
                continue
            
            # Copiar valores não sensíveis
            anonymized[key] = value
            
        return anonymized
    
    def __str__(self) -> str:
        """Representação em string do domínio."""
        return f"DataMeshDomain({self.name})"
    
    def __repr__(self) -> str:
        """Representação oficial do domínio."""
        return self.__str__()


class DomainRegistry:
    """Registro central de domínios de dados."""
    
    _instance = None
    
    def __new__(cls):
        """Implementação de Singleton."""
        if cls._instance is None:
            cls._instance = super(DomainRegistry, cls).__new__(cls)
            cls._instance.domains = {}
        return cls._instance
    
    def register_domain(self, domain: DataMeshDomain) -> None:
        """
        Registra um domínio no registro.
        
        Args:
            domain: Domínio a ser registrado
        """
        self.domains[domain.name] = domain
        logger.info(f"Domínio {domain.name} registrado")
    
    def get_domain(self, name: str) -> Optional[DataMeshDomain]:
        """
        Obtém um domínio pelo nome.
        
        Args:
            name: Nome do domínio
            
        Returns:
            Optional[DataMeshDomain]: Domínio encontrado ou None
        """
        return self.domains.get(name)
    
    def get_all_domains(self) -> Dict[str, DataMeshDomain]:
        """
        Obtém todos os domínios registrados.
        
        Returns:
            Dict[str, DataMeshDomain]: Todos os domínios
        """
        return self.domains
    
    def load_data_to_all_domains(self, raw_data: Dict[str, Any]) -> None:
        """
        Carrega dados em todos os domínios registrados.
        
        Args:
            raw_data: Dados brutos da aplicação
        """
        for domain in self.domains.values():
            try:
                domain.load_data(raw_data)
                logger.info(f"Dados carregados no domínio {domain.name}")
            except Exception as e:
                logger.error(f"Erro ao carregar dados no domínio {domain.name}: {str(e)}")
    
    def get_all_metrics(self) -> Dict[str, Dict[str, Any]]:
        """
        Obtém métricas de todos os domínios.
        
        Returns:
            Dict[str, Dict[str, Any]]: Métricas de todos os domínios
        """
        metrics = {}
        for name, domain in self.domains.items():
            try:
                domain.update_metrics_cache()
                metrics[name] = domain.get_metrics()
            except Exception as e:
                logger.error(f"Erro ao obter métricas do domínio {name}: {str(e)}")
                metrics[name] = {"error": str(e)}
        
        return metrics
    
    def get_domain_schemas(self) -> List[Dict[str, Any]]:
        """
        Obtém schemas de todos os domínios.
        
        Returns:
            List[Dict[str, Any]]: Schemas de todos os domínios
        """
        return [domain.get_domain_schema() for domain in self.domains.values()]

"""
Domínio de Agenda no Data Mesh.

Este módulo implementa o domínio de Agenda, responsável por dados de agendamentos,
produção médica, tempo de atendimento e cancelamentos.
"""
from typing import Dict, List, Any, Optional
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from ..data_mesh_domains import DataMeshDomain

# Configurar logging
logger = logging.getLogger(__name__)

class AgendaDomain(DataMeshDomain):
    """Domínio de Agenda no Data Mesh."""
    
    def __init__(self):
        """Inicializa o domínio de Agenda."""
        super().__init__(
            name="agenda",
            description="Domínio responsável por dados de agendamentos, produção médica, tempo de atendimento e cancelamentos"
        )
        self.agendamentos = []
        self.producao_medica = []
        self.tempo_atendimento = []
        self.cancelamentos = []
        
        # Definir KPIs e métricas do domínio
        self.kpis = [
            "total_agendamentos",
            "taxa_ocupacao",
            "tempo_medio_atendimento",
            "taxa_cancelamento",
            "taxa_no_show",
            "distribuicao_por_dia_semana",
            "distribuicao_por_hora",
            "distribuicao_por_profissional",
            "distribuicao_por_procedimento",
            "distribuicao_por_status"
        ]
    
    def load_data(self, raw_data: Dict[str, Any]) -> None:
        """
        Carrega dados brutos no domínio de Agenda.
        
        Args:
            raw_data: Dados brutos da aplicação
        """
        if "agenda" not in raw_data:
            logger.warning("Dados de agenda não encontrados")
            return
        
        agenda_data = raw_data["agenda"]
        
        # Carregar agendamentos
        self.agendamentos = agenda_data.get("agendamentos", [])
        
        # Carregar produção médica
        self.producao_medica = agenda_data.get("producao_medica", [])
        
        # Carregar tempo de atendimento
        self.tempo_atendimento = agenda_data.get("tempo_atendimento", [])
        
        # Carregar cancelamentos
        self.cancelamentos = agenda_data.get("cancelamentos", [])
        
        # Atualizar métricas após carregar novos dados
        self.update_metrics_cache()
        
        logger.info(f"Dados carregados no domínio de Agenda: {len(self.agendamentos)} agendamentos")
    
    def _calculate_metrics(self) -> Dict[str, Any]:
        """
        Calcula métricas do domínio de Agenda.
        
        Returns:
            Dict[str, Any]: Métricas calculadas
        """
        metrics = {}
        
        # Total de agendamentos
        metrics["total_agendamentos"] = len(self.agendamentos)
        
        # Converter para DataFrame para facilitar análises
        if self.agendamentos:
            df = pd.DataFrame(self.agendamentos)
            
            # Taxa de ocupação (agendamentos realizados / total)
            if "status" in df.columns:
                realizados = df[df["status"] == "Realizado"].shape[0]
                metrics["taxa_ocupacao"] = round(realizados / len(df) * 100, 2) if len(df) > 0 else 0
                
                # Taxa de cancelamento
                cancelados = df[df["status"] == "Cancelado"].shape[0]
                metrics["taxa_cancelamento"] = round(cancelados / len(df) * 100, 2) if len(df) > 0 else 0
                
                # Taxa de no-show
                no_show = df[df["status"] == "Faltou"].shape[0]
                metrics["taxa_no_show"] = round(no_show / len(df) * 100, 2) if len(df) > 0 else 0
                
                # Distribuição por status
                status_counts = df["status"].value_counts().to_dict()
                metrics["distribuicao_por_status"] = {
                    status: {"count": count, "percentage": round(count / len(df) * 100, 2)}
                    for status, count in status_counts.items()
                }
            
            # Tempo médio de atendimento
            if self.tempo_atendimento:
                tempo_df = pd.DataFrame(self.tempo_atendimento)
                if "duracao" in tempo_df.columns:
                    metrics["tempo_medio_atendimento"] = round(tempo_df["duracao"].mean(), 2)
            
            # Distribuição por dia da semana
            if "data" in df.columns:
                # Converter string de data para datetime
                try:
                    df["data_dt"] = pd.to_datetime(df["data"], format="%d/%m/%Y", errors="coerce")
                    df["dia_semana"] = df["data_dt"].dt.day_name()
                    dia_semana_counts = df["dia_semana"].value_counts().to_dict()
                    metrics["distribuicao_por_dia_semana"] = {
                        dia: {"count": count, "percentage": round(count / len(df) * 100, 2)}
                        for dia, count in dia_semana_counts.items()
                    }
                except Exception as e:
                    logger.error(f"Erro ao calcular distribuição por dia da semana: {str(e)}")
            
            # Distribuição por hora
            if "hora" in df.columns:
                try:
                    hora_counts = df["hora"].str.split(":", expand=True)[0].value_counts().to_dict()
                    metrics["distribuicao_por_hora"] = {
                        hora: {"count": count, "percentage": round(count / len(df) * 100, 2)}
                        for hora, count in hora_counts.items()
                    }
                except Exception as e:
                    logger.error(f"Erro ao calcular distribuição por hora: {str(e)}")
            
            # Distribuição por profissional
            if "profissional" in df.columns:
                prof_counts = df["profissional"].value_counts().to_dict()
                # Limitar a 10 profissionais mais frequentes para não expor todos os dados
                top_profs = {k: prof_counts[k] for k in list(prof_counts.keys())[:10]}
                metrics["distribuicao_por_profissional"] = {
                    f"profissional_{i}": {"count": count, "percentage": round(count / len(df) * 100, 2)}
                    for i, (_, count) in enumerate(top_profs.items())
                }
            
            # Distribuição por procedimento
            if "procedimento" in df.columns:
                proc_counts = df["procedimento"].value_counts().to_dict()
                # Limitar a 10 procedimentos mais frequentes
                top_procs = {k: proc_counts[k] for k in list(proc_counts.keys())[:10]}
                metrics["distribuicao_por_procedimento"] = {
                    proc: {"count": count, "percentage": round(count / len(df) * 100, 2)}
                    for proc, count in top_procs.items()
                }
        
        return metrics
    
    def get_metrics(self) -> Dict[str, Any]:
        """
        Retorna métricas anônimas do domínio de Agenda.
        
        Returns:
            Dict[str, Any]: Métricas anônimas
        """
        # Atualizar métricas se necessário
        self.update_metrics_cache()
        return self.metrics_cache
    
    def get_contextual_data(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Retorna dados contextuais com base no contexto fornecido.
        
        Args:
            context: Contexto da consulta
            
        Returns:
            Dict[str, Any]: Dados contextuais anônimos
        """
        result = {}
        
        # Filtrar por período
        if "periodo" in context:
            periodo = context["periodo"]
            if periodo == "hoje":
                hoje = datetime.now().strftime("%d/%m/%Y")
                agendamentos_hoje = [a for a in self.agendamentos if a.get("data") == hoje]
                result["agendamentos_periodo"] = self._get_agendamentos_anonimos(agendamentos_hoje)
            elif periodo == "semana":
                # Implementar filtro para a semana atual
                pass
            elif periodo == "mes":
                # Implementar filtro para o mês atual
                pass
        
        # Filtrar por profissional (usando ID anônimo)
        if "profissional" in context:
            prof = context["profissional"]
            agendamentos_prof = [a for a in self.agendamentos if a.get("profissional") == prof]
            result["agendamentos_profissional"] = self._get_agendamentos_anonimos(agendamentos_prof)
        
        # Filtrar por procedimento
        if "procedimento" in context:
            proc = context["procedimento"]
            agendamentos_proc = [a for a in self.agendamentos if a.get("procedimento") == proc]
            result["agendamentos_procedimento"] = self._get_agendamentos_anonimos(agendamentos_proc)
        
        # Filtrar por status
        if "status" in context:
            status = context["status"]
            agendamentos_status = [a for a in self.agendamentos if a.get("status") == status]
            result["agendamentos_status"] = self._get_agendamentos_anonimos(agendamentos_status)
        
        # Adicionar métricas relevantes ao contexto
        result["metricas"] = self.get_metrics()
        
        return result
    
    def _get_agendamentos_anonimos(self, agendamentos: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Retorna versão anônima dos agendamentos.
        
        Args:
            agendamentos: Lista de agendamentos
            
        Returns:
            List[Dict[str, Any]]: Agendamentos anonimizados
        """
        return [self.anonymize_data(a) for a in agendamentos]

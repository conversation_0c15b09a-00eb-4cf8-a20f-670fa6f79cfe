"""
Domínio AmigoCare+ no Data Mesh.

Este módulo implementa o domínio AmigoCare+, responsável por dados de avaliação NPS,
leads, campanhas, acompanhamento de pacientes e indicadores de qualidade.
"""
from typing import Dict, List, Any, Optional
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from ..data_mesh_domains import DataMeshDomain

# Configurar logging
logger = logging.getLogger(__name__)

class AmigoCareDomain(DataMeshDomain):
    """Domínio AmigoCare+ no Data Mesh."""
    
    def __init__(self):
        """Inicializa o domínio AmigoCare+."""
        super().__init__(
            name="amigocare",
            description="Domínio responsável por dados de avaliação NPS, leads, campanhas, acompanhamento de pacientes e indicadores de qualidade"
        )
        self.avaliacao_nps = []
        self.leads = []
        self.campanhas = []
        self.acompanhamento_pacientes = []
        self.indicadores_qualidade = []
        
        # Definir KPIs e métricas do domínio
        self.kpis = [
            "nps_medio",
            "distribuicao_nps",
            "total_leads",
            "taxa_conversao_leads",
            "custo_aquisicao_cliente",
            "roi_campanhas",
            "leads_por_origem",
            "leads_por_status",
            "eficacia_tratamentos",
            "satisfacao_por_profissional",
            "indicadores_qualidade_media"
        ]
    
    def load_data(self, raw_data: Dict[str, Any]) -> None:
        """
        Carrega dados brutos no domínio AmigoCare+.
        
        Args:
            raw_data: Dados brutos da aplicação
        """
        if "amigocare" not in raw_data:
            logger.warning("Dados de AmigoCare+ não encontrados")
            return
        
        amigocare_data = raw_data["amigocare"]
        
        # Carregar avaliação NPS
        self.avaliacao_nps = amigocare_data.get("avaliacao_nps", [])
        
        # Carregar leads
        self.leads = amigocare_data.get("leads", [])
        
        # Carregar campanhas
        self.campanhas = amigocare_data.get("campanhas", [])
        
        # Carregar acompanhamento de pacientes
        self.acompanhamento_pacientes = amigocare_data.get("acompanhamento_pacientes", [])
        
        # Carregar indicadores de qualidade
        self.indicadores_qualidade = amigocare_data.get("indicadores_qualidade", [])
        
        # Atualizar métricas após carregar novos dados
        self.update_metrics_cache()
        
        logger.info(f"Dados carregados no domínio AmigoCare+: {len(self.avaliacao_nps)} avaliações NPS, {len(self.leads)} leads")
    
    def _calculate_metrics(self) -> Dict[str, Any]:
        """
        Calcula métricas do domínio AmigoCare+.
        
        Returns:
            Dict[str, Any]: Métricas calculadas
        """
        metrics = {}
        
        # NPS médio
        if self.avaliacao_nps:
            df_nps = pd.DataFrame(self.avaliacao_nps)
            if "score" in df_nps.columns:
                metrics["nps_medio"] = round(df_nps["score"].mean(), 2)
                
                # Distribuição NPS (Detratores, Neutros, Promotores)
                detratores = df_nps[df_nps["score"] <= 6].shape[0]
                neutros = df_nps[(df_nps["score"] > 6) & (df_nps["score"] < 9)].shape[0]
                promotores = df_nps[df_nps["score"] >= 9].shape[0]
                
                total_nps = len(df_nps)
                metrics["distribuicao_nps"] = {
                    "detratores": {
                        "count": detratores,
                        "percentage": round(detratores / total_nps * 100, 2) if total_nps > 0 else 0
                    },
                    "neutros": {
                        "count": neutros,
                        "percentage": round(neutros / total_nps * 100, 2) if total_nps > 0 else 0
                    },
                    "promotores": {
                        "count": promotores,
                        "percentage": round(promotores / total_nps * 100, 2) if total_nps > 0 else 0
                    }
                }
                
                # Cálculo do NPS (% promotores - % detratores)
                metrics["nps_score"] = round(
                    metrics["distribuicao_nps"]["promotores"]["percentage"] - 
                    metrics["distribuicao_nps"]["detratores"]["percentage"], 
                    2
                )
                
                # Satisfação por categoria
                if "categoria" in df_nps.columns:
                    categoria_scores = df_nps.groupby("categoria")["score"].mean().to_dict()
                    metrics["satisfacao_por_categoria"] = {
                        categoria: round(score, 2)
                        for categoria, score in categoria_scores.items()
                    }
                
                # Satisfação por profissional (anonimizado)
                if "profissional" in df_nps.columns:
                    prof_scores = df_nps.groupby("profissional")["score"].mean().to_dict()
                    metrics["satisfacao_por_profissional"] = {
                        f"profissional_{i}": round(score, 2)
                        for i, (_, score) in enumerate(prof_scores.items())
                    }
        
        # Métricas de leads
        if self.leads:
            df_leads = pd.DataFrame(self.leads)
            
            # Total de leads
            metrics["total_leads"] = len(df_leads)
            
            # Leads por origem
            if "origem" in df_leads.columns:
                origem_counts = df_leads["origem"].value_counts().to_dict()
                metrics["leads_por_origem"] = {
                    origem: {
                        "count": count,
                        "percentage": round(count / len(df_leads) * 100, 2)
                    }
                    for origem, count in origem_counts.items()
                }
            
            # Leads por status
            if "status" in df_leads.columns:
                status_counts = df_leads["status"].value_counts().to_dict()
                metrics["leads_por_status"] = {
                    status: {
                        "count": count,
                        "percentage": round(count / len(df_leads) * 100, 2)
                    }
                    for status, count in status_counts.items()
                }
                
                # Taxa de conversão de leads
                if "Convertido" in status_counts:
                    metrics["taxa_conversao_leads"] = round(status_counts["Convertido"] / len(df_leads) * 100, 2)
                else:
                    metrics["taxa_conversao_leads"] = 0
            
            # Valor potencial médio
            if "valor_potencial" in df_leads.columns:
                metrics["valor_potencial_medio"] = round(df_leads["valor_potencial"].mean(), 2)
        
        # Métricas de campanhas
        if self.campanhas:
            df_campanhas = pd.DataFrame(self.campanhas)
            
            # ROI médio de campanhas
            if "roi" in df_campanhas.columns:
                metrics["roi_campanhas"] = round(df_campanhas["roi"].mean(), 2)
            
            # Custo de aquisição de cliente
            if "investimento" in df_campanhas.columns and "leads_convertidos" in df_campanhas.columns:
                total_investimento = df_campanhas["investimento"].sum()
                total_convertidos = df_campanhas["leads_convertidos"].sum()
                
                if total_convertidos > 0:
                    metrics["custo_aquisicao_cliente"] = round(total_investimento / total_convertidos, 2)
            
            # Campanhas por tipo
            if "tipo" in df_campanhas.columns:
                tipo_counts = df_campanhas["tipo"].value_counts().to_dict()
                metrics["campanhas_por_tipo"] = {
                    tipo: {
                        "count": count,
                        "percentage": round(count / len(df_campanhas) * 100, 2)
                    }
                    for tipo, count in tipo_counts.items()
                }
        
        # Métricas de acompanhamento de pacientes
        if self.acompanhamento_pacientes:
            df_acomp = pd.DataFrame(self.acompanhamento_pacientes)
            
            # Eficácia dos tratamentos
            if "resultado" in df_acomp.columns:
                resultado_counts = df_acomp["resultado"].value_counts().to_dict()
                metrics["eficacia_tratamentos"] = {
                    resultado: {
                        "count": count,
                        "percentage": round(count / len(df_acomp) * 100, 2) if len(df_acomp) > 0 else 0
                    }
                    for resultado, count in resultado_counts.items()
                }
                
                # Percentual de resultados excelentes/bons
                bons_resultados = df_acomp[df_acomp["resultado"].isin(["Excelente", "Bom"])].shape[0]
                metrics["percentual_bons_resultados"] = round(bons_resultados / len(df_acomp) * 100, 2) if len(df_acomp) > 0 else 0
            
            # Distribuição por tipo de tratamento
            if "tipo_tratamento" in df_acomp.columns:
                tipo_counts = df_acomp["tipo_tratamento"].value_counts().to_dict()
                metrics["distribuicao_por_tipo_tratamento"] = {
                    tipo: {
                        "count": count,
                        "percentage": round(count / len(df_acomp) * 100, 2)
                    }
                    for tipo, count in tipo_counts.items()
                }
        
        # Métricas de indicadores de qualidade
        if self.indicadores_qualidade:
            df_qualidade = pd.DataFrame(self.indicadores_qualidade)
            
            # Média dos indicadores de qualidade
            if "valor" in df_qualidade.columns and "indicador" in df_qualidade.columns:
                indicador_medias = df_qualidade.groupby("indicador")["valor"].mean().to_dict()
                metrics["indicadores_qualidade_media"] = {
                    indicador: round(valor, 2)
                    for indicador, valor in indicador_medias.items()
                }
        
        return metrics
    
    def get_metrics(self) -> Dict[str, Any]:
        """
        Retorna métricas anônimas do domínio AmigoCare+.
        
        Returns:
            Dict[str, Any]: Métricas anônimas
        """
        # Atualizar métricas se necessário
        self.update_metrics_cache()
        return self.metrics_cache
    
    def get_contextual_data(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Retorna dados contextuais com base no contexto fornecido.
        
        Args:
            context: Contexto da consulta
            
        Returns:
            Dict[str, Any]: Dados contextuais anônimos
        """
        result = {}
        
        # Filtrar por período
        if "periodo" in context:
            periodo = context["periodo"]
            if periodo == "mes_atual":
                # Implementar filtro para o mês atual
                pass
            elif periodo == "trimestre":
                # Implementar filtro para o trimestre atual
                pass
            elif periodo == "ano":
                # Implementar filtro para o ano atual
                pass
        
        # Filtrar por origem de lead
        if "origem_lead" in context:
            origem = context["origem_lead"]
            leads_origem = [l for l in self.leads if l.get("origem") == origem]
            result["leads_origem"] = [self.anonymize_data(l) for l in leads_origem]
        
        # Filtrar por tipo de campanha
        if "tipo_campanha" in context:
            tipo = context["tipo_campanha"]
            campanhas_tipo = [c for c in self.campanhas if c.get("tipo") == tipo]
            result["campanhas_tipo"] = [self.anonymize_data(c) for c in campanhas_tipo]
        
        # Filtrar por categoria de NPS
        if "categoria_nps" in context:
            categoria = context["categoria_nps"]
            nps_categoria = [n for n in self.avaliacao_nps if n.get("categoria") == categoria]
            result["nps_categoria"] = [self.anonymize_data(n) for n in nps_categoria]
        
        # Adicionar métricas relevantes ao contexto
        result["metricas"] = self.get_metrics()
        
        return result

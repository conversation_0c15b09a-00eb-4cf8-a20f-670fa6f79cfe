"""
Domínio AmigoStudio no Data Mesh.

Este módulo implementa o domínio AmigoStudio, responsável por análises com IA
e visualizações de dados.
"""
from typing import Dict, List, Any, Optional
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import json
from ..data_mesh_domains import DataMeshDomain

# Configurar logging
logger = logging.getLogger(__name__)

class AmigoStudioDomain(DataMeshDomain):
    """Domínio AmigoStudio no Data Mesh."""
    
    def __init__(self):
        """Inicializa o domínio AmigoStudio."""
        super().__init__(
            name="amigostudio",
            description="Domínio responsável por análises com IA e visualizações de dados"
        )
        self.analises_salvas = []
        self.visualizacoes_salvas = []
        self.consultas_frequentes = []
        self.modelos_treinados = []
        self.datasets_processados = []
        
        # Definir KPIs e métricas do domínio
        self.kpis = [
            "total_analises",
            "total_visualizacoes",
            "consultas_populares",
            "tipos_analise_frequentes",
            "tempo_medio_execucao",
            "precisao_modelos",
            "uso_por_usuario",
            "tendencias_consulta"
        ]
    
    def load_data(self, raw_data: Dict[str, Any]) -> None:
        """
        Carrega dados brutos no domínio AmigoStudio.
        
        Args:
            raw_data: Dados brutos da aplicação
        """
        # AmigoStudio pode não ter dados diretos no raw_data
        # Vamos extrair informações relevantes de outros domínios
        
        # Carregar análises salvas (se existirem)
        if "amigostudio" in raw_data and "analises_salvas" in raw_data["amigostudio"]:
            self.analises_salvas = raw_data["amigostudio"]["analises_salvas"]
        
        # Carregar visualizações salvas (se existirem)
        if "amigostudio" in raw_data and "visualizacoes_salvas" in raw_data["amigostudio"]:
            self.visualizacoes_salvas = raw_data["amigostudio"]["visualizacoes_salvas"]
        
        # Carregar consultas frequentes (se existirem)
        if "amigostudio" in raw_data and "consultas_frequentes" in raw_data["amigostudio"]:
            self.consultas_frequentes = raw_data["amigostudio"]["consultas_frequentes"]
        
        # Carregar modelos treinados (se existirem)
        if "amigostudio" in raw_data and "modelos_treinados" in raw_data["amigostudio"]:
            self.modelos_treinados = raw_data["amigostudio"]["modelos_treinados"]
        
        # Carregar datasets processados (se existirem)
        if "amigostudio" in raw_data and "datasets_processados" in raw_data["amigostudio"]:
            self.datasets_processados = raw_data["amigostudio"]["datasets_processados"]
        
        # Se não houver dados específicos, criar dados simulados para métricas
        if not self.analises_salvas:
            self._create_simulated_data()
        
        # Atualizar métricas após carregar novos dados
        self.update_metrics_cache()
        
        logger.info(f"Dados carregados no domínio AmigoStudio: {len(self.analises_salvas)} análises salvas")
    
    def _create_simulated_data(self):
        """Cria dados simulados para o domínio AmigoStudio."""
        # Análises salvas simuladas
        self.analises_salvas = [
            {
                "id": f"analise_{i}",
                "titulo": f"Análise {i}",
                "tipo": np.random.choice(["previsão", "classificação", "agrupamento", "regressão", "série temporal"]),
                "dominio": np.random.choice(["agenda", "financeiro", "paciente", "amigocare", "visao360"]),
                "data_criacao": (datetime.now() - timedelta(days=np.random.randint(1, 180))).strftime("%Y-%m-%d"),
                "tempo_execucao": np.random.randint(1, 60),
                "usuario": f"usuario_{np.random.randint(1, 10)}",
                "metricas_desempenho": {
                    "precisao": round(np.random.uniform(0.7, 0.99), 2),
                    "recall": round(np.random.uniform(0.7, 0.99), 2),
                    "f1": round(np.random.uniform(0.7, 0.99), 2)
                }
            }
            for i in range(1, 21)  # 20 análises simuladas
        ]
        
        # Visualizações salvas simuladas
        self.visualizacoes_salvas = [
            {
                "id": f"viz_{i}",
                "titulo": f"Visualização {i}",
                "tipo": np.random.choice(["gráfico de barras", "gráfico de linha", "gráfico de pizza", "heatmap", "scatter plot"]),
                "dominio": np.random.choice(["agenda", "financeiro", "paciente", "amigocare", "visao360"]),
                "data_criacao": (datetime.now() - timedelta(days=np.random.randint(1, 180))).strftime("%Y-%m-%d"),
                "usuario": f"usuario_{np.random.randint(1, 10)}",
                "visualizacoes": np.random.randint(1, 100)
            }
            for i in range(1, 31)  # 30 visualizações simuladas
        ]
        
        # Consultas frequentes simuladas
        self.consultas_frequentes = [
            {
                "id": f"consulta_{i}",
                "texto": f"Consulta {i}",
                "dominio": np.random.choice(["agenda", "financeiro", "paciente", "amigocare", "visao360"]),
                "frequencia": np.random.randint(1, 50),
                "ultima_execucao": (datetime.now() - timedelta(days=np.random.randint(1, 30))).strftime("%Y-%m-%d")
            }
            for i in range(1, 16)  # 15 consultas simuladas
        ]
    
    def _calculate_metrics(self) -> Dict[str, Any]:
        """
        Calcula métricas do domínio AmigoStudio.
        
        Returns:
            Dict[str, Any]: Métricas calculadas
        """
        metrics = {}
        
        # Total de análises
        metrics["total_analises"] = len(self.analises_salvas)
        
        # Total de visualizações
        metrics["total_visualizacoes"] = len(self.visualizacoes_salvas)
        
        # Análises por tipo
        if self.analises_salvas:
            df_analises = pd.DataFrame(self.analises_salvas)
            
            if "tipo" in df_analises.columns:
                tipo_counts = df_analises["tipo"].value_counts().to_dict()
                metrics["tipos_analise_frequentes"] = {
                    tipo: {
                        "count": count,
                        "percentage": round(count / len(df_analises) * 100, 2)
                    }
                    for tipo, count in tipo_counts.items()
                }
            
            # Tempo médio de execução
            if "tempo_execucao" in df_analises.columns:
                metrics["tempo_medio_execucao"] = round(df_analises["tempo_execucao"].mean(), 2)
            
            # Precisão média dos modelos
            if "metricas_desempenho" in df_analises.columns:
                try:
                    precisoes = [a.get("metricas_desempenho", {}).get("precisao", 0) for a in self.analises_salvas]
                    if precisoes:
                        metrics["precisao_media_modelos"] = round(sum(precisoes) / len(precisoes), 2)
                except Exception as e:
                    logger.error(f"Erro ao calcular precisão média: {str(e)}")
            
            # Análises por domínio
            if "dominio" in df_analises.columns:
                dominio_counts = df_analises["dominio"].value_counts().to_dict()
                metrics["analises_por_dominio"] = {
                    dominio: {
                        "count": count,
                        "percentage": round(count / len(df_analises) * 100, 2)
                    }
                    for dominio, count in dominio_counts.items()
                }
        
        # Visualizações por tipo
        if self.visualizacoes_salvas:
            df_viz = pd.DataFrame(self.visualizacoes_salvas)
            
            if "tipo" in df_viz.columns:
                tipo_counts = df_viz["tipo"].value_counts().to_dict()
                metrics["tipos_visualizacao_frequentes"] = {
                    tipo: {
                        "count": count,
                        "percentage": round(count / len(df_viz) * 100, 2)
                    }
                    for tipo, count in tipo_counts.items()
                }
            
            # Visualizações por domínio
            if "dominio" in df_viz.columns:
                dominio_counts = df_viz["dominio"].value_counts().to_dict()
                metrics["visualizacoes_por_dominio"] = {
                    dominio: {
                        "count": count,
                        "percentage": round(count / len(df_viz) * 100, 2)
                    }
                    for dominio, count in dominio_counts.items()
                }
        
        # Consultas populares
        if self.consultas_frequentes:
            df_consultas = pd.DataFrame(self.consultas_frequentes)
            
            if "frequencia" in df_consultas.columns:
                # Top 5 consultas mais frequentes
                top_consultas = df_consultas.nlargest(5, "frequencia")
                metrics["consultas_populares"] = [
                    {
                        "texto": row["texto"],
                        "frequencia": row["frequencia"],
                        "dominio": row["dominio"]
                    }
                    for _, row in top_consultas.iterrows()
                ]
            
            # Consultas por domínio
            if "dominio" in df_consultas.columns:
                dominio_counts = df_consultas["dominio"].value_counts().to_dict()
                metrics["consultas_por_dominio"] = {
                    dominio: {
                        "count": count,
                        "percentage": round(count / len(df_consultas) * 100, 2)
                    }
                    for dominio, count in dominio_counts.items()
                }
        
        # Uso por usuário (anonimizado)
        usuarios = {}
        
        # Contar análises por usuário
        if self.analises_salvas and "usuario" in self.analises_salvas[0]:
            for analise in self.analises_salvas:
                usuario = analise["usuario"]
                if usuario not in usuarios:
                    usuarios[usuario] = {"analises": 0, "visualizacoes": 0}
                usuarios[usuario]["analises"] += 1
        
        # Contar visualizações por usuário
        if self.visualizacoes_salvas and "usuario" in self.visualizacoes_salvas[0]:
            for viz in self.visualizacoes_salvas:
                usuario = viz["usuario"]
                if usuario not in usuarios:
                    usuarios[usuario] = {"analises": 0, "visualizacoes": 0}
                usuarios[usuario]["visualizacoes"] += 1
        
        # Anonimizar usuários
        metrics["uso_por_usuario"] = {
            f"usuario_{i}": dados
            for i, (_, dados) in enumerate(usuarios.items())
        }
        
        return metrics
    
    def get_metrics(self) -> Dict[str, Any]:
        """
        Retorna métricas anônimas do domínio AmigoStudio.
        
        Returns:
            Dict[str, Any]: Métricas anônimas
        """
        # Atualizar métricas se necessário
        self.update_metrics_cache()
        return self.metrics_cache
    
    def get_contextual_data(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Retorna dados contextuais com base no contexto fornecido.
        
        Args:
            context: Contexto da consulta
            
        Returns:
            Dict[str, Any]: Dados contextuais anônimos
        """
        result = {}
        
        # Filtrar por tipo de análise
        if "tipo_analise" in context:
            tipo = context["tipo_analise"]
            analises_tipo = [a for a in self.analises_salvas if a.get("tipo") == tipo]
            result["analises_tipo"] = [self.anonymize_data(a) for a in analises_tipo]
        
        # Filtrar por tipo de visualização
        if "tipo_visualizacao" in context:
            tipo = context["tipo_visualizacao"]
            viz_tipo = [v for v in self.visualizacoes_salvas if v.get("tipo") == tipo]
            result["visualizacoes_tipo"] = [self.anonymize_data(v) for v in viz_tipo]
        
        # Filtrar por domínio
        if "dominio" in context:
            dominio = context["dominio"]
            
            # Análises do domínio
            analises_dominio = [a for a in self.analises_salvas if a.get("dominio") == dominio]
            result["analises_dominio"] = [self.anonymize_data(a) for a in analises_dominio]
            
            # Visualizações do domínio
            viz_dominio = [v for v in self.visualizacoes_salvas if v.get("dominio") == dominio]
            result["visualizacoes_dominio"] = [self.anonymize_data(v) for v in viz_dominio]
            
            # Consultas do domínio
            consultas_dominio = [c for c in self.consultas_frequentes if c.get("dominio") == dominio]
            result["consultas_dominio"] = [self.anonymize_data(c) for c in consultas_dominio]
        
        # Adicionar métricas relevantes ao contexto
        result["metricas"] = self.get_metrics()
        
        return result

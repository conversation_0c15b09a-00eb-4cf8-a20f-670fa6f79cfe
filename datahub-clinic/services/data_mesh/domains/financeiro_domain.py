"""
Domínio Financeiro no Data Mesh.

Este módulo implementa o domínio Financeiro, responsável por dados de contas a receber,
contas a pagar, fluxo de caixa e fechamento de caixa.
"""
from typing import Dict, List, Any, Optional
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from ..data_mesh_domains import DataMeshDomain

# Configurar logging
logger = logging.getLogger(__name__)

class FinanceiroDomain(DataMeshDomain):
    """Domínio Financeiro no Data Mesh."""
    
    def __init__(self):
        """Inicializa o domínio Financeiro."""
        super().__init__(
            name="financeiro",
            description="Domínio responsável por dados de contas a receber, contas a pagar, fluxo de caixa e fechamento de caixa"
        )
        self.contas_receber = []
        self.contas_pagar = []
        self.fluxo_caixa = []
        self.fechamento_caixa = []
        
        # Definir KPIs e métricas do domínio
        self.kpis = [
            "total_receita",
            "total_despesa",
            "resultado_liquido",
            "taxa_inadimplencia",
            "ticket_medio",
            "distribuicao_por_forma_pagamento",
            "tendencia_receita",
            "tendencia_despesa",
            "previsao_fluxo_caixa",
            "distribuicao_por_categoria_despesa"
        ]
    
    def load_data(self, raw_data: Dict[str, Any]) -> None:
        """
        Carrega dados brutos no domínio Financeiro.
        
        Args:
            raw_data: Dados brutos da aplicação
        """
        if "financeiro" not in raw_data:
            logger.warning("Dados financeiros não encontrados")
            return
        
        financeiro_data = raw_data["financeiro"]
        
        # Carregar contas a receber
        self.contas_receber = financeiro_data.get("contas_receber", [])
        
        # Carregar contas a pagar
        self.contas_pagar = financeiro_data.get("contas_pagar", [])
        
        # Carregar fluxo de caixa
        self.fluxo_caixa = financeiro_data.get("fluxo_caixa", [])
        
        # Carregar fechamento de caixa
        self.fechamento_caixa = financeiro_data.get("fechamento_caixa", [])
        
        # Atualizar métricas após carregar novos dados
        self.update_metrics_cache()
        
        logger.info(f"Dados carregados no domínio Financeiro: {len(self.contas_receber)} contas a receber, {len(self.contas_pagar)} contas a pagar")
    
    def _calculate_metrics(self) -> Dict[str, Any]:
        """
        Calcula métricas do domínio Financeiro.
        
        Returns:
            Dict[str, Any]: Métricas calculadas
        """
        metrics = {}
        
        # Total de receita (contas a receber)
        if self.contas_receber:
            df_receber = pd.DataFrame(self.contas_receber)
            if "valor" in df_receber.columns:
                metrics["total_receita"] = round(df_receber["valor"].sum(), 2)
                
                # Ticket médio
                metrics["ticket_medio"] = round(df_receber["valor"].mean(), 2)
                
                # Taxa de inadimplência
                if "status" in df_receber.columns:
                    atrasados = df_receber[df_receber["status"] == "Atrasado"]
                    metrics["taxa_inadimplencia"] = round(atrasados["valor"].sum() / df_receber["valor"].sum() * 100, 2) if df_receber["valor"].sum() > 0 else 0
                    
                    # Distribuição por status
                    status_values = df_receber.groupby("status")["valor"].sum().to_dict()
                    metrics["distribuicao_por_status_receita"] = {
                        status: {
                            "valor": round(valor, 2),
                            "percentual": round(valor / df_receber["valor"].sum() * 100, 2) if df_receber["valor"].sum() > 0 else 0
                        }
                        for status, valor in status_values.items()
                    }
                
                # Distribuição por forma de pagamento
                if "forma_pagamento" in df_receber.columns:
                    forma_values = df_receber.groupby("forma_pagamento")["valor"].sum().to_dict()
                    metrics["distribuicao_por_forma_pagamento"] = {
                        forma: {
                            "valor": round(valor, 2),
                            "percentual": round(valor / df_receber["valor"].sum() * 100, 2) if df_receber["valor"].sum() > 0 else 0
                        }
                        for forma, valor in forma_values.items()
                    }
        
        # Total de despesa (contas a pagar)
        if self.contas_pagar:
            df_pagar = pd.DataFrame(self.contas_pagar)
            if "valor" in df_pagar.columns:
                metrics["total_despesa"] = round(df_pagar["valor"].sum(), 2)
                
                # Distribuição por categoria de despesa
                if "categoria" in df_pagar.columns:
                    categoria_values = df_pagar.groupby("categoria")["valor"].sum().to_dict()
                    metrics["distribuicao_por_categoria_despesa"] = {
                        categoria: {
                            "valor": round(valor, 2),
                            "percentual": round(valor / df_pagar["valor"].sum() * 100, 2) if df_pagar["valor"].sum() > 0 else 0
                        }
                        for categoria, valor in categoria_values.items()
                    }
        
        # Resultado líquido
        if "total_receita" in metrics and "total_despesa" in metrics:
            metrics["resultado_liquido"] = round(metrics["total_receita"] - metrics["total_despesa"], 2)
        
        # Tendência de receita e despesa (fluxo de caixa)
        if self.fluxo_caixa:
            df_fluxo = pd.DataFrame(self.fluxo_caixa)
            if "mes" in df_fluxo.columns and "receita" in df_fluxo.columns and "despesa" in df_fluxo.columns:
                # Ordenar por mês
                meses_ordem = ["Janeiro", "Fevereiro", "Março", "Abril", "Maio", "Junho", 
                              "Julho", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro"]
                df_fluxo["mes_ordem"] = df_fluxo["mes"].apply(lambda x: meses_ordem.index(x) if x in meses_ordem else -1)
                df_fluxo = df_fluxo.sort_values("mes_ordem")
                
                # Tendência de receita
                metrics["tendencia_receita"] = {
                    "meses": df_fluxo["mes"].tolist(),
                    "valores": df_fluxo["receita"].tolist()
                }
                
                # Tendência de despesa
                metrics["tendencia_despesa"] = {
                    "meses": df_fluxo["mes"].tolist(),
                    "valores": df_fluxo["despesa"].tolist()
                }
                
                # Previsão de fluxo de caixa (próximos 3 meses)
                # Implementação simplificada usando média móvel
                if len(df_fluxo) >= 3:
                    ultima_receita = df_fluxo["receita"].iloc[-3:].mean()
                    ultima_despesa = df_fluxo["despesa"].iloc[-3:].mean()
                    
                    # Crescimento médio
                    if len(df_fluxo) >= 6:
                        crescimento_receita = (df_fluxo["receita"].iloc[-3:].mean() / df_fluxo["receita"].iloc[-6:-3].mean() - 1) if df_fluxo["receita"].iloc[-6:-3].mean() > 0 else 0
                        crescimento_despesa = (df_fluxo["despesa"].iloc[-3:].mean() / df_fluxo["despesa"].iloc[-6:-3].mean() - 1) if df_fluxo["despesa"].iloc[-6:-3].mean() > 0 else 0
                    else:
                        crescimento_receita = 0.05  # Crescimento padrão de 5%
                        crescimento_despesa = 0.03  # Crescimento padrão de 3%
                    
                    # Próximos 3 meses
                    proximos_meses = []
                    ultimo_mes_idx = df_fluxo["mes_ordem"].iloc[-1]
                    
                    for i in range(1, 4):
                        mes_idx = (ultimo_mes_idx + i) % 12
                        mes = meses_ordem[mes_idx]
                        receita_prevista = ultima_receita * (1 + crescimento_receita) ** i
                        despesa_prevista = ultima_despesa * (1 + crescimento_despesa) ** i
                        
                        proximos_meses.append({
                            "mes": mes,
                            "receita": round(receita_prevista, 2),
                            "despesa": round(despesa_prevista, 2),
                            "resultado": round(receita_prevista - despesa_prevista, 2)
                        })
                    
                    metrics["previsao_fluxo_caixa"] = proximos_meses
        
        return metrics
    
    def get_metrics(self) -> Dict[str, Any]:
        """
        Retorna métricas anônimas do domínio Financeiro.
        
        Returns:
            Dict[str, Any]: Métricas anônimas
        """
        # Atualizar métricas se necessário
        self.update_metrics_cache()
        return self.metrics_cache
    
    def get_contextual_data(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Retorna dados contextuais com base no contexto fornecido.
        
        Args:
            context: Contexto da consulta
            
        Returns:
            Dict[str, Any]: Dados contextuais anônimos
        """
        result = {}
        
        # Filtrar por período
        if "periodo" in context:
            periodo = context["periodo"]
            if periodo == "mes_atual":
                # Implementar filtro para o mês atual
                pass
            elif periodo == "trimestre":
                # Implementar filtro para o trimestre atual
                pass
            elif periodo == "ano":
                # Implementar filtro para o ano atual
                pass
        
        # Filtrar por categoria de despesa
        if "categoria_despesa" in context:
            categoria = context["categoria_despesa"]
            despesas_categoria = [d for d in self.contas_pagar if d.get("categoria") == categoria]
            result["despesas_categoria"] = [self.anonymize_data(d) for d in despesas_categoria]
        
        # Filtrar por forma de pagamento
        if "forma_pagamento" in context:
            forma = context["forma_pagamento"]
            receitas_forma = [r for r in self.contas_receber if r.get("forma_pagamento") == forma]
            result["receitas_forma_pagamento"] = [self.anonymize_data(r) for r in receitas_forma]
        
        # Adicionar métricas relevantes ao contexto
        result["metricas"] = self.get_metrics()
        
        return result

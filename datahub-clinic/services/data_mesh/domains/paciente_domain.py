"""
Domínio de Paciente no Data Mesh.

Este módulo implementa o domínio de Paciente, responsável por dados de atendimentos realizados,
créditos disponíveis, orçamentos fechados e orçamentos abertos.
"""
from typing import Dict, List, Any, Optional
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from ..data_mesh_domains import DataMeshDomain

# Configurar logging
logger = logging.getLogger(__name__)

class PacienteDomain(DataMeshDomain):
    """Domínio de Paciente no Data Mesh."""
    
    def __init__(self):
        """Inicializa o domínio de Paciente."""
        super().__init__(
            name="paciente",
            description="Domínio responsável por dados de atendimentos realizados, créditos disponíveis, orçamentos fechados e orçamentos abertos"
        )
        self.atendimentos_realizados = []
        self.creditos_disponiveis = []
        self.orcamentos_fechados = []
        self.orcamentos_abertos = []
        
        # Definir KPIs e métricas do domínio
        self.kpis = [
            "total_pacientes",
            "pacientes_ativos",
            "taxa_retorno",
            "ticket_medio_paciente",
            "distribuicao_por_procedimento",
            "distribuicao_por_faixa_etaria",
            "taxa_conversao_orcamentos",
            "valor_medio_orcamento",
            "credito_medio_disponivel",
            "frequencia_media_visitas"
        ]
    
    def load_data(self, raw_data: Dict[str, Any]) -> None:
        """
        Carrega dados brutos no domínio de Paciente.
        
        Args:
            raw_data: Dados brutos da aplicação
        """
        if "paciente" not in raw_data:
            logger.warning("Dados de paciente não encontrados")
            return
        
        paciente_data = raw_data["paciente"]
        
        # Carregar atendimentos realizados
        self.atendimentos_realizados = paciente_data.get("atendimentos_realizados", [])
        
        # Carregar créditos disponíveis
        self.creditos_disponiveis = paciente_data.get("creditos_disponiveis", [])
        
        # Carregar orçamentos fechados
        self.orcamentos_fechados = paciente_data.get("orcamentos_fechados", [])
        
        # Carregar orçamentos abertos
        self.orcamentos_abertos = paciente_data.get("orcamentos_abertos", [])
        
        # Atualizar métricas após carregar novos dados
        self.update_metrics_cache()
        
        logger.info(f"Dados carregados no domínio de Paciente: {len(self.atendimentos_realizados)} atendimentos realizados")
    
    def _calculate_metrics(self) -> Dict[str, Any]:
        """
        Calcula métricas do domínio de Paciente.
        
        Returns:
            Dict[str, Any]: Métricas calculadas
        """
        metrics = {}
        
        # Extrair pacientes únicos dos atendimentos
        pacientes_unicos = set()
        if self.atendimentos_realizados:
            for atendimento in self.atendimentos_realizados:
                if "paciente" in atendimento:
                    pacientes_unicos.add(atendimento["paciente"])
        
        # Total de pacientes únicos
        metrics["total_pacientes"] = len(pacientes_unicos)
        
        # Converter para DataFrame para facilitar análises
        if self.atendimentos_realizados:
            df_atendimentos = pd.DataFrame(self.atendimentos_realizados)
            
            # Pacientes ativos (com atendimento nos últimos 90 dias)
            if "data" in df_atendimentos.columns:
                try:
                    df_atendimentos["data_dt"] = pd.to_datetime(df_atendimentos["data"], format="%d/%m/%Y", errors="coerce")
                    hoje = datetime.now()
                    limite_ativo = hoje - timedelta(days=90)
                    atendimentos_recentes = df_atendimentos[df_atendimentos["data_dt"] >= limite_ativo]
                    pacientes_ativos = atendimentos_recentes["paciente"].nunique() if "paciente" in df_atendimentos.columns else 0
                    metrics["pacientes_ativos"] = pacientes_ativos
                    metrics["percentual_pacientes_ativos"] = round(pacientes_ativos / metrics["total_pacientes"] * 100, 2) if metrics["total_pacientes"] > 0 else 0
                except Exception as e:
                    logger.error(f"Erro ao calcular pacientes ativos: {str(e)}")
            
            # Taxa de retorno (pacientes com mais de um atendimento)
            if "paciente" in df_atendimentos.columns:
                contagem_atendimentos = df_atendimentos["paciente"].value_counts()
                pacientes_retorno = (contagem_atendimentos > 1).sum()
                metrics["taxa_retorno"] = round(pacientes_retorno / metrics["total_pacientes"] * 100, 2) if metrics["total_pacientes"] > 0 else 0
            
            # Distribuição por procedimento
            if "procedimento" in df_atendimentos.columns:
                proc_counts = df_atendimentos["procedimento"].value_counts().to_dict()
                # Limitar a 10 procedimentos mais frequentes
                top_procs = {k: proc_counts[k] for k in list(proc_counts.keys())[:10]}
                metrics["distribuicao_por_procedimento"] = {
                    proc: {"count": count, "percentage": round(count / len(df_atendimentos) * 100, 2)}
                    for proc, count in top_procs.items()
                }
            
            # Frequência média de visitas por paciente
            if "paciente" in df_atendimentos.columns:
                freq_visitas = df_atendimentos["paciente"].value_counts().mean()
                metrics["frequencia_media_visitas"] = round(freq_visitas, 2)
        
        # Ticket médio por paciente
        if self.orcamentos_fechados:
            df_orcamentos = pd.DataFrame(self.orcamentos_fechados)
            if "valor_final" in df_orcamentos.columns:
                metrics["valor_medio_orcamento"] = round(df_orcamentos["valor_final"].mean(), 2)
                
                # Ticket médio por paciente
                if "paciente" in df_orcamentos.columns:
                    ticket_medio = df_orcamentos.groupby("paciente")["valor_final"].sum().mean()
                    metrics["ticket_medio_paciente"] = round(ticket_medio, 2)
        
        # Taxa de conversão de orçamentos
        total_orcamentos = len(self.orcamentos_fechados) + len(self.orcamentos_abertos)
        if total_orcamentos > 0:
            metrics["taxa_conversao_orcamentos"] = round(len(self.orcamentos_fechados) / total_orcamentos * 100, 2)
        
        # Crédito médio disponível
        if self.creditos_disponiveis:
            df_creditos = pd.DataFrame(self.creditos_disponiveis)
            if "credito_disponivel" in df_creditos.columns:
                metrics["credito_medio_disponivel"] = round(df_creditos["credito_disponivel"].mean(), 2)
        
        return metrics
    
    def get_metrics(self) -> Dict[str, Any]:
        """
        Retorna métricas anônimas do domínio de Paciente.
        
        Returns:
            Dict[str, Any]: Métricas anônimas
        """
        # Atualizar métricas se necessário
        self.update_metrics_cache()
        return self.metrics_cache
    
    def get_contextual_data(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Retorna dados contextuais com base no contexto fornecido.
        
        Args:
            context: Contexto da consulta
            
        Returns:
            Dict[str, Any]: Dados contextuais anônimos
        """
        result = {}
        
        # Filtrar por período
        if "periodo" in context:
            periodo = context["periodo"]
            if periodo == "mes_atual":
                # Implementar filtro para o mês atual
                pass
            elif periodo == "trimestre":
                # Implementar filtro para o trimestre atual
                pass
            elif periodo == "ano":
                # Implementar filtro para o ano atual
                pass
        
        # Filtrar por procedimento
        if "procedimento" in context:
            procedimento = context["procedimento"]
            atendimentos_proc = [a for a in self.atendimentos_realizados if a.get("procedimento") == procedimento]
            result["atendimentos_procedimento"] = [self.anonymize_data(a) for a in atendimentos_proc]
        
        # Filtrar por status de orçamento
        if "status_orcamento" in context:
            status = context["status_orcamento"]
            if status == "fechado":
                orcamentos_status = [o for o in self.orcamentos_fechados if o.get("status") == "Aprovado"]
                result["orcamentos_status"] = [self.anonymize_data(o) for o in orcamentos_status]
            elif status == "aberto":
                orcamentos_status = [o for o in self.orcamentos_abertos if o.get("status") in ["Em análise", "Aguardando aprovação", "Enviado"]]
                result["orcamentos_status"] = [self.anonymize_data(o) for o in orcamentos_status]
        
        # Adicionar métricas relevantes ao contexto
        result["metricas"] = self.get_metrics()
        
        return result

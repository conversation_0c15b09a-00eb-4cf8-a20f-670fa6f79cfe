"""
Inicialização do Data Mesh na aplicação.

Este módulo fornece funções para inicializar o Data Mesh na aplicação principal.
"""
import logging
from flask import current_app
from .data_mesh_domains import DomainRegistry
from .domains.agenda_domain import AgendaDomain
from .domains.financeiro_domain import FinanceiroDomain
from .domains.paciente_domain import PacienteDomain
from .domains.amigocare_domain import AmigoCareDomain
from .domains.visao360_domain import Visao360Domain
from .domains.amigostudio_domain import AmigoStudioDomain

# Configurar logging
logger = logging.getLogger(__name__)

def initialize_data_mesh_in_app(app):
    """
    Inicializa o Data Mesh na aplicação Flask.
    
    Args:
        app: Aplicação Flask
    """
    try:
        # Registrar os domínios
        registry = DomainRegistry()
        
        # Verificar se os domínios já foram registrados
        if not registry.domains:
            # Registrar domínios
            registry.register_domain(AgendaDomain())
            registry.register_domain(FinanceiroDomain())
            registry.register_domain(PacienteDomain())
            registry.register_domain(AmigoCareDomain())
            registry.register_domain(Visao360Domain())
            registry.register_domain(AmigoStudioDomain())
            
            logger.info("Domínios do Data Mesh registrados com sucesso")
        
        # Carregar dados mockados
        try:
            # Obter a função get_mock_data do app
            get_mock_data = app.view_functions.get('get_mock_data')
            if get_mock_data:
                mock_data = get_mock_data()
                
                # Carregar dados em todos os domínios
                registry.load_data_to_all_domains(mock_data)
                
                logger.info("Dados carregados em todos os domínios do Data Mesh")
            else:
                logger.warning("Função get_mock_data não encontrada na aplicação")
        except Exception as e:
            logger.error(f"Erro ao carregar dados mockados: {str(e)}")
        
        # Armazenar o registro no contexto da aplicação
        app.config['DATA_MESH_REGISTRY'] = registry
        
        logger.info("Data Mesh inicializado na aplicação")
        
        return registry
        
    except Exception as e:
        logger.error(f"Erro ao inicializar Data Mesh: {str(e)}")
        return None

def get_domain_registry(app=None):
    """
    Obtém o registro de domínios do Data Mesh.
    
    Args:
        app: Aplicação Flask (opcional)
        
    Returns:
        DomainRegistry: Registro de domínios
    """
    try:
        # Se app não for fornecido, tentar obter do contexto atual
        if app is None:
            app = current_app
        
        # Verificar se o registro já existe na aplicação
        if 'DATA_MESH_REGISTRY' in app.config:
            return app.config['DATA_MESH_REGISTRY']
        
        # Se não existir, inicializar
        return initialize_data_mesh_in_app(app)
        
    except Exception as e:
        logger.error(f"Erro ao obter registro de domínios: {str(e)}")
        
        # Retornar um novo registro como fallback
        return DomainRegistry()

def get_domain(domain_name, app=None):
    """
    Obtém um domínio específico do Data Mesh.
    
    Args:
        domain_name: Nome do domínio
        app: Aplicação Flask (opcional)
        
    Returns:
        DataMeshDomain: Domínio solicitado ou None
    """
    try:
        # Obter o registro de domínios
        registry = get_domain_registry(app)
        
        # Retornar o domínio solicitado
        return registry.get_domain(domain_name)
        
    except Exception as e:
        logger.error(f"Erro ao obter domínio {domain_name}: {str(e)}")
        return None

def get_all_domains(app=None):
    """
    Obtém todos os domínios do Data Mesh.
    
    Args:
        app: Aplicação Flask (opcional)
        
    Returns:
        Dict[str, DataMeshDomain]: Todos os domínios
    """
    try:
        # Obter o registro de domínios
        registry = get_domain_registry(app)
        
        # Retornar todos os domínios
        return registry.get_all_domains()
        
    except Exception as e:
        logger.error(f"Erro ao obter todos os domínios: {str(e)}")
        return {}

"""
Serviço central para gerenciamento de fontes de dados.
"""
import os
import json
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DataService:
    """Serviço central para gerenciamento de fontes de dados."""
    
    def __init__(self, config=None):
        """
        Inicializa o serviço de dados.
        
        Args:
            config (dict, optional): Configuração da fonte de dados.
        """
        self.config = config or {}
        self.current_source = None
        self._initialize_data_source()
    
    def _initialize_data_source(self):
        """Inicializa a fonte de dados com base na configuração."""
        source_type = self.config.get('type', 'mock')
        
        try:
            if source_type == 'mock':
                from .data_sources.mock_data_source import MockDataSource
                self.current_source = MockDataSource()
                logger.info("Fonte de dados mockados inicializada")
            
            elif source_type == 'json':
                from .data_sources.json_file_source import JsonFileDataSource
                file_path = self.config.get('file_path')
                self.current_source = JsonFileDataSource(file_path)
                logger.info(f"Fonte de dados JSON inicializada: {file_path}")
            
            elif source_type == 'excel':
                from .data_sources.excel_source import ExcelDataSource
                file_path = self.config.get('file_path')
                sheet_mapping = self.config.get('sheet_mapping', {})
                self.current_source = ExcelDataSource(file_path, sheet_mapping)
                logger.info(f"Fonte de dados Excel inicializada: {file_path}")
            
            elif source_type == 'csv':
                from .data_sources.csv_source import CsvDataSource
                file_paths = self.config.get('file_paths', {})
                delimiter = self.config.get('delimiter', ',')
                encoding = self.config.get('encoding', 'utf-8')
                self.current_source = CsvDataSource(file_paths, delimiter, encoding)
                logger.info(f"Fonte de dados CSV inicializada: {list(file_paths.values())}")
            
            elif source_type == 'database':
                from .data_sources.database_source import DatabaseDataSource
                connection_string = self.config.get('connection_string')
                query_mapping = self.config.get('query_mapping', {})
                self.current_source = DatabaseDataSource(connection_string, query_mapping)
                logger.info(f"Fonte de dados de banco de dados inicializada")
            
            else:
                from .data_sources.mock_data_source import MockDataSource
                self.current_source = MockDataSource()
                logger.warning(f"Tipo de fonte de dados desconhecido: {source_type}. Usando dados mockados.")
        
        except Exception as e:
            logger.error(f"Erro ao inicializar fonte de dados: {e}")
            # Fallback para dados mockados em caso de erro
            from .data_sources.mock_data_source import MockDataSource
            self.current_source = MockDataSource()
            logger.info("Usando dados mockados como fallback devido a erro")
    
    def set_config(self, config):
        """
        Atualiza a configuração e reinicializa a fonte de dados.
        
        Args:
            config (dict): Nova configuração.
        """
        self.config = config
        self._initialize_data_source()
    
    def get_data(self):
        """
        Obtém dados da fonte atual.
        
        Returns:
            dict: Dados estruturados.
        """
        if self.current_source:
            try:
                return self.current_source.get_data()
            except Exception as e:
                logger.error(f"Erro ao obter dados da fonte atual: {e}")
                # Fallback para dados mockados em caso de erro
                from .data_sources.mock_data_source import MockDataSource
                return MockDataSource().get_data()
        
        # Fallback para dados mockados se não houver fonte configurada
        from .data_sources.mock_data_source import MockDataSource
        return MockDataSource().get_data()
    
    def save_config(self, config_path=None):
        """
        Salva a configuração atual em um arquivo JSON.
        
        Args:
            config_path (str, optional): Caminho para o arquivo de configuração.
                Se não for fornecido, usa o caminho padrão.
        """
        if not config_path:
            # Caminho padrão para o arquivo de configuração
            config_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config')
            os.makedirs(config_dir, exist_ok=True)
            config_path = os.path.join(config_dir, 'data_source.json')
        
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Configuração salva em: {config_path}")
    
    def load_config(self, config_path=None):
        """
        Carrega a configuração de um arquivo JSON.
        
        Args:
            config_path (str, optional): Caminho para o arquivo de configuração.
                Se não for fornecido, usa o caminho padrão.
        """
        if not config_path:
            # Caminho padrão para o arquivo de configuração
            config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', 'data_source.json')
        
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            
            self._initialize_data_source()
            logger.info(f"Configuração carregada de: {config_path}")
        else:
            logger.warning(f"Arquivo de configuração não encontrado: {config_path}")

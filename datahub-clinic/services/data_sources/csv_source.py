"""
Fonte de dados de arquivos CSV.
"""
import os
import pandas as pd
from .data_source import DataSource

class CsvDataSource(DataSource):
    """Fonte de dados de arquivos CSV."""
    
    def __init__(self, file_paths, delimiter=',', encoding='utf-8'):
        """
        Inicializa a fonte de dados CSV.
        
        Args:
            file_paths (dict): Mapeamento de módulos para caminhos de arquivos CSV.
            delimiter (str, optional): Delimitador usado nos arquivos CSV.
            encoding (str, optional): Codificação dos arquivos CSV.
        """
        self.file_paths = file_paths
        self.delimiter = delimiter
        self.encoding = encoding
    
    def get_data(self):
        """
        Carrega dados dos arquivos CSV.
        
        Returns:
            dict: Dados estruturados.
        """
        # Estrutura de dados resultante
        data = {
            'agenda': {'agendamentos': [], 'producao_medica': [], 'tempo_atendimento': [], 'cancelamentos': []},
            'financeiro': {'contas_receber': [], 'contas_pagar': [], 'fluxo_caixa': [], 'fechamento_caixa': []},
            'paciente': {'atendimentos_realizados': [], 'creditos_disponiveis': [], 'orcamentos_fechados': [], 'orcamentos_abertos': []},
            'amigocare': {'avaliacao_nps': [], 'leads': [], 'campanhas': [], 'acompanhamento_pacientes': [], 'indicadores_qualidade': []},
            'visao360': {'pacientes_integrados': [], 'agendamentos_integrados': [], 'transacoes_integradas': [], 'jornadas_paciente': [], 'recomendacoes_estrutura': []},
            'resumo': {'total_agendamentos': 0, 'total_atendimentos': 0, 'total_receita': 0, 'total_despesa': 0}
        }
        
        # Processar cada arquivo CSV
        for module, file_path in self.file_paths.items():
            if file_path and os.path.exists(file_path):
                df = pd.read_csv(file_path, delimiter=self.delimiter, encoding=self.encoding)
                
                # Processar dados de acordo com o módulo
                if module in data:
                    self._process_module_data(module, df, data)
        
        return data
    
    def _process_module_data(self, module, df, data):
        """
        Processa os dados de um módulo específico.
        
        Args:
            module (str): Nome do módulo.
            df (DataFrame): DataFrame com os dados do módulo.
            data (dict): Dicionário de dados a ser atualizado.
        """
        # Converter DataFrame para dicionário
        records = df.to_dict('records')
        
        # Processar de acordo com o módulo e a estrutura do CSV
        # Assumimos que cada CSV tem uma coluna 'tipo' que indica o tipo de dado
        if 'tipo' in df.columns:
            for tipo in df['tipo'].unique():
                tipo_df = df[df['tipo'] == tipo]
                tipo_records = tipo_df.to_dict('records')
                
                # Mapear o tipo para a chave correspondente no módulo
                if module == 'agenda':
                    if tipo == 'agendamentos':
                        data[module]['agendamentos'] = tipo_records
                    elif tipo == 'producao_medica':
                        data[module]['producao_medica'] = tipo_records
                    elif tipo == 'tempo_atendimento':
                        data[module]['tempo_atendimento'] = tipo_records
                    elif tipo == 'cancelamentos':
                        data[module]['cancelamentos'] = tipo_records
                
                elif module == 'financeiro':
                    if tipo == 'contas_receber':
                        data[module]['contas_receber'] = tipo_records
                    elif tipo == 'contas_pagar':
                        data[module]['contas_pagar'] = tipo_records
                    elif tipo == 'fluxo_caixa':
                        data[module]['fluxo_caixa'] = tipo_records
                    elif tipo == 'fechamento_caixa':
                        data[module]['fechamento_caixa'] = tipo_records
                
                # Processar outros módulos de forma similar
                # ...
        else:
            # Se não houver coluna 'tipo', assumir que todos os registros são do mesmo tipo
            # e usar o nome do arquivo para determinar o tipo
            file_name = os.path.basename(self.file_paths[module]).lower()
            
            if module == 'agenda':
                if 'agendamentos' in file_name:
                    data[module]['agendamentos'] = records
                elif 'producao' in file_name:
                    data[module]['producao_medica'] = records
                elif 'tempo' in file_name:
                    data[module]['tempo_atendimento'] = records
                elif 'cancelamentos' in file_name:
                    data[module]['cancelamentos'] = records
                else:
                    # Se não conseguir determinar o tipo, colocar em todos
                    data[module]['agendamentos'] = records
                    data[module]['producao_medica'] = records
                    data[module]['tempo_atendimento'] = records
                    data[module]['cancelamentos'] = records
            
            # Processar outros módulos de forma similar
            # ...

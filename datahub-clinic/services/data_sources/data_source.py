"""
Interface base para todas as fontes de dados.
"""

class DataSource:
    """Interface base para todas as fontes de dados."""
    
    def get_data(self):
        """
        Obtém todos os dados da fonte.
        
        Returns:
            dict: Dados estruturados no formato esperado pelo aplicativo.
        """
        raise NotImplementedError("Método deve ser implementado pela classe concreta")

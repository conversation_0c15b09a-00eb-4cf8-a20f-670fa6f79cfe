"""
Fonte de dados de banco de dados.
"""
import os
import pandas as pd
import sqlalchemy
from .data_source import DataSource

class DatabaseDataSource(DataSource):
    """Fonte de dados de banco de dados."""
    
    def __init__(self, connection_string, query_mapping=None):
        """
        Inicializa a fonte de dados de banco de dados.
        
        Args:
            connection_string (str): String de conexão com o banco de dados.
            query_mapping (dict, optional): Mapeamento de módulos para queries SQL.
        """
        self.connection_string = connection_string
        self.query_mapping = query_mapping or {}
        self.engine = None
        
        # Inicializar conexão com o banco de dados
        if connection_string:
            try:
                self.engine = sqlalchemy.create_engine(connection_string)
            except Exception as e:
                print(f"Erro ao conectar ao banco de dados: {e}")
    
    def get_data(self):
        """
        Carrega dados do banco de dados.
        
        Returns:
            dict: Dados estruturados.
        """
        if not self.engine:
            raise ValueError("Conexão com o banco de dados não inicializada")
        
        # Estrutura de dados resultante
        data = {
            'agenda': {'agendamentos': [], 'producao_medica': [], 'tempo_atendimento': [], 'cancelamentos': []},
            'financeiro': {'contas_receber': [], 'contas_pagar': [], 'fluxo_caixa': [], 'fechamento_caixa': []},
            'paciente': {'atendimentos_realizados': [], 'creditos_disponiveis': [], 'orcamentos_fechados': [], 'orcamentos_abertos': []},
            'amigocare': {'avaliacao_nps': [], 'leads': [], 'campanhas': [], 'acompanhamento_pacientes': [], 'indicadores_qualidade': []},
            'visao360': {'pacientes_integrados': [], 'agendamentos_integrados': [], 'transacoes_integradas': [], 'jornadas_paciente': [], 'recomendacoes_estrutura': []},
            'resumo': {'total_agendamentos': 0, 'total_atendimentos': 0, 'total_receita': 0, 'total_despesa': 0}
        }
        
        # Executar queries para cada módulo
        for module, queries in self.query_mapping.items():
            if module in data:
                for submodule, query in queries.items():
                    if submodule in data[module]:
                        try:
                            df = pd.read_sql(query, self.engine)
                            data[module][submodule] = df.to_dict('records')
                        except Exception as e:
                            print(f"Erro ao executar query para {module}.{submodule}: {e}")
        
        # Calcular dados de resumo
        self._calculate_summary(data)
        
        return data
    
    def _calculate_summary(self, data):
        """
        Calcula dados de resumo com base nos dados carregados.
        
        Args:
            data (dict): Dados carregados.
        """
        # Total de agendamentos
        data['resumo']['total_agendamentos'] = len(data['agenda']['agendamentos'])
        
        # Total de atendimentos realizados
        atendimentos_realizados = [a for a in data['agenda']['agendamentos'] 
                                  if a.get('status') == 'Realizado']
        data['resumo']['total_atendimentos'] = len(atendimentos_realizados)
        
        # Total de receita
        receita = sum(float(c.get('valor', 0)) for c in data['financeiro']['contas_receber'] 
                     if c.get('status') == 'Pago')
        data['resumo']['total_receita'] = receita
        
        # Total de despesa
        despesa = sum(float(c.get('valor', 0)) for c in data['financeiro']['contas_pagar'] 
                     if c.get('status') == 'Pago')
        data['resumo']['total_despesa'] = despesa
        
        # Taxa de ocupação
        if data['resumo']['total_agendamentos'] > 0:
            taxa_ocupacao = (data['resumo']['total_atendimentos'] / data['resumo']['total_agendamentos']) * 100
            data['resumo']['taxa_ocupacao'] = round(taxa_ocupacao, 2)
        
        # Tempo médio de atendimento
        tempos = [float(t.get('tempo_atendimento', 0)) for t in data['agenda']['tempo_atendimento']]
        if tempos:
            data['resumo']['tempo_medio_atendimento'] = round(sum(tempos) / len(tempos), 2)
        
        # Taxa de cancelamento
        cancelamentos = [c for c in data['agenda']['agendamentos'] 
                        if c.get('status') == 'Cancelado']
        if data['resumo']['total_agendamentos'] > 0:
            taxa_cancelamento = (len(cancelamentos) / data['resumo']['total_agendamentos']) * 100
            data['resumo']['taxa_cancelamento'] = round(taxa_cancelamento, 2)

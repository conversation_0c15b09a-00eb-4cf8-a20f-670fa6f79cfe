"""
Fonte de dados de arquivo Excel (XLSX).
"""
import os
import pandas as pd
from .data_source import DataSource

class ExcelDataSource(DataSource):
    """Fonte de dados de arquivo Excel (XLSX)."""
    
    def __init__(self, file_path, sheet_mapping=None):
        """
        Inicializa a fonte de dados Excel.
        
        Args:
            file_path (str): Caminho para o arquivo Excel.
            sheet_mapping (dict, optional): Mapeamento de abas para módulos.
        """
        self.file_path = file_path
        self.sheet_mapping = sheet_mapping or {
            'agenda': 'Agenda',
            'financeiro': 'Financeiro',
            'paciente': 'Paciente',
            'amigocare': 'AmigoCare',
            'visao360': 'Visao360',
            'resumo': 'Resumo'
        }
    
    def get_data(self):
        """
        Carrega dados do arquivo Excel.
        
        Returns:
            dict: Dados estruturados.
        """
        if not self.file_path or not os.path.exists(self.file_path):
            raise FileNotFoundError(f"Arquivo Excel não encontrado: {self.file_path}")
        
        # Carregar o arquivo Excel
        excel_file = pd.ExcelFile(self.file_path)
        
        # Estrutura de dados resultante
        data = {
            'agenda': {'agendamentos': [], 'producao_medica': [], 'tempo_atendimento': [], 'cancelamentos': []},
            'financeiro': {'contas_receber': [], 'contas_pagar': [], 'fluxo_caixa': [], 'fechamento_caixa': []},
            'paciente': {'atendimentos_realizados': [], 'creditos_disponiveis': [], 'orcamentos_fechados': [], 'orcamentos_abertos': []},
            'amigocare': {'avaliacao_nps': [], 'leads': [], 'campanhas': [], 'acompanhamento_pacientes': [], 'indicadores_qualidade': []},
            'visao360': {'pacientes_integrados': [], 'agendamentos_integrados': [], 'transacoes_integradas': [], 'jornadas_paciente': [], 'recomendacoes_estrutura': []},
            'resumo': {'total_agendamentos': 0, 'total_atendimentos': 0, 'total_receita': 0, 'total_despesa': 0}
        }
        
        # Processar cada aba do Excel
        for module, sheet_name in self.sheet_mapping.items():
            if sheet_name in excel_file.sheet_names:
                df = excel_file.parse(sheet_name)
                
                # Processar dados de acordo com o módulo
                if module in data:
                    self._process_module_data(module, df, data)
        
        return data
    
    def _process_module_data(self, module, df, data):
        """
        Processa os dados de um módulo específico.
        
        Args:
            module (str): Nome do módulo.
            df (DataFrame): DataFrame com os dados do módulo.
            data (dict): Dicionário de dados a ser atualizado.
        """
        # Converter DataFrame para dicionário
        records = df.to_dict('records')
        
        # Processar de acordo com o módulo
        if module == 'agenda':
            if 'agendamentos' in df.columns:
                data[module]['agendamentos'] = records
            if 'producao_medica' in df.columns:
                data[module]['producao_medica'] = records
            if 'tempo_atendimento' in df.columns:
                data[module]['tempo_atendimento'] = records
            if 'cancelamentos' in df.columns:
                data[module]['cancelamentos'] = records
        
        elif module == 'financeiro':
            if 'contas_receber' in df.columns:
                data[module]['contas_receber'] = records
            if 'contas_pagar' in df.columns:
                data[module]['contas_pagar'] = records
            if 'fluxo_caixa' in df.columns:
                data[module]['fluxo_caixa'] = records
            if 'fechamento_caixa' in df.columns:
                data[module]['fechamento_caixa'] = records
        
        elif module == 'paciente':
            if 'atendimentos_realizados' in df.columns:
                data[module]['atendimentos_realizados'] = records
            if 'creditos_disponiveis' in df.columns:
                data[module]['creditos_disponiveis'] = records
            if 'orcamentos_fechados' in df.columns:
                data[module]['orcamentos_fechados'] = records
            if 'orcamentos_abertos' in df.columns:
                data[module]['orcamentos_abertos'] = records
        
        elif module == 'amigocare':
            if 'avaliacao_nps' in df.columns:
                data[module]['avaliacao_nps'] = records
            if 'leads' in df.columns:
                data[module]['leads'] = records
            if 'campanhas' in df.columns:
                data[module]['campanhas'] = records
            if 'acompanhamento_pacientes' in df.columns:
                data[module]['acompanhamento_pacientes'] = records
            if 'indicadores_qualidade' in df.columns:
                data[module]['indicadores_qualidade'] = records
        
        elif module == 'visao360':
            if 'pacientes_integrados' in df.columns:
                data[module]['pacientes_integrados'] = records
            if 'agendamentos_integrados' in df.columns:
                data[module]['agendamentos_integrados'] = records
            if 'transacoes_integradas' in df.columns:
                data[module]['transacoes_integradas'] = records
            if 'jornadas_paciente' in df.columns:
                data[module]['jornadas_paciente'] = records
            if 'recomendacoes_estrutura' in df.columns:
                data[module]['recomendacoes_estrutura'] = records
        
        elif module == 'resumo':
            # Processar dados de resumo
            for key in data['resumo'].keys():
                if key in df.columns:
                    # Pegar o primeiro valor para métricas simples
                    if not isinstance(data['resumo'][key], list) and len(df) > 0:
                        data['resumo'][key] = df[key].iloc[0]

"""
Fonte de dados de arquivo JSON.
"""
import json
import os
from .data_source import DataSource

class JsonFileDataSource(DataSource):
    """Fonte de dados de arquivo JSON."""
    
    def __init__(self, file_path):
        """
        Inicializa a fonte de dados JSON.
        
        Args:
            file_path (str): Caminho para o arquivo JSON.
        """
        self.file_path = file_path
    
    def get_data(self):
        """
        Carrega dados do arquivo JSON.
        
        Returns:
            dict: Dados estruturados.
        """
        if not self.file_path or not os.path.exists(self.file_path):
            raise FileNotFoundError(f"Arquivo JSON não encontrado: {self.file_path}")
        
        with open(self.file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Validar estrutura dos dados
        self._validate_data_structure(data)
        
        return data
    
    def _validate_data_structure(self, data):
        """
        Valida a estrutura dos dados.
        
        Args:
            data (dict): Dados a serem validados.
            
        Raises:
            ValueError: Se a estrutura dos dados for inválida.
        """
        required_modules = ['agenda', 'financeiro', 'paciente', 'amigocare', 'visao360', 'resumo']
        
        for module in required_modules:
            if module not in data:
                raise ValueError(f"Módulo obrigatório ausente nos dados: {module}")

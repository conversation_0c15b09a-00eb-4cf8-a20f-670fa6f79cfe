"""
Fonte de dados mockados usando a função existente.
"""
from .data_source import DataSource

class MockDataSource(DataSource):
    """Fonte de dados mockados usando a função existente."""
    
    def get_data(self):
        """
        Obtém dados mockados.
        
        Returns:
            dict: Dados mockados estruturados.
        """
        # Importar a função generate_mock_data do módulo principal
        # Isso evita problemas de importação circular
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
        from app import generate_mock_data
        
        return generate_mock_data()

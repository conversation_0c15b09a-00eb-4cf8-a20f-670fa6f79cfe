"""
Serviço para geração e manipulação de embeddings.
"""
import os
import json
import hashlib
import random
from typing import List, Dict, Any, Union
from dotenv import load_dotenv

# Carregar variáveis de ambiente
load_dotenv()

# Tenta importar numpy, mas fornece uma alternativa se não estiver disponível
try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False
    print("NumPy não está disponível. Usando alternativas para cálculos.")

# Tenta importar requests, mas fornece uma alternativa se não estiver disponível
try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False
    print("Requests não está disponível. Usando modo de fallback para embeddings.")

# Configuração da OpenAI
OPENAI_EMBEDDING_URL = "https://api.openai.com/v1/embeddings"
EMBEDDING_MODEL = "text-embedding-3-small"

# Função para verificar se podemos usar embeddings reais
def can_use_real_embeddings():
    api_key = os.getenv('OPENAI_API_KEY')
    return REQUESTS_AVAILABLE and NUMPY_AVAILABLE and api_key and api_key != 'sua-chave-api-aqui'

class EmbeddingService:
    """
    Serviço para geração e manipulação de embeddings.
    """

    @staticmethod
    def create_embedding(text: str) -> List[float]:
        """
        Cria um embedding para o texto fornecido usando a API da OpenAI.
        Se a API não estiver disponível, gera um embedding simulado.

        Args:
            text (str): Texto para gerar o embedding

        Returns:
            List[float]: Vetor de embedding
        """
        if not text:
            return []

        # Se não devemos usar embeddings reais, gerar um simulado
        if not can_use_real_embeddings():
            return EmbeddingService._generate_simulated_embedding(text)

        # Obter a chave da API
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            return EmbeddingService._generate_simulated_embedding(text)

        try:
            # Tentar usar a nova sintaxe do OpenAI (versão 1.0.0+)
            try:
                import openai
                client = openai.OpenAI(api_key=api_key)

                response = client.embeddings.create(
                    model=EMBEDDING_MODEL,
                    input=text
                )

                # Extrair o embedding
                return response.data[0].embedding

            except ImportError:
                # Fallback para requests se o openai não estiver disponível
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {api_key}"
                }

                payload = {
                    "model": EMBEDDING_MODEL,
                    "input": text
                }

                response = requests.post(OPENAI_EMBEDDING_URL, headers=headers, json=payload)
                response_data = response.json()

                if 'data' in response_data and len(response_data['data']) > 0:
                    return response_data['data'][0]['embedding']
                else:
                    print(f"Erro ao gerar embedding: {response_data}")
                    # Fallback para embedding simulado
                    return EmbeddingService._generate_simulated_embedding(text)

        except Exception as e:
            print(f"Erro ao chamar API de embeddings: {str(e)}")
            # Fallback para embedding simulado
            return EmbeddingService._generate_simulated_embedding(text)

    @staticmethod
    def _generate_simulated_embedding(text: str, dimension: int = 1536) -> List[float]:
        """
        Gera um embedding simulado baseado no hash do texto.

        Args:
            text (str): Texto para gerar o embedding simulado
            dimension (int): Dimensão do embedding (padrão: 1536 para compatibilidade com OpenAI)

        Returns:
            List[float]: Embedding simulado
        """
        if not text:
            return [0.0] * dimension

        # Usa o hash do texto como seed para o gerador de números aleatórios
        text_hash = hashlib.md5(text.encode()).hexdigest()
        random.seed(text_hash)

        # Gera valores aleatórios, mas determinísticos para o mesmo texto
        embedding = [random.uniform(-1.0, 1.0) for _ in range(dimension)]

        # Normaliza o vetor para ter norma 1 (como embeddings reais)
        # Calcula a norma do vetor
        norm_squared = sum(x*x for x in embedding)
        norm = norm_squared ** 0.5

        # Normaliza cada componente
        if norm > 0:
            embedding = [x/norm for x in embedding]

        return embedding

    @staticmethod
    def create_embeddings_batch(texts: List[str]) -> List[List[float]]:
        """
        Cria embeddings para uma lista de textos.
        Se a API não estiver disponível, gera embeddings simulados.

        Args:
            texts (List[str]): Lista de textos para gerar embeddings

        Returns:
            List[List[float]]: Lista de vetores de embedding
        """
        if not texts:
            return []

        # Filtra textos vazios
        filtered_texts = [text for text in texts if text]

        if not filtered_texts:
            return []

        # Se não devemos usar embeddings reais, gerar simulados
        if not can_use_real_embeddings():
            return [EmbeddingService._generate_simulated_embedding(text) for text in filtered_texts]

        # Obter a chave da API
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            return [EmbeddingService._generate_simulated_embedding(text) for text in filtered_texts]

        try:
            # Tentar usar a nova sintaxe do OpenAI (versão 1.0.0+)
            try:
                import openai
                client = openai.OpenAI(api_key=api_key)

                response = client.embeddings.create(
                    model=EMBEDDING_MODEL,
                    input=filtered_texts
                )

                # Extrair os embeddings
                # Ordena os embeddings pelo índice para manter a ordem original
                sorted_embeddings = sorted(response.data, key=lambda x: x.index)
                return [item.embedding for item in sorted_embeddings]

            except ImportError:
                # Fallback para requests se o openai não estiver disponível
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {api_key}"
                }

                payload = {
                    "model": EMBEDDING_MODEL,
                    "input": filtered_texts
                }

                response = requests.post(OPENAI_EMBEDDING_URL, headers=headers, json=payload)
                response_data = response.json()

                if 'data' in response_data:
                    # Ordena os embeddings pelo índice para manter a ordem original
                    sorted_embeddings = sorted(response_data['data'], key=lambda x: x['index'])
                    return [item['embedding'] for item in sorted_embeddings]
                else:
                    print(f"Erro ao gerar embeddings em lote: {response_data}")
                    # Fallback para embeddings simulados
                    return [EmbeddingService._generate_simulated_embedding(text) for text in filtered_texts]

        except Exception as e:
            print(f"Erro ao chamar API de embeddings em lote: {str(e)}")
            # Fallback para embeddings simulados
            return [EmbeddingService._generate_simulated_embedding(text) for text in filtered_texts]

    @staticmethod
    def cosine_similarity(embedding1: List[float], embedding2: List[float]) -> float:
        """
        Calcula a similaridade de cosseno entre dois embeddings.
        Funciona com ou sem NumPy.

        Args:
            embedding1 (List[float]): Primeiro embedding
            embedding2 (List[float]): Segundo embedding

        Returns:
            float: Similaridade de cosseno (entre -1 e 1, onde 1 é mais similar)
        """
        if not embedding1 or not embedding2:
            return 0.0

        # Verifica se os embeddings têm o mesmo tamanho
        if len(embedding1) != len(embedding2):
            print(f"Aviso: Embeddings com tamanhos diferentes: {len(embedding1)} vs {len(embedding2)}")
            # Ajusta para o menor tamanho
            min_len = min(len(embedding1), len(embedding2))
            embedding1 = embedding1[:min_len]
            embedding2 = embedding2[:min_len]

        # Usa NumPy se disponível (mais rápido)
        if NUMPY_AVAILABLE:
            try:
                # Converte para arrays numpy
                vec1 = np.array(embedding1)
                vec2 = np.array(embedding2)

                # Calcula similaridade de cosseno
                dot_product = np.dot(vec1, vec2)
                norm1 = np.linalg.norm(vec1)
                norm2 = np.linalg.norm(vec2)

                if norm1 == 0 or norm2 == 0:
                    return 0.0

                return float(dot_product / (norm1 * norm2))
            except Exception as e:
                print(f"Erro ao usar NumPy para similaridade: {str(e)}")
                # Fallback para implementação pura em Python

        # Implementação pura em Python (sem NumPy)
        # Produto escalar
        dot_product = sum(a * b for a, b in zip(embedding1, embedding2))

        # Normas
        norm1_squared = sum(a * a for a in embedding1)
        norm2_squared = sum(b * b for b in embedding2)

        norm1 = norm1_squared ** 0.5
        norm2 = norm2_squared ** 0.5

        if norm1 == 0 or norm2 == 0:
            return 0.0

        return dot_product / (norm1 * norm2)

    @staticmethod
    def find_most_similar(query_embedding: List[float], embeddings_list: List[List[float]],
                          items: List[Any], top_k: int = 5) -> List[Dict[str, Any]]:
        """
        Encontra os itens mais similares ao embedding de consulta.

        Args:
            query_embedding (List[float]): Embedding da consulta
            embeddings_list (List[List[float]]): Lista de embeddings para comparar
            items (List[Any]): Lista de itens correspondentes aos embeddings
            top_k (int): Número de itens mais similares para retornar

        Returns:
            List[Dict[str, Any]]: Lista de dicionários com item e score de similaridade
        """
        if not query_embedding or not embeddings_list or not items:
            return []

        if len(embeddings_list) != len(items):
            raise ValueError("O número de embeddings deve ser igual ao número de itens")

        similarities = []

        for i, emb in enumerate(embeddings_list):
            similarity = EmbeddingService.cosine_similarity(query_embedding, emb)
            similarities.append({
                "item": items[i],
                "similarity": similarity
            })

        # Ordena por similaridade (maior para menor)
        sorted_similarities = sorted(similarities, key=lambda x: x["similarity"], reverse=True)

        # Retorna os top_k mais similares
        return sorted_similarities[:top_k]

    @staticmethod
    def create_context_embedding(context: Dict[str, Any]) -> List[float]:
        """
        Cria um embedding para o contexto da página.

        Args:
            context (Dict[str, Any]): Contexto da página

        Returns:
            List[float]: Embedding do contexto
        """
        # Extrai informações relevantes do contexto
        page_context = context.get('page', {})

        # Constrói um texto representativo do contexto
        context_text = f"""
        Módulo: {page_context.get('module', '')}
        Página: {page_context.get('formatted_title', page_context.get('title', ''))}
        Descrição: {page_context.get('page_description', '')}
        """

        # Adiciona métricas-chave
        metrics = page_context.get('key_metrics', {})
        if metrics:
            context_text += "Métricas-chave:\n"
            for key, value in metrics.items():
                context_text += f"- {key}: {value}\n"

        # Adiciona elementos da página
        elements = page_context.get('page_elements', [])
        if elements:
            context_text += "Elementos da página:\n"
            for element in elements:
                context_text += f"- {element}\n"

        # Gera o embedding
        return EmbeddingService.create_embedding(context_text)

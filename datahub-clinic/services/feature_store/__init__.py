"""
Pacote Feature Store para a aplicação.

Este pacote implementa uma feature store para armazenar, gerenciar e servir
features para modelos de machine learning e análises.
"""
from .feature_store import FeatureStore, Feature, FeatureGroup
from .feature_registry import FeatureRegistry

__all__ = [
    'FeatureStore',
    'Feature',
    'FeatureGroup',
    'FeatureRegistry',
    'initialize_feature_store'
]

def initialize_feature_store():
    """
    Inicializa a Feature Store.
    
    Returns:
        FeatureStore: Feature Store inicializada
    """
    from .feature_store import FeatureStore
    return FeatureStore()

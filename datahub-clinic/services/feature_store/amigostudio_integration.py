"""
Integração da Feature Store com o AmigoStudio.

Este módulo fornece funções para integrar a Feature Store com o AmigoStudio,
permitindo o uso de features em análises e modelos.
"""
from typing import Dict, List, Any, Optional
import pandas as pd
import numpy as np
import json
import logging
from flask import current_app
from .feature_registry import FeatureRegistry
from .feature_store import FeatureStore, Feature, FeatureGroup

# Configurar logging
logger = logging.getLogger(__name__)

def initialize_feature_store_in_app(app):
    """
    Inicializa a Feature Store na aplicação Flask.
    
    Args:
        app: Aplicação Flask
    """
    try:
        # Inicializar a Feature Store
        feature_store = FeatureStore()
        
        # Inicializar o registro de features
        feature_registry = FeatureRegistry()
        
        # Registrar features padrão
        feature_registry.register_default_features()
        
        # Armazenar no contexto da aplicação
        app.config['FEATURE_STORE'] = feature_store
        app.config['FEATURE_REGISTRY'] = feature_registry
        
        logger.info("Feature Store inicializada na aplicação")
        
        return feature_store, feature_registry
        
    except Exception as e:
        logger.error(f"Erro ao inicializar Feature Store: {str(e)}")
        return None, None

def get_feature_store(app=None):
    """
    Obtém a Feature Store.
    
    Args:
        app: Aplicação Flask (opcional)
        
    Returns:
        FeatureStore: Feature Store
    """
    try:
        # Se app não for fornecido, tentar obter do contexto atual
        if app is None:
            app = current_app
        
        # Verificar se a Feature Store já existe na aplicação
        if 'FEATURE_STORE' in app.config:
            return app.config['FEATURE_STORE']
        
        # Se não existir, inicializar
        feature_store, _ = initialize_feature_store_in_app(app)
        return feature_store
        
    except Exception as e:
        logger.error(f"Erro ao obter Feature Store: {str(e)}")
        
        # Retornar uma nova Feature Store como fallback
        return FeatureStore()

def get_feature_registry(app=None):
    """
    Obtém o registro de features.
    
    Args:
        app: Aplicação Flask (opcional)
        
    Returns:
        FeatureRegistry: Registro de features
    """
    try:
        # Se app não for fornecido, tentar obter do contexto atual
        if app is None:
            app = current_app
        
        # Verificar se o registro já existe na aplicação
        if 'FEATURE_REGISTRY' in app.config:
            return app.config['FEATURE_REGISTRY']
        
        # Se não existir, inicializar
        _, feature_registry = initialize_feature_store_in_app(app)
        return feature_registry
        
    except Exception as e:
        logger.error(f"Erro ao obter registro de features: {str(e)}")
        
        # Retornar um novo registro como fallback
        return FeatureRegistry()

def get_features_for_analysis(query: str = None, domain: str = None, 
                            tags: List[str] = None) -> Dict[str, Any]:
    """
    Obtém features para análise no AmigoStudio.
    
    Args:
        query: Texto para pesquisa (opcional)
        domain: Domínio para filtrar (opcional)
        tags: Tags para filtrar (opcional)
        
    Returns:
        Dict[str, Any]: Features para análise
    """
    try:
        # Obter o registro de features
        registry = get_feature_registry()
        
        # Pesquisar features
        features = registry.search_features(query, domain, tags)
        
        # Organizar features por grupo
        features_by_group = {}
        for feature_info in features:
            group_name = feature_info["group_name"]
            if group_name not in features_by_group:
                features_by_group[group_name] = []
            
            features_by_group[group_name].append({
                "name": feature_info["feature"]["name"],
                "description": feature_info["feature"]["description"],
                "data_type": feature_info["feature"]["data_type"],
                "value": feature_info["value"],
                "domain": feature_info["feature"]["domain"],
                "tags": feature_info["feature"]["tags"]
            })
        
        return {
            "features_by_group": features_by_group,
            "total_features": len(features)
        }
        
    except Exception as e:
        logger.error(f"Erro ao obter features para análise: {str(e)}")
        return {
            "features_by_group": {},
            "total_features": 0,
            "error": str(e)
        }

def get_features_as_dataframe(group_names: List[str] = None) -> pd.DataFrame:
    """
    Obtém features como DataFrame para uso no AmigoStudio.
    
    Args:
        group_names: Nomes dos grupos de features (opcional)
        
    Returns:
        pd.DataFrame: DataFrame com as features
    """
    try:
        # Obter a Feature Store
        feature_store = get_feature_store()
        
        # Se não forem especificados grupos, usar todos
        if not group_names:
            group_names = [group.name for group in feature_store.list_feature_groups()]
        
        # Criar DataFrame com os valores das features
        data = {}
        
        for group_name in group_names:
            # Obter valores das features do grupo
            group_values = feature_store.get_feature_values(group_name)
            
            # Adicionar ao dicionário de dados
            for feature_name, value in group_values.items():
                # Usar nome completo da feature (grupo.feature)
                full_name = f"{group_name}.{feature_name}"
                data[full_name] = [value]
        
        return pd.DataFrame(data)
        
    except Exception as e:
        logger.error(f"Erro ao obter features como DataFrame: {str(e)}")
        return pd.DataFrame()

def get_features_for_streamlit() -> str:
    """
    Obtém código Streamlit para visualizar features.
    
    Returns:
        str: Código Streamlit
    """
    try:
        # Obter features como DataFrame
        df = get_features_as_dataframe()
        
        # Criar código Streamlit
        code = """
import streamlit as st
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# Configurar a página
st.set_page_config(
    page_title="Feature Store - AmigoStudio Pro",
    page_icon="📊",
    layout="wide"
)

# Título
st.title("Feature Store - AmigoStudio Pro")
st.write("Visualização das features disponíveis na Feature Store")

# Carregar dados das features
features_data = {data}

# Exibir DataFrame
st.subheader("Features Disponíveis")
st.dataframe(features_data)

# Visualizações
st.subheader("Visualizações")

# Selecionar features para visualização
col1, col2 = st.columns(2)

with col1:
    numeric_features = features_data.select_dtypes(include=['float64', 'int64']).columns.tolist()
    selected_feature = st.selectbox("Selecione uma feature para visualizar", numeric_features)

# Criar visualização
if selected_feature:
    with col2:
        chart_type = st.selectbox(
            "Tipo de visualização",
            ["Valor Atual", "Histórico (Simulado)"]
        )
    
    if chart_type == "Valor Atual":
        # Exibir valor atual
        value = features_data[selected_feature].iloc[0]
        st.metric(label=selected_feature, value=f"{value:.2f}" if isinstance(value, float) else value)
        
        # Criar um gráfico de barras para comparação
        fig, ax = plt.subplots(figsize=(10, 5))
        features_data.iloc[0][numeric_features].plot(kind='bar', ax=ax)
        plt.title("Comparação de Features")
        plt.xticks(rotation=45, ha='right')
        plt.tight_layout()
        st.pyplot(fig)
    else:
        # Simular histórico
        dates = pd.date_range(end=pd.Timestamp.now(), periods=30, freq='D')
        
        # Gerar valores simulados com tendência
        np.random.seed(42)  # Para reprodutibilidade
        base_value = features_data[selected_feature].iloc[0]
        trend = np.linspace(-0.2, 0.2, 30)  # Tendência linear
        noise = np.random.normal(0, 0.05, 30)  # Ruído aleatório
        
        # Calcular valores históricos simulados
        historical_values = base_value * (1 + trend + noise)
        
        # Criar DataFrame histórico
        historical_df = pd.DataFrame({
            'Data': dates,
            'Valor': historical_values
        })
        
        # Plotar gráfico de linha
        fig, ax = plt.subplots(figsize=(10, 5))
        sns.lineplot(x='Data', y='Valor', data=historical_df, ax=ax)
        plt.title(f"Histórico Simulado - {selected_feature}")
        plt.xticks(rotation=45)
        plt.tight_layout()
        st.pyplot(fig)
        
        # Exibir tabela de histórico
        st.dataframe(historical_df)

# Correlação entre features
st.subheader("Correlação entre Features")

# Calcular correlação (simulada)
numeric_df = features_data[numeric_features]
if len(numeric_features) > 1:
    # Criar matriz de correlação simulada
    corr_matrix = pd.DataFrame(np.random.uniform(-1, 1, (len(numeric_features), len(numeric_features))), 
                             columns=numeric_features, index=numeric_features)
    
    # Garantir que a diagonal seja 1
    for i in range(len(numeric_features)):
        corr_matrix.iloc[i, i] = 1.0
    
    # Garantir simetria
    corr_matrix = (corr_matrix + corr_matrix.T) / 2
    
    # Plotar heatmap
    fig, ax = plt.subplots(figsize=(10, 8))
    sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', vmin=-1, vmax=1, ax=ax)
    plt.title("Matriz de Correlação (Simulada)")
    plt.tight_layout()
    st.pyplot(fig)
else:
    st.write("Necessário mais de uma feature numérica para calcular correlações.")

# Informações adicionais
st.subheader("Informações Adicionais")
st.write('''
Esta visualização mostra as features disponíveis na Feature Store do AmigoStudio Pro.
As features são extraídas de diferentes domínios de negócio e podem ser usadas para análises e modelos.

Para usar estas features em seus próprios scripts:

```python
# Importar a Feature Store
from services.feature_store import get_features_as_dataframe

# Obter features como DataFrame
features_df = get_features_as_dataframe()

# Usar as features em análises
# ...
```
''')
        """.format(data=df.to_dict())
        
        return code
        
    except Exception as e:
        logger.error(f"Erro ao gerar código Streamlit: {str(e)}")
        return f"""
import streamlit as st

st.title("Feature Store - AmigoStudio Pro")
st.error("Erro ao carregar features: {str(e)}")
        """

def register_custom_feature(group_name: str, feature_name: str, description: str,
                          data_type: str, value: Any, domain: str = None,
                          tags: List[str] = None) -> Dict[str, Any]:
    """
    Registra uma feature personalizada.
    
    Args:
        group_name: Nome do grupo
        feature_name: Nome da feature
        description: Descrição da feature
        data_type: Tipo de dados da feature
        value: Valor da feature
        domain: Domínio da feature (opcional)
        tags: Tags da feature (opcional)
        
    Returns:
        Dict[str, Any]: Resultado do registro
    """
    try:
        # Obter o registro de features
        registry = get_feature_registry()
        
        # Registrar a feature
        feature = registry.register_feature(
            group_name=group_name,
            feature_name=feature_name,
            description=description,
            data_type=data_type,
            domain=domain,
            tags=tags
        )
        
        # Definir o valor da feature
        feature_store = get_feature_store()
        feature_store.set_feature_value(group_name, feature_name, value)
        
        logger.info(f"Feature personalizada '{feature_name}' registrada no grupo '{group_name}'")
        
        return {
            "success": True,
            "feature": feature.to_dict(),
            "message": f"Feature '{feature_name}' registrada com sucesso"
        }
        
    except Exception as e:
        logger.error(f"Erro ao registrar feature personalizada: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "message": f"Erro ao registrar feature: {str(e)}"
        }

"""
Registro de features para a Feature Store.

Este módulo implementa o registro de features para a Feature Store,
permitindo o registro e descoberta de features.
"""
from typing import Dict, List, Any, Optional, Union
import pandas as pd
import numpy as np
from datetime import datetime
import logging
from .feature_store import FeatureStore, Feature, FeatureGroup

# Configurar logging
logger = logging.getLogger(__name__)

class FeatureRegistry:
    """Classe para registro e descoberta de features."""
    
    _instance = None
    
    def __new__(cls):
        """Implementação de Singleton."""
        if cls._instance is None:
            cls._instance = super(FeatureRegistry, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """Inicializa o registro de features."""
        if self._initialized:
            return
            
        self.feature_store = FeatureStore()
        self._initialized = True
        logger.info("Registro de features inicializado")
    
    def register_feature(self, group_name: str, feature_name: str, description: str,
                       data_type: str, domain: str = None, tags: List[str] = None,
                       metadata: Dict[str, Any] = None) -> Feature:
        """
        Registra uma nova feature.
        
        Args:
            group_name: Nome do grupo
            feature_name: Nome da feature
            description: Descrição da feature
            data_type: Tipo de dados da feature
            domain: Domínio da feature (opcional)
            tags: Tags da feature (opcional)
            metadata: Metadados da feature (opcional)
            
        Returns:
            Feature: Feature registrada
        """
        # Verificar se o grupo existe
        group = self.feature_store.get_feature_group(group_name)
        if not group:
            # Se o grupo não existir, criar um novo
            domain = domain or "default"
            group = self.feature_store.create_feature_group(
                name=group_name,
                description=f"Grupo de features {group_name}",
                domain=domain
            )
        
        # Criar a feature
        feature = Feature(
            name=feature_name,
            description=description,
            domain=group.domain,
            data_type=data_type,
            tags=tags or [],
            metadata=metadata or {}
        )
        
        # Adicionar a feature ao grupo
        self.feature_store.add_feature(group_name, feature)
        
        logger.info(f"Feature '{feature_name}' registrada no grupo '{group_name}'")
        return feature
    
    def register_data_mesh_feature(self, group_name: str, feature_name: str, description: str,
                                 domain_name: str, metric_name: str, data_type: str = "float",
                                 tags: List[str] = None) -> Feature:
        """
        Registra uma feature baseada em uma métrica do Data Mesh.
        
        Args:
            group_name: Nome do grupo
            feature_name: Nome da feature
            description: Descrição da feature
            domain_name: Nome do domínio no Data Mesh
            metric_name: Nome da métrica no domínio
            data_type: Tipo de dados da feature (opcional)
            tags: Tags da feature (opcional)
            
        Returns:
            Feature: Feature registrada
        """
        # Criar metadados para a feature
        metadata = {
            "source": "data_mesh",
            "domain_name": domain_name,
            "metric_name": metric_name
        }
        
        # Registrar a feature
        feature = self.register_feature(
            group_name=group_name,
            feature_name=feature_name,
            description=description,
            data_type=data_type,
            domain=domain_name,
            tags=tags or ["data_mesh"],
            metadata=metadata
        )
        
        # Atualizar o valor da feature a partir do Data Mesh
        self.feature_store.update_feature_from_data_mesh(
            group_name=group_name,
            feature_name=feature_name,
            domain_name=domain_name,
            metric_name=metric_name
        )
        
        return feature
    
    def search_features(self, query: str = None, domain: str = None, 
                      tags: List[str] = None) -> List[Dict[str, Any]]:
        """
        Pesquisa features com base em critérios.
        
        Args:
            query: Texto para pesquisa (opcional)
            domain: Domínio para filtrar (opcional)
            tags: Tags para filtrar (opcional)
            
        Returns:
            List[Dict[str, Any]]: Lista de features encontradas
        """
        results = []
        
        # Obter todos os grupos de features
        groups = self.feature_store.list_feature_groups(domain)
        
        for group in groups:
            for feature in group.features:
                # Verificar se a feature corresponde aos critérios
                match = True
                
                # Verificar query
                if query:
                    query_lower = query.lower()
                    name_match = query_lower in feature.name.lower()
                    desc_match = query_lower in feature.description.lower()
                    if not (name_match or desc_match):
                        match = False
                
                # Verificar tags
                if tags and match:
                    if not any(tag in feature.tags for tag in tags):
                        match = False
                
                # Adicionar feature aos resultados se corresponder
                if match:
                    results.append({
                        "group_name": group.name,
                        "feature": feature.to_dict(),
                        "value": self.feature_store.get_feature_value(group.name, feature.name)
                    })
        
        return results
    
    def get_feature_details(self, group_name: str, feature_name: str) -> Dict[str, Any]:
        """
        Obtém detalhes de uma feature.
        
        Args:
            group_name: Nome do grupo
            feature_name: Nome da feature
            
        Returns:
            Dict[str, Any]: Detalhes da feature
        """
        feature = self.feature_store.get_feature(group_name, feature_name)
        if not feature:
            return {}
        
        value = self.feature_store.get_feature_value(group_name, feature_name)
        
        return {
            "feature": feature.to_dict(),
            "value": value,
            "statistics": feature.statistics
        }
    
    def update_all_data_mesh_features(self) -> None:
        """Atualiza todas as features baseadas no Data Mesh."""
        self.feature_store.update_all_features_from_data_mesh()
    
    def get_features_for_model(self, model_name: str) -> pd.DataFrame:
        """
        Obtém features para um modelo específico.
        
        Args:
            model_name: Nome do modelo
            
        Returns:
            pd.DataFrame: DataFrame com as features para o modelo
        """
        # Pesquisar features com a tag do modelo
        features = self.search_features(tags=[f"model:{model_name}"])
        
        # Criar DataFrame com os valores das features
        data = {}
        for feature_info in features:
            group_name = feature_info["group_name"]
            feature_name = feature_info["feature"]["name"]
            value = self.feature_store.get_feature_value(group_name, feature_name)
            data[feature_name] = [value]
        
        return pd.DataFrame(data)
    
    def register_default_features(self) -> None:
        """Registra features padrão para a aplicação."""
        try:
            # Registrar features do domínio Agenda
            self.register_data_mesh_feature(
                group_name="agenda_metrics",
                feature_name="total_agendamentos",
                description="Total de agendamentos",
                domain_name="agenda",
                metric_name="total_agendamentos",
                data_type="int",
                tags=["agenda", "volume"]
            )
            
            self.register_data_mesh_feature(
                group_name="agenda_metrics",
                feature_name="taxa_ocupacao",
                description="Taxa de ocupação da agenda",
                domain_name="agenda",
                metric_name="taxa_ocupacao",
                data_type="float",
                tags=["agenda", "eficiencia"]
            )
            
            self.register_data_mesh_feature(
                group_name="agenda_metrics",
                feature_name="tempo_medio_atendimento",
                description="Tempo médio de atendimento",
                domain_name="agenda",
                metric_name="tempo_medio_atendimento",
                data_type="float",
                tags=["agenda", "eficiencia"]
            )
            
            # Registrar features do domínio Financeiro
            self.register_data_mesh_feature(
                group_name="financeiro_metrics",
                feature_name="total_receita",
                description="Total de receita",
                domain_name="financeiro",
                metric_name="total_receita",
                data_type="float",
                tags=["financeiro", "receita"]
            )
            
            self.register_data_mesh_feature(
                group_name="financeiro_metrics",
                feature_name="total_despesa",
                description="Total de despesa",
                domain_name="financeiro",
                metric_name="total_despesa",
                data_type="float",
                tags=["financeiro", "despesa"]
            )
            
            self.register_data_mesh_feature(
                group_name="financeiro_metrics",
                feature_name="resultado_liquido",
                description="Resultado líquido",
                domain_name="financeiro",
                metric_name="resultado_liquido",
                data_type="float",
                tags=["financeiro", "resultado"]
            )
            
            # Registrar features do domínio Paciente
            self.register_data_mesh_feature(
                group_name="paciente_metrics",
                feature_name="total_pacientes",
                description="Total de pacientes",
                domain_name="paciente",
                metric_name="total_pacientes",
                data_type="int",
                tags=["paciente", "volume"]
            )
            
            self.register_data_mesh_feature(
                group_name="paciente_metrics",
                feature_name="taxa_retorno",
                description="Taxa de retorno de pacientes",
                domain_name="paciente",
                metric_name="taxa_retorno",
                data_type="float",
                tags=["paciente", "engajamento"]
            )
            
            # Registrar features do domínio AmigoCare
            self.register_data_mesh_feature(
                group_name="amigocare_metrics",
                feature_name="nps_medio",
                description="NPS médio",
                domain_name="amigocare",
                metric_name="nps_medio",
                data_type="float",
                tags=["amigocare", "satisfacao"]
            )
            
            self.register_data_mesh_feature(
                group_name="amigocare_metrics",
                feature_name="total_leads",
                description="Total de leads",
                domain_name="amigocare",
                metric_name="total_leads",
                data_type="int",
                tags=["amigocare", "marketing"]
            )
            
            # Registrar features do domínio Visão 360
            self.register_data_mesh_feature(
                group_name="visao360_metrics",
                feature_name="lifetime_value_medio",
                description="Lifetime Value médio dos pacientes",
                domain_name="visao360",
                metric_name="lifetime_value_medio",
                data_type="float",
                tags=["visao360", "valor"]
            )
            
            logger.info("Features padrão registradas com sucesso")
            
        except Exception as e:
            logger.error(f"Erro ao registrar features padrão: {str(e)}")

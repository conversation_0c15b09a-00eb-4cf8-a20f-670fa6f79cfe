"""
Implementação da Feature Store.

Este módulo implementa a Feature Store para armazenar, gerenciar e servir
features para modelos de machine learning e análises.
"""
from typing import Dict, List, Any, Optional, Union, Callable
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import logging
import os
import pickle
from dataclasses import dataclass, field, asdict

# Configurar logging
logger = logging.getLogger(__name__)

@dataclass
class Feature:
    """Classe que representa uma feature na Feature Store."""
    
    name: str
    description: str
    domain: str
    data_type: str
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    version: int = 1
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    transformation: Optional[Callable] = None
    statistics: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Converte a feature para um dicionário.
        
        Returns:
            Dict[str, Any]: Representação da feature como dicionário
        """
        result = asdict(self)
        # Remover campos não serializáveis
        result.pop('transformation', None)
        # Converter datetime para string
        result['created_at'] = result['created_at'].isoformat()
        result['updated_at'] = result['updated_at'].isoformat()
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Feature':
        """
        Cria uma feature a partir de um dicionário.
        
        Args:
            data: Dicionário com dados da feature
            
        Returns:
            Feature: Feature criada
        """
        # Converter strings para datetime
        if 'created_at' in data and isinstance(data['created_at'], str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        if 'updated_at' in data and isinstance(data['updated_at'], str):
            data['updated_at'] = datetime.fromisoformat(data['updated_at'])
        
        # Remover transformation se existir
        transformation = data.pop('transformation', None)
        
        # Criar feature
        feature = cls(**data)
        
        # Adicionar transformation se existir
        if transformation:
            feature.transformation = transformation
        
        return feature


@dataclass
class FeatureGroup:
    """Classe que representa um grupo de features na Feature Store."""
    
    name: str
    description: str
    domain: str
    features: List[Feature] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    version: int = 1
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def add_feature(self, feature: Feature) -> None:
        """
        Adiciona uma feature ao grupo.
        
        Args:
            feature: Feature a ser adicionada
        """
        # Verificar se a feature já existe
        for i, f in enumerate(self.features):
            if f.name == feature.name:
                # Atualizar feature existente
                self.features[i] = feature
                self.updated_at = datetime.now()
                return
        
        # Adicionar nova feature
        self.features.append(feature)
        self.updated_at = datetime.now()
    
    def remove_feature(self, feature_name: str) -> bool:
        """
        Remove uma feature do grupo.
        
        Args:
            feature_name: Nome da feature a ser removida
            
        Returns:
            bool: True se a feature foi removida, False caso contrário
        """
        for i, feature in enumerate(self.features):
            if feature.name == feature_name:
                self.features.pop(i)
                self.updated_at = datetime.now()
                return True
        return False
    
    def get_feature(self, feature_name: str) -> Optional[Feature]:
        """
        Obtém uma feature pelo nome.
        
        Args:
            feature_name: Nome da feature
            
        Returns:
            Optional[Feature]: Feature encontrada ou None
        """
        for feature in self.features:
            if feature.name == feature_name:
                return feature
        return None
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Converte o grupo de features para um dicionário.
        
        Returns:
            Dict[str, Any]: Representação do grupo como dicionário
        """
        result = {
            'name': self.name,
            'description': self.description,
            'domain': self.domain,
            'features': [f.to_dict() for f in self.features],
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'version': self.version,
            'tags': self.tags,
            'metadata': self.metadata
        }
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FeatureGroup':
        """
        Cria um grupo de features a partir de um dicionário.
        
        Args:
            data: Dicionário com dados do grupo
            
        Returns:
            FeatureGroup: Grupo de features criado
        """
        # Extrair features
        features_data = data.pop('features', [])
        
        # Converter strings para datetime
        if 'created_at' in data and isinstance(data['created_at'], str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        if 'updated_at' in data and isinstance(data['updated_at'], str):
            data['updated_at'] = datetime.fromisoformat(data['updated_at'])
        
        # Criar grupo
        group = cls(**data)
        
        # Adicionar features
        for feature_data in features_data:
            feature = Feature.from_dict(feature_data)
            group.add_feature(feature)
        
        return group


class FeatureStore:
    """Classe principal da Feature Store."""
    
    _instance = None
    
    def __new__(cls):
        """Implementação de Singleton."""
        if cls._instance is None:
            cls._instance = super(FeatureStore, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """Inicializa a Feature Store."""
        if self._initialized:
            return
            
        self.feature_groups = {}
        self.feature_values = {}
        self.storage_path = os.path.join(os.path.dirname(__file__), 'storage')
        
        # Criar diretório de armazenamento se não existir
        if not os.path.exists(self.storage_path):
            os.makedirs(self.storage_path)
        
        # Carregar feature groups do armazenamento
        self._load_feature_groups()
        
        self._initialized = True
        logger.info("Feature Store inicializada")
    
    def _load_feature_groups(self) -> None:
        """Carrega os grupos de features do armazenamento."""
        try:
            groups_file = os.path.join(self.storage_path, 'feature_groups.json')
            if os.path.exists(groups_file):
                with open(groups_file, 'r') as f:
                    groups_data = json.load(f)
                
                for group_data in groups_data:
                    group = FeatureGroup.from_dict(group_data)
                    self.feature_groups[group.name] = group
                
                logger.info(f"Carregados {len(self.feature_groups)} grupos de features")
            
            # Carregar valores das features
            values_file = os.path.join(self.storage_path, 'feature_values.pkl')
            if os.path.exists(values_file):
                with open(values_file, 'rb') as f:
                    self.feature_values = pickle.load(f)
                
                logger.info(f"Carregados valores para {len(self.feature_values)} features")
        
        except Exception as e:
            logger.error(f"Erro ao carregar feature groups: {str(e)}")
    
    def _save_feature_groups(self) -> None:
        """Salva os grupos de features no armazenamento."""
        try:
            groups_data = [group.to_dict() for group in self.feature_groups.values()]
            
            groups_file = os.path.join(self.storage_path, 'feature_groups.json')
            with open(groups_file, 'w') as f:
                json.dump(groups_data, f, indent=2)
            
            # Salvar valores das features
            values_file = os.path.join(self.storage_path, 'feature_values.pkl')
            with open(values_file, 'wb') as f:
                pickle.dump(self.feature_values, f)
            
            logger.info(f"Salvos {len(self.feature_groups)} grupos de features")
        
        except Exception as e:
            logger.error(f"Erro ao salvar feature groups: {str(e)}")
    
    def create_feature_group(self, name: str, description: str, domain: str, 
                           tags: List[str] = None, metadata: Dict[str, Any] = None) -> FeatureGroup:
        """
        Cria um novo grupo de features.
        
        Args:
            name: Nome do grupo
            description: Descrição do grupo
            domain: Domínio do grupo
            tags: Tags do grupo
            metadata: Metadados do grupo
            
        Returns:
            FeatureGroup: Grupo de features criado
        """
        if name in self.feature_groups:
            raise ValueError(f"Grupo de features '{name}' já existe")
        
        group = FeatureGroup(
            name=name,
            description=description,
            domain=domain,
            tags=tags or [],
            metadata=metadata or {}
        )
        
        self.feature_groups[name] = group
        self._save_feature_groups()
        
        logger.info(f"Grupo de features '{name}' criado")
        return group
    
    def get_feature_group(self, name: str) -> Optional[FeatureGroup]:
        """
        Obtém um grupo de features pelo nome.
        
        Args:
            name: Nome do grupo
            
        Returns:
            Optional[FeatureGroup]: Grupo de features encontrado ou None
        """
        return self.feature_groups.get(name)
    
    def list_feature_groups(self, domain: str = None) -> List[FeatureGroup]:
        """
        Lista todos os grupos de features.
        
        Args:
            domain: Filtrar por domínio (opcional)
            
        Returns:
            List[FeatureGroup]: Lista de grupos de features
        """
        if domain:
            return [group for group in self.feature_groups.values() if group.domain == domain]
        return list(self.feature_groups.values())
    
    def delete_feature_group(self, name: str) -> bool:
        """
        Remove um grupo de features.
        
        Args:
            name: Nome do grupo
            
        Returns:
            bool: True se o grupo foi removido, False caso contrário
        """
        if name in self.feature_groups:
            del self.feature_groups[name]
            self._save_feature_groups()
            logger.info(f"Grupo de features '{name}' removido")
            return True
        return False
    
    def add_feature(self, group_name: str, feature: Feature) -> None:
        """
        Adiciona uma feature a um grupo.
        
        Args:
            group_name: Nome do grupo
            feature: Feature a ser adicionada
            
        Raises:
            ValueError: Se o grupo não existir
        """
        group = self.get_feature_group(group_name)
        if not group:
            raise ValueError(f"Grupo de features '{group_name}' não existe")
        
        group.add_feature(feature)
        self._save_feature_groups()
        
        logger.info(f"Feature '{feature.name}' adicionada ao grupo '{group_name}'")
    
    def get_feature(self, group_name: str, feature_name: str) -> Optional[Feature]:
        """
        Obtém uma feature pelo nome.
        
        Args:
            group_name: Nome do grupo
            feature_name: Nome da feature
            
        Returns:
            Optional[Feature]: Feature encontrada ou None
        """
        group = self.get_feature_group(group_name)
        if not group:
            return None
        
        return group.get_feature(feature_name)
    
    def set_feature_value(self, group_name: str, feature_name: str, value: Any) -> None:
        """
        Define o valor de uma feature.
        
        Args:
            group_name: Nome do grupo
            feature_name: Nome da feature
            value: Valor da feature
        """
        feature_key = f"{group_name}.{feature_name}"
        self.feature_values[feature_key] = value
        self._save_feature_groups()
    
    def get_feature_value(self, group_name: str, feature_name: str) -> Any:
        """
        Obtém o valor de uma feature.
        
        Args:
            group_name: Nome do grupo
            feature_name: Nome da feature
            
        Returns:
            Any: Valor da feature ou None
        """
        feature_key = f"{group_name}.{feature_name}"
        return self.feature_values.get(feature_key)
    
    def get_feature_values(self, group_name: str) -> Dict[str, Any]:
        """
        Obtém os valores de todas as features de um grupo.
        
        Args:
            group_name: Nome do grupo
            
        Returns:
            Dict[str, Any]: Valores das features
        """
        result = {}
        prefix = f"{group_name}."
        
        for key, value in self.feature_values.items():
            if key.startswith(prefix):
                feature_name = key[len(prefix):]
                result[feature_name] = value
        
        return result
    
    def get_feature_values_as_dataframe(self, group_name: str) -> pd.DataFrame:
        """
        Obtém os valores de todas as features de um grupo como DataFrame.
        
        Args:
            group_name: Nome do grupo
            
        Returns:
            pd.DataFrame: DataFrame com os valores das features
        """
        values = self.get_feature_values(group_name)
        return pd.DataFrame([values])
    
    def update_feature_from_data_mesh(self, group_name: str, feature_name: str, 
                                    domain_name: str, metric_name: str) -> None:
        """
        Atualiza o valor de uma feature a partir do Data Mesh.
        
        Args:
            group_name: Nome do grupo
            feature_name: Nome da feature
            domain_name: Nome do domínio no Data Mesh
            metric_name: Nome da métrica no domínio
        """
        try:
            # Importar o módulo de integração do Data Mesh
            from ..data_mesh.initialize import get_domain
            
            # Obter o domínio
            domain = get_domain(domain_name)
            if not domain:
                logger.error(f"Domínio '{domain_name}' não encontrado")
                return
            
            # Obter as métricas do domínio
            metrics = domain.get_metrics()
            
            # Verificar se a métrica existe
            if metric_name not in metrics:
                logger.error(f"Métrica '{metric_name}' não encontrada no domínio '{domain_name}'")
                return
            
            # Obter o valor da métrica
            value = metrics[metric_name]
            
            # Definir o valor da feature
            self.set_feature_value(group_name, feature_name, value)
            
            logger.info(f"Feature '{feature_name}' atualizada com valor do Data Mesh")
            
        except Exception as e:
            logger.error(f"Erro ao atualizar feature do Data Mesh: {str(e)}")
    
    def update_all_features_from_data_mesh(self) -> None:
        """Atualiza todas as features a partir do Data Mesh."""
        try:
            # Importar o módulo de integração do Data Mesh
            from ..data_mesh.initialize import get_all_domains
            
            # Obter todos os domínios
            domains = get_all_domains()
            
            # Iterar sobre os grupos de features
            for group_name, group in self.feature_groups.items():
                # Verificar se o domínio existe
                if group.domain in domains:
                    domain = domains[group.domain]
                    
                    # Obter as métricas do domínio
                    metrics = domain.get_metrics()
                    
                    # Iterar sobre as features do grupo
                    for feature in group.features:
                        # Verificar se a feature tem metadados de origem
                        if 'source' in feature.metadata and feature.metadata['source'] == 'data_mesh':
                            metric_name = feature.metadata.get('metric_name')
                            
                            # Verificar se a métrica existe
                            if metric_name and metric_name in metrics:
                                # Obter o valor da métrica
                                value = metrics[metric_name]
                                
                                # Definir o valor da feature
                                self.set_feature_value(group_name, feature.name, value)
                                
                                logger.info(f"Feature '{feature.name}' atualizada com valor do Data Mesh")
            
            logger.info("Todas as features atualizadas do Data Mesh")
            
        except Exception as e:
            logger.error(f"Erro ao atualizar features do Data Mesh: {str(e)}")
    
    def get_feature_statistics(self, group_name: str, feature_name: str) -> Dict[str, Any]:
        """
        Obtém estatísticas de uma feature.
        
        Args:
            group_name: Nome do grupo
            feature_name: Nome da feature
            
        Returns:
            Dict[str, Any]: Estatísticas da feature
        """
        feature = self.get_feature(group_name, feature_name)
        if not feature:
            return {}
        
        return feature.statistics

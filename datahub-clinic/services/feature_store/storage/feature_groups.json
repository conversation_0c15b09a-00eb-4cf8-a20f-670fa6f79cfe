[{"name": "agenda_metrics", "description": "Grupo de features agenda_metrics", "domain": "agenda", "features": [{"name": "total_agendamentos", "description": "Total de agendamentos", "domain": "agenda", "data_type": "int", "created_at": "2025-06-21T15:08:01.803847", "updated_at": "2025-06-21T15:08:01.803859", "version": 1, "tags": ["agenda", "volume"], "metadata": {"source": "data_mesh", "domain_name": "agenda", "metric_name": "total_agendamentos"}, "statistics": {}}, {"name": "taxa_ocupacao", "description": "Taxa de ocupação da agenda", "domain": "agenda", "data_type": "float", "created_at": "2025-06-21T15:08:01.809203", "updated_at": "2025-06-21T15:08:01.809211", "version": 1, "tags": ["agenda", "eficiencia"], "metadata": {"source": "data_mesh", "domain_name": "agenda", "metric_name": "taxa_ocupacao"}, "statistics": {}}, {"name": "tempo_medio_atendimento", "description": "Tempo médio de atendimento", "domain": "agenda", "data_type": "float", "created_at": "2025-06-21T15:08:01.813933", "updated_at": "2025-06-21T15:08:01.813941", "version": 1, "tags": ["agenda", "eficiencia"], "metadata": {"source": "data_mesh", "domain_name": "agenda", "metric_name": "tempo_medio_atendimento"}, "statistics": {}}], "created_at": "2025-05-11T18:21:41.060811", "updated_at": "2025-06-21T15:08:01.813948", "version": 1, "tags": [], "metadata": {}}, {"name": "financeiro_metrics", "description": "Grupo de features financeiro_metrics", "domain": "financeiro", "features": [{"name": "total_receita", "description": "Total de receita", "domain": "financeiro", "data_type": "float", "created_at": "2025-06-21T15:08:01.818145", "updated_at": "2025-06-21T15:08:01.818149", "version": 1, "tags": ["financeiro", "receita"], "metadata": {"source": "data_mesh", "domain_name": "financeiro", "metric_name": "total_receita"}, "statistics": {}}, {"name": "total_despesa", "description": "Total de despesa", "domain": "financeiro", "data_type": "float", "created_at": "2025-06-21T15:08:01.822150", "updated_at": "2025-06-21T15:08:01.822153", "version": 1, "tags": ["financeiro", "despesa"], "metadata": {"source": "data_mesh", "domain_name": "financeiro", "metric_name": "total_despesa"}, "statistics": {}}, {"name": "resultado_liquido", "description": "Resultado líquido", "domain": "financeiro", "data_type": "float", "created_at": "2025-06-21T15:08:01.826441", "updated_at": "2025-06-21T15:08:01.826444", "version": 1, "tags": ["financeiro", "resultado"], "metadata": {"source": "data_mesh", "domain_name": "financeiro", "metric_name": "resultado_liquido"}, "statistics": {}}], "created_at": "2025-05-11T18:21:41.084734", "updated_at": "2025-06-21T15:08:01.826447", "version": 1, "tags": [], "metadata": {}}, {"name": "paciente_metrics", "description": "Grupo de features paciente_metrics", "domain": "paciente", "features": [{"name": "total_pacientes", "description": "Total de pacientes", "domain": "paciente", "data_type": "int", "created_at": "2025-06-21T15:08:01.830031", "updated_at": "2025-06-21T15:08:01.830034", "version": 1, "tags": ["paciente", "volume"], "metadata": {"source": "data_mesh", "domain_name": "paciente", "metric_name": "total_pacientes"}, "statistics": {}}, {"name": "taxa_retorno", "description": "Taxa de retorno de pacientes", "domain": "paciente", "data_type": "float", "created_at": "2025-06-21T15:08:01.833571", "updated_at": "2025-06-21T15:08:01.833573", "version": 1, "tags": ["paciente", "engajamento"], "metadata": {"source": "data_mesh", "domain_name": "paciente", "metric_name": "taxa_retorno"}, "statistics": {}}], "created_at": "2025-05-11T18:21:41.133867", "updated_at": "2025-06-21T15:08:01.833575", "version": 1, "tags": [], "metadata": {}}, {"name": "amigocare_metrics", "description": "Grupo de features amigocare_metrics", "domain": "amigocare", "features": [{"name": "nps_medio", "description": "NPS médio", "domain": "amigocare", "data_type": "float", "created_at": "2025-06-21T15:08:01.836562", "updated_at": "2025-06-21T15:08:01.836565", "version": 1, "tags": ["amigocare", "satisfacao"], "metadata": {"source": "data_mesh", "domain_name": "amigocare", "metric_name": "nps_medio"}, "statistics": {}}, {"name": "total_leads", "description": "Total de leads", "domain": "amigocare", "data_type": "int", "created_at": "2025-06-21T15:08:01.839370", "updated_at": "2025-06-21T15:08:01.839373", "version": 1, "tags": ["amigocare", "marketing"], "metadata": {"source": "data_mesh", "domain_name": "amigocare", "metric_name": "total_leads"}, "statistics": {}}], "created_at": "2025-05-11T18:21:41.483299", "updated_at": "2025-06-21T15:08:01.839376", "version": 1, "tags": [], "metadata": {}}, {"name": "visao360_metrics", "description": "Grupo de features visao360_metrics", "domain": "visao360", "features": [{"name": "lifetime_value_medio", "description": "Lifetime Value médio dos pacientes", "domain": "visao360", "data_type": "float", "created_at": "2025-06-21T15:08:01.842808", "updated_at": "2025-06-21T15:08:01.842810", "version": 1, "tags": ["visao360", "valor"], "metadata": {"source": "data_mesh", "domain_name": "visao360", "metric_name": "lifetime_value_medio"}, "statistics": {}}], "created_at": "2025-05-11T18:21:41.630913", "updated_at": "2025-06-21T15:08:01.842813", "version": 1, "tags": [], "metadata": {}}]
"""
Serviço de integração com o GPT.
Responsável por enviar dados para o GPT e processar as respostas.
"""
import os
import json
import random
import requests
from datetime import datetime
from dotenv import load_dotenv

# Carregar variáveis de ambiente
load_dotenv()

class GPTService:
    """
    Classe responsável por integrar com o GPT para gerar insights.
    """

    @staticmethod
    def generate_insight(data, insight_type="list", category=None):
        """
        Gera um insight baseado nos dados fornecidos.

        Args:
            data (dict): Dados formatados para o GPT
            insight_type (str): Tipo de insight (list, text, stat)
            category (str, optional): Categoria do insight

        Returns:
            dict: Insight gerado
        """
        # Tentar usar a API real do GPT
        try:
            # Verificar se temos uma chave de API
            api_key = os.getenv('OPENAI_API_KEY')

            # Se temos API key, usar a API real
            if api_key:
                return GPTService._call_openai_api(data, insight_type, category)
            else:
                print("Chave da API OpenAI não encontrada. Usando simulação.")
                return GPTService._simulate_insight(data, insight_type, category)

        except Exception as e:
            print(f"Erro ao gerar insight com API: {e}")
            # Em caso de erro, usar a simulação
            return GPTService._simulate_insight(data, insight_type, category)

    @staticmethod
    def _create_insight(data, content, insight_type="list", category=None):
        """
        Cria um insight com base no conteúdo processado da API.

        Args:
            data (dict): Dados formatados para o GPT
            content: Conteúdo processado da resposta da API
            insight_type (str): Tipo de insight (list, text, stat)
            category (str, optional): Categoria do insight

        Returns:
            dict: Insight criado
        """
        # Extrair informações relevantes dos dados
        module_context = data.get('context', '').split(',')[0].replace('Dados do módulo ', '')

        # Determinar título e descrição com base no contexto
        title_map = {
            'agenda': 'Ações para Agenda',
            'financeiro': 'Ações Financeiras',
            'paciente': 'Ações para Pacientes',
            'amigocare': 'Ações para Satisfação',
            'visao360': 'Ações Integradas'
        }

        description_map = {
            'agenda': 'Otimize sua operação',
            'financeiro': 'Aumente sua receita',
            'paciente': 'Melhore a retenção',
            'amigocare': 'Eleve o NPS',
            'visao360': 'Integre seus dados'
        }

        # Definir título e descrição
        title = title_map.get(module_context, 'Ações Recomendadas')
        description = description_map.get(module_context, 'Implemente para resultados imediatos')

        # Determinar categoria se não fornecida
        if not category:
            category_map = {
                'agenda': 'Otimização',
                'financeiro': 'Receita',
                'paciente': 'Fidelização',
                'amigocare': 'Experiência',
                'visao360': 'Estratégia'
            }
            category = category_map.get(module_context, 'Ação Prioritária')

        # Montar o insight
        insight = {
            'title': title,
            'description': description,
            'content': content,
            'category': category,
            'insight_type': insight_type,
            'generated_at': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        return insight

    @staticmethod
    def _simulate_insight(data, insight_type="list", category=None):
        """
        Simula um insight baseado nos dados fornecidos.
        Usado para desenvolvimento e quando não há API key configurada.

        Args:
            data (dict): Dados formatados para o GPT
            insight_type (str): Tipo de insight (list, text, stat)
            category (str, optional): Categoria do insight

        Returns:
            dict: Insight simulado
        """
        # Extrair informações relevantes dos dados
        module_context = data.get('context', '').split(',')[0].replace('Dados do módulo ', '')
        report_context = data.get('context', '').split(',')[1].replace(' relatório ', '') if ',' in data.get('context', '') else ''

        # Determinar título e descrição com base no contexto - orientados a ações
        title_map = {
            'agenda': 'Ações para Agenda',
            'financeiro': 'Ações Financeiras',
            'paciente': 'Ações para Pacientes',
            'amigocare': 'Ações para Satisfação',
            'visao360': 'Ações Integradas'
        }

        description_map = {
            'agenda': 'Otimize sua operação',
            'financeiro': 'Aumente sua receita',
            'paciente': 'Melhore a retenção',
            'amigocare': 'Eleve o NPS',
            'visao360': 'Integre seus dados'
        }

        # Definir título e descrição
        title = title_map.get(module_context, 'Ações Recomendadas')
        description = description_map.get(module_context, 'Implemente para resultados imediatos')

        # Gerar conteúdo baseado no tipo de insight
        content = []

        if insight_type == "list":
            # Insights específicos por módulo - orientados a ações e alto impacto
            insights_by_module = {
                'agenda': [
                    f"<strong>Ação:</strong> Concentre profissionais entre 14h-16h (<strong>+{random.randint(15, 35)}%</strong> agendamentos)",
                    f"<strong>Ação:</strong> Promova horários de terça/quinta (<strong>{random.randint(20, 40)}%</strong> de ociosidade)",
                    f"<strong>Ação:</strong> Implemente confirmação via WhatsApp (<strong>-{random.randint(30, 60)}%</strong> faltas)"
                ],
                'financeiro': [
                    f"<strong>Ação:</strong> Amplie oferta de procedimentos estéticos (<strong>{random.randint(20, 40)}%</strong> da receita)",
                    f"<strong>Ação:</strong> Priorize pagamentos via PIX (<strong>+{random.randint(5, 15)}%/mês</strong> de crescimento)",
                    f"<strong>Ação:</strong> Implemente cobrança preventiva (<strong>{random.randint(10, 25)}%</strong> de atrasos)"
                ],
                'paciente': [
                    f"<strong>Ação:</strong> Crie programa de fidelização (<strong>{random.randint(25, 45)}%</strong> retornam em 30 dias)",
                    f"<strong>Ação:</strong> Incentive indicações (<strong>{random.randint(2, 4)}x</strong> mais conversões)",
                    f"<strong>Ação:</strong> Automatize follow-up em 48h (<strong>+{random.randint(20, 40)}%</strong> retornos)"
                ],
                'amigocare': [
                    f"<strong>Ação:</strong> Reduza tempo de espera para <15min (<strong>+{random.randint(10, 20)} pontos</strong> no NPS)",
                    f"<strong>Ação:</strong> Envie mensagem pós-consulta (<strong>+{random.randint(10, 25)}%</strong> no NPS)",
                    f"<strong>Ação:</strong> Treine equipe em atendimento (<strong>{random.randint(80, 95)}%</strong> das avaliações positivas)"
                ],
                'visao360': [
                    f"<strong>Ação:</strong> Complete histórico financeiro (<strong>{random.randint(2, 5)}x</strong> mais aprovações)",
                    f"<strong>Ação:</strong> Integre dados entre módulos (<strong>+{random.randint(30, 60)}%</strong> precisão)",
                    f"<strong>Ação:</strong> Otimize tempo de espera (<strong>{random.randint(60, 90)}%</strong> correlação com valor gasto)"
                ]
            }

            # Selecionar insights do módulo atual
            content = insights_by_module.get(module_context, [
                "<strong>Ação:</strong> Analise padrões de uso para otimizar recursos",
                "<strong>Ação:</strong> Implemente melhorias baseadas em dados",
                "<strong>Ação:</strong> Utilize correlações para decisões estratégicas"
            ])

        elif insight_type == "text":
            text_insights = {
                'agenda': f"<strong>PLANO DE AÇÃO:</strong> Implemente sistema de confirmação 24h antes para reduzir <strong>{random.randint(20, 40)}%</strong> das remarcações e aumentar produtividade em <strong>{random.randint(30, 50)}%</strong>.",
                'financeiro': f"<strong>PLANO DE AÇÃO:</strong> Distribua cobranças ao longo do mês para evitar concentração de <strong>{random.randint(15, 30)}%</strong> das receitas na última semana e melhorar fluxo de caixa em <strong>{random.randint(20, 40)}%</strong>.",
                'paciente': f"<strong>PLANO DE AÇÃO:</strong> Crie campanha de recuperação para os <strong>{random.randint(25, 45)}%</strong> de pacientes sem follow-up. Potencial de receita adicional: <strong>R$ {random.randint(30, 80)}K</strong>.",
                'amigocare': f"<strong>PLANO DE AÇÃO:</strong> Desenvolva programa de recuperação para pacientes com NPS < 7 que têm <strong>{random.randint(40, 70)}%</strong> menos retornos. Aumento potencial na retenção: <strong>{random.randint(15, 35)}%</strong>.",
                'visao360': f"<strong>PLANO DE AÇÃO:</strong> Implemente programa de fidelidade para pacientes com +3 visitas que geram <strong>{random.randint(30, 60)}%</strong> mais indicações. Potencial de amplificação: <strong>{random.randint(20, 50)}%</strong>."
            }

            content = text_insights.get(module_context, "<strong>PLANO DE AÇÃO:</strong> Implemente as recomendações para melhorar resultados imediatamente.")

        elif insight_type == "stat":
            # Para o tipo stat, precisamos de um valor, tendência e contexto
            stat_insights = {
                'agenda': {
                    'value': f"{random.randint(70, 95)}%",
                    'trend': random.choice([1, -1, 1]),  # Mais chance de ser positivo
                    'trend_text': f"{random.randint(3, 15)}% vs mês ant.",
                    'context': "Otimize horários para aumentar ocupação"
                },
                'financeiro': {
                    'value': f"R$ {random.randint(5, 50)}K",
                    'trend': random.choice([1, 1, -1]),  # Mais chance de ser positivo
                    'trend_text': f"{random.randint(5, 20)}% vs mês ant.",
                    'context': "Capture esta receita adicional potencial"
                },
                'paciente': {
                    'value': f"{random.randint(60, 85)}%",
                    'trend': random.choice([1, -1]),
                    'trend_text': f"{random.randint(2, 10)}% vs mês ant.",
                    'context': "Implemente programa de fidelização agora"
                },
                'amigocare': {
                    'value': f"{random.randint(30, 70)}",
                    'trend': random.choice([1, 1, -1]),  # Mais chance de ser positivo
                    'trend_text': f"{random.randint(5, 15)} pts vs mês ant.",
                    'context': "Melhore atendimento para aumentar NPS"
                },
                'visao360': {
                    'value': f"{random.randint(3, 8)}.{random.randint(1, 9)}x",
                    'trend': 1,  # Sempre positivo
                    'trend_text': "Aumento eficiência",
                    'context': "Integre todos os módulos para este ganho"
                }
            }

            content = stat_insights.get(module_context, {
                'value': f"{random.randint(10, 90)}%",
                'trend': random.choice([1, -1]),
                'trend_text': f"{random.randint(5, 15)}% variação",
                'context': "Tome ação imediata para otimizar este indicador"
            })

        # Determinar categoria se não fornecida
        if not category:
            category_map = {
                'agenda': 'Otimização',
                'financeiro': 'Receita',
                'paciente': 'Fidelização',
                'amigocare': 'Experiência',
                'visao360': 'Estratégia'
            }
            category = category_map.get(module_context, 'Ação Prioritária')

        # Montar o insight
        insight = {
            'title': title,
            'description': description,
            'content': content,
            'category': category,
            'insight_type': insight_type,
            'generated_at': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        return insight

    @staticmethod
    def _call_openai_api(data, insight_type="list", category=None):
        """
        Faz uma chamada real à API do OpenAI para gerar insights.

        Args:
            data (dict): Dados formatados para o GPT
            insight_type (str): Tipo de insight (list, text, stat)
            category (str, optional): Categoria do insight

        Returns:
            dict: Insight gerado pela API do OpenAI
        """
        try:
            # Obter a chave da API do ambiente
            api_key = os.getenv("OPENAI_API_KEY")
            if not api_key:
                print("Chave da API OpenAI não encontrada. Usando simulação.")
                return GPTService._simulate_insight(data, insight_type, category)

            # Preparar o contexto para o GPT
            module_context = data.get('context', '').split(',')[0].replace('Dados do módulo ', '')

            # Construir o prompt baseado no tipo de insight
            if insight_type == "list":
                prompt = f"""
                Você é um assistente especializado em análise de dados para clínicas médicas.
                Gere 3 insights acionáveis para o módulo {module_context} com base nos dados fornecidos.
                Cada insight deve começar com "Ação:" e incluir uma recomendação clara e o impacto esperado.
                Formato: ["<strong>Ação:</strong> Recomendação específica (impacto em <strong>percentual</strong>)", ...]
                """
            elif insight_type == "text":
                prompt = f"""
                Você é um assistente especializado em análise de dados para clínicas médicas.
                Gere um plano de ação para o módulo {module_context} com base nos dados fornecidos.
                O plano deve começar com "<strong>PLANO DE AÇÃO:</strong>" e incluir uma recomendação clara e o impacto esperado.
                Formato: "<strong>PLANO DE AÇÃO:</strong> Recomendação específica para aumentar/reduzir <strong>X%</strong> de alguma métrica."
                """
            elif insight_type == "stat":
                prompt = f"""
                Você é um assistente especializado em análise de dados para clínicas médicas.
                Gere uma estatística importante para o módulo {module_context} com base nos dados fornecidos.
                Inclua um valor percentual ou monetário, uma tendência (positiva ou negativa) e um contexto acionável.
                Retorne em formato JSON: {{
                  "value": "X%",
                  "trend": 1 ou -1,
                  "trend_text": "Y% vs mês ant.",
                  "context": "Ação recomendada"
                }}
                """
            else:
                return GPTService._simulate_insight(data, insight_type, category)

            # Fazer a chamada à API usando a nova sintaxe do OpenAI (versão 1.0.0+)
            try:
                import openai
                client = openai.OpenAI(api_key=api_key)

                response = client.chat.completions.create(
                    model="gpt-4o-mini",  # GPT-4 Nano
                    messages=[
                        {"role": "system", "content": prompt},
                        {"role": "user", "content": f"Dados do módulo {module_context}: {json.dumps(data)}"}
                    ],
                    temperature=0.7,
                    max_tokens=500
                )

                # Extrair a resposta
                content = response.choices[0].message.content
                return GPTService._format_insight(content, insight_type, category)

            except ImportError:
                # Fallback para requests se o openai não estiver disponível
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {api_key}"
                }

                payload = {
                    "model": "gpt-4o-mini",  # GPT-4 Nano
                    "messages": [
                        {"role": "system", "content": prompt},
                        {"role": "user", "content": f"Dados do módulo {module_context}: {json.dumps(data)}"}
                    ],
                    "temperature": 0.7,
                    "max_tokens": 500
                }

                response = requests.post(
                    "https://api.openai.com/v1/chat/completions",
                    headers=headers,
                    json=payload
                )

                # Processar a resposta do método requests
                if response.status_code != 200:
                    print(f"Erro na API OpenAI: {response.status_code} - {response.text}")
                    return GPTService._simulate_insight(data, insight_type, category)

                # Processar a resposta
                response_data = response.json()
                content = response_data["choices"][0]["message"]["content"]

            # Processar o conteúdo baseado no tipo de insight
            if insight_type == "list":
                try:
                    # Tentar extrair uma lista do texto
                    import re
                    items = re.findall(r'"([^"]*)"', content)
                    if not items:
                        items = content.split("\n")
                        items = [item.strip() for item in items if item.strip()]

                    # Limpar itens
                    items = [item.replace("- ", "") for item in items]

                    # Se ainda não temos itens, usar simulação
                    if not items or len(items) < 2:
                        return GPTService._simulate_insight(data, insight_type, category)

                    # Limitar a 3 itens
                    items = items[:3]

                    # Criar insight com o conteúdo processado
                    return GPTService._create_insight(data, items, insight_type, category)
                except Exception as e:
                    print(f"Erro ao processar resposta de lista: {e}")
                    return GPTService._simulate_insight(data, insight_type, category)

            elif insight_type == "text":
                # Usar o texto diretamente
                return GPTService._create_insight(data, content, insight_type, category)

            elif insight_type == "stat":
                try:
                    # Tentar extrair JSON
                    import re
                    json_match = re.search(r'\{.*\}', content, re.DOTALL)
                    if json_match:
                        stat_data = json.loads(json_match.group(0))
                        return GPTService._create_insight(data, stat_data, insight_type, category)
                    else:
                        return GPTService._simulate_insight(data, insight_type, category)
                except Exception as e:
                    print(f"Erro ao processar resposta de estatística: {e}")
                    return GPTService._simulate_insight(data, insight_type, category)

            # Fallback para simulação
            return GPTService._simulate_insight(data, insight_type, category)

        except Exception as e:
            print(f"Erro ao chamar API OpenAI: {e}")
            # Em caso de erro, usar a simulação
            return GPTService._simulate_insight(data, insight_type, category)

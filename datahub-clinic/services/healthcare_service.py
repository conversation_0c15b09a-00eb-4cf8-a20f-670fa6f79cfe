"""
Serviço especializado para análise de dados de saúde.
"""
import os
import json
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import logging

# Configurar logging
logger = logging.getLogger(__name__)

class HealthcareService:
    """Serviço especializado para análise de dados de saúde."""
    
    def __init__(self):
        """Inicializa o serviço."""
        self.metrics = {
            "patient_journey": ["agendamento", "check-in", "atendimento", "pagamento", "retorno"],
            "kpis": ["taxa_ocupacao", "tempo_medio_atendimento", "satisfacao_paciente", "taxa_retorno", "rentabilidade_procedimento"],
            "specialties": ["clínica geral", "odontologia", "dermatologia", "cardiologia", "ortopedia", "pediatria", "ginecologia", "oftalmologia"]
        }
    
    def analyze_patient_journey(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analisa a jornada do paciente na clínica.
        
        Args:
            data: Dados da aplicação
            
        Returns:
            dict: Análise da jornada do paciente
        """
        try:
            # Extrair dados relevantes
            agendamentos = self._extract_appointments(data)
            pacientes = self._extract_patients(data)
            
            if not agendamentos or not pacientes:
                return {
                    "status": "error",
                    "message": "Dados insuficientes para análise de jornada do paciente",
                    "journey_stages": []
                }
            
            # Analisar estágios da jornada
            journey_stages = self._analyze_journey_stages(agendamentos, pacientes)
            
            # Identificar gargalos
            bottlenecks = self._identify_bottlenecks(journey_stages)
            
            # Calcular métricas de tempo
            time_metrics = self._calculate_time_metrics(journey_stages)
            
            return {
                "status": "success",
                "journey_stages": journey_stages,
                "bottlenecks": bottlenecks,
                "time_metrics": time_metrics,
                "recommendations": self._generate_journey_recommendations(bottlenecks, time_metrics)
            }
        
        except Exception as e:
            logger.error(f"Erro ao analisar jornada do paciente: {str(e)}")
            return {
                "status": "error",
                "message": f"Erro ao analisar jornada do paciente: {str(e)}",
                "journey_stages": []
            }
    
    def predict_appointment_demand(self, data: Dict[str, Any], days_ahead: int = 30) -> Dict[str, Any]:
        """
        Prevê a demanda de agendamentos para os próximos dias.
        
        Args:
            data: Dados da aplicação
            days_ahead: Número de dias para previsão
            
        Returns:
            dict: Previsão de demanda
        """
        try:
            # Extrair dados de agendamentos
            agendamentos = self._extract_appointments(data)
            
            if not agendamentos:
                return {
                    "status": "error",
                    "message": "Dados insuficientes para previsão de demanda",
                    "forecast": []
                }
            
            # Converter para DataFrame
            df = pd.DataFrame(agendamentos)
            
            # Verificar se há coluna de data
            date_columns = [col for col in df.columns if 'data' in col.lower() or 'date' in col.lower()]
            if not date_columns:
                return {
                    "status": "error",
                    "message": "Dados de agendamento não contêm informação de data",
                    "forecast": []
                }
            
            # Usar a primeira coluna de data encontrada
            date_col = date_columns[0]
            
            # Converter para datetime
            df[date_col] = pd.to_datetime(df[date_col], errors='coerce')
            
            # Remover valores nulos
            df = df.dropna(subset=[date_col])
            
            # Agrupar por data e contar agendamentos
            daily_counts = df.groupby(df[date_col].dt.date).size().reset_index()
            daily_counts.columns = ['date', 'count']
            
            # Calcular média móvel de 7 dias
            if len(daily_counts) >= 7:
                daily_counts['moving_avg'] = daily_counts['count'].rolling(window=7).mean()
                daily_counts = daily_counts.dropna()
            else:
                daily_counts['moving_avg'] = daily_counts['count'].mean()
            
            # Gerar previsão simples baseada na média móvel e padrões semanais
            forecast = self._generate_simple_forecast(daily_counts, days_ahead)
            
            return {
                "status": "success",
                "forecast": forecast,
                "avg_daily_appointments": round(daily_counts['count'].mean(), 2),
                "peak_day": self._find_peak_day(daily_counts),
                "recommendations": self._generate_demand_recommendations(forecast)
            }
        
        except Exception as e:
            logger.error(f"Erro ao prever demanda de agendamentos: {str(e)}")
            return {
                "status": "error",
                "message": f"Erro ao prever demanda de agendamentos: {str(e)}",
                "forecast": []
            }
    
    def analyze_procedure_profitability(self, data: Dict[str, Any], procedure_type: Optional[str] = None) -> Dict[str, Any]:
        """
        Analisa a rentabilidade por procedimento.
        
        Args:
            data: Dados da aplicação
            procedure_type: Tipo de procedimento específico para análise
            
        Returns:
            dict: Análise de rentabilidade
        """
        try:
            # Extrair dados financeiros e de procedimentos
            financeiro = self._extract_financial_data(data)
            procedimentos = self._extract_procedures(data)
            
            if not financeiro or not procedimentos:
                return {
                    "status": "error",
                    "message": "Dados insuficientes para análise de rentabilidade",
                    "procedures": []
                }
            
            # Calcular rentabilidade por procedimento
            profitability = self._calculate_procedure_profitability(financeiro, procedimentos)
            
            # Filtrar por tipo de procedimento, se especificado
            if procedure_type and profitability:
                profitability = [p for p in profitability if p.get('tipo', '').lower() == procedure_type.lower()]
            
            # Ordenar por rentabilidade
            profitability = sorted(profitability, key=lambda x: x.get('rentabilidade', 0), reverse=True)
            
            return {
                "status": "success",
                "procedures": profitability,
                "most_profitable": profitability[0] if profitability else None,
                "least_profitable": profitability[-1] if profitability else None,
                "recommendations": self._generate_profitability_recommendations(profitability)
            }
        
        except Exception as e:
            logger.error(f"Erro ao analisar rentabilidade por procedimento: {str(e)}")
            return {
                "status": "error",
                "message": f"Erro ao analisar rentabilidade por procedimento: {str(e)}",
                "procedures": []
            }
    
    def detect_patient_churn_risk(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Detecta pacientes com risco de abandono.
        
        Args:
            data: Dados da aplicação
            
        Returns:
            dict: Análise de risco de abandono
        """
        try:
            # Extrair dados de pacientes e atendimentos
            pacientes = self._extract_patients(data)
            atendimentos = self._extract_appointments(data)
            
            if not pacientes or not atendimentos:
                return {
                    "status": "error",
                    "message": "Dados insuficientes para análise de risco de abandono",
                    "at_risk_patients": []
                }
            
            # Identificar pacientes com risco de abandono
            at_risk_patients = self._identify_churn_risk(pacientes, atendimentos)
            
            return {
                "status": "success",
                "at_risk_patients": at_risk_patients,
                "total_at_risk": len(at_risk_patients),
                "risk_percentage": round(len(at_risk_patients) / len(pacientes) * 100, 2) if pacientes else 0,
                "recommendations": self._generate_retention_recommendations(at_risk_patients)
            }
        
        except Exception as e:
            logger.error(f"Erro ao detectar pacientes com risco de abandono: {str(e)}")
            return {
                "status": "error",
                "message": f"Erro ao detectar pacientes com risco de abandono: {str(e)}",
                "at_risk_patients": []
            }
    
    # Métodos auxiliares para extração de dados
    
    def _extract_appointments(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extrai dados de agendamentos."""
        appointments = []
        
        # Tentar extrair de diferentes locais nos dados
        if "agenda" in data and "agendamentos" in data["agenda"]:
            appointments.extend(data["agenda"]["agendamentos"])
        
        if "visao360" in data and "agendamentos_integrados" in data["visao360"]:
            appointments.extend(data["visao360"]["agendamentos_integrados"])
        
        return appointments
    
    def _extract_patients(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extrai dados de pacientes."""
        patients = []
        
        # Tentar extrair de diferentes locais nos dados
        if "paciente" in data and "atendimentos_realizados" in data["paciente"]:
            # Extrair pacientes únicos dos atendimentos
            patient_ids = set()
            for atendimento in data["paciente"]["atendimentos_realizados"]:
                if "paciente_id" in atendimento and "paciente" in atendimento:
                    patient_id = atendimento["paciente_id"]
                    if patient_id not in patient_ids:
                        patient_ids.add(patient_id)
                        patients.append({
                            "paciente_id": patient_id,
                            "nome": atendimento["paciente"],
                            "ultimo_atendimento": atendimento.get("data", None)
                        })
        
        if "visao360" in data and "pacientes_integrados" in data["visao360"]:
            # Adicionar pacientes da visão 360
            existing_ids = {p.get("paciente_id") for p in patients}
            for paciente in data["visao360"]["pacientes_integrados"]:
                if "paciente_id" in paciente and paciente["paciente_id"] not in existing_ids:
                    patients.append(paciente)
                    existing_ids.add(paciente["paciente_id"])
        
        return patients
    
    def _extract_financial_data(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extrai dados financeiros."""
        financial_data = []
        
        # Tentar extrair de diferentes locais nos dados
        if "financeiro" in data:
            if "contas_receber" in data["financeiro"]:
                financial_data.extend(data["financeiro"]["contas_receber"])
            
            if "fluxo_caixa" in data["financeiro"]:
                financial_data.extend(data["financeiro"]["fluxo_caixa"])
        
        if "visao360" in data and "transacoes_integradas" in data["visao360"]:
            financial_data.extend(data["visao360"]["transacoes_integradas"])
        
        return financial_data
    
    def _extract_procedures(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extrai dados de procedimentos."""
        procedures = []
        
        # Tentar extrair de diferentes locais nos dados
        if "paciente" in data:
            if "atendimentos_realizados" in data["paciente"]:
                for atendimento in data["paciente"]["atendimentos_realizados"]:
                    if "procedimento" in atendimento:
                        procedures.append({
                            "tipo": atendimento["procedimento"],
                            "valor": atendimento.get("valor", 0),
                            "data": atendimento.get("data", None),
                            "paciente_id": atendimento.get("paciente_id", None)
                        })
        
        return procedures
    
    # Implementações simuladas dos métodos de análise
    
    def _analyze_journey_stages(self, agendamentos, pacientes):
        """Analisa os estágios da jornada do paciente."""
        # Implementação simulada
        return [
            {"stage": "agendamento", "avg_time": 5, "satisfaction": 4.2},
            {"stage": "check-in", "avg_time": 8, "satisfaction": 3.8},
            {"stage": "atendimento", "avg_time": 25, "satisfaction": 4.5},
            {"stage": "pagamento", "avg_time": 7, "satisfaction": 3.9},
            {"stage": "retorno", "avg_time": None, "satisfaction": 4.0}
        ]
    
    def _identify_bottlenecks(self, journey_stages):
        """Identifica gargalos na jornada do paciente."""
        # Implementação simulada
        return [
            {"stage": "check-in", "issue": "Tempo de espera elevado", "impact": "Alto"},
            {"stage": "pagamento", "issue": "Satisfação abaixo da média", "impact": "Médio"}
        ]
    
    def _calculate_time_metrics(self, journey_stages):
        """Calcula métricas de tempo para a jornada do paciente."""
        # Implementação simulada
        return {
            "total_avg_time": 45,
            "longest_stage": "atendimento",
            "shortest_stage": "agendamento"
        }
    
    def _generate_simple_forecast(self, daily_counts, days_ahead):
        """Gera uma previsão simples de demanda."""
        # Implementação simulada
        today = datetime.now().date()
        forecast = []
        
        for i in range(days_ahead):
            forecast_date = today + timedelta(days=i)
            forecast.append({
                "date": forecast_date.strftime("%Y-%m-%d"),
                "weekday": forecast_date.strftime("%A"),
                "predicted_appointments": round(daily_counts['moving_avg'].mean() * (1.2 if forecast_date.weekday() < 5 else 0.7), 0)
            })
        
        return forecast
    
    def _find_peak_day(self, daily_counts):
        """Encontra o dia de pico de agendamentos."""
        # Implementação simulada
        return "Segunda-feira"
    
    def _calculate_procedure_profitability(self, financeiro, procedimentos):
        """Calcula a rentabilidade por procedimento."""
        # Implementação simulada
        return [
            {"tipo": "Consulta", "rentabilidade": 85, "custo_medio": 45, "receita_media": 130},
            {"tipo": "Exame", "rentabilidade": 70, "custo_medio": 60, "receita_media": 130},
            {"tipo": "Procedimento", "rentabilidade": 65, "custo_medio": 120, "receita_media": 185}
        ]
    
    def _identify_churn_risk(self, pacientes, atendimentos):
        """Identifica pacientes com risco de abandono."""
        # Implementação simulada
        return [
            {"paciente_id": "P001", "nome": "João Silva", "risco": "Alto", "ultimo_atendimento": "2023-01-15", "dias_sem_contato": 180},
            {"paciente_id": "P015", "nome": "Maria Oliveira", "risco": "Médio", "ultimo_atendimento": "2023-03-22", "dias_sem_contato": 120}
        ]
    
    # Métodos para gerar recomendações
    
    def _generate_journey_recommendations(self, bottlenecks, time_metrics):
        """Gera recomendações para melhorar a jornada do paciente."""
        # Implementação simulada
        return [
            "Otimizar o processo de check-in com pré-cadastro online",
            "Implementar sistema de pagamento móvel para reduzir tempo de espera",
            "Enviar lembretes de consulta com instruções de preparação"
        ]
    
    def _generate_demand_recommendations(self, forecast):
        """Gera recomendações para gestão de demanda."""
        # Implementação simulada
        return [
            "Aumentar a capacidade de atendimento nas segundas-feiras",
            "Oferecer descontos para consultas em horários de baixa demanda",
            "Implementar sistema de agendamento online para distribuir melhor a demanda"
        ]
    
    def _generate_profitability_recommendations(self, profitability):
        """Gera recomendações para melhorar a rentabilidade."""
        # Implementação simulada
        return [
            "Focar em aumentar o volume de consultas, que têm a maior rentabilidade",
            "Revisar custos dos procedimentos com menor rentabilidade",
            "Implementar pacotes de serviços para aumentar o ticket médio"
        ]
    
    def _generate_retention_recommendations(self, at_risk_patients):
        """Gera recomendações para retenção de pacientes."""
        # Implementação simulada
        return [
            "Implementar programa de fidelidade com benefícios progressivos",
            "Realizar campanhas de reativação para pacientes sem contato há mais de 90 dias",
            "Oferecer check-up gratuito para pacientes de alto risco de abandono"
        ]

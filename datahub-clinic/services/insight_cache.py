"""
Serviço de cache para insights gerados pelo GPT.
Armazena insights para evitar chamadas desnecessárias à API.
"""
import time
from datetime import datetime, timedelta

class InsightCache:
    """
    Classe responsável por gerenciar o cache de insights.
    """
    
    # Cache de insights: {chave: (timestamp, insight)}
    _cache = {}
    
    # Tempo de expiração do cache em segundos (30 minutos)
    _expiration_time = 30 * 60
    
    @classmethod
    def get_insight(cls, key):
        """
        Obtém um insight do cache.
        
        Args:
            key (str): Chave do insight
            
        Returns:
            dict: Insight armazenado ou None se não encontrado ou expirado
        """
        if key not in cls._cache:
            return None
            
        timestamp, insight = cls._cache[key]
        
        # Verificar se o cache expirou
        if time.time() - timestamp > cls._expiration_time:
            # Remover do cache
            del cls._cache[key]
            return None
            
        return insight
    
    @classmethod
    def set_insight(cls, key, insight):
        """
        Armazena um insight no cache.
        
        Args:
            key (str): Chave do insight
            insight (dict): Insight a ser armazenado
            
        Returns:
            None
        """
        cls._cache[key] = (time.time(), insight)
    
    @classmethod
    def generate_key(cls, module_name, report_name, insight_type):
        """
        Gera uma chave para o cache.
        
        Args:
            module_name (str): Nome do módulo
            report_name (str): Nome do relatório
            insight_type (str): Tipo de insight
            
        Returns:
            str: Chave para o cache
        """
        return f"{module_name}:{report_name}:{insight_type}"
    
    @classmethod
    def clear_cache(cls):
        """
        Limpa todo o cache.
        
        Returns:
            None
        """
        cls._cache.clear()
    
    @classmethod
    def clear_expired(cls):
        """
        Remove insights expirados do cache.
        
        Returns:
            int: Número de insights removidos
        """
        keys_to_remove = []
        
        for key, (timestamp, _) in cls._cache.items():
            if time.time() - timestamp > cls._expiration_time:
                keys_to_remove.append(key)
                
        for key in keys_to_remove:
            del cls._cache[key]
            
        return len(keys_to_remove)

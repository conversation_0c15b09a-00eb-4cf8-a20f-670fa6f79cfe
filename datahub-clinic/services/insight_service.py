"""
Serviço de insights para widgets.
Integra os serviços de agregação de dados, GPT e cache.
"""
from .data_aggregator import DataAggregator
from .gpt_service import GPTService
from .insight_cache import InsightCache

class InsightService:
    """
    Classe responsável por gerar insights para widgets.
    """

    @staticmethod
    def get_insights_for_page(module_name, report_name, mock_data, num_insights=3, insight_types=None, page_context=None):
        """
        Obtém insights para uma página específica.

        Args:
            module_name (str): Nome do módulo
            report_name (str): Nome do relatório
            mock_data (dict): Dados mockados
            num_insights (int, optional): Número de insights a serem gerados
            insight_types (list, optional): Lista de tipos de insights a serem gerados
            page_context (dict, optional): Contexto específico da página

        Returns:
            list: Lista de insights gerados
        """
        if insight_types is None:
            insight_types = ["list", "text", "stat"]

        insights = []

        # Se temos contexto da página, usamos uma chave de cache diferente
        context_hash = ""
        if page_context:
            import hashlib
            import json
            # Criar um hash do contexto da página para usar na chave de cache
            context_str = json.dumps(page_context, sort_keys=True)
            context_hash = hashlib.md5(context_str.encode()).hexdigest()[:8]

        for i in range(num_insights):
            # Selecionar tipo de insight
            insight_type = insight_types[i % len(insight_types)]

            # Verificar se já temos no cache
            cache_key = InsightCache.generate_key(module_name, report_name, f"{insight_type}_{i}{context_hash}")
            cached_insight = InsightCache.get_insight(cache_key)

            if cached_insight:
                insights.append(cached_insight)
                continue

            # Obter dados para o insight
            data = DataAggregator.get_data_for_insight(module_name, report_name, mock_data, page_context)

            # Gerar insight
            insight = GPTService.generate_insight(data, insight_type)

            # Armazenar no cache
            InsightCache.set_insight(cache_key, insight)

            # Adicionar à lista
            insights.append(insight)

        return insights

    @staticmethod
    def get_single_insight(module_name, report_name, mock_data, insight_type="list", category=None, page_context=None):
        """
        Obtém um único insight.

        Args:
            module_name (str): Nome do módulo
            report_name (str): Nome do relatório
            mock_data (dict): Dados mockados
            insight_type (str, optional): Tipo de insight
            category (str, optional): Categoria do insight
            page_context (dict, optional): Contexto específico da página

        Returns:
            dict: Insight gerado
        """
        # Se temos contexto da página, usamos uma chave de cache diferente
        context_hash = ""
        if page_context:
            import hashlib
            import json
            # Criar um hash do contexto da página para usar na chave de cache
            context_str = json.dumps(page_context, sort_keys=True)
            context_hash = hashlib.md5(context_str.encode()).hexdigest()[:8]

        # Verificar se já temos no cache
        cache_key = InsightCache.generate_key(module_name, report_name, f"{insight_type}{context_hash}")
        cached_insight = InsightCache.get_insight(cache_key)

        if cached_insight:
            return cached_insight

        # Obter dados para o insight
        data = DataAggregator.get_data_for_insight(module_name, report_name, mock_data, page_context)

        # Gerar insight
        insight = GPTService.generate_insight(data, insight_type, category)

        # Armazenar no cache
        InsightCache.set_insight(cache_key, insight)

        return insight

"""
Serviço para integração com LangChain e processamento de linguagem natural.
Versão compatível com Mac M2 usando apenas bibliotecas básicas do Python.
"""
import os
import json
import pandas as pd
import re
import math
from collections import Counter
from typing import Dict, List, Any, Optional
import openai
from dotenv import load_dotenv

# Carregar variáveis de ambiente
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

# Configurar a API da OpenAI
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")

def cosine_similarity_simple(vec1, vec2):
    """Calcula similaridade de cosseno entre dois vetores."""
    dot_product = sum(a * b for a, b in zip(vec1, vec2))
    magnitude1 = math.sqrt(sum(a * a for a in vec1))
    magnitude2 = math.sqrt(sum(a * a for a in vec2))

    if magnitude1 == 0 or magnitude2 == 0:
        return 0

    return dot_product / (magnitude1 * magnitude2)

def text_to_vector(text):
    """Converte texto em vetor simples usando contagem de palavras."""
    # Limpar e dividir o texto
    words = re.findall(r'\b\w+\b', text.lower())
    return Counter(words)

class SimpleVectorStore:
    """Vector store simples usando apenas bibliotecas básicas do Python."""

    def __init__(self):
        self.documents = []
        self.document_vectors = []
        self.vocabulary = set()

    def add_documents(self, documents):
        """Adiciona documentos ao vector store."""
        self.documents = documents
        self.document_vectors = []

        # Construir vocabulário
        for doc in documents:
            words = re.findall(r'\b\w+\b', doc.page_content.lower())
            self.vocabulary.update(words)

        # Converter vocabulário para lista ordenada
        vocab_list = sorted(list(self.vocabulary))

        # Criar vetores para cada documento
        for doc in documents:
            word_count = text_to_vector(doc.page_content)
            vector = [word_count.get(word, 0) for word in vocab_list]
            self.document_vectors.append(vector)

    def similarity_search(self, query: str, k: int = 3):
        """Busca documentos similares à query."""
        if not self.documents:
            return []

        # Converter query em vetor
        query_word_count = text_to_vector(query)
        vocab_list = sorted(list(self.vocabulary))
        query_vector = [query_word_count.get(word, 0) for word in vocab_list]

        # Calcular similaridades
        similarities = []
        for i, doc_vector in enumerate(self.document_vectors):
            sim = cosine_similarity_simple(query_vector, doc_vector)
            similarities.append((sim, i))

        # Ordenar por similaridade e retornar os k melhores
        similarities.sort(reverse=True)
        return [self.documents[i] for sim, i in similarities[:k] if sim > 0]

class Document:
    """Classe simples para representar documentos."""

    def __init__(self, page_content: str, metadata: Dict[str, Any] = None):
        self.page_content = page_content
        self.metadata = metadata or {}

class LangChainService:
    """Serviço para integração com LangChain usando scikit-learn."""

    def __init__(self):
        """Inicializa o serviço."""
        # Obter a chave da API
        self.api_key = os.getenv("OPENAI_API_KEY")

        # Inicializar o vector store simples
        self.vector_store = None
        self.data_context = {}

    def initialize_vector_store(self, data: Dict[str, Any]):
        """
        Inicializa o vector store com os dados da aplicação.

        Args:
            data: Dados da aplicação
        """
        # Converter dados para documentos
        documents = []

        for module_name, module_data in data.items():
            if isinstance(module_data, dict):
                for report_name, report_data in module_data.items():
                    if isinstance(report_data, list) and len(report_data) > 0:
                        # Converter lista para DataFrame
                        df = pd.DataFrame(report_data)

                        # Adicionar metadados
                        metadata = {
                            "module": module_name,
                            "report": report_name,
                            "columns": list(df.columns),
                            "row_count": len(df)
                        }

                        # Adicionar estatísticas básicas para colunas numéricas
                        numeric_stats = {}
                        for col in df.columns:
                            if pd.api.types.is_numeric_dtype(df[col]):
                                numeric_stats[col] = {
                                    "mean": float(df[col].mean()),
                                    "median": float(df[col].median()),
                                    "min": float(df[col].min()),
                                    "max": float(df[col].max())
                                }

                        metadata["numeric_stats"] = numeric_stats

                        # Converter DataFrame para texto
                        text = f"Módulo: {module_name}\nRelatório: {report_name}\n"
                        text += f"Colunas: {', '.join(map(str, df.columns))}\n"
                        text += f"Número de linhas: {len(df)}\n\n"

                        # Adicionar amostra de dados
                        text += "Amostra de dados:\n"
                        text += df.head(5).to_string() + "\n\n"

                        # Adicionar estatísticas
                        text += "Estatísticas:\n"
                        for col, stats in numeric_stats.items():
                            text += f"{col}: média={stats['mean']:.2f}, mediana={stats['median']:.2f}, min={stats['min']:.2f}, max={stats['max']:.2f}\n"

                        # Criar documento
                        document = Document(page_content=text, metadata=metadata)
                        documents.append(document)

                        # Armazenar dados no contexto
                        self.data_context[f"{module_name}.{report_name}"] = {
                            "data": report_data,
                            "metadata": metadata
                        }

        # Dividir documentos em chunks simples (sem dependência externa)
        docs = []
        for doc in documents:
            # Dividir texto em chunks de 1000 caracteres
            text = doc.page_content
            chunk_size = 1000
            for i in range(0, len(text), chunk_size):
                chunk_text = text[i:i + chunk_size]
                if chunk_text.strip():  # Só adicionar chunks não vazios
                    docs.append(Document(chunk_text, doc.metadata))

        # Criar vector store simples
        self.vector_store = SimpleVectorStore()
        self.vector_store.add_documents(docs)

    def analyze_data(self, query: str, generate_code: bool = False, domain: str = "healthcare") -> Dict[str, Any]:
        """
        Analisa dados com base em uma consulta em linguagem natural.

        Args:
            query: Consulta em linguagem natural
            generate_code: Se deve gerar código Python
            domain: Domínio de especialização (healthcare, finance, etc.)

        Returns:
            dict: Resposta contendo análise e código (se solicitado)
        """
        if not self.vector_store:
            return {
                "text": "Erro: Vector store não inicializado. Por favor, inicialize com os dados primeiro.",
                "code": None
            }

        # Buscar documentos relevantes
        docs = self.vector_store.similarity_search(query, k=3)

        # Extrair contexto dos documentos
        context = "\n\n".join([doc.page_content for doc in docs])

        # Selecionar o prompt especializado com base no domínio
        if domain == "healthcare":
            prompt_template = """
            Você é um analista de dados especializado em saúde, com profundo conhecimento em gestão de clínicas, análise de dados de pacientes e indicadores de saúde.

            CONTEXTO DOS DADOS:
            {context}

            CONSULTA DO USUÁRIO:
            {query}

            Ao responder:
            1. Foque em insights relevantes para clínicas de saúde
            2. Considere métricas importantes como: taxa de ocupação, tempo médio de atendimento, satisfação do paciente, taxa de retorno, e rentabilidade por procedimento
            3. Sugira correlações entre dados que possam não ser óbvias
            4. Quando relevante, mencione benchmarks do setor de saúde
            5. Considere a jornada completa do paciente na clínica
            6. Relacione os dados com impacto financeiro e operacional para a clínica
            7. Sugira ações práticas baseadas nos insights
            """
        else:
            # Prompt padrão existente
            prompt_template = """
            Você é um assistente de análise de dados especializado em Python e visualização de dados.

            CONTEXTO DOS DADOS:
            {context}

            CONSULTA DO USUÁRIO:
            {query}

            """

        if generate_code:
            if domain == "healthcare":
                prompt_template += """
                Por favor, forneça uma análise detalhada dos dados e gere código Python para visualização ou análise adicional.
                O código deve usar bibliotecas como pandas, numpy, matplotlib e seaborn.
                Formate o código dentro de blocos de código Markdown com ```python e ```.

                Para análises de saúde, considere usar visualizações como:
                1. Gráficos de linha para tendências temporais de atendimentos
                2. Gráficos de calor para identificar horários de pico
                3. Gráficos de dispersão para correlações entre variáveis (ex: tempo de espera vs. satisfação)
                4. Gráficos de barras para comparar desempenho entre médicos ou especialidades
                5. Gráficos de funil para análise de conversão de pacientes
                6. Mapas de calor para visualizar a ocupação da clínica
                7. Gráficos de radar para comparar KPIs entre diferentes períodos

                Inclua comentários explicativos no código e use cores apropriadas para visualizações médicas.
                """
            else:
                prompt_template += """
                Por favor, forneça uma análise detalhada dos dados e gere código Python para visualização ou análise adicional.
                O código deve usar bibliotecas como pandas, numpy, matplotlib e seaborn.
                Formate o código dentro de blocos de código Markdown com ```python e ```.
                """
        else:
            if domain == "healthcare":
                prompt_template += """
                Por favor, forneça uma análise detalhada dos dados com foco em insights acionáveis para gestão de clínicas.
                Inclua recomendações específicas baseadas nos dados analisados.
                """
            else:
                prompt_template += """
                Por favor, forneça uma análise detalhada dos dados com base na consulta do usuário.
                """

        # Formatar o prompt com os dados
        formatted_prompt = prompt_template.format(context=context, query=query)

        # Chamar OpenAI diretamente
        try:
            client = openai.OpenAI(api_key=self.api_key)
            openai_response = client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "Você é um assistente especializado em análise de dados."},
                    {"role": "user", "content": formatted_prompt}
                ],
                temperature=0.7,
                max_tokens=1500
            )
            response = openai_response.choices[0].message.content
        except Exception as e:
            response = f"Erro ao gerar resposta: {str(e)}"

        # Extrair código, se houver
        code = None
        if "```python" in response:
            code_start = response.find("```python") + 9
            code_end = response.find("```", code_start)
            if code_end != -1:
                code = response[code_start:code_end].strip()

        return {
            "text": response,
            "code": code
        }

    def generate_code_from_description(self, description: str, data_context: Dict[str, Any]) -> str:
        """
        Gera código Python a partir de uma descrição em linguagem natural.

        Args:
            description: Descrição em linguagem natural
            data_context: Contexto dos dados

        Returns:
            str: Código Python gerado
        """
        # Preparar prompt
        prompt_template = """
        Você é um especialista em programação Python para análise de dados e visualização.

        CONTEXTO DOS DADOS:
        {data_context}

        TAREFA:
        Gere código Python para a seguinte tarefa: {description}

        O código deve:
        1. Ser completo e executável
        2. Usar bibliotecas como pandas, numpy, matplotlib, seaborn
        3. Incluir comentários explicativos
        4. Ser otimizado e seguir boas práticas

        CÓDIGO PYTHON:
        ```python
        """

        # Converter contexto para string
        data_context_str = json.dumps(data_context, indent=2)

        # Formatar o prompt com os dados
        formatted_prompt = prompt_template.format(data_context=data_context_str, description=description)

        # Chamar OpenAI diretamente
        try:
            client = openai.OpenAI(api_key=self.api_key)
            openai_response = client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "Você é um especialista em programação Python."},
                    {"role": "user", "content": formatted_prompt}
                ],
                temperature=0.7,
                max_tokens=1500
            )
            response = openai_response.choices[0].message.content
        except Exception as e:
            response = f"Erro ao gerar código: {str(e)}"

        # Limpar resposta
        if "```" in response:
            response = response.split("```")[0]

        return response.strip()

    def get_data_for_analysis(self, module_name: str, report_name: str) -> Dict[str, Any]:
        """
        Obtém dados para análise.

        Args:
            module_name: Nome do módulo
            report_name: Nome do relatório

        Returns:
            dict: Dados para análise
        """
        key = f"{module_name}.{report_name}"
        return self.data_context.get(key, {"data": [], "metadata": {}})

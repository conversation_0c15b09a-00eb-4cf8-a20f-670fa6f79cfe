/* Estilos para o Agente Inteligente */

/* Toggle Button */
.agent-toggle {
    position: fixed;
    right: 20px;
    bottom: 80px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #007AFF;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 1000;
    transition: all 0.3s ease;
}

.agent-toggle:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
}

.agent-toggle svg {
    width: 28px;
    height: 28px;
    color: white;
}

/* Agent Panel */
.agent-panel {
    position: fixed;
    right: -520px;
    top: 0;
    width: 500px;
    height: 100vh;
    background-color: white;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
    z-index: 999;
    transition: right 0.4s ease;
    display: flex;
    flex-direction: column;
    border-left: 1px solid #e5e7eb;
}

.agent-panel.open {
    right: 0;
}

/* Agent Header */
.agent-header {
    padding: 16px;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.agent-header-title {
    display: flex;
    align-items: center;
}

.agent-header-title svg {
    width: 20px;
    height: 20px;
    color: #007AFF;
    margin-right: 8px;
}

.agent-header-title h3 {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.agent-header-actions {
    display: flex;
    align-items: center;
}

.agent-action-btn {
    background: none;
    border: none;
    cursor: pointer;
    color: #6b7280;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 4px;
    transition: all 0.2s ease;
}

.agent-action-btn svg {
    width: 18px;
    height: 18px;
}

.agent-action-btn:hover {
    background-color: #f3f4f6;
    color: #1f2937;
}

.agent-close {
    background: none;
    border: none;
    cursor: pointer;
    color: #6b7280;
    margin-left: 4px;
}

.agent-close:hover {
    color: #1f2937;
}

/* Agent Content */
.agent-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    display: flex;
    flex-direction: column;
}

/* Conversation History */
.agent-conversation {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 16px;
}

.agent-message {
    margin-bottom: 16px;
    max-width: 85%;
}

.user-message {
    margin-left: auto;
    background-color: #007AFF;
    color: white;
    border-radius: 18px 18px 4px 18px;
    padding: 10px 14px;
}

.agent-response {
    background-color: #f3f4f6;
    color: #1f2937;
    border-radius: 18px 18px 18px 4px;
    padding: 10px 14px;
}

/* Multi-format Content */
.response-content {
    margin-top: 8px;
}

.response-text {
    font-size: 14px;
    line-height: 1.5;
}

.response-chart {
    margin-top: 12px;
    background-color: white;
    border-radius: 8px;
    padding: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    height: 250px;
    width: 100%;
}

.response-list {
    margin-top: 8px;
    padding-left: 20px;
}

.response-list li {
    margin-bottom: 4px;
    font-size: 14px;
}

.response-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 12px;
    font-size: 13px;
}

.response-table th,
.response-table td {
    border: 1px solid #e5e7eb;
    padding: 8px;
    text-align: left;
}

.response-table th {
    background-color: #f9fafb;
}

/* Input Area */
.agent-input {
    display: flex;
    border-top: 1px solid #e5e7eb;
    padding: 12px 16px;
}

.agent-input input {
    flex: 1;
    border: 1px solid #d1d5db;
    border-radius: 20px;
    padding: 8px 16px;
    font-size: 14px;
    outline: none;
}

.agent-input input:focus {
    border-color: #007AFF;
    box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.2);
}

.agent-input button {
    background-color: #007AFF;
    color: white;
    border: none;
    border-radius: 20px;
    padding: 8px 16px;
    margin-left: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
}

.agent-input button:hover {
    background-color: #0062cc;
}

/* Loading Indicator */
.agent-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.agent-loading-dots {
    display: flex;
}

.agent-loading-dots span {
    width: 8px;
    height: 8px;
    margin: 0 4px;
    background-color: #007AFF;
    border-radius: 50%;
    animation: dot-pulse 1.5s infinite ease-in-out;
}

.agent-loading-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.agent-loading-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes dot-pulse {
    0%, 100% {
        transform: scale(0.7);
        opacity: 0.5;
    }
    50% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Welcome Message */
.welcome-message {
    background-color: #f0f7ff;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 20px;
    border-left: 4px solid #007AFF;
}

.welcome-message h4 {
    font-size: 15px;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: #1f2937;
}

.welcome-message p {
    font-size: 14px;
    color: #4b5563;
    margin: 0 0 12px 0;
    line-height: 1.5;
}

.welcome-suggestions {
    margin-top: 12px;
}

.suggestion-chip {
    display: inline-block;
    background-color: white;
    border: 1px solid #d1d5db;
    border-radius: 16px;
    padding: 6px 12px;
    margin: 0 8px 8px 0;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.suggestion-chip:hover {
    background-color: #007AFF;
    color: white;
    border-color: #007AFF;
}

/* History Header */
.history-header {
    background-color: #f9fafb;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 20px;
    border-bottom: 1px solid #e5e7eb;
    text-align: center;
}

.history-header h4 {
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: #1f2937;
}

.history-header p {
    font-size: 14px;
    color: #6b7280;
    margin: 0;
}

/* Context Badge */
.context-badge {
    display: inline-flex;
    align-items: center;
    background-color: #f0f7ff;
    border-radius: 12px;
    padding: 4px 8px;
    margin-top: 8px;
    font-size: 12px;
    color: #007AFF;
}

.context-badge svg {
    width: 12px;
    height: 12px;
    margin-right: 4px;
}

/* Relevance Indicator */
.relevance-indicator {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-right: 6px;
    position: relative;
}

.relevance-indicator svg {
    width: 14px;
    height: 14px;
}

.relevance-high {
    color: #10b981; /* Verde */
}

.relevance-medium {
    color: #f59e0b; /* Amarelo */
}

.relevance-low {
    color: #ef4444; /* Vermelho */
}

/* Code Block */
.response-code {
    background-color: #1f2937;
    color: #f9fafb;
    border-radius: 8px;
    padding: 12px;
    margin-top: 12px;
    font-family: monospace;
    font-size: 13px;
    overflow-x: auto;
    white-space: pre;
}

/* Markdown Content */
.response-markdown h1,
.response-markdown h2,
.response-markdown h3 {
    margin-top: 16px;
    margin-bottom: 8px;
    font-weight: 600;
}

.response-markdown h1 {
    font-size: 18px;
}

.response-markdown h2 {
    font-size: 16px;
}

.response-markdown h3 {
    font-size: 14px;
}

.response-markdown p {
    margin-bottom: 8px;
}

.response-markdown ul,
.response-markdown ol {
    margin-top: 8px;
    margin-bottom: 8px;
    padding-left: 20px;
}

.response-markdown code {
    background-color: #f3f4f6;
    padding: 2px 4px;
    border-radius: 4px;
    font-family: monospace;
    font-size: 12px;
}

/* Tooltip for context info */
.context-info {
    position: relative;
    display: inline-block;
    margin-left: 8px;
    cursor: help;
}

.context-info svg {
    width: 14px;
    height: 14px;
    color: #6b7280;
}

.context-tooltip {
    visibility: hidden;
    position: absolute;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #1f2937;
    color: white;
    text-align: center;
    padding: 8px 12px;
    border-radius: 6px;
    width: 200px;
    font-size: 12px;
    z-index: 1;
    opacity: 0;
    transition: opacity 0.3s;
}

.context-info:hover .context-tooltip {
    visibility: visible;
    opacity: 1;
}

/**
 * Amigo Intelligence Agent
 *
 * Este script implementa o agente inteligente que fica disponível em todas as páginas
 * do Amigo DataHub. O agente tem acesso ao contexto geral e específico da página atual
 * para fornecer respostas personalizadas em múltiplos formatos.
 */

// Contexto global do aplicativo
let globalContext = {
    app_name: "Amigo DataHub",
    modules: [
        { id: "agenda", name: "Agenda", pages: ["index", "agendamentos", "cancelamentos", "producao_medica", "tempo_atendimento"] },
        { id: "financeiro", name: "Financeiro", pages: ["index", "contas_receber", "contas_pagar", "fluxo_caixa", "fechamento_caixa"] },
        { id: "paciente", name: "Paciente", pages: ["index", "atendimentos", "creditos", "orcamentos_abertos", "orcamentos_fechados"] },
        { id: "amigocare", name: "AmigoCare+", pages: ["index", "leads", "campanhas", "avaliacao_nps", "funil_vendas", "acompanhamento_pacientes"] },
        { id: "visao360", name: "Visão 360", pages: ["index", "pacientes", "paciente_detalhe", "agendamentos", "transacoes", "recomendacoes"] }
    ],
    data_sources: [
        { id: "agenda", path: "/relatorios/agenda/" },
        { id: "financeiro", path: "/relatorios/financeiro/" },
        { id: "paciente", path: "/relatorios/paciente/" },
        { id: "amigocare", path: "/relatorios/amigocare/" }
    ],
    global_schema: {
        // Esquema global simplificado
        entities: [
            { name: "Paciente", required_fields: ["paciente_id", "nome"] },
            { name: "Agendamento", required_fields: ["agendamento_id", "paciente_id", "data", "status"] },
            { name: "TransacaoFinanceira", required_fields: ["transacao_id", "valor", "tipo", "agendamento_id"] },
            { name: "Profissional", required_fields: ["profissional_id", "nome", "especialidade"] }
        ]
    }
};

// Contexto atual da página
let currentPageContext = {};

// Histórico de conversas
let conversationHistory = [];

/**
 * Inicializa o agente inteligente
 */
function initAgent() {
    // Verifica se o agente já foi inicializado
    if (document.getElementById('agent-toggle')) {
        console.log('Agente já inicializado');
        return;
    }

    console.log('Inicializando Amigo Intelligence Agent...');

    // Adiciona o HTML do agente ao body
    document.body.insertAdjacentHTML('beforeend', createAgentHTML());

    // Adiciona os event listeners
    setupEventListeners();

    // Aguarda um momento para que o contexto da página seja completamente carregado
    setTimeout(() => {
        // Detecta o contexto da página atual
        detectCurrentPageContext();

        // Carrega o histórico de conversas do localStorage (se existir)
        loadConversationHistory();

        console.log('Amigo Intelligence Agent inicializado com sucesso');
    }, 500);
}

/**
 * Cria o HTML do agente
 */
function createAgentHTML() {
    return `
        <!-- Toggle Button -->
        <div class="agent-toggle" id="agent-toggle">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
            </svg>
        </div>

        <!-- Agent Panel -->
        <div class="agent-panel" id="agent-panel">
            <div class="agent-header">
                <div class="agent-header-title">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
                    </svg>
                    <h3>Amigo Intelligence</h3>
                    <div class="context-info">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z" />
                        </svg>
                        <div class="context-tooltip">
                            Tenho acesso ao contexto desta página e aos dados carregados no sistema.
                        </div>
                    </div>
                </div>
                <div class="agent-header-actions">
                    <button class="agent-action-btn" id="agent-history-btn" title="Ver histórico completo">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </button>
                    <button class="agent-action-btn" id="agent-clear-btn" title="Limpar histórico">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
                        </svg>
                    </button>
                    <button class="agent-action-btn" id="agent-new-btn" title="Novo chat">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                        </svg>
                    </button>
                    <button class="agent-close" id="agent-close">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>

            <div class="agent-content">
                <div class="agent-conversation" id="agent-conversation">
                    <div class="welcome-message">
                        <h4>Olá! Como posso ajudar?</h4>
                        <p>Sou o assistente inteligente do Amigo DataHub. Posso responder perguntas sobre os dados, gerar análises e ajudar com insights.</p>
                        <div class="welcome-suggestions">
                            <div class="suggestion-chip" onclick="askSuggestion(this)">Resumo desta página</div>
                            <div class="suggestion-chip" onclick="askSuggestion(this)">Principais insights</div>
                            <div class="suggestion-chip" onclick="askSuggestion(this)">Tendências recentes</div>
                            <div class="suggestion-chip" onclick="askSuggestion(this)">Recomendações</div>
                        </div>
                    </div>
                </div>

                <div class="agent-input">
                    <input type="text" id="agent-input-field" placeholder="Digite sua pergunta..." />
                    <button id="agent-submit">Enviar</button>
                </div>
            </div>
        </div>
    `;
}

/**
 * Configura os event listeners
 */
function setupEventListeners() {
    // Toggle para abrir/fechar o painel
    document.getElementById('agent-toggle').addEventListener('click', toggleAgentPanel);

    // Botão para fechar o painel
    document.getElementById('agent-close').addEventListener('click', closeAgentPanel);

    // Envio de pergunta (botão)
    document.getElementById('agent-submit').addEventListener('click', handleUserQuestion);

    // Envio de pergunta (tecla Enter)
    document.getElementById('agent-input-field').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            handleUserQuestion();
        }
    });

    // Botão para ver histórico completo
    document.getElementById('agent-history-btn').addEventListener('click', showFullHistory);

    // Botão para limpar histórico
    document.getElementById('agent-clear-btn').addEventListener('click', clearHistory);

    // Botão para novo chat
    document.getElementById('agent-new-btn').addEventListener('click', startNewChat);
}

/**
 * Abre/fecha o painel do agente
 */
function toggleAgentPanel() {
    const panel = document.getElementById('agent-panel');
    panel.classList.toggle('open');
}

/**
 * Fecha o painel do agente
 */
function closeAgentPanel() {
    const panel = document.getElementById('agent-panel');
    panel.classList.remove('open');
}

/**
 * Detecta o contexto da página atual
 */
function detectCurrentPageContext() {
    console.log('Detectando contexto da página...');

    // Obtém a URL atual
    const currentPath = window.location.pathname;
    console.log('URL atual:', currentPath);

    // Extrai o módulo e a página da URL
    const pathParts = currentPath.split('/').filter(part => part.length > 0);
    console.log('Partes da URL:', pathParts);

    // Define valores padrão para o contexto básico
    let module = 'dashboard';
    let page = 'index';

    // Tenta extrair módulo e página da URL
    if (pathParts.length >= 1) {
        // Se temos pelo menos uma parte na URL, consideramos como módulo
        if (pathParts[0] === 'amigo-dataapp') {
            // Se a primeira parte é 'amigo-dataapp', pegamos a segunda como módulo
            if (pathParts.length >= 2) {
                module = pathParts[1];
                page = pathParts.length >= 3 ? pathParts[2].replace('.html', '') : 'index';
            }
        } else {
            // Caso contrário, a primeira parte é o módulo
            module = pathParts[0];
            page = pathParts.length >= 2 ? pathParts[1].replace('.html', '') : 'index';
        }
    }

    console.log('Módulo detectado da URL:', module);
    console.log('Página detectada da URL:', page);

    // Tenta extrair o título da página do DOM
    let pageTitle = document.title.replace(' - Amigo DataHub', '').trim();

    // Tenta extrair o título da página de elementos específicos
    const pageTitleElement = document.querySelector('h1, .page-title, .hero-title, .header-title, header h2');
    if (pageTitleElement) {
        pageTitle = pageTitleElement.textContent.trim();
        console.log('Título extraído do DOM:', pageTitle);
    }

    // Define o contexto básico da página
    currentPageContext = {
        module: module,
        page: page,
        url: currentPath,
        title: pageTitle,
        timestamp: new Date().toISOString()
    };

    // Tenta obter contexto específico da página
    try {
        // Verifica se existe uma variável pageContext definida na página
        if (typeof window.pageContext !== 'undefined') {
            console.log('pageContext encontrado na página:', window.pageContext);

            // Mescla o contexto básico com o contexto específico da página
            currentPageContext = {
                ...currentPageContext,
                ...window.pageContext,
                // Garante que module e page estejam definidos
                module: window.pageContext.module || module,
                page: window.pageContext.page || page
            };

            // Adiciona título e descrição formatados para uso nas respostas
            if (window.pageContext.page_title) {
                currentPageContext.formatted_title = window.pageContext.page_title;
            }
        } else {
            console.log('Nenhum pageContext encontrado na página');
        }

        // Se ainda não temos um título formatado, usa o título extraído do DOM
        if (!currentPageContext.formatted_title && pageTitle) {
            currentPageContext.formatted_title = pageTitle;
            console.log('Usando título do DOM como formatted_title:', pageTitle);
        }

        // Tenta extrair métricas-chave da página
        extractPageMetrics();

        // Tenta extrair dados de gráficos da página
        extractChartData();

        // Tenta extrair o título da página novamente após as extrações
        if (!currentPageContext.formatted_title && currentPageContext.page_title) {
            currentPageContext.formatted_title = currentPageContext.page_title;
            console.log('Usando page_title como formatted_title:', currentPageContext.page_title);
        }

        // Tenta extrair o módulo do breadcrumb
        const breadcrumb = document.querySelector('.breadcrumb, .breadcrumbs, .nav-breadcrumb');
        if (breadcrumb) {
            const breadcrumbItems = breadcrumb.querySelectorAll('li, .breadcrumb-item, a');
            if (breadcrumbItems.length >= 2) {
                const moduleText = breadcrumbItems[1].textContent.trim();
                if (moduleText && moduleText.length > 0) {
                    // Verifica se o texto corresponde a algum módulo conhecido
                    const knownModule = globalContext.modules.find(m =>
                        moduleText.toLowerCase().includes(m.name.toLowerCase()) ||
                        m.name.toLowerCase().includes(moduleText.toLowerCase())
                    );

                    if (knownModule) {
                        currentPageContext.module = knownModule.id;
                        console.log('Módulo extraído do breadcrumb:', knownModule.id);
                    }
                }
            }
        }

        // Verifica se o título da página contém o nome de algum módulo
        if (pageTitle) {
            globalContext.modules.forEach(m => {
                if (pageTitle.toLowerCase().includes(m.name.toLowerCase())) {
                    currentPageContext.module = m.id;
                    console.log('Módulo extraído do título da página:', m.id);
                }
            });
        }

        // Se ainda não temos um título formatado, usa uma combinação de módulo e página
        if (!currentPageContext.formatted_title) {
            const moduleName = globalContext.modules.find(m => m.id === currentPageContext.module)?.name || currentPageContext.module;
            currentPageContext.formatted_title = `${moduleName} - ${currentPageContext.page.charAt(0).toUpperCase() + currentPageContext.page.slice(1)}`;
            console.log('Criando formatted_title a partir do módulo e página:', currentPageContext.formatted_title);
        }

        console.log('Contexto final da página:', currentPageContext);
    } catch (error) {
        console.error('Erro ao detectar contexto da página:', error);
    }
}

/**
 * Extrai métricas-chave da página atual
 */
function extractPageMetrics() {
    console.log('Extraindo métricas-chave da página...');

    // Tenta extrair métricas de cards e KPIs
    const metricElements = document.querySelectorAll('.metric-card, .kpi-card, .card-metric, .card-kpi, .card.metric, .card.kpi, .dashboard-card');
    console.log('Elementos de métrica encontrados:', metricElements.length);

    if (metricElements.length > 0) {
        const metrics = {};

        metricElements.forEach((element, index) => {
            const label = element.querySelector('.metric-label, .kpi-label, .card-title, .metric-title, .kpi-title, .card-header, h3, h4, .title');
            const value = element.querySelector('.metric-value, .kpi-value, .card-value, .metric-number, .kpi-number, .value, .number, strong, .card-body');

            if (label && value) {
                const labelText = label.textContent.trim();
                const valueText = value.textContent.trim();
                metrics[labelText] = valueText;
                console.log(`Métrica ${index + 1} extraída:`, labelText, valueText);
            } else {
                console.log(`Elemento de métrica ${index + 1} não possui label ou value`);
                if (label) console.log('  Label encontrado:', label.textContent.trim());
                if (value) console.log('  Value encontrado:', value.textContent.trim());
            }
        });

        // Se já existirem métricas no contexto, mescla com as novas
        if (currentPageContext.key_metrics) {
            currentPageContext.key_metrics = { ...currentPageContext.key_metrics, ...metrics };
        } else {
            currentPageContext.key_metrics = metrics;
        }

        // Mantém a propriedade metrics para compatibilidade
        currentPageContext.metrics = currentPageContext.key_metrics;

        console.log('Métricas extraídas:', currentPageContext.key_metrics);
    } else {
        console.log('Nenhum elemento de métrica encontrado');
    }

    // Tenta extrair o título da página
    const pageTitleElements = document.querySelectorAll('h1, .page-title, .hero-title, .header-title, .main-title, header h2, .dashboard-title');
    console.log('Elementos de título encontrados:', pageTitleElements.length);

    if (pageTitleElements.length > 0) {
        // Prioriza o primeiro elemento h1
        let bestTitleElement = null;

        for (const element of pageTitleElements) {
            if (element.tagName.toLowerCase() === 'h1') {
                bestTitleElement = element;
                break;
            }
            if (!bestTitleElement) {
                bestTitleElement = element;
            }
        }

        if (bestTitleElement && !currentPageContext.page_title) {
            currentPageContext.page_title = bestTitleElement.textContent.trim();
            console.log('Título da página extraído:', currentPageContext.page_title);
        }
    } else {
        console.log('Nenhum elemento de título encontrado');
    }

    // Tenta extrair a descrição da página
    const pageDescriptionElements = document.querySelectorAll('.page-description, .hero-description, .subtitle, .description, .header-subtitle, .text-muted');
    console.log('Elementos de descrição encontrados:', pageDescriptionElements.length);

    if (pageDescriptionElements.length > 0 && !currentPageContext.page_description) {
        currentPageContext.page_description = pageDescriptionElements[0].textContent.trim();
        console.log('Descrição da página extraída:', currentPageContext.page_description);
    } else {
        console.log('Nenhum elemento de descrição encontrado');
    }

    // Tenta extrair o módulo da página a partir de classes ou IDs
    const bodyClasses = document.body.className.split(' ');
    for (const cls of bodyClasses) {
        if (cls.includes('module-')) {
            const moduleFromClass = cls.replace('module-', '');
            if (moduleFromClass) {
                currentPageContext.module = moduleFromClass;
                console.log('Módulo extraído da classe do body:', moduleFromClass);
            }
        }
    }

    // Tenta extrair o módulo e a página a partir de elementos de navegação ativos
    const activeNavItems = document.querySelectorAll('.nav-item.active, .sidebar-item.active, .nav-link.active, .sidebar-link.active');
    if (activeNavItems.length > 0) {
        const activeText = activeNavItems[0].textContent.trim();
        console.log('Item de navegação ativo encontrado:', activeText);

        // Verifica se o texto corresponde a algum módulo conhecido
        globalContext.modules.forEach(m => {
            if (activeText.toLowerCase().includes(m.name.toLowerCase())) {
                currentPageContext.module = m.id;
                console.log('Módulo extraído do item de navegação ativo:', m.id);
            }
        });
    }
}

/**
 * Extrai dados de gráficos da página atual
 */
function extractChartData() {
    console.log('Extraindo dados de gráficos da página...');

    // Verifica se existem instâncias de Chart.js na página
    if (typeof window.charts !== 'undefined') {
        console.log('Instâncias de Chart.js encontradas:', Object.keys(window.charts).length);

        const chartData = {};

        // Itera sobre os gráficos disponíveis
        Object.keys(window.charts).forEach(chartId => {
            const chart = window.charts[chartId];

            if (chart && chart.data) {
                // Tenta encontrar o título do gráfico no DOM
                let chartTitle = '';
                const chartElement = document.getElementById(chartId);
                if (chartElement) {
                    // Tenta encontrar o título no container do gráfico
                    const chartContainer = chartElement.closest('.chart-container, .card, .graph-card, .chart-card, .dashboard-card');
                    if (chartContainer) {
                        const titleElement = chartContainer.querySelector('.card-title, .chart-title, .graph-title, h3, h4, .title, caption');
                        if (titleElement) {
                            chartTitle = titleElement.textContent.trim();
                            console.log(`Título do gráfico ${chartId} encontrado:`, chartTitle);
                        } else {
                            console.log(`Nenhum título encontrado para o gráfico ${chartId}`);
                        }
                    } else {
                        console.log(`Nenhum container encontrado para o gráfico ${chartId}`);
                    }
                } else {
                    console.log(`Elemento do gráfico ${chartId} não encontrado no DOM`);
                }

                // Se não encontrou título, tenta usar o label do dataset
                if (!chartTitle && chart.data.datasets && chart.data.datasets.length > 0 && chart.data.datasets[0].label) {
                    chartTitle = chart.data.datasets[0].label;
                    console.log(`Usando label do dataset como título para o gráfico ${chartId}:`, chartTitle);
                }

                chartData[chartId] = {
                    title: chartTitle,
                    type: chart.config.type,
                    labels: chart.data.labels,
                    datasets: chart.data.datasets.map(dataset => ({
                        label: dataset.label,
                        data: dataset.data
                    }))
                };

                console.log(`Dados extraídos do gráfico ${chartId}:`, {
                    title: chartTitle,
                    type: chart.config.type,
                    labels: chart.data.labels ? chart.data.labels.length : 0,
                    datasets: chart.data.datasets ? chart.data.datasets.length : 0
                });
            }
        });

        currentPageContext.charts = chartData;
        console.log('Total de gráficos extraídos:', Object.keys(chartData).length);

        // Extrai os elementos da página com base nos gráficos
        const chartElements = Object.values(chartData)
            .filter(chart => chart.title)
            .map(chart => chart.title);

        if (chartElements.length > 0) {
            console.log('Elementos extraídos dos gráficos:', chartElements);

            if (!currentPageContext.page_elements) {
                currentPageContext.page_elements = chartElements;
            } else {
                currentPageContext.page_elements = [...currentPageContext.page_elements, ...chartElements];
            }
        }
    } else {
        console.log('Nenhuma instância de Chart.js encontrada');
    }

    // Tenta extrair elementos da página a partir de títulos de seção
    const sectionTitles = document.querySelectorAll('.section-title, .card-title:not(.metric-card .card-title), h2, h3:not(.metric-card h3), .dashboard-section-title, .panel-title');
    console.log('Títulos de seção encontrados:', sectionTitles.length);

    if (sectionTitles.length > 0) {
        const sectionElements = Array.from(sectionTitles)
            .map(element => element.textContent.trim())
            .filter(title => title.length > 0);

        console.log('Elementos extraídos dos títulos de seção:', sectionElements);

        if (!currentPageContext.page_elements) {
            currentPageContext.page_elements = sectionElements;
        } else {
            // Adiciona apenas elementos que ainda não existem
            sectionElements.forEach(element => {
                if (!currentPageContext.page_elements.includes(element)) {
                    currentPageContext.page_elements.push(element);
                }
            });
        }
    }

    // Tenta extrair elementos da página a partir de tabelas
    const tables = document.querySelectorAll('table');
    console.log('Tabelas encontradas:', tables.length);

    if (tables.length > 0) {
        tables.forEach((table, index) => {
            // Tenta encontrar o título da tabela
            let tableTitle = '';

            // Verifica se a tabela tem um caption
            const caption = table.querySelector('caption');
            if (caption) {
                tableTitle = caption.textContent.trim();
            } else {
                // Verifica se a tabela está dentro de um card com título
                const tableContainer = table.closest('.card, .table-container, .dashboard-card');
                if (tableContainer) {
                    const titleElement = tableContainer.querySelector('.card-title, .table-title, h3, h4, .title');
                    if (titleElement) {
                        tableTitle = titleElement.textContent.trim();
                    }
                }
            }

            // Se não encontrou título, usa um genérico
            if (!tableTitle) {
                // Tenta extrair o título a partir das colunas da tabela
                const headerRow = table.querySelector('thead tr');
                if (headerRow) {
                    const headers = Array.from(headerRow.querySelectorAll('th'))
                        .map(th => th.textContent.trim())
                        .filter(text => text.length > 0);

                    if (headers.length > 0) {
                        tableTitle = `Tabela de ${headers[0]}`;
                    }
                }

                // Se ainda não tem título, usa um genérico
                if (!tableTitle) {
                    tableTitle = `Tabela ${index + 1}`;
                }
            }

            console.log(`Título da tabela ${index + 1}:`, tableTitle);

            // Adiciona o título da tabela aos elementos da página
            if (!currentPageContext.page_elements) {
                currentPageContext.page_elements = [tableTitle];
            } else if (!currentPageContext.page_elements.includes(tableTitle)) {
                currentPageContext.page_elements.push(tableTitle);
            }
        });
    }

    console.log('Elementos finais da página:', currentPageContext.page_elements);
}

/**
 * Carrega o histórico de conversas do localStorage
 */
function loadConversationHistory() {
    const savedHistory = localStorage.getItem('amigoAgentHistory');

    if (savedHistory) {
        try {
            conversationHistory = JSON.parse(savedHistory);

            // Limita o histórico a 20 mensagens para não sobrecarregar
            if (conversationHistory.length > 20) {
                conversationHistory = conversationHistory.slice(-20);
            }

            // Renderiza o histórico no painel
            renderConversationHistory();
        } catch (error) {
            console.error('Erro ao carregar histórico de conversas:', error);
            conversationHistory = [];
        }
    }
}

/**
 * Salva o histórico de conversas no localStorage
 */
function saveConversationHistory() {
    try {
        localStorage.setItem('amigoAgentHistory', JSON.stringify(conversationHistory));
    } catch (error) {
        console.error('Erro ao salvar histórico de conversas:', error);
    }
}

/**
 * Renderiza o histórico de conversas no painel
 */
function renderConversationHistory() {
    const container = document.getElementById('agent-conversation');

    // Limpa o conteúdo atual, exceto a mensagem de boas-vindas
    const welcomeMessage = container.querySelector('.welcome-message');
    container.innerHTML = '';

    if (welcomeMessage && conversationHistory.length === 0) {
        container.appendChild(welcomeMessage);
    }

    // Adiciona cada mensagem do histórico
    conversationHistory.forEach(message => {
        if (message.type === 'user') {
            addUserMessage(message.text);
        } else if (message.type === 'agent') {
            addAgentResponse(message.response);
        }
    });

    // Rola para o final da conversa
    container.scrollTop = container.scrollHeight;
}

/**
 * Adiciona uma mensagem do usuário à conversa
 */
function addUserMessage(text) {
    const container = document.getElementById('agent-conversation');
    const messageElement = document.createElement('div');

    messageElement.className = 'agent-message user-message';
    messageElement.textContent = text;

    container.appendChild(messageElement);
    container.scrollTop = container.scrollHeight;
}

/**
 * Adiciona uma resposta do agente à conversa
 */
function addAgentResponse(response) {
    const container = document.getElementById('agent-conversation');
    const messageElement = document.createElement('div');

    messageElement.className = 'agent-message agent-response';

    // Verifica o tipo de resposta e formata adequadamente
    if (typeof response === 'string') {
        // Resposta simples de texto
        messageElement.innerHTML = `<div class="response-content response-text">${response}</div>`;
    } else {
        // Resposta em formato complexo
        let contentHTML = '';

        if (response.text) {
            contentHTML += `<div class="response-content response-text">${response.text}</div>`;
        }

        if (response.markdown) {
            try {
                // Configura o marked para renderizar o markdown
                marked.setOptions({
                    breaks: true,
                    gfm: true
                });
                contentHTML += `<div class="response-content response-markdown">${marked.parse(response.markdown)}</div>`;
            } catch (error) {
                console.error('Erro ao renderizar markdown:', error);
                contentHTML += `<div class="response-content response-text">${response.markdown}</div>`;
            }
        }

        if (response.list && response.list.length > 0) {
            contentHTML += `<ul class="response-content response-list">
                ${response.list.map(item => `<li>${item}</li>`).join('')}
            </ul>`;
        }

        if (response.table && response.table.headers && response.table.rows) {
            contentHTML += `<table class="response-content response-table">
                <thead>
                    <tr>${response.table.headers.map(header => `<th>${header}</th>`).join('')}</tr>
                </thead>
                <tbody>
                    ${response.table.rows.map(row =>
                        `<tr>${row.map(cell => `<td>${cell}</td>`).join('')}</tr>`
                    ).join('')}
                </tbody>
            </table>`;
        }

        if (response.chart) {
            const chartId = 'chart-' + Date.now();
            contentHTML += `<div class="response-content response-chart">
                <canvas id="${chartId}"></canvas>
            </div>`;

            // Renderiza o gráfico após adicionar o elemento ao DOM
            setTimeout(() => {
                renderChart(chartId, response.chart);
            }, 100);
        }

        if (response.code) {
            contentHTML += `<pre class="response-content response-code">${response.code}</pre>`;
        }

        messageElement.innerHTML = contentHTML;
    }

    // Adiciona o badge de contexto se aplicável
    if (response.context) {
        const contextBadge = document.createElement('div');
        contextBadge.className = 'context-badge';

        // Verificar se há informação de relevância no contexto
        let contextText = response.context;
        let relevanceHTML = '';

        if (contextText.includes('Relevância:')) {
            // Extrair o valor de relevância
            const relevanceMatch = contextText.match(/Relevância: (\d+\.\d+)/);
            if (relevanceMatch && relevanceMatch[1]) {
                const relevanceScore = parseFloat(relevanceMatch[1]);

                // Determinar a classe CSS com base na pontuação
                let relevanceClass = 'relevance-low';
                if (relevanceScore >= 0.8) {
                    relevanceClass = 'relevance-high';
                } else if (relevanceScore >= 0.5) {
                    relevanceClass = 'relevance-medium';
                }

                // Criar o indicador de relevância
                relevanceHTML = `
                    <span class="relevance-indicator ${relevanceClass}" title="Relevância: ${relevanceScore}">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z" />
                        </svg>
                    </span>
                `;

                // Remover a parte de relevância do texto de contexto para exibição
                contextText = contextText.replace(/ \(Relevância: \d+\.\d+\)/, '');
            }
        }

        contextBadge.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 01-1.043 3.296 3.745 3.745 0 01-3.296 1.043A3.745 3.745 0 0112 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 01-3.296-1.043 3.745 3.745 0 01-1.043-3.296A3.745 3.745 0 013 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 011.043-3.296 3.746 3.746 0 013.296-1.043A3.746 3.746 0 0112 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 013.296 1.043 3.746 3.746 0 011.043 3.296A3.745 3.745 0 0121 12z" />
            </svg>
            ${relevanceHTML}
            ${contextText}
        `;
        messageElement.appendChild(contextBadge);
    }

    container.appendChild(messageElement);
    container.scrollTop = container.scrollHeight;
}

/**
 * Renderiza um gráfico usando Chart.js
 */
function renderChart(chartId, chartData) {
    try {
        console.log('Renderizando gráfico:', chartId, chartData);

        const canvas = document.getElementById(chartId);
        if (!canvas) {
            console.error('Elemento canvas não encontrado:', chartId);
            return;
        }

        const ctx = canvas.getContext('2d');
        if (!ctx) {
            console.error('Contexto 2D não disponível para o canvas:', chartId);
            return;
        }

        // Configura cores padrão
        const defaultColors = [
            'rgba(0, 122, 255, 0.7)',
            'rgba(88, 86, 214, 0.7)',
            'rgba(90, 200, 250, 0.7)',
            'rgba(85, 85, 85, 0.7)',
            'rgba(52, 170, 220, 0.7)'
        ];

        // Aplica cores aos datasets se não estiverem definidas
        if (chartData.datasets) {
            chartData.datasets.forEach((dataset, index) => {
                if (!dataset.backgroundColor) {
                    if (chartData.type === 'pie' || chartData.type === 'doughnut' || chartData.type === 'polarArea') {
                        // Para gráficos de pizza, rosca e área polar, usa um array de cores
                        dataset.backgroundColor = defaultColors;
                    } else {
                        // Para outros tipos de gráfico, usa uma única cor
                        dataset.backgroundColor = defaultColors[index % defaultColors.length];
                    }
                }

                if (!dataset.borderColor && chartData.type !== 'pie' && chartData.type !== 'doughnut' && chartData.type !== 'polarArea') {
                    dataset.borderColor = defaultColors[index % defaultColors.length].replace('0.7', '1');
                }
            });
        }

        // Configura opções específicas por tipo de gráfico
        let chartOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        font: {
                            size: 12
                        }
                    }
                },
                tooltip: {
                    enabled: true
                }
            }
        };

        // Opções específicas por tipo de gráfico
        if (chartData.type === 'bar') {
            chartOptions = {
                ...chartOptions,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            };
        } else if (chartData.type === 'pie' || chartData.type === 'doughnut') {
            chartOptions = {
                ...chartOptions,
                cutout: chartData.type === 'doughnut' ? '50%' : undefined,
                plugins: {
                    ...chartOptions.plugins,
                    legend: {
                        ...chartOptions.plugins.legend,
                        position: 'right'
                    }
                }
            };
        }

        // Mescla com as opções fornecidas
        const finalOptions = chartData.options ? { ...chartOptions, ...chartData.options } : chartOptions;

        // Verifica se já existe um gráfico neste canvas e o destrói
        if (window.agentCharts && window.agentCharts[chartId]) {
            window.agentCharts[chartId].destroy();
        }

        // Inicializa o objeto de gráficos do agente se não existir
        if (!window.agentCharts) {
            window.agentCharts = {};
        }

        // Cria o gráfico
        window.agentCharts[chartId] = new Chart(ctx, {
            type: chartData.type || 'bar',
            data: {
                labels: chartData.labels || [],
                datasets: chartData.datasets || []
            },
            options: finalOptions
        });

        console.log('Gráfico renderizado com sucesso:', chartId);
    } catch (error) {
        console.error('Erro ao renderizar gráfico:', error);
    }
}

/**
 * Processa a pergunta do usuário
 */
function handleUserQuestion() {
    const inputField = document.getElementById('agent-input-field');
    const question = inputField.value.trim();

    if (question.length === 0) return;

    // Adiciona a pergunta à conversa
    addUserMessage(question);

    // Limpa o campo de entrada
    inputField.value = '';

    // Adiciona indicador de carregamento
    const loadingElement = document.createElement('div');
    loadingElement.className = 'agent-loading';
    loadingElement.innerHTML = `
        <div class="agent-loading-dots">
            <span></span>
            <span></span>
            <span></span>
        </div>
    `;
    document.getElementById('agent-conversation').appendChild(loadingElement);

    // Adiciona a pergunta ao histórico
    conversationHistory.push({
        type: 'user',
        text: question,
        timestamp: new Date().toISOString()
    });

    // Processa a pergunta e gera resposta
    processQuestion(question)
        .then(response => {
            // Remove o indicador de carregamento
            document.getElementById('agent-conversation').removeChild(loadingElement);

            // Adiciona a resposta à conversa
            addAgentResponse(response);

            // Adiciona a resposta ao histórico
            conversationHistory.push({
                type: 'agent',
                response: response,
                timestamp: new Date().toISOString()
            });

            // Salva o histórico atualizado
            saveConversationHistory();
        })
        .catch(error => {
            // Remove o indicador de carregamento
            document.getElementById('agent-conversation').removeChild(loadingElement);

            // Adiciona mensagem de erro
            addAgentResponse("Desculpe, ocorreu um erro ao processar sua pergunta. Por favor, tente novamente.");
            console.error('Erro ao processar pergunta:', error);
        });
}

/**
 * Processa uma pergunta e gera uma resposta usando a API da OpenAI
 */
async function processQuestion(question) {
    // Prepara o contexto completo para enviar ao backend
    const context = {
        global: globalContext,
        page: currentPageContext,
        conversation: conversationHistory.slice(-5) // Últimas 5 mensagens para contexto
    };

    try {
        console.log('Enviando pergunta para o backend:', question);
        console.log('Contexto:', context);

        // Faz a requisição para o backend
        const response = await fetch('/api/agent/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                question: question,
                context: context,
                conversation_history: conversationHistory
            })
        });

        if (!response.ok) {
            throw new Error(`Erro na requisição: ${response.status} ${response.statusText}`);
        }

        // Processa a resposta
        const data = await response.json();
        console.log('Resposta do backend:', data);

        return data;
    } catch (error) {
        console.error('Erro ao processar pergunta:', error);

        // Resposta de fallback em caso de erro
        return {
            text: `Desculpe, ocorreu um erro ao processar sua pergunta: ${error.message}. Por favor, tente novamente mais tarde.`,
            context: "Erro de comunicação"
        };
    }
}

// A função generateMockResponse foi removida pois agora usamos a API da OpenAI

/**
 * Processa uma sugestão de pergunta
 */
function askSuggestion(element) {
    const question = element.textContent;
    document.getElementById('agent-input-field').value = question;
    handleUserQuestion();
}

/**
 * Mostra o histórico completo de conversas
 */
function showFullHistory() {
    // Verifica se há histórico para mostrar
    if (conversationHistory.length === 0) {
        alert('Não há histórico de conversas para mostrar.');
        return;
    }

    // Limpa a área de conversa
    const container = document.getElementById('agent-conversation');
    container.innerHTML = '';

    // Adiciona um cabeçalho
    const headerElement = document.createElement('div');
    headerElement.className = 'history-header';
    headerElement.innerHTML = `
        <h4>Histórico Completo</h4>
        <p>Mostrando ${conversationHistory.length} mensagens</p>
    `;
    container.appendChild(headerElement);

    // Adiciona cada mensagem do histórico
    conversationHistory.forEach(message => {
        if (message.type === 'user') {
            addUserMessage(message.text);
        } else if (message.type === 'agent') {
            addAgentResponse(message.response);
        }
    });

    // Rola para o final da conversa
    container.scrollTop = container.scrollHeight;
}

/**
 * Limpa o histórico de conversas
 */
function clearHistory() {
    // Pede confirmação ao usuário
    if (confirm('Tem certeza que deseja limpar todo o histórico de conversas?')) {
        // Limpa o histórico
        conversationHistory = [];

        // Salva o histórico vazio
        saveConversationHistory();

        // Limpa a área de conversa e adiciona a mensagem de boas-vindas
        const container = document.getElementById('agent-conversation');
        container.innerHTML = '';

        const welcomeMessage = document.createElement('div');
        welcomeMessage.className = 'welcome-message';
        welcomeMessage.innerHTML = `
            <h4>Olá! Como posso ajudar?</h4>
            <p>Sou o assistente inteligente do Amigo DataHub. Posso responder perguntas sobre os dados, gerar análises e ajudar com insights.</p>
            <div class="welcome-suggestions">
                <div class="suggestion-chip" onclick="askSuggestion(this)">Resumo desta página</div>
                <div class="suggestion-chip" onclick="askSuggestion(this)">Principais insights</div>
                <div class="suggestion-chip" onclick="askSuggestion(this)">Tendências recentes</div>
                <div class="suggestion-chip" onclick="askSuggestion(this)">Recomendações</div>
            </div>
        `;
        container.appendChild(welcomeMessage);

        // Exibe uma mensagem de sucesso
        alert('Histórico de conversas limpo com sucesso!');
    }
}

/**
 * Inicia um novo chat
 */
function startNewChat() {
    // Não limpa o histórico, apenas a visualização atual
    const container = document.getElementById('agent-conversation');
    container.innerHTML = '';

    // Adiciona a mensagem de boas-vindas
    const welcomeMessage = document.createElement('div');
    welcomeMessage.className = 'welcome-message';
    welcomeMessage.innerHTML = `
        <h4>Novo Chat Iniciado!</h4>
        <p>Sou o assistente inteligente do Amigo DataHub. Como posso ajudar?</p>
        <div class="welcome-suggestions">
            <div class="suggestion-chip" onclick="askSuggestion(this)">Resumo desta página</div>
            <div class="suggestion-chip" onclick="askSuggestion(this)">Principais insights</div>
            <div class="suggestion-chip" onclick="askSuggestion(this)">Tendências recentes</div>
            <div class="suggestion-chip" onclick="askSuggestion(this)">Recomendações</div>
        </div>
    `;
    container.appendChild(welcomeMessage);

    // Limpa o campo de entrada
    document.getElementById('agent-input-field').value = '';

    // Foca no campo de entrada
    document.getElementById('agent-input-field').focus();
}

// Inicializa o agente quando o DOM estiver pronto
document.addEventListener('DOMContentLoaded', initAgent);

/**
 * Amigo Studio Pro - Exemplos de Gráficos
 * 
 * Este arquivo contém exemplos de gráficos para testes e demonstrações.
 */

// Exemplo de gráfico de barras
const barChartExample = {
    type: 'bar',
    title: 'Tempo Médio de Atendimento por Profissional',
    description: 'Comparação do tempo médio de atendimento em minutos por profissional',
    labels: ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>'],
    datasets: [
        {
            label: 'Tempo Médio (min)',
            data: [25, 30, 20, 35, 28],
            backgroundColor: 'rgba(0, 122, 255, 0.7)',
            borderColor: 'rgba(0, 122, 255, 1)',
            borderWidth: 1
        }
    ],
    options: {
        scales: {
            y: {
                beginAtZero: true,
                title: {
                    display: true,
                    text: 'Tempo (minutos)'
                }
            }
        },
        plugins: {
            title: {
                display: true,
                text: 'Tempo Médio de Atendimento por Profissional'
            }
        }
    }
};

// Exemplo de gráfico de boxplot
const boxplotChartExample = {
    type: 'boxplot',
    title: 'Distribuição de Tempo por Tipo de Procedimento',
    description: 'Boxplot mostrando a distribuição de tempo por tipo de procedimento',
    labels: ['Procedimento X', 'Procedimento Y', 'Procedimento Z', 'Procedimento W'],
    datasets: [
        {
            label: 'Tempo (min)',
            data: [
                {
                    min: 10,
                    q1: 12,
                    median: 15,
                    q3: 18,
                    max: 25
                },
                {
                    min: 30,
                    q1: 35,
                    median: 40,
                    q3: 45,
                    max: 60
                },
                {
                    min: 20,
                    q1: 25,
                    median: 30,
                    q3: 35,
                    max: 45
                },
                {
                    min: 15,
                    q1: 20,
                    median: 25,
                    q3: 30,
                    max: 40
                }
            ],
            backgroundColor: 'rgba(0, 122, 255, 0.5)',
            borderColor: 'rgba(0, 122, 255, 1)',
            borderWidth: 1
        }
    ],
    options: {
        scales: {
            y: {
                beginAtZero: true,
                title: {
                    display: true,
                    text: 'Tempo (minutos)'
                }
            }
        },
        plugins: {
            title: {
                display: true,
                text: 'Distribuição de Tempo por Tipo de Procedimento'
            }
        }
    }
};

// Exemplo de gráfico de linha
const lineChartExample = {
    type: 'line',
    title: 'Evolução do Faturamento Mensal',
    description: 'Evolução do faturamento mensal ao longo do ano',
    labels: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'],
    datasets: [
        {
            label: 'Faturamento (R$)',
            data: [320000, 310000, 315000, 340000, 360000, 375000, 390000, 400000, 380000, 390000, 410000, 430000],
            backgroundColor: 'rgba(0, 122, 255, 0.1)',
            borderColor: 'rgba(0, 122, 255, 1)',
            borderWidth: 2,
            tension: 0.4,
            fill: true
        }
    ],
    options: {
        scales: {
            y: {
                beginAtZero: false,
                title: {
                    display: true,
                    text: 'Valor (R$)'
                }
            }
        },
        plugins: {
            title: {
                display: true,
                text: 'Evolução do Faturamento Mensal'
            }
        }
    }
};

// Exemplo de gráfico de pizza
const pieChartExample = {
    type: 'pie',
    title: 'Distribuição de Pacientes por Convênio',
    description: 'Distribuição percentual de pacientes por convênio',
    labels: ['Particular', 'Convênio A', 'Convênio B', 'Convênio C', 'Outros'],
    datasets: [
        {
            data: [30, 25, 20, 15, 10],
            backgroundColor: [
                'rgba(0, 122, 255, 0.7)',
                'rgba(88, 86, 214, 0.7)',
                'rgba(90, 200, 250, 0.7)',
                'rgba(52, 199, 89, 0.7)',
                'rgba(255, 149, 0, 0.7)'
            ],
            borderColor: [
                'rgba(0, 122, 255, 1)',
                'rgba(88, 86, 214, 1)',
                'rgba(90, 200, 250, 1)',
                'rgba(52, 199, 89, 1)',
                'rgba(255, 149, 0, 1)'
            ],
            borderWidth: 1
        }
    ],
    options: {
        plugins: {
            title: {
                display: true,
                text: 'Distribuição de Pacientes por Convênio'
            }
        }
    }
};

// Exportar exemplos para uso global
window.ChartExamples = {
    barChartExample,
    boxplotChartExample,
    lineChartExample,
    pieChartExample
};

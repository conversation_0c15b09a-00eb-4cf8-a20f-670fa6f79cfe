/**
 * Amigo Studio Pro - Assistente de Gráficos
 *
 * Este módulo fornece funções para ajudar os usuários a criar gráficos no chat.
 */

// Função para abrir o assistente de gráficos
function openChartHelper() {
    // Criar container para o assistente
    const helperContainer = document.createElement('div');
    helperContainer.id = 'chart-helper-container';
    helperContainer.className = 'p-4 bg-white rounded-lg shadow-md';
    helperContainer.style.position = 'fixed';
    helperContainer.style.top = '50%';
    helperContainer.style.left = '50%';
    helperContainer.style.transform = 'translate(-50%, -50%)';
    helperContainer.style.zIndex = '9999';
    helperContainer.style.width = '800px';
    helperContainer.style.maxHeight = '90vh';
    helperContainer.style.overflowY = 'auto';

    // Adicionar título
    const title = document.createElement('h2');
    title.className = 'text-xl font-bold mb-4';
    title.textContent = 'Assistente de Gráficos';
    helperContainer.appendChild(title);

    // Adicionar botão para fechar
    const closeButton = document.createElement('button');
    closeButton.className = 'absolute top-4 right-4 text-gray-500 hover:text-gray-700';
    closeButton.innerHTML = '<i class="fas fa-times"></i>';
    closeButton.onclick = () => {
        document.body.removeChild(helperContainer);
    };
    helperContainer.appendChild(closeButton);

    // Adicionar conteúdo
    const content = document.createElement('div');
    content.className = 'mt-4';

    // Seleção de tipo de gráfico
    const typeSection = document.createElement('div');
    typeSection.className = 'mb-6';

    const typeLabel = document.createElement('label');
    typeLabel.className = 'block text-sm font-medium text-gray-700 mb-2';
    typeLabel.textContent = 'Tipo de Gráfico';
    typeSection.appendChild(typeLabel);

    const typeSelect = document.createElement('select');
    typeSelect.id = 'chart-type-select';
    typeSelect.className = 'block w-full p-2 border border-gray-300 rounded-md';

    const chartTypes = [
        { value: 'bar', label: 'Gráfico de Barras' },
        { value: 'line', label: 'Gráfico de Linha' },
        { value: 'pie', label: 'Gráfico de Pizza' },
        { value: 'doughnut', label: 'Gráfico de Rosca' },
        { value: 'radar', label: 'Gráfico de Radar' },
        { value: 'polarArea', label: 'Gráfico de Área Polar' },
        { value: 'scatter', label: 'Gráfico de Dispersão' }
    ];

    chartTypes.forEach(type => {
        const option = document.createElement('option');
        option.value = type.value;
        option.textContent = type.label;
        typeSelect.appendChild(option);
    });

    typeSection.appendChild(typeSelect);
    content.appendChild(typeSection);

    // Título do gráfico
    const titleSection = document.createElement('div');
    titleSection.className = 'mb-6';

    const titleLabel = document.createElement('label');
    titleLabel.className = 'block text-sm font-medium text-gray-700 mb-2';
    titleLabel.textContent = 'Título do Gráfico';
    titleSection.appendChild(titleLabel);

    const titleInput = document.createElement('input');
    titleInput.id = 'chart-title-input';
    titleInput.type = 'text';
    titleInput.className = 'block w-full p-2 border border-gray-300 rounded-md';
    titleInput.placeholder = 'Digite o título do gráfico';
    titleSection.appendChild(titleInput);

    content.appendChild(titleSection);

    // Dados do gráfico
    const dataSection = document.createElement('div');
    dataSection.className = 'mb-6';

    const dataLabel = document.createElement('label');
    dataLabel.className = 'block text-sm font-medium text-gray-700 mb-2';
    dataLabel.textContent = 'Dados do Gráfico';
    dataSection.appendChild(dataLabel);

    // Rótulos (eixo X)
    const labelsLabel = document.createElement('label');
    labelsLabel.className = 'block text-sm font-medium text-gray-700 mt-4 mb-2';
    labelsLabel.textContent = 'Rótulos (eixo X)';
    dataSection.appendChild(labelsLabel);

    const labelsInput = document.createElement('input');
    labelsInput.id = 'chart-labels-input';
    labelsInput.type = 'text';
    labelsInput.className = 'block w-full p-2 border border-gray-300 rounded-md';
    labelsInput.placeholder = 'Rótulo 1, Rótulo 2, Rótulo 3, ...';
    dataSection.appendChild(labelsInput);

    // Valores (eixo Y)
    const valuesLabel = document.createElement('label');
    valuesLabel.className = 'block text-sm font-medium text-gray-700 mt-4 mb-2';
    valuesLabel.textContent = 'Valores (eixo Y)';
    dataSection.appendChild(valuesLabel);

    const valuesInput = document.createElement('input');
    valuesInput.id = 'chart-values-input';
    valuesInput.type = 'text';
    valuesInput.className = 'block w-full p-2 border border-gray-300 rounded-md';
    valuesInput.placeholder = '10, 20, 30, ...';
    dataSection.appendChild(valuesInput);

    content.appendChild(dataSection);

    // Botão para gerar o gráfico
    const generateButton = document.createElement('button');
    generateButton.className = 'w-full bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded';
    generateButton.textContent = 'Gerar Gráfico';
    generateButton.onclick = () => {
        generateChartFromInputs();
        document.body.removeChild(helperContainer);
    };
    content.appendChild(generateButton);

    helperContainer.appendChild(content);

    // Adicionar ao corpo do documento
    document.body.appendChild(helperContainer);
}

// Função para validar os inputs do usuário
function validateChartInputs() {
    const chartTitle = document.getElementById('chart-title-input').value;
    const labelsStr = document.getElementById('chart-labels-input').value;
    const valuesStr = document.getElementById('chart-values-input').value;

    // Verificar se os rótulos e valores foram fornecidos
    if (!labelsStr.trim()) {
        alert('Por favor, forneça pelo menos um rótulo para o eixo X.');
        return false;
    }

    if (!valuesStr.trim()) {
        alert('Por favor, forneça pelo menos um valor para o eixo Y.');
        return false;
    }

    // Verificar se os valores são números válidos
    const values = valuesStr.split(',');
    for (let i = 0; i < values.length; i++) {
        const value = parseFloat(values[i].trim());
        if (isNaN(value)) {
            alert(`O valor "${values[i].trim()}" não é um número válido.`);
            return false;
        }
    }

    return true;
}

// Função para gerar um gráfico a partir dos inputs do usuário
function generateChartFromInputs() {
    // Validar inputs
    if (!validateChartInputs()) {
        return;
    }

    // Obter valores dos inputs
    const chartType = document.getElementById('chart-type-select').value;
    const chartTitle = document.getElementById('chart-title-input').value;
    const labelsStr = document.getElementById('chart-labels-input').value;
    const valuesStr = document.getElementById('chart-values-input').value;

    // Processar rótulos
    const labels = labelsStr.split(',').map(label => label.trim());

    // Processar valores
    const values = valuesStr.split(',').map(value => parseFloat(value.trim()));

    // Criar objeto de dados do gráfico
    const chartData = {
        type: chartType,
        title: chartTitle || 'Gráfico',
        labels: labels,
        datasets: [
            {
                label: 'Dados',
                data: values,
                backgroundColor: 'rgba(0, 122, 255, 0.7)',
                borderColor: 'rgba(0, 122, 255, 1)',
                borderWidth: 1
            }
        ],
        options: {
            plugins: {
                title: {
                    display: true,
                    text: chartTitle || 'Gráfico'
                }
            }
        }
    };

    // Verificar se o JSON é válido usando a função do ChartProcessor
    const chartJson = JSON.stringify(chartData, null, 2);
    if (window.ChartProcessor && typeof window.ChartProcessor.isValidJSON === 'function') {
        if (!window.ChartProcessor.isValidJSON(chartJson)) {
            alert('Erro ao gerar o JSON do gráfico. Por favor, verifique os dados fornecidos.');
            return;
        }
    }

    // Criar mensagem com o gráfico
    let message = `# ${chartTitle || 'Gráfico'}\n\n`;
    message += "```chart\n";
    message += chartJson;
    message += "\n```\n\n";

    // Adicionar mensagem ao chat como se fosse do assistente
    addMessage('assistant', message);
}

// Exportar funções para uso global
window.ChartHelper = {
    openChartHelper,
    generateChartFromInputs,
    validateChartInputs
};

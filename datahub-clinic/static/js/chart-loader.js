/**
 * Carregador de bibliotecas para o Amigo Studio Pro
 * Este script carrega as bibliotecas necessárias (Chart.js, Popper.js, Tippy.js)
 * de forma que evite conflitos com o sistema de módulos AMD do Monaco Editor
 */

// Variável global para controlar o carregamento das bibliotecas
window.librariesStatus = {
    chart: false,
    popper: false,
    tippy: false,
    allLoaded: false
};

// URLs das bibliotecas
const CHART_JS_URL = 'https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js';
const POPPER_JS_URL = 'https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js';
const TIPPY_JS_URL = 'https://cdn.jsdelivr.net/npm/tippy.js@6.3.7/dist/tippy-bundle.umd.min.js';

// URLs alternativas (fallback)
const CHART_JS_FALLBACK_URL = 'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js';
const POPPER_JS_FALLBACK_URL = 'https://cdnjs.cloudflare.com/ajax/libs/popper.js/2.11.6/umd/popper.min.js';
const TIPPY_JS_FALLBACK_URL = 'https://cdnjs.cloudflare.com/ajax/libs/tippy.js/6.3.7/tippy-bundle.umd.min.js';

/**
 * Carrega um script via XHR e o executa no escopo global
 * Esta abordagem evita que o script seja processado pelo RequireJS
 *
 * @param {string} url - URL do script a ser carregado
 * @param {string} fallbackUrl - URL alternativa caso a primeira falhe
 * @param {Function} callback - Função a ser chamada após o carregamento
 */
function loadScriptViaXHR(url, fallbackUrl, callback) {
    const xhr = new XMLHttpRequest();
    xhr.open('GET', url, true);

    xhr.onload = function() {
        if (xhr.status >= 200 && xhr.status < 300) {
            // Executar o script no escopo global
            const scriptContent = xhr.responseText;
            const scriptElement = document.createElement('script');
            scriptElement.textContent = scriptContent;
            document.head.appendChild(scriptElement);

            console.log(`Script carregado com sucesso: ${url}`);
            callback && callback(true);
        } else {
            console.warn(`Falha ao carregar script: ${url}. Tentando fallback...`);

            // Tentar URL alternativa
            if (fallbackUrl) {
                const fallbackXhr = new XMLHttpRequest();
                fallbackXhr.open('GET', fallbackUrl, true);

                fallbackXhr.onload = function() {
                    if (fallbackXhr.status >= 200 && fallbackXhr.status < 300) {
                        // Executar o script alternativo no escopo global
                        const scriptContent = fallbackXhr.responseText;
                        const scriptElement = document.createElement('script');
                        scriptElement.textContent = scriptContent;
                        document.head.appendChild(scriptElement);

                        console.log(`Script alternativo carregado com sucesso: ${fallbackUrl}`);
                        callback && callback(true);
                    } else {
                        console.error(`Falha ao carregar script alternativo: ${fallbackUrl}`);
                        callback && callback(false);
                    }
                };

                fallbackXhr.onerror = function() {
                    console.error(`Erro de rede ao carregar script alternativo: ${fallbackUrl}`);
                    callback && callback(false);
                };

                fallbackXhr.send();
            } else {
                console.error(`Falha ao carregar script e nenhum fallback disponível: ${url}`);
                callback && callback(false);
            }
        }
    };

    xhr.onerror = function() {
        console.warn(`Erro de rede ao carregar script: ${url}. Tentando fallback...`);

        // Tentar URL alternativa
        if (fallbackUrl) {
            loadScriptViaXHR(fallbackUrl, null, callback);
        } else {
            console.error(`Erro de rede ao carregar script e nenhum fallback disponível: ${url}`);
            callback && callback(false);
        }
    };

    xhr.send();
}

/**
 * Verifica se todas as bibliotecas foram carregadas
 * e dispara um evento quando todas estiverem prontas
 */
function checkAllLibrariesLoaded() {
    if (window.librariesStatus.chart &&
        window.librariesStatus.popper &&
        window.librariesStatus.tippy) {

        window.librariesStatus.allLoaded = true;
        console.log('Todas as bibliotecas foram carregadas com sucesso');

        // Disparar evento de carregamento completo
        const event = new Event('alllibrariesloaded');
        document.dispatchEvent(event);

        // Inicializar tooltips se o Tippy.js estiver disponível
        if (typeof tippy === 'function') {
            console.log('Inicializando tooltips...');
            try {
                tippy('[data-tippy-content]');
                console.log('Tooltips inicializados com sucesso');
            } catch (e) {
                console.warn('Erro ao inicializar tooltips:', e);
            }
        }
    }
}

// Carregar Chart.js
console.log('Carregando Chart.js...');
loadScriptViaXHR(CHART_JS_URL, CHART_JS_FALLBACK_URL, function(success) {
    if (success) {
        window.librariesStatus.chart = true;
        window.chartJsLoaded = true;

        // Verificar se o Chart.js está realmente disponível
        if (typeof Chart === 'function') {
            console.log('Chart.js está disponível globalmente');

            // Disparar evento de carregamento do Chart.js
            const event = new Event('chartjsloaded');
            document.dispatchEvent(event);
        } else {
            console.warn('Chart.js foi carregado, mas não está disponível globalmente');

            // Tentar definir Chart globalmente se estiver disponível em algum escopo
            setTimeout(function() {
                if (typeof Chart === 'function') {
                    console.log('Chart.js detectado após timeout');
                    const event = new Event('chartjsloaded');
                    document.dispatchEvent(event);
                } else {
                    console.warn('Chart.js ainda não está disponível após timeout');

                    // Tentar carregar diretamente via script tag
                    const scriptElement = document.createElement('script');
                    scriptElement.src = CHART_JS_FALLBACK_URL;
                    scriptElement.onload = function() {
                        console.log('Chart.js carregado via script tag');
                        window.chartJsLoaded = true;
                        const event = new Event('chartjsloaded');
                        document.dispatchEvent(event);
                    };
                    document.head.appendChild(scriptElement);
                }
            }, 1000);
        }

        checkAllLibrariesLoaded();
    }
});

// Carregar Popper.js
console.log('Carregando Popper.js...');
loadScriptViaXHR(POPPER_JS_URL, POPPER_JS_FALLBACK_URL, function(success) {
    if (success) {
        window.librariesStatus.popper = true;

        // Verificar se o Popper.js está realmente disponível
        if (typeof Popper === 'object') {
            console.log('Popper.js está disponível globalmente');
        } else {
            console.warn('Popper.js foi carregado, mas não está disponível globalmente');
        }

        // Carregar Tippy.js após o Popper.js
        console.log('Carregando Tippy.js...');
        loadScriptViaXHR(TIPPY_JS_URL, TIPPY_JS_FALLBACK_URL, function(success) {
            if (success) {
                window.librariesStatus.tippy = true;
                window.tippyLoaded = true;

                // Verificar se o Tippy.js está realmente disponível
                if (typeof tippy === 'function') {
                    console.log('Tippy.js está disponível globalmente');

                    // Disparar evento de carregamento do Tippy.js
                    const event = new Event('tippyloaded');
                    document.dispatchEvent(event);
                } else {
                    console.warn('Tippy.js foi carregado, mas não está disponível globalmente');
                }

                checkAllLibrariesLoaded();
            }
        });
    }
});

// Verificar periodicamente se as bibliotecas estão disponíveis
let checkCount = 0;
const maxChecks = 10;
const checkInterval = setInterval(function() {
    checkCount++;

    // Verificar Chart.js
    if (!window.librariesStatus.chart && typeof Chart === 'function') {
        console.log('Chart.js detectado durante verificação periódica');
        window.librariesStatus.chart = true;
        window.chartJsLoaded = true;
        document.dispatchEvent(new Event('chartjsloaded'));
    }

    // Verificar Popper.js
    if (!window.librariesStatus.popper && typeof Popper === 'object') {
        console.log('Popper.js detectado durante verificação periódica');
        window.librariesStatus.popper = true;
    }

    // Verificar Tippy.js
    if (!window.librariesStatus.tippy && typeof tippy === 'function') {
        console.log('Tippy.js detectado durante verificação periódica');
        window.librariesStatus.tippy = true;
        window.tippyLoaded = true;
        document.dispatchEvent(new Event('tippyloaded'));
    }

    // Verificar se todas as bibliotecas foram carregadas
    checkAllLibrariesLoaded();

    // Parar a verificação após um número máximo de tentativas
    if (window.librariesStatus.allLoaded || checkCount >= maxChecks) {
        clearInterval(checkInterval);
    }
}, 1000);

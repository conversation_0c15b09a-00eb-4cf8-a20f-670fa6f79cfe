/**
 * Amigo Studio Pro - Chart Processor
 *
 * Este módulo processa e renderiza gráficos no chat do Amigo Studio Pro.
 * Ele lida com diferentes formatos de dados de gráficos e garante uma renderização consistente.
 */

// Configurações globais para gráficos
const defaultChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
        legend: {
            position: 'top',
        },
        title: {
            display: true,
            text: 'Gráfic<PERSON>'
        }
    },
    scales: {
        y: {
            beginAtZero: true
        }
    }
};

// Cores padrão para gráficos
const defaultColors = [
    'rgba(0, 122, 255, 0.7)',   // Azul
    'rgba(88, 86, 214, 0.7)',   // Roxo
    'rgba(90, 200, 250, 0.7)',  // Azul claro
    'rgba(52, 199, 89, 0.7)',   // Verde
    'rgba(255, 149, 0, 0.7)',   // <PERSON><PERSON>
    'rgba(255, 59, 48, 0.7)',   // Vermelho
    'rgba(175, 82, 222, 0.7)',  // Roxo claro
    'rgba(85, 85, 85, 0.7)'     // Cinza
];

// Cores de borda padrão
const defaultBorderColors = [
    'rgba(0, 122, 255, 1)',
    'rgba(88, 86, 214, 1)',
    'rgba(90, 200, 250, 1)',
    'rgba(52, 199, 89, 1)',
    'rgba(255, 149, 0, 1)',
    'rgba(255, 59, 48, 1)',
    'rgba(175, 82, 222, 1)',
    'rgba(85, 85, 85, 1)'
];

/**
 * Garante que o Chart.js esteja disponível
 */
function ensureChartJsAvailable() {
    // Verificar se o Chart.js já está disponível
    if (typeof Chart === 'function') {
        console.log('Chart.js já está disponível');
        window.chartJsLoaded = true;
        return true;
    }

    console.warn('Chart.js não está disponível. Criando implementação mínima...');

    // Criar uma implementação mínima do Chart.js
    window.Chart = function(ctx, config) {
        this.ctx = ctx;
        this.config = config;
        this.type = config.type || 'bar';
        this.data = config.data || {};
        this.options = config.options || {};

        // Método para destruir o gráfico
        this.destroy = function() {
            console.log('Destruindo gráfico...');
            // Limpar o canvas
            if (this.ctx) {
                this.ctx.clearRect(0, 0, this.ctx.canvas.width, this.ctx.canvas.height);
            }
        };

        // Método para atualizar o gráfico
        this.update = function() {
            console.log('Atualizando gráfico...');
            // Implementação mínima
        };

        // Renderizar o gráfico
        console.log('Renderizando gráfico mínimo do tipo:', this.type);

        // Desenhar um texto informando que o Chart.js completo está sendo carregado
        if (this.ctx) {
            this.ctx.font = '14px Arial';
            this.ctx.fillStyle = '#333';
            this.ctx.textAlign = 'center';
            this.ctx.fillText('Carregando gráfico...', this.ctx.canvas.width / 2, this.ctx.canvas.height / 2);

            // Desenhar uma borda
            this.ctx.strokeStyle = '#ccc';
            this.ctx.lineWidth = 1;
            this.ctx.strokeRect(0, 0, this.ctx.canvas.width, this.ctx.canvas.height);
        }
    };

    window.chartJsLoaded = true;
    document.dispatchEvent(new Event('chartjsloaded'));

    // Tentar carregar a versão completa do Chart.js
    var script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js';
    document.head.appendChild(script);

    return true;
}

/**
 * Processa uma resposta de texto e extrai/renderiza gráficos
 *
 * @param {string} response - Texto da resposta
 * @returns {string} - Resposta processada com gráficos renderizados
 */
function processChartResponse(response) {
    // Garantir que o Chart.js esteja disponível
    ensureChartJsAvailable();

    if (!response) return response;

    // Verificar se a resposta contém dados de gráfico
    if (response.includes('```chart') && response.includes('```')) {
        console.log('Detectados blocos de gráfico na resposta');

        // Processar todos os gráficos na resposta
        let processedResponse = response;

        // Usar regex para encontrar todos os blocos de gráfico
        // Esta regex melhorada captura o conteúdo entre ```chart, ```json chart e ``` mesmo com quebras de linha
        let chartMatches = [...processedResponse.matchAll(/```(?:json\s+)?chart\s*([\s\S]*?)```/g)];

        console.log(`Encontrados ${chartMatches.length} blocos de gráfico`);

        chartMatches.forEach((match, index) => {
            try {
                // Obter o conteúdo JSON e limpar espaços extras
                let chartJsonStr = match[1].trim();

                // Verificar se o JSON começa com { e termina com }
                if (!chartJsonStr.startsWith('{') || !chartJsonStr.endsWith('}')) {
                    console.warn('Formato JSON inválido, tentando corrigir...');

                    // Tentar extrair apenas a parte JSON válida
                    const jsonStartIndex = chartJsonStr.indexOf('{');
                    const jsonEndIndex = chartJsonStr.lastIndexOf('}') + 1;

                    if (jsonStartIndex >= 0 && jsonEndIndex > jsonStartIndex) {
                        chartJsonStr = chartJsonStr.substring(jsonStartIndex, jsonEndIndex);
                    } else {
                        throw new Error('Não foi possível encontrar um objeto JSON válido');
                    }
                }

                // Tentar corrigir o JSON usando a função de correção
                chartJsonStr = tryFixJSON(chartJsonStr);

                console.log('JSON processado:', chartJsonStr);

                // Analisar o JSON
                let chartData;
                try {
                    if (isValidJSON(chartJsonStr)) {
                        chartData = JSON.parse(chartJsonStr);
                    } else {
                        throw new Error('JSON inválido após tentativas de correção');
                    }
                } catch (jsonError) {
                    console.error('Erro ao analisar JSON:', jsonError);

                    // Criar um objeto básico para não quebrar a renderização
                    chartData = {
                        type: 'bar',
                        labels: ['Erro'],
                        datasets: [{
                            label: 'Erro de Parsing',
                            data: [0],
                            backgroundColor: 'rgba(255, 0, 0, 0.5)'
                        }]
                    };

                    // Adicionar mensagem de erro
                    throw new Error(`Erro ao analisar JSON: ${jsonError.message}`);
                }

                // Criar ID único para o gráfico
                const chartId = `chart-${Date.now()}-${index}`;

                // Criar o HTML simplificado com o canvas
                const chartHtmlWithScript = `<div class="chart-container my-4" style="height: 400px;"><canvas id="${chartId}"></canvas></div>`;

                // Substituir o bloco de código do gráfico pelo HTML simplificado
                processedResponse = processedResponse.replace(match[0], chartHtmlWithScript);

                // Adicionar script para renderizar o gráfico após a resposta ser adicionada ao DOM
                // Usar um timeout maior e verificar se o elemento existe
                setTimeout(() => {
                    renderChartFromDataWithRetry(chartId, chartData, 0);
                }, 300);
            } catch (error) {
                console.error('Erro ao processar gráfico:', error);

                // Substituir o bloco de código por uma mensagem de erro
                const errorHtml = `<div class="error-message p-3 bg-red-50 text-red-600 rounded-md">Erro ao processar gráfico: ${error.message}</div>`;
                processedResponse = processedResponse.replace(match[0], errorHtml);
            }
        });

        return processedResponse;
    }

    return response;
}

/**
 * Renderiza um gráfico com tentativas de retry
 *
 * @param {string} chartId - ID do elemento canvas
 * @param {object} chartData - Dados do gráfico
 * @param {number} retryCount - Número de tentativas já realizadas
 */
function renderChartFromDataWithRetry(chartId, chartData, retryCount = 0) {
    const maxRetries = 10;
    const retryDelay = 200;

    const canvas = document.getElementById(chartId);
    if (!canvas) {
        if (retryCount < maxRetries) {
            console.log(`Tentativa ${retryCount + 1}/${maxRetries} - Canvas ${chartId} não encontrado, tentando novamente...`);
            setTimeout(() => {
                renderChartFromDataWithRetry(chartId, chartData, retryCount + 1);
            }, retryDelay);
            return;
        } else {
            console.error(`Canvas ${chartId} não encontrado após ${maxRetries} tentativas`);
            return;
        }
    }

    // Canvas encontrado, renderizar o gráfico
    renderChartFromData(chartId, chartData);
}

/**
 * Renderiza um gráfico a partir de dados
 *
 * @param {string} chartId - ID do elemento canvas
 * @param {object} chartData - Dados do gráfico
 */
function renderChartFromData(chartId, chartData) {
    try {
        console.log('Renderizando gráfico:', chartId);

        const canvas = document.getElementById(chartId);
        if (!canvas) {
            console.error('Elemento canvas não encontrado:', chartId);
            return;
        }

        const ctx = canvas.getContext('2d');
        if (!ctx) {
            console.error('Contexto 2D não disponível para o canvas:', chartId);
            return;
        }

        // Garantir que o Chart.js esteja disponível
        ensureChartJsAvailable();

        // Aplicar cores padrão se não especificadas
        if (chartData.datasets && Array.isArray(chartData.datasets)) {
            chartData.datasets.forEach((dataset, index) => {
                const colorIndex = index % defaultColors.length;

                // Aplicar cor de fundo padrão se não especificada
                if (!dataset.backgroundColor) {
                    dataset.backgroundColor = defaultColors[colorIndex];
                }

                // Aplicar cor de borda padrão se não especificada
                if (!dataset.borderColor) {
                    dataset.borderColor = defaultBorderColors[colorIndex];
                }

                // Aplicar largura de borda padrão se não especificada
                if (!dataset.borderWidth) {
                    dataset.borderWidth = 1;
                }
            });
        }

        // Mesclar opções padrão com as opções fornecidas
        const finalOptions = chartData.options
            ? { ...defaultChartOptions, ...chartData.options }
            : defaultChartOptions;

        // Verificar se já existe um gráfico neste canvas e destruí-lo
        if (window.chartInstances && window.chartInstances[chartId]) {
            try {
                window.chartInstances[chartId].destroy();
            } catch (e) {
                console.warn('Erro ao destruir gráfico existente:', e);
            }
        }

        // Inicializar o objeto de instâncias de gráficos se não existir
        if (!window.chartInstances) {
            window.chartInstances = {};
        }

        // Normalizar dados do gráfico
        const normalizedData = normalizeChartData(chartData);

        // Criar o gráfico
        try {
            window.chartInstances[chartId] = new Chart(ctx, {
                type: normalizedData.type || 'bar',
                data: {
                    labels: normalizedData.labels || [],
                    datasets: normalizedData.datasets || []
                },
                options: finalOptions
            });

            console.log('Gráfico renderizado com sucesso:', chartId);
        } catch (chartError) {
            console.error('Erro ao criar instância do Chart:', chartError);
            throw chartError;
        }
    } catch (error) {
        console.error('Erro ao renderizar gráfico:', error);

        // Exibir mensagem de erro no lugar do gráfico
        const canvas = document.getElementById(chartId);
        if (canvas) {
            const container = canvas.parentElement;
            if (container) {
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error-message p-3 bg-red-50 text-red-600 rounded-md';
                errorDiv.textContent = `Erro ao renderizar gráfico: ${error.message}`;
                container.replaceChild(errorDiv, canvas);
            }
        }
    }
}

/**
 * Normaliza os dados do gráfico para o formato esperado pelo Chart.js
 *
 * @param {object} chartData - Dados do gráfico
 * @returns {object} - Dados normalizados
 */
function normalizeChartData(chartData) {
    // Se já tem a estrutura correta, retornar como está
    if (chartData.labels && chartData.datasets) {
        return chartData;
    }

    // Se tem estrutura aninhada com 'data'
    if (chartData.data && chartData.data.labels && chartData.data.datasets) {
        return {
            type: chartData.type || 'bar',
            labels: chartData.data.labels,
            datasets: chartData.data.datasets
        };
    }

    // Se tem arrays x e y (formato de linha)
    if (chartData.x && chartData.y) {
        return {
            type: chartData.type || 'line',
            labels: chartData.labels || chartData.x.map((_, i) => `Item ${i + 1}`),
            datasets: [{
                label: chartData.title || 'Dados',
                data: chartData.y,
                backgroundColor: 'rgba(54, 162, 235, 0.5)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 2,
                tension: 0.4
            }]
        };
    }

    // Se tem values (formato de pizza)
    if (chartData.values && chartData.labels) {
        return {
            type: chartData.type || 'pie',
            labels: chartData.labels,
            datasets: [{
                data: chartData.values,
                backgroundColor: [
                    'rgba(54, 162, 235, 0.7)',
                    'rgba(255, 99, 132, 0.7)',
                    'rgba(255, 205, 86, 0.7)',
                    'rgba(75, 192, 192, 0.7)',
                    'rgba(153, 102, 255, 0.7)'
                ]
            }]
        };
    }

    // Fallback: retornar dados básicos
    return {
        type: chartData.type || 'bar',
        labels: chartData.labels || ['Item 1', 'Item 2', 'Item 3'],
        datasets: chartData.datasets || [{
            label: 'Dados',
            data: [10, 20, 30],
            backgroundColor: 'rgba(54, 162, 235, 0.5)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1
        }]
    };
}

/**
 * Verifica se uma string JSON é válida
 *
 * @param {string} jsonString - String JSON a ser verificada
 * @returns {boolean} - Verdadeiro se a string for um JSON válido
 */
function isValidJSON(jsonString) {
    try {
        JSON.parse(jsonString);
        return true;
    } catch (e) {
        return false;
    }
}

/**
 * Tenta corrigir uma string JSON inválida
 *
 * @param {string} jsonString - String JSON a ser corrigida
 * @returns {string} - String JSON corrigida ou a original se não for possível corrigir
 */
function tryFixJSON(jsonString) {
    console.log('Tentando corrigir JSON:', jsonString);

    // Remover comentários
    let fixed = jsonString.replace(/\/\/.*$/gm, '');
    fixed = fixed.replace(/\/\*[\s\S]*?\*\//g, '');

    // Substituir [...] por array vazio
    fixed = fixed.replace(/\[\s*\.\.\.\s*\]/g, '[]');

    // Remover vírgulas extras no final de arrays e objetos
    fixed = fixed.replace(/,\s*}/g, '}');
    fixed = fixed.replace(/,\s*\]/g, ']');

    // Adicionar aspas a propriedades sem aspas (comum em JSON escrito à mão)
    fixed = fixed.replace(/(\{|\,)\s*([a-zA-Z0-9_]+)\s*\:/g, '$1"$2":');

    // Verificar se a correção funcionou
    if (isValidJSON(fixed)) {
        console.log('JSON corrigido com sucesso:', fixed);
        return fixed;
    }

    // Se não funcionou, tentar extrair apenas a parte JSON válida
    const jsonStartIndex = jsonString.indexOf('{');
    const jsonEndIndex = jsonString.lastIndexOf('}') + 1;

    if (jsonStartIndex >= 0 && jsonEndIndex > jsonStartIndex) {
        fixed = jsonString.substring(jsonStartIndex, jsonEndIndex);

        // Aplicar as mesmas correções à parte extraída
        fixed = fixed.replace(/\/\/.*$/gm, '');
        fixed = fixed.replace(/\/\*[\s\S]*?\*\//g, '');
        fixed = fixed.replace(/\[\s*\.\.\.\s*\]/g, '[]');
        fixed = fixed.replace(/,\s*}/g, '}');
        fixed = fixed.replace(/,\s*\]/g, ']');
        fixed = fixed.replace(/(\{|\,)\s*([a-zA-Z0-9_]+)\s*\:/g, '$1"$2":');

        // Verificar se a extração funcionou
        if (isValidJSON(fixed)) {
            console.log('JSON extraído e corrigido com sucesso:', fixed);
            return fixed;
        }
    }

    // Tentar uma abordagem mais agressiva
    try {
        // Substituir aspas simples por aspas duplas
        fixed = jsonString.replace(/'/g, '"');

        // Adicionar aspas a propriedades sem aspas
        fixed = fixed.replace(/(\{|\,)\s*([a-zA-Z0-9_]+)\s*\:/g, '$1"$2":');

        // Remover espaços extras
        fixed = fixed.replace(/\s+/g, ' ').trim();

        // Verificar se a correção funcionou
        if (isValidJSON(fixed)) {
            console.log('JSON corrigido com abordagem agressiva:', fixed);
            return fixed;
        }
    } catch (e) {
        console.warn('Erro ao tentar corrigir JSON de forma agressiva:', e);
    }

    // Se nada funcionou, tentar criar um objeto JSON básico
    console.warn('Não foi possível corrigir o JSON. Criando um objeto básico.');

    // Extrair informações básicas do JSON original
    const typeMatch = jsonString.match(/"type"\s*:\s*"([^"]+)"/);
    const labelsMatch = jsonString.match(/"labels"\s*:\s*\[(.*?)\]/);
    const dataMatch = jsonString.match(/"data"\s*:\s*\[(.*?)\]/);

    const type = typeMatch ? typeMatch[1] : 'bar';
    const labels = labelsMatch ? JSON.parse(`[${labelsMatch[1]}]`) : ['Item 1', 'Item 2', 'Item 3'];
    const data = dataMatch ? JSON.parse(`[${dataMatch[1]}]`) : [10, 20, 30];

    const basicJSON = {
        type: type,
        labels: labels,
        datasets: [{
            label: 'Dados',
            data: data,
            backgroundColor: 'rgba(54, 162, 235, 0.5)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1
        }]
    };

    console.log('Objeto JSON básico criado:', basicJSON);
    return JSON.stringify(basicJSON);
}

// Exportar funções para uso global
window.ChartProcessor = {
    processChartResponse,
    renderChartFromData,
    renderChartFromDataWithRetry,
    normalizeChartData,
    isValidJSON,
    tryFixJSON
};

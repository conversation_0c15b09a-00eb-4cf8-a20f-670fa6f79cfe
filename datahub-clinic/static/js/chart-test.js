/**
 * <PERSON><PERSON><PERSON>lo de teste para gráficos
 * Este script contém funções para testar a renderização de gráficos
 */

// Dados de exemplo para gráficos de teste
const testChartData = {
    bar: {
        type: 'bar',
        labels: ['Janeiro', 'Fevereiro', '<PERSON><PERSON><PERSON>', '<PERSON>bril', '<PERSON><PERSON>'],
        datasets: [
            {
                label: 'Vendas',
                data: [12, 19, 3, 5, 2],
                backgroundColor: 'rgba(54, 162, 235, 0.5)'
            }
        ]
    },
    line: {
        type: 'line',
        labels: ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio'],
        datasets: [
            {
                label: 'Temperatura',
                data: [22, 24, 27, 23, 20],
                borderColor: 'rgba(255, 99, 132, 1)',
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                fill: true
            }
        ]
    },
    pie: {
        type: 'pie',
        labels: ['Consultas', 'Exames', 'Cirurgias', 'Outros'],
        datasets: [
            {
                data: [300, 200, 100, 50],
                backgroundColor: [
                    'rgba(54, 162, 235, 0.5)',
                    'rgba(255, 99, 132, 0.5)',
                    'rgba(75, 192, 192, 0.5)',
                    'rgba(255, 206, 86, 0.5)'
                ]
            }
        ]
    },
    boxplot: {
        type: 'boxplot',
        labels: ['Consulta', 'Exame', 'Cirurgia'],
        datasets: [
            {
                label: 'Tempo de Atendimento',
                data: [[25, 30, 35], [35, 40, 50], [110, 115, 120]]
            }
        ]
    }
};

/**
 * Testa a renderização de um gráfico diretamente no DOM
 *
 * @param {string} containerId - ID do elemento container onde o gráfico será renderizado
 * @param {string} chartType - Tipo de gráfico a ser renderizado (bar, line, pie, boxplot)
 * @returns {boolean} - Verdadeiro se o gráfico foi renderizado com sucesso
 */
function testChartRendering(containerId, chartType) {
    // Verificar se o container existe
    const container = document.getElementById(containerId);
    if (!container) {
        console.error(`Container ${containerId} não encontrado`);
        return false;
    }

    // Verificar se o Chart.js está disponível
    if (typeof Chart !== 'function') {
        console.error('Chart.js não está disponível');

        // Exibir mensagem de erro no container
        container.innerHTML = `<div class="flex items-center justify-center h-full bg-red-50 text-red-600 text-sm p-2 rounded">Chart.js não disponível</div>`;

        return false;
    }

    // Obter os dados do gráfico
    const chartData = testChartData[chartType] || testChartData.bar;

    try {
        // Criar o canvas para o gráfico (se não existir)
        let canvas = container.querySelector('canvas');
        if (!canvas) {
            canvas = document.createElement('canvas');
            // Limpar o container e adicionar o canvas
            container.innerHTML = '';
            container.appendChild(canvas);
        }

        // Obter o contexto 2D do canvas
        const ctx = canvas.getContext('2d');

        // Criar o gráfico com opções simplificadas
        new Chart(ctx, {
            type: chartData.type === 'boxplot' ? 'bar' : chartData.type,
            data: {
                labels: chartData.labels,
                datasets: chartData.type === 'boxplot'
                    ? chartData.datasets.map(ds => {
                        // Converter dados de boxplot para médias
                        return {
                            ...ds,
                            data: ds.data.map(d => Array.isArray(d)
                                ? d.reduce((a, b) => a + b, 0) / d.length
                                : d)
                        };
                    })
                    : chartData.datasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: 500 // Animação mais rápida
                },
                plugins: {
                    legend: {
                        display: chartData.datasets.length > 1,
                        position: 'top',
                    },
                    tooltip: {
                        enabled: true
                    }
                }
            }
        });

        console.log(`Gráfico de teste (${chartType}) renderizado com sucesso`);
        return true;
    } catch (error) {
        console.error(`Erro ao renderizar gráfico de teste (${chartType}):`, error);

        // Exibir mensagem de erro no container (mais compacta)
        container.innerHTML = `<div class="flex items-center justify-center h-full bg-red-50 text-red-600 text-sm p-2 rounded">Erro: ${error.message}</div>`;

        return false;
    }
}

/**
 * Testa a renderização de todos os tipos de gráficos
 *
 * @param {string} containerId - ID do elemento container onde os gráficos serão renderizados
 * @returns {object} - Objeto com os resultados dos testes
 */
function testAllChartTypes(containerId) {
    // Verificar se o container existe
    const container = document.getElementById(containerId);
    if (!container) {
        console.error(`Container ${containerId} não encontrado`);
        return { success: false, error: 'Container não encontrado' };
    }

    // Limpar o container e adicionar título
    container.innerHTML = '<h3 class="text-lg font-semibold mb-4">Teste de Renderização de Gráficos</h3>';

    // Criar grid para os gráficos (2 colunas)
    const chartGrid = document.createElement('div');
    chartGrid.className = 'grid grid-cols-1 md:grid-cols-2 gap-4';
    container.appendChild(chartGrid);

    // Criar containers para cada tipo de gráfico
    const chartTypes = Object.keys(testChartData);
    const results = {};

    chartTypes.forEach(chartType => {
        // Criar container para o gráfico com título integrado
        const chartContainer = document.createElement('div');
        chartContainer.className = 'chart-test-item border border-gray-200 rounded-md overflow-hidden';

        // Adicionar título dentro do container
        chartContainer.innerHTML = `
            <div class="bg-gray-50 px-3 py-2 border-b border-gray-200">
                <h4 class="text-sm font-medium">Gráfico de ${chartType.charAt(0).toUpperCase() + chartType.slice(1)}</h4>
            </div>
            <div id="test-chart-${chartType}" class="chart-container" style="height: 250px;"></div>
        `;

        // Adicionar ao grid
        chartGrid.appendChild(chartContainer);

        // Renderizar o gráfico
        const chartId = `test-chart-${chartType}`;

        // Testar a renderização
        results[chartType] = testChartRendering(chartId, chartType);
    });

    // Adicionar resumo dos resultados de forma mais compacta
    const successCount = Object.values(results).filter(r => r).length;
    const totalCount = Object.keys(results).length;

    const summary = document.createElement('div');
    summary.className = 'mt-4 flex items-center justify-between p-3 rounded-md';

    if (successCount === totalCount) {
        summary.className += ' bg-green-50 text-green-600 border border-green-200';
        summary.innerHTML = `
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                <span><strong>Todos os ${totalCount} gráficos renderizados com sucesso!</strong></span>
            </div>
            <span class="text-xs">${new Date().toLocaleTimeString()}</span>
        `;
    } else {
        summary.className += ' bg-yellow-50 text-yellow-600 border border-yellow-200';
        summary.innerHTML = `
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                <span><strong>${successCount} de ${totalCount} gráficos renderizados.</strong> Verifique o console.</span>
            </div>
            <span class="text-xs">${new Date().toLocaleTimeString()}</span>
        `;
    }

    container.appendChild(summary);

    return { success: successCount === totalCount, results };
}

// Exportar funções para uso global
window.ChartTest = {
    testChartRendering,
    testAllChartTypes,
    testChartData
};

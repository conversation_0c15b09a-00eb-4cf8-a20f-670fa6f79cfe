/**
 * Amigo DataHub - dashboard-charts.js
 * Script para inicializar os gráficos da página inicial
 */

document.addEventListener('DOMContentLoaded', function() {
    // Inicializar gráficos
    initializeCharts();
});

function initializeCharts() {
    // Gráfico de Leads por Mês
    if (document.getElementById('leadsChart')) {
        const leadsCtx = document.getElementById('leadsChart').getContext('2d');
        new Chart(leadsCtx, {
            type: 'line',
            data: {
                labels: Object.keys(window.pageData?.leads_por_mes || {}),
                datasets: [{
                    label: 'Leads',
                    data: Object.values(window.pageData?.leads_por_mes || {}),
                    backgroundColor: 'rgba(0, 122, 255, 0.1)',
                    borderColor: 'rgba(0, 122, 255, 1)',
                    borderWidth: 2,
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            display: false
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    // Gráfico de Funil de Vendas
    if (document.getElementById('funilChart')) {
        const funilCtx = document.getElementById('funilChart').getContext('2d');
        const etapas = window.pageData?.etapas_funil || [];

        new Chart(funilCtx, {
            type: 'bar',
            data: {
                labels: etapas.map(etapa => etapa.etapa),
                datasets: [{
                    label: 'Leads',
                    data: etapas.map(etapa => etapa.quantidade),
                    backgroundColor: [
                        'rgba(0, 122, 255, 0.9)',
                        'rgba(0, 122, 255, 0.8)',
                        'rgba(0, 122, 255, 0.7)',
                        'rgba(0, 122, 255, 0.6)',
                        'rgba(0, 122, 255, 0.5)',
                        'rgba(0, 122, 255, 0.4)'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: 'y',
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        beginAtZero: true,
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    // Gráfico de Áreas de Formação
    if (document.getElementById('areasChart')) {
        const areasCtx = document.getElementById('areasChart').getContext('2d');
        const areas = window.pageData?.distribuicao_areas || [];

        new Chart(areasCtx, {
            type: 'pie',
            data: {
                labels: areas.map(area => area.area),
                datasets: [{
                    label: 'Quantidade',
                    data: areas.map(area => area.quantidade),
                    backgroundColor: [
                        'rgba(0, 122, 255, 0.9)',
                        'rgba(0, 122, 255, 0.8)',
                        'rgba(0, 122, 255, 0.7)',
                        'rgba(0, 122, 255, 0.6)',
                        'rgba(0, 122, 255, 0.5)',
                        'rgba(0, 122, 255, 0.4)',
                        'rgba(0, 122, 255, 0.3)',
                        'rgba(120, 120, 120, 0.5)'
                    ],
                    borderWidth: 1,
                    borderColor: 'rgba(255, 255, 255, 0.8)'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            color: '#333'
                        }
                    }
                }
            }
        });
    }

    // Gráfico de Origens
    if (document.getElementById('origensChart')) {
        const origensCtx = document.getElementById('origensChart').getContext('2d');
        const origens = window.pageData?.distribuicao_origens || [];

        new Chart(origensCtx, {
            type: 'pie',
            data: {
                labels: origens.map(origem => origem.origem),
                datasets: [{
                    label: 'Quantidade',
                    data: origens.map(origem => origem.quantidade),
                    backgroundColor: [
                        'rgba(0, 122, 255, 0.9)',
                        'rgba(0, 122, 255, 0.8)',
                        'rgba(0, 122, 255, 0.7)',
                        'rgba(0, 122, 255, 0.6)',
                        'rgba(0, 122, 255, 0.5)',
                        'rgba(120, 120, 120, 0.5)',
                        'rgba(180, 180, 180, 0.5)',
                        'rgba(220, 220, 220, 0.5)'
                    ],
                    borderWidth: 1,
                    borderColor: 'rgba(255, 255, 255, 0.8)'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            color: '#333'
                        }
                    }
                }
            }
        });
    }
}

/**
 * Dashboard Customizer - Sistema de personalização de KPIs
 */

class DashboardCustomizer {
    constructor() {
        this.config = null;
        this.availableKpis = null;
        this.isCustomizing = false;
        this.init();
    }

    async init() {
        await this.loadConfig();
        await this.loadAvailableKpis();
        this.setupEventListeners();
        this.setupFixedKpisAnalytics();
        this.renderDashboard();
    }

    async loadConfig() {
        try {
            const response = await fetch('/api/dashboard/config');
            this.config = await response.json();
        } catch (error) {
            console.error('Erro ao carregar configuração:', error);
            this.config = this.getDefaultConfig();
        }
    }

    async loadAvailableKpis() {
        try {
            const response = await fetch('/api/dashboard/kpis/available');
            this.availableKpis = await response.json();
        } catch (error) {
            console.error('Erro ao carregar KPIs disponíveis:', error);
        }
    }

    getDefaultConfig() {
        return {
            fixed_kpis: ["vendas_total", "producao_total", "agendamentos_mes_corrente", "atendimentos_mes_corrente"],
            selected_optional_kpis: [
                "perc_conversao_leads",
                "nps_periodo_anterior",
                "ticket_medio_periodo",
                "perc_faltas_cancelamentos",
                "base_pacientes_total",
                "novos_cadastros_periodo",
                "perc_orcamentos_fechados",
                "creditos_in_house"
            ],
            layout: { grid_columns: 4, compact_mode: false },
            preferences: { auto_refresh: 300, show_trends: true }
        };
    }

    setupEventListeners() {
        // Botão de personalizar dashboard
        const customizeBtn = document.getElementById('customize-dashboard-btn');
        if (customizeBtn) {
            customizeBtn.addEventListener('click', () => this.toggleCustomizeMode());
        }

        // Escape para sair do modo de personalização
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isCustomizing) {
                this.exitCustomizeMode();
            }
        });
    }

    setupFixedKpisAnalytics() {
        // Configurar event listeners para os KPIs fixos
        const fixedKpiCards = document.querySelectorAll('.kpi-card[data-kpi]');
        fixedKpiCards.forEach(card => {
            const analyticsBtn = card.querySelector('.analytics-btn');
            if (analyticsBtn) {
                const kpiKey = card.getAttribute('data-kpi');
                analyticsBtn.addEventListener('click', (e) => {
                    e.stopPropagation();

                    // Buscar dados do KPI nos availableKpis
                    let kpi = null;
                    if (this.availableKpis) {
                        kpi = this.availableKpis.fixed_kpis[kpiKey] || this.availableKpis.optional_kpis[kpiKey];
                    }

                    if (!kpi) {
                        // Criar dados básicos se não encontrar
                        kpi = {
                            name: kpiKey.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
                            format: kpiKey.includes('total') ? 'currency' : 'number',
                            value: this.getKpiValueFromDOM(card)
                        };
                    }

                    this.toggleAnalytics(card, kpiKey, kpi);
                });
            }
        });
    }

    getKpiValueFromDOM(card) {
        // Extrair valor do KPI do DOM
        const valueElement = card.querySelector('.text-2xl, .text-lg');
        if (valueElement) {
            const text = valueElement.textContent.trim();
            // Remover formatação e converter para número
            const numericValue = text.replace(/[^\d,.-]/g, '').replace(',', '.');
            return parseFloat(numericValue) || 0;
        }
        return 0;
    }

    toggleCustomizeMode() {
        if (this.isCustomizing) {
            this.exitCustomizeMode();
        } else {
            this.enterCustomizeMode();
        }
    }

    enterCustomizeMode() {
        this.isCustomizing = true;
        document.body.classList.add('customizing-dashboard');
        
        // Criar overlay de personalização
        this.createCustomizeOverlay();
        
        // Atualizar botão
        const btn = document.getElementById('customize-dashboard-btn');
        btn.innerHTML = `
            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
            Sair da Personalização
        `;
        btn.classList.remove('text-blue-600', 'bg-blue-50', 'hover:bg-blue-100');
        btn.classList.add('text-red-600', 'bg-red-50', 'hover:bg-red-100');
    }

    exitCustomizeMode() {
        this.isCustomizing = false;
        document.body.classList.remove('customizing-dashboard');
        
        // Remover overlay
        const overlay = document.getElementById('customize-overlay');
        if (overlay) {
            overlay.remove();
        }
        
        // Restaurar botão
        const btn = document.getElementById('customize-dashboard-btn');
        btn.innerHTML = `
            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="1.5">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            Personalizar Dashboard
        `;
        btn.classList.remove('text-red-600', 'bg-red-50', 'hover:bg-red-100');
        btn.classList.add('text-blue-600', 'bg-blue-50', 'hover:bg-blue-100');
    }

    createCustomizeOverlay() {
        const overlay = document.createElement('div');
        overlay.id = 'customize-overlay';
        overlay.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center';
        
        overlay.innerHTML = `
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h2 class="text-xl font-semibold text-gray-900">Personalizar Dashboard</h2>
                        <button id="close-customize" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>
                
                <div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
                    <div class="text-center mb-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">Personalize seu Dashboard</h3>
                        <p class="text-gray-600">Selecione até 12 métricas adicionais para acompanhar</p>
                    </div>

                    <!-- KPIs Opcionais -->
                    <div>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4" id="optional-kpis-list">
                            <!-- Será preenchido dinamicamente -->
                        </div>
                    </div>
                </div>
                
                <div class="p-6 border-t border-gray-200 flex justify-end space-x-3">
                    <button id="cancel-customize" class="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors">
                        Cancelar
                    </button>
                    <button id="save-customize" class="px-4 py-2 text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors">
                        Salvar Configuração
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(overlay);
        
        // Event listeners do overlay
        document.getElementById('close-customize').addEventListener('click', () => this.exitCustomizeMode());
        document.getElementById('cancel-customize').addEventListener('click', () => this.exitCustomizeMode());
        document.getElementById('save-customize').addEventListener('click', () => this.saveConfiguration());
        
        // Preencher dados
        this.populateCustomizeOverlay();
    }

    populateCustomizeOverlay() {
        if (!this.availableKpis) return;
        
        // Apenas KPIs Opcionais
        const optionalList = document.getElementById('optional-kpis-list');
        optionalList.innerHTML = '';
        
        Object.entries(this.availableKpis.optional_kpis).forEach(([key, kpi]) => {
            const isSelected = this.config.selected_optional_kpis.includes(key);
            const item = this.createKpiItem(key, kpi, false, isSelected);
            optionalList.appendChild(item);
        });
    }

    createKpiItem(key, kpi, isFixed, isSelected = false) {
        const div = document.createElement('div');

        if (isFixed) {
            div.className = 'p-4 border-2 border-gray-300 rounded-lg bg-gray-50 cursor-not-allowed opacity-75';
        } else {
            div.className = `p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
                isSelected
                    ? 'border-blue-500 bg-blue-50 shadow-md'
                    : 'border-gray-200 bg-white hover:border-blue-300 hover:shadow-sm'
            }`;
        }

        const iconSvg = this.getIconSvg(kpi.icon);

        div.innerHTML = `
            <div class="text-center">
                <div class="flex justify-center mb-3">
                    <div class="w-10 h-10 flex items-center justify-center text-blue-600 bg-blue-100 rounded-lg">
                        ${iconSvg}
                    </div>
                </div>
                <h4 class="text-sm font-semibold text-gray-900 mb-2">${kpi.name}</h4>
                <div class="text-xl font-bold text-gray-900 mb-2">${kpi.value || 'N/A'}</div>
                <p class="text-xs text-gray-500 mb-3">${kpi.description}</p>
                ${!isFixed ? `
                    <div class="flex items-center justify-center">
                        <div class="w-5 h-5 rounded border-2 ${
                            isSelected
                                ? 'bg-blue-500 border-blue-500'
                                : 'border-gray-300'
                        } flex items-center justify-center">
                            ${isSelected ? '<svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>' : ''}
                        </div>
                        <span class="ml-2 text-xs font-medium ${isSelected ? 'text-blue-600' : 'text-gray-500'}">
                            ${isSelected ? 'Selecionado' : 'Clique para selecionar'}
                        </span>
                    </div>
                ` : `
                    <div class="flex items-center justify-center">
                        <span class="text-xs text-gray-500 bg-gray-200 px-3 py-1 rounded-full">KPI Fixo</span>
                    </div>
                `}
            </div>
            ${!isFixed ? `<input type="checkbox" ${isSelected ? 'checked' : ''} data-kpi="${key}" class="kpi-checkbox hidden">` : ''}
        `;

        // Adicionar evento de clique para KPIs opcionais
        if (!isFixed) {
            div.addEventListener('click', () => {
                const checkbox = div.querySelector('.kpi-checkbox');
                checkbox.checked = !checkbox.checked;

                // Atualizar visual do card
                this.updateKpiCardVisual(div, checkbox.checked);
            });
        }

        return div;
    }

    updateKpiCardVisual(cardElement, isSelected) {
        const checkboxContainer = cardElement.querySelector('.w-5.h-5.rounded.border-2');
        const statusText = cardElement.querySelector('span.ml-2');

        if (isSelected) {
            cardElement.className = 'p-4 border-2 border-blue-500 bg-blue-50 shadow-md rounded-lg cursor-pointer transition-all duration-200';
            checkboxContainer.className = 'w-5 h-5 rounded border-2 bg-blue-500 border-blue-500 flex items-center justify-center';
            checkboxContainer.innerHTML = '<svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>';
            statusText.textContent = 'Selecionado';
            statusText.className = 'ml-2 text-xs font-medium text-blue-600';
        } else {
            cardElement.className = 'p-4 border-2 border-gray-200 bg-white hover:border-blue-300 hover:shadow-sm rounded-lg cursor-pointer transition-all duration-200';
            checkboxContainer.className = 'w-5 h-5 rounded border-2 border-gray-300 flex items-center justify-center';
            checkboxContainer.innerHTML = '';
            statusText.textContent = 'Clique para selecionar';
            statusText.className = 'ml-2 text-xs font-medium text-gray-500';
        }
    }

    getColorClass(color) {
        // Padronizar todas as cores para tons de azul
        return 'bg-blue-100 text-blue-600';
    }

    getIconSvg(iconName) {
        const icons = {
            // KPIs de Pessoas
            'users': '<svg class="w-4 h-4 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path></svg>',
            'user-plus': '<svg class="w-4 h-4 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path></svg>',
            'user-minus': '<svg class="w-4 h-4 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7a4 4 0 11-8 0 4 4 0 018 0zM9 14a6 6 0 00-6 6v1h12v-1a6 6 0 00-6-6zM21 12h-6"></path></svg>',
            'user-circle': '<svg class="w-4 h-4 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>',

            // KPIs Financeiros
            'banknotes': '<svg class="w-4 h-4 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v12m-3-2.818l.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.268-.268-1.268-.732 0-.464.543-.732 1.268-.732.725 0 1.268.268 1.268.732"></path></svg>',
            'credit-card': '<svg class="w-4 h-4 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path></svg>',

            // KPIs de Agendamento
            'calendar-plus': '<svg class="w-4 h-4 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>',
            'x-circle': '<svg class="w-4 h-4 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>',
            'arrow-path': '<svg class="w-4 h-4 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99"></path></svg>',

            // KPIs de Performance
            'funnel': '<svg class="w-4 h-4 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path></svg>',
            'document-check': '<svg class="w-4 h-4 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path></svg>',
            'sparkles': '<svg class="w-4 h-4 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z"></path></svg>',
            'star': '<svg class="w-4 h-4 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path></svg>',

            // KPIs de Rankings
            'chart-bar': '<svg class="w-4 h-4 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z"></path></svg>',
            'trophy': '<svg class="w-4 h-4 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path></svg>',
            'clipboard-document-list': '<svg class="w-4 h-4 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path></svg>'
        };
        return icons[iconName] || icons['chart-bar'];
    }

    async saveConfiguration() {
        // Coletar KPIs selecionados
        const checkboxes = document.querySelectorAll('.kpi-checkbox:checked');
        const selectedKpis = Array.from(checkboxes).map(cb => cb.dataset.kpi);

        // Permitir até 12 KPIs opcionais (3 fileiras de 4)
        if (selectedKpis.length > 12) {
            this.showNotification('Você pode selecionar no máximo 12 KPIs opcionais.', 'error');
            return;
        }

        // Atualizar configuração
        this.config.selected_optional_kpis = selectedKpis;

        // Manter configurações de layout padrão já que removemos os controles
        this.config.layout.grid_columns = 4;
        this.config.layout.compact_mode = false;

        try {
            // Mostrar loading no botão
            const saveBtn = document.getElementById('save-customize');
            const originalText = saveBtn.textContent;
            saveBtn.textContent = 'Salvando...';
            saveBtn.disabled = true;

            const response = await fetch('/api/dashboard/config', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(this.config)
            });

            if (response.ok) {
                this.exitCustomizeMode();
                await this.renderDashboard();
                this.showNotification('Configuração salva com sucesso!', 'success');
            } else {
                throw new Error('Erro ao salvar configuração');
            }
        } catch (error) {
            console.error('Erro ao salvar:', error);
            this.showNotification('Erro ao salvar configuração', 'error');

            // Restaurar botão em caso de erro
            const saveBtn = document.getElementById('save-customize');
            if (saveBtn) {
                saveBtn.textContent = 'Salvar Configuração';
                saveBtn.disabled = false;
            }
        }
    }

    async renderDashboard() {
        // Esta função re-renderiza os KPIs opcionais dinamicamente
        if (this.isCustomizing) return;

        console.log('Atualizando dashboard com configuração:', this.config);

        // Encontrar o container dos KPIs opcionais
        const optionalKpisGrid = document.getElementById('optional-kpis-grid');
        if (!optionalKpisGrid) {
            console.warn('Container de KPIs opcionais não encontrado');
            return;
        }

        // Limpar KPIs atuais
        optionalKpisGrid.innerHTML = '';

        // Renderizar KPIs selecionados
        if (this.config.selected_optional_kpis && this.config.selected_optional_kpis.length > 0) {
            for (const kpiKey of this.config.selected_optional_kpis) {
                if (this.availableKpis && this.availableKpis.optional_kpis[kpiKey]) {
                    const kpi = this.availableKpis.optional_kpis[kpiKey];
                    const kpiCard = this.createKpiCard(kpiKey, kpi);
                    optionalKpisGrid.appendChild(kpiCard);
                }
            }
        } else {
            // Mostrar mensagem quando não há KPIs selecionados
            optionalKpisGrid.innerHTML = `
                <div class="col-span-full text-center py-8 text-gray-500">
                    <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <p class="text-sm">Nenhum KPI adicional selecionado</p>
                    <p class="text-xs mt-1">Clique em "Personalizar Dashboard" para adicionar métricas</p>
                </div>
            `;
        }
    }

    createKpiCard(kpiKey, kpi) {
        const card = document.createElement('div');
        card.className = 'bg-white rounded-lg p-4 border border-gray-200 hover:shadow-md transition-shadow duration-300';
        card.setAttribute('data-kpi', kpiKey);

        const iconSvg = this.getIconSvg(kpi.icon);

        // Formatar valor baseado no tipo
        let formattedValue = kpi.value;

        // Verificar se o valor é válido
        if (kpi.value === null || kpi.value === undefined) {
            formattedValue = 'N/A';
        } else if (kpi.format === 'currency') {
            const numValue = typeof kpi.value === 'number' ? kpi.value : parseFloat(kpi.value) || 0;
            formattedValue = `R$ ${numValue.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`;
        } else if (kpi.format === 'percentage') {
            const numValue = typeof kpi.value === 'number' ? kpi.value : parseFloat(kpi.value) || 0;
            formattedValue = `${numValue.toFixed(1)}%`;
        } else if (kpi.format === 'decimal') {
            const numValue = typeof kpi.value === 'number' ? kpi.value : parseFloat(kpi.value) || 0;
            formattedValue = numValue.toFixed(1);
        } else if (kpi.format === 'number') {
            const numValue = typeof kpi.value === 'number' ? kpi.value : parseInt(kpi.value) || 0;
            formattedValue = numValue.toLocaleString('pt-BR');
        } else if (kpi.format === 'list' && Array.isArray(kpi.value)) {
            // Para listas (top 3), mostrar apenas o primeiro item
            if (kpi.value.length > 0) {
                const firstItem = kpi.value[0];
                if (firstItem.percentual) {
                    formattedValue = `${firstItem.nome}: ${firstItem.percentual}%`;
                } else if (firstItem.quantidade) {
                    formattedValue = `${firstItem.nome}: ${firstItem.quantidade}`;
                } else if (firstItem.valor) {
                    const valor = typeof firstItem.valor === 'number' ? firstItem.valor : parseFloat(firstItem.valor) || 0;
                    formattedValue = `${firstItem.nome}: R$ ${valor.toLocaleString('pt-BR')}`;
                } else {
                    formattedValue = firstItem.nome || 'Sem dados';
                }
            } else {
                formattedValue = 'Sem dados';
            }
        } else {
            // Fallback para outros tipos
            formattedValue = kpi.value.toString();
        }

        // Gerar tendência simulada
        const trendDirection = Math.random() > 0.5 ? 'up' : 'down';
        const trendValue = (Math.random() * 10 + 1).toFixed(1);
        const trendColor = trendDirection === 'up' ? 'text-green-600' : 'text-red-600';
        const trendIcon = trendDirection === 'up' ?
            '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>' :
            '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>';

        card.innerHTML = `
            <div class="flex items-center justify-between mb-3">
                <div class="flex items-center">
                    ${iconSvg}
                    <div>
                        <p class="text-xs font-medium text-gray-600">${kpi.name}</p>
                    </div>
                </div>
                <button class="analytics-btn text-blue-500 hover:text-blue-700 hover:bg-blue-50 p-1 rounded transition-colors duration-200"
                        data-kpi="${kpiKey}"
                        title="Ver Analítico">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </button>
            </div>
            <div>
                <p class="text-lg font-bold text-gray-900">${formattedValue}</p>
                <div class="flex items-center mt-1">
                    <svg class="w-3 h-3 ${trendColor} mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        ${trendIcon}
                    </svg>
                    <span class="text-xs ${trendColor}">${trendDirection === 'up' ? '+' : '-'}${trendValue}% vs mês anterior</span>
                </div>
            </div>

            <!-- Seção Analítica (inicialmente oculta) -->
            <div class="analytics-section hidden mt-4 pt-4 border-t border-gray-200">
                <div class="space-y-2">
                    <div class="text-xs font-medium text-gray-700 mb-2">Detalhamento:</div>
                    <div class="analytics-content">
                        <!-- Conteúdo será carregado dinamicamente -->
                    </div>
                </div>
            </div>
        `;

        // Adicionar event listener para o botão de analítico
        const analyticsBtn = card.querySelector('.analytics-btn');
        analyticsBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleAnalytics(card, kpiKey, kpi);
        });

        return card;
    }

    toggleAnalytics(card, kpiKey, kpi) {
        const analyticsSection = card.querySelector('.analytics-section');
        const analyticsContent = card.querySelector('.analytics-content');
        const analyticsBtn = card.querySelector('.analytics-btn');

        if (analyticsSection.classList.contains('hidden')) {
            // Expandir - mostrar analítico
            analyticsSection.classList.remove('hidden');
            analyticsBtn.innerHTML = `
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                </svg>
            `;
            analyticsBtn.title = "Ocultar Analítico";

            // Carregar dados analíticos
            this.loadAnalyticsData(analyticsContent, kpiKey, kpi);

        } else {
            // Recolher - ocultar analítico
            analyticsSection.classList.add('hidden');
            analyticsBtn.innerHTML = `
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
            `;
            analyticsBtn.title = "Ver Analítico";
        }
    }

    loadAnalyticsData(container, kpiKey, kpi) {
        // Mostrar loading
        container.innerHTML = `
            <div class="flex items-center justify-center py-4">
                <svg class="w-4 h-4 animate-spin text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                <span class="text-xs text-gray-500">Carregando dados...</span>
            </div>
        `;

        // Simular dados analíticos baseados no tipo de KPI
        setTimeout(() => {
            const analyticsData = this.generateAnalyticsData(kpiKey, kpi);
            container.innerHTML = this.renderAnalyticsData(analyticsData, kpi);
        }, 500);
    }

    generateAnalyticsData(kpiKey, kpi) {
        // Gerar dados analíticos simulados baseados no tipo de KPI
        const data = {
            periods: [],
            breakdown: [],
            insights: []
        };

        // Dados dos últimos 6 meses
        const months = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun'];
        for (let i = 0; i < 6; i++) {
            const baseValue = kpi.value;
            const variation = (Math.random() - 0.5) * 0.3; // ±15% de variação
            const value = baseValue * (1 + variation);
            const delta = i > 0 ? ((value - data.periods[i-1].value) / data.periods[i-1].value * 100) : 0;

            data.periods.push({
                period: months[i],
                value: value,
                delta: delta,
                formatted: this.formatValue(value, kpi.format)
            });
        }

        // Breakdown por categoria (se aplicável)
        if (kpi.format === 'list' && Array.isArray(kpi.value)) {
            data.breakdown = kpi.value.slice(0, 5); // Top 5
        } else {
            // Gerar breakdown simulado
            const categories = ['Categoria A', 'Categoria B', 'Categoria C', 'Categoria D'];
            for (let i = 0; i < 4; i++) {
                const percentage = Math.random() * 30 + 10; // 10-40%
                data.breakdown.push({
                    name: categories[i],
                    value: kpi.value * (percentage / 100),
                    percentage: percentage.toFixed(1),
                    formatted: this.formatValue(kpi.value * (percentage / 100), kpi.format)
                });
            }
        }

        // Insights automáticos
        const lastPeriod = data.periods[data.periods.length - 1];
        const previousPeriod = data.periods[data.periods.length - 2];

        if (lastPeriod.delta > 5) {
            data.insights.push({
                type: 'positive',
                text: `Crescimento de ${lastPeriod.delta.toFixed(1)}% em relação ao mês anterior`
            });
        } else if (lastPeriod.delta < -5) {
            data.insights.push({
                type: 'negative',
                text: `Queda de ${Math.abs(lastPeriod.delta).toFixed(1)}% em relação ao mês anterior`
            });
        } else {
            data.insights.push({
                type: 'neutral',
                text: `Variação estável de ${lastPeriod.delta.toFixed(1)}% em relação ao mês anterior`
            });
        }

        return data;
    }

    formatValue(value, format) {
        if (format === 'currency') {
            return `R$ ${value.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`;
        } else if (format === 'percentage') {
            return `${value.toFixed(1)}%`;
        } else if (format === 'decimal') {
            return value.toFixed(1);
        } else if (format === 'number') {
            return value.toLocaleString('pt-BR');
        }
        return value.toString();
    }

    renderAnalyticsData(data, kpi) {
        let html = '';

        // Evolução temporal em formato de lista limpa
        html += `
            <div class="mb-3">
                <div class="text-xs font-semibold text-gray-700 mb-2 flex items-center">
                    <svg class="w-3 h-3 text-blue-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    Evolução (6 meses)
                </div>
                <div class="bg-gray-50 rounded-md p-2 space-y-1">
        `;

        data.periods.forEach((period, index) => {
            const deltaColor = period.delta > 0 ? 'text-green-600' : period.delta < 0 ? 'text-red-600' : 'text-gray-600';
            const deltaIcon = period.delta > 0 ? '↗' : period.delta < 0 ? '↘' : '→';

            html += `
                <div class="flex justify-between items-center text-xs py-1 px-2 bg-white rounded border border-gray-100">
                    <span class="font-medium text-gray-700">${period.period}</span>
                    <div class="flex items-center space-x-2">
                        <span class="font-semibold text-gray-900">${period.formatted}</span>
                        ${index > 0 ? `<span class="${deltaColor} text-xs font-medium">${deltaIcon} ${Math.abs(period.delta).toFixed(1)}%</span>` : ''}
                    </div>
                </div>
            `;
        });

        html += `
                </div>
            </div>
        `;

        // Breakdown por categoria em formato melhorado
        if (data.breakdown.length > 0) {
            html += `
                <div class="mb-3">
                    <div class="text-xs font-semibold text-gray-700 mb-2 flex items-center">
                        <svg class="w-3 h-3 text-blue-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                        </svg>
                        Detalhamento
                    </div>
                    <div class="bg-gray-50 rounded-md p-2 space-y-1">
            `;

            data.breakdown.forEach((item, index) => {
                const isTop = index === 0;
                const borderColor = isTop ? 'border-blue-200' : 'border-gray-100';
                const bgColor = isTop ? 'bg-blue-50' : 'bg-white';

                html += `
                    <div class="flex justify-between items-center text-xs py-1 px-2 ${bgColor} rounded border ${borderColor}">
                        <span class="font-medium text-gray-700">${item.name}</span>
                        <div class="flex items-center space-x-1">
                            <span class="font-semibold text-gray-900">${item.formatted || item.percentual + '%'}</span>
                            ${item.percentage ? `<span class="text-gray-500 text-xs">(${item.percentage}%)</span>` : ''}
                        </div>
                    </div>
                `;
            });

            html += `
                    </div>
                </div>
            `;
        }

        // Insights em formato de cards pequenos
        if (data.insights.length > 0) {
            html += `
                <div>
                    <div class="text-xs font-semibold text-gray-700 mb-2 flex items-center">
                        <svg class="w-3 h-3 text-blue-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Insights
                    </div>
                    <div class="space-y-1">
            `;

            data.insights.forEach(insight => {
                const bgColor = insight.type === 'positive' ? 'bg-green-50 border-green-200' :
                               insight.type === 'negative' ? 'bg-red-50 border-red-200' : 'bg-blue-50 border-blue-200';
                const iconColor = insight.type === 'positive' ? 'text-green-600' :
                                insight.type === 'negative' ? 'text-red-600' : 'text-blue-600';
                const icon = insight.type === 'positive' ? '✓' :
                           insight.type === 'negative' ? '⚠' : 'ℹ';

                html += `
                    <div class="flex items-start text-xs p-2 ${bgColor} rounded border">
                        <span class="${iconColor} mr-2 font-bold">${icon}</span>
                        <span class="text-gray-700 leading-relaxed">${insight.text}</span>
                    </div>
                `;
            });

            html += `
                    </div>
                </div>
            `;
        }

        return html;
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg ${
            type === 'success' ? 'bg-green-100 text-green-800 border border-green-200' :
            type === 'error' ? 'bg-red-100 text-red-800 border border-red-200' :
            'bg-blue-100 text-blue-800 border border-blue-200'
        }`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

// Inicializar quando o DOM estiver pronto
document.addEventListener('DOMContentLoaded', () => {
    window.dashboardCustomizer = new DashboardCustomizer();
});

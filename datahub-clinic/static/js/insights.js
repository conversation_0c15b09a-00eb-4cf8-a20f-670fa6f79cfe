/**
 * Detecta o contexto da página atual.
 *
 * @returns {Object} - Objeto com informações do contexto da página
 */
function detectPageContext() {
    const context = {
        page_title: document.title,
        page_elements: [],
        key_metrics: {}
    };

    // Detectar elementos da página
    const headings = Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6'));
    if (headings.length > 0) {
        context.page_elements = headings.map(h => h.textContent.trim()).filter(Boolean);
    }

    // Detectar descrição da página
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
        context.page_description = metaDescription.getAttribute('content');
    } else {
        // Tentar encontrar um parágrafo que possa servir como descrição
        const firstParagraph = document.querySelector('p');
        if (firstParagraph) {
            context.page_description = firstParagraph.textContent.trim();
        }
    }

    // Detectar métricas principais (KPIs)
    const kpiElements = Array.from(document.querySelectorAll('.kpi-card, [data-kpi], .metric-card'));
    kpiElements.forEach(kpi => {
        const kpiTitle = kpi.querySelector('.kpi-title, .metric-title, h3, h4')?.textContent.trim();
        const kpiValue = kpi.querySelector('.kpi-value, .metric-value, .value, strong')?.textContent.trim();

        if (kpiTitle && kpiValue) {
            context.key_metrics[kpiTitle] = kpiValue;
        }
    });

    // Detectar foco da análise
    const breadcrumbs = Array.from(document.querySelectorAll('.breadcrumb, .breadcrumbs, nav ol, nav ul'));
    if (breadcrumbs.length > 0) {
        const breadcrumbItems = Array.from(breadcrumbs[0].querySelectorAll('li, a')).map(item => item.textContent.trim());
        if (breadcrumbItems.length > 0) {
            context.analysis_focus = breadcrumbItems.join(' > ');
        }
    }

    // Detectar dados de tabelas
    const tables = Array.from(document.querySelectorAll('table'));
    if (tables.length > 0) {
        context.table_headers = [];
        const firstTable = tables[0];
        const headers = Array.from(firstTable.querySelectorAll('th')).map(th => th.textContent.trim());
        if (headers.length > 0) {
            context.table_headers = headers;
        }
    }

    // Detectar gráficos
    const charts = Array.from(document.querySelectorAll('canvas[id*="Chart"], [data-chart]'));
    if (charts.length > 0) {
        context.charts = charts.map(chart => chart.id || 'chart').filter(Boolean);
    }

    return context;
}

/**
 * Carrega insights dinamicamente via AJAX.
 *
 * @param {string} moduleName - Nome do módulo
 * @param {string} reportName - Nome do relatório
 * @param {string} containerId - ID do container onde os insights serão exibidos
 * @param {number} numInsights - Número de insights a serem carregados
 * @param {Array} insightTypes - Tipos de insights a serem carregados
 * @param {Object} customContext - Contexto personalizado (opcional)
 */
function loadInsights(moduleName, reportName, containerId, numInsights = 3, insightTypes = ['list', 'text', 'stat'], customContext = null) {
    const container = document.getElementById(containerId);

    if (!container) {
        console.error(`Container com ID ${containerId} não encontrado.`);
        return;
    }

    // Exibir indicador de carregamento
    container.innerHTML = `
        <div class="flex justify-center items-center p-10">
            <svg class="animate-spin h-8 w-8 text-systemBlue" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
        </div>
    `;

    // Detectar contexto da página ou usar contexto personalizado
    const pageContext = customContext || detectPageContext();

    // Adicionar informações específicas do módulo e relatório
    pageContext.module = moduleName;
    pageContext.report = reportName;

    // Fazer requisição AJAX com o contexto da página
    fetch(`/api/insights/${moduleName}/${reportName}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            page_context: pageContext,
            num_insights: numInsights,
            types: insightTypes
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Erro ao carregar insights');
        }
        return response.json();
    })
    .then(insights => {
        // Preservar a classe original do container
        const originalClass = container.className;

        // Limpar container
        container.innerHTML = '';

        // Restaurar a classe original
        container.className = originalClass;

        // Adicionar cada insight diretamente ao container
        insights.forEach(insight => {
            const card = createInsightCard(insight);
            container.appendChild(card);
        });
    })
    .catch(error => {
        console.error('Erro ao carregar insights:', error);
        container.innerHTML = `
            <div class="bg-red-50 p-4 rounded-md">
                <p class="text-red-500 text-sm">Erro ao carregar insights. Tente novamente mais tarde.</p>
            </div>
        `;
    });
}

/**
 * Cria um card de insight.
 *
 * @param {Object} insight - Dados do insight
 * @returns {HTMLElement} - Elemento HTML do card
 */
function createInsightCard(insight) {
    const card = document.createElement('div');
    card.className = 'bg-white rounded-view p-5 border border-gray-200 flex flex-col hover:shadow-sm transition-shadow duration-200 h-64 w-full';

    // Cabeçalho do card
    const header = document.createElement('div');
    header.className = 'flex items-center mb-1';
    header.innerHTML = `
        <svg class="w-4 h-4 text-systemBlue mr-2 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z"></path>
        </svg>
        <span class="text-base font-semibold text-gray-800 truncate">${insight.title}</span>
    `;
    card.appendChild(header);

    // Descrição
    const description = document.createElement('p');
    description.className = 'text-xs text-label-secondary mb-2 line-clamp-1';
    description.textContent = insight.description;
    card.appendChild(description);

    // Conteúdo do card
    const contentContainer = document.createElement('div');
    contentContainer.className = 'flex-grow overflow-y-auto';

    // Conteúdo baseado no tipo de insight
    if (insight.insight_type === 'text') {
        contentContainer.innerHTML = `<p class="text-xs text-label-secondary">${insight.content}</p>`;
    } else if (insight.insight_type === 'list') {
        contentContainer.innerHTML = `
            <ul class="text-xs text-label-secondary space-y-1">
                ${insight.content.map(item => `
                    <li class="flex items-start">
                        <span class="text-systemBlue mr-1 flex-shrink-0">•</span>
                        <span>${item}</span>
                    </li>
                `).join('')}
            </ul>
        `;
    } else if (insight.insight_type === 'stat') {
        const trendClass = insight.content.trend > 0 ? 'text-systemGreen' : (insight.content.trend < 0 ? 'text-red-500' : 'text-label-secondary');
        const trendIcon = insight.content.trend > 0 ? '↑' : (insight.content.trend < 0 ? '↓' : '→');

        contentContainer.innerHTML = `
            <div>
                <div class="flex items-baseline">
                    <span class="text-2xl font-semibold text-label-DEFAULT">${insight.content.value}</span>
                    <span class="ml-2 text-xs ${trendClass}">
                        ${trendIcon} ${insight.content.trend_text}
                    </span>
                </div>
                <p class="text-xs text-label-secondary mt-1">${insight.content.context}</p>
            </div>
        `;
    }

    card.appendChild(contentContainer);

    // Categoria
    if (insight.category) {
        const categoryDiv = document.createElement('div');
        categoryDiv.className = 'flex justify-between items-center mt-2 mb-2';
        categoryDiv.innerHTML = `
            <span class="inline-block bg-blue-100 text-blue-700 text-xs font-medium px-2 py-0.5 rounded-full">${insight.category}</span>
        `;
        card.appendChild(categoryDiv);
    }

    // Rodapé
    const footer = document.createElement('div');
    footer.className = 'flex items-center text-xs pt-2 border-t border-gray-100 mt-auto';
    footer.innerHTML = `
        <div class="flex items-center ml-auto">
            <svg class="w-4 h-4 text-systemBlue mr-1 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
            </svg>
            <span class="text-gray-800 font-medium text-xs">Powered by Amigo Intelligence</span>
        </div>
    `;

    card.appendChild(footer);

    return card;
}

/**
 * Carrega um único insight.
 *
 * @param {string} moduleName - Nome do módulo
 * @param {string} reportName - Nome do relatório
 * @param {string} containerId - ID do container onde o insight será exibido
 * @param {string} insightType - Tipo de insight a ser carregado
 * @param {string} category - Categoria do insight
 * @param {Object} customContext - Contexto personalizado (opcional)
 */
function loadSingleInsight(moduleName, reportName, containerId, insightType = 'list', category = null, customContext = null) {
    const container = document.getElementById(containerId);

    if (!container) {
        console.error(`Container com ID ${containerId} não encontrado.`);
        return;
    }

    // Exibir indicador de carregamento
    container.innerHTML = `
        <div class="flex justify-center items-center p-5">
            <svg class="animate-spin h-6 w-6 text-systemBlue" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
        </div>
    `;

    // Detectar contexto da página ou usar contexto personalizado
    const pageContext = customContext || detectPageContext();

    // Adicionar informações específicas do módulo e relatório
    pageContext.module = moduleName;
    pageContext.report = reportName;

    // Preparar dados para a requisição
    const requestData = {
        page_context: pageContext
    };

    // Fazer requisição AJAX com o contexto da página
    fetch(`/api/insights/${moduleName}/${reportName}/single`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            page_context: pageContext,
            type: insightType,
            category: category
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Erro ao carregar insight');
        }
        return response.json();
    })
    .then(insight => {
        // Preservar a classe original do container
        const originalClass = container.className;

        // Criar card de insight
        const card = createInsightCard(insight);

        // Limpar container e adicionar card
        container.innerHTML = '';

        // Restaurar a classe original
        container.className = originalClass;

        container.appendChild(card);
    })
    .catch(error => {
        console.error('Erro ao carregar insight:', error);
        container.innerHTML = `
            <div class="bg-red-50 p-4 rounded-md">
                <p class="text-red-500 text-sm">Erro ao carregar insight. Tente novamente mais tarde.</p>
            </div>
        `;
    });
}

/**
 * Amigo DataHub - main.js
 * Script principal para funcionalidades comuns do aplicativo
 */

// Função para exibir popup de aviso sobre dados mockados (desativada)
function mostrarAvisoMockData() {
    // Função desativada - não exibe mais o aviso de dados mockados
    return;
}

// Função para adicionar indicador de dados mockados em cada card/tabela (desativada)
function adicionarIndicadoresMockData() {
    // Função desativada - não exibe mais os indicadores de dados mockados
    return;
}

// Executar quando o DOM estiver carregado
document.addEventListener('DOMContentLoaded', function() {
    // Funções de aviso de dados mockados desativadas
    // mostrarAvisoMockData();
    // adicionarIndicadoresMockData();
});

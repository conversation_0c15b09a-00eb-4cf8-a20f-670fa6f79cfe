/**
 * Mini Charts para KPIs do Dashboard
 * Cria pequenos gráficos usando Chart.js para os cards de métricas
 */

class MiniCharts {
    constructor(mainChartData = null) {
        this.charts = {};
        this.mainChartData = mainChartData;
        this.colors = {
            blue: '#0056CC',      // systemBlue escuro
            lightBlue: '#007AFF', // systemBlue
            gray: '#6D6D70',      // systemGray escuro
            lightGray: '#8E8E93', // systemGray
            green: '#34C759'      // systemGreen
        };

        this.init();
    }

    init() {
        // Aguardar o DOM estar carregado
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.createAllCharts());
        } else {
            this.createAllCharts();
        }
    }

    createAllCharts() {
        this.createGrowthChart();
        this.createPeakChart();
        this.createAverageChart();
        this.createTrendChart();
    }

    createGrowthChart() {
        const canvas = document.getElementById('growth-mini-chart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');

        // Usar dados reais do gráfico principal ou dados simulados
        let growthData, labels;
        if (this.mainChartData && this.mainChartData.length > 0) {
            // Pegar últimos 6 meses de crescimento Y/Y
            const lastSixMonths = this.mainChartData.slice(-6);
            growthData = lastSixMonths.map(d => parseFloat(d.yoyGrowth));
            labels = lastSixMonths.map(d => d.month);
        } else {
            // Dados simulados de crescimento mensal Y/Y
            growthData = [8.2, 9.1, 10.5, 11.8, 12.3, 12.9];
            labels = ['Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'];
        }

        this.charts.growth = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    data: growthData,
                    borderColor: this.colors.blue,
                    backgroundColor: this.colors.blue + '20',
                    borderWidth: 1.5,
                    fill: true,
                    tension: 0.2,
                    pointRadius: 0,
                    pointHoverRadius: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false },
                    tooltip: { enabled: false }
                },
                scales: {
                    x: { display: false },
                    y: { display: false }
                },
                elements: {
                    point: { radius: 0 }
                },
                interaction: {
                    intersect: false
                }
            }
        });
    }

    createPeakChart() {
        const canvas = document.getElementById('peak-mini-chart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');

        // Usar dados reais do gráfico principal ou dados simulados
        let salesData, labels, peakIndex;
        if (this.mainChartData && this.mainChartData.length > 0) {
            // Pegar últimos 6 meses de vendas 2024
            const lastSixMonths = this.mainChartData.slice(-6);
            salesData = lastSixMonths.map(d => d.year2024 / 1000); // Converter para K
            labels = lastSixMonths.map(d => d.month);
            // Encontrar o índice do maior valor
            peakIndex = salesData.indexOf(Math.max(...salesData));
        } else {
            // Dados simulados mostrando o pico em outubro
            salesData = [95, 102, 108, 118, 110, 105];
            labels = ['Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'];
            peakIndex = 3;
        }

        this.charts.peak = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    data: salesData,
                    backgroundColor: salesData.map((value, index) =>
                        index === peakIndex ? this.colors.gray : this.colors.lightGray + '60'
                    ),
                    borderColor: this.colors.gray,
                    borderWidth: 0,
                    borderRadius: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false },
                    tooltip: { enabled: false }
                },
                scales: {
                    x: { display: false },
                    y: { display: false }
                },
                interaction: {
                    intersect: false
                }
            }
        });
    }

    createAverageChart() {
        const canvas = document.getElementById('avg-mini-chart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');

        // Dados simulados mostrando média móvel
        const avgData = [98, 100, 101, 102, 103, 102];
        const targetLine = Array(6).fill(102); // Linha da média

        this.charts.average = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'],
                datasets: [
                    {
                        data: avgData,
                        borderColor: this.colors.purple,
                        backgroundColor: this.colors.purple + '20',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4,
                        pointRadius: 0
                    },
                    {
                        data: targetLine,
                        borderColor: this.colors.gray,
                        borderWidth: 1,
                        borderDash: [2, 2],
                        fill: false,
                        pointRadius: 0
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false },
                    tooltip: { enabled: false }
                },
                scales: {
                    x: { display: false },
                    y: { display: false }
                },
                interaction: {
                    intersect: false
                }
            }
        });
    }

    createTrendChart() {
        const canvas = document.getElementById('trend-mini-chart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');

        // Dados simulados mostrando tendência crescente
        const trendData = [85, 92, 98, 105, 112, 118];

        this.charts.trend = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'],
                datasets: [{
                    data: trendData,
                    borderColor: this.colors.green,
                    backgroundColor: this.colors.green + '15',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.1,
                    pointRadius: 0,
                    pointHoverRadius: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false },
                    tooltip: { enabled: false }
                },
                scales: {
                    x: { display: false },
                    y: { display: false }
                },
                elements: {
                    point: { radius: 0 }
                },
                interaction: {
                    intersect: false
                }
            }
        });
    }

    // Método para atualizar os charts com novos dados
    updateCharts(newData) {
        if (newData.growth && this.charts.growth) {
            this.charts.growth.data.datasets[0].data = newData.growth;
            this.charts.growth.update('none');
        }

        if (newData.peak && this.charts.peak) {
            this.charts.peak.data.datasets[0].data = newData.peak;
            this.charts.peak.update('none');
        }

        if (newData.average && this.charts.average) {
            this.charts.average.data.datasets[0].data = newData.average;
            this.charts.average.update('none');
        }

        if (newData.trend && this.charts.trend) {
            this.charts.trend.data.datasets[0].data = newData.trend;
            this.charts.trend.update('none');
        }
    }

    // Método para destruir todos os charts
    destroy() {
        Object.values(this.charts).forEach(chart => {
            if (chart) chart.destroy();
        });
        this.charts = {};
    }
}

// Função para atualizar os deltas (setas verdes/vermelhas) dinamicamente
function updateKPIDeltas() {
    // Simular dados dinâmicos
    const deltas = {
        growth: { value: 2.1, positive: true },
        peak: { value: 'R$ 118K', positive: true },
        average: { value: 8.5, positive: true },
        trend: { value: 'Forte', positive: true }
    };

    // Atualizar delta do crescimento
    const growthDelta = document.querySelector('#yoy-growth').closest('.bg-white').querySelector('.bg-green-50 span:last-child');
    if (growthDelta) {
        growthDelta.textContent = `+${deltas.growth.value}%`;
        growthDelta.className = `text-xs font-medium ${deltas.growth.positive ? 'text-green-600' : 'text-red-600'}`;
    }

    // Atualizar outros deltas conforme necessário
    // ... implementar para outros KPIs
}

// Função para animar os números dos KPIs
function animateKPINumbers() {
    const kpiElements = [
        { id: 'yoy-growth', target: 12.9, suffix: '%', prefix: '+' },
        { id: 'best-month', target: 'Out', isText: true },
        { id: 'avg-2024', target: 102, suffix: 'K', prefix: 'R$ ' },
        { id: 'trend', target: '↗ Crescente', isText: true }
    ];

    kpiElements.forEach(kpi => {
        const element = document.getElementById(kpi.id);
        if (!element || kpi.isText) return;

        const startValue = 0;
        const duration = 2000;
        const startTime = performance.now();

        function updateValue(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // Easing function (ease-out)
            const easeOut = 1 - Math.pow(1 - progress, 3);
            const currentValue = startValue + (kpi.target - startValue) * easeOut;

            const prefix = kpi.prefix || '';
            const suffix = kpi.suffix || '';
            element.textContent = `${prefix}${currentValue.toFixed(1)}${suffix}`;

            if (progress < 1) {
                requestAnimationFrame(updateValue);
            } else {
                element.textContent = `${prefix}${kpi.target}${suffix}`;
            }
        }

        requestAnimationFrame(updateValue);
    });
}

// Função para inicializar mini charts com dados do gráfico principal
function initializeMiniCharts(mainChartData = null) {
    // Aguardar um pouco para garantir que o Chart.js esteja carregado
    setTimeout(() => {
        const miniCharts = new MiniCharts(mainChartData);

        // Animar números após os gráficos serem criados
        setTimeout(() => {
            animateKPINumbers();
            updateKPIDeltas();
        }, 500);

        // Tornar disponível globalmente para debugging
        window.miniCharts = miniCharts;
    }, 100);
}

// Inicializar quando o DOM estiver carregado
document.addEventListener('DOMContentLoaded', function() {
    // Aguardar o gráfico principal ser criado
    setTimeout(() => {
        // Tentar obter dados do gráfico principal
        const mainChartData = window.yoyChart ? window.yoyChart.data : null;
        initializeMiniCharts(mainChartData);
    }, 2000); // Aguardar 2 segundos para o gráfico principal ser criado
});

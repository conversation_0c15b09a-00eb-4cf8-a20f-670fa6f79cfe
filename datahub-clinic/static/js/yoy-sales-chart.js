/**
 * Gráfico de Vendas Year-over-Year com D3.js
 * Implementa visualização de vendas comparando 2023 vs 2024 com média móvel
 */

class YoYSalesChart {
    constructor(containerId) {
        this.containerId = containerId;
        this.container = d3.select(`#${containerId}`);
        this.margin = { top: 20, right: 30, bottom: 40, left: 60 };
        this.width = 0;
        this.height = 0;
        this.svg = null;
        this.xScale = null;
        this.yScale = null;
        this.line = null;
        this.data = this.generateMockData();

        // Cores do design system
        this.colors = {
            year2023: '#6D6D70',      // systemGray mais escuro
            year2024: '#0056CC',      // systemBlue mais escuro
            year2025: '#003D82',      // systemBlue mais escuro para 2025
            movingAvg: '#0056CC',     // systemBlue escuro sem transparência
            globalTrend: '#FF6B35',   // Laranja para linha de tendência global
            grid: '#E5E5EA',          // systemGray.extralight
            text: '#1D1D1F'           // label.primary
        };

        this.init();
    }

    generateMockData() {
        const months = [
            'Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun',
            'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'
        ];

        // 2023: Crescimento moderado e estável
        const data2023 = [
            75000, 78000, 82000, 85000, 83000, 80000,
            78000, 81000, 85000, 88000, 86000, 72000
        ];

        // 2024: BOOM! Crescimento explosivo especialmente no meio do ano
        const data2024 = [
            95000, 105000, 125000, 145000, 165000, 180000,
            195000, 210000, 225000, 240000, 220000, 185000
        ];

        // 2025: Começa baixo, supera 2024, depois queda em maio
        const data2025 = [
            85000,  // Jan - Começa abaixo de 100K (vs 95K de 2024)
            92000,  // Fev - Ainda abaixo de 2024 (vs 105K)
            115000, // Mar - Começa a superar 2024 (vs 125K ainda abaixo)
            155000, // Abr - Supera 2024 (vs 145K)
            175000, // Mai - Pico, mas depois queda
            140000, // Jun - Queda significativa no fim de maio/junho
            null, null, null, null, null, null // Julho em diante ainda não aconteceu
        ];

        const result = [];

        for (let i = 0; i < months.length; i++) {
            const month = months[i];
            const value2023 = data2023[i];
            const value2024 = data2024[i];
            const value2025 = data2025[i];

            // Calcular média móvel de 3 meses para 2024
            let movingAvg = value2024;
            if (i >= 2) {
                movingAvg = (data2024[i-2] + data2024[i-1] + data2024[i]) / 3;
            } else if (i === 1) {
                movingAvg = (data2024[0] + data2024[1]) / 2;
            }

            result.push({
                month: month,
                monthIndex: i,
                year2023: value2023,
                year2024: value2024,
                year2025: value2025,
                movingAvg: movingAvg,
                yoyGrowth: ((value2024 - value2023) / value2023 * 100).toFixed(1),
                yoy2025Growth: value2025 ? ((value2025 - value2024) / value2024 * 100).toFixed(1) : null
            });
        }

        // Calcular linha de tendência global considerando todos os anos
        this.calculateGlobalTrend(result);

        return result;
    }

    calculateGlobalTrend(data) {
        // Coletar todos os valores disponíveis de todos os anos
        const allValues = [];
        const allMonthIndices = [];

        data.forEach((d, i) => {
            // Adicionar 2023
            allValues.push(d.year2023);
            allMonthIndices.push(i);

            // Adicionar 2024 (12 meses depois)
            allValues.push(d.year2024);
            allMonthIndices.push(i + 12);

            // Adicionar 2025 se disponível (24 meses depois)
            if (d.year2025 !== null) {
                allValues.push(d.year2025);
                allMonthIndices.push(i + 24);
            }
        });

        // Calcular regressão linear para tendência global
        const n = allValues.length;
        const sumX = allMonthIndices.reduce((a, b) => a + b, 0);
        const sumY = allValues.reduce((a, b) => a + b, 0);
        const sumXY = allMonthIndices.reduce((sum, x, i) => sum + x * allValues[i], 0);
        const sumXX = allMonthIndices.reduce((sum, x) => sum + x * x, 0);

        const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
        const intercept = (sumY - slope * sumX) / n;

        // Adicionar valores de tendência global para cada mês
        data.forEach((d, i) => {
            d.globalTrend = slope * i + intercept;
        });
    }

    init() {
        this.setupDimensions();
        this.createSVG();
        this.createScales();
        this.createAxes();
        this.createGridlines();
        this.createLines();
        this.createPoints();
        this.createTooltip();
        this.updateMetrics();

        // Responsividade
        window.addEventListener('resize', () => this.handleResize());

        // Inicializar botões de toggle da legenda
        this.initLegendToggle();
    }

    setupDimensions() {
        const containerRect = this.container.node().getBoundingClientRect();
        this.width = containerRect.width - this.margin.left - this.margin.right;
        this.height = 400 - this.margin.top - this.margin.bottom;
    }

    createSVG() {
        this.container.selectAll('*').remove();

        this.svg = this.container
            .append('svg')
            .attr('width', this.width + this.margin.left + this.margin.right)
            .attr('height', this.height + this.margin.top + this.margin.bottom)
            .append('g')
            .attr('transform', `translate(${this.margin.left},${this.margin.top})`);
    }

    createScales() {
        this.xScale = d3.scalePoint()
            .domain(this.data.map(d => d.month))
            .range([0, this.width])
            .padding(0.1);

        const allValues = [
            ...this.data.map(d => d.year2023),
            ...this.data.map(d => d.year2024),
            ...this.data.map(d => d.movingAvg),
            ...this.data.map(d => d.year2025).filter(v => v !== null),
            ...this.data.map(d => d.globalTrend)
        ];

        const yMin = d3.min(allValues) * 0.9;
        const yMax = d3.max(allValues) * 1.1;

        this.yScale = d3.scaleLinear()
            .domain([yMin, yMax])
            .range([this.height, 0]);
    }

    createAxes() {
        // Eixo X
        this.svg.append('g')
            .attr('class', 'x-axis')
            .attr('transform', `translate(0,${this.height})`)
            .call(d3.axisBottom(this.xScale))
            .selectAll('text')
            .style('font-size', '12px')
            .style('fill', this.colors.text);

        // Eixo Y
        this.svg.append('g')
            .attr('class', 'y-axis')
            .call(d3.axisLeft(this.yScale)
                .tickFormat(d => `R$ ${(d/1000).toFixed(0)}K`)
                .ticks(6))
            .selectAll('text')
            .style('font-size', '12px')
            .style('fill', this.colors.text);

        // Remover linhas dos eixos
        this.svg.selectAll('.domain').remove();
        this.svg.selectAll('.tick line').remove();
    }

    createGridlines() {
        // Linhas de grade horizontais
        this.svg.append('g')
            .attr('class', 'grid')
            .call(d3.axisLeft(this.yScale)
                .tickSize(-this.width)
                .tickFormat('')
                .ticks(6))
            .selectAll('line')
            .style('stroke', this.colors.grid)
            .style('stroke-width', 1)
            .style('opacity', 0.7);

        this.svg.select('.grid .domain').remove();
    }

    createLines() {
        this.line = d3.line()
            .x(d => this.xScale(d.month))
            .y(d => this.yScale(d.value))
            .curve(d3.curveLinear) // Linha mais reta
            .defined(d => d.value !== null); // Não desenhar onde não há dados

        // Linha 2023
        this.svg.append('path')
            .datum(this.data.map(d => ({ month: d.month, value: d.year2023 })))
            .attr('class', 'line-2023')
            .attr('fill', 'none')
            .attr('stroke', this.colors.year2023)
            .attr('stroke-width', 1.5) // Mais fina
            .attr('d', this.line);

        // Linha 2024
        this.svg.append('path')
            .datum(this.data.map(d => ({ month: d.month, value: d.year2024 })))
            .attr('class', 'line-2024')
            .attr('fill', 'none')
            .attr('stroke', this.colors.year2024)
            .attr('stroke-width', 2) // Mais fina
            .attr('d', this.line);

        // Linha 2025 (apenas até novembro)
        this.svg.append('path')
            .datum(this.data.map(d => ({ month: d.month, value: d.year2025 })))
            .attr('class', 'line-2025')
            .attr('fill', 'none')
            .attr('stroke', this.colors.year2025)
            .attr('stroke-width', 2) // Mais fina
            .attr('d', this.line);

        // Linha média móvel (mais visível)
        this.svg.append('path')
            .datum(this.data.map(d => ({ month: d.month, value: d.movingAvg })))
            .attr('class', 'line-moving-avg')
            .attr('fill', 'none')
            .attr('stroke', this.colors.movingAvg)
            .attr('stroke-width', 2) // Mais espessa
            .attr('stroke-dasharray', '5,5') // Traços maiores
            .attr('stroke-opacity', 0.8) // Mais opaca
            .attr('d', this.line);

        // Linha de tendência global
        this.svg.append('path')
            .datum(this.data.map(d => ({ month: d.month, value: d.globalTrend })))
            .attr('class', 'line-global-trend')
            .attr('fill', 'none')
            .attr('stroke', this.colors.globalTrend)
            .attr('stroke-width', 2)
            .attr('stroke-dasharray', '8,4') // Traços longos
            .attr('stroke-opacity', 0.7)
            .attr('d', this.line);

        // Animação das linhas
        this.svg.selectAll('.line-2023, .line-2024, .line-2025, .line-moving-avg, .line-global-trend')
            .each(function() {
                const totalLength = this.getTotalLength();
                d3.select(this)
                    .attr('stroke-dasharray', totalLength + ' ' + totalLength)
                    .attr('stroke-dashoffset', totalLength)
                    .transition()
                    .duration(2000)
                    .ease(d3.easeLinear)
                    .attr('stroke-dashoffset', 0)
                    .on('end', function() {
                        const isMovingAvg = this.classList.contains('line-moving-avg');
                        const isGlobalTrend = this.classList.contains('line-global-trend');
                        if (isMovingAvg) {
                            d3.select(this).attr('stroke-dasharray', '5,5');
                        } else if (isGlobalTrend) {
                            d3.select(this).attr('stroke-dasharray', '8,4');
                        } else {
                            d3.select(this).attr('stroke-dasharray', 'none');
                        }
                    });
            });
    }

    createPoints() {
        // Pontos 2023
        this.svg.selectAll('.point-2023')
            .data(this.data)
            .enter()
            .append('circle')
            .attr('class', 'point-2023')
            .attr('cx', d => this.xScale(d.month))
            .attr('cy', d => this.yScale(d.year2023))
            .attr('r', 3) // Menor
            .attr('fill', 'white')
            .attr('stroke', this.colors.year2023)
            .attr('stroke-width', 1.5)
            .style('opacity', 0)
            .transition()
            .delay((d, i) => i * 100 + 1000)
            .duration(300)
            .style('opacity', 1);

        // Pontos 2024
        this.svg.selectAll('.point-2024')
            .data(this.data)
            .enter()
            .append('circle')
            .attr('class', 'point-2024')
            .attr('cx', d => this.xScale(d.month))
            .attr('cy', d => this.yScale(d.year2024))
            .attr('r', 4) // Menor
            .attr('fill', this.colors.year2024)
            .attr('stroke', 'white')
            .attr('stroke-width', 1.5)
            .style('opacity', 0)
            .transition()
            .delay((d, i) => i * 100 + 1200)
            .duration(300)
            .style('opacity', 1);

        // Pontos 2025 (apenas onde há dados)
        this.svg.selectAll('.point-2025')
            .data(this.data.filter(d => d.year2025 !== null))
            .enter()
            .append('circle')
            .attr('class', 'point-2025')
            .attr('cx', d => this.xScale(d.month))
            .attr('cy', d => this.yScale(d.year2025))
            .attr('r', 4) // Menor
            .attr('fill', this.colors.year2025)
            .attr('stroke', 'white')
            .attr('stroke-width', 1.5)
            .style('opacity', 0)
            .transition()
            .delay((d, i) => i * 100 + 1400)
            .duration(300)
            .style('opacity', 1);
    }

    createTooltip() {
        const tooltip = d3.select('#chart-tooltip');

        // Área invisível para capturar mouse
        this.svg.append('rect')
            .attr('width', this.width)
            .attr('height', this.height)
            .attr('fill', 'transparent')
            .on('mousemove', (event) => {
                const [mouseX] = d3.pointer(event);
                const monthIndex = Math.round(this.xScale.invert ? this.xScale.invert(mouseX) :
                    this.data.findIndex(d => Math.abs(this.xScale(d.month) - mouseX) ===
                        Math.min(...this.data.map(item => Math.abs(this.xScale(item.month) - mouseX)))));

                if (monthIndex >= 0 && monthIndex < this.data.length) {
                    const data = this.data[monthIndex];
                    this.showTooltip(event, data, tooltip);
                }
            })
            .on('mouseleave', () => {
                tooltip
                    .style('opacity', 0)
                    .style('transform', 'scale(0.95)')
                    .style('transition', 'all 0.2s ease-in');
            });
    }

    showTooltip(event, data, tooltip) {
        const formatCurrency = (value) => `R$ ${(value/1000).toFixed(0)}K`;

        // Determinar qual ano mostrar no título baseado nos dados disponíveis
        let titleYear = '2024';
        let currentValue = data.year2024;
        if (data.year2025 !== null) {
            titleYear = '2025';
            currentValue = data.year2025;
        }

        // Calcular deltas para setas
        const delta2024vs2023 = ((data.year2024 - data.year2023) / data.year2023 * 100);
        const delta2025vs2024 = data.year2025 ? ((data.year2025 - data.year2024) / data.year2024 * 100) : null;

        const getDeltaDisplay = (delta) => {
            if (delta === null) return '';
            const arrow = delta >= 0 ? '↗' : '↘';
            const color = delta >= 0 ? 'text-green-600' : 'text-red-600';
            return `<span class="${color} text-xs ml-1">${arrow} ${Math.abs(delta).toFixed(1)}%</span>`;
        };

        const content = `
            <div class="border-b border-gray-100 pb-2 mb-3">
                <div class="text-xs text-gray-500 uppercase tracking-wide mb-1">${data.month} ${titleYear}</div>
                <div class="text-lg font-mono font-bold text-gray-900">${formatCurrency(currentValue)}</div>
            </div>

            <div class="space-y-1.5 text-xs">
                ${data.year2025 !== null ? `
                <div class="flex justify-between items-center font-mono">
                    <span class="text-gray-600">2025</span>
                    <div class="flex items-center">
                        <span class="font-semibold text-gray-900">${formatCurrency(data.year2025)}</span>
                        ${getDeltaDisplay(delta2025vs2024)}
                    </div>
                </div>` : ''}

                <div class="flex justify-between items-center font-mono">
                    <span class="text-gray-600">2024</span>
                    <div class="flex items-center">
                        <span class="font-semibold text-gray-900">${formatCurrency(data.year2024)}</span>
                        ${getDeltaDisplay(delta2024vs2023)}
                    </div>
                </div>

                <div class="flex justify-between items-center font-mono">
                    <span class="text-gray-600">2023</span>
                    <span class="font-semibold text-gray-900">${formatCurrency(data.year2023)}</span>
                </div>
            </div>

            <div class="border-t border-gray-100 mt-3 pt-2 space-y-1.5 text-xs">
                <div class="flex justify-between items-center font-mono">
                    <span class="text-gray-500">YoY</span>
                    <span class="font-semibold ${data.yoyGrowth >= 0 ? 'text-green-600' : 'text-red-600'}">${data.yoyGrowth >= 0 ? '+' : ''}${data.yoyGrowth}%</span>
                </div>

                <div class="flex justify-between items-center font-mono">
                    <span class="text-gray-500">MA3</span>
                    <span class="font-semibold text-gray-900">${formatCurrency(data.movingAvg)}</span>
                </div>

                <div class="flex justify-between items-center font-mono">
                    <span class="text-gray-500">TREND</span>
                    <span class="font-semibold text-gray-900">${formatCurrency(data.globalTrend)}</span>
                </div>
            </div>
        `;

        document.getElementById('tooltip-content').innerHTML = content;

        // Posicionamento simples e funcional
        const containerRect = this.container.node().getBoundingClientRect();

        // Calcular posição baseada no mouse
        let left = event.pageX - containerRect.left + 10;
        let top = event.pageY - containerRect.top - 10;

        // Ajustar se sair da tela
        if (left + 200 > this.width) { // 200px é aproximadamente a largura do tooltip
            left = event.pageX - containerRect.left - 210;
        }
        if (top < 0) {
            top = event.pageY - containerRect.top + 10;
        }

        tooltip
            .style('left', left + 'px')
            .style('top', top + 'px')
            .style('opacity', 1)
            .style('transform', 'scale(1)');
    }

    updateMetrics() {
        // Calcular métricas
        const totalGrowth = this.data.reduce((sum, d) => sum + parseFloat(d.yoyGrowth), 0) / this.data.length;
        const bestMonth = this.data.reduce((best, current) =>
            current.year2024 > best.year2024 ? current : best, this.data[0]);
        const avg2024 = this.data.reduce((sum, d) => sum + d.year2024, 0) / this.data.length;

        // Calcular tendência dos últimos 6 meses
        const recentData = this.data.slice(-6);
        const trendSlope = this.calculateTrend(recentData.map(d => d.year2024));
        const trendDirection = trendSlope > 0 ? 'Crescente' : 'Decrescente';

        // Atualizar elementos da UI com animação
        this.animateValue('yoy-growth', totalGrowth, '%', '+');
        this.animateValue('avg-2024', avg2024/1000, 'K', 'R$ ');

        // Atualizar textos
        setTimeout(() => {
            document.getElementById('best-month').textContent = bestMonth.month;
            document.getElementById('trend').textContent = `↗ ${trendDirection}`;
        }, 500);

        // Atualizar deltas nos cards
        this.updateDeltas(totalGrowth, bestMonth, avg2024, trendDirection);
    }

    calculateTrend(values) {
        const n = values.length;
        const x = Array.from({length: n}, (_, i) => i);
        const sumX = x.reduce((a, b) => a + b, 0);
        const sumY = values.reduce((a, b) => a + b, 0);
        const sumXY = x.reduce((sum, xi, i) => sum + xi * values[i], 0);
        const sumXX = x.reduce((sum, xi) => sum + xi * xi, 0);

        return (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    }

    animateValue(elementId, targetValue, suffix = '', prefix = '') {
        const element = document.getElementById(elementId);
        if (!element) return;

        const startValue = 0;
        const duration = 1500;
        const startTime = performance.now();

        function updateValue(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // Easing function (ease-out)
            const easeOut = 1 - Math.pow(1 - progress, 3);
            const currentValue = startValue + (targetValue - startValue) * easeOut;

            element.textContent = `${prefix}${currentValue.toFixed(1)}${suffix}`;

            if (progress < 1) {
                requestAnimationFrame(updateValue);
            }
        }

        requestAnimationFrame(updateValue);
    }

    updateDeltas(growth, bestMonth, avg2024, trend) {
        // Simular deltas baseados nos dados reais
        const deltas = {
            growth: Math.random() * 3 + 1, // 1-4%
            peak: bestMonth.year2024,
            average: Math.random() * 10 + 5, // 5-15%
            trend: trend === 'Crescente' ? 'Forte' : 'Fraca'
        };

        // Atualizar deltas visuais nos cards
        setTimeout(() => {
            const growthCard = document.querySelector('#yoy-growth').closest('.bg-white');
            const avgCard = document.querySelector('#avg-2024').closest('.bg-white');

            if (growthCard) {
                const deltaElement = growthCard.querySelector('.bg-green-50 span:last-child');
                if (deltaElement) {
                    deltaElement.textContent = `+${deltas.growth.toFixed(1)}%`;
                }
            }

            if (avgCard) {
                const deltaElement = avgCard.querySelector('.bg-green-50 span:last-child');
                if (deltaElement) {
                    deltaElement.textContent = `+${deltas.average.toFixed(1)}%`;
                }
            }
        }, 1000);
    }

    initLegendToggle() {
        // Estado inicial das linhas (todas visíveis)
        this.lineVisibility = {
            '2023': true,
            '2024': true,
            '2025': true,
            'moving-avg': true,
            'global-trend': true
        };

        // Adicionar event listeners aos botões
        const toggleButtons = document.querySelectorAll('.legend-toggle');
        toggleButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const lineType = button.getAttribute('data-line');
                this.toggleLine(lineType, button);
            });
        });
    }

    toggleLine(lineType, button) {
        // Alternar visibilidade
        this.lineVisibility[lineType] = !this.lineVisibility[lineType];
        const isVisible = this.lineVisibility[lineType];

        // Atualizar classe do botão
        if (isVisible) {
            button.classList.remove('inactive');
            button.classList.add('active');
        } else {
            button.classList.remove('active');
            button.classList.add('inactive');
        }

        // Mapear tipos de linha para classes CSS
        const lineClassMap = {
            '2023': '.line-2023',
            '2024': '.line-2024',
            '2025': '.line-2025',
            'moving-avg': '.line-moving-avg',
            'global-trend': '.line-global-trend'
        };

        const pointClassMap = {
            '2023': '.point-2023',
            '2024': '.point-2024',
            '2025': '.point-2025'
        };

        // Animar linha
        const lineSelector = lineClassMap[lineType];
        if (lineSelector) {
            this.svg.select(lineSelector)
                .transition()
                .duration(300)
                .style('opacity', isVisible ? 1 : 0)
                .style('pointer-events', isVisible ? 'auto' : 'none');
        }

        // Animar pontos (se existirem)
        const pointSelector = pointClassMap[lineType];
        if (pointSelector) {
            this.svg.selectAll(pointSelector)
                .transition()
                .duration(300)
                .style('opacity', isVisible ? 1 : 0)
                .style('pointer-events', isVisible ? 'auto' : 'none');
        }
    }

    handleResize() {
        this.setupDimensions();
        this.createSVG();
        this.createScales();
        this.createAxes();
        this.createGridlines();
        this.createLines();
        this.createPoints();
        this.createTooltip();
        this.initLegendToggle(); // Reinicializar botões após resize
    }
}

// Inicializar o gráfico quando o DOM estiver carregado
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('yoy-sales-chart')) {
        window.yoyChart = new YoYSalesChart('yoy-sales-chart');
    }
});

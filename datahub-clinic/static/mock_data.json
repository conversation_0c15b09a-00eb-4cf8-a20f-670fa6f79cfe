{"faturamento_mensal": {"meses": ["Jan", "<PERSON>v", "Mar", "Abr", "<PERSON>", "Jun", "Jul", "Ago", "Set", "Out", "Nov", "<PERSON>z"], "valores": [320000, 310000, 315000, 340000, 360000, 375000, 390000, 400000, 380000, 390000, 410000, 430000], "meta": [300000, 300000, 320000, 320000, 350000, 350000, 370000, 370000, 390000, 390000, 410000, 410000]}, "pacientes_mensais": {"meses": ["Jan", "<PERSON>v", "Mar", "Abr", "<PERSON>", "Jun", "Jul", "Ago", "Set", "Out", "Nov", "<PERSON>z"], "novos": [45, 38, 42, 48, 52, 58, 62, 65, 60, 63, 68, 72], "recorrentes": [180, 185, 190, 195, 200, 210, 215, 220, 215, 220, 225, 230]}, "procedimentos": {"nomes": ["Consulta Clínica", "<PERSON>ame de Rotina", "Cirurgia Simples", "Cirurgia Complexa", "Tratamento Contínuo", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "quantidade": [450, 320, 85, 35, 120, 65], "valor_medio": [250, 350, 1200, 3500, 800, 600], "custo_medio": [150, 200, 700, 2200, 500, 400]}, "profissionais": {"nomes": ["Dr. <PERSON>", "<PERSON><PERSON><PERSON>", "Dr. <PERSON>", "<PERSON><PERSON><PERSON>", "Dr. <PERSON>"], "especialidades": ["Clínico Geral", "Cardiologia", "Ortopedia", "Pediatria", "Dermatologia"], "atendimentos_mes": [120, 95, 85, 110, 90], "valor_medio_atendimento": [280, 350, 320, 250, 300], "avaliacao_pacientes": [4.8, 4.9, 4.7, 4.8, 4.6]}, "ocupacao_agenda": {"dias_semana": ["Segunda", "<PERSON><PERSON><PERSON>", "Quarta", "<PERSON><PERSON><PERSON>", "Sexta", "Sábado"], "manha": [85, 75, 80, 70, 75, 90], "tarde": [90, 85, 80, 85, 70, 60], "noite": [70, 75, 80, 85, 60, 0]}, "satisfacao_pacientes": {"meses": ["Jan", "<PERSON>v", "Mar", "Abr", "<PERSON>", "Jun", "Jul", "Ago", "Set", "Out", "Nov", "<PERSON>z"], "nps": [75, 76, 78, 77, 80, 82, 83, 85, 84, 86, 87, 88], "detratores": [10, 9, 8, 9, 7, 6, 6, 5, 5, 4, 4, 3], "neutros": [15, 15, 14, 14, 13, 12, 11, 10, 11, 10, 9, 9], "promotores": [75, 76, 78, 77, 80, 82, 83, 85, 84, 86, 87, 88]}, "marketing": {"canais": ["Google Ads", "Facebook", "Instagram", "Indicação", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Outros"], "leads": [120, 85, 95, 150, 80, 30], "conversoes": [35, 25, 30, 60, 40, 10], "custo_aquisicao": [120, 80, 90, 20, 50, 40], "valor_medio_primeiro_atendimento": [280, 250, 260, 300, 320, 270]}, "financeiro": {"categorias_despesas": ["Pessoal", "Insumos", "Equipamentos", "<PERSON><PERSON><PERSON>", "Marketing", "Administrativo", "Impostos"], "valores_despesas": [150000, 80000, 30000, 25000, 20000, 15000, 60000], "categorias_receitas": ["Consultas", "Exames", "Procedimentos", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Outros"], "valores_receitas": [180000, 120000, 150000, 90000, 20000]}, "tempo_atendimento": {"especialidades": ["Clínico Geral", "Cardiologia", "Ortopedia", "Pediatria", "Dermatologia"], "tempo_medio_minutos": [25, 35, 30, 20, 25], "tempo_espera_minutos": [15, 20, 18, 12, 15]}, "jornada_paciente": {"etapas": ["Agendamento", "Recepção", "Espera", "Atendimento", "Pagamento", "Pós-atendimento"], "satisfacao": [85, 90, 75, 92, 88, 80], "tempo_medio_minutos": [5, 3, 18, 30, 5, 10]}, "previsao_demanda": {"proximos_meses": ["Jan", "<PERSON>v", "Mar", "Abr", "<PERSON>", "Jun"], "consultas_previstas": [460, 480, 500, 520, 540, 560], "exames_previstos": [320, 330, 350, 360, 380, 400], "procedimentos_previstos": [120, 125, 130, 135, 140, 145]}}
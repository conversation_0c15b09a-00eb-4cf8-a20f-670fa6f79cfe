{% extends "base.html" %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h1 class="text-2xl font-bold mb-6">Configuração de Fonte de Dados</h1>
    
    <form method="post" class="bg-white p-6 rounded-lg shadow-md">
        <div class="mb-4">
            <label class="block text-gray-700 font-bold mb-2">Tipo de Fonte de Dados</label>
            <select name="source_type" id="source_type" class="w-full px-3 py-2 border rounded-lg" onchange="showSourceOptions()">
                <option value="mock" {% if config.type == 'mock' %}selected{% endif %}>Dados Mockados</option>
                <option value="json" {% if config.type == 'json' %}selected{% endif %}>Arquivo JSON</option>
                <option value="excel" {% if config.type == 'excel' %}selected{% endif %}>Arquivo Excel (XLSX)</option>
                <option value="csv" {% if config.type == 'csv' %}selected{% endif %}>Arquivos CSV</option>
                <option value="database" {% if config.type == 'database' %}selected{% endif %}>Banco de Dados</option>
            </select>
        </div>
        
        <!-- Opções para JSON -->
        <div id="json_options" class="source-options {% if config.type != 'json' %}hidden{% endif %}">
            <div class="mb-4">
                <label class="block text-gray-700 font-bold mb-2">Caminho do Arquivo JSON</label>
                <input type="text" name="file_path" class="w-full px-3 py-2 border rounded-lg" value="{{ config.file_path }}">
                <p class="text-sm text-gray-500 mt-1">Caminho completo para o arquivo JSON com a estrutura de dados completa.</p>
            </div>
        </div>
        
        <!-- Opções para Excel -->
        <div id="excel_options" class="source-options {% if config.type != 'excel' %}hidden{% endif %}">
            <div class="mb-4">
                <label class="block text-gray-700 font-bold mb-2">Caminho do Arquivo Excel</label>
                <input type="text" name="file_path" class="w-full px-3 py-2 border rounded-lg" value="{{ config.file_path }}">
                <p class="text-sm text-gray-500 mt-1">Caminho completo para o arquivo Excel (XLSX).</p>
            </div>
            
            <div class="mb-4">
                <label class="block text-gray-700 font-bold mb-2">Mapeamento de Abas</label>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-gray-600 text-sm mb-1">Agenda</label>
                        <input type="text" name="sheet_agenda" class="w-full px-3 py-2 border rounded-lg" value="{{ config.sheet_mapping.agenda if config.sheet_mapping else 'Agenda' }}">
                    </div>
                    <div>
                        <label class="block text-gray-600 text-sm mb-1">Financeiro</label>
                        <input type="text" name="sheet_financeiro" class="w-full px-3 py-2 border rounded-lg" value="{{ config.sheet_mapping.financeiro if config.sheet_mapping else 'Financeiro' }}">
                    </div>
                    <div>
                        <label class="block text-gray-600 text-sm mb-1">Paciente</label>
                        <input type="text" name="sheet_paciente" class="w-full px-3 py-2 border rounded-lg" value="{{ config.sheet_mapping.paciente if config.sheet_mapping else 'Paciente' }}">
                    </div>
                    <div>
                        <label class="block text-gray-600 text-sm mb-1">AmigoCare</label>
                        <input type="text" name="sheet_amigocare" class="w-full px-3 py-2 border rounded-lg" value="{{ config.sheet_mapping.amigocare if config.sheet_mapping else 'AmigoCare' }}">
                    </div>
                    <div>
                        <label class="block text-gray-600 text-sm mb-1">Visão 360</label>
                        <input type="text" name="sheet_visao360" class="w-full px-3 py-2 border rounded-lg" value="{{ config.sheet_mapping.visao360 if config.sheet_mapping else 'Visao360' }}">
                    </div>
                    <div>
                        <label class="block text-gray-600 text-sm mb-1">Resumo</label>
                        <input type="text" name="sheet_resumo" class="w-full px-3 py-2 border rounded-lg" value="{{ config.sheet_mapping.resumo if config.sheet_mapping else 'Resumo' }}">
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Opções para CSV -->
        <div id="csv_options" class="source-options {% if config.type != 'csv' %}hidden{% endif %}">
            <div class="mb-4">
                <label class="block text-gray-700 font-bold mb-2">Arquivos CSV</label>
                <div class="grid grid-cols-1 gap-4">
                    <div>
                        <label class="block text-gray-600 text-sm mb-1">Agenda</label>
                        <input type="text" name="csv_agenda" class="w-full px-3 py-2 border rounded-lg" value="{{ config.file_paths.agenda if config.file_paths else '' }}">
                    </div>
                    <div>
                        <label class="block text-gray-600 text-sm mb-1">Financeiro</label>
                        <input type="text" name="csv_financeiro" class="w-full px-3 py-2 border rounded-lg" value="{{ config.file_paths.financeiro if config.file_paths else '' }}">
                    </div>
                    <div>
                        <label class="block text-gray-600 text-sm mb-1">Paciente</label>
                        <input type="text" name="csv_paciente" class="w-full px-3 py-2 border rounded-lg" value="{{ config.file_paths.paciente if config.file_paths else '' }}">
                    </div>
                    <div>
                        <label class="block text-gray-600 text-sm mb-1">AmigoCare</label>
                        <input type="text" name="csv_amigocare" class="w-full px-3 py-2 border rounded-lg" value="{{ config.file_paths.amigocare if config.file_paths else '' }}">
                    </div>
                    <div>
                        <label class="block text-gray-600 text-sm mb-1">Visão 360</label>
                        <input type="text" name="csv_visao360" class="w-full px-3 py-2 border rounded-lg" value="{{ config.file_paths.visao360 if config.file_paths else '' }}">
                    </div>
                </div>
            </div>
            
            <div class="mb-4">
                <label class="block text-gray-700 font-bold mb-2">Configurações CSV</label>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-gray-600 text-sm mb-1">Delimitador</label>
                        <input type="text" name="csv_delimiter" class="w-full px-3 py-2 border rounded-lg" value="{{ config.delimiter if config.delimiter else ',' }}">
                    </div>
                    <div>
                        <label class="block text-gray-600 text-sm mb-1">Codificação</label>
                        <input type="text" name="csv_encoding" class="w-full px-3 py-2 border rounded-lg" value="{{ config.encoding if config.encoding else 'utf-8' }}">
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Opções para Banco de Dados -->
        <div id="database_options" class="source-options {% if config.type != 'database' %}hidden{% endif %}">
            <div class="mb-4">
                <label class="block text-gray-700 font-bold mb-2">String de Conexão</label>
                <input type="text" name="connection_string" class="w-full px-3 py-2 border rounded-lg" value="{{ config.connection_string }}">
                <p class="text-sm text-gray-500 mt-1">Exemplo: postgresql://usuario:senha@localhost/banco_dados</p>
            </div>
            
            <div class="mb-4">
                <label class="block text-gray-700 font-bold mb-2">Configuração Avançada</label>
                <p class="text-sm text-gray-500 mb-2">Para configurar consultas SQL específicas para cada módulo, edite o arquivo de configuração diretamente.</p>
            </div>
        </div>
        
        <div class="mt-6">
            <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded">
                Salvar Configuração
            </button>
        </div>
    </form>
</div>

<script>
    function showSourceOptions() {
        // Esconder todas as opções
        document.querySelectorAll('.source-options').forEach(function(el) {
            el.classList.add('hidden');
        });
        
        // Mostrar opções relevantes
        const sourceType = document.getElementById('source_type').value;
        const optionsDiv = document.getElementById(sourceType + '_options');
        if (optionsDiv) {
            optionsDiv.classList.remove('hidden');
        }
    }
    
    // Inicializar
    document.addEventListener('DOMContentLoaded', function() {
        showSourceOptions();
    });
</script>
{% endblock %}

{% extends 'base.html' %}

{% block title %}Agendamentos - Amigo DataHub{% endblock %}

{% block header %}Agendamentos{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="bg-gradient-to-r from-blue-50 to-white rounded-lg shadow-sm p-6 mb-5 overflow-hidden relative">
    <!-- Decorative Elements -->
    <div class="absolute top-0 right-0 w-64 h-64 opacity-10">
        <svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg" class="text-systemBlue fill-current">
            <path d="M42.7,-73.2C55.9,-65.7,67.7,-55.3,76.3,-42.1C84.9,-28.9,90.3,-14.5,89.8,-0.3C89.3,13.9,82.8,27.8,74.1,40.2C65.4,52.7,54.4,63.8,41.4,70.8C28.3,77.8,14.1,80.7,-0.2,81C-14.6,81.3,-29.2,79,-42.2,72.4C-55.2,65.8,-66.6,54.9,-73.3,41.8C-80,28.6,-82,14.3,-81.2,0.5C-80.4,-13.3,-76.8,-26.6,-69.8,-38.5C-62.8,-50.5,-52.4,-61.1,-40.1,-69.2C-27.8,-77.3,-13.9,-82.9,0.5,-83.7C14.8,-84.5,29.6,-80.6,42.7,-73.2Z" transform="translate(100 100)" />
        </svg>
    </div>

    <div class="flex flex-col md:flex-row items-center">
        <div class="md:w-2/3 mb-6 md:mb-0 md:pr-6 relative z-10">
            <div class="inline-block bg-blue-100 text-systemBlue text-xs font-medium px-2.5 py-1 rounded-full mb-3">GESTÃO DE AGENDA</div>
            <h1 class="text-3xl font-bold text-gray-800 mb-3">Central de Agendamentos</h1>
            <p class="text-gray-600 text-sm mb-6 max-w-2xl">
                Gerencie todos os agendamentos da clínica em um só lugar. Visualize, crie e acompanhe consultas e procedimentos de forma eficiente e organizada.
            </p>

            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                <div class="bg-white rounded-lg p-3 shadow-sm border border-gray-100">
                    <div class="flex items-center">
                        <div class="w-10 h-10 rounded-full bg-blue-50 flex items-center justify-center mr-3">
                            <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="text-2xl font-semibold text-gray-800">{{ agendamentos|selectattr('data', 'equalto', now.strftime('%d/%m/%Y'))|list|length }}</div>
                            <div class="text-xs text-gray-500">Hoje</div>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg p-3 shadow-sm border border-gray-100">
                    <div class="flex items-center">
                        <div class="w-10 h-10 rounded-full bg-green-50 flex items-center justify-center mr-3">
                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="text-2xl font-semibold text-gray-800">{{ agendamentos|selectattr('status', 'equalto', 'Confirmado')|list|length }}</div>
                            <div class="text-xs text-gray-500">Confirmados</div>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg p-3 shadow-sm border border-gray-100">
                    <div class="flex items-center">
                        <div class="w-10 h-10 rounded-full bg-orange-50 flex items-center justify-center mr-3">
                            <svg class="w-5 h-5 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="text-2xl font-semibold text-gray-800">{{ agendamentos|selectattr('status', 'equalto', 'Agendado')|list|length }}</div>
                            <div class="text-xs text-gray-500">Pendentes</div>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg p-3 shadow-sm border border-gray-100">
                    <div class="flex items-center">
                        <div class="w-10 h-10 rounded-full bg-blue-50 flex items-center justify-center mr-3">
                            <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="text-2xl font-semibold text-gray-800">{{ agendamentos|length }}</div>
                            <div class="text-xs text-gray-500">Total</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex flex-wrap gap-3">
                <button class="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center transition-colors">
                    <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Novo Agendamento
                </button>
                <button class="bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-md text-sm border border-gray-200 flex items-center transition-colors">
                    <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    Ver Calendário
                </button>
                <button class="bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-md text-sm border border-gray-200 flex items-center transition-colors">
                    <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Relatórios
                </button>
            </div>
        </div>

        <div class="md:w-1/3 flex justify-center items-center relative z-10">
            <img src="/static/images/calendar-illustration.svg" alt="Gestão de Agenda" class="w-64 h-64 object-contain" onerror="this.src='https://cdn-icons-png.flaticon.com/512/2693/2693507.png'; this.onerror=null;">
        </div>
    </div>

    <!-- Decorative Wave -->
    <div class="absolute bottom-0 left-0 right-0 h-8 overflow-hidden">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" class="w-full h-full text-white fill-current">
            <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V120H0V95.8C57.1,118.92,136.33,111.31,213.25,91.5c31.83-8.15,62.6-18.64,97.95-24.32A321.18,321.18,0,0,1,321.39,56.44Z"></path>
        </svg>
    </div>
</div>

<!-- Insights com IA -->
<div class="mb-5">
    <div class="flex items-center justify-between mb-4">
        <div class="flex items-center">
            <svg class="w-5 h-5 text-systemBlue mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z"></path>
            </svg>
            <h2 class="text-base font-semibold text-gray-800">Amigo Intelligence</h2>
        </div>
        <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">Atualizado agora</span>
    </div>

    <div id="insights-container" class="grid grid-cols-1 md:grid-cols-3 gap-3">
        {% for insight in insights %}
            {% with
                title=insight.title,
                description=insight.description,
                insight_type=insight.insight_type,
                content=insight.content,
                category=insight.category
            %}
                {% include 'components/ai_insight_widget.html' %}
            {% endwith %}
        {% endfor %}
    </div>
</div>

<script>
    // Carregar insights dinamicamente após 5 segundos (para demonstração)
    setTimeout(() => {
        // Contexto específico da página de agendamentos
        const pageContext = {
            page_title: "Agendamentos",
            page_description: "Análise e gestão de agendamentos da clínica, com foco em otimização e redução de faltas",
            key_metrics: {
                "Total de Agendamentos": document.querySelector('.kpi-total-agendamentos .value')?.textContent || "1.245",
                "Taxa de Comparecimento": document.querySelector('.kpi-taxa-comparecimento .value')?.textContent || "78%",
                "Agendamentos Confirmados": document.querySelector('.kpi-confirmados .value')?.textContent || "85%"
            },
            analysis_focus: "Otimização de agenda e redução de faltas",
            page_elements: [
                "Distribuição por Status",
                "Agendamentos por Profissional",
                "Evolução Mensal",
                "Horários Mais Procurados"
            ]
        };

        loadInsights('agenda', 'agendamentos', 'insights-container', 3, ['list', 'text', 'stat'], pageContext);
    }, 5000);
</script>

<!-- Filtros Rápidos -->
<div class="bg-white rounded-lg shadow-sm p-4 mb-5">
    <div class="flex justify-between items-center mb-3">
        <h2 class="text-sm font-medium text-gray-700">Visualização Rápida</h2>
        <div class="flex items-center gap-2">
            <div class="relative">
                <input type="date" class="text-xs border border-gray-200 rounded-md px-2 py-1 pr-8 focus:outline-none focus:ring-1 focus:ring-systemBlue">
                <svg class="w-4 h-4 text-gray-500 absolute right-2 top-1/2 transform -translate-y-1/2 pointer-events-none" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
            </div>
            <button class="text-xs text-systemBlue hover:text-blue-700 transition-colors">Limpar filtros</button>
        </div>
    </div>

    <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-2">
        <button class="bg-blue-50 text-systemBlue px-3 py-2 rounded-md text-xs font-medium flex items-center justify-center transition-colors">
            <svg class="w-3.5 h-3.5 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
            Hoje
        </button>
        <button class="bg-white hover:bg-gray-50 text-gray-700 px-3 py-2 rounded-md text-xs font-medium flex items-center justify-center border border-gray-200 transition-colors">
            <svg class="w-3.5 h-3.5 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
            Amanhã
        </button>
        <button class="bg-white hover:bg-gray-50 text-gray-700 px-3 py-2 rounded-md text-xs font-medium flex items-center justify-center border border-gray-200 transition-colors">
            <svg class="w-3.5 h-3.5 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
            Esta semana
        </button>
        <button class="bg-white hover:bg-gray-50 text-gray-700 px-3 py-2 rounded-md text-xs font-medium flex items-center justify-center border border-gray-200 transition-colors">
            <svg class="w-3.5 h-3.5 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            Confirmados
        </button>
        <button class="bg-white hover:bg-gray-50 text-gray-700 px-3 py-2 rounded-md text-xs font-medium flex items-center justify-center border border-gray-200 transition-colors">
            <svg class="w-3.5 h-3.5 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            Pendentes
        </button>
        <button class="bg-white hover:bg-gray-50 text-gray-700 px-3 py-2 rounded-md text-xs font-medium flex items-center justify-center border border-gray-200 transition-colors">
            <svg class="w-3.5 h-3.5 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
            </svg>
            Mais filtros
        </button>
    </div>
</div>

<!-- Resumo e Análises Avançadas -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-5">
    <!-- Resumo por Status -->
    <div class="bg-white rounded-lg shadow-sm p-4">
        <div class="flex items-center mb-3">
            <div class="w-8 h-8 rounded-lg bg-blue-50 flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                </svg>
            </div>
            <div>
                <h3 class="text-sm font-medium text-gray-800">Status</h3>
                <p class="text-xs text-gray-500">Hoje</p>
            </div>
        </div>
        <div class="space-y-2">
            <div class="flex justify-between items-center">
                <span class="text-xs text-gray-600">Confirmados</span>
                <span class="text-xs font-medium text-systemBlue">{{ agendamentos|selectattr('data', 'equalto', now.strftime('%d/%m/%Y'))|selectattr('status', 'equalto', 'Confirmado')|list|length }}</span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-xs text-gray-600">Realizados</span>
                <span class="text-xs font-medium text-green-600">{{ agendamentos|selectattr('data', 'equalto', now.strftime('%d/%m/%Y'))|selectattr('status', 'equalto', 'Realizado')|list|length }}</span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-xs text-gray-600">Pendentes</span>
                <span class="text-xs font-medium text-orange-500">{{ agendamentos|selectattr('data', 'equalto', now.strftime('%d/%m/%Y'))|selectattr('status', 'equalto', 'Agendado')|list|length }}</span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-xs text-gray-600">Total</span>
                <span class="text-xs font-medium">{{ agendamentos|selectattr('data', 'equalto', now.strftime('%d/%m/%Y'))|list|length }}</span>
            </div>
        </div>
    </div>

    <!-- Ocupação por Unidade -->
    <div class="bg-white rounded-lg shadow-sm p-4">
        <div class="flex items-center mb-3">
            <div class="w-8 h-8 rounded-lg bg-blue-50 flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
            </div>
            <div>
                <h3 class="text-sm font-medium text-gray-800">Ocupação</h3>
                <p class="text-xs text-gray-500">Por unidade</p>
            </div>
        </div>
        <div class="space-y-2">
            <div>
                <div class="flex justify-between mb-1">
                    <span class="text-xs text-gray-600">Recife</span>
                    <span class="text-xs font-medium text-systemBlue">95%</span>
                </div>
                <div class="h-1.5 bg-gray-100 rounded-full overflow-hidden">
                    <div class="h-full bg-systemBlue" style="width: 95%"></div>
                </div>
            </div>
            <div>
                <div class="flex justify-between mb-1">
                    <span class="text-xs text-gray-600">Morumbi</span>
                    <span class="text-xs font-medium text-systemBlue">92%</span>
                </div>
                <div class="h-1.5 bg-gray-100 rounded-full overflow-hidden">
                    <div class="h-full bg-systemBlue" style="width: 92%"></div>
                </div>
            </div>
            <div>
                <div class="flex justify-between mb-1">
                    <span class="text-xs text-gray-600">BRASILIA</span>
                    <span class="text-xs font-medium text-systemBlue">78%</span>
                </div>
                <div class="h-1.5 bg-gray-100 rounded-full overflow-hidden">
                    <div class="h-full bg-systemBlue" style="width: 78%"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Agendamentos por Profissional -->
    <div class="bg-white rounded-lg shadow-sm p-4">
        <div class="flex items-center mb-3">
            <div class="w-8 h-8 rounded-lg bg-blue-50 flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
            </div>
            <div>
                <h3 class="text-sm font-medium text-gray-800">Profissionais</h3>
                <p class="text-xs text-gray-500">Hoje</p>
            </div>
        </div>
        <div class="space-y-2">
            <div class="flex justify-between items-center">
                <span class="text-xs text-gray-600">Dr. Carlos</span>
                <span class="text-xs font-medium">8</span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-xs text-gray-600">Dra. Ana</span>
                <span class="text-xs font-medium">6</span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-xs text-gray-600">Dr. Roberto</span>
                <span class="text-xs font-medium">5</span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-xs text-gray-600">Outros</span>
                <span class="text-xs font-medium">12</span>
            </div>
        </div>
    </div>

    <!-- Horários Disponíveis -->
    <div class="bg-white rounded-lg shadow-sm p-4">
        <div class="flex items-center mb-3">
            <div class="w-8 h-8 rounded-lg bg-blue-50 flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div>
                <h3 class="text-sm font-medium text-gray-800">Horários Disponíveis</h3>
                <p class="text-xs text-gray-500">Próximas 24h</p>
            </div>
        </div>
        <div class="space-y-2">
            <div class="flex justify-between items-center">
                <span class="text-xs text-gray-600">Morumbi</span>
                <span class="text-xs font-medium">3 vagas</span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-xs text-gray-600">BRASILIA</span>
                <span class="text-xs font-medium">2 vagas</span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-xs text-gray-600">Recife</span>
                <span class="text-xs font-medium">1 vaga</span>
            </div>
            <div class="mt-2 pt-2 border-t border-gray-100">
                <a href="#" class="text-xs text-systemBlue hover:text-blue-700 font-medium">Ver todos os horários →</a>
            </div>
        </div>
    </div>
</div>

<!-- Filtros Avançados -->
<div class="bg-white rounded-lg shadow-sm p-4 mb-5">
    <div class="flex justify-between items-center mb-3">
        <div class="flex items-center">
            <svg class="w-4 h-4 text-gray-500 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
            </svg>
            <h2 class="text-sm font-medium text-gray-700">Filtros Avançados</h2>
        </div>
        <button class="text-xs text-systemBlue hover:text-blue-700 transition-colors">Limpar</button>
    </div>

    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-3">
        <div>
            <label for="data" class="block text-xs font-medium text-gray-500 mb-1">Data</label>
            <input type="date" id="data" class="block w-full px-3 py-1.5 text-sm border border-gray-200 focus:outline-none focus:ring-1 focus:ring-systemBlue focus:border-systemBlue rounded-md transition-colors">
        </div>

        <div>
            <label for="unidade" class="block text-xs font-medium text-gray-500 mb-1">Unidade</label>
            <select id="unidade" class="block w-full px-3 py-1.5 text-sm border border-gray-200 focus:outline-none focus:ring-1 focus:ring-systemBlue focus:border-systemBlue rounded-md transition-colors">
                <option value="">Todas</option>
                <option>Morumbi</option>
                <option>BRASILIA</option>
                <option>Recife</option>
                <option>Espinheiro</option>
                <option>CLÍNICA (RL)</option>
            </select>
        </div>

        <div>
            <label for="profissional" class="block text-xs font-medium text-gray-500 mb-1">Profissional</label>
            <select id="profissional" class="block w-full px-3 py-1.5 text-sm border border-gray-200 focus:outline-none focus:ring-1 focus:ring-systemBlue focus:border-systemBlue rounded-md transition-colors">
                <option value="">Todos</option>
                <option>Equipe Amigo - BRUNO LIMA</option>
                <option>Equipe Amigo - Caio Menezes</option>
                <option>Equipe Amigo - Giulia Pedrosa 2</option>
                <option>Rayara Toledo Souza</option>
            </select>
        </div>

        <div>
            <label for="status" class="block text-xs font-medium text-gray-500 mb-1">Status</label>
            <select id="status" class="block w-full px-3 py-1.5 text-sm border border-gray-200 focus:outline-none focus:ring-1 focus:ring-systemBlue focus:border-systemBlue rounded-md transition-colors">
                <option value="">Todos</option>
                <option>Agendado</option>
                <option>Confirmado</option>
                <option>Realizado</option>
                <option>Cancelado</option>
                <option>Faltou</option>
            </select>
        </div>

        <div class="flex items-end">
            <button class="w-full bg-systemBlue hover:bg-blue-600 text-white px-4 py-1.5 rounded-md transition-colors text-sm font-medium">
                Aplicar Filtros
            </button>
        </div>
    </div>
</div>

<!-- Visualizações e Análises -->
<div class="flex mb-3 gap-2">
    <button id="btn-tabela" class="bg-systemBlue text-white px-3 py-1.5 rounded-md text-xs font-medium transition-colors">
        <svg class="w-3.5 h-3.5 inline-block mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
        </svg>
        Tabela
    </button>
    <button id="btn-calendario" class="bg-white hover:bg-gray-50 text-gray-700 px-3 py-1.5 rounded-md text-xs font-medium border border-gray-200 transition-colors">
        <svg class="w-3.5 h-3.5 inline-block mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
        </svg>
        Calendário
    </button>
    <button id="btn-analise" class="bg-white hover:bg-gray-50 text-gray-700 px-3 py-1.5 rounded-md text-xs font-medium border border-gray-200 transition-colors">
        <svg class="w-3.5 h-3.5 inline-block mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
        </svg>
        Análise
    </button>
</div>

<!-- Card de Insight Flutuante - Previsão de Faltas -->
<div class="bg-white rounded-lg shadow-sm p-4 mb-5 border-l-4 border-systemBlue">
    <div class="flex items-center justify-between">
        <div class="flex items-center">
            <svg class="w-5 h-5 text-systemBlue mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
            </svg>
            <span class="text-base font-semibold text-gray-800">Previsão de Faltas</span>
        </div>
        <span class="inline-block bg-blue-100 text-blue-700 text-xs font-medium px-2 py-0.5 rounded-full">Previsão</span>
    </div>

    <div class="mt-3 grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
            <p class="text-xs text-gray-600 mb-1">Pacientes com alto risco de falta hoje:</p>
            <div class="flex items-center">
                <span class="text-xl font-semibold text-gray-800">5</span>
                <span class="ml-2 text-xs text-red-500">+2 vs. média</span>
            </div>
            <p class="text-xs text-gray-500 mt-1">Baseado em histórico e padrões</p>
        </div>

        <div>
            <p class="text-xs text-gray-600 mb-1">Principais fatores de risco:</p>
            <ul class="text-xs text-gray-800 space-y-1">
                <li class="flex items-start">
                    <span class="text-systemBlue mr-1">•</span>
                    <span>Histórico de faltas anteriores (3+)</span>
                </li>
                <li class="flex items-start">
                    <span class="text-systemBlue mr-1">•</span>
                    <span>Agendamento feito há mais de 30 dias</span>
                </li>
            </ul>
        </div>

        <div>
            <p class="text-xs text-gray-600 mb-1">Ação recomendada:</p>
            <p class="text-xs text-gray-800">Enviar confirmação adicional via WhatsApp para os 5 pacientes identificados com risco de falta.</p>
            <button class="mt-2 bg-systemBlue hover:bg-blue-600 text-white px-3 py-1 rounded text-xs transition-colors">
                Enviar confirmações
            </button>
        </div>
    </div>

    <div class="mt-3 pt-2 border-t border-gray-100 flex justify-between items-center">
        <p class="text-xs text-gray-500">A confirmação proativa pode reduzir faltas em até 68%</p>
        <span class="text-xs text-gray-500">Powered by Amigo Intelligence</span>
    </div>
</div>

<!-- Grid de Insights de Agendamento -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-5">
    <!-- Card 1: Otimização de Horários -->
    <div class="bg-white rounded-lg shadow-sm p-4 border-l-4 border-green-500">
        <div class="flex items-center justify-between mb-3">
            <div class="flex items-center">
                <svg class="w-4 h-4 text-green-500 mr-1.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span class="text-sm font-medium text-gray-800">Otimização de Horários</span>
            </div>
            <span class="inline-block bg-green-50 text-green-600 text-xs font-medium px-2 py-0.5 rounded-full">Eficiência</span>
        </div>

        <div class="flex mb-3">
            <div class="w-1/2 pr-2">
                <div class="relative pt-1">
                    <div class="flex items-center justify-between mb-1">
                        <span class="text-xs font-medium text-gray-600">Manhã (8h-12h)</span>
                        <span class="text-xs font-medium text-gray-800">92%</span>
                    </div>
                    <div class="overflow-hidden h-1.5 text-xs flex rounded bg-gray-100">
                        <div style="width: 92%" class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-green-500"></div>
                    </div>
                </div>
                <div class="relative pt-1 mt-2">
                    <div class="flex items-center justify-between mb-1">
                        <span class="text-xs font-medium text-gray-600">Tarde (14h-18h)</span>
                        <span class="text-xs font-medium text-gray-800">85%</span>
                    </div>
                    <div class="overflow-hidden h-1.5 text-xs flex rounded bg-gray-100">
                        <div style="width: 85%" class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-green-500"></div>
                    </div>
                </div>
            </div>
            <div class="w-1/2 pl-2">
                <div class="relative pt-1">
                    <div class="flex items-center justify-between mb-1">
                        <span class="text-xs font-medium text-gray-600">Almoço (12h-14h)</span>
                        <span class="text-xs font-medium text-red-600">38%</span>
                    </div>
                    <div class="overflow-hidden h-1.5 text-xs flex rounded bg-gray-100">
                        <div style="width: 38%" class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-red-400"></div>
                    </div>
                </div>
                <div class="relative pt-1 mt-2">
                    <div class="flex items-center justify-between mb-1">
                        <span class="text-xs font-medium text-gray-600">Noite (18h-20h)</span>
                        <span class="text-xs font-medium text-yellow-600">65%</span>
                    </div>
                    <div class="overflow-hidden h-1.5 text-xs flex rounded bg-gray-100">
                        <div style="width: 65%" class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-yellow-400"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-gray-50 p-2 rounded text-xs text-gray-600">
            <p><strong>Recomendação:</strong> Oferecer desconto de 15% para consultas entre 12h-14h pode aumentar a ocupação em 28% neste horário.</p>
        </div>

        <div class="mt-2 pt-2 border-t border-gray-100 flex justify-between items-center">
            <button class="text-xs text-green-600 hover:text-green-700 font-medium">
                Implementar campanha
            </button>
            <span class="text-xs text-gray-400">Amigo Intelligence</span>
        </div>
    </div>

    <!-- Card 2: Análise de Canais -->
    <div class="bg-white rounded-lg shadow-sm p-4 border-l-4 border-blue-500">
        <div class="flex items-center justify-between mb-3">
            <div class="flex items-center">
                <svg class="w-4 h-4 text-blue-500 mr-1.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M10.34 15.84c-.688-.06-1.386-.09-2.09-.09H7.5a4.5 4.5 0 110-9h.75c.704 0 1.402-.03 2.09-.09m0 9.18c.253.962.584 1.892.985 2.783.247.55.06 1.21-.463 1.511l-.657.38c-.551.318-1.26.117-1.527-.461a20.845 20.845 0 01-1.44-4.282m3.102.069a18.03 18.03 0 01-.59-4.59c0-1.586.205-3.124.59-4.59m0 9.18a23.848 23.848 0 018.835 2.535M10.34 6.66a23.847 23.847 0 008.835-2.535m0 0A23.74 23.74 0 0018.795 3m.38 1.125a23.91 23.91 0 011.014 5.395m-1.014 8.855c-.118.38-.245.754-.38 1.125m.38-1.125a23.91 23.91 0 001.014-5.395m0-3.46c.495.413.811 1.035.811 1.73 0 .695-.316 1.317-.811 1.73m0-3.46a24.347 24.347 0 010 3.46" />
                </svg>
                <span class="text-sm font-medium text-gray-800">Canais de Agendamento</span>
            </div>
            <span class="inline-block bg-blue-50 text-blue-600 text-xs font-medium px-2 py-0.5 rounded-full">Marketing</span>
        </div>

        <div class="mb-3">
            <div class="relative">
                <canvas id="channelsChart" height="120"></canvas>
            </div>
        </div>

        <div class="grid grid-cols-2 gap-2 mb-3">
            <div class="bg-blue-50 rounded p-2">
                <p class="text-xs text-gray-700 font-medium">Canal mais eficiente</p>
                <p class="text-sm text-blue-700 font-semibold">WhatsApp</p>
                <p class="text-xs text-gray-500">Taxa de conversão: 82%</p>
            </div>
            <div class="bg-blue-50 rounded p-2">
                <p class="text-xs text-gray-700 font-medium">Oportunidade</p>
                <p class="text-sm text-blue-700 font-semibold">Instagram</p>
                <p class="text-xs text-gray-500">Crescimento: +28%</p>
            </div>
        </div>

        <div class="mt-2 pt-2 border-t border-gray-100 flex justify-between items-center">
            <button class="text-xs text-blue-600 hover:text-blue-700 font-medium">
                Ver análise completa
            </button>
            <span class="text-xs text-gray-400">Amigo Intelligence</span>
        </div>
    </div>

    <!-- Card 3: Previsão de Faltas -->
    <div class="bg-white rounded-lg shadow-sm p-4 border-l-4 border-purple-500">
        <div class="flex items-center justify-between mb-3">
            <div class="flex items-center">
                <svg class="w-4 h-4 text-purple-500 mr-1.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z" />
                </svg>
                <span class="text-sm font-medium text-gray-800">Previsão de Faltas</span>
            </div>
            <span class="inline-block bg-purple-50 text-purple-600 text-xs font-medium px-2 py-0.5 rounded-full">Previsão</span>
        </div>

        <div class="flex items-center justify-center mb-3">
            <div class="w-24 h-24 relative">
                <svg viewBox="0 0 36 36" class="w-full h-full">
                    <path d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" fill="none" stroke="#eee" stroke-width="3" />
                    <path d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" fill="none" stroke="#a855f7" stroke-width="3" stroke-dasharray="75, 100" />
                    <text x="18" y="20.5" class="text-xs font-semibold" text-anchor="middle" fill="#6b21a8">25%</text>
                </svg>
            </div>
            <div class="ml-4">
                <p class="text-xs text-gray-600 mb-1">Pacientes com alto risco de falta hoje:</p>
                <div class="flex items-center">
                    <span class="text-xl font-semibold text-gray-800">5</span>
                    <span class="ml-2 text-xs text-red-500">+2 vs. média</span>
                </div>
                <p class="text-xs text-gray-500 mt-1">Baseado em histórico e padrões</p>
            </div>
        </div>

        <div class="bg-purple-50 p-2 rounded text-xs text-gray-600 mb-3">
            <p><strong>Ação recomendada:</strong> Enviar confirmação adicional via WhatsApp para os 5 pacientes identificados com risco de falta.</p>
            <button class="mt-2 bg-purple-500 hover:bg-purple-600 text-white px-3 py-1 rounded text-xs transition-colors w-full">
                Enviar confirmações
            </button>
        </div>

        <div class="mt-2 pt-2 border-t border-gray-100 flex justify-between items-center">
            <p class="text-xs text-gray-500">Redução potencial de faltas: <span class="font-medium">68%</span></p>
            <span class="text-xs text-gray-400">Amigo Intelligence</span>
        </div>
    </div>
</div>

<!-- Tabela de Agendamentos -->
<div id="view-tabela" class="bg-white rounded-lg shadow-sm p-4 mb-5">
    <div class="flex justify-between items-center mb-3">
        <div class="flex items-center">
            <svg class="w-4 h-4 text-gray-500 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
            </svg>
            <h2 class="text-sm font-medium text-gray-700">Agendamentos</h2>
        </div>
        <div class="flex gap-2">
            <button class="bg-white hover:bg-gray-50 text-gray-700 px-2 py-1 rounded text-xs border border-gray-200 transition-colors flex items-center">
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                </svg>
                Exportar
            </button>
            <button class="bg-white hover:bg-gray-50 text-gray-700 px-2 py-1 rounded text-xs border border-gray-200 transition-colors flex items-center">
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                </svg>
                Imprimir
            </button>
        </div>
    </div>

    <div class="overflow-x-auto">
        <table class="w-full text-xs">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Código</th>
                    <th class="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Data</th>
                    <th class="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Hora</th>
                    <th class="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Paciente</th>
                    <th class="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Profissional</th>
                    <th class="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Unidade</th>
                    <th class="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Tipo</th>
                    <th class="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Pagamento</th>
                    <th class="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Valor</th>
                    <th class="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Ações</th>
                </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
                {% for agendamento in (agendamentos|selectattr('data', 'equalto', now.strftime('%d/%m/%Y'))|list + agendamentos|rejectattr('data', 'equalto', now.strftime('%d/%m/%Y'))|list)[:15] %}
                <tr class="hover:bg-gray-50">
                    <td class="px-3 py-2 text-gray-800">{{ agendamento.codigo }}</td>
                    <td class="px-3 py-2 text-gray-800">{{ agendamento.data }}</td>
                    <td class="px-3 py-2 text-gray-800">{{ agendamento.hora }}</td>
                    <td class="px-3 py-2 text-gray-800">{{ agendamento.paciente }}</td>
                    <td class="px-3 py-2 text-gray-800">{{ agendamento.profissional }}</td>
                    <td class="px-3 py-2 text-gray-800">{{ agendamento.unidade }}</td>
                    <td class="px-3 py-2 text-gray-800">{{ agendamento.tipo_atendimento }}</td>
                    <td class="px-3 py-2 text-gray-800">{{ agendamento.forma_pagamento }}</td>
                    <td class="px-3 py-2 text-gray-800">R$ {{ "%.2f"|format(agendamento.valor) }}</td>
                    <td class="px-3 py-2">
                        <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium
                        {% if agendamento.status == 'Realizado' %}
                            bg-blue-50 text-systemBlue
                        {% elif agendamento.status == 'Agendado' %}
                            bg-orange-50 text-orange-600
                        {% elif agendamento.status == 'Confirmado' %}
                            bg-green-50 text-green-600
                        {% elif agendamento.status == 'Cancelado' %}
                            bg-gray-100 text-gray-600
                        {% elif agendamento.status == 'Faltou' %}
                            bg-red-50 text-red-600
                        {% endif %}
                        ">
                            {{ agendamento.status }}
                        </span>
                    </td>
                    <td class="px-3 py-2">
                        <div class="flex gap-1">
                            <button class="p-1 text-gray-500 hover:text-systemBlue hover:bg-blue-50 rounded transition-colors">
                                <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                            </button>
                            <button class="p-1 text-gray-500 hover:text-systemBlue hover:bg-blue-50 rounded transition-colors">
                                <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                            </button>
                            <button class="p-1 text-gray-500 hover:text-systemBlue hover:bg-blue-50 rounded transition-colors">
                                <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </button>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Paginação -->
    <div class="flex justify-between items-center mt-3 text-xs">
        <div class="text-gray-500">
            Mostrando 8 de {{ agendamentos|length }} agendamentos
        </div>
        <div class="flex gap-2">
            <button class="px-2 py-1 rounded bg-white border border-gray-200 text-gray-500 hover:bg-gray-50 transition-colors">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>
            <button class="px-2 py-1 rounded bg-systemBlue text-white">1</button>
            <button class="px-2 py-1 rounded bg-white border border-gray-200 text-gray-500 hover:bg-gray-50 transition-colors">2</button>
            <button class="px-2 py-1 rounded bg-white border border-gray-200 text-gray-500 hover:bg-gray-50 transition-colors">3</button>
            <button class="px-2 py-1 rounded bg-white border border-gray-200 text-gray-500 hover:bg-gray-50 transition-colors">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </button>
        </div>
    </div>
</div>

<!-- Visualização de Calendário -->
<div id="view-calendario" class="bg-white rounded-view p-6 border border-gray-200 hidden">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-base font-semibold text-gray-800">Calendário de Agendamentos</h2>
        <div class="flex space-x-2">
            <button class="bg-white hover:bg-gray-50 text-gray-700 px-3 py-1 rounded-md transition duration-150 ease-in-out text-xs border border-gray-200">
                Exportar
            </button>
            <button class="bg-white hover:bg-gray-50 text-gray-700 px-3 py-1 rounded-md transition duration-150 ease-in-out text-xs border border-gray-200">
                Imprimir
            </button>
        </div>
    </div>

    <!-- Navegação do Calendário -->
    <div class="flex justify-between items-center mb-4">
        <div class="flex items-center space-x-2">
            <button class="p-1 rounded-md bg-white hover:bg-gray-50 border border-gray-200">
                <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>
            <h3 class="text-lg font-medium text-gray-800">Julho 2023</h3>
            <button class="p-1 rounded-md bg-white hover:bg-gray-50 border border-gray-200">
                <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </button>
        </div>
        <div class="flex space-x-2">
            <button class="bg-systemBlue text-white px-3 py-1 rounded-md transition duration-150 ease-in-out text-xs">
                Hoje
            </button>
            <button class="bg-white hover:bg-gray-50 text-gray-700 px-3 py-1 rounded-md transition duration-150 ease-in-out text-xs border border-gray-200">
                Mês
            </button>
            <button class="bg-white hover:bg-gray-50 text-gray-700 px-3 py-1 rounded-md transition duration-150 ease-in-out text-xs border border-gray-200">
                Semana
            </button>
            <button class="bg-white hover:bg-gray-50 text-gray-700 px-3 py-1 rounded-md transition duration-150 ease-in-out text-xs border border-gray-200">
                Dia
            </button>
        </div>
    </div>

    <!-- Legenda do Calendário -->
    <div class="flex items-center justify-end mb-4">
        <div class="flex items-center mr-4">
            <span class="inline-block w-3 h-3 bg-systemBlue rounded-full mr-1"></span>
            <span class="text-xs text-gray-500">Agendado</span>
        </div>
        <div class="flex items-center mr-4">
            <span class="inline-block w-3 h-3 bg-blue-300 rounded-full mr-1"></span>
            <span class="text-xs text-gray-500">Confirmado</span>
        </div>
        <div class="flex items-center mr-4">
            <span class="inline-block w-3 h-3 bg-gray-400 rounded-full mr-1"></span>
            <span class="text-xs text-gray-500">Realizado</span>
        </div>
        <div class="flex items-center">
            <span class="inline-block w-3 h-3 bg-gray-300 rounded-full mr-1"></span>
            <span class="text-xs text-gray-500">Cancelado/Faltou</span>
        </div>
    </div>

    <!-- Grade do Calendário -->
    <div class="border border-gray-200 rounded-md overflow-hidden">
        <!-- Cabeçalho dos dias da semana -->
        <div class="grid grid-cols-7 bg-gray-50">
            <div class="py-2 text-center text-xs font-medium text-gray-500">Dom</div>
            <div class="py-2 text-center text-xs font-medium text-gray-500">Seg</div>
            <div class="py-2 text-center text-xs font-medium text-gray-500">Ter</div>
            <div class="py-2 text-center text-xs font-medium text-gray-500">Qua</div>
            <div class="py-2 text-center text-xs font-medium text-gray-500">Qui</div>
            <div class="py-2 text-center text-xs font-medium text-gray-500">Sex</div>
            <div class="py-2 text-center text-xs font-medium text-gray-500">Sáb</div>
        </div>

        <!-- Dias do mês -->
        <div class="grid grid-cols-7 divide-x divide-y divide-gray-200">
            <!-- Semana 1 -->
            <div class="h-24 p-1 bg-gray-50">
                <span class="text-xs text-gray-400">25</span>
            </div>
            <div class="h-24 p-1 bg-gray-50">
                <span class="text-xs text-gray-400">26</span>
            </div>
            <div class="h-24 p-1 bg-gray-50">
                <span class="text-xs text-gray-400">27</span>
            </div>
            <div class="h-24 p-1 bg-gray-50">
                <span class="text-xs text-gray-400">28</span>
            </div>
            <div class="h-24 p-1 bg-gray-50">
                <span class="text-xs text-gray-400">29</span>
            </div>
            <div class="h-24 p-1 bg-gray-50">
                <span class="text-xs text-gray-400">30</span>
            </div>
            <div class="h-24 p-1 relative">
                <span class="text-xs text-gray-700">1</span>
                <div class="absolute top-6 left-1 right-1">
                    <div class="text-xs p-1 mb-1 bg-blue-50 text-systemBlue rounded truncate">3 agendamentos</div>
                </div>
            </div>

            <!-- Semana 2 -->
            <div class="h-24 p-1 relative">
                <span class="text-xs text-gray-700">2</span>
            </div>
            <div class="h-24 p-1 relative">
                <span class="text-xs text-gray-700">3</span>
                <div class="absolute top-6 left-1 right-1">
                    <div class="text-xs p-1 mb-1 bg-blue-50 text-systemBlue rounded truncate">5 agendamentos</div>
                </div>
            </div>
            <div class="h-24 p-1 relative">
                <span class="text-xs text-gray-700">4</span>
                <div class="absolute top-6 left-1 right-1">
                    <div class="text-xs p-1 mb-1 bg-blue-50 text-systemBlue rounded truncate">7 agendamentos</div>
                </div>
            </div>
            <div class="h-24 p-1 relative">
                <span class="text-xs text-gray-700">5</span>
                <div class="absolute top-6 left-1 right-1">
                    <div class="text-xs p-1 mb-1 bg-blue-50 text-systemBlue rounded truncate">4 agendamentos</div>
                </div>
            </div>
            <div class="h-24 p-1 relative">
                <span class="text-xs text-gray-700">6</span>
                <div class="absolute top-6 left-1 right-1">
                    <div class="text-xs p-1 mb-1 bg-blue-50 text-systemBlue rounded truncate">6 agendamentos</div>
                </div>
            </div>
            <div class="h-24 p-1 relative">
                <span class="text-xs text-gray-700">7</span>
                <div class="absolute top-6 left-1 right-1">
                    <div class="text-xs p-1 mb-1 bg-blue-50 text-systemBlue rounded truncate">2 agendamentos</div>
                </div>
            </div>
            <div class="h-24 p-1 relative">
                <span class="text-xs text-gray-700">8</span>
            </div>

            <!-- Semana 3 (com dia atual destacado) -->
            <div class="h-24 p-1 relative">
                <span class="text-xs text-gray-700">9</span>
            </div>
            <div class="h-24 p-1 relative">
                <span class="text-xs text-gray-700">10</span>
                <div class="absolute top-6 left-1 right-1">
                    <div class="text-xs p-1 mb-1 bg-blue-50 text-systemBlue rounded truncate">3 agendamentos</div>
                </div>
            </div>
            <div class="h-24 p-1 relative">
                <span class="text-xs text-gray-700">11</span>
            </div>
            <div class="h-24 p-1 relative">
                <span class="text-xs text-gray-700">12</span>
                <div class="absolute top-6 left-1 right-1">
                    <div class="text-xs p-1 mb-1 bg-blue-50 text-systemBlue rounded truncate">5 agendamentos</div>
                </div>
            </div>
            <div class="h-24 p-1 relative">
                <span class="text-xs text-gray-700">13</span>
            </div>
            <div class="h-24 p-1 relative">
                <span class="text-xs text-gray-700">14</span>
                <div class="absolute top-6 left-1 right-1">
                    <div class="text-xs p-1 mb-1 bg-blue-50 text-systemBlue rounded truncate">4 agendamentos</div>
                </div>
            </div>
            <div class="h-24 p-1 relative bg-blue-50">
                <span class="text-xs font-medium text-systemBlue">15</span>
                <div class="absolute top-6 left-1 right-1">
                    <div class="text-xs p-1 mb-1 bg-systemBlue text-white rounded truncate">8 agendamentos</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Análise de Padrões -->
<div id="view-analise" class="bg-white rounded-view p-6 border border-gray-200 hidden">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-base font-semibold text-gray-800">Análise de Padrões de Agendamento</h2>
        <div class="flex items-center">
            <div class="flex items-center">
                <svg class="w-4 h-4 text-systemBlue mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
                </svg>
                <span class="text-xs text-gray-800 font-medium">Amigo Intelligence</span>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- Distribuição por Dia da Semana -->
        <div class="bg-white border border-gray-200 rounded-md p-4">
            <h3 class="text-sm font-semibold text-gray-800 mb-3">Distribuição por Dia da Semana</h3>
            <div class="h-64">
                <canvas id="diaSemanaChart"></canvas>
            </div>
            <div class="mt-3 text-xs text-gray-500 bg-gray-50 p-3 rounded-md">
                <p><strong class="text-gray-700">Insight:</strong> Terça e quinta-feira são os dias mais ocupados. Considere redistribuir agendamentos para segunda e quarta.</p>
            </div>
        </div>

        <!-- Distribuição por Horário -->
        <div class="bg-white border border-gray-200 rounded-md p-4">
            <h3 class="text-sm font-semibold text-gray-800 mb-3">Distribuição por Horário</h3>
            <div class="h-64">
                <canvas id="horarioChart"></canvas>
            </div>
            <div class="mt-3 text-xs text-gray-500 bg-gray-50 p-3 rounded-md">
                <p><strong class="text-gray-700">Insight:</strong> Picos de agendamento entre 9-11h e 14-16h. Horários entre 12-14h estão subutilizados.</p>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- Previsão de Demanda -->
        <div class="bg-white border border-gray-200 rounded-md p-4">
            <h3 class="text-sm font-semibold text-gray-800 mb-3">Previsão de Demanda (Próximos 30 dias)</h3>
            <div class="h-64">
                <canvas id="previsaoDemandaChart"></canvas>
            </div>
            <div class="mt-3 text-xs text-gray-500 bg-gray-50 p-3 rounded-md">
                <p><strong class="text-gray-700">Insight:</strong> Espera-se um aumento de 22% na demanda nas próximas 2 semanas devido à sazonalidade. Recomenda-se otimizar a agenda dos especialistas mais requisitados.</p>
            </div>
        </div>

        <!-- Correlação entre Variáveis -->
        <div class="bg-white border border-gray-200 rounded-md p-4">
            <h3 class="text-sm font-semibold text-gray-800 mb-3">Fatores que Influenciam Cancelamentos</h3>
            <div class="space-y-3">
                <div>
                    <div class="flex justify-between mb-1">
                        <span class="text-xs font-medium text-gray-700">Tempo de espera > 15 min</span>
                        <span class="text-xs font-medium text-systemBlue">+68%</span>
                    </div>
                    <div class="h-2 bg-gray-100 rounded-full overflow-hidden">
                        <div class="h-full bg-systemBlue" style="width: 68%"></div>
                    </div>
                </div>
                <div>
                    <div class="flex justify-between mb-1">
                        <span class="text-xs font-medium text-gray-700">Reagendamento prévio</span>
                        <span class="text-xs font-medium text-systemBlue">+42%</span>
                    </div>
                    <div class="h-2 bg-gray-100 rounded-full overflow-hidden">
                        <div class="h-full bg-systemBlue" style="width: 42%"></div>
                    </div>
                </div>
                <div>
                    <div class="flex justify-between mb-1">
                        <span class="text-xs font-medium text-gray-700">Horário após 17h</span>
                        <span class="text-xs font-medium text-systemBlue">+35%</span>
                    </div>
                    <div class="h-2 bg-gray-100 rounded-full overflow-hidden">
                        <div class="h-full bg-systemBlue" style="width: 35%"></div>
                    </div>
                </div>
                <div>
                    <div class="flex justify-between mb-1">
                        <span class="text-xs font-medium text-gray-700">Distância > 10km</span>
                        <span class="text-xs font-medium text-systemBlue">+28%</span>
                    </div>
                    <div class="h-2 bg-gray-100 rounded-full overflow-hidden">
                        <div class="h-full bg-systemBlue" style="width: 28%"></div>
                    </div>
                </div>
            </div>
            <div class="mt-4 text-xs text-gray-500 bg-gray-50 p-3 rounded-md">
                <p><strong class="text-gray-700">Recomendação:</strong> Implementar sistema de confirmação 24h antes para pacientes com fatores de risco de cancelamento.</p>
            </div>
        </div>
    </div>

    <!-- Novas Análises de Agendamentos -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- Tempo Médio Antes da Consulta -->
        <div class="bg-white border border-gray-200 rounded-md p-4">
            <h3 class="text-sm font-semibold text-gray-800 mb-3">Tempo Médio de Espera Antes da Consulta</h3>
            <div class="h-64">
                <canvas id="tempoEsperaChart"></canvas>
            </div>
            <div class="mt-3 text-xs text-gray-500 bg-gray-50 p-3 rounded-md">
                <p><strong class="text-gray-700">Insight:</strong> O tempo médio de espera é de 12 minutos, com picos nas segundas-feiras (18 min). A clínica mantém um padrão de atendimento eficiente, mas recomenda-se otimizar o fluxo no início da semana.</p>
            </div>
        </div>

        <!-- Agendamentos por Canal -->
        <div class="bg-white border border-gray-200 rounded-md p-4">
            <h3 class="text-sm font-semibold text-gray-800 mb-3">Agendamentos por Canal</h3>
            <div class="h-64">
                <canvas id="canalAgendamentoChart"></canvas>
            </div>
            <div class="mt-3 text-xs text-gray-500 bg-gray-50 p-3 rounded-md">
                <p><strong class="text-gray-700">Insight:</strong> O WhatsApp é o canal mais utilizado (48%), seguido pelo site (32%). A clínica tem excelente presença digital, com 80% dos agendamentos realizados por canais online.</p>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Média de Agendamentos por Paciente -->
        <div class="bg-white border border-gray-200 rounded-md p-4">
            <h3 class="text-sm font-semibold text-gray-800 mb-3">Média de Agendamentos por Paciente</h3>
            <div class="h-64">
                <canvas id="mediaAgendamentosChart"></canvas>
            </div>
            <div class="mt-3 text-xs text-gray-500 bg-gray-50 p-3 rounded-md">
                <p><strong class="text-gray-700">Insight:</strong> Pacientes de tratamentos contínuos têm média de 5,8 agendamentos por mês. A alta frequência reflete a qualidade do acompanhamento e a fidelidade dos pacientes.</p>
            </div>
        </div>

        <!-- Taxa de Presença nos Atendimentos -->
        <div class="bg-white border border-gray-200 rounded-md p-4">
            <h3 class="text-sm font-semibold text-gray-800 mb-3">Taxa de Presença nos Atendimentos</h3>
            <div class="h-64">
                <canvas id="taxaPresencaChart"></canvas>
            </div>
            <div class="mt-3 text-xs text-gray-500 bg-gray-50 p-3 rounded-md">
                <p><strong class="text-gray-700">Insight:</strong> A taxa de presença é de 94% após a implementação de confirmações por WhatsApp. Pacientes que confirmam têm 98% de chance de comparecer, um dos melhores índices do setor.</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Alternação entre visualizações
        const btnTabela = document.getElementById('btn-tabela');
        const btnCalendario = document.getElementById('btn-calendario');
        const btnAnalise = document.getElementById('btn-analise');

        const viewTabela = document.getElementById('view-tabela');
        const viewCalendario = document.getElementById('view-calendario');
        const viewAnalise = document.getElementById('view-analise');

        btnTabela.addEventListener('click', function() {
            viewTabela.classList.remove('hidden');
            viewCalendario.classList.add('hidden');
            viewAnalise.classList.add('hidden');

            btnTabela.classList.remove('bg-white', 'hover:bg-gray-50', 'text-gray-800', 'border', 'border-gray-200');
            btnTabela.classList.add('bg-systemBlue', 'text-white');

            btnCalendario.classList.remove('bg-systemBlue', 'text-white');
            btnCalendario.classList.add('bg-white', 'hover:bg-gray-50', 'text-gray-800', 'border', 'border-gray-200');

            btnAnalise.classList.remove('bg-systemBlue', 'text-white');
            btnAnalise.classList.add('bg-white', 'hover:bg-gray-50', 'text-gray-800', 'border', 'border-gray-200');
        });

        btnCalendario.addEventListener('click', function() {
            viewTabela.classList.add('hidden');
            viewCalendario.classList.remove('hidden');
            viewAnalise.classList.add('hidden');

            btnTabela.classList.remove('bg-systemBlue', 'text-white');
            btnTabela.classList.add('bg-white', 'hover:bg-gray-50', 'text-gray-800', 'border', 'border-gray-200');

            btnCalendario.classList.remove('bg-white', 'hover:bg-gray-50', 'text-gray-800', 'border', 'border-gray-200');
            btnCalendario.classList.add('bg-systemBlue', 'text-white');

            btnAnalise.classList.remove('bg-systemBlue', 'text-white');
            btnAnalise.classList.add('bg-white', 'hover:bg-gray-50', 'text-gray-800', 'border', 'border-gray-200');
        });

        btnAnalise.addEventListener('click', function() {
            viewTabela.classList.add('hidden');
            viewCalendario.classList.add('hidden');
            viewAnalise.classList.remove('hidden');

            btnTabela.classList.remove('bg-systemBlue', 'text-white');
            btnTabela.classList.add('bg-white', 'hover:bg-gray-50', 'text-gray-800', 'border', 'border-gray-200');

            btnCalendario.classList.remove('bg-systemBlue', 'text-white');
            btnCalendario.classList.add('bg-white', 'hover:bg-gray-50', 'text-gray-800', 'border', 'border-gray-200');

            btnAnalise.classList.remove('bg-white', 'hover:bg-gray-50', 'text-gray-800', 'border', 'border-gray-200');
            btnAnalise.classList.add('bg-systemBlue', 'text-white');

            // Renderizar gráficos de análise quando a visualização é exibida
            renderizarGraficosAnalise();
        });

        // Função para renderizar os gráficos de análise
        function renderizarGraficosAnalise() {
            // Configuração global para cores consistentes
            Chart.defaults.color = '#6B7280'; // text-gray-500
            Chart.defaults.borderColor = '#E5E7EB'; // border-gray-200

            // Gráfico de distribuição por dia da semana
            const diaSemanaCtx = document.getElementById('diaSemanaChart').getContext('2d');
            new Chart(diaSemanaCtx, {
                type: 'bar',
                data: {
                    labels: ['Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado', 'Domingo'],
                    datasets: [{
                        label: 'Agendamentos',
                        data: [42, 65, 38, 58, 45, 25, 10],
                        backgroundColor: '#0A84FF', // systemBlue
                        borderColor: '#0A84FF',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: '#F3F4F6' // gray-100
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });

            // Gráfico de distribuição por horário
            const horarioCtx = document.getElementById('horarioChart').getContext('2d');
            new Chart(horarioCtx, {
                type: 'line',
                data: {
                    labels: ['8h', '9h', '10h', '11h', '12h', '13h', '14h', '15h', '16h', '17h', '18h'],
                    datasets: [{
                        label: 'Agendamentos',
                        data: [15, 28, 32, 25, 12, 10, 22, 30, 28, 20, 8],
                        backgroundColor: 'rgba(10, 132, 255, 0.1)', // systemBlue com transparência
                        borderColor: '#0A84FF',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: '#F3F4F6' // gray-100
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });

            // Gráfico de previsão de demanda
            const previsaoCtx = document.getElementById('previsaoDemandaChart').getContext('2d');
            new Chart(previsaoCtx, {
                type: 'line',
                data: {
                    labels: ['Semana 1', 'Semana 2', 'Semana 3', 'Semana 4', 'Semana 5', 'Semana 6'],
                    datasets: [{
                        label: 'Histórico',
                        data: [280, 295, 310, 305, 315, 325],
                        backgroundColor: 'rgba(10, 132, 255, 0.1)', // systemBlue com transparência
                        borderColor: '#0A84FF',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true
                    }, {
                        label: 'Previsão',
                        data: [325, 345, 375, 395, null, null],
                        backgroundColor: 'rgba(156, 163, 175, 0.1)', // gray-400 com transparência
                        borderColor: '#9CA3AF', // gray-400
                        borderWidth: 2,
                        borderDash: [5, 5],
                        tension: 0.4,
                        fill: false
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                color: '#4B5563' // gray-600
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: '#F3F4F6' // gray-100
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });

            // Novos gráficos adicionados

            // Gráfico de canais de agendamento para o novo card
            if (document.getElementById('channelsChart')) {
                const channelsCtx = document.getElementById('channelsChart').getContext('2d');
                new Chart(channelsCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['WhatsApp', 'Site', 'Telefone', 'Instagram', 'Presencial'],
                        datasets: [{
                            data: [48, 22, 15, 10, 5],
                            backgroundColor: [
                                'rgba(37, 99, 235, 0.8)',
                                'rgba(59, 130, 246, 0.7)',
                                'rgba(96, 165, 250, 0.6)',
                                'rgba(147, 197, 253, 0.5)',
                                'rgba(191, 219, 254, 0.4)'
                            ],
                            borderColor: [
                                'rgba(37, 99, 235, 1)',
                                'rgba(59, 130, 246, 1)',
                                'rgba(96, 165, 250, 1)',
                                'rgba(147, 197, 253, 1)',
                                'rgba(191, 219, 254, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'right',
                                labels: {
                                    boxWidth: 10,
                                    font: {
                                        size: 10
                                    }
                                }
                            }
                        }
                    }
                });
            }

            // Gráfico de tempo médio de espera antes da consulta
            if (document.getElementById('tempoEsperaChart')) {
                const tempoEsperaCtx = document.getElementById('tempoEsperaChart').getContext('2d');
                new Chart(tempoEsperaCtx, {
                    type: 'bar',
                    data: {
                        labels: ['Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado'],
                        datasets: [{
                            label: 'Tempo médio (minutos)',
                            data: [18, 14, 10, 11, 12, 8],
                            backgroundColor: 'rgba(10, 132, 255, 0.7)',
                            borderColor: '#0A84FF',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return `${context.raw} minutos`;
                                    }
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: 'Minutos'
                                },
                                grid: {
                                    color: '#F3F4F6'
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                }
                            }
                        }
                    }
                });
            }

            // Gráfico de agendamentos por canal
            if (document.getElementById('canalAgendamentoChart')) {
                const canalAgendamentoCtx = document.getElementById('canalAgendamentoChart').getContext('2d');
                new Chart(canalAgendamentoCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['WhatsApp', 'Site', 'Telefone', 'Presencial', 'App'],
                        datasets: [{
                            data: [48, 32, 10, 5, 5],
                            backgroundColor: [
                                'rgba(10, 132, 255, 0.8)',
                                'rgba(52, 199, 89, 0.8)',
                                'rgba(255, 149, 0, 0.8)',
                                'rgba(175, 82, 222, 0.8)',
                                'rgba(88, 86, 214, 0.8)'
                            ],
                            borderColor: [
                                '#0A84FF',
                                '#34C759',
                                '#FF9500',
                                '#AF52DE',
                                '#5856D6'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'right',
                                labels: {
                                    color: '#4B5563',
                                    font: {
                                        size: 11
                                    }
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return `${context.label}: ${context.raw}%`;
                                    }
                                }
                            }
                        }
                    }
                });
            }

            // Gráfico de média de agendamentos por paciente
            if (document.getElementById('mediaAgendamentosChart')) {
                const mediaAgendamentosCtx = document.getElementById('mediaAgendamentosChart').getContext('2d');
                new Chart(mediaAgendamentosCtx, {
                    type: 'bar',
                    data: {
                        labels: ['Tratamento contínuo', 'Consulta de rotina', 'Procedimento único', 'Emergência'],
                        datasets: [{
                            label: 'Média de agendamentos por mês',
                            data: [5.8, 2.3, 1.2, 1.5],
                            backgroundColor: 'rgba(10, 132, 255, 0.7)',
                            borderColor: '#0A84FF',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        indexAxis: 'y',
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            x: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: 'Média de agendamentos'
                                },
                                grid: {
                                    color: '#F3F4F6'
                                }
                            },
                            y: {
                                grid: {
                                    display: false
                                }
                            }
                        }
                    }
                });
            }

            // Gráfico de taxa de presença nos atendimentos
            if (document.getElementById('taxaPresencaChart')) {
                const taxaPresencaCtx = document.getElementById('taxaPresencaChart').getContext('2d');
                new Chart(taxaPresencaCtx, {
                    type: 'line',
                    data: {
                        labels: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago'],
                        datasets: [{
                            label: 'Sem confirmação',
                            data: [78, 80, 79, 81, 82, 83, 84, 85],
                            backgroundColor: 'rgba(255, 149, 0, 0.1)',
                            borderColor: '#FF9500',
                            borderWidth: 2,
                            tension: 0.4,
                            fill: true
                        }, {
                            label: 'Com confirmação WhatsApp',
                            data: [88, 90, 92, 94, 95, 96, 97, 98],
                            backgroundColor: 'rgba(52, 199, 89, 0.1)',
                            borderColor: '#34C759',
                            borderWidth: 2,
                            tension: 0.4,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'top',
                                labels: {
                                    color: '#4B5563'
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return `${context.dataset.label}: ${context.raw}%`;
                                    }
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: 'Taxa de presença (%)'
                                },
                                grid: {
                                    color: '#F3F4F6'
                                },
                                min: 75,
                                max: 100
                            },
                            x: {
                                grid: {
                                    display: false
                                }
                            }
                        }
                    }
                });
            }
        }
    });
</script>
{% endblock %}
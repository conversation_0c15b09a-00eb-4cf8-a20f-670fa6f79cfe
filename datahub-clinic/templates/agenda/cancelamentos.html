{% extends 'base.html' %}

{% block title %}Cancelamentos - Amigo DataHub{% endblock %}

{% block header %}Cancelamentos e Faltas{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="bg-gradient-to-r from-blue-50 to-white rounded-view p-8 border border-gray-200 mb-6 overflow-hidden relative">
    <!-- Background Elements -->
    <div class="absolute top-0 right-0 w-full h-full opacity-10 overflow-hidden">
        <svg class="absolute top-10 right-10 w-64 h-64 text-systemBlue" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <svg class="absolute bottom-10 left-1/4 w-32 h-32 text-systemBlue" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
        </svg>
    </div>

    <div class="flex flex-col md:flex-row items-center relative z-10">
        <div class="md:w-2/3 mb-6 md:mb-0 md:pr-8">
            <div class="inline-block bg-systemBlue text-white text-xs font-semibold px-3 py-1 rounded-full mb-3">AGENDA</div>
            <h1 class="text-3xl font-bold text-gray-800 mb-4">Análise de Cancelamentos e Faltas</h1>
            <p class="text-gray-600 mb-6">Identifique padrões, reduza taxas de cancelamento e minimize o impacto financeiro com análises detalhadas e recomendações inteligentes baseadas em dados históricos.</p>
            <div class="grid grid-cols-3 gap-4 mb-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">{{ taxa_cancelamento }}%</div>
                    <div class="text-xs text-gray-500">Taxa de cancelamento</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">{{ cancelamentos|length }}</div>
                    <div class="text-xs text-gray-500">Total de cancelamentos</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">R$ {{ (cancelamentos|length * 150)|round|int }}</div>
                    <div class="text-xs text-gray-500">Impacto financeiro</div>
                </div>
            </div>
            <div class="flex flex-wrap gap-3">
                <button class="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    Gerar Relatório
                </button>
                <button class="bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-md text-sm border border-gray-200 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                    </svg>
                    Filtrar Dados
                </button>
            </div>
        </div>
        <div class="md:w-1/3 flex justify-center">
            <div class="w-64 h-64 bg-white rounded-full shadow-lg flex items-center justify-center p-4 relative">
                <div class="absolute inset-0 rounded-full border-8 border-blue-100"></div>
                <div class="absolute inset-0 rounded-full border-8 border-systemBlue border-opacity-70" style="clip-path: polygon(50% 50%, 100% 0, 100% 50%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-300 border-opacity-70" style="clip-path: polygon(50% 50%, 100% 50%, 100% 100%, 50% 100%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-200 border-opacity-70" style="clip-path: polygon(50% 50%, 50% 100%, 0 100%, 0 50%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-400 border-opacity-70" style="clip-path: polygon(50% 50%, 0 50%, 0 0, 50% 0);"></div>
                <div class="z-10 text-center">
                    <div class="text-3xl font-bold text-gray-800">-25%</div>
                    <div class="text-sm text-gray-500">Redução projetada</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Resumo e Insights -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <!-- Total de Cancelamentos -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-gray-800">Total de Cancelamentos</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">{{ cancelamentos|length }}</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Cancelamentos no período.</span>
            <span class="ml-auto text-gray-800 font-medium">+8%</span>
        </div>
    </div>

    <!-- Taxa de Cancelamento -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-gray-800">Taxa de Cancelamento</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">{{ taxa_cancelamento }}%</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Percentual de agendamentos cancelados.</span>
            <span class="ml-auto text-gray-800 font-medium">+2.5%</span>
        </div>
    </div>

    <!-- Motivo Principal -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-gray-800">Motivo Principal</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">Reagendamento</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Motivo mais frequente de cancelamento.</span>
            <span class="ml-auto text-systemBlue font-medium">42%</span>
        </div>
    </div>

    <!-- Impacto Financeiro -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex justify-between items-center mb-2">
            <p class="text-sm font-semibold text-gray-800">Impacto Financeiro</p>
            <div class="flex items-center">
                <svg class="w-4 h-4 text-systemBlue mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
                </svg>
                <span class="text-xs text-gray-800 font-medium">Amigo Intelligence</span>
            </div>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">R$ {{ (cancelamentos|length * 150)|round|int }}</p>
        <div class="flex justify-between text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Valor estimado de perda por cancelamentos</span>
            <span class="text-gray-800">+12%</span>
        </div>
    </div>
</div>

<!-- Insights com IA -->
<div class="mb-5">
    <div class="flex items-center justify-between mb-4">
        <div class="flex items-center">
            <svg class="w-5 h-5 text-systemBlue mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
            </svg>
            <h2 class="text-base font-semibold text-gray-800">Amigo Intelligence</h2>
        </div>
        <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">Atualizado agora</span>
    </div>

    <div id="insights-container" class="grid grid-cols-1 md:grid-cols-3 gap-3">
        {% for insight in insights %}
            {% with
                title=insight.title,
                description=insight.description,
                insight_type=insight.insight_type,
                content=insight.content,
                category=insight.category
            %}
                {% include 'components/ai_insight_widget.html' %}
            {% endwith %}
        {% endfor %}
    </div>
</div>

<script>
    // Carregar insights dinamicamente após 5 segundos (para demonstração)
    setTimeout(() => {
        // Contexto específico da página de cancelamentos
        const pageContext = {
            page_title: "Cancelamentos",
            page_description: "Análise de cancelamentos e faltas, com foco em redução e prevenção",
            key_metrics: {
                "Total de Cancelamentos": document.querySelector('.kpi-total-cancelamentos .value')?.textContent || "245",
                "Taxa de Cancelamento": document.querySelector('.kpi-taxa-cancelamento .value')?.textContent || "18%",
                "Cancelamentos Evitáveis": document.querySelector('.kpi-evitaveis .value')?.textContent || "65%"
            },
            analysis_focus: "Redução de cancelamentos e faltas",
            page_elements: [
                "Motivos de Cancelamento",
                "Cancelamentos por Profissional",
                "Evolução Mensal",
                "Horários com Maior Incidência"
            ]
        };

        loadInsights('agenda', 'cancelamentos', 'insights-container', 3, ['list', 'text', 'stat'], pageContext);
    }, 5000);
</script>

<!-- Filtros -->
<div class="bg-white rounded-view p-4 mb-6 border border-gray-200">
    <div class="flex flex-wrap gap-4">
        <div class="w-full md:w-auto">
            <label for="data_inicio" class="block text-xs font-medium text-label-secondary mb-1">Data Início</label>
            <input type="date" id="data_inicio" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
        </div>
        <div class="w-full md:w-auto">
            <label for="data_fim" class="block text-xs font-medium text-label-secondary mb-1">Data Fim</label>
            <input type="date" id="data_fim" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
        </div>
        <div class="w-full md:w-auto">
            <label for="unidade" class="block text-xs font-medium text-label-secondary mb-1">Unidade</label>
            <select id="unidade" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
                <option value="">Todas</option>
                <option>Morumbi</option>
                <option>BRASILIA</option>
                <option>Recife</option>
                <option>Espinheiro</option>
                <option>CLÍNICA (RL)</option>
            </select>
        </div>
        <div class="w-full md:w-auto">
            <label for="motivo" class="block text-xs font-medium text-label-secondary mb-1">Motivo</label>
            <select id="motivo" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
                <option value="">Todos</option>
                <option>Solicitação do paciente</option>
                <option>Indisponibilidade do médico</option>
                <option>Reagendamento</option>
                <option>Outros</option>
            </select>
        </div>
        <div class="w-full md:w-auto flex items-end">
            <button class="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-control transition duration-150 ease-in-out text-sm">
                Filtrar
            </button>
        </div>
    </div>
</div>

<!-- Análises Avançadas -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Cancelamentos por Motivo -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold">Cancelamentos por Motivo</h2>
            <div class="flex items-center">
                <select id="visualizacaoMotivo" class="text-xs border-systemGray-lightest rounded-control mr-2">
                    <option value="pie">Pizza</option>
                    <option value="doughnut">Rosca</option>
                    <option value="bar">Barras</option>
                </select>
            </div>
        </div>
        <div class="h-64">
            <canvas id="motivoChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> Reagendamentos representam 42% dos cancelamentos, seguidos por solicitações do paciente (28%). Implementar política de reagendamento pode reduzir em até 15%.</p>
        </div>
    </div>

    <!-- Cancelamentos por Unidade -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold">Cancelamentos por Unidade</h2>
            <div class="flex items-center">
                <select id="visualizacaoUnidade" class="text-xs border-systemGray-lightest rounded-control mr-2">
                    <option value="bar">Barras</option>
                    <option value="horizontalBar">Barras Horizontais</option>
                    <option value="polarArea">Polar</option>
                </select>
            </div>
        </div>
        <div class="h-64">
            <canvas id="unidadeChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> Unidade Morumbi apresenta taxa de cancelamento 32% acima da média. Recomenda-se revisão do processo de confirmação de agendamentos.</p>
        </div>
    </div>
</div>

<!-- Tendências e Previsões -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Tendência de Cancelamentos -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold">Tendência de Cancelamentos</h2>
            <div class="flex items-center">
                <svg class="w-4 h-4 text-systemBlue mr-1" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 1L9 9H2L7 14.5L5 22L12 17.5L19 22L17 14.5L22 9H15L12 1Z"></path>
                </svg>
                <span class="text-xs text-systemBlue font-medium">ML Forecast</span>
            </div>
        </div>
        <div class="h-64">
            <canvas id="tendenciaCancelamentosChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Previsão:</strong> Implementando as recomendações sugeridas, espera-se redução de 25% nos cancelamentos nos próximos 3 meses.</p>
        </div>
    </div>

    <!-- Distribuição por Dia e Horário -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold">Distribuição por Dia e Horário</h2>
            <div class="flex items-center">
                <svg class="w-4 h-4 text-systemBlue mr-1" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 1L9 9H2L7 14.5L5 22L12 17.5L19 22L17 14.5L22 9H15L12 1Z"></path>
                </svg>
                <span class="text-xs text-systemBlue font-medium">Data Analysis</span>
            </div>
        </div>
        <div class="h-64">
            <canvas id="heatmapChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> Maior concentração de cancelamentos ocorre nas segundas-feiras entre 8h-10h (32%) e sextas-feiras entre 16h-18h (28%).</p>
        </div>
    </div>
</div>

<!-- Correlação entre Motivos e Dias da Semana -->
<div class="bg-white rounded-view p-6 border border-gray-200 mb-6">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-base font-semibold">Correlação entre Motivos de Faltas e Dias da Semana</h2>
        <div class="flex items-center">
            <svg class="w-4 h-4 text-systemBlue mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
            </svg>
            <span class="text-xs text-systemBlue font-medium">Amigo Intelligence</span>
        </div>
    </div>
    <div class="h-80">
        <canvas id="correlacaoMotivosDiasChart"></canvas>
    </div>
    <div class="mt-2 text-xs text-label-secondary">
        <p><strong>Insight:</strong> Solicitações de pacientes são mais frequentes às segundas-feiras, enquanto indisponibilidade médica ocorre mais às sextas-feiras. Reagendamentos concentram-se no meio da semana.</p>
    </div>
</div>

<!-- Gráficos de Dispersão -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Dispersão: Tempo de Antecedência vs. Motivo -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold">Tempo de Antecedência vs. Motivo</h2>
            <div class="flex items-center">
                <svg class="w-4 h-4 text-systemBlue mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                </svg>
                <span class="text-xs text-systemBlue font-medium">Amigo Intelligence</span>
            </div>
        </div>
        <div class="h-64">
            <canvas id="dispersaoTempoMotivoChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> Cancelamentos por solicitação do paciente tendem a ocorrer com maior antecedência (média de 48h), enquanto cancelamentos por indisponibilidade médica ocorrem mais próximos à data agendada (média de 24h).</p>
        </div>
    </div>

    <!-- Dispersão: Valor do Procedimento vs. Taxa de Cancelamento -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold">Valor do Procedimento vs. Taxa de Cancelamento</h2>
            <div class="flex items-center">
                <svg class="w-4 h-4 text-systemBlue mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                </svg>
                <span class="text-xs text-systemBlue font-medium">Amigo Intelligence</span>
            </div>
        </div>
        <div class="h-64">
            <canvas id="dispersaoValorTaxaChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> Procedimentos de maior valor apresentam menor taxa de cancelamento (correlação negativa de -0.72), sugerindo maior comprometimento dos pacientes com tratamentos mais caros.</p>
        </div>
    </div>
</div>

<!-- Dispersão: Histórico de Cancelamentos por Paciente -->
<div class="bg-white rounded-view p-6 border border-gray-200 mb-6">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-base font-semibold">Histórico de Cancelamentos por Paciente</h2>
        <div class="flex items-center">
            <svg class="w-4 h-4 text-systemBlue mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
            </svg>
            <span class="text-xs text-systemBlue font-medium">Amigo Intelligence</span>
        </div>
    </div>
    <div class="h-80">
        <canvas id="dispersaoPacienteHistoricoChart"></canvas>
    </div>
    <div class="mt-2 text-xs text-label-secondary">
        <p><strong>Insight:</strong> 15% dos pacientes são responsáveis por 60% dos cancelamentos. Implementar políticas específicas para este grupo pode reduzir significativamente a taxa geral de cancelamentos.</p>
    </div>
</div>

<!-- Tabela de Cancelamentos -->
<div class="bg-white rounded-view p-6 border border-gray-200">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-base font-semibold">Detalhamento dos Cancelamentos</h2>
        <div class="flex space-x-2">
            <button class="bg-systemGray-ultralight hover:bg-systemGray-extralight text-gray-800 px-3 py-1 rounded-full transition duration-150 ease-in-out text-xs border border-systemGray-lightest">
                Exportar
            </button>
            <button class="bg-systemGray-ultralight hover:bg-systemGray-extralight text-gray-800 px-3 py-1 rounded-full transition duration-150 ease-in-out text-xs border border-systemGray-lightest">
                Imprimir
            </button>
        </div>
    </div>

    <div class="overflow-x-auto">
        <table class="w-full text-sm data-table">
            <thead>
                <tr>
                    <th>Código</th>
                    <th>Data</th>
                    <th>Paciente</th>
                    <th>Profissional</th>
                    <th>Unidade</th>
                    <th>Tipo</th>
                    <th>Procedimento</th>
                    <th>Motivo</th>
                </tr>
            </thead>
            <tbody>
                {% for cancelamento in cancelamentos %}
                <tr>
                    <td>{{ cancelamento.codigo }}</td>
                    <td>{{ cancelamento.data }}</td>
                    <td>{{ cancelamento.paciente }}</td>
                    <td>{{ cancelamento.profissional }}</td>
                    <td>{{ cancelamento.unidade }}</td>
                    <td>{{ cancelamento.tipo_atendimento }}</td>
                    <td>{{ cancelamento.procedimento }}</td>
                    <td>{{ cancelamento.motivo_cancelamento }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Paginação -->
    <div class="flex justify-between items-center mt-4">
        <div class="text-sm text-label-secondary">
            Mostrando 1-20 de {{ cancelamentos|length }} resultados
        </div>
        <div class="flex space-x-1">
            <button class="px-3 py-1 rounded-md bg-systemGray-ultralight text-label-secondary text-sm">Anterior</button>
            <button class="px-3 py-1 rounded-md bg-systemBlue text-white text-sm">1</button>
            <button class="px-3 py-1 rounded-md bg-systemGray-ultralight text-label-secondary text-sm">2</button>
            <button class="px-3 py-1 rounded-md bg-systemGray-ultralight text-label-secondary text-sm">3</button>
            <button class="px-3 py-1 rounded-md bg-systemGray-ultralight text-label-secondary text-sm">Próximo</button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Calcular cancelamentos por motivo
        const motivos = {
            'Solicitação do paciente': 0,
            'Indisponibilidade do médico': 0,
            'Reagendamento': 0,
            'Outros': 0
        };

        {% for cancelamento in cancelamentos %}
        motivos['{{ cancelamento.motivo_cancelamento }}'] += 1;
        {% endfor %}

        const motivoLabels = Object.keys(motivos);
        const motivoValues = Object.values(motivos);

        // Calcular cancelamentos por unidade
        const unidades = {};

        {% for cancelamento in cancelamentos %}
        if ('{{ cancelamento.unidade }}' in unidades) {
            unidades['{{ cancelamento.unidade }}'] += 1;
        } else {
            unidades['{{ cancelamento.unidade }}'] = 1;
        }
        {% endfor %}

        const unidadeLabels = Object.keys(unidades);
        const unidadeValues = Object.values(unidades);

        // Dados para tendência de cancelamentos
        const meses = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set'];
        const cancelamentosHistoricos = [18, 20, 22, 25, 28, 30, 32, 35, {{ cancelamentos|length }}];

        // Previsão para os próximos meses
        const mesesFuturos = ['Out', 'Nov', 'Dez'];
        const cancelamentosPreditos = [
            {{ cancelamentos|length * 0.90 | round | int }},
            {{ cancelamentos|length * 0.80 | round | int }},
            {{ cancelamentos|length * 0.75 | round | int }}
        ];

        // Dados para heatmap de distribuição por dia e horário
        const diasSemana = ['Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado'];
        const horarios = ['8h-10h', '10h-12h', '12h-14h', '14h-16h', '16h-18h'];

        // Gerar dados simulados para o heatmap
        const heatmapData = [
            [12, 8, 5, 10, 15],  // Segunda
            [8, 6, 3, 7, 10],    // Terça
            [10, 7, 4, 9, 12],   // Quarta
            [9, 5, 3, 8, 11],    // Quinta
            [14, 9, 6, 12, 16],  // Sexta
            [7, 4, 2, 5, 8]      // Sábado
        ];

        // Dados simulados para correlação entre motivos e dias da semana
        const motivosPorDia = {
            'Solicitação do paciente': [15, 8, 6, 7, 10, 5],
            'Indisponibilidade do médico': [5, 7, 8, 10, 12, 3],
            'Reagendamento': [8, 10, 12, 9, 7, 4],
            'Outros': [4, 3, 5, 6, 4, 2]
        };

        // Dados simulados para gráficos de dispersão
        const dispersaoTempoMotivo = {
            'Solicitação do paciente': {
                x: [48, 72, 36, 60, 24, 96, 84, 36, 48, 72],
                y: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
            },
            'Indisponibilidade do médico': {
                x: [24, 12, 36, 18, 24, 6, 24, 12, 24, 18],
                y: [2, 2, 2, 2, 2, 2, 2, 2, 2, 2]
            },
            'Reagendamento': {
                x: [36, 48, 24, 36, 12, 24, 36, 48, 36, 24],
                y: [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
            },
            'Outros': {
                x: [24, 36, 12, 48, 24, 36, 12, 24, 36, 48],
                y: [4, 4, 4, 4, 4, 4, 4, 4, 4, 4]
            }
        };

        // Dados para dispersão de valor vs taxa
        const dispersaoValorTaxa = {
            x: [50, 100, 150, 200, 250, 300, 350, 400, 450, 500, 550, 600],  // Valores em R$
            y: [25, 22, 20, 18, 15, 12, 10, 8, 7, 5, 4, 3]  // Taxas de cancelamento em %
        };

        // Dados para dispersão de histórico por paciente
        const dispersaoPacienteHistorico = {
            x: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20],  // Número de pacientes
            y: [1, 1, 1, 2, 2, 2, 3, 3, 4, 4, 5, 6, 7, 8, 10, 12, 15, 18, 22, 25]  // Número de cancelamentos
        };

        // Configurar gráfico de cancelamentos por motivo
        const motivoChart = new Chart(
            document.getElementById('motivoChart'),
            {
                type: 'pie',
                data: {
                    labels: motivoLabels,
                    datasets: [{
                        data: motivoValues,
                        backgroundColor: [
                            'rgba(0, 122, 255, 0.7)',
                            'rgba(142, 142, 147, 0.7)',
                            'rgba(174, 174, 178, 0.7)',
                            'rgba(199, 199, 204, 0.7)'
                        ],
                        borderColor: [
                            'rgba(0, 122, 255, 1)',
                            'rgba(142, 142, 147, 1)',
                            'rgba(174, 174, 178, 1)',
                            'rgba(199, 199, 204, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = context.chart.data.datasets[0].data.reduce((a, b) => a + b, 0);
                                    const percentage = Math.round((value / total) * 100);
                                    return `${label}: ${value} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            }
        );

        // Configurar gráfico de cancelamentos por unidade
        const unidadeChart = new Chart(
            document.getElementById('unidadeChart'),
            {
                type: 'bar',
                data: {
                    labels: unidadeLabels,
                    datasets: [{
                        label: 'Cancelamentos',
                        data: unidadeValues,
                        backgroundColor: 'rgba(0, 122, 255, 0.7)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.dataset.label || '';
                                    const value = context.raw || 0;
                                    const total = context.chart.data.datasets[0].data.reduce((a, b) => a + b, 0);
                                    const percentage = Math.round((value / total) * 100);
                                    return `${label}: ${value} (${percentage}%)`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            }
        );

        // Configurar gráfico de tendência de cancelamentos
        const tendenciaChart = new Chart(
            document.getElementById('tendenciaCancelamentosChart'),
            {
                type: 'line',
                data: {
                    labels: [...meses, ...mesesFuturos],
                    datasets: [
                        {
                            label: 'Histórico',
                            data: [...cancelamentosHistoricos, null, null, null],
                            backgroundColor: 'rgba(0, 122, 255, 0.1)',
                            borderColor: 'rgba(0, 122, 255, 1)',
                            borderWidth: 2,
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: 'Previsão',
                            data: [null, null, null, null, null, null, null, null, cancelamentosHistoricos[8], ...cancelamentosPreditos],
                            backgroundColor: 'rgba(0, 122, 255, 0.05)',
                            borderColor: 'rgba(0, 122, 255, 0.7)',
                            borderWidth: 2,
                            borderDash: [5, 5],
                            tension: 0.4,
                            fill: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.dataset.label || '';
                                    const value = context.raw || 0;
                                    return value ? `${label}: ${value} cancelamentos` : '';
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            title: {
                                display: true,
                                text: 'Número de Cancelamentos'
                            },
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            }
        );

        // Configurar gráfico de dispersão: Tempo de Antecedência vs. Motivo
        const dispersaoTempoMotivoChart = new Chart(
            document.getElementById('dispersaoTempoMotivoChart'),
            {
                type: 'scatter',
                data: {
                    datasets: [
                        {
                            label: 'Solicitação do paciente',
                            data: dispersaoTempoMotivo['Solicitação do paciente'].x.map((x, i) => ({
                                x: x,
                                y: dispersaoTempoMotivo['Solicitação do paciente'].y[i]
                            })),
                            backgroundColor: 'rgba(0, 122, 255, 0.7)',
                            borderColor: 'rgba(0, 122, 255, 1)',
                            pointRadius: 6,
                            pointHoverRadius: 8
                        },
                        {
                            label: 'Indisponibilidade do médico',
                            data: dispersaoTempoMotivo['Indisponibilidade do médico'].x.map((x, i) => ({
                                x: x,
                                y: dispersaoTempoMotivo['Indisponibilidade do médico'].y[i]
                            })),
                            backgroundColor: 'rgba(142, 142, 147, 0.7)',
                            borderColor: 'rgba(142, 142, 147, 1)',
                            pointRadius: 6,
                            pointHoverRadius: 8
                        },
                        {
                            label: 'Reagendamento',
                            data: dispersaoTempoMotivo['Reagendamento'].x.map((x, i) => ({
                                x: x,
                                y: dispersaoTempoMotivo['Reagendamento'].y[i]
                            })),
                            backgroundColor: 'rgba(174, 174, 178, 0.7)',
                            borderColor: 'rgba(174, 174, 178, 1)',
                            pointRadius: 6,
                            pointHoverRadius: 8
                        },
                        {
                            label: 'Outros',
                            data: dispersaoTempoMotivo['Outros'].x.map((x, i) => ({
                                x: x,
                                y: dispersaoTempoMotivo['Outros'].y[i]
                            })),
                            backgroundColor: 'rgba(199, 199, 204, 0.7)',
                            borderColor: 'rgba(199, 199, 204, 1)',
                            pointRadius: 6,
                            pointHoverRadius: 8
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const motivo = context.dataset.label;
                                    const horas = context.parsed.x;
                                    return `${motivo}: ${horas}h de antecedência`;
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'Horas de Antecedência'
                            },
                            min: 0,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Motivo'
                            },
                            ticks: {
                                callback: function(value) {
                                    const motivos = ['', 'Solicitação do paciente', 'Indisponibilidade do médico', 'Reagendamento', 'Outros'];
                                    return motivos[value] || '';
                                }
                            },
                            min: 0.5,
                            max: 4.5,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        }
                    }
                }
            }
        );

        // Configurar gráfico de dispersão: Valor do Procedimento vs. Taxa de Cancelamento
        const dispersaoValorTaxaChart = new Chart(
            document.getElementById('dispersaoValorTaxaChart'),
            {
                type: 'scatter',
                data: {
                    datasets: [{
                        label: 'Procedimentos',
                        data: dispersaoValorTaxa.x.map((x, i) => ({
                            x: x,
                            y: dispersaoValorTaxa.y[i]
                        })),
                        backgroundColor: 'rgba(0, 122, 255, 0.7)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        pointRadius: 6,
                        pointHoverRadius: 8,
                        showLine: true,
                        borderWidth: 2,
                        borderDash: [5, 5],
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const valor = context.parsed.x;
                                    const taxa = context.parsed.y;
                                    return `Valor: R$ ${valor.toFixed(2)} | Taxa: ${taxa}%`;
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'Valor do Procedimento (R$)'
                            },
                            min: 0,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Taxa de Cancelamento (%)'
                            },
                            min: 0,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        }
                    }
                }
            }
        );

        // Configurar gráfico de dispersão: Histórico de Cancelamentos por Paciente
        const dispersaoPacienteHistoricoChart = new Chart(
            document.getElementById('dispersaoPacienteHistoricoChart'),
            {
                type: 'scatter',
                data: {
                    datasets: [{
                        label: 'Pacientes',
                        data: dispersaoPacienteHistorico.x.map((x, i) => ({
                            x: x,
                            y: dispersaoPacienteHistorico.y[i]
                        })),
                        backgroundColor: 'rgba(0, 122, 255, 0.7)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        pointRadius: 6,
                        pointHoverRadius: 8
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const paciente = context.parsed.x;
                                    const cancelamentos = context.parsed.y;
                                    return `Paciente #${paciente}: ${cancelamentos} cancelamentos`;
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'Paciente (ID)'
                            },
                            min: 0,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Número de Cancelamentos'
                            },
                            min: 0,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        }
                    }
                }
            }
        );

        // Configurar gráfico de correlação entre motivos e dias da semana
        const correlacaoMotivosDiasChart = new Chart(
            document.getElementById('correlacaoMotivosDiasChart'),
            {
                type: 'bar',
                data: {
                    labels: diasSemana,
                    datasets: Object.keys(motivosPorDia).map((motivo, index) => ({
                        label: motivo,
                        data: motivosPorDia[motivo],
                        backgroundColor: [
                            'rgba(0, 122, 255, 0.7)',
                            'rgba(142, 142, 147, 0.7)',
                            'rgba(174, 174, 178, 0.7)',
                            'rgba(199, 199, 204, 0.7)'
                        ][index],
                        borderColor: [
                            'rgba(0, 122, 255, 1)',
                            'rgba(142, 142, 147, 1)',
                            'rgba(174, 174, 178, 1)',
                            'rgba(199, 199, 204, 1)'
                        ][index],
                        borderWidth: 1
                    }))
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.dataset.label || '';
                                    const value = context.raw || 0;
                                    return `${label}: ${value} cancelamentos`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Número de Cancelamentos'
                            },
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            }
        );

        // Configurar gráfico de heatmap para distribuição por dia e horário
        const heatmapChart = new Chart(
            document.getElementById('heatmapChart'),
            {
                type: 'matrix',
                data: {
                    datasets: [{
                        label: 'Cancelamentos',
                        data: diasSemana.flatMap((dia, i) =>
                            horarios.map((horario, j) => ({
                                x: j,
                                y: i,
                                v: heatmapData[i][j]
                            }))
                        ),
                        backgroundColor(context) {
                            const value = context.dataset.data[context.dataIndex].v;
                            const alpha = Math.min(value / 35, 1); // Normalizar para o valor máximo
                            return `rgba(0, 122, 255, ${alpha})`;
                        },
                        borderColor: 'white',
                        borderWidth: 1,
                        width: ({ chart }) => (chart.chartArea || {}).width / horarios.length - 1,
                        height: ({ chart }) => (chart.chartArea || {}).height / diasSemana.length - 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        tooltip: {
                            callbacks: {
                                title() {
                                    return '';
                                },
                                label(context) {
                                    const v = context.dataset.data[context.dataIndex];
                                    return [`${diasSemana[v.y]}, ${horarios[v.x]}`, `Cancelamentos: ${v.v}`];
                                }
                            }
                        },
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            type: 'category',
                            labels: horarios,
                            offset: true,
                            ticks: {
                                display: true
                            },
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            type: 'category',
                            labels: diasSemana,
                            offset: true,
                            ticks: {
                                display: true
                            },
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            }
        );

        // Event listeners para os seletores de visualização
        document.getElementById('visualizacaoMotivo').addEventListener('change', function() {
            // Alterar o tipo de gráfico
            motivoChart.destroy(); // Destruir o gráfico atual

            // Criar novo gráfico com o tipo selecionado
            const newType = this.value;
            const newOptions = {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.chart.data.datasets[0].data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            };

            // Ajustar opções específicas para cada tipo de gráfico
            if (newType === 'pie' || newType === 'doughnut') {
                newOptions.plugins.legend = {
                    position: 'right'
                };
            } else if (newType === 'bar') {
                newOptions.plugins.legend = {
                    display: false
                };
                newOptions.scales = {
                    y: {
                        beginAtZero: true,
                        grid: {
                            display: true,
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                };
            }

            // Criar novo gráfico
            const ctx = document.getElementById('motivoChart').getContext('2d');
            window.motivoChart = new Chart(ctx, {
                type: newType,
                data: {
                    labels: motivoLabels,
                    datasets: [{
                        label: 'Cancelamentos',
                        data: motivoValues,
                        backgroundColor: [
                            'rgba(0, 122, 255, 0.7)',
                            'rgba(142, 142, 147, 0.7)',
                            'rgba(174, 174, 178, 0.7)',
                            'rgba(199, 199, 204, 0.7)'
                        ],
                        borderColor: [
                            'rgba(0, 122, 255, 1)',
                            'rgba(142, 142, 147, 1)',
                            'rgba(174, 174, 178, 1)',
                            'rgba(199, 199, 204, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: newOptions
            });
        });

        document.getElementById('visualizacaoUnidade').addEventListener('change', function() {
            // Alterar o tipo de gráfico
            unidadeChart.destroy(); // Destruir o gráfico atual

            // Criar novo gráfico com o tipo selecionado
            const newType = this.value;
            const newOptions = {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.dataset.label || '';
                                const value = context.raw || 0;
                                const total = context.chart.data.datasets[0].data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            };

            // Ajustar opções específicas para cada tipo de gráfico
            if (newType === 'bar' || newType === 'horizontalBar') {
                newOptions.indexAxis = newType === 'horizontalBar' ? 'y' : 'x';
                newOptions.scales = {
                    x: {
                        beginAtZero: true,
                        grid: {
                            display: newType === 'horizontalBar',
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            display: newType === 'bar',
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    }
                };
            } else if (newType === 'polarArea') {
                newOptions.scales = undefined;
                newOptions.plugins.legend = {
                    position: 'right'
                };
            }

            // Criar novo gráfico
            const ctx = document.getElementById('unidadeChart').getContext('2d');
            window.unidadeChart = new Chart(ctx, {
                type: newType === 'horizontalBar' ? 'bar' : newType, // 'horizontalBar' é tratado com indexAxis
                data: {
                    labels: unidadeLabels,
                    datasets: [{
                        label: 'Cancelamentos',
                        data: unidadeValues,
                        backgroundColor: [
                            'rgba(0, 122, 255, 0.7)',
                            'rgba(142, 142, 147, 0.7)',
                            'rgba(174, 174, 178, 0.7)',
                            'rgba(199, 199, 204, 0.7)',
                            'rgba(209, 209, 214, 0.7)'
                        ],
                        borderColor: [
                            'rgba(0, 122, 255, 1)',
                            'rgba(142, 142, 147, 1)',
                            'rgba(174, 174, 178, 1)',
                            'rgba(199, 199, 204, 1)',
                            'rgba(209, 209, 214, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: newOptions
            });
        });

        // Adicionar funcionalidade aos botões de filtro
        document.querySelector('button.bg-systemBlue').addEventListener('click', function() {
            // Simular filtragem de dados
            alert('Filtros aplicados! Em um ambiente real, os dados seriam filtrados de acordo com os critérios selecionados.');
        });

        // Adicionar funcionalidade aos botões de exportação e impressão
        document.querySelectorAll('.bg-systemGray-ultralight').forEach(button => {
            button.addEventListener('click', function() {
                if (this.textContent.trim() === 'Exportar') {
                    alert('Exportando dados... Em um ambiente real, os dados seriam exportados para Excel ou CSV.');
                } else if (this.textContent.trim() === 'Imprimir') {
                    alert('Preparando impressão... Em um ambiente real, seria aberta a janela de impressão.');
                }
            });
        });
    });
</script>
{% endblock %}

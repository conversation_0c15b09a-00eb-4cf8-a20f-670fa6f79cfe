{% extends 'base.html' %}

{% block title %}Módulo Agenda - Amigo DataApp{% endblock %}

{% block header %}Módulo Agenda{% endblock %}

{% block content %}
<!-- Hero do módulo -->
<div class="bg-white rounded-view p-8 mb-8 border border-gray-200">
    <div class="flex flex-col md:flex-row items-center">
        <div class="md:w-2/3">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">M<PERSON><PERSON><PERSON> Agenda</h1>
            <p class="text-lg text-label-secondary mb-6">G<PERSON><PERSON><PERSON> agendamentos, acompanhe a produção médica e analise o tempo de atendimento.</p>
            <div class="flex space-x-4">
                <a href="{{ url_for('agendamentos') }}" class="bg-systemBlue hover:bg-blue-600 text-white px-6 py-2 rounded-full transition duration-150 ease-in-out">
                    Ver Agendamentos
                </a>
                <a href="{{ url_for('producao_medica') }}" class="bg-systemGray-ultralight hover:bg-systemGray-extralight text-gray-800 px-6 py-2 rounded-full transition duration-150 ease-in-out border border-systemGray-lightest">
                    Produção Médica
                </a>
            </div>
        </div>
        <div class="md:w-1/3 mt-6 md:mt-0 flex justify-center">
            <div class="w-48 h-48 bg-blue-100 rounded-full flex items-center justify-center">
                <svg class="w-24 h-24 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
            </div>
        </div>
    </div>
</div>

<!-- Resumo do módulo -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total de Agendamentos -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-gray-900">Total de Agendamentos</p>
        </div>
        <p class="text-3xl font-semibold text-gray-900 mb-1">{{ dados.agendamentos|length }}</p>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Agendamentos realizados nos últimos 30 dias.</p>
    </div>

    <!-- Atendimentos Realizados -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-gray-900">Atendimentos Realizados</p>
        </div>
        <p class="text-3xl font-semibold text-gray-900 mb-1">{{ dados.producao_medica|length }}</p>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Atendimentos concluídos nos últimos 30 dias.</p>
    </div>

    <!-- Tempo Médio de Atendimento -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-gray-900">Tempo Médio de Atendimento</p>
        </div>
        <p class="text-3xl font-semibold text-gray-900 mb-1">
            {% set tempo_total = 0 %}
            {% for tempo in dados.tempo_atendimento %}
                {% set tempo_total = tempo_total + tempo.tempo_atendimento %}
            {% endfor %}
            {{ (tempo_total / (dados.tempo_atendimento|length if dados.tempo_atendimento|length > 0 else 1))|round|int }} min
        </p>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Tempo médio de duração dos atendimentos.</p>
    </div>

    <!-- Cancelamentos -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-gray-900">Cancelamentos</p>
        </div>
        <p class="text-3xl font-semibold text-gray-900 mb-1">{{ dados.cancelamentos|length }}</p>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Agendamentos cancelados nos últimos 30 dias.</p>
    </div>
</div>

<!-- Insights com IA -->
<div class="mb-8">
    <div class="flex items-center justify-between mb-4">
        <div class="flex items-center">
            <svg class="w-5 h-5 text-systemBlue mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
            </svg>
            <h2 class="text-base font-semibold text-gray-800">Amigo Intelligence</h2>
        </div>
        <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">Atualizado agora</span>
    </div>

    <div id="insights-container" class="grid grid-cols-1 md:grid-cols-3 gap-3">
        {% for insight in insights %}
            {% with
                title=insight.title,
                description=insight.description,
                insight_type=insight.insight_type,
                content=insight.content,
                category=insight.category,
                action_text=insight.action_text,
                action_url=insight.action_url
            %}
                {% include 'components/ai_insight_widget.html' %}
            {% endwith %}
        {% endfor %}
    </div>
</div>

<script>
    // Carregar insights dinamicamente após 5 segundos (para demonstração)
    setTimeout(() => {
        // Contexto específico da página de índice da agenda
        const pageContext = {
            page_title: "Módulo Agenda",
            page_description: "Visão geral do módulo de agenda, com foco em otimização de agendamentos e produtividade",
            key_metrics: {
                "Total de Agendamentos": "{{ dados.agendamentos|length }}",
                "Atendimentos Realizados": "{{ dados.producao_medica|length }}",
                "Tempo Médio de Atendimento": "{{ (tempo_total / (dados.tempo_atendimento|length if dados.tempo_atendimento|length > 0 else 1))|round|int }} min",
                "Cancelamentos": "{{ dados.cancelamentos|length }}"
            },
            analysis_focus: "Otimização de agenda e produtividade dos profissionais",
            page_elements: [
                "Resumo do módulo",
                "Relatórios disponíveis"
            ]
        };

        loadInsights('agenda', 'index', 'insights-container', 3, ['list', 'text', 'stat'], pageContext);
    }, 5000);
</script>

<!-- Insights Estáticos (Temporários) -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8 hidden">
    <!-- Insight de Otimização de Agenda -->
    {% with
        title="Otimização de Agenda",
        description="Oportunidades para melhorar a ocupação",
        insight_type="list",
        content=[
            "Terças e quintas têm <strong>27% menos</strong> agendamentos que segundas e quartas",
            "Horários entre 10h-11h têm <strong>taxa de ocupação de 42%</strong>, abaixo da média",
            "Redistribuir 3 profissionais para o período da tarde aumentaria a eficiência em <strong>18%</strong>"
        ],
        category="Eficiência",
        action_text="Ver detalhes",
        action_url=url_for('agendamentos')
    %}
        {% include 'components/ai_insight_widget.html' %}
    {% endwith %}

    <!-- Insight de Cancelamentos -->
    {% with
        title="Análise de Cancelamentos",
        description="Padrões e oportunidades de melhoria",
        insight_type="text",
        content="<strong>68%</strong> dos cancelamentos ocorrem com menos de 24h de antecedência. Implementar um sistema de confirmação automática 48h antes poderia reduzir a taxa de cancelamentos em até <strong>35%</strong>, aumentando a ocupação e o faturamento.",
        category="Oportunidade",
        action_text="Explorar soluções",
        action_url=url_for('cancelamentos')
    %}
        {% include 'components/ai_insight_widget.html' %}
    {% endwith %}

    <!-- Insight de Produtividade -->
    {% with
        title="Produtividade dos Profissionais",
        description="Análise comparativa de desempenho",
        insight_type="list",
        content=[
            "Dr. Ricardo tem tempo médio de atendimento <strong>22% menor</strong> que a média, mantendo alta satisfação",
            "Dra. Ana tem <strong>maior taxa de retorno</strong> de pacientes (87%)",
            "Especialidade de Dermatologia tem <strong>maior potencial</strong> para aumento de slots"
        ],
        category="Produtividade",
        action_text="Ver análise completa",
        action_url=url_for('producao_medica')
    %}
        {% include 'components/ai_insight_widget.html' %}
    {% endwith %}
</div>

<!-- Relatórios disponíveis -->
<div class="bg-white rounded-view p-6 border border-gray-200">
    <h2 class="text-xl font-semibold mb-6">Relatórios Disponíveis</h2>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Agendamentos -->
        <a href="{{ url_for('agendamentos') }}" class="bg-systemGray-ultralight hover:bg-systemGray-extralight p-6 rounded-view border border-systemGray-lightest transition duration-150 ease-in-out">
            <div class="flex flex-col items-center text-center">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                    <svg class="w-8 h-8 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <h3 class="text-base font-semibold mb-2">Agendamentos</h3>
                <p class="text-sm text-label-secondary">Visualize e gerencie todos os agendamentos.</p>
            </div>
        </a>

        <!-- Produção Médica -->
        <a href="{{ url_for('producao_medica') }}" class="bg-systemGray-ultralight hover:bg-systemGray-extralight p-6 rounded-view border border-systemGray-lightest transition duration-150 ease-in-out">
            <div class="flex flex-col items-center text-center">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                    <svg class="w-8 h-8 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <h3 class="text-base font-semibold mb-2">Produção Médica</h3>
                <p class="text-sm text-label-secondary">Acompanhe a produtividade dos profissionais.</p>
            </div>
        </a>

        <!-- Tempo de Atendimento -->
        <a href="{{ url_for('tempo_atendimento') }}" class="bg-systemGray-ultralight hover:bg-systemGray-extralight p-6 rounded-view border border-systemGray-lightest transition duration-150 ease-in-out">
            <div class="flex flex-col items-center text-center">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                    <svg class="w-8 h-8 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-base font-semibold mb-2">Tempo de Atendimento</h3>
                <p class="text-sm text-label-secondary">Analise o tempo de espera e duração dos atendimentos.</p>
            </div>
        </a>

        <!-- Cancelamentos -->
        <a href="{{ url_for('cancelamentos') }}" class="bg-systemGray-ultralight hover:bg-systemGray-extralight p-6 rounded-view border border-systemGray-lightest transition duration-150 ease-in-out">
            <div class="flex flex-col items-center text-center">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                    <svg class="w-8 h-8 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </div>
                <h3 class="text-base font-semibold mb-2">Cancelamentos</h3>
                <p class="text-sm text-label-secondary">Acompanhe os cancelamentos e seus motivos.</p>
            </div>
        </a>
    </div>
</div>
{% endblock %}

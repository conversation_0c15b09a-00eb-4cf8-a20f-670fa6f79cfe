{% extends 'base.html' %}

{% block title %}Produção Médica - Amigo DataHub{% endblock %}

{% block header %}Produção Médica{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="bg-gradient-to-r from-blue-50 to-white rounded-view p-8 border border-gray-200 mb-6 overflow-hidden relative">
    <!-- Background Elements -->
    <div class="absolute top-0 right-0 w-full h-full opacity-10 overflow-hidden">
        <svg class="absolute top-10 right-10 w-64 h-64 text-systemBlue" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 17a2 2 0 11-4 0 2 2 0 014 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h1m8-1a1 1 0 01-1 1H9m4-1V8a1 1 0 011-1h2.586a1 1 0 01.707.293l3.414 3.414a1 1 0 01.293.707V16a1 1 0 01-1 1h-1m-6-1a1 1 0 001 1h1M5 17a2 2 0 104 0m-4 0a2 2 0 114 0m6 0a2 2 0 104 0m-4 0a2 2 0 114 0"></path>
        </svg>
        <svg class="absolute bottom-10 left-1/4 w-32 h-32 text-systemBlue" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
        </svg>
    </div>

    <div class="flex flex-col md:flex-row items-center relative z-10">
        <div class="md:w-2/3 mb-6 md:mb-0 md:pr-8">
            <div class="inline-block bg-systemBlue text-white text-xs font-semibold px-3 py-1 rounded-full mb-3">AGENDA</div>
            <h1 class="text-3xl font-bold text-gray-800 mb-4">Análise de Produção Médica</h1>
            <p class="text-gray-600 mb-6">Monitore o desempenho dos profissionais, compare a produtividade entre unidades e identifique oportunidades para otimizar a alocação de recursos e aumentar a receita.</p>
            <div class="grid grid-cols-3 gap-4 mb-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">{{ producao|length }}</div>
                    <div class="text-xs text-gray-500">Atendimentos realizados</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">{{ "%.0f"|format(producao|sum(attribute='valor')) }}</div>
                    <div class="text-xs text-gray-500">Valor total (R$)</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">{{ producao|groupby('profissional')|list|length }}</div>
                    <div class="text-xs text-gray-500">Profissionais ativos</div>
                </div>
            </div>
            <div class="flex flex-wrap gap-3">
                <button class="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    Gerar Relatório
                </button>
                <button class="bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-md text-sm border border-gray-200 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                    </svg>
                    Filtrar Dados
                </button>
            </div>
        </div>
        <div class="md:w-1/3 flex justify-center">
            <div class="w-64 h-64 bg-white rounded-full shadow-lg flex items-center justify-center p-4 relative">
                <div class="absolute inset-0 rounded-full border-8 border-blue-100"></div>
                <div class="absolute inset-0 rounded-full border-8 border-systemBlue border-opacity-70" style="clip-path: polygon(50% 50%, 100% 0, 100% 50%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-300 border-opacity-70" style="clip-path: polygon(50% 50%, 100% 50%, 100% 100%, 50% 100%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-200 border-opacity-70" style="clip-path: polygon(50% 50%, 50% 100%, 0 100%, 0 50%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-400 border-opacity-70" style="clip-path: polygon(50% 50%, 0 50%, 0 0, 50% 0);"></div>
                <div class="z-10 text-center">
                    <div class="text-3xl font-bold text-gray-800">{{ "%.0f"|format((producao|sum(attribute='valor')) / (producao|length if producao|length > 0 else 1)) }}</div>
                    <div class="text-sm text-gray-500">Valor médio (R$)</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Insights com IA -->
<div class="mb-5">
    <div class="flex items-center justify-between mb-4">
        <div class="flex items-center">
            <svg class="w-5 h-5 text-systemBlue mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
            </svg>
            <h2 class="text-base font-semibold text-gray-800">Amigo Intelligence</h2>
        </div>
        <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">Atualizado agora</span>
    </div>

    <div id="insights-container" class="grid grid-cols-1 md:grid-cols-3 gap-3">
        {% for insight in insights %}
            {% with
                title=insight.title,
                description=insight.description,
                insight_type=insight.insight_type,
                content=insight.content,
                category=insight.category
            %}
                {% include 'components/ai_insight_widget.html' %}
            {% endwith %}
        {% endfor %}
    </div>
</div>

<script>
    // Carregar insights dinamicamente após 5 segundos (para demonstração)
    setTimeout(() => {
        // Contexto específico da página de produção médica
        const pageContext = {
            page_title: "Produção Médica",
            page_description: "Análise da produtividade dos profissionais, com foco em otimização e eficiência",
            key_metrics: {
                "Total de Atendimentos": document.querySelector('.kpi-total-atendimentos .value')?.textContent || "1.876",
                "Média por Profissional": document.querySelector('.kpi-media-profissional .value')?.textContent || "156",
                "Taxa de Ocupação": document.querySelector('.kpi-taxa-ocupacao .value')?.textContent || "72%"
            },
            analysis_focus: "Otimização da produtividade e eficiência dos profissionais",
            page_elements: [
                "Atendimentos por Profissional",
                "Produtividade por Dia da Semana",
                "Evolução Mensal",
                "Tipos de Procedimentos"
            ]
        };

        loadInsights('agenda', 'producao_medica', 'insights-container', 3, ['list', 'text', 'stat'], pageContext);
    }, 5000);
</script>

<!-- Resumo e Insights -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <!-- Total de Atendimentos -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-gray-800">Total de Atendimentos</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">{{ producao|length }}</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Atendimentos realizados no período.</span>
            <span class="ml-auto text-gray-800 font-medium">+12%</span>
        </div>
    </div>

    <!-- Valor Total -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-gray-800">Valor Total</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">R$ {{ "%.0f"|format(producao|sum(attribute='valor')) }}</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Valor total dos atendimentos realizados.</span>
            <span class="ml-auto text-gray-800 font-medium">+8.5%</span>
        </div>
    </div>

    <!-- Valor Médio -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-gray-800">Valor Médio</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">R$ {{ "%.0f"|format((producao|sum(attribute='valor')) / (producao|length if producao|length > 0 else 1)) }}</p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Valor médio por atendimento.</span>
            <span class="ml-auto text-gray-800 font-medium">-3.2%</span>
        </div>
    </div>

    <!-- Previsão Próximo Mês -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex justify-between items-center mb-2">
            <p class="text-sm font-semibold text-gray-800">Previsão Próximo Mês</p>
            <div class="flex items-center">
                <svg class="w-4 h-4 text-systemBlue mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
                </svg>
                <span class="text-xs text-gray-800 font-medium">Amigo Intelligence</span>
            </div>
        </div>
        <p class="text-3xl font-semibold text-systemBlue mb-1">R$ {{ "%.0f"|format(producao|sum(attribute='valor') * 1.15) }}</p>
        <div class="flex justify-between text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Baseado em tendências históricas</span>
            <span class="text-gray-800">+15%</span>
        </div>
    </div>
</div>

<!-- Insights e Recomendações -->
<div class="bg-white rounded-view p-5 border border-gray-200 mb-6">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-base font-semibold text-label-DEFAULT">Insights de Produção</h2>
        <div class="flex items-center">
            <svg class="w-4 h-4 text-systemBlue mr-1" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 1L9 9H2L7 14.5L5 22L12 17.5L19 22L17 14.5L22 9H15L12 1Z"></path>
            </svg>
            <span class="text-xs text-systemBlue font-medium">AI Analytics</span>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Oportunidades de Crescimento -->
        <div class="bg-systemGray-ultralight p-4 rounded-view">
            <div class="flex items-center mb-3">
                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-sm font-semibold">Oportunidades de Crescimento</h3>
                    <p class="text-xs text-label-secondary">Especialidades com potencial</p>
                </div>
            </div>
            <ul class="space-y-2 text-sm">
                <li class="flex justify-between">
                    <span>Cardiologia</span>
                    <span class="font-medium text-systemGreen">+22%</span>
                </li>
                <li class="flex justify-between">
                    <span>Dermatologia</span>
                    <span class="font-medium text-systemGreen">+18%</span>
                </li>
                <li class="flex justify-between">
                    <span>Ortopedia</span>
                    <span class="font-medium text-systemGreen">+15%</span>
                </li>
            </ul>
        </div>

        <!-- Profissionais Destaques -->
        <div class="bg-systemGray-ultralight p-4 rounded-view">
            <div class="flex items-center mb-3">
                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-sm font-semibold">Profissionais Destaques</h3>
                    <p class="text-xs text-label-secondary">Maior crescimento no período</p>
                </div>
            </div>
            <ul class="space-y-2 text-sm">
                <li class="flex justify-between">
                    <span>Dr. Bruno Lima</span>
                    <span class="font-medium text-systemGreen">+24%</span>
                </li>
                <li class="flex justify-between">
                    <span>Dra. Giulia Pedrosa</span>
                    <span class="font-medium text-systemGreen">+19%</span>
                </li>
                <li class="flex justify-between">
                    <span>Dr. Caio Menezes</span>
                    <span class="font-medium text-systemGreen">+12%</span>
                </li>
            </ul>
        </div>

        <!-- Recomendações -->
        <div class="bg-systemGray-ultralight p-4 rounded-view">
            <div class="flex items-center mb-3">
                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-sm font-semibold">Recomendações</h3>
                    <p class="text-xs text-label-secondary">Ações para otimização</p>
                </div>
            </div>
            <ul class="space-y-2 text-sm">
                <li class="flex items-start">
                    <svg class="w-4 h-4 text-systemBlue mr-1 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <span>Aumentar disponibilidade de Cardiologia em 15%</span>
                </li>
                <li class="flex items-start">
                    <svg class="w-4 h-4 text-systemBlue mr-1 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <span>Otimizar agenda da unidade Recife (ociosidade de 12%)</span>
                </li>
                <li class="flex items-start">
                    <svg class="w-4 h-4 text-systemBlue mr-1 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <span>Revisar tabela de preços para procedimentos de Ortopedia</span>
                </li>
            </ul>
        </div>
    </div>
</div>

<!-- Filtros -->
<div class="bg-white rounded-view p-4 mb-6 border border-gray-200">
    <div class="flex flex-wrap gap-4">
        <div class="w-full md:w-auto">
            <label for="data_inicio" class="block text-xs font-medium text-label-secondary mb-1">Data Início</label>
            <input type="date" id="data_inicio" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
        </div>
        <div class="w-full md:w-auto">
            <label for="data_fim" class="block text-xs font-medium text-label-secondary mb-1">Data Fim</label>
            <input type="date" id="data_fim" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
        </div>
        <div class="w-full md:w-auto">
            <label for="unidade" class="block text-xs font-medium text-label-secondary mb-1">Unidade</label>
            <select id="unidade" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
                <option value="">Todas</option>
                <option>Morumbi</option>
                <option>BRASILIA</option>
                <option>Recife</option>
                <option>Espinheiro</option>
                <option>CLÍNICA (RL)</option>
            </select>
        </div>
        <div class="w-full md:w-auto">
            <label for="profissional" class="block text-xs font-medium text-label-secondary mb-1">Profissional</label>
            <select id="profissional" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
                <option value="">Todos</option>
                <option>Equipe Amigo - BRUNO LIMA</option>
                <option>Equipe Amigo - Caio Menezes</option>
                <option>Equipe Amigo - Giulia Pedrosa 2</option>
                <option>Rayara Toledo Souza</option>
            </select>
        </div>
        <div class="w-full md:w-auto flex items-end">
            <button class="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-control transition duration-150 ease-in-out text-sm">
                Filtrar
            </button>
        </div>
    </div>
</div>

<!-- Análises Avançadas -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Produção por Profissional -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Produção por Profissional</h2>
            <div class="flex items-center">
                <select id="visualizacaoProfissional" class="text-xs border-systemGray-lightest rounded-control mr-2">
                    <option value="bar">Barras</option>
                    <option value="horizontalBar">Barras Horizontais</option>
                    <option value="radar">Radar</option>
                </select>
            </div>
        </div>
        <div class="h-64">
            <canvas id="producaoProfissionalChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> Dr. Bruno Lima apresenta crescimento consistente de 24% nos últimos 3 meses, com foco em procedimentos de alta complexidade.</p>
        </div>
    </div>

    <!-- Produção por Unidade -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Produção por Unidade</h2>
            <div class="flex items-center">
                <select id="visualizacaoUnidade" class="text-xs border-systemGray-lightest rounded-control mr-2">
                    <option value="pie">Pizza</option>
                    <option value="doughnut">Rosca</option>
                    <option value="polarArea">Polar</option>
                </select>
            </div>
        </div>
        <div class="h-64">
            <canvas id="producaoUnidadeChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> Unidade Morumbi representa 42% da produção total, com crescimento de 15% em relação ao período anterior.</p>
        </div>
    </div>
</div>

<!-- Tendências e Comparações -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Tendência de Produção -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Tendência de Produção</h2>
            <div class="flex items-center">
                <svg class="w-4 h-4 text-systemBlue mr-1" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 1L9 9H2L7 14.5L5 22L12 17.5L19 22L17 14.5L22 9H15L12 1Z"></path>
                </svg>
                <span class="text-xs text-systemBlue font-medium">ML Forecast</span>
            </div>
        </div>
        <div class="h-64">
            <canvas id="tendenciaProducaoChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Previsão:</strong> Crescimento projetado de 15% nos próximos 3 meses, com pico esperado em Outubro devido à sazonalidade.</p>
        </div>
    </div>

    <!-- Comparação de Procedimentos -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Comparação de Procedimentos</h2>
            <div class="flex items-center">
                <select id="periodoComparacao" class="text-xs border-systemGray-lightest rounded-control mr-2">
                    <option value="mes">Mês Atual vs Anterior</option>
                    <option value="trimestre">Trimestre Atual vs Anterior</option>
                </select>
            </div>
        </div>
        <div class="h-64">
            <canvas id="comparacaoProcedimentosChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> Procedimentos estéticos apresentaram o maior crescimento (+28%), enquanto consultas de rotina tiveram queda de 5%.</p>
        </div>
    </div>
</div>

<!-- Tabela de Produção -->
<div class="bg-white rounded-view p-6 border border-gray-200">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-base font-semibold text-label-DEFAULT">Detalhamento da Produção</h2>
        <div class="flex space-x-2">
            <button class="bg-systemGray-ultralight hover:bg-systemGray-extralight text-gray-800 px-3 py-1 rounded-full transition duration-150 ease-in-out text-xs border border-systemGray-lightest">
                Exportar
            </button>
            <button class="bg-systemGray-ultralight hover:bg-systemGray-extralight text-gray-800 px-3 py-1 rounded-full transition duration-150 ease-in-out text-xs border border-systemGray-lightest">
                Imprimir
            </button>
        </div>
    </div>

    <div class="overflow-x-auto">
        <table class="w-full text-sm data-table">
            <thead>
                <tr>
                    <th>Código</th>
                    <th>Data</th>
                    <th>Paciente</th>
                    <th>Profissional</th>
                    <th>Unidade</th>
                    <th>Tipo</th>
                    <th>Procedimento</th>
                    <th>Forma Pagamento</th>
                    <th>Valor</th>
                </tr>
            </thead>
            <tbody>
                {% for item in producao %}
                <tr>
                    <td>{{ item.codigo }}</td>
                    <td>{{ item.data }}</td>
                    <td>{{ item.paciente }}</td>
                    <td>{{ item.profissional }}</td>
                    <td>{{ item.unidade }}</td>
                    <td>{{ item.tipo_atendimento }}</td>
                    <td>{{ item.procedimento }}</td>
                    <td>{{ item.forma_pagamento }}</td>
                    <td>R$ {{ "%.2f"|format(item.valor) }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Paginação -->
    <div class="flex justify-between items-center mt-4">
        <div class="text-sm text-label-secondary">
            Mostrando 1-20 de {{ producao|length }} resultados
        </div>
        <div class="flex space-x-1">
            <button class="px-3 py-1 rounded-md bg-systemGray-ultralight text-label-secondary text-sm">Anterior</button>
            <button class="px-3 py-1 rounded-md bg-systemBlue text-white text-sm">1</button>
            <button class="px-3 py-1 rounded-md bg-systemGray-ultralight text-label-secondary text-sm">2</button>
            <button class="px-3 py-1 rounded-md bg-systemGray-ultralight text-label-secondary text-sm">3</button>
            <button class="px-3 py-1 rounded-md bg-systemGray-ultralight text-label-secondary text-sm">Próximo</button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Calcular produção por profissional
        const profissionais = {};
        {% for item in producao %}
        if ('{{ item.profissional }}' in profissionais) {
            profissionais['{{ item.profissional }}'] += {{ item.valor }};
        } else {
            profissionais['{{ item.profissional }}'] = {{ item.valor }};
        }
        {% endfor %}

        const profLabels = Object.keys(profissionais);
        const profValues = Object.values(profissionais);

        // Calcular produção por unidade
        const unidades = {};
        {% for item in producao %}
        if ('{{ item.unidade }}' in unidades) {
            unidades['{{ item.unidade }}'] += {{ item.valor }};
        } else {
            unidades['{{ item.unidade }}'] = {{ item.valor }};
        }
        {% endfor %}

        const unidLabels = Object.keys(unidades);
        const unidValues = Object.values(unidades);

        // Dados para tendência de produção
        const meses = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set'];
        const producaoHistorica = [
            {{ (producao|sum(attribute='valor') * 0.65) | round(2) }},
            {{ (producao|sum(attribute='valor') * 0.70) | round(2) }},
            {{ (producao|sum(attribute='valor') * 0.75) | round(2) }},
            {{ (producao|sum(attribute='valor') * 0.80) | round(2) }},
            {{ (producao|sum(attribute='valor') * 0.85) | round(2) }},
            {{ (producao|sum(attribute='valor') * 0.90) | round(2) }},
            {{ (producao|sum(attribute='valor') * 0.95) | round(2) }},
            {{ (producao|sum(attribute='valor') * 0.98) | round(2) }},
            {{ producao|sum(attribute='valor') | round(2) }}
        ];

        // Previsão para os próximos meses
        const mesesFuturos = ['Out', 'Nov', 'Dez'];
        const producaoPrevista = [
            {{ (producao|sum(attribute='valor') * 1.05) | round(2) }},
            {{ (producao|sum(attribute='valor') * 1.10) | round(2) }},
            {{ (producao|sum(attribute='valor') * 1.15) | round(2) }}
        ];

        // Dados para comparação de procedimentos
        const procedimentos = ['Consultas', 'Exames', 'Cirurgias', 'Procedimentos Estéticos', 'Fisioterapia'];
        const mesAtual = [45000, 32000, 65000, 38000, 28000];
        const mesAnterior = [42000, 30000, 62000, 29500, 26000];

        // Configurar gráfico de produção por profissional
        const profChart = new Chart(
            document.getElementById('producaoProfissionalChart'),
            {
                type: 'bar',
                data: {
                    labels: profLabels,
                    datasets: [{
                        label: 'Valor (R$)',
                        data: profValues,
                        backgroundColor: 'rgba(0, 122, 255, 0.7)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.dataset.label || '';
                                    const value = context.raw || 0;
                                    return `${label}: R$ ${value.toLocaleString('pt-BR', {minimumFractionDigits: 2, maximumFractionDigits: 2})}`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            }
        );

        // Configurar gráfico de produção por unidade
        const unidChart = new Chart(
            document.getElementById('producaoUnidadeChart'),
            {
                type: 'pie',
                data: {
                    labels: unidLabels,
                    datasets: [{
                        data: unidValues,
                        backgroundColor: [
                            'rgba(0, 122, 255, 0.7)',
                            'rgba(142, 142, 147, 0.7)',
                            'rgba(174, 174, 178, 0.7)',
                            'rgba(199, 199, 204, 0.7)',
                            'rgba(209, 209, 214, 0.7)'
                        ],
                        borderColor: [
                            'rgba(0, 122, 255, 1)',
                            'rgba(142, 142, 147, 1)',
                            'rgba(174, 174, 178, 1)',
                            'rgba(199, 199, 204, 1)',
                            'rgba(209, 209, 214, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = context.chart.data.datasets[0].data.reduce((a, b) => a + b, 0);
                                    const percentage = Math.round((value / total) * 100);
                                    return `${label}: R$ ${value.toLocaleString('pt-BR', {minimumFractionDigits: 2, maximumFractionDigits: 2})} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            }
        );

        // Configurar gráfico de tendência de produção
        const tendenciaChart = new Chart(
            document.getElementById('tendenciaProducaoChart'),
            {
                type: 'line',
                data: {
                    labels: [...meses, ...mesesFuturos],
                    datasets: [
                        {
                            label: 'Histórico',
                            data: [...producaoHistorica, null, null, null],
                            backgroundColor: 'rgba(0, 122, 255, 0.1)',
                            borderColor: 'rgba(0, 122, 255, 1)',
                            borderWidth: 2,
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: 'Previsão',
                            data: [null, null, null, null, null, null, null, null, producaoHistorica[8], ...producaoPrevista],
                            backgroundColor: 'rgba(0, 122, 255, 0.05)',
                            borderColor: 'rgba(0, 122, 255, 0.7)',
                            borderWidth: 2,
                            borderDash: [5, 5],
                            tension: 0.4,
                            fill: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.dataset.label || '';
                                    const value = context.raw || 0;
                                    return value ? `${label}: R$ ${value.toLocaleString('pt-BR', {minimumFractionDigits: 2, maximumFractionDigits: 2})}` : '';
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            }
        );

        // Configurar gráfico de comparação de procedimentos
        const comparacaoChart = new Chart(
            document.getElementById('comparacaoProcedimentosChart'),
            {
                type: 'bar',
                data: {
                    labels: procedimentos,
                    datasets: [
                        {
                            label: 'Mês Atual',
                            data: mesAtual,
                            backgroundColor: 'rgba(0, 122, 255, 0.7)',
                            borderColor: 'rgba(0, 122, 255, 1)',
                            borderWidth: 1
                        },
                        {
                            label: 'Mês Anterior',
                            data: mesAnterior,
                            backgroundColor: 'rgba(142, 142, 147, 0.7)',
                            borderColor: 'rgba(142, 142, 147, 1)',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.dataset.label || '';
                                    const value = context.raw || 0;
                                    return `${label}: R$ ${value.toLocaleString('pt-BR', {minimumFractionDigits: 2, maximumFractionDigits: 2})}`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            }
        );

        // Event listeners para os seletores de visualização
        document.getElementById('visualizacaoProfissional').addEventListener('change', function() {
            // Alterar o tipo de gráfico
            profChart.destroy(); // Destruir o gráfico atual

            // Criar novo gráfico com o tipo selecionado
            const newType = this.value;
            const newOptions = {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.dataset.label || '';
                                const value = context.raw || 0;
                                return `${label}: R$ ${value.toLocaleString('pt-BR', {minimumFractionDigits: 2, maximumFractionDigits: 2})}`;
                            }
                        }
                    }
                }
            };

            // Ajustar opções específicas para cada tipo de gráfico
            if (newType === 'bar' || newType === 'horizontalBar') {
                newOptions.indexAxis = newType === 'horizontalBar' ? 'y' : 'x';
                newOptions.scales = {
                    x: {
                        beginAtZero: true,
                        grid: {
                            display: newType === 'horizontalBar',
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            display: newType === 'bar',
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    }
                };
            } else if (newType === 'radar') {
                newOptions.scales = undefined;
                newOptions.plugins.legend.display = true;
                newOptions.elements = {
                    line: {
                        borderWidth: 3
                    }
                };
            }

            // Criar novo gráfico
            const ctx = document.getElementById('producaoProfissionalChart').getContext('2d');
            window.profChart = new Chart(ctx, {
                type: newType === 'horizontalBar' ? 'bar' : newType, // 'horizontalBar' é tratado com indexAxis
                data: {
                    labels: profLabels,
                    datasets: [{
                        label: 'Valor (R$)',
                        data: profValues,
                        backgroundColor: 'rgba(0, 122, 255, 0.7)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 1
                    }]
                },
                options: newOptions
            });
        });

        document.getElementById('visualizacaoUnidade').addEventListener('change', function() {
            // Alterar o tipo de gráfico
            unidChart.destroy(); // Destruir o gráfico atual

            // Criar novo gráfico com o tipo selecionado
            const newType = this.value;
            const ctx = document.getElementById('producaoUnidadeChart').getContext('2d');
            window.unidChart = new Chart(ctx, {
                type: newType,
                data: {
                    labels: unidLabels,
                    datasets: [{
                        data: unidValues,
                        backgroundColor: [
                            'rgba(0, 122, 255, 0.7)',
                            'rgba(142, 142, 147, 0.7)',
                            'rgba(174, 174, 178, 0.7)',
                            'rgba(199, 199, 204, 0.7)',
                            'rgba(209, 209, 214, 0.7)'
                        ],
                        borderColor: [
                            'rgba(0, 122, 255, 1)',
                            'rgba(142, 142, 147, 1)',
                            'rgba(174, 174, 178, 1)',
                            'rgba(199, 199, 204, 1)',
                            'rgba(209, 209, 214, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = context.chart.data.datasets[0].data.reduce((a, b) => a + b, 0);
                                    const percentage = Math.round((value / total) * 100);
                                    return `${label}: R$ ${value.toLocaleString('pt-BR', {minimumFractionDigits: 2, maximumFractionDigits: 2})} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });
        });

        document.getElementById('periodoComparacao').addEventListener('change', function() {
            // Alterar os dados do gráfico de comparação
            const periodo = this.value;

            if (periodo === 'trimestre') {
                // Dados para comparação trimestral (valores diferentes)
                comparacaoChart.data.datasets[0].data = [135000, 96000, 195000, 114000, 84000];
                comparacaoChart.data.datasets[1].data = [126000, 90000, 186000, 88500, 78000];
                comparacaoChart.data.datasets[0].label = 'Trimestre Atual';
                comparacaoChart.data.datasets[1].label = 'Trimestre Anterior';
            } else {
                // Dados para comparação mensal (valores originais)
                comparacaoChart.data.datasets[0].data = mesAtual;
                comparacaoChart.data.datasets[1].data = mesAnterior;
                comparacaoChart.data.datasets[0].label = 'Mês Atual';
                comparacaoChart.data.datasets[1].label = 'Mês Anterior';
            }

            comparacaoChart.update();
        });

        // Adicionar funcionalidade aos botões de filtro
        document.querySelector('button.bg-systemBlue').addEventListener('click', function() {
            // Simular filtragem de dados
            alert('Filtros aplicados! Em um ambiente real, os dados seriam filtrados de acordo com os critérios selecionados.');
        });

        // Adicionar funcionalidade aos botões de exportação e impressão
        document.querySelectorAll('.bg-systemGray-ultralight').forEach(button => {
            button.addEventListener('click', function() {
                if (this.textContent.trim() === 'Exportar') {
                    alert('Exportando dados... Em um ambiente real, os dados seriam exportados para Excel ou CSV.');
                } else if (this.textContent.trim() === 'Imprimir') {
                    alert('Preparando impressão... Em um ambiente real, seria aberta a janela de impressão.');
                }
            });
        });
    });
</script>
{% endblock %}

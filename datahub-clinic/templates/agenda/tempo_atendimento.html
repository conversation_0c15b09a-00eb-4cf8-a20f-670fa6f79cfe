{% extends 'base.html' %}

{% block title %}Tempo de Atendimento - Amigo DataHub{% endblock %}

{% block header %}Tempo de Atendimento{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="bg-gradient-to-r from-blue-50 to-white rounded-view p-8 border border-gray-200 mb-6 overflow-hidden relative">
    <!-- Background Elements -->
    <div class="absolute top-0 right-0 w-full h-full opacity-10 overflow-hidden">
        <svg class="absolute top-10 right-10 w-64 h-64 text-systemBlue" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <svg class="absolute bottom-10 left-1/4 w-32 h-32 text-systemBlue" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
    </div>

    <div class="flex flex-col md:flex-row items-center relative z-10">
        <div class="md:w-2/3 mb-6 md:mb-0 md:pr-8">
            <div class="inline-block bg-systemBlue text-white text-xs font-semibold px-3 py-1 rounded-full mb-3">AGENDA</div>
            <h1 class="text-3xl font-bold text-gray-800 mb-4">Análise de Tempo de Atendimento</h1>
            <p class="text-gray-600 mb-6">Monitore e otimize os tempos de atendimento e espera para melhorar a experiência do paciente e a eficiência operacional da clínica com análises detalhadas e recomendações inteligentes.</p>
            <div class="grid grid-cols-3 gap-4 mb-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">{{ tempo_medio }} min</div>
                    <div class="text-xs text-gray-500">Tempo médio de atendimento</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">{{ espera_media }} min</div>
                    <div class="text-xs text-gray-500">Tempo médio de espera</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">{{ eficiencia }}%</div>
                    <div class="text-xs text-gray-500">Eficiência operacional</div>
                </div>
            </div>
            <div class="flex flex-wrap gap-3">
                <button class="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    Gerar Relatório
                </button>
                <button class="bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-md text-sm border border-gray-200 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                    </svg>
                    Filtrar Dados
                </button>
            </div>
        </div>
        <div class="md:w-1/3 flex justify-center">
            <div class="w-64 h-64 bg-white rounded-full shadow-lg flex items-center justify-center p-4 relative">
                <div class="absolute inset-0 rounded-full border-8 border-blue-100"></div>
                <div class="absolute inset-0 rounded-full border-8 border-systemBlue border-opacity-70" style="clip-path: polygon(50% 50%, 100% 0, 100% 50%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-300 border-opacity-70" style="clip-path: polygon(50% 50%, 100% 50%, 100% 100%, 50% 100%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-200 border-opacity-70" style="clip-path: polygon(50% 50%, 50% 100%, 0 100%, 0 50%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-400 border-opacity-70" style="clip-path: polygon(50% 50%, 0 50%, 0 0, 50% 0);"></div>
                <div class="z-10 text-center">
                    <div class="text-3xl font-bold text-gray-800">-15%</div>
                    <div class="text-sm text-gray-500">Potencial de otimização</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Resumo e Insights -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <!-- Tempo Médio de Atendimento -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-gray-800">Tempo Médio de Atendimento</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">
            {{ tempo_medio }} min
        </p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Tempo médio de duração dos atendimentos.</span>
            <span class="ml-auto text-systemBlue font-medium">-5%</span>
        </div>
    </div>

    <!-- Tempo Médio de Espera -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-gray-800">Tempo Médio de Espera</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">
            {{ espera_media }} min
        </p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Tempo médio de espera antes do atendimento.</span>
            <span class="ml-auto text-gray-800 font-medium">+8%</span>
        </div>
    </div>

    <!-- Eficiência Operacional -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-gray-800">Eficiência Operacional</p>
        </div>
        <p class="text-3xl font-semibold text-gray-800 mb-1">
            {{ eficiencia }}%
        </p>
        <div class="flex items-center text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Relação entre tempo de atendimento e espera.</span>
            <span class="ml-auto {% if eficiencia > 75 %}text-systemBlue{% else %}text-gray-800{% endif %} font-medium">{% if eficiencia > 75 %}+{% endif %}{{ eficiencia - 75 }}%</span>
        </div>
    </div>

    <!-- Previsão de Otimização -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex justify-between items-center mb-2">
            <p class="text-sm font-semibold text-gray-800">Potencial de Otimização</p>
            <div class="flex items-center">
                <svg class="w-4 h-4 text-systemBlue mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
                </svg>
                <span class="text-xs text-gray-800 font-medium">Amigo Intelligence</span>
            </div>
        </div>
        <p class="text-3xl font-semibold text-systemBlue mb-1">{{ potencial_otimizacao }} min</p>
        <div class="flex justify-between text-xs text-gray-500 mt-auto pt-2 border-t border-gray-200">
            <span>Redução possível no tempo médio</span>
            <span class="text-systemBlue">-15%</span>
        </div>
    </div>
</div>

<!-- Insights com IA -->
<div class="mb-5">
    <div class="flex items-center justify-between mb-4">
        <div class="flex items-center">
            <svg class="w-5 h-5 text-systemBlue mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
            </svg>
            <h2 class="text-base font-semibold text-gray-800">Amigo Intelligence</h2>
        </div>
        <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">Atualizado agora</span>
    </div>

    <div id="insights-container" class="grid grid-cols-1 md:grid-cols-3 gap-3">
        {% for insight in insights %}
            {% with
                title=insight.title,
                description=insight.description,
                insight_type=insight.insight_type,
                content=insight.content,
                category=insight.category
            %}
                {% include 'components/ai_insight_widget.html' %}
            {% endwith %}
        {% endfor %}
    </div>
</div>

<script>
    // Carregar insights dinamicamente após 5 segundos (para demonstração)
    setTimeout(() => {
        // Contexto específico da página de tempo de atendimento
        const pageContext = {
            page_title: "Tempo de Atendimento",
            page_description: "Análise do tempo de atendimento, com foco em eficiência e satisfação do paciente",
            key_metrics: {
                "Tempo Médio": document.querySelector('.kpi-tempo-medio .value')?.textContent || "42 min",
                "Variação Mensal": document.querySelector('.kpi-variacao-mensal .value')?.textContent || "-5%",
                "Satisfação do Paciente": document.querySelector('.kpi-satisfacao .value')?.textContent || "4.2/5"
            },
            analysis_focus: "Otimização do tempo de atendimento e satisfação do paciente",
            page_elements: [
                "Tempo por Tipo de Atendimento",
                "Tempo por Profissional",
                "Evolução Mensal",
                "Correlação Tempo x Satisfação"
            ]
        };

        loadInsights('agenda', 'tempo_atendimento', 'insights-container', 3, ['list', 'text', 'stat'], pageContext);
    }, 5000);
</script>

<!-- Insights e Recomendações Adicionais -->
<div class="bg-white rounded-view p-6 border border-gray-200 mb-6">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-base font-semibold text-gray-800">Insights de Eficiência</h2>
        <div class="flex items-center">
            <svg class="w-4 h-4 text-systemBlue mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
            </svg>
            <span class="text-xs text-gray-800 font-medium">Powered by Amigo Intelligence</span>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Pontos Críticos -->
        <div class="bg-gray-50 p-5 rounded-md border border-gray-200">
            <div class="flex items-center mb-3">
                <div class="w-10 h-10 bg-white rounded-full flex items-center justify-center mr-3 border border-gray-200 shadow-sm">
                    <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-sm font-semibold text-gray-800">Pontos Críticos</h3>
                    <p class="text-xs text-gray-500">Horários e unidades com maior espera</p>
                </div>
            </div>
            <ul class="space-y-3 text-sm">
                <li class="flex justify-between items-center p-2 bg-white rounded-md border border-gray-200">
                    <span class="text-gray-700">Morumbi (9h-11h)</span>
                    <span class="font-medium text-systemBlue">25 min</span>
                </li>
                <li class="flex justify-between items-center p-2 bg-white rounded-md border border-gray-200">
                    <span class="text-gray-700">Recife (14h-16h)</span>
                    <span class="font-medium text-systemBlue">22 min</span>
                </li>
                <li class="flex justify-between items-center p-2 bg-white rounded-md border border-gray-200">
                    <span class="text-gray-700">BRASILIA (8h-10h)</span>
                    <span class="font-medium text-systemBlue">18 min</span>
                </li>
            </ul>
        </div>

        <!-- Procedimentos Otimizáveis -->
        <div class="bg-gray-50 p-5 rounded-md border border-gray-200">
            <div class="flex items-center mb-3">
                <div class="w-10 h-10 bg-white rounded-full flex items-center justify-center mr-3 border border-gray-200 shadow-sm">
                    <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-sm font-semibold text-gray-800">Procedimentos Otimizáveis</h3>
                    <p class="text-xs text-gray-500">Potencial de redução de tempo</p>
                </div>
            </div>
            <ul class="space-y-3 text-sm">
                <li class="flex justify-between items-center p-2 bg-white rounded-md border border-gray-200">
                    <span class="text-gray-700">Consulta Ortopédica</span>
                    <span class="font-medium text-systemBlue">-22%</span>
                </li>
                <li class="flex justify-between items-center p-2 bg-white rounded-md border border-gray-200">
                    <span class="text-gray-700">Exame Dermatológico</span>
                    <span class="font-medium text-systemBlue">-18%</span>
                </li>
                <li class="flex justify-between items-center p-2 bg-white rounded-md border border-gray-200">
                    <span class="text-gray-700">Fisioterapia</span>
                    <span class="font-medium text-systemBlue">-15%</span>
                </li>
            </ul>
        </div>

        <!-- Recomendações -->
        <div class="bg-gray-50 p-5 rounded-md border border-gray-200">
            <div class="flex items-center mb-3">
                <div class="w-10 h-10 bg-white rounded-full flex items-center justify-center mr-3 border border-gray-200 shadow-sm">
                    <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-sm font-semibold text-gray-800">Recomendações</h3>
                    <p class="text-xs text-gray-500">Ações para otimização</p>
                </div>
            </div>
            <ul class="space-y-3 text-sm">
                <li class="flex items-center p-2 bg-white rounded-md border border-gray-200">
                    <svg class="w-4 h-4 text-systemBlue mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    <span class="text-gray-700">Redistribuir agendamentos nos horários de pico</span>
                </li>
                <li class="flex items-center p-2 bg-white rounded-md border border-gray-200">
                    <svg class="w-4 h-4 text-systemBlue mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    <span class="text-gray-700">Padronizar protocolos de atendimento ortopédico</span>
                </li>
                <li class="flex items-center p-2 bg-white rounded-md border border-gray-200">
                    <svg class="w-4 h-4 text-systemBlue mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    <span class="text-gray-700">Implementar check-in digital para reduzir espera</span>
                </li>
            </ul>
        </div>
    </div>
</div>

<!-- Filtros -->
<div class="bg-white rounded-view p-5 mb-6 border border-gray-200">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-sm font-semibold text-gray-800">Filtros</h2>
        <button class="text-xs text-systemBlue hover:text-blue-700 transition-colors">Limpar Filtros</button>
    </div>
    <div class="flex flex-wrap gap-4">
        <div class="w-full md:w-auto">
            <label for="data_inicio" class="block text-xs font-medium text-gray-500 mb-1">Data Início</label>
            <input type="date" id="data_inicio" class="block w-full px-3 py-2 text-sm border border-gray-200 focus:outline-none focus:ring-1 focus:ring-systemBlue focus:border-systemBlue rounded-md transition duration-150 ease-in-out">
        </div>
        <div class="w-full md:w-auto">
            <label for="data_fim" class="block text-xs font-medium text-gray-500 mb-1">Data Fim</label>
            <input type="date" id="data_fim" class="block w-full px-3 py-2 text-sm border border-gray-200 focus:outline-none focus:ring-1 focus:ring-systemBlue focus:border-systemBlue rounded-md transition duration-150 ease-in-out">
        </div>
        <div class="w-full md:w-auto">
            <label for="unidade" class="block text-xs font-medium text-gray-500 mb-1">Unidade</label>
            <select id="unidade" class="block w-full px-3 py-2 text-sm border border-gray-200 focus:outline-none focus:ring-1 focus:ring-systemBlue focus:border-systemBlue rounded-md transition duration-150 ease-in-out">
                <option value="">Todas</option>
                <option>Morumbi</option>
                <option>BRASILIA</option>
                <option>Recife</option>
                <option>Espinheiro</option>
                <option>CLÍNICA (RL)</option>
            </select>
        </div>
        <div class="w-full md:w-auto">
            <label for="profissional" class="block text-xs font-medium text-gray-500 mb-1">Profissional</label>
            <select id="profissional" class="block w-full px-3 py-2 text-sm border border-gray-200 focus:outline-none focus:ring-1 focus:ring-systemBlue focus:border-systemBlue rounded-md transition duration-150 ease-in-out">
                <option value="">Todos</option>
                <option>Equipe Amigo - BRUNO LIMA</option>
                <option>Equipe Amigo - Caio Menezes</option>
                <option>Equipe Amigo - Giulia Pedrosa 2</option>
                <option>Rayara Toledo Souza</option>
            </select>
        </div>
        <div class="w-full md:w-auto flex items-end">
            <button class="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-md transition duration-150 ease-in-out text-sm">
                Filtrar
            </button>
        </div>
    </div>
</div>

<!-- Análises Avançadas -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Tempo Médio por Tipo de Atendimento -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-gray-800">Tempo Médio por Tipo de Atendimento</h2>
            <div class="flex items-center">
                <select id="visualizacaoTipo" class="text-xs border border-gray-200 rounded-md px-2 py-1 focus:outline-none focus:ring-1 focus:ring-systemBlue focus:border-systemBlue">
                    <option value="bar">Barras</option>
                    <option value="radar">Radar</option>
                    <option value="polarArea">Polar</option>
                </select>
            </div>
        </div>
        <div class="h-64">
            <canvas id="tempoTipoChart"></canvas>
        </div>
        <div class="mt-3 text-xs text-gray-500 bg-gray-50 p-3 rounded-md">
            <p><strong class="text-gray-700">Insight:</strong> Consultas ortopédicas estão 22% acima do tempo médio ideal. Recomenda-se revisão do protocolo de atendimento.</p>
        </div>
    </div>

    <!-- Tempo Médio por Profissional -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-gray-800">Tempo Médio por Profissional</h2>
            <div class="flex items-center">
                <select id="visualizacaoProfissional" class="text-xs border border-gray-200 rounded-md px-2 py-1 focus:outline-none focus:ring-1 focus:ring-systemBlue focus:border-systemBlue">
                    <option value="horizontalBar">Barras Horizontais</option>
                    <option value="bar">Barras Verticais</option>
                    <option value="line">Linha</option>
                </select>
            </div>
        </div>
        <div class="h-64">
            <canvas id="tempoProfissionalChart"></canvas>
        </div>
        <div class="mt-3 text-xs text-gray-500 bg-gray-50 p-3 rounded-md">
            <p><strong class="text-gray-700">Insight:</strong> Dr. Bruno Lima apresenta o menor tempo médio de atendimento (15% abaixo da média) mantendo alta satisfação do paciente.</p>
        </div>
    </div>
</div>

<!-- Tendências e Comparações -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Tendência de Tempo de Atendimento -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-gray-800">Tendência de Tempo de Atendimento</h2>
            <div class="flex items-center">
                <svg class="w-4 h-4 text-systemBlue mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
                </svg>
                <span class="text-xs text-gray-800 font-medium">Amigo Intelligence</span>
            </div>
        </div>
        <div class="h-64">
            <canvas id="tendenciaTempoChart"></canvas>
        </div>
        <div class="mt-3 text-xs text-gray-500 bg-gray-50 p-3 rounded-md">
            <p><strong class="text-gray-700">Previsão:</strong> Redução projetada de 15% no tempo médio de atendimento nos próximos 3 meses com implementação das recomendações.</p>
        </div>
    </div>

    <!-- Correlação Tempo x Satisfação -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-gray-800">Correlação Tempo x Satisfação</h2>
            <div class="flex items-center">
                <svg class="w-4 h-4 text-systemBlue mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
                </svg>
                <span class="text-xs text-gray-800 font-medium">Amigo Intelligence</span>
            </div>
        </div>
        <div class="h-64">
            <canvas id="correlacaoChart"></canvas>
        </div>
        <div class="mt-3 text-xs text-gray-500 bg-gray-50 p-3 rounded-md">
            <p><strong class="text-gray-700">Insight:</strong> Não há correlação significativa entre tempo de atendimento e satisfação até 30 minutos. Acima disso, cada 5 minutos adicionais reduz a satisfação em 0.2 pontos.</p>
        </div>
    </div>
</div>

<!-- Tabela de Tempos -->
<div class="bg-white rounded-view p-6 border border-gray-200">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-base font-semibold text-gray-800">Detalhamento dos Tempos</h2>
        <div class="flex space-x-2">
            <button class="bg-white hover:bg-gray-50 text-gray-700 px-3 py-1 rounded-md transition duration-150 ease-in-out text-xs border border-gray-200">
                Exportar
            </button>
            <button class="bg-white hover:bg-gray-50 text-gray-700 px-3 py-1 rounded-md transition duration-150 ease-in-out text-xs border border-gray-200">
                Imprimir
            </button>
        </div>
    </div>

    <div class="overflow-x-auto">
        <table class="w-full text-sm">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Código</th>
                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Data</th>
                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Paciente</th>
                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Profissional</th>
                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tipo</th>
                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hora Agendada</th>
                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hora Chegada</th>
                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Início</th>
                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fim</th>
                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Espera (min)</th>
                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duração (min)</th>
                </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
                {% for tempo in tempos[:8] %}
                <tr class="hover:bg-gray-50">
                    <td class="px-4 py-3 text-gray-800">{{ tempo.codigo }}</td>
                    <td class="px-4 py-3 text-gray-800">{{ tempo.data }}</td>
                    <td class="px-4 py-3 text-gray-800">{{ tempo.paciente }}</td>
                    <td class="px-4 py-3 text-gray-800">{{ tempo.profissional }}</td>
                    <td class="px-4 py-3 text-gray-800">{{ tempo.tipo_atendimento }}</td>
                    <td class="px-4 py-3 text-gray-800">{{ tempo.hora_agendamento }}</td>
                    <td class="px-4 py-3 text-gray-800">{{ tempo.hora_chegada }}</td>
                    <td class="px-4 py-3 text-gray-800">{{ tempo.inicio_atendimento }}</td>
                    <td class="px-4 py-3 text-gray-800">{{ tempo.fim_atendimento }}</td>
                    <td class="px-4 py-3 text-gray-800">{{ tempo.espera_minutos }}</td>
                    <td class="px-4 py-3 text-gray-800">{{ tempo.tempo_atendimento }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Paginação -->
    <div class="flex justify-between items-center mt-4">
        <div class="text-sm text-gray-500">
            Mostrando 8 de {{ tempos|length }} registros
        </div>
        <div>
            <button class="px-4 py-2 rounded-md bg-white border border-gray-200 text-systemBlue text-sm hover:bg-gray-50">
                Ver todos os registros
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Configuração global para cores consistentes
        Chart.defaults.color = '#6B7280'; // text-gray-500
        Chart.defaults.borderColor = '#E5E7EB'; // border-gray-200

        // Calcular tempo médio por tipo de atendimento
        const tipoTempos = {};
        const tipoContagem = {};

        {% for tempo in tempos %}
        if ('{{ tempo.tipo_atendimento }}' in tipoTempos) {
            tipoTempos['{{ tempo.tipo_atendimento }}'] += {{ tempo.tempo_atendimento }};
            tipoContagem['{{ tempo.tipo_atendimento }}'] += 1;
        } else {
            tipoTempos['{{ tempo.tipo_atendimento }}'] = {{ tempo.tempo_atendimento }};
            tipoContagem['{{ tempo.tipo_atendimento }}'] = 1;
        }
        {% endfor %}

        const tipoLabels = Object.keys(tipoTempos);
        const tipoValues = tipoLabels.map(tipo => tipoTempos[tipo] / tipoContagem[tipo]);

        // Calcular tempo médio por profissional
        const profTempos = {};
        const profContagem = {};

        {% for tempo in tempos %}
        if ('{{ tempo.profissional }}' in profTempos) {
            profTempos['{{ tempo.profissional }}'] += {{ tempo.tempo_atendimento }};
            profContagem['{{ tempo.profissional }}'] += 1;
        } else {
            profTempos['{{ tempo.profissional }}'] = {{ tempo.tempo_atendimento }};
            profContagem['{{ tempo.profissional }}'] = 1;
        }
        {% endfor %}

        const profLabels = Object.keys(profTempos);
        const profValues = profLabels.map(prof => profTempos[prof] / profContagem[prof]);

        // Dados para tendência de tempo
        const meses = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set'];
        {% set tempo_medio = (tempos|sum(attribute='tempo_atendimento') / (tempos|length if tempos|length > 0 else 1))|round|int %}
        const tempoHistorico = [
            {{ tempo_medio * 1.15 }},
            {{ tempo_medio * 1.12 }},
            {{ tempo_medio * 1.10 }},
            {{ tempo_medio * 1.08 }},
            {{ tempo_medio * 1.05 }},
            {{ tempo_medio * 1.03 }},
            {{ tempo_medio * 1.01 }},
            {{ tempo_medio * 1.00 }},
            {{ tempo_medio }}
        ];

        // Previsão para os próximos meses
        const mesesFuturos = ['Out', 'Nov', 'Dez'];
        const tempoPrevisto = [
            {{ tempo_medio * 0.95 }},
            {{ tempo_medio * 0.90 }},
            {{ tempo_medio * 0.85 }}
        ];

        // Dados para correlação tempo x satisfação
        const atendimentos = [
            { tempo: 15, satisfacao: 4.9 },
            { tempo: 20, satisfacao: 4.8 },
            { tempo: 25, satisfacao: 4.8 },
            { tempo: 30, satisfacao: 4.7 },
            { tempo: 35, satisfacao: 4.5 },
            { tempo: 40, satisfacao: 4.3 },
            { tempo: 45, satisfacao: 4.0 },
            { tempo: 50, satisfacao: 3.8 },
            { tempo: 55, satisfacao: 3.5 },
            { tempo: 60, satisfacao: 3.2 }
        ];

        // Configurar gráfico de tempo por tipo de atendimento
        const tipoChart = new Chart(
            document.getElementById('tempoTipoChart'),
            {
                type: 'bar',
                data: {
                    labels: tipoLabels,
                    datasets: [{
                        label: 'Tempo Médio (min)',
                        data: tipoValues,
                        backgroundColor: '#0A84FF', // systemBlue
                        borderColor: '#0A84FF',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.dataset.label || '';
                                    const value = context.raw || 0;
                                    return `${label}: ${value.toFixed(1)} min`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: '#F3F4F6' // gray-100
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            }
        );

        // Configurar gráfico de tempo por profissional
        const profChart = new Chart(
            document.getElementById('tempoProfissionalChart'),
            {
                type: 'bar',
                data: {
                    labels: profLabels,
                    datasets: [{
                        label: 'Tempo Médio (min)',
                        data: profValues,
                        backgroundColor: '#0A84FF', // systemBlue
                        borderColor: '#0A84FF',
                        borderWidth: 1
                    }]
                },
                options: {
                    indexAxis: 'y',
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.dataset.label || '';
                                    const value = context.raw || 0;
                                    return `${label}: ${value.toFixed(1)} min`;
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: '#F3F4F6' // gray-100
                            }
                        },
                        y: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            }
        );

        // Configurar gráfico de tendência de tempo
        const tendenciaChart = new Chart(
            document.getElementById('tendenciaTempoChart'),
            {
                type: 'line',
                data: {
                    labels: [...meses, ...mesesFuturos],
                    datasets: [
                        {
                            label: 'Histórico',
                            data: [...tempoHistorico, null, null, null],
                            backgroundColor: 'rgba(10, 132, 255, 0.1)', // systemBlue com transparência
                            borderColor: '#0A84FF',
                            borderWidth: 2,
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: 'Previsão',
                            data: [null, null, null, null, null, null, null, null, tempoHistorico[8], ...tempoPrevisto],
                            backgroundColor: 'rgba(156, 163, 175, 0.1)', // gray-400 com transparência
                            borderColor: '#9CA3AF', // gray-400
                            borderWidth: 2,
                            borderDash: [5, 5],
                            tension: 0.4,
                            fill: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.dataset.label || '';
                                    const value = context.raw || 0;
                                    return value ? `${label}: ${value.toFixed(1)} min` : '';
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            title: {
                                display: true,
                                text: 'Tempo Médio (min)',
                                color: '#4B5563' // gray-600
                            },
                            grid: {
                                display: true,
                                color: '#F3F4F6' // gray-100
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            }
        );

        // Configurar gráfico de correlação tempo x satisfação
        const correlacaoChart = new Chart(
            document.getElementById('correlacaoChart'),
            {
                type: 'scatter',
                data: {
                    datasets: [{
                        label: 'Atendimentos',
                        data: atendimentos.map(a => ({
                            x: a.tempo,
                            y: a.satisfacao
                        })),
                        backgroundColor: '#0A84FF', // systemBlue
                        borderColor: '#0A84FF',
                        borderWidth: 1,
                        pointRadius: 6,
                        pointHoverRadius: 8
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const index = context.dataIndex;
                                    const atendimento = atendimentos[index];
                                    return `Tempo: ${atendimento.tempo} min, Satisfação: ${atendimento.satisfacao}`;
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'Tempo de Atendimento (min)',
                                color: '#4B5563' // gray-600
                            },
                            min: 10,
                            max: 65,
                            grid: {
                                display: true,
                                color: '#F3F4F6' // gray-100
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Satisfação do Paciente',
                                color: '#4B5563' // gray-600
                            },
                            min: 3.0,
                            max: 5.0,
                            grid: {
                                display: true,
                                color: '#F3F4F6' // gray-100
                            }
                        }
                    }
                }
            }
        );

        // Event listeners para os seletores de visualização
        document.getElementById('visualizacaoTipo').addEventListener('change', function() {
            // Alterar o tipo de gráfico
            tipoChart.destroy(); // Destruir o gráfico atual

            // Criar novo gráfico com o tipo selecionado
            const newType = this.value;
            const newOptions = {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.dataset.label || '';
                                const value = context.raw || 0;
                                return `${label}: ${value.toFixed(1)} min`;
                            }
                        }
                    }
                }
            };

            // Ajustar opções específicas para cada tipo de gráfico
            if (newType === 'bar') {
                newOptions.scales = {
                    y: {
                        beginAtZero: true,
                        grid: {
                            display: true,
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                };
            } else if (newType === 'radar') {
                newOptions.scales = undefined;
                newOptions.elements = {
                    line: {
                        borderWidth: 3
                    }
                };
            } else if (newType === 'polarArea') {
                newOptions.scales = undefined;
            }

            // Criar novo gráfico
            const ctx = document.getElementById('tempoTipoChart').getContext('2d');
            window.tipoChart = new Chart(ctx, {
                type: newType,
                data: {
                    labels: tipoLabels,
                    datasets: [{
                        label: 'Tempo Médio (min)',
                        data: tipoValues,
                        backgroundColor: [
                            'rgba(0, 122, 255, 0.7)',
                            'rgba(142, 142, 147, 0.7)',
                            'rgba(174, 174, 178, 0.7)',
                            'rgba(199, 199, 204, 0.7)',
                            'rgba(209, 209, 214, 0.7)',
                            'rgba(229, 229, 234, 0.7)'
                        ],
                        borderColor: [
                            'rgba(0, 122, 255, 1)',
                            'rgba(142, 142, 147, 1)',
                            'rgba(174, 174, 178, 1)',
                            'rgba(199, 199, 204, 1)',
                            'rgba(209, 209, 214, 1)',
                            'rgba(229, 229, 234, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: newOptions
            });
        });

        document.getElementById('visualizacaoProfissional').addEventListener('change', function() {
            // Alterar o tipo de gráfico
            profChart.destroy(); // Destruir o gráfico atual

            // Criar novo gráfico com o tipo selecionado
            const newType = this.value;
            const newOptions = {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.dataset.label || '';
                                const value = context.raw || 0;
                                return `${label}: ${value.toFixed(1)} min`;
                            }
                        }
                    }
                }
            };

            // Ajustar opções específicas para cada tipo de gráfico
            if (newType === 'horizontalBar') {
                newOptions.indexAxis = 'y';
                newOptions.scales = {
                    x: {
                        beginAtZero: true,
                        grid: {
                            display: true,
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    },
                    y: {
                        grid: {
                            display: false
                        }
                    }
                };
            } else if (newType === 'bar') {
                newOptions.scales = {
                    y: {
                        beginAtZero: true,
                        grid: {
                            display: true,
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                };
            } else if (newType === 'line') {
                newOptions.scales = {
                    y: {
                        beginAtZero: true,
                        grid: {
                            display: true,
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                };
                newOptions.elements = {
                    line: {
                        tension: 0.4
                    }
                };
            }

            // Criar novo gráfico
            const ctx = document.getElementById('tempoProfissionalChart').getContext('2d');
            window.profChart = new Chart(ctx, {
                type: newType === 'horizontalBar' ? 'bar' : newType, // 'horizontalBar' é tratado com indexAxis
                data: {
                    labels: profLabels,
                    datasets: [{
                        label: 'Tempo Médio (min)',
                        data: profValues,
                        backgroundColor: 'rgba(0, 122, 255, 0.7)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 1
                    }]
                },
                options: newOptions
            });
        });

        // Adicionar funcionalidade aos botões de filtro
        document.querySelector('button.bg-systemBlue').addEventListener('click', function() {
            // Simular filtragem de dados
            alert('Filtros aplicados! Em um ambiente real, os dados seriam filtrados de acordo com os critérios selecionados.');
        });

        // Adicionar funcionalidade aos botões de exportação e impressão
        document.querySelectorAll('.bg-white.hover\\:bg-gray-50').forEach(button => {
            button.addEventListener('click', function() {
                if (this.textContent.trim() === 'Exportar') {
                    alert('Exportando dados... Em um ambiente real, os dados seriam exportados para Excel ou CSV.');
                } else if (this.textContent.trim() === 'Imprimir') {
                    alert('Preparando impressão... Em um ambiente real, seria aberta a janela de impressão.');
                }
            });
        });
    });
</script>
{% endblock %}

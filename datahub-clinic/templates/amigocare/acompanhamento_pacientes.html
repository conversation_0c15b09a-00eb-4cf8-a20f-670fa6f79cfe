{% extends 'base.html' %}

{% block title %}Acompanhamento de Pacientes - Amigo DataHub{% endblock %}

{% block header %}Acompanhamento de Pacientes{% endblock %}

{% block content %}
<!-- Resumo e Insights -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <!-- Total de Pacientes -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-label-DEFAULT">Total de Pacientes</p>
        </div>
        <p class="text-3xl font-semibold text-label-DEFAULT mb-1">
            {{ '{:,}'.format(acompanhamentos|length).replace(',', '.') if acompanhamentos|length < 1000 else '{:.1f}K'.format(acompanhamentos|length/1000) }}
        </p>
        <div class="flex items-center text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">
            <span>Em acompanhamento</span>
        </div>
    </div>

    <!-- Em Tratamento -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-label-DEFAULT">Em Tratamento</p>
        </div>
        {% set em_tratamento = acompanhamentos|selectattr('status', 'equalto', 'Em tratamento')|list|length %}
        <p class="text-3xl font-semibold text-label-DEFAULT mb-1">
            {{ '{:,}'.format(em_tratamento).replace(',', '.') if em_tratamento < 1000 else '{:.1f}K'.format(em_tratamento/1000) }}
        </p>
        <div class="flex items-center text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">
            <span>Pacientes ativos</span>
            <span class="ml-auto text-systemBlue font-medium">{{ (em_tratamento / acompanhamentos|length * 100)|round|int }}%</span>
        </div>
    </div>

    <!-- Concluídos -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-label-DEFAULT">Concluídos</p>
        </div>
        {% set concluidos = acompanhamentos|selectattr('status', 'equalto', 'Concluído')|list|length %}
        <p class="text-3xl font-semibold text-label-DEFAULT mb-1">
            {{ '{:,}'.format(concluidos).replace(',', '.') if concluidos < 1000 else '{:.1f}K'.format(concluidos/1000) }}
        </p>
        <div class="flex items-center text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">
            <span>Tratamentos finalizados</span>
            <span class="ml-auto text-systemGreen font-medium">{{ (concluidos / acompanhamentos|length * 100)|round|int }}%</span>
        </div>
    </div>

    <!-- Taxa de Sucesso -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-label-DEFAULT">Taxa de Sucesso</p>
        </div>
        {% set resultados_excelentes = acompanhamentos|selectattr('resultado', 'equalto', 'Excelente')|list|length %}
        {% set resultados_bons = acompanhamentos|selectattr('resultado', 'equalto', 'Bom')|list|length %}
        {% set taxa_sucesso = ((resultados_excelentes + resultados_bons) / concluidos * 100)|round|int if concluidos > 0 else 0 %}
        <p class="text-3xl font-semibold text-label-DEFAULT mb-1">
            {{ taxa_sucesso }}%
        </p>
        <div class="flex items-center text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">
            <span>Resultados Excelente/Bom</span>
            <span class="ml-auto {% if taxa_sucesso >= 80 %}text-systemGreen{% elif taxa_sucesso >= 60 %}text-systemBlue{% else %}text-red-600{% endif %} font-medium">
                {% if taxa_sucesso >= 80 %}Ótimo{% elif taxa_sucesso >= 60 %}Bom{% else %}Precisa melhorar{% endif %}
            </span>
        </div>
    </div>
</div>

<!-- Filtros -->
<div class="bg-white rounded-view p-4 mb-6 border border-gray-200">
    <div class="flex flex-wrap gap-4">
        <div class="w-full md:w-auto">
            <label for="profissional" class="block text-xs font-medium text-label-secondary mb-1">Profissional</label>
            <select id="profissional" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
                <option value="">Todos</option>
                {% for profissional in acompanhamentos|map(attribute='profissional')|unique %}
                <option>{{ profissional }}</option>
                {% endfor %}
            </select>
        </div>
        <div class="w-full md:w-auto">
            <label for="tipo_tratamento" class="block text-xs font-medium text-label-secondary mb-1">Tipo de Tratamento</label>
            <select id="tipo_tratamento" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
                <option value="">Todos</option>
                {% for tipo in acompanhamentos|map(attribute='tipo_tratamento')|unique %}
                <option>{{ tipo }}</option>
                {% endfor %}
            </select>
        </div>
        <div class="w-full md:w-auto">
            <label for="status" class="block text-xs font-medium text-label-secondary mb-1">Status</label>
            <select id="status" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
                <option value="">Todos</option>
                <option>Em tratamento</option>
                <option>Concluído</option>
                <option>Interrompido</option>
                <option>Aguardando retorno</option>
            </select>
        </div>
        <div class="w-full md:w-auto flex items-end">
            <button class="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-control transition duration-150 ease-in-out text-sm">
                Filtrar
            </button>
        </div>
    </div>
</div>

<!-- Gráficos -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Distribuição por Tipo de Tratamento -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <h2 class="text-base font-semibold text-label-DEFAULT mb-4">Distribuição por Tipo de Tratamento</h2>
        <div class="h-64">
            <canvas id="tipoTratamentoChart"></canvas>
        </div>
    </div>

    <!-- Distribuição por Status -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <h2 class="text-base font-semibold text-label-DEFAULT mb-4">Distribuição por Status</h2>
        <div class="h-64">
            <canvas id="statusChart"></canvas>
        </div>
    </div>
</div>

<!-- Insights com IA -->
<div class="bg-white rounded-view p-6 border border-gray-200 mb-6">
    <div class="flex items-center justify-between mb-4">
        <div class="flex items-center">
            <svg class="w-5 h-5 text-systemBlue mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
            </svg>
            <h2 class="text-base font-semibold text-gray-800">Amigo Intelligence</h2>
        </div>
        <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">Atualizado agora</span>
    </div>

    <div id="insights-container" class="grid grid-cols-1 md:grid-cols-3 gap-3">
        {% for insight in insights %}
            {% with
                title=insight.title,
                description=insight.description,
                insight_type=insight.insight_type,
                content=insight.content,
                category=insight.category,
                action_text=insight.action_text,
                action_url=insight.action_url
            %}
                {% include 'components/ai_insight_widget.html' %}
            {% endwith %}
        {% endfor %}
    </div>
</div>

<script>
    // Carregar insights dinamicamente após 5 segundos (para demonstração)
    setTimeout(() => {
        // Contexto específico da página de acompanhamento de pacientes
        const pageContext = {
            page_title: "Acompanhamento de Pacientes",
            page_description: "Monitoramento e análise de tratamentos e resultados clínicos",
            key_metrics: {
                "Total de Pacientes": "{{ acompanhamentos|length }}",
                "Em Tratamento": "{{ em_tratamento }}",
                "Concluídos": "{{ concluidos }}",
                "Taxa de Sucesso": "{{ taxa_sucesso }}%"
            },
            analysis_focus: "Eficácia de tratamentos e fatores de sucesso",
            page_elements: [
                "Distribuição por Tipo de Tratamento",
                "Distribuição por Status",
                "Resultados por Tipo de Tratamento"
            ],
            treatment_data: {
                "tipos": [{% for tipo in acompanhamentos|map(attribute='tipo_tratamento')|unique %}"{{ tipo }}"{% if not loop.last %}, {% endif %}{% endfor %}],
                "status": ["Em tratamento", "Concluído", "Interrompido", "Aguardando retorno"],
                "resultados": ["Excelente", "Bom", "Regular", "Insatisfatório"]
            }
        };

        loadInsights('amigocare', 'acompanhamento_pacientes', 'insights-container', 3, ['list', 'text', 'stat'], pageContext);
    }, 5000);
</script>

<!-- Análise de Resultados -->
<div class="bg-white rounded-view p-6 border border-gray-200 mb-6">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-base font-semibold text-label-DEFAULT">Análise de Resultados</h2>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Resultados por Tipo de Tratamento -->
        <div class="h-64">
            <canvas id="resultadosTratamentoChart"></canvas>
        </div>

        <!-- Insights Estáticos (Temporários) -->
        <div class="bg-systemGray-ultralight p-4 rounded-view hidden">
            <div class="flex items-center mb-3">
                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-sm font-semibold">Insights de Tratamentos</h3>
                    <p class="text-xs text-label-secondary">Análise de eficácia</p>
                </div>
            </div>
            <ul class="space-y-2 text-sm">
                <li class="flex items-start">
                    <svg class="w-4 h-4 text-systemBlue mr-1 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <span>Fisioterapia apresenta a maior taxa de conclusão (78%)</span>
                </li>
                <li class="flex items-start">
                    <svg class="w-4 h-4 text-systemBlue mr-1 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <span>Tratamentos com duração de 8-12 semanas têm melhores resultados</span>
                </li>
                <li class="flex items-start">
                    <svg class="w-4 h-4 text-systemBlue mr-1 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <span>Pacientes com acompanhamento semanal têm 65% mais chances de conclusão</span>
                </li>
                <li class="flex items-start">
                    <svg class="w-4 h-4 text-systemBlue mr-1 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <span>Terapia Ocupacional mostra resultados mais rápidos (média de 6 semanas)</span>
                </li>
            </ul>
        </div>
    </div>
</div>

<!-- Tabela de Acompanhamentos -->
<div class="bg-white rounded-view p-6 border border-gray-200">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-base font-semibold text-label-DEFAULT">Detalhamento dos Acompanhamentos</h2>
        <div class="flex space-x-2">
            <button class="bg-systemGray-ultralight hover:bg-systemGray-extralight text-gray-800 px-3 py-1 rounded-full transition duration-150 ease-in-out text-xs border border-systemGray-lightest">
                Exportar
            </button>
            <button class="bg-systemGray-ultralight hover:bg-systemGray-extralight text-gray-800 px-3 py-1 rounded-full transition duration-150 ease-in-out text-xs border border-systemGray-lightest">
                Imprimir
            </button>
        </div>
    </div>

    <div class="overflow-x-auto">
        <table class="w-full text-sm data-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Paciente</th>
                    <th>Profissional</th>
                    <th>Tipo Tratamento</th>
                    <th>Data Início</th>
                    <th>Data Término</th>
                    <th>Status</th>
                    <th>Resultado</th>
                    <th>Ações</th>
                </tr>
            </thead>
            <tbody>
                {% for acompanhamento in acompanhamentos %}
                <tr>
                    <td>{{ acompanhamento.id }}</td>
                    <td>{{ acompanhamento.paciente }}</td>
                    <td>{{ acompanhamento.profissional }}</td>
                    <td>{{ acompanhamento.tipo_tratamento }}</td>
                    <td>{{ acompanhamento.data_inicio }}</td>
                    <td>{{ acompanhamento.data_termino }}</td>
                    <td>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                        {% if acompanhamento.status == 'Concluído' %}
                            bg-green-100 text-green-800
                        {% elif acompanhamento.status == 'Em tratamento' %}
                            bg-blue-100 text-blue-800
                        {% elif acompanhamento.status == 'Aguardando retorno' %}
                            bg-purple-100 text-purple-800
                        {% else %}
                            bg-red-100 text-red-800
                        {% endif %}
                        ">
                            {{ acompanhamento.status }}
                        </span>
                    </td>
                    <td>
                        {% if acompanhamento.resultado %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            {% if acompanhamento.resultado == 'Excelente' %}
                                bg-green-100 text-green-800
                            {% elif acompanhamento.resultado == 'Bom' %}
                                bg-blue-100 text-blue-800
                            {% elif acompanhamento.resultado == 'Regular' %}
                                bg-yellow-100 text-yellow-800
                            {% else %}
                                bg-red-100 text-red-800
                            {% endif %}
                            ">
                                {{ acompanhamento.resultado }}
                            </span>
                        {% else %}
                            -
                        {% endif %}
                    </td>
                    <td>
                        <div class="flex space-x-1">
                            <button class="p-1 text-systemBlue hover:bg-systemGray-ultralight rounded" title="Ver detalhes">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                            </button>
                            <button class="p-1 text-systemBlue hover:bg-systemGray-ultralight rounded" title="Editar">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                            </button>
                            <button class="p-1 text-systemBlue hover:bg-systemGray-ultralight rounded" title="Adicionar evolução">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </button>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Paginação -->
    <div class="flex justify-between items-center mt-4">
        <div class="text-sm text-label-secondary">
            Mostrando 1-20 de {{ acompanhamentos|length }} resultados
        </div>
        <div class="flex space-x-1">
            <button class="px-3 py-1 rounded-md bg-systemGray-ultralight text-label-secondary text-sm">Anterior</button>
            <button class="px-3 py-1 rounded-md bg-systemBlue text-white text-sm">1</button>
            <button class="px-3 py-1 rounded-md bg-systemGray-ultralight text-label-secondary text-sm">2</button>
            <button class="px-3 py-1 rounded-md bg-systemGray-ultralight text-label-secondary text-sm">3</button>
            <button class="px-3 py-1 rounded-md bg-systemGray-ultralight text-label-secondary text-sm">Próximo</button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Dados para o gráfico de distribuição por tipo de tratamento
        const tiposTratamento = [];
        const tiposCounts = [];

        {% for tipo in acompanhamentos|map(attribute='tipo_tratamento')|unique %}
        tiposTratamento.push("{{ tipo }}");
        tiposCounts.push({{ acompanhamentos|selectattr('tipo_tratamento', 'equalto', tipo)|list|length }});
        {% endfor %}

        // Gráfico de distribuição por tipo de tratamento
        const tipoTratamentoChart = new Chart(
            document.getElementById('tipoTratamentoChart'),
            {
                type: 'pie',
                data: {
                    labels: tiposTratamento,
                    datasets: [{
                        data: tiposCounts,
                        backgroundColor: [
                            'rgba(0, 122, 255, 0.7)',
                            'rgba(142, 142, 147, 0.7)',
                            'rgba(174, 174, 178, 0.7)',
                            'rgba(199, 199, 204, 0.7)',
                            'rgba(209, 209, 214, 0.7)'
                        ],
                        borderColor: [
                            'rgba(0, 122, 255, 1)',
                            'rgba(142, 142, 147, 1)',
                            'rgba(174, 174, 178, 1)',
                            'rgba(199, 199, 204, 1)',
                            'rgba(209, 209, 214, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        }
                    }
                }
            }
        );

        // Dados para o gráfico de distribuição por status
        const statusLabels = ['Em tratamento', 'Concluído', 'Interrompido', 'Aguardando retorno'];
        const statusCounts = [
            {{ acompanhamentos|selectattr('status', 'equalto', 'Em tratamento')|list|length }},
            {{ acompanhamentos|selectattr('status', 'equalto', 'Concluído')|list|length }},
            {{ acompanhamentos|selectattr('status', 'equalto', 'Interrompido')|list|length }},
            {{ acompanhamentos|selectattr('status', 'equalto', 'Aguardando retorno')|list|length }}
        ];

        // Gráfico de distribuição por status
        const statusChart = new Chart(
            document.getElementById('statusChart'),
            {
                type: 'doughnut',
                data: {
                    labels: statusLabels,
                    datasets: [{
                        data: statusCounts,
                        backgroundColor: [
                            'rgba(0, 122, 255, 0.7)',
                            'rgba(142, 142, 147, 0.7)',
                            'rgba(174, 174, 178, 0.7)',
                            'rgba(199, 199, 204, 0.7)'
                        ],
                        borderColor: [
                            'rgba(0, 122, 255, 1)',
                            'rgba(142, 142, 147, 1)',
                            'rgba(174, 174, 178, 1)',
                            'rgba(199, 199, 204, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        }
                    }
                }
            }
        );

        // Dados para o gráfico de resultados por tipo de tratamento
        const resultadosLabels = tiposTratamento;
        const resultadosExcelente = [];
        const resultadosBom = [];
        const resultadosRegular = [];
        const resultadosInsatisfatorio = [];

        {% for tipo in acompanhamentos|map(attribute='tipo_tratamento')|unique %}
        {% set tipo_concluidos = acompanhamentos|selectattr('tipo_tratamento', 'equalto', tipo)|selectattr('status', 'equalto', 'Concluído')|list %}
        {% set tipo_total = tipo_concluidos|length %}
        {% if tipo_total > 0 %}
        resultadosExcelente.push({{ (tipo_concluidos|selectattr('resultado', 'equalto', 'Excelente')|list|length / tipo_total * 100)|round|int }});
        resultadosBom.push({{ (tipo_concluidos|selectattr('resultado', 'equalto', 'Bom')|list|length / tipo_total * 100)|round|int }});
        resultadosRegular.push({{ (tipo_concluidos|selectattr('resultado', 'equalto', 'Regular')|list|length / tipo_total * 100)|round|int }});
        resultadosInsatisfatorio.push({{ (tipo_concluidos|selectattr('resultado', 'equalto', 'Insatisfatório')|list|length / tipo_total * 100)|round|int }});
        {% else %}
        resultadosExcelente.push(0);
        resultadosBom.push(0);
        resultadosRegular.push(0);
        resultadosInsatisfatorio.push(0);
        {% endif %}
        {% endfor %}

        // Gráfico de resultados por tipo de tratamento
        const resultadosTratamentoChart = new Chart(
            document.getElementById('resultadosTratamentoChart'),
            {
                type: 'bar',
                data: {
                    labels: resultadosLabels,
                    datasets: [
                        {
                            label: 'Excelente',
                            data: resultadosExcelente,
                            backgroundColor: 'rgba(0, 122, 255, 0.7)',
                            borderColor: 'rgba(0, 122, 255, 1)',
                            borderWidth: 1
                        },
                        {
                            label: 'Bom',
                            data: resultadosBom,
                            backgroundColor: 'rgba(142, 142, 147, 0.7)',
                            borderColor: 'rgba(142, 142, 147, 1)',
                            borderWidth: 1
                        },
                        {
                            label: 'Regular',
                            data: resultadosRegular,
                            backgroundColor: 'rgba(174, 174, 178, 0.7)',
                            borderColor: 'rgba(174, 174, 178, 1)',
                            borderWidth: 1
                        },
                        {
                            label: 'Insatisfatório',
                            data: resultadosInsatisfatorio,
                            backgroundColor: 'rgba(199, 199, 204, 0.7)',
                            borderColor: 'rgba(199, 199, 204, 1)',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            stacked: true,
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            stacked: true,
                            max: 100,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            },
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    }
                }
            }
        );
    });
</script>
{% endblock %}

{% extends 'base.html' %}

{% block title %}Avaliação NPS - Amigo DataHub{% endblock %}

{% block header %}Avaliação NPS{% endblock %}

{% block content %}
<!-- Resumo e Insights -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <!-- NPS Geral -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-label-DEFAULT">NPS Geral</p>
        </div>
        {% set promotores = avaliacoes|selectattr('score', 'ge', 9)|list|length %}
        {% set detratores = avaliacoes|selectattr('score', 'le', 6)|list|length %}
        {% set total = avaliacoes|length %}
        {% set nps = ((promotores / total * 100) - (detratores / total * 100))|round|int %}

        <p class="text-3xl font-semibold text-label-DEFAULT mb-1">
            {{ nps }}
        </p>
        <div class="flex items-center text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">
            <span>Net Promoter Score</span>
            <span class="ml-auto {% if nps > 50 %}text-systemGreen{% elif nps > 0 %}text-systemBlue{% else %}text-red-600{% endif %} font-medium">
                {% if nps > 50 %}Excelente{% elif nps > 30 %}Bom{% elif nps > 0 %}Regular{% else %}Crítico{% endif %}
            </span>
        </div>
    </div>

    <!-- Promotores -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-label-DEFAULT">Promotores</p>
        </div>
        <p class="text-3xl font-semibold text-label-DEFAULT mb-1">
            {{ '{:,}'.format(promotores).replace(',', '.') if promotores < 1000 else '{:.1f}K'.format(promotores/1000) }}
        </p>
        <div class="flex items-center text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">
            <span>Notas 9-10</span>
            <span class="ml-auto text-systemGreen font-medium">{{ (promotores / total * 100)|round|int }}%</span>
        </div>
    </div>

    <!-- Neutros -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-label-DEFAULT">Neutros</p>
        </div>
        {% set neutros = avaliacoes|selectattr('score', 'ge', 7)|selectattr('score', 'le', 8)|list|length %}
        <p class="text-3xl font-semibold text-label-DEFAULT mb-1">
            {{ '{:,}'.format(neutros).replace(',', '.') if neutros < 1000 else '{:.1f}K'.format(neutros/1000) }}
        </p>
        <div class="flex items-center text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">
            <span>Notas 7-8</span>
            <span class="ml-auto text-systemBlue font-medium">{{ (neutros / total * 100)|round|int }}%</span>
        </div>
    </div>

    <!-- Detratores -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-label-DEFAULT">Detratores</p>
        </div>
        <p class="text-3xl font-semibold text-label-DEFAULT mb-1">
            {{ '{:,}'.format(detratores).replace(',', '.') if detratores < 1000 else '{:.1f}K'.format(detratores/1000) }}
        </p>
        <div class="flex items-center text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">
            <span>Notas 0-6</span>
            <span class="ml-auto text-red-600 font-medium">{{ (detratores / total * 100)|round|int }}%</span>
        </div>
    </div>
</div>

<!-- Filtros -->
<div class="bg-white rounded-view p-4 mb-6 border border-gray-200">
    <div class="flex flex-wrap gap-4">
        <div class="w-full md:w-auto">
            <label for="data_inicio" class="block text-xs font-medium text-label-secondary mb-1">Data Início</label>
            <input type="date" id="data_inicio" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
        </div>
        <div class="w-full md:w-auto">
            <label for="data_fim" class="block text-xs font-medium text-label-secondary mb-1">Data Fim</label>
            <input type="date" id="data_fim" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
        </div>
        <div class="w-full md:w-auto">
            <label for="unidade" class="block text-xs font-medium text-label-secondary mb-1">Unidade</label>
            <select id="unidade" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
                <option value="">Todas</option>
                {% for unidade in avaliacoes|map(attribute='unidade')|unique %}
                <option>{{ unidade }}</option>
                {% endfor %}
            </select>
        </div>
        <div class="w-full md:w-auto">
            <label for="categoria" class="block text-xs font-medium text-label-secondary mb-1">Categoria</label>
            <select id="categoria" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
                <option value="">Todas</option>
                {% for categoria in avaliacoes|map(attribute='categoria')|unique %}
                <option>{{ categoria }}</option>
                {% endfor %}
            </select>
        </div>
        <div class="w-full md:w-auto flex items-end">
            <button class="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-control transition duration-150 ease-in-out text-sm">
                Filtrar
            </button>
        </div>
    </div>
</div>

<!-- Gráficos -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- NPS por Categoria -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <h2 class="text-base font-semibold text-label-DEFAULT mb-4">NPS por Categoria</h2>
        <div class="h-64">
            <canvas id="npsCategoriasChart"></canvas>
        </div>
    </div>

    <!-- Evolução do NPS -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <h2 class="text-base font-semibold text-label-DEFAULT mb-4">Evolução do NPS</h2>
        <div class="h-64">
            <canvas id="evolucaoNpsChart"></canvas>
        </div>
    </div>
</div>

<!-- Insights com IA -->
<div class="bg-white rounded-view p-6 border border-gray-200 mb-6">
    <div class="flex items-center justify-between mb-4">
        <div class="flex items-center">
            <svg class="w-5 h-5 text-systemBlue mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
            </svg>
            <h2 class="text-base font-semibold text-gray-800">Amigo Intelligence</h2>
        </div>
        <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">Atualizado agora</span>
    </div>

    <div id="insights-container" class="grid grid-cols-1 md:grid-cols-3 gap-3">
        {% for insight in insights %}
            {% with
                title=insight.title,
                description=insight.description,
                insight_type=insight.insight_type,
                content=insight.content,
                category=insight.category,
                action_text=insight.action_text,
                action_url=insight.action_url
            %}
                {% include 'components/ai_insight_widget.html' %}
            {% endwith %}
        {% endfor %}
    </div>
</div>

<script>
    // Carregar insights dinamicamente após 5 segundos (para demonstração)
    setTimeout(() => {
        // Contexto específico da página de avaliação NPS
        const pageContext = {
            page_title: "Avaliação NPS",
            page_description: "Análise de satisfação dos pacientes através do Net Promoter Score",
            key_metrics: {
                "NPS Geral": "{{ nps }}",
                "Promotores": "{{ (promotores / total * 100)|round|int }}%",
                "Neutros": "{{ (neutros / total * 100)|round|int }}%",
                "Detratores": "{{ (detratores / total * 100)|round|int }}%"
            },
            analysis_focus: "Análise de comentários e identificação de oportunidades de melhoria",
            page_elements: [
                "NPS por Categoria",
                "Evolução do NPS",
                "Detalhamento das Avaliações"
            ],
            nps_data: {
                "categorias": [{% for categoria in avaliacoes|map(attribute='categoria')|unique %}"{{ categoria }}"{% if not loop.last %}, {% endif %}{% endfor %}],
                "comentarios_positivos": [
                    "Atendimento atencioso",
                    "Profissionais qualificados",
                    "Instalações limpas"
                ],
                "comentarios_negativos": [
                    "Tempo de espera",
                    "Dificuldade de agendamento",
                    "Estacionamento limitado"
                ]
            }
        };

        loadInsights('amigocare', 'avaliacao_nps', 'insights-container', 3, ['list', 'text', 'stat'], pageContext);
    }, 5000);
</script>

<!-- Análise de Comentários (Temporário) -->
<div class="bg-white rounded-view p-6 border border-gray-200 mb-6 hidden">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-base font-semibold text-label-DEFAULT">Análise de Comentários</h2>
        <div class="flex items-center">
            <svg class="w-4 h-4 text-systemBlue mr-1" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 1L9 9H2L7 14.5L5 22L12 17.5L19 22L17 14.5L22 9H15L12 1Z"></path>
            </svg>
            <span class="text-xs text-systemBlue font-medium">AI Analytics</span>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Temas Positivos -->
        <div class="bg-systemGray-ultralight p-4 rounded-view">
            <div class="flex items-center mb-3">
                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-sm font-semibold">Temas Positivos</h3>
                    <p class="text-xs text-label-secondary">Mencionados com frequência</p>
                </div>
            </div>
            <ul class="space-y-2 text-sm">
                <li class="flex items-start">
                    <svg class="w-4 h-4 text-systemBlue mr-1 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <span>Atendimento atencioso (78%)</span>
                </li>
                <li class="flex items-start">
                    <svg class="w-4 h-4 text-systemBlue mr-1 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <span>Profissionais qualificados (65%)</span>
                </li>
                <li class="flex items-start">
                    <svg class="w-4 h-4 text-systemBlue mr-1 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <span>Instalações limpas (52%)</span>
                </li>
            </ul>
        </div>

        <!-- Temas Negativos -->
        <div class="bg-systemGray-ultralight p-4 rounded-view">
            <div class="flex items-center mb-3">
                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14H5.236a2 2 0 01-1.789-2.894l3.5-7A2 2 0 018.736 3h4.018a2 2 0 01.485.06l3.76.94m-7 10v5a2 2 0 002 2h.096c.5 0 .905-.405.905-.904 0-.715.211-1.413.608-2.008L17 13V4m-7 10h2m5-10h2a2 2 0 012 2v6a2 2 0 01-2 2h-2.5"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-sm font-semibold">Temas Negativos</h3>
                    <p class="text-xs text-label-secondary">Áreas para melhoria</p>
                </div>
            </div>
            <ul class="space-y-2 text-sm">
                <li class="flex items-start">
                    <svg class="w-4 h-4 text-systemBlue mr-1 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <span>Tempo de espera (85%)</span>
                </li>
                <li class="flex items-start">
                    <svg class="w-4 h-4 text-systemBlue mr-1 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <span>Dificuldade de agendamento (42%)</span>
                </li>
                <li class="flex items-start">
                    <svg class="w-4 h-4 text-systemBlue mr-1 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <span>Estacionamento limitado (38%)</span>
                </li>
            </ul>
        </div>

        <!-- Recomendações -->
        <div class="bg-systemGray-ultralight p-4 rounded-view">
            <div class="flex items-center mb-3">
                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-sm font-semibold">Recomendações</h3>
                    <p class="text-xs text-label-secondary">Ações para melhorar o NPS</p>
                </div>
            </div>
            <ul class="space-y-2 text-sm">
                <li class="flex items-start">
                    <svg class="w-4 h-4 text-systemBlue mr-1 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <span>Otimizar processo de agendamento</span>
                </li>
                <li class="flex items-start">
                    <svg class="w-4 h-4 text-systemBlue mr-1 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <span>Reduzir tempo de espera em 15%</span>
                </li>
                <li class="flex items-start">
                    <svg class="w-4 h-4 text-systemBlue mr-1 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <span>Implementar sistema de feedback em tempo real</span>
                </li>
            </ul>
        </div>
    </div>
</div>

<!-- Tabela de Avaliações -->
<div class="bg-white rounded-view p-6 border border-gray-200">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-base font-semibold text-label-DEFAULT">Detalhamento das Avaliações</h2>
        <div class="flex space-x-2">
            <button class="bg-systemGray-ultralight hover:bg-systemGray-extralight text-gray-800 px-3 py-1 rounded-full transition duration-150 ease-in-out text-xs border border-systemGray-lightest">
                Exportar
            </button>
            <button class="bg-systemGray-ultralight hover:bg-systemGray-extralight text-gray-800 px-3 py-1 rounded-full transition duration-150 ease-in-out text-xs border border-systemGray-lightest">
                Imprimir
            </button>
        </div>
    </div>

    <div class="overflow-x-auto">
        <table class="w-full text-sm data-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Data</th>
                    <th>Paciente</th>
                    <th>Unidade</th>
                    <th>Categoria</th>
                    <th>Score</th>
                    <th>Comentário</th>
                </tr>
            </thead>
            <tbody>
                {% for avaliacao in avaliacoes %}
                <tr>
                    <td>{{ avaliacao.id }}</td>
                    <td>{{ avaliacao.data }}</td>
                    <td>{{ avaliacao.paciente }}</td>
                    <td>{{ avaliacao.unidade }}</td>
                    <td>{{ avaliacao.categoria }}</td>
                    <td>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                        {% if avaliacao.score >= 9 %}
                            bg-green-100 text-green-800
                        {% elif avaliacao.score >= 7 %}
                            bg-blue-100 text-blue-800
                        {% else %}
                            bg-red-100 text-red-800
                        {% endif %}
                        ">
                            {{ avaliacao.score }}
                        </span>
                    </td>
                    <td class="max-w-xs truncate">{{ avaliacao.comentario }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Paginação -->
    <div class="flex justify-between items-center mt-4">
        <div class="text-sm text-label-secondary">
            Mostrando 1-20 de {{ avaliacoes|length }} resultados
        </div>
        <div class="flex space-x-1">
            <button class="px-3 py-1 rounded-md bg-systemGray-ultralight text-label-secondary text-sm">Anterior</button>
            <button class="px-3 py-1 rounded-md bg-systemBlue text-white text-sm">1</button>
            <button class="px-3 py-1 rounded-md bg-systemGray-ultralight text-label-secondary text-sm">2</button>
            <button class="px-3 py-1 rounded-md bg-systemGray-ultralight text-label-secondary text-sm">3</button>
            <button class="px-3 py-1 rounded-md bg-systemGray-ultralight text-label-secondary text-sm">Próximo</button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Dados para o gráfico de NPS por categoria
        const categorias = [];
        const npsValues = [];

        {% for categoria in avaliacoes|map(attribute='categoria')|unique %}
        categorias.push("{{ categoria }}");

        {% set cat_avaliacoes = avaliacoes|selectattr('categoria', 'equalto', categoria)|list %}
        {% set cat_promotores = cat_avaliacoes|selectattr('score', 'ge', 9)|list|length %}
        {% set cat_detratores = cat_avaliacoes|selectattr('score', 'le', 6)|list|length %}
        {% set cat_total = cat_avaliacoes|length %}
        {% set cat_nps = ((cat_promotores / cat_total * 100) - (cat_detratores / cat_total * 100))|round|int %}

        npsValues.push({{ cat_nps }});
        {% endfor %}

        // Gráfico de NPS por categoria
        const npsCategoriasChart = new Chart(
            document.getElementById('npsCategoriasChart'),
            {
                type: 'bar',
                data: {
                    labels: categorias,
                    datasets: [{
                        label: 'NPS',
                        data: npsValues,
                        backgroundColor: 'rgba(0, 122, 255, 0.7)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            }
        );

        // Dados para o gráfico de evolução do NPS
        const meses = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'];
        const evolucaoNPS = [45, 48, 52, 55, 58, 62, 65, 68, 70, 72, 75, {{ nps }}];

        // Gráfico de evolução do NPS
        const evolucaoNpsChart = new Chart(
            document.getElementById('evolucaoNpsChart'),
            {
                type: 'line',
                data: {
                    labels: meses,
                    datasets: [{
                        label: 'NPS',
                        data: evolucaoNPS,
                        backgroundColor: 'rgba(0, 122, 255, 0.1)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            }
        );
    });
</script>
{% endblock %}

{% extends 'base.html' %}

{% block title %}Campanhas - Amigo DataHub{% endblock %}

{% block header %}Campanhas{% endblock %}

{% block content %}
<!-- Resumo e Insights -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <!-- Total de Campanhas -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-label-DEFAULT">Total de Campanhas</p>
        </div>
        <p class="text-3xl font-semibold text-label-DEFAULT mb-1">
            {{ '{:,}'.format(campanhas|length).replace(',', '.') if campanhas|length < 1000 else '{:.1f}K'.format(campanhas|length/1000) }}
        </p>
        <div class="flex items-center text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">
            <span>Campanhas registradas</span>
        </div>
    </div>

    <!-- <PERSON>anhas Ativas -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-label-DEFAULT">Campanhas Ativas</p>
        </div>
        {% set ativas = campanhas|selectattr('status', 'equalto', 'Em andamento')|list|length %}
        <p class="text-3xl font-semibold text-label-DEFAULT mb-1">
            {{ ativas }}
        </p>
        <div class="flex items-center text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">
            <span>Em andamento</span>
            <span class="ml-auto text-systemBlue font-medium">{{ (ativas / campanhas|length * 100)|round|int }}%</span>
        </div>
    </div>

    <!-- ROI Médio -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-label-DEFAULT">ROI Médio</p>
        </div>
        {% set campanhas_concluidas = campanhas|selectattr('status', 'equalto', 'Concluída')|list %}
        {% set roi_medio = (campanhas_concluidas|sum(attribute='roi') / campanhas_concluidas|length)|round|int if campanhas_concluidas|length > 0 else 0 %}
        <p class="text-3xl font-semibold text-label-DEFAULT mb-1">
            {{ roi_medio }}%
        </p>
        <div class="flex items-center text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">
            <span>Retorno sobre investimento</span>
            <span class="ml-auto {% if roi_medio > 200 %}text-systemGreen{% elif roi_medio > 100 %}text-systemBlue{% else %}text-red-600{% endif %} font-medium">
                {% if roi_medio > 200 %}Excelente{% elif roi_medio > 100 %}Bom{% else %}Baixo{% endif %}
            </span>
        </div>
    </div>

    <!-- Leads Gerados -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-label-DEFAULT">Leads Gerados</p>
        </div>
        {% set total_leads = campanhas|sum(attribute='leads_gerados') %}
        <p class="text-3xl font-semibold text-label-DEFAULT mb-1">
            {{ '{:,}'.format(total_leads).replace(',', '.') if total_leads < 1000 else '{:.1f}K'.format(total_leads/1000) }}
        </p>
        <div class="flex items-center text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">
            <span>Total de leads captados</span>
        </div>
    </div>
</div>

<!-- Filtros -->
<div class="bg-white rounded-view p-4 mb-6 border border-gray-200">
    <div class="flex flex-wrap gap-4">
        <div class="w-full md:w-auto">
            <label for="data_inicio" class="block text-xs font-medium text-label-secondary mb-1">Data Início</label>
            <input type="date" id="data_inicio" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
        </div>
        <div class="w-full md:w-auto">
            <label for="data_fim" class="block text-xs font-medium text-label-secondary mb-1">Data Fim</label>
            <input type="date" id="data_fim" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
        </div>
        <div class="w-full md:w-auto">
            <label for="tipo" class="block text-xs font-medium text-label-secondary mb-1">Tipo</label>
            <select id="tipo" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
                <option value="">Todos</option>
                {% for tipo in campanhas|map(attribute='tipo')|unique %}
                <option>{{ tipo }}</option>
                {% endfor %}
            </select>
        </div>
        <div class="w-full md:w-auto">
            <label for="status" class="block text-xs font-medium text-label-secondary mb-1">Status</label>
            <select id="status" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
                <option value="">Todos</option>
                {% for status in campanhas|map(attribute='status')|unique %}
                <option>{{ status }}</option>
                {% endfor %}
            </select>
        </div>
        <div class="w-full md:w-auto flex items-end">
            <button class="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-control transition duration-150 ease-in-out text-sm">
                Filtrar
            </button>
        </div>
    </div>
</div>

<!-- Gráficos -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Campanhas por Tipo -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <h2 class="text-base font-semibold text-label-DEFAULT mb-4">Campanhas por Tipo</h2>
        <div class="h-64">
            <canvas id="campanhasTipoChart"></canvas>
        </div>
    </div>

    <!-- Desempenho de Campanhas -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <h2 class="text-base font-semibold text-label-DEFAULT mb-4">Desempenho de Campanhas</h2>
        <div class="h-64">
            <canvas id="desempenhoChart"></canvas>
        </div>
    </div>
</div>

<!-- Insights com IA -->
<div class="mb-5">
    <div class="flex items-center justify-between mb-4">
        <div class="flex items-center">
            <svg class="w-5 h-5 text-systemBlue mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
            </svg>
            <h2 class="text-base font-semibold text-gray-800">Amigo Intelligence</h2>
        </div>
        <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">Atualizado agora</span>
    </div>

    <div id="insights-container" class="grid grid-cols-1 md:grid-cols-3 gap-3">
        {% for insight in insights %}
            {% with
                title=insight.title,
                description=insight.description,
                insight_type=insight.insight_type,
                content=insight.content,
                category=insight.category
            %}
                {% include 'components/ai_insight_widget.html' %}
            {% endwith %}
        {% endfor %}
    </div>
</div>

<script>
    // Carregar insights dinamicamente após 5 segundos (para demonstração)
    setTimeout(() => {
        // Contexto específico da página de campanhas
        const pageContext = {
            page_title: "AmigoCare+ - Campanhas de Marketing",
            page_description: "Análise e gestão de campanhas de marketing da clínica, com foco em ROI e conversão",
            key_metrics: {
                "Campanhas Ativas": document.querySelector('.kpi-campanhas-ativas .value')?.textContent || "8",
                "ROI Médio": document.querySelector('.kpi-roi-medio .value')?.textContent || "320%",
                "Conversão Média": document.querySelector('.kpi-conversao-media .value')?.textContent || "28%"
            },
            analysis_focus: "Otimização de campanhas e aumento de conversão",
            page_elements: [
                "Campanhas de Maior ROI",
                "Tipos Mais Eficientes",
                "Recomendações",
                "Campanhas por Status"
            ]
        };

        loadInsights('amigocare', 'campanhas', 'insights-container', 3, ['list', 'text', 'stat'], pageContext);
    }, 5000);
</script>

<!-- Análise de Campanhas -->
<div class="bg-white rounded-view p-6 border border-gray-200 mb-6">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-base font-semibold text-label-DEFAULT">Análise de Campanhas</h2>
        <div class="flex items-center">
            <svg class="w-4 h-4 text-systemBlue mr-1" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 1L9 9H2L7 14.5L5 22L12 17.5L19 22L17 14.5L22 9H15L12 1Z"></path>
            </svg>
            <span class="text-xs text-systemBlue font-medium">Powered by Amigo Intelligence</span>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Campanhas de Maior ROI -->
        <div class="bg-systemGray-ultralight p-4 rounded-view">
            <div class="flex items-center mb-3">
                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-sm font-semibold">Campanhas de Maior ROI</h3>
                    <p class="text-xs text-label-secondary">Melhor retorno sobre investimento</p>
                </div>
            </div>
            <ul class="space-y-2 text-sm">
                {% set top_campanhas = campanhas|selectattr('status', 'equalto', 'Concluída')|sort(attribute='roi', reverse=true)|list %}
                {% for campanha in top_campanhas[:3] %}
                <li class="flex items-start">
                    <svg class="w-4 h-4 text-systemBlue mr-1 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <span>{{ campanha.nome }} ({{ campanha.roi }}%)</span>
                </li>
                {% endfor %}
            </ul>
        </div>

        <!-- Tipos Mais Eficientes -->
        <div class="bg-systemGray-ultralight p-4 rounded-view">
            <div class="flex items-center mb-3">
                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-sm font-semibold">Tipos Mais Eficientes</h3>
                    <p class="text-xs text-label-secondary">Por taxa de conversão</p>
                </div>
            </div>
            <ul class="space-y-2 text-sm">
                <li class="flex items-start">
                    <svg class="w-4 h-4 text-systemBlue mr-1 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <span>WhatsApp (32% conversão)</span>
                </li>
                <li class="flex items-start">
                    <svg class="w-4 h-4 text-systemBlue mr-1 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <span>Eventos (28% conversão)</span>
                </li>
                <li class="flex items-start">
                    <svg class="w-4 h-4 text-systemBlue mr-1 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <span>E-mail Marketing (18% conversão)</span>
                </li>
            </ul>
        </div>

        <!-- Recomendações -->
        <div class="bg-systemGray-ultralight p-4 rounded-view">
            <div class="flex items-center mb-3">
                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-sm font-semibold">Recomendações</h3>
                    <p class="text-xs text-label-secondary">Ações para melhorar resultados</p>
                </div>
            </div>
            <ul class="space-y-2 text-sm">
                <li class="flex items-start">
                    <svg class="w-4 h-4 text-systemBlue mr-1 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <span>Aumentar investimento em WhatsApp</span>
                </li>
                <li class="flex items-start">
                    <svg class="w-4 h-4 text-systemBlue mr-1 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <span>Reduzir orçamento em Google Ads</span>
                </li>
                <li class="flex items-start">
                    <svg class="w-4 h-4 text-systemBlue mr-1 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <span>Planejar mais eventos presenciais</span>
                </li>
            </ul>
        </div>
    </div>
</div>

<!-- Tabela de Campanhas -->
<div class="bg-white rounded-view p-6 border border-gray-200">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-base font-semibold text-label-DEFAULT">Detalhamento das Campanhas</h2>
        <div class="flex space-x-2">
            <button class="bg-systemGray-ultralight hover:bg-systemGray-extralight text-gray-800 px-3 py-1 rounded-full transition duration-150 ease-in-out text-xs border border-systemGray-lightest">
                Exportar
            </button>
            <button class="bg-systemBlue hover:bg-blue-600 text-white px-3 py-1 rounded-full transition duration-150 ease-in-out text-xs">
                Nova Campanha
            </button>
        </div>
    </div>

    <div class="overflow-x-auto">
        <table class="w-full text-sm data-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Nome</th>
                    <th>Tipo</th>
                    <th>Status</th>
                    <th>Data Início</th>
                    <th>Data Fim</th>
                    <th>Público Alvo</th>
                    <th>Alcance</th>
                    <th>Leads</th>
                    <th>Conversões</th>
                    <th>Custo</th>
                    <th>Receita</th>
                    <th>ROI</th>
                    <th>Ações</th>
                </tr>
            </thead>
            <tbody>
                {% for campanha in campanhas %}
                <tr>
                    <td>{{ campanha.id }}</td>
                    <td>{{ campanha.nome }}</td>
                    <td>{{ campanha.tipo }}</td>
                    <td>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                        {% if campanha.status == 'Concluída' %}
                            bg-green-100 text-green-800
                        {% elif campanha.status == 'Cancelada' %}
                            bg-red-100 text-red-800
                        {% elif campanha.status == 'Em andamento' %}
                            bg-blue-100 text-blue-800
                        {% else %}
                            bg-gray-100 text-gray-800
                        {% endif %}
                        ">
                            {{ campanha.status }}
                        </span>
                    </td>
                    <td>{{ campanha.data_inicio }}</td>
                    <td>{{ campanha.data_fim }}</td>
                    <td>{{ '{:,}'.format(campanha.publico_alvo).replace(',', '.') }}</td>
                    <td>{{ '{:,}'.format(campanha.alcance).replace(',', '.') }}</td>
                    <td>{{ '{:,}'.format(campanha.leads_gerados).replace(',', '.') }}</td>
                    <td>{{ '{:,}'.format(campanha.conversoes).replace(',', '.') }}</td>
                    <td>R$ {{ '{:,.2f}'.format(campanha.custo).replace(',', '.') }}</td>
                    <td>R$ {{ '{:,.2f}'.format(campanha.receita).replace(',', '.') }}</td>
                    <td>
                        <span class="{% if campanha.roi > 200 %}text-systemGreen{% elif campanha.roi > 100 %}text-systemBlue{% elif campanha.roi > 0 %}text-gray-600{% else %}text-red-600{% endif %} font-medium">
                            {{ campanha.roi }}%
                        </span>
                    </td>
                    <td>
                        <div class="flex space-x-1">
                            <button class="p-1 text-systemBlue hover:bg-systemGray-ultralight rounded" title="Ver detalhes">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                            </button>
                            <button class="p-1 text-systemBlue hover:bg-systemGray-ultralight rounded" title="Editar">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                            </button>
                            <button class="p-1 text-systemBlue hover:bg-systemGray-ultralight rounded" title="Duplicar">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                            </button>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Paginação -->
    <div class="flex justify-between items-center mt-4">
        <div class="text-sm text-label-secondary">
            Mostrando 1-20 de {{ campanhas|length }} resultados
        </div>
        <div class="flex space-x-1">
            <button class="px-3 py-1 rounded-md bg-systemGray-ultralight text-label-secondary text-sm">Anterior</button>
            <button class="px-3 py-1 rounded-md bg-systemBlue text-white text-sm">1</button>
            <button class="px-3 py-1 rounded-md bg-systemGray-ultralight text-label-secondary text-sm">2</button>
            <button class="px-3 py-1 rounded-md bg-systemGray-ultralight text-label-secondary text-sm">Próximo</button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Dados para o gráfico de campanhas por tipo
        const tipos = [];
        const tiposCount = [];

        {% for tipo in campanhas|map(attribute='tipo')|unique %}
        tipos.push("{{ tipo }}");
        tiposCount.push({{ campanhas|selectattr('tipo', 'equalto', tipo)|list|length }});
        {% endfor %}

        // Gráfico de campanhas por tipo
        const campanhasTipoChart = new Chart(
            document.getElementById('campanhasTipoChart'),
            {
                type: 'doughnut',
                data: {
                    labels: tipos,
                    datasets: [{
                        data: tiposCount,
                        backgroundColor: [
                            'rgba(0, 122, 255, 0.7)',
                            'rgba(142, 142, 147, 0.7)',
                            'rgba(174, 174, 178, 0.7)',
                            'rgba(199, 199, 204, 0.7)',
                            'rgba(209, 209, 214, 0.7)',
                            'rgba(229, 229, 234, 0.7)'
                        ],
                        borderColor: [
                            'rgba(0, 122, 255, 1)',
                            'rgba(142, 142, 147, 1)',
                            'rgba(174, 174, 178, 1)',
                            'rgba(199, 199, 204, 1)',
                            'rgba(209, 209, 214, 1)',
                            'rgba(229, 229, 234, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        }
                    }
                }
            }
        );

        // Dados para o gráfico de desempenho de campanhas
        const campanhasNomes = [];
        const campanhasROI = [];

        {% set top_roi_campanhas = campanhas|selectattr('status', 'equalto', 'Concluída')|sort(attribute='roi', reverse=true)|list %}
        {% for campanha in top_roi_campanhas[:5] %}
        {% set nome_curto = campanha.nome %}
        {% if campanha.nome|length > 15 %}
            {% set nome_curto = campanha.nome[0:15] + '...' %}
        {% endif %}
        campanhasNomes.push("{{ nome_curto }}");
        campanhasROI.push({{ campanha.roi }});
        {% endfor %}

        // Gráfico de desempenho de campanhas
        const desempenhoChart = new Chart(
            document.getElementById('desempenhoChart'),
            {
                type: 'bar',
                data: {
                    labels: campanhasNomes,
                    datasets: [{
                        label: 'ROI (%)',
                        data: campanhasROI,
                        backgroundColor: 'rgba(0, 122, 255, 0.7)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            }
        );
    });
</script>
{% endblock %}

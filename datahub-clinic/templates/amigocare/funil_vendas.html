{% extends 'base.html' %}

{% block title %}Funil de Vendas - Amigo DataHub{% endblock %}

{% block header %}<PERSON><PERSON>end<PERSON>{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="bg-white rounded-lg shadow-sm p-6 mb-5">
    <div class="flex flex-col md:flex-row items-center">
        <div class="md:w-2/3 mb-4 md:mb-0 md:pr-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-2">Funil de Vendas</h1>
            <p class="text-gray-600 mb-4">
                Visualize e analise o processo de conversão de leads em pacientes, identificando oportunidades de melhoria em cada etapa do funil de vendas.
            </p>
            <div class="flex flex-wrap gap-3">
                <button class="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
                    Exportar Relatório
                </button>
                <button class="bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-md text-sm font-medium border border-gray-200 transition-colors">
                    Configurar Metas
                </button>
            </div>
        </div>
        <div class="md:w-1/3 flex justify-center">
            <img src="/static/images/sales-funnel.svg" alt="Funil de Vendas" class="w-48 h-48 object-contain" onerror="this.src='https://cdn-icons-png.flaticon.com/512/2784/2784403.png'; this.onerror=null;">
        </div>
    </div>
</div>

<!-- KPIs -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-5">
    <!-- Total de Leads -->
    <div class="bg-white rounded-lg shadow-sm p-4">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                </svg>
            </div>
            <div>
                <p class="text-xs font-medium text-gray-500">Total de Leads</p>
                <p class="text-xl font-semibold text-gray-800">{{ leads|length }}</p>
            </div>
        </div>
        <div class="flex items-center text-xs text-gray-500 mt-2 pt-2 border-t border-gray-100">
            <span>Últimos 30 dias</span>
        </div>
    </div>

    <!-- Taxa de Conversão -->
    {% set leads_convertidos = leads|selectattr('status', 'equalto', 'Convertido')|list|length %}
    {% set taxa_conversao = (leads_convertidos / leads|length * 100)|round|int if leads|length > 0 else 0 %}
    <div class="bg-white rounded-lg shadow-sm p-4">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mr-3">
                <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div>
                <p class="text-xs font-medium text-gray-500">Taxa de Conversão</p>
                <p class="text-xl font-semibold text-gray-800">{{ taxa_conversao }}%</p>
            </div>
        </div>
        <div class="flex items-center text-xs text-gray-500 mt-2 pt-2 border-t border-gray-100">
            <span>Meta: 25%</span>
            <span class="ml-auto {% if taxa_conversao >= 25 %}text-green-600{% else %}text-red-600{% endif %}">
                {{ "+" if taxa_conversao >= 25 else "" }}{{ taxa_conversao - 25 }}%
            </span>
        </div>
    </div>

    <!-- Ticket Médio -->
    {% set valor_total = 0 %}
    {% for lead in leads|selectattr('status', 'equalto', 'Convertido')|list %}
        {% set valor_total = valor_total + lead.valor_potencial %}
    {% endfor %}
    {% set ticket_medio = (valor_total / leads_convertidos)|round|int if leads_convertidos > 0 else 0 %}
    <div class="bg-white rounded-lg shadow-sm p-4">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div>
                <p class="text-xs font-medium text-gray-500">Ticket Médio</p>
                <p class="text-xl font-semibold text-gray-800">R$ {{ ticket_medio }}</p>
            </div>
        </div>
        <div class="flex items-center text-xs text-gray-500 mt-2 pt-2 border-t border-gray-100">
            <span>Últimos 30 dias</span>
        </div>
    </div>

    <!-- ROI Campanhas -->
    {% set campanhas_concluidas = campanhas|selectattr('status', 'equalto', 'Concluída')|list %}
    {% set roi_medio = (campanhas_concluidas|sum(attribute='roi') / campanhas_concluidas|length)|round|int if campanhas_concluidas|length > 0 else 0 %}
    <div class="bg-white rounded-lg shadow-sm p-4">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mr-3">
                <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                </svg>
            </div>
            <div>
                <p class="text-xs font-medium text-gray-500">ROI Médio Campanhas</p>
                <p class="text-xl font-semibold text-gray-800">{{ roi_medio }}%</p>
            </div>
        </div>
        <div class="flex items-center text-xs text-gray-500 mt-2 pt-2 border-t border-gray-100">
            <span>Campanhas concluídas</span>
        </div>
    </div>
</div>

<!-- Insights com IA -->
<div class="mb-5">
    <div class="flex items-center justify-between mb-4">
        <div class="flex items-center">
            <svg class="w-5 h-5 text-systemBlue mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
            </svg>
            <h2 class="text-base font-semibold text-gray-800">Amigo Intelligence</h2>
        </div>
        <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">Atualizado agora</span>
    </div>

    <div id="insights-container" class="grid grid-cols-1 md:grid-cols-3 gap-3">
        {% for insight in insights %}
            {% with
                title=insight.title,
                description=insight.description,
                insight_type=insight.insight_type,
                content=insight.content,
                category=insight.category
            %}
                {% include 'components/ai_insight_widget.html' %}
            {% endwith %}
        {% endfor %}
    </div>
</div>

<script>
    // Carregar insights dinamicamente após 5 segundos (para demonstração)
    setTimeout(() => {
        // Contexto específico da página de funil de vendas
        const pageContext = {
            page_title: "AmigoCare+ - Funil de Vendas",
            page_description: "Análise completa do funil de vendas da clínica, com foco em conversão e otimização",
            key_metrics: {
                "Leads Ativos": document.querySelector('.kpi-leads-ativos .value')?.textContent || "145",
                "Taxa de Conversão": document.querySelector('.kpi-taxa-conversao .value')?.textContent || "32%",
                "Tempo Médio de Conversão": document.querySelector('.kpi-tempo-conversao .value')?.textContent || "14 dias"
            },
            analysis_focus: "Otimização do funil de vendas e aumento de conversão",
            page_elements: [
                "Gargalos do Funil",
                "Canais Mais Eficientes",
                "Tempo de Conversão",
                "Distribuição por Etapa"
            ]
        };

        loadInsights('amigocare', 'funil_vendas', 'insights-container', 3, ['list', 'text', 'stat'], pageContext);
    }, 5000);
</script>

<!-- Análise de Funil com IA -->
<div class="bg-white rounded-lg shadow-sm p-4 mb-5">
    <div class="flex justify-between items-center mb-3">
        <div class="flex items-center">
            <svg class="w-4 h-4 text-systemBlue mr-1.5" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 1L9 9H2L7 14.5L5 22L12 17.5L19 22L17 14.5L22 9H15L12 1Z"></path>
            </svg>
            <h2 class="text-sm font-medium text-gray-700">Powered by Amigo Intelligence</h2>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <!-- Insight 1 -->
        <div class="bg-gray-50 p-4 rounded-md">
            <div class="flex items-center mb-3">
                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-sm font-medium">Gargalos do Funil</h3>
                    <p class="text-xs text-gray-500">Pontos de melhoria</p>
                </div>
            </div>
            <ul class="space-y-2 text-xs">
                <li class="flex justify-between">
                    <span>Contato inicial → Qualificação</span>
                    <span class="font-medium text-red-600">-32%</span>
                </li>
                <li class="flex justify-between">
                    <span>Proposta → Fechamento</span>
                    <span class="font-medium text-red-600">-28%</span>
                </li>
                <li class="flex justify-between">
                    <span>Qualificação → Proposta</span>
                    <span class="font-medium text-green-600">+5%</span>
                </li>
            </ul>
        </div>

        <!-- Insight 2 -->
        <div class="bg-gray-50 p-4 rounded-md">
            <div class="flex items-center mb-3">
                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-sm font-medium">Canais Mais Eficientes</h3>
                    <p class="text-xs text-gray-500">Por taxa de conversão</p>
                </div>
            </div>
            <ul class="space-y-2 text-xs">
                <li class="flex justify-between">
                    <span>Indicação</span>
                    <span class="font-medium">42%</span>
                </li>
                <li class="flex justify-between">
                    <span>Instagram</span>
                    <span class="font-medium">28%</span>
                </li>
                <li class="flex justify-between">
                    <span>Google</span>
                    <span class="font-medium">15%</span>
                </li>
            </ul>
        </div>

        <!-- Insight 3 -->
        <div class="bg-gray-50 p-4 rounded-md">
            <div class="flex items-center mb-3">
                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-sm font-medium">Tempo de Conversão</h3>
                    <p class="text-xs text-gray-500">Dias até fechamento</p>
                </div>
            </div>
            <ul class="space-y-2 text-xs">
                <li class="flex justify-between">
                    <span>Média geral</span>
                    <span class="font-medium">14 dias</span>
                </li>
                <li class="flex justify-between">
                    <span>Procedimentos estéticos</span>
                    <span class="font-medium">8 dias</span>
                </li>
                <li class="flex justify-between">
                    <span>Tratamentos contínuos</span>
                    <span class="font-medium">21 dias</span>
                </li>
            </ul>
        </div>
    </div>
</div>

<!-- Funil de Vendas Visual -->
<div class="bg-white rounded-lg shadow-sm p-4 mb-5">
    <div class="flex justify-between items-center mb-3">
        <h2 class="text-sm font-medium text-gray-700">Visualização do Funil de Vendas</h2>
        <div class="flex items-center">
            <select id="periodoFunil" class="text-xs border border-gray-200 rounded-md px-2 py-1 bg-white">
                <option value="mes">Último Mês</option>
                <option value="trimestre">Último Trimestre</option>
                <option value="semestre">Último Semestre</option>
                <option value="ano">Último Ano</option>
            </select>
        </div>
    </div>

    <div class="flex justify-center">
        <div class="w-full max-w-3xl">
            <!-- Funil Visual -->
            <div class="relative py-4">
                <!-- Etapa 1: Leads -->
                {% set leads_total = leads|length %}
                {% set leads_percent = 100 %}
                <div class="flex flex-col items-center mb-1">
                    <div class="w-full bg-blue-100 rounded-t-lg py-3 px-4 text-center">
                        <p class="text-sm font-medium text-gray-800">Leads Captados</p>
                        <p class="text-lg font-semibold text-systemBlue">{{ leads_total }}</p>
                        <p class="text-xs text-gray-600">100%</p>
                    </div>
                    <div class="w-0 h-0 border-l-[25px] border-r-[25px] border-t-[15px] border-transparent border-t-blue-100"></div>
                </div>

                <!-- Etapa 2: Qualificados -->
                {% set leads_qualificados = leads|selectattr('status', 'in', ['Contatado', 'Em negociação', 'Convertido'])|list|length %}
                {% set qualificados_percent = (leads_qualificados / leads_total * 100)|round|int if leads_total > 0 else 0 %}
                <div class="flex flex-col items-center mb-1">
                    <div class="w-[85%] bg-blue-200 py-3 px-4 text-center">
                        <p class="text-sm font-medium text-gray-800">Leads Qualificados</p>
                        <p class="text-lg font-semibold text-systemBlue">{{ leads_qualificados }}</p>
                        <p class="text-xs text-gray-600">{{ qualificados_percent }}%</p>
                    </div>
                    <div class="w-0 h-0 border-l-[20px] border-r-[20px] border-t-[12px] border-transparent border-t-blue-200"></div>
                </div>

                <!-- Etapa 3: Negociação -->
                {% set leads_negociacao = leads|selectattr('status', 'in', ['Em negociação', 'Convertido'])|list|length %}
                {% set negociacao_percent = (leads_negociacao / leads_total * 100)|round|int if leads_total > 0 else 0 %}
                <div class="flex flex-col items-center mb-1">
                    <div class="w-[70%] bg-blue-300 py-3 px-4 text-center">
                        <p class="text-sm font-medium text-gray-800">Em Negociação</p>
                        <p class="text-lg font-semibold text-systemBlue">{{ leads_negociacao }}</p>
                        <p class="text-xs text-gray-600">{{ negociacao_percent }}%</p>
                    </div>
                    <div class="w-0 h-0 border-l-[15px] border-r-[15px] border-t-[10px] border-transparent border-t-blue-300"></div>
                </div>

                <!-- Etapa 4: Convertidos -->
                {% set leads_convertidos = leads|selectattr('status', 'equalto', 'Convertido')|list|length %}
                {% set convertidos_percent = (leads_convertidos / leads_total * 100)|round|int if leads_total > 0 else 0 %}
                <div class="flex flex-col items-center">
                    <div class="w-[55%] bg-blue-500 rounded-b-lg py-3 px-4 text-center">
                        <p class="text-sm font-medium text-white">Convertidos</p>
                        <p class="text-lg font-semibold text-white">{{ leads_convertidos }}</p>
                        <p class="text-xs text-blue-100">{{ convertidos_percent }}%</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Análise por Origem e Interesse -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-5 mb-5">
    <!-- Conversão por Origem -->
    <div class="bg-white rounded-lg shadow-sm p-4">
        <h2 class="text-sm font-medium text-gray-700 mb-3">Conversão por Origem</h2>
        <div class="h-64">
            <canvas id="origemChart"></canvas>
        </div>
    </div>

    <!-- Conversão por Interesse -->
    <div class="bg-white rounded-lg shadow-sm p-4">
        <h2 class="text-sm font-medium text-gray-700 mb-3">Conversão por Interesse</h2>
        <div class="h-64">
            <canvas id="interesseChart"></canvas>
        </div>
    </div>
</div>

<!-- Tabela de Leads em Negociação -->
<div class="bg-white rounded-lg shadow-sm p-4 mb-5">
    <div class="flex justify-between items-center mb-3">
        <h2 class="text-sm font-medium text-gray-700">Leads em Negociação</h2>
        <button class="bg-systemBlue hover:bg-blue-600 text-white px-3 py-1 rounded-md text-xs font-medium transition-colors">
            Ver Todos
        </button>
    </div>

    <div class="overflow-x-auto">
        <table class="w-full text-xs">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Nome</th>
                    <th class="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Origem</th>
                    <th class="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Interesse</th>
                    <th class="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Valor Potencial</th>
                    <th class="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Probabilidade</th>
                    <th class="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Último Contato</th>
                    <th class="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Ações</th>
                </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
                {% set leads_negociacao = leads|selectattr('status', 'equalto', 'Em negociação')|sort(attribute='probabilidade', reverse=true)|list %}
                {% for lead in leads_negociacao[:8] %}
                <tr class="hover:bg-gray-50">
                    <td class="px-3 py-2 text-gray-800">{{ lead.nome }}</td>
                    <td class="px-3 py-2 text-gray-800">{{ lead.origem }}</td>
                    <td class="px-3 py-2 text-gray-800">{{ lead.interesse }}</td>
                    <td class="px-3 py-2 text-gray-800">R$ {{ lead.valor_potencial|round|int }}</td>
                    <td class="px-3 py-2">
                        <div class="flex items-center">
                            <div class="w-full bg-gray-200 rounded-full h-2 mr-2">
                                <div class="bg-systemBlue h-2 rounded-full" style="width: {{ lead.probabilidade }}%"></div>
                            </div>
                            <span class="text-gray-800">{{ lead.probabilidade }}%</span>
                        </div>
                    </td>
                    <td class="px-3 py-2 text-gray-800">{{ lead.data_ultimo_contato }}</td>
                    <td class="px-3 py-2">
                        <div class="flex space-x-2">
                            <button class="text-systemBlue hover:text-blue-700">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                            </button>
                            <button class="text-systemBlue hover:text-blue-700">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                                </svg>
                            </button>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Dados para o gráfico de conversão por origem
        const origens = [];
        const origensConversao = [];

        {% set origens_unicas = leads|map(attribute='origem')|unique|list %}
        {% for origem in origens_unicas %}
        origens.push("{{ origem }}");

        {% set origem_leads = leads|selectattr('origem', 'equalto', origem)|list %}
        {% set origem_convertidos = origem_leads|selectattr('status', 'equalto', 'Convertido')|list|length %}
        {% set origem_taxa = (origem_convertidos / origem_leads|length * 100)|round|int if origem_leads|length > 0 else 0 %}

        origensConversao.push({{ origem_taxa }});
        {% endfor %}

        // Gráfico de conversão por origem
        const origemChart = new Chart(
            document.getElementById('origemChart'),
            {
                type: 'bar',
                data: {
                    labels: origens,
                    datasets: [{
                        label: 'Taxa de Conversão (%)',
                        data: origensConversao,
                        backgroundColor: 'rgba(0, 122, 255, 0.7)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            title: {
                                display: true,
                                text: 'Taxa de Conversão (%)'
                            }
                        }
                    }
                }
            }
        );

        // Dados para o gráfico de conversão por interesse
        const interesses = [];
        const interessesConversao = [];

        {% set interesses_unicos = leads|map(attribute='interesse')|unique|list %}
        {% for interesse in interesses_unicos %}
        interesses.push("{{ interesse }}");

        {% set interesse_leads = leads|selectattr('interesse', 'equalto', interesse)|list %}
        {% set interesse_convertidos = interesse_leads|selectattr('status', 'equalto', 'Convertido')|list|length %}
        {% set interesse_taxa = (interesse_convertidos / interesse_leads|length * 100)|round|int if interesse_leads|length > 0 else 0 %}

        interessesConversao.push({{ interesse_taxa }});
        {% endfor %}

        // Gráfico de conversão por interesse
        const interesseChart = new Chart(
            document.getElementById('interesseChart'),
            {
                type: 'bar',
                data: {
                    labels: interesses,
                    datasets: [{
                        label: 'Taxa de Conversão (%)',
                        data: interessesConversao,
                        backgroundColor: 'rgba(52, 199, 89, 0.7)',
                        borderColor: 'rgba(52, 199, 89, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            title: {
                                display: true,
                                text: 'Taxa de Conversão (%)'
                            }
                        }
                    }
                }
            }
        );
    });
</script>
{% endblock %}

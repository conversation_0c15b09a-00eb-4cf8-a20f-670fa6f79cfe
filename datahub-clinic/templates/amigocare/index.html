{% extends 'base.html' %}

{% block title %}Amigo Care+ - Amigo DataHub{% endblock %}

{% block header %}Amigo Care+{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="bg-white rounded-view p-6 border border-gray-200 mb-6">
    <div class="flex flex-col md:flex-row items-center">
        <div class="md:w-2/3 mb-6 md:mb-0 md:pr-6">
            <h1 class="text-2xl font-bold text-label-DEFAULT mb-2">Bem-vindo ao Amigo Care+</h1>
            <p class="text-label-secondary mb-4">
                O módulo Amigo Care+ oferece ferramentas avançadas para acompanhamento da satisfação dos pacientes, gestão de leads e campanhas de marketing, permitindo melhorar a experiência do paciente e impulsionar o crescimento da clínica.
            </p>
            <div class="flex flex-wrap gap-3">
                <a href="{{ url_for('avaliacao_nps') }}" class="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-control transition duration-150 ease-in-out text-sm inline-flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                    </svg>
                    Avaliação NPS
                </a>
                <a href="{{ url_for('leads') }}" class="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-control transition duration-150 ease-in-out text-sm inline-flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                    </svg>
                    Leads
                </a>
                <a href="{{ url_for('campanhas') }}" class="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-control transition duration-150 ease-in-out text-sm inline-flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"></path>
                    </svg>
                    Campanhas
                </a>
            </div>
        </div>
        <div class="md:w-1/3">
            <div class="bg-systemGray-ultralight p-4 rounded-view">
                <div class="flex items-center mb-3">
                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                        <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-sm font-semibold">Dica Rápida</h3>
                        <p class="text-xs text-label-secondary">Melhore seus resultados</p>
                    </div>
                </div>
                <p class="text-sm text-label-DEFAULT">
                    Pacientes que recebem follow-up após o atendimento têm 35% mais chances de retornar para novos procedimentos. Configure lembretes automáticos para sua equipe entrar em contato com pacientes após consultas.
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Resumo e Insights -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
    <!-- NPS Geral -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-label-DEFAULT">NPS Geral</p>
        </div>
        {% set promotores = dados.avaliacao_nps|selectattr('score', 'ge', 9)|list|length %}
        {% set detratores = dados.avaliacao_nps|selectattr('score', 'le', 6)|list|length %}
        {% set total = dados.avaliacao_nps|length %}
        {% set nps = ((promotores / total * 100) - (detratores / total * 100))|round|int if total > 0 else 0 %}

        <p class="text-3xl font-semibold text-label-DEFAULT mb-1">
            {{ nps }}
        </p>
        <div class="flex items-center text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">
            <span>Net Promoter Score</span>
            <span class="ml-auto {% if nps > 50 %}text-systemGreen{% elif nps > 0 %}text-systemBlue{% else %}text-red-600{% endif %} font-medium">
                {% if nps > 50 %}Excelente{% elif nps > 30 %}Bom{% elif nps > 0 %}Regular{% else %}Crítico{% endif %}
            </span>
        </div>
    </div>

    <!-- Leads Ativos -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-label-DEFAULT">Leads Ativos</p>
        </div>
        {% set ativos = dados.leads|selectattr('status', 'in', ['Novo', 'Contatado', 'Em negociação'])|list|length %}
        <p class="text-3xl font-semibold text-label-DEFAULT mb-1">
            {{ '{:,}'.format(ativos).replace(',', '.') if ativos < 1000 else '{:.1f}K'.format(ativos/1000) }}
        </p>
        <div class="flex items-center text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">
            <span>Em processo de conversão</span>
            <span class="ml-auto text-systemBlue font-medium">{{ (ativos / dados.leads|length * 100)|round|int if dados.leads|length > 0 else 0 }}%</span>
        </div>
    </div>

    <!-- Campanhas Ativas -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-label-DEFAULT">Campanhas Ativas</p>
        </div>
        {% set ativas = dados.campanhas|selectattr('status', 'equalto', 'Em andamento')|list|length %}
        <p class="text-3xl font-semibold text-label-DEFAULT mb-1">
            {{ ativas }}
        </p>
        <div class="flex items-center text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">
            <span>Em andamento</span>
            <span class="ml-auto text-systemBlue font-medium">{{ (ativas / dados.campanhas|length * 100)|round|int if dados.campanhas|length > 0 else 0 }}%</span>
        </div>
    </div>
</div>

<!-- Insights com IA -->
<div class="mb-6">
    <div class="flex items-center justify-between mb-4">
        <div class="flex items-center">
            <svg class="w-5 h-5 text-systemBlue mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
            </svg>
            <h2 class="text-base font-semibold text-gray-800">Amigo Intelligence</h2>
        </div>
        <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">Atualizado agora</span>
    </div>

    <div id="insights-container" class="grid grid-cols-1 md:grid-cols-3 gap-3">
        {% for insight in insights %}
            {% with
                title=insight.title,
                description=insight.description,
                insight_type=insight.insight_type,
                content=insight.content,
                category=insight.category,
                action_text=insight.action_text,
                action_url=insight.action_url
            %}
                {% include 'components/ai_insight_widget.html' %}
            {% endwith %}
        {% endfor %}
    </div>
</div>

<script>
    // Carregar insights dinamicamente após 5 segundos (para demonstração)
    setTimeout(() => {
        // Contexto específico da página de índice do AmigoCare+
        const pageContext = {
            page_title: "Amigo Care+",
            page_description: "Ferramentas avançadas para acompanhamento da satisfação dos pacientes, gestão de leads e campanhas de marketing",
            key_metrics: {
                "NPS Geral": "{{ nps }}",
                "Leads Ativos": "{{ ativos }}",
                "Campanhas Ativas": "{{ ativas }}"
            },
            analysis_focus: "Satisfação do paciente, conversão de leads e eficácia de campanhas",
            page_elements: [
                "Avaliação NPS",
                "Leads",
                "Campanhas",
                "Atividades Recentes"
            ]
        };

        loadInsights('amigocare', 'index', 'insights-container', 3, ['list', 'text', 'stat'], pageContext);
    }, 5000);
</script>

<!-- Insights Estáticos (Temporários) -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6 hidden">
    <!-- Insight de NPS -->
    {% with
        title="Análise de NPS",
        description="Fatores que impactam a satisfação",
        insight_type="list",
        content=[
            "Tempo de espera superior a 15 minutos reduz o NPS em <strong>18 pontos</strong>",
            "Pacientes atendidos pelo Dr. Carlos têm NPS <strong>12% maior</strong> que a média",
            "Confirmações por WhatsApp aumentam a satisfação em <strong>9%</strong>"
        ],
        category="Satisfação",
        action_text="Ver análise completa",
        action_url=url_for('avaliacao_nps')
    %}
        {% include 'components/ai_insight_widget.html' %}
    {% endwith %}

    <!-- Insight de Leads -->
    {% with
        title="Conversão de Leads",
        description="Oportunidades de melhoria",
        insight_type="text",
        content="Leads contatados em até <strong>30 minutos</strong> têm taxa de conversão <strong>3.2x maior</strong>. Implementar um sistema de resposta rápida poderia aumentar a conversão geral em até <strong>28%</strong>, gerando aproximadamente <strong>R$ 32K</strong> em receita adicional por mês.",
        category="Conversão",
        action_text="Explorar estratégias",
        action_url=url_for('leads')
    %}
        {% include 'components/ai_insight_widget.html' %}
    {% endwith %}

    <!-- Insight de Campanhas -->
    {% with
        title="Eficácia de Campanhas",
        description="Análise de desempenho",
        insight_type="list",
        content=[
            "Campanhas de <strong>reativação</strong> têm ROI médio de <strong>320%</strong>",
            "E-mails enviados às <strong>terças, 10h</strong> têm taxa de abertura <strong>22% maior</strong>",
            "Segmentação por histórico de tratamento aumenta conversão em <strong>35%</strong>"
        ],
        category="Marketing",
        action_text="Ver detalhes",
        action_url=url_for('campanhas')
    %}
        {% include 'components/ai_insight_widget.html' %}
    {% endwith %}
</div>

<!-- Gráficos -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Evolução do NPS -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <h2 class="text-base font-semibold text-label-DEFAULT mb-4">Evolução do NPS</h2>
        <div class="h-64">
            <canvas id="evolucaoNpsChart"></canvas>
        </div>
    </div>

    <!-- Leads por Origem -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <h2 class="text-base font-semibold text-label-DEFAULT mb-4">Leads por Origem</h2>
        <div class="h-64">
            <canvas id="leadsOrigemChart"></canvas>
        </div>
    </div>
</div>

<!-- Atividades Recentes -->
<div class="bg-white rounded-view p-6 border border-gray-200">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-base font-semibold text-label-DEFAULT">Atividades Recentes</h2>
    </div>

    <div class="space-y-4">
        <div class="flex items-start">
            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                </svg>
            </div>
            <div>
                <p class="text-sm font-medium text-label-DEFAULT">Nova avaliação NPS recebida</p>
                <p class="text-xs text-label-secondary">{{ dados.avaliacao_nps[0].paciente }} avaliou com nota {{ dados.avaliacao_nps[0].score }} - {{ dados.avaliacao_nps[0].categoria }}</p>
                <p class="text-xs text-gray-500 mt-1">Hoje às 14:32</p>
            </div>
        </div>

        <div class="flex items-start">
            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                </svg>
            </div>
            <div>
                <p class="text-sm font-medium text-label-DEFAULT">Novo lead cadastrado</p>
                <p class="text-xs text-label-secondary">{{ dados.leads[0].nome }} - Interesse em {{ dados.leads[0].interesse }}</p>
                <p class="text-xs text-gray-500 mt-1">Hoje às 11:15</p>
            </div>
        </div>

        <div class="flex items-start">
            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"></path>
                </svg>
            </div>
            <div>
                <p class="text-sm font-medium text-label-DEFAULT">Campanha iniciada</p>
                <p class="text-xs text-label-secondary">{{ dados.campanhas[0].nome }}</p>
                <p class="text-xs text-gray-500 mt-1">Ontem às 09:45</p>
            </div>
        </div>

        <div class="flex items-start">
            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div>
                <p class="text-sm font-medium text-label-DEFAULT">Lead convertido</p>
                <p class="text-xs text-label-secondary">{{ dados.leads[1].nome }} - {{ dados.leads[1].interesse }}</p>
                <p class="text-xs text-gray-500 mt-1">Ontem às 16:20</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Dados para o gráfico de evolução do NPS
        const meses = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'];
        {% set nps = ((promotores / total * 100) - (detratores / total * 100))|round|int if total > 0 else 0 %}
        const evolucaoNPS = [45, 48, 52, 55, 58, 62, 65, 68, 70, 72, 75, {{ nps }}];

        // Gráfico de evolução do NPS
        const evolucaoNpsChart = new Chart(
            document.getElementById('evolucaoNpsChart'),
            {
                type: 'line',
                data: {
                    labels: meses,
                    datasets: [{
                        label: 'NPS',
                        data: evolucaoNPS,
                        backgroundColor: 'rgba(0, 122, 255, 0.1)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            }
        );

        // Dados para o gráfico de leads por origem
        const origens = [];
        const origensCount = [];

        {% for origem in dados.leads|map(attribute='origem')|unique %}
        origens.push("{{ origem }}");
        origensCount.push({{ dados.leads|selectattr('origem', 'equalto', origem)|list|length }});
        {% endfor %}

        // Gráfico de leads por origem
        const leadsOrigemChart = new Chart(
            document.getElementById('leadsOrigemChart'),
            {
                type: 'pie',
                data: {
                    labels: origens,
                    datasets: [{
                        data: origensCount,
                        backgroundColor: [
                            'rgba(0, 122, 255, 0.7)',
                            'rgba(142, 142, 147, 0.7)',
                            'rgba(174, 174, 178, 0.7)',
                            'rgba(199, 199, 204, 0.7)',
                            'rgba(209, 209, 214, 0.7)',
                            'rgba(229, 229, 234, 0.7)'
                        ],
                        borderColor: [
                            'rgba(0, 122, 255, 1)',
                            'rgba(142, 142, 147, 1)',
                            'rgba(174, 174, 178, 1)',
                            'rgba(199, 199, 204, 1)',
                            'rgba(209, 209, 214, 1)',
                            'rgba(229, 229, 234, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        }
                    }
                }
            }
        );
    });
</script>
{% endblock %}

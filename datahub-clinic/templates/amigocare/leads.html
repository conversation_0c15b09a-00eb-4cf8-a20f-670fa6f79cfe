{% extends 'base.html' %}

{% block title %}Leads - Amigo DataHub{% endblock %}

{% block header %}Leads{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="bg-white rounded-lg shadow-sm p-6 mb-5">
    <div class="flex flex-col md:flex-row items-center">
        <div class="md:w-2/3 mb-4 md:mb-0 md:pr-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-2">Gestão de Leads</h1>
            <p class="text-gray-600 mb-4">
                Acompanhe e gerencie seus leads de forma eficiente, desde a captação até a conversão em pacientes.
            </p>
            <div class="flex flex-wrap gap-3">
                <button class="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
                    Novo Lead
                </button>
                <button class="bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-md text-sm font-medium border border-gray-200 transition-colors">
                    Exportar Relatório
                </button>
            </div>
        </div>
        <div class="md:w-1/3 flex justify-center">
            <img src="/static/images/leads.svg" alt="Gestão de Leads" class="w-48 h-48 object-contain" onerror="this.src='https://cdn-icons-png.flaticon.com/512/3588/3588614.png'; this.onerror=null;">
        </div>
    </div>
</div>

<!-- KPIs -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-5">
    <!-- Total de Leads -->
    <div class="bg-white rounded-lg shadow-sm p-4">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                </svg>
            </div>
            <div>
                <p class="text-xs font-medium text-gray-500">Total de Leads</p>
                <p class="text-xl font-semibold text-gray-800">{{ leads|length }}</p>
            </div>
        </div>
        <div class="flex items-center text-xs text-gray-500 mt-2 pt-2 border-t border-gray-100">
            <span>Últimos 30 dias</span>
        </div>
    </div>

    <!-- Taxa de Conversão -->
    {% set convertidos = leads|selectattr('status', 'equalto', 'Convertido')|list|length %}
    {% set taxa_conversao = (convertidos / leads|length * 100)|round|int if leads|length > 0 else 0 %}
    <div class="bg-white rounded-lg shadow-sm p-4">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mr-3">
                <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div>
                <p class="text-xs font-medium text-gray-500">Taxa de Conversão</p>
                <p class="text-xl font-semibold text-gray-800">{{ taxa_conversao }}%</p>
            </div>
        </div>
        <div class="flex items-center text-xs text-gray-500 mt-2 pt-2 border-t border-gray-100">
            <span>Leads convertidos</span>
            <span class="ml-auto {% if taxa_conversao > 20 %}text-green-600{% elif taxa_conversao > 10 %}text-systemBlue{% else %}text-red-600{% endif %} font-medium">
                {% if taxa_conversao > 20 %}Excelente{% elif taxa_conversao > 10 %}Bom{% else %}Precisa melhorar{% endif %}
            </span>
        </div>
    </div>

    <!-- Valor Potencial -->
    {% set valor_total = leads|sum(attribute='valor_potencial') %}
    <div class="bg-white rounded-lg shadow-sm p-4">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div>
                <p class="text-xs font-medium text-gray-500">Valor Potencial</p>
                <p class="text-xl font-semibold text-gray-800">R$ {{ (valor_total/1000)|round|int }}K</p>
            </div>
        </div>
        <div class="flex items-center text-xs text-gray-500 mt-2 pt-2 border-t border-gray-100">
            <span>Receita estimada</span>
        </div>
    </div>

    <!-- Leads Ativos -->
    {% set ativos = leads|selectattr('status', 'in', ['Novo', 'Contatado', 'Em negociação'])|list|length %}
    <div class="bg-white rounded-lg shadow-sm p-4">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 rounded-full bg-yellow-100 flex items-center justify-center mr-3">
                <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div>
                <p class="text-xs font-medium text-gray-500">Leads Ativos</p>
                <p class="text-xl font-semibold text-gray-800">{{ ativos }}</p>
            </div>
        </div>
        <div class="flex items-center text-xs text-gray-500 mt-2 pt-2 border-t border-gray-100">
            <span>Em processo de conversão</span>
            <span class="ml-auto text-systemBlue font-medium">{{ (ativos / leads|length * 100)|round|int }}%</span>
        </div>
    </div>
</div>

<!-- Filtros Avançados -->
<div class="bg-white rounded-lg shadow-sm p-4 mb-5">
    <div class="flex justify-between items-center mb-3">
        <div class="flex items-center">
            <svg class="w-4 h-4 text-gray-500 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
            </svg>
            <h2 class="text-sm font-medium text-gray-700">Filtros Avançados</h2>
        </div>
        <button class="text-xs text-systemBlue hover:text-blue-700 transition-colors">Limpar</button>
    </div>

    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-3">
        <div>
            <label for="data_inicio" class="block text-xs font-medium text-gray-500 mb-1">Data Início</label>
            <input type="date" id="data_inicio" class="block w-full px-3 py-1.5 text-sm border border-gray-200 focus:outline-none focus:ring-1 focus:ring-systemBlue focus:border-systemBlue rounded-md transition-colors">
        </div>

        <div>
            <label for="data_fim" class="block text-xs font-medium text-gray-500 mb-1">Data Fim</label>
            <input type="date" id="data_fim" class="block w-full px-3 py-1.5 text-sm border border-gray-200 focus:outline-none focus:ring-1 focus:ring-systemBlue focus:border-systemBlue rounded-md transition-colors">
        </div>

        <div>
            <label for="origem" class="block text-xs font-medium text-gray-500 mb-1">Origem</label>
            <select id="origem" class="block w-full px-3 py-1.5 text-sm border border-gray-200 focus:outline-none focus:ring-1 focus:ring-systemBlue focus:border-systemBlue rounded-md transition-colors">
                <option value="">Todas</option>
                {% for origem in leads|map(attribute='origem')|unique %}
                <option>{{ origem }}</option>
                {% endfor %}
            </select>
        </div>

        <div>
            <label for="status" class="block text-xs font-medium text-gray-500 mb-1">Status</label>
            <select id="status" class="block w-full px-3 py-1.5 text-sm border border-gray-200 focus:outline-none focus:ring-1 focus:ring-systemBlue focus:border-systemBlue rounded-md transition-colors">
                <option value="">Todos</option>
                {% for status in leads|map(attribute='status')|unique %}
                <option>{{ status }}</option>
                {% endfor %}
            </select>
        </div>

        <div class="flex items-end">
            <button class="w-full bg-systemBlue hover:bg-blue-600 text-white px-4 py-1.5 rounded-md transition-colors text-sm font-medium">
                Aplicar Filtros
            </button>
        </div>
    </div>
</div>

<!-- Gráficos -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-5 mb-5">
    <!-- Leads por Origem -->
    <div class="bg-white rounded-lg shadow-sm p-4">
        <h2 class="text-sm font-medium text-gray-700 mb-3">Leads por Origem</h2>
        <div class="h-64">
            <canvas id="leadsOrigemChart"></canvas>
        </div>
    </div>

    <!-- Leads por Status -->
    <div class="bg-white rounded-lg shadow-sm p-4">
        <h2 class="text-sm font-medium text-gray-700 mb-3">Leads por Status</h2>
        <div class="h-64">
            <canvas id="leadsStatusChart"></canvas>
        </div>
    </div>
</div>

<!-- Insights com IA -->
<div class="bg-white rounded-lg shadow-sm p-4 mb-5">
    <div class="flex items-center justify-between mb-4">
        <div class="flex items-center">
            <svg class="w-5 h-5 text-systemBlue mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
            </svg>
            <h2 class="text-base font-semibold text-gray-800">Amigo Intelligence</h2>
        </div>
        <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">Atualizado agora</span>
    </div>

    <div id="insights-container" class="grid grid-cols-1 md:grid-cols-3 gap-3">
        {% for insight in insights %}
            {% with
                title=insight.title,
                description=insight.description,
                insight_type=insight.insight_type,
                content=insight.content,
                category=insight.category,
                action_text=insight.action_text,
                action_url=insight.action_url
            %}
                {% include 'components/ai_insight_widget.html' %}
            {% endwith %}
        {% endfor %}
    </div>
</div>

<script>
    // Carregar insights dinamicamente após 5 segundos (para demonstração)
    setTimeout(() => {
        // Contexto específico da página de leads
        const pageContext = {
            page_title: "Gestão de Leads",
            page_description: "Acompanhamento e gestão de leads desde a captação até a conversão em pacientes",
            key_metrics: {
                "Total de Leads": "{{ leads|length }}",
                "Taxa de Conversão": "{{ taxa_conversao }}%",
                "Valor Potencial": "R$ {{ (valor_total/1000)|round|int }}K",
                "Leads Ativos": "{{ ativos }}"
            },
            analysis_focus: "Conversão de leads e otimização de canais de captação",
            page_elements: [
                "Leads por Origem",
                "Leads por Status",
                "Detalhamento dos Leads"
            ],
            lead_data: {
                "origens": [{% for origem in leads|map(attribute='origem')|unique %}"{{ origem }}"{% if not loop.last %}, {% endif %}{% endfor %}],
                "status": ["Novo", "Contatado", "Em negociação", "Convertido", "Perdido"],
                "interesses": [{% for interesse in leads|map(attribute='interesse')|unique %}"{{ interesse }}"{% if not loop.last %}, {% endif %}{% endfor %}]
            }
        };

        loadInsights('amigocare', 'leads', 'insights-container', 3, ['list', 'text', 'stat'], pageContext);
    }, 5000);
</script>

<!-- Insights Estáticos (Temporários) -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-4 hidden">
    <!-- Insight 1 -->
    <div class="bg-gray-50 p-4 rounded-md">
        <div class="flex items-center mb-3">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                </svg>
            </div>
            <div>
                <h3 class="text-sm font-medium">Melhores Origens</h3>
                <p class="text-xs text-gray-500">Por taxa de conversão</p>
            </div>
        </div>
        <ul class="space-y-2 text-xs">
            <li class="flex justify-between">
                <span>Indicação</span>
                <span class="font-medium">35%</span>
            </li>
            <li class="flex justify-between">
                <span>WhatsApp</span>
                <span class="font-medium">28%</span>
            </li>
            <li class="flex justify-between">
                <span>Instagram</span>
                <span class="font-medium">22%</span>
            </li>
        </ul>
    </div>

    <!-- Insight 2 -->
    <div class="bg-gray-50 p-4 rounded-md">
        <div class="flex items-center mb-3">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                </svg>
            </div>
            <div>
                <h3 class="text-sm font-medium">Interesses Populares</h3>
                <p class="text-xs text-gray-500">Procedimentos mais buscados</p>
            </div>
        </div>
        <ul class="space-y-2 text-xs">
            <li class="flex justify-between">
                <span>BOTOX MALAR</span>
                <span class="font-medium">32%</span>
            </li>
            <li class="flex justify-between">
                <span>Sessão de Fisioterapia</span>
                <span class="font-medium">24%</span>
            </li>
            <li class="flex justify-between">
                <span>Consulta - Amigo tech</span>
                <span class="font-medium">18%</span>
            </li>
        </ul>
    </div>

    <!-- Insight 3 -->
    <div class="bg-gray-50 p-4 rounded-md">
        <div class="flex items-center mb-3">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div>
                <h3 class="text-sm font-medium">Recomendações</h3>
                <p class="text-xs text-gray-500">Ações para melhorar conversão</p>
            </div>
        </div>
        <ul class="space-y-2 text-xs">
            <li class="flex items-start">
                <svg class="w-3.5 h-3.5 text-systemBlue mr-1 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span>Aumentar investimento em Instagram</span>
            </li>
            <li class="flex items-start">
                <svg class="w-3.5 h-3.5 text-systemBlue mr-1 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span>Implementar programa de indicação</span>
            </li>
            <li class="flex items-start">
                <svg class="w-3.5 h-3.5 text-systemBlue mr-1 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span>Reduzir tempo de resposta para leads do WhatsApp</span>
            </li>
        </ul>
    </div>
</div>

<!-- Tabela de Leads -->
<div class="bg-white rounded-lg shadow-sm p-4 mb-5">
    <div class="flex justify-between items-center mb-3">
        <h2 class="text-sm font-medium text-gray-700">Detalhamento dos Leads</h2>
        <div class="flex gap-2">
            <button class="bg-white hover:bg-gray-50 text-gray-700 px-2 py-1 rounded text-xs border border-gray-200 transition-colors flex items-center">
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                </svg>
                Exportar
            </button>
            <button class="bg-systemBlue hover:bg-blue-600 text-white px-2 py-1 rounded text-xs transition-colors flex items-center">
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Novo Lead
            </button>
        </div>
    </div>

    <div class="overflow-x-auto">
        <table class="w-full text-xs">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">ID</th>
                    <th class="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Data</th>
                    <th class="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Nome</th>
                    <th class="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Origem</th>
                    <th class="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Interesse</th>
                    <th class="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Valor</th>
                    <th class="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Prob.</th>
                    <th class="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Ações</th>
                </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
                {% for lead in leads[:8] %}
                <tr class="hover:bg-gray-50">
                    <td class="px-3 py-2 text-gray-800">{{ lead.id }}</td>
                    <td class="px-3 py-2 text-gray-800">{{ lead.data }}</td>
                    <td class="px-3 py-2 text-gray-800">{{ lead.nome }}</td>
                    <td class="px-3 py-2 text-gray-800">{{ lead.origem }}</td>
                    <td class="px-3 py-2">
                        <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium
                        {% if lead.status == 'Convertido' %}
                            bg-green-100 text-green-800
                        {% elif lead.status == 'Perdido' %}
                            bg-red-100 text-red-800
                        {% elif lead.status == 'Em negociação' %}
                            bg-blue-100 text-blue-800
                        {% elif lead.status == 'Contatado' %}
                            bg-purple-100 text-purple-800
                        {% else %}
                            bg-gray-100 text-gray-800
                        {% endif %}
                        ">
                            {{ lead.status }}
                        </span>
                    </td>
                    <td class="px-3 py-2 text-gray-800">{{ lead.interesse }}</td>
                    <td class="px-3 py-2 text-gray-800">R$ {{ lead.valor_potencial|round|int }}</td>
                    <td class="px-3 py-2">
                        <div class="flex items-center">
                            <div class="w-12 bg-gray-200 rounded-full h-1.5 mr-2">
                                <div class="h-1.5 rounded-full
                                {% if lead.probabilidade >= 70 %}
                                    bg-green-600
                                {% elif lead.probabilidade >= 30 %}
                                    bg-systemBlue
                                {% else %}
                                    bg-gray-400
                                {% endif %}"
                                style="width: {{ lead.probabilidade }}%"></div>
                            </div>
                            <span class="text-gray-800">{{ lead.probabilidade }}%</span>
                        </div>
                    </td>
                    <td class="px-3 py-2">
                        <div class="flex space-x-2">
                            <button class="text-systemBlue hover:text-blue-700">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                            </button>
                            <button class="text-systemBlue hover:text-blue-700">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                                </svg>
                            </button>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Paginação -->
    <div class="flex justify-between items-center mt-3 text-xs">
        <div class="text-gray-500">
            Mostrando 8 de {{ leads|length }} leads
        </div>
        <div class="flex gap-2">
            <button class="px-2 py-1 rounded bg-white border border-gray-200 text-gray-500 hover:bg-gray-50 transition-colors">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>
            <button class="px-2 py-1 rounded bg-systemBlue text-white">1</button>
            <button class="px-2 py-1 rounded bg-white border border-gray-200 text-gray-500 hover:bg-gray-50 transition-colors">2</button>
            <button class="px-2 py-1 rounded bg-white border border-gray-200 text-gray-500 hover:bg-gray-50 transition-colors">3</button>
            <button class="px-2 py-1 rounded bg-white border border-gray-200 text-gray-500 hover:bg-gray-50 transition-colors">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Dados para o gráfico de leads por origem
        const origens = [];
        const origensCount = [];

        {% for origem in leads|map(attribute='origem')|unique %}
        origens.push("{{ origem }}");
        origensCount.push({{ leads|selectattr('origem', 'equalto', origem)|list|length }});
        {% endfor %}

        // Gráfico de leads por origem
        const leadsOrigemChart = new Chart(
            document.getElementById('leadsOrigemChart'),
            {
                type: 'pie',
                data: {
                    labels: origens,
                    datasets: [{
                        data: origensCount,
                        backgroundColor: [
                            'rgba(0, 122, 255, 0.7)',
                            'rgba(142, 142, 147, 0.7)',
                            'rgba(174, 174, 178, 0.7)',
                            'rgba(199, 199, 204, 0.7)',
                            'rgba(209, 209, 214, 0.7)',
                            'rgba(229, 229, 234, 0.7)'
                        ],
                        borderColor: [
                            'rgba(0, 122, 255, 1)',
                            'rgba(142, 142, 147, 1)',
                            'rgba(174, 174, 178, 1)',
                            'rgba(199, 199, 204, 1)',
                            'rgba(209, 209, 214, 1)',
                            'rgba(229, 229, 234, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        }
                    }
                }
            }
        );

        // Dados para o gráfico de leads por status
        const statusLabels = ['Novo', 'Contatado', 'Em negociação', 'Convertido', 'Perdido'];
        const statusCounts = [
            {{ leads|selectattr('status', 'equalto', 'Novo')|list|length }},
            {{ leads|selectattr('status', 'equalto', 'Contatado')|list|length }},
            {{ leads|selectattr('status', 'equalto', 'Em negociação')|list|length }},
            {{ leads|selectattr('status', 'equalto', 'Convertido')|list|length }},
            {{ leads|selectattr('status', 'equalto', 'Perdido')|list|length }}
        ];

        // Gráfico de leads por status
        const leadsStatusChart = new Chart(
            document.getElementById('leadsStatusChart'),
            {
                type: 'bar',
                data: {
                    labels: statusLabels,
                    datasets: [{
                        label: 'Quantidade',
                        data: statusCounts,
                        backgroundColor: [
                            'rgba(142, 142, 147, 0.7)',
                            'rgba(174, 174, 178, 0.7)',
                            'rgba(0, 122, 255, 0.7)',
                            'rgba(52, 199, 89, 0.7)',
                            'rgba(255, 59, 48, 0.7)'
                        ],
                        borderColor: [
                            'rgba(142, 142, 147, 1)',
                            'rgba(174, 174, 178, 1)',
                            'rgba(0, 122, 255, 1)',
                            'rgba(52, 199, 89, 1)',
                            'rgba(255, 59, 48, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            }
        );
    });
</script>
{% endblock %}

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Feature Store - Amigo DataStudio</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            overflow-x: hidden;
        }

        .main-header {
            background-color: white;
            border-bottom: 1px solid #e5e7eb;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .sidebar-active {
            background-color: rgba(0, 122, 255, 0.1);
            color: #007AFF;
            font-weight: 500;
        }

        .btn-primary {
            background-color: #007AFF;
            color: white;
            transition: all 0.2s;
        }

        .btn-primary:hover {
            background-color: #0056b3;
        }

        .btn-secondary {
            background-color: #34C759;
            color: white;
            transition: all 0.2s;
        }

        .btn-secondary:hover {
            background-color: #2aa147;
        }

        .btn-danger {
            background-color: #FF3B30;
            color: white;
            transition: all 0.2s;
        }

        .btn-danger:hover {
            background-color: #d63229;
        }

        .feature-card {
            transition: all 0.2s;
        }

        .feature-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        .toolbox-item {
            transition: all 0.2s;
            cursor: pointer;
        }

        .toolbox-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .chart-container {
            height: 250px;
            width: 100%;
        }

        .model-card {
            transition: all 0.2s;
        }

        .model-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        .tab-active {
            color: #007AFF;
            border-color: #007AFF;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .toast {
            position: fixed;
            top: 1rem;
            right: 1rem;
            padding: 0.75rem 1.5rem;
            border-radius: 0.375rem;
            z-index: 50;
            animation: fadeInOut 3s forwards;
        }

        @keyframes fadeInOut {
            0% { opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { opacity: 0; }
        }

        /* Estilo para o switch de tema */
        .theme-switch {
            position: relative;
            display: inline-block;
            width: 40px;
            height: 20px;
        }

        .theme-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #2196F3;
        }

        input:checked + .slider:before {
            transform: translateX(20px);
        }

        /* Tema escuro */
        .dark-mode {
            background-color: #1a1a1a;
            color: #e0e0e0;
        }

        .dark-mode .main-header {
            background-color: #252525;
            border-bottom-color: #333;
        }

        .dark-mode .text-gray-900 {
            color: #e0e0e0;
        }

        .dark-mode .text-gray-600,
        .dark-mode .text-gray-500,
        .dark-mode .text-gray-700 {
            color: #aaa;
        }

        .dark-mode .bg-white {
            background-color: #252525;
        }

        .dark-mode .border-gray-200,
        .dark-mode .border-gray-100 {
            border-color: #333;
        }

        .dark-mode .shadow {
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3);
        }

        .dark-mode .bg-gray-50 {
            background-color: #1e1e1e;
        }

        .dark-mode .hover\:bg-gray-50:hover {
            background-color: #2a2a2a;
        }

        .dark-mode .hover\:bg-gray-100:hover {
            background-color: #333;
        }

        .dark-mode .model-category-btn.bg-white {
            background-color: #252525;
            border-color: #444;
            color: #ccc;
        }

        .dark-mode .model-category-btn.bg-white:hover {
            background-color: #333;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Cabeçalho Principal -->
    <header class="main-header py-3 px-6 shadow-sm">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-6">
                <a href="/" class="flex items-center">
                    <img src="/static/amigo-logo.png" alt="Amigo Logo" class="h-12 mr-2">
                    <div class="h-5 w-0.5 bg-blue-400 mx-1.5"></div>
                    <span class="text-xl font-bold text-gray-900">Data<span class="font-semibold">Studio</span></span>
                </a>

                <nav class="hidden md:flex space-x-1">
                    <a href="/" class="px-2 py-1 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-100">
                        <i class="fas fa-arrow-left mr-1"></i>Voltar para o DataHub
                    </a>
                </nav>
            </div>

            <div class="flex items-center space-x-3">
                <a href="/amigostudio-pro/feature-store" class="flex items-center justify-center border border-blue-400 text-blue-600 hover:bg-blue-50 px-2.5 py-1 rounded text-sm font-medium transition-all duration-200">
                    <i class="fas fa-database mr-1.5"></i> Feature Store
                </a>
                <!-- Switch de Tema -->
                <label class="theme-switch">
                    <input type="checkbox" id="theme-toggle">
                    <span class="slider"></span>
                </label>
                <span class="text-xs text-gray-600 ml-1"><i class="fas fa-moon"></i></span>
            </div>
        </div>
    </header>

    <main class="px-4 py-6">
        <!-- Título e Descrição -->
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Data Features Store</h1>
                <p class="text-gray-600">Gerencie features para análises avançadas e modelos de machine learning</p>
            </div>
            <div class="flex space-x-3">
                <button id="create-feature-btn" class="btn-primary px-4 py-2 rounded-lg">
                    <i class="fas fa-plus mr-2"></i>Nova Feature
                </button>
                <button id="create-model-btn" class="btn-secondary px-4 py-2 rounded-lg">
                    <i class="fas fa-robot mr-2"></i>Novo Modelo
                </button>
            </div>
        </div>

        <!-- Abas Principais -->
        <div class="mb-6">
            <div class="border-b border-gray-200">
                <nav class="flex -mb-px">
                    <button id="hero-tab-btn" class="tab-btn tab-active px-4 py-2 text-sm font-medium border-b-2 border-blue-600">
                        <i class="fas fa-star mr-1"></i> Destaques
                    </button>
                    <button id="features-tab-btn" class="tab-btn px-4 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300 border-b-2 border-transparent">
                        <i class="fas fa-database mr-1"></i> Features
                    </button>
                    <button id="models-tab-btn" class="tab-btn px-4 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300 border-b-2 border-transparent">
                        <i class="fas fa-robot mr-1"></i> Modelos ML
                    </button>
                    <button id="toolbox-tab-btn" class="tab-btn px-4 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300 border-b-2 border-transparent">
                        <i class="fas fa-briefcase-medical mr-1"></i> Maleta de Ferramentas
                    </button>
                </nav>
            </div>
        </div>

        <!-- Conteúdo das Abas -->
        <!-- 0. Aba de Destaques (Hero) -->
        <div id="hero-tab" class="tab-content active">
            <div class="bg-gradient-to-br from-blue-200 via-blue-100 to-gray-100 rounded-xl overflow-hidden shadow-lg mb-8">
                <div class="relative">
                    <!-- Overlay de padrão artístico -->
                    <div class="absolute inset-0 opacity-10" style="background-image: url('data:image/svg+xml,%3Csvg width=\'100\' height=\'100\' viewBox=\'0 0 100 100\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cpath d=\'M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z\' fill=\'%234B91F1\' fill-opacity=\'0.3\' fill-rule=\'evenodd\'/%3E%3C/svg%3E')"></div>

                    <!-- Elementos decorativos -->
                    <div class="absolute top-0 right-0 w-64 h-64 bg-blue-300 rounded-full filter blur-3xl opacity-20 -mr-32 -mt-32"></div>
                    <div class="absolute bottom-0 left-0 w-64 h-64 bg-gray-200 rounded-full filter blur-3xl opacity-20 -ml-32 -mb-32"></div>

                    <div class="px-8 py-12 relative z-10">
                        <div class="max-w-6xl mx-auto">
                            <div class="flex flex-col md:flex-row items-center justify-between mb-10">
                                <div class="mb-8 md:mb-0 md:mr-8 max-w-3xl text-center md:text-left">
                                    <div class="flex items-center justify-center md:justify-start mb-4">
                                        <img src="/static/amigo-logo.png" alt="Amigo Logo" class="h-12 mr-3">
                                        <h1 class="text-3xl md:text-4xl font-bold text-gray-800">Data Features Store</h1>
                                    </div>
                                    <p class="text-gray-600 text-lg">Potencialize suas análises com features avançadas e modelos de machine learning</p>
                                </div>
                                <div class="flex flex-wrap gap-3">
                                    <a href="#" onclick="switchTab('features'); return false;" class="bg-white text-blue-600 hover:bg-blue-50 px-5 py-2 rounded-lg font-medium transition-all duration-200 flex items-center shadow-md border border-blue-200">
                                        <i class="fas fa-database mr-2"></i> Ver Features
                                    </a>
                                    <a href="#" onclick="switchTab('models'); return false;" class="bg-blue-400 text-white hover:bg-blue-500 px-5 py-2 rounded-lg font-medium transition-all duration-200 flex items-center shadow-md">
                                        <i class="fas fa-robot mr-2"></i> Explorar Modelos
                                    </a>
                                </div>
                            </div>

                            <!-- Estatísticas em cards mais suaves -->
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                                <div class="bg-white bg-opacity-80 backdrop-blur-sm rounded-lg p-6 border border-blue-200 shadow-md">
                                    <div class="flex items-center">
                                        <div class="w-14 h-14 rounded-full bg-blue-100 flex items-center justify-center mr-4">
                                            <i class="fas fa-robot text-blue-500 text-2xl"></i>
                                        </div>
                                        <div>
                                            <div class="text-3xl font-bold text-blue-600 mb-1">25+</div>
                                            <div class="text-gray-600">Modelos Pré-treinados</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="bg-white bg-opacity-80 backdrop-blur-sm rounded-lg p-6 border border-blue-200 shadow-md">
                                    <div class="flex items-center">
                                        <div class="w-14 h-14 rounded-full bg-blue-100 flex items-center justify-center mr-4">
                                            <i class="fas fa-database text-blue-500 text-2xl"></i>
                                        </div>
                                        <div>
                                            <div class="text-3xl font-bold text-blue-600 mb-1">150+</div>
                                            <div class="text-gray-600">Features Disponíveis</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="bg-white bg-opacity-80 backdrop-blur-sm rounded-lg p-6 border border-blue-200 shadow-md">
                                    <div class="flex items-center">
                                        <div class="w-14 h-14 rounded-full bg-blue-100 flex items-center justify-center mr-4">
                                            <i class="fas fa-chart-line text-blue-500 text-2xl"></i>
                                        </div>
                                        <div>
                                            <div class="text-3xl font-bold text-blue-600 mb-1">92%</div>
                                            <div class="text-gray-600">Precisão Média</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Features mais utilizadas -->
            <div class="mb-10">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Features Mais Utilizadas</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-5 hover:shadow-md transition-all duration-200">
                        <div class="flex justify-between items-start mb-4">
                            <div>
                                <h3 class="font-semibold text-lg text-gray-900">Taxa de Ocupação</h3>
                                <p class="text-sm text-gray-600">Taxa de ocupação da agenda por período</p>
                            </div>
                            <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">agenda</span>
                        </div>
                        <div class="flex justify-between items-center mb-3">
                            <div class="flex flex-wrap gap-1">
                                <span class="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs">ocupação</span>
                                <span class="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs">agenda</span>
                            </div>
                            <span class="text-lg font-medium text-blue-600">78.5%</span>
                        </div>
                        <div class="flex items-center justify-between pt-3 border-t border-gray-100">
                            <div class="flex items-center">
                                <img src="/static/amigo-logo.png" alt="Amigo Tech" class="h-5 mr-2">
                                <span class="text-xs text-gray-500">Desenvolvido por Amigo Tech</span>
                            </div>
                            <span class="text-xs text-gray-500">Dr. Marcos Silva</span>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-5 hover:shadow-md transition-all duration-200">
                        <div class="flex justify-between items-start mb-4">
                            <div>
                                <h3 class="font-semibold text-lg text-gray-900">Lifetime Value</h3>
                                <p class="text-sm text-gray-600">Valor médio do paciente ao longo do tempo</p>
                            </div>
                            <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">financeiro</span>
                        </div>
                        <div class="flex justify-between items-center mb-3">
                            <div class="flex flex-wrap gap-1">
                                <span class="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs">valor</span>
                                <span class="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs">paciente</span>
                            </div>
                            <span class="text-lg font-medium text-green-600">R$ 3.250,00</span>
                        </div>
                        <div class="flex items-center justify-between pt-3 border-t border-gray-100">
                            <div class="flex items-center">
                                <img src="/static/amigo-logo.png" alt="Amigo Tech" class="h-5 mr-2">
                                <span class="text-xs text-gray-500">Verificado por Amigo Tech</span>
                            </div>
                            <span class="text-xs text-gray-500">Dra. Carla Mendes</span>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-5 hover:shadow-md transition-all duration-200">
                        <div class="flex justify-between items-start mb-4">
                            <div>
                                <h3 class="font-semibold text-lg text-gray-900">Taxa de Retorno</h3>
                                <p class="text-sm text-gray-600">Percentual de pacientes que retornam</p>
                            </div>
                            <span class="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs">paciente</span>
                        </div>
                        <div class="flex justify-between items-center mb-3">
                            <div class="flex flex-wrap gap-1">
                                <span class="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs">retenção</span>
                                <span class="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs">paciente</span>
                            </div>
                            <span class="text-lg font-medium text-purple-600">65.2%</span>
                        </div>
                        <div class="flex items-center justify-between pt-3 border-t border-gray-100">
                            <div class="flex items-center">
                                <img src="/static/amigo-logo.png" alt="Amigo Tech" class="h-5 mr-2">
                                <span class="text-xs text-gray-500">Desenvolvido por Amigo Tech</span>
                            </div>
                            <span class="text-xs text-gray-500">Dr. Paulo Oliveira</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modelos mais utilizados -->
            <div class="mb-10">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Modelos Mais Utilizados</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-all duration-200">
                        <div class="bg-blue-50 px-4 py-2 border-b border-gray-200">
                            <div class="flex justify-between items-center">
                                <div class="flex items-center">
                                    <span class="text-xs font-medium text-blue-600 mr-2">Preditivo</span>
                                    <div class="flex items-center">
                                        <img src="/static/amigo-logo.png" alt="Amigo Tech" class="h-4 mr-1">
                                        <span class="text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">Amigo Tech</span>
                                    </div>
                                </div>
                                <span class="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">Free</span>
                            </div>
                        </div>
                        <div class="p-4">
                            <h3 class="text-lg font-semibold text-gray-900 mb-1">Previsão de Demanda de Agendamentos</h3>
                            <p class="text-sm text-gray-600 mb-3">Modelo que prevê a demanda futura de agendamentos por especialidade</p>
                            <div class="flex items-center text-sm text-gray-500 mb-2">
                                <i class="fas fa-chart-line mr-2 text-blue-500"></i>
                                <span>Precisão: 88.7%</span>
                            </div>
                            <div class="flex items-center text-sm text-gray-500 mb-3">
                                <i class="fas fa-calendar-alt mr-2 text-blue-500"></i>
                                <span>Atualizado: 20/10/2023</span>
                            </div>
                            <div class="flex items-center text-xs text-gray-500 pt-2 border-t border-gray-100">
                                <i class="fas fa-user-md mr-1"></i>
                                <span>Desenvolvido por: Dr. André Martins</span>
                            </div>
                        </div>
                        <div class="bg-gray-50 px-4 py-3 border-t border-gray-200">
                            <button class="w-full bg-blue-600 hover:bg-blue-700 text-white py-1.5 px-3 rounded text-sm font-medium">
                                Utilizar Modelo
                            </button>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-all duration-200">
                        <div class="bg-purple-50 px-4 py-2 border-b border-gray-200">
                            <div class="flex justify-between items-center">
                                <div class="flex items-center">
                                    <span class="text-xs font-medium text-purple-600 mr-2">Segmentação</span>
                                    <div class="flex items-center">
                                        <img src="/static/amigo-logo.png" alt="Amigo Tech" class="h-4 mr-1">
                                        <span class="text-xs bg-purple-100 text-purple-800 px-2 py-0.5 rounded-full">Verificado</span>
                                    </div>
                                </div>
                                <span class="text-xs bg-yellow-100 text-yellow-800 px-2 py-0.5 rounded-full">Premium</span>
                            </div>
                        </div>
                        <div class="p-4">
                            <h3 class="text-lg font-semibold text-gray-900 mb-1">Segmentação de Pacientes</h3>
                            <p class="text-sm text-gray-600 mb-3">Modelo que segmenta pacientes com base em padrões de uso e características</p>
                            <div class="flex items-center text-sm text-gray-500 mb-2">
                                <i class="fas fa-chart-line mr-2 text-purple-500"></i>
                                <span>Precisão: 90.1%</span>
                            </div>
                            <div class="flex items-center text-sm text-gray-500 mb-3">
                                <i class="fas fa-calendar-alt mr-2 text-purple-500"></i>
                                <span>Atualizado: 15/09/2023</span>
                            </div>
                            <div class="flex items-center text-xs text-gray-500 pt-2 border-t border-gray-100">
                                <i class="fas fa-user-md mr-1"></i>
                                <span>Desenvolvido por: Dra. Renata Almeida</span>
                            </div>
                        </div>
                        <div class="bg-gray-50 px-4 py-3 border-t border-gray-200">
                            <button class="w-full bg-purple-600 hover:bg-purple-700 text-white py-1.5 px-3 rounded text-sm font-medium">
                                Utilizar Modelo
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 1. Aba de Features -->
        <div id="features-tab" class="tab-content hidden">
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                <!-- Painel de Navegação -->
                <div class="lg:col-span-1">
                    <div class="bg-white p-4 rounded-lg shadow">
                        <div class="relative mb-4">
                            <input type="text" id="search-input" placeholder="Buscar features..." class="w-full border rounded-lg px-3 py-2 pl-10">
                            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                        </div>

                        <h2 class="text-lg font-semibold mb-3">Domínios</h2>
                        <ul class="space-y-1 mb-4" id="domain-list">
                            <li><a href="#" class="domain-link block p-2 rounded hover:bg-blue-50 sidebar-active" data-domain="all">Todos os Domínios</a></li>
                            <li><a href="#" class="domain-link block p-2 rounded hover:bg-blue-50" data-domain="equipamentos">Equipamentos Médicos</a></li>
                            <li><a href="#" class="domain-link block p-2 rounded hover:bg-blue-50" data-domain="agenda">Agenda</a></li>
                            <li><a href="#" class="domain-link block p-2 rounded hover:bg-blue-50" data-domain="financeiro">Financeiro</a></li>
                            <li><a href="#" class="domain-link block p-2 rounded hover:bg-blue-50" data-domain="paciente">Paciente</a></li>
                            <li><a href="#" class="domain-link block p-2 rounded hover:bg-blue-50" data-domain="amigocare">AmigoCare+</a></li>
                            <li><a href="#" class="domain-link block p-2 rounded hover:bg-blue-50" data-domain="visao360">Visão 360</a></li>
                        </ul>

                        <h2 class="text-lg font-semibold mb-3">Categorias</h2>
                        <div class="flex flex-wrap gap-2 mb-4" id="tag-list">
                            <span class="tag-badge px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs cursor-pointer">equipamentos</span>
                            <span class="tag-badge px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs cursor-pointer">diagnóstico</span>
                            <span class="tag-badge px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs cursor-pointer">monitoramento</span>
                            <span class="tag-badge px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs cursor-pointer">eficiência</span>
                            <span class="tag-badge px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs cursor-pointer">manutenção</span>
                            <span class="tag-badge px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs cursor-pointer">volume</span>
                            <span class="tag-badge px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs cursor-pointer">receita</span>
                            <span class="tag-badge px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs cursor-pointer">despesa</span>
                            <span class="tag-badge px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs cursor-pointer">resultado</span>
                            <span class="tag-badge px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs cursor-pointer">engajamento</span>
                            <span class="tag-badge px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs cursor-pointer">satisfação</span>
                        </div>

                        <h2 class="text-lg font-semibold mb-3">Tipos de Feature</h2>
                        <ul class="space-y-1" id="type-list">
                            <li><a href="#" class="type-link block p-2 rounded hover:bg-blue-50" data-type="all">Todos os Tipos</a></li>
                            <li><a href="#" class="type-link block p-2 rounded hover:bg-blue-50" data-type="numérico">Numérico</a></li>
                            <li><a href="#" class="type-link block p-2 rounded hover:bg-blue-50" data-type="categórico">Categórico</a></li>
                            <li><a href="#" class="type-link block p-2 rounded hover:bg-blue-50" data-type="temporal">Temporal</a></li>
                            <li><a href="#" class="type-link block p-2 rounded hover:bg-blue-50" data-type="texto">Texto</a></li>
                        </ul>
                    </div>
                </div>

                <!-- Lista de Features -->
                <div class="lg:col-span-3">
                    <div class="bg-white p-4 rounded-lg shadow">
                        <h2 class="text-xl font-semibold mb-4">Features Disponíveis</h2>
                        <div id="features-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <!-- Features serão inseridas aqui via JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 2. Aba de Modelos ML -->
        <div id="models-tab" class="tab-content hidden">
            <div class="mb-6">
                <h1 class="text-2xl font-bold text-gray-900 mb-2">Modelos de Machine Learning</h1>
                <p class="text-gray-600">Explore e utilize modelos pré-treinados para análise avançada de dados</p>
            </div>

            <!-- Categorias de Modelos -->
            <div class="flex flex-wrap gap-3 mb-8">
                <button class="model-category-btn bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium">
                    Todos os Modelos
                </button>
                <button class="model-category-btn bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-50">
                    Modelos Preditivos
                </button>
                <button class="model-category-btn bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-50">
                    Segmentação e Clusterização
                </button>
                <button class="model-category-btn bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-50">
                    Modelos de Recomendação
                </button>
                <button class="model-category-btn bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-50">
                    Detecção de Anomalias
                </button>
                <button class="model-category-btn bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-50">
                    Séries Temporais
                </button>
            </div>

            <div class="grid grid-cols-1 gap-8">
                <!-- Modelos para Gestão Clínica -->
                <div>
                    <div class="flex items-center mb-4">
                        <div class="w-1 h-6 bg-purple-600 mr-3"></div>
                        <h2 class="text-xl font-bold text-gray-900">Modelos para Gestão Clínica</h2>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" id="clinic-models-container">
                        <!-- Modelos serão inseridos aqui via JavaScript -->

                        <!-- Modelo: Segmentação de Pacientes -->
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-all duration-200">
                            <div class="bg-purple-50 px-4 py-2 border-b border-gray-200">
                                <div class="flex justify-between items-center">
                                    <div class="flex items-center">
                                        <span class="text-xs font-medium text-purple-600 mr-2">Clustering</span>
                                        <div class="flex items-center">
                                            <img src="/static/amigo-logo.png" alt="Amigo Tech" class="h-4 mr-1">
                                            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">Amigo Tech</span>
                                        </div>
                                    </div>
                                    <span class="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">Ativo</span>
                                </div>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold text-gray-900 mb-1">Segmentação de Pacientes</h3>
                                <p class="text-sm text-gray-600 mb-3">Modelo que segmenta pacientes com base em padrões de uso e características</p>
                                <div class="flex items-center text-sm text-gray-500 mb-2">
                                    <i class="fas fa-chart-line mr-2 text-purple-500"></i>
                                    <span>Precisão: 90.1%</span>
                                </div>
                                <div class="flex flex-wrap gap-1 mb-3">
                                    <span class="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs">total_pacientes</span>
                                    <span class="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs">taxa_retorno</span>
                                </div>
                                <div class="flex items-center text-xs text-gray-500 pt-2 border-t border-gray-100">
                                    <div class="flex items-center">
                                        <img src="/static/amigo-logo.png" alt="Amigo Tech" class="h-4 mr-1">
                                        <span>Desenvolvido por: Dra. Renata Almeida</span>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-gray-50 px-4 py-3 border-t border-gray-200">
                                <button class="w-full bg-purple-600 hover:bg-purple-700 text-white py-1.5 px-3 rounded text-sm font-medium">
                                    Configurar
                                </button>
                            </div>
                        </div>

                        <!-- Modelo: Previsão de Demanda de Agendamentos -->
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-all duration-200">
                            <div class="bg-purple-50 px-4 py-2 border-b border-gray-200">
                                <div class="flex justify-between items-center">
                                    <div class="flex items-center">
                                        <span class="text-xs font-medium text-purple-600 mr-2">Série Temporal</span>
                                        <div class="flex items-center">
                                            <div class="w-4 h-4 rounded-full bg-blue-100 flex items-center justify-center mr-1">
                                                <i class="fas fa-check-circle text-blue-600 text-xs"></i>
                                            </div>
                                            <span class="text-xs bg-purple-100 text-purple-800 px-2 py-0.5 rounded-full">Verificado</span>
                                        </div>
                                    </div>
                                    <span class="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">Ativo</span>
                                </div>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold text-gray-900 mb-1">Previsão de Demanda de Agendamentos</h3>
                                <p class="text-sm text-gray-600 mb-3">Modelo que prevê a demanda futura de agendamentos por especialidade</p>
                                <div class="flex items-center text-sm text-gray-500 mb-2">
                                    <i class="fas fa-chart-line mr-2 text-purple-500"></i>
                                    <span>Precisão: 88.7%</span>
                                </div>
                                <div class="flex flex-wrap gap-1 mb-3">
                                    <span class="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs">total_agendamentos</span>
                                    <span class="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs">taxa_ocupacao</span>
                                </div>
                                <div class="flex items-center text-xs text-gray-500 pt-2 border-t border-gray-100">
                                    <div class="flex items-center">
                                        <span>Verificado por: Amigo Tech</span>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-gray-50 px-4 py-3 border-t border-gray-200">
                                <button class="w-full bg-purple-600 hover:bg-purple-700 text-white py-1.5 px-3 rounded text-sm font-medium">
                                    Configurar
                                </button>
                            </div>
                        </div>

                        <!-- Modelo: Previsão de Receita -->
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-all duration-200">
                            <div class="bg-purple-50 px-4 py-2 border-b border-gray-200">
                                <div class="flex justify-between items-center">
                                    <div class="flex items-center">
                                        <span class="text-xs font-medium text-purple-600 mr-2">Série Temporal</span>
                                        <div class="flex items-center">
                                            <img src="/static/amigo-logo.png" alt="Amigo Tech" class="h-4 mr-1">
                                            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">Amigo Tech</span>
                                        </div>
                                    </div>
                                    <span class="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">Ativo</span>
                                </div>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold text-gray-900 mb-1">Previsão de Receita</h3>
                                <p class="text-sm text-gray-600 mb-3">Modelo que prevê receita futura com base em tendências históricas</p>
                                <div class="flex items-center text-sm text-gray-500 mb-2">
                                    <i class="fas fa-chart-line mr-2 text-purple-500"></i>
                                    <span>Precisão: 87.5%</span>
                                </div>
                                <div class="flex flex-wrap gap-1 mb-3">
                                    <span class="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs">total_receita</span>
                                    <span class="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs">total_despesa</span>
                                    <span class="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs">resultado_liquido</span>
                                </div>
                                <div class="flex items-center text-xs text-gray-500 pt-2 border-t border-gray-100">
                                    <div class="flex items-center">
                                        <img src="/static/amigo-logo.png" alt="Amigo Tech" class="h-4 mr-1">
                                        <span>Desenvolvido por: Dr. Marcos Silva</span>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-gray-50 px-4 py-3 border-t border-gray-200">
                                <button class="w-full bg-purple-600 hover:bg-purple-700 text-white py-1.5 px-3 rounded text-sm font-medium">
                                    Configurar
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Modelos para Equipamentos Médicos -->
                <div>
                    <div class="flex items-center mb-4">
                        <div class="w-1 h-6 bg-blue-600 mr-3"></div>
                        <h2 class="text-xl font-bold text-gray-900">Modelos para Equipamentos Médicos</h2>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" id="medical-models-container">
                        <!-- Modelos serão inseridos aqui via JavaScript -->

                        <!-- Modelo: Previsão de Falhas em Equipamentos -->
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-all duration-200">
                            <div class="bg-blue-50 px-4 py-2 border-b border-gray-200">
                                <div class="flex justify-between items-center">
                                    <div class="flex items-center">
                                        <span class="text-xs font-medium text-blue-600 mr-2">Classificação</span>
                                        <div class="flex items-center">
                                            <img src="/static/amigo-logo.png" alt="Amigo Tech" class="h-4 mr-1">
                                            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">Amigo Tech</span>
                                        </div>
                                    </div>
                                    <span class="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">Ativo</span>
                                </div>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold text-gray-900 mb-1">Previsão de Falhas em Equipamentos</h3>
                                <p class="text-sm text-gray-600 mb-3">Modelo que prevê falhas em equipamentos médicos com base em padrões de uso e leituras de sensores</p>
                                <div class="flex items-center text-sm text-gray-500 mb-2">
                                    <i class="fas fa-chart-line mr-2 text-blue-500"></i>
                                    <span>Precisão: 94.5%</span>
                                </div>
                                <div class="flex flex-wrap gap-1 mb-3">
                                    <span class="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs">mtbf</span>
                                    <span class="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs">uptime_equipamento</span>
                                    <span class="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs">desvio_padrao_leituras</span>
                                </div>
                                <div class="flex items-center text-xs text-gray-500 pt-2 border-t border-gray-100">
                                    <div class="flex items-center">
                                        <img src="/static/amigo-logo.png" alt="Amigo Tech" class="h-4 mr-1">
                                        <span>Desenvolvido por: Dr. Ricardo Santos</span>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-gray-50 px-4 py-3 border-t border-gray-200">
                                <button class="w-full bg-blue-600 hover:bg-blue-700 text-white py-1.5 px-3 rounded text-sm font-medium">
                                    Configurar
                                </button>
                            </div>
                        </div>

                        <!-- Modelo: Otimização de Calibração -->
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-all duration-200">
                            <div class="bg-blue-50 px-4 py-2 border-b border-gray-200">
                                <div class="flex justify-between items-center">
                                    <div class="flex items-center">
                                        <span class="text-xs font-medium text-blue-600 mr-2">Regressão</span>
                                        <div class="flex items-center">
                                            <div class="w-4 h-4 rounded-full bg-blue-100 flex items-center justify-center mr-1">
                                                <i class="fas fa-check-circle text-blue-600 text-xs"></i>
                                            </div>
                                            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">Verificado</span>
                                        </div>
                                    </div>
                                    <span class="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">Ativo</span>
                                </div>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold text-gray-900 mb-1">Otimização de Calibração</h3>
                                <p class="text-sm text-gray-600 mb-3">Modelo que determina o momento ideal para calibração de equipamentos de diagnóstico</p>
                                <div class="flex items-center text-sm text-gray-500 mb-2">
                                    <i class="fas fa-chart-line mr-2 text-blue-500"></i>
                                    <span>Precisão: 91.2%</span>
                                </div>
                                <div class="flex flex-wrap gap-1 mb-3">
                                    <span class="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs">intervalo_calibracao</span>
                                    <span class="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs">precisao_diagnostico</span>
                                    <span class="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs">desvio_padrao_leituras</span>
                                </div>
                                <div class="flex items-center text-xs text-gray-500 pt-2 border-t border-gray-100">
                                    <div class="flex items-center">
                                        <span>Verificado por: Amigo Tech</span>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-gray-50 px-4 py-3 border-t border-gray-200">
                                <button class="w-full bg-blue-600 hover:bg-blue-700 text-white py-1.5 px-3 rounded text-sm font-medium">
                                    Configurar
                                </button>
                            </div>
                        </div>

                        <!-- Modelo: Detecção de Anomalias em Exames -->
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-all duration-200">
                            <div class="bg-blue-50 px-4 py-2 border-b border-gray-200">
                                <div class="flex justify-between items-center">
                                    <div class="flex items-center">
                                        <span class="text-xs font-medium text-blue-600 mr-2">Detecção de Anomalias</span>
                                        <div class="flex items-center">
                                            <img src="/static/amigo-logo.png" alt="Amigo Tech" class="h-4 mr-1">
                                            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">Amigo Tech</span>
                                        </div>
                                    </div>
                                    <span class="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">Ativo</span>
                                </div>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold text-gray-900 mb-1">Detecção de Anomalias em Exames</h3>
                                <p class="text-sm text-gray-600 mb-3">Modelo que identifica anomalias em resultados de exames para reduzir falsos negativos</p>
                                <div class="flex items-center text-sm text-gray-500 mb-2">
                                    <i class="fas fa-chart-line mr-2 text-blue-500"></i>
                                    <span>Precisão: 96.8%</span>
                                </div>
                                <div class="flex flex-wrap gap-1 mb-3">
                                    <span class="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs">precisao_diagnostico</span>
                                    <span class="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs">taxa_falsos_positivos</span>
                                </div>
                                <div class="flex items-center text-xs text-gray-500 pt-2 border-t border-gray-100">
                                    <div class="flex items-center">
                                        <img src="/static/amigo-logo.png" alt="Amigo Tech" class="h-4 mr-1">
                                        <span>Desenvolvido por: Dra. Ana Beatriz Costa</span>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-gray-50 px-4 py-3 border-t border-gray-200">
                                <button class="w-full bg-blue-600 hover:bg-blue-700 text-white py-1.5 px-3 rounded text-sm font-medium">
                                    Configurar
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Modelos para Análise de Equipamentos -->
                <div>
                    <div class="flex items-center mb-4">
                        <div class="w-1 h-6 bg-green-600 mr-3"></div>
                        <h2 class="text-xl font-bold text-gray-900">Modelos para Análise de Equipamentos</h2>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" id="equipment-analysis-container">
                        <!-- Modelos serão inseridos aqui via JavaScript -->

                        <!-- Modelo: Previsão de Vida Útil de Equipamentos -->
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-all duration-200">
                            <div class="bg-green-50 px-4 py-2 border-b border-gray-200">
                                <div class="flex justify-between items-center">
                                    <div class="flex items-center">
                                        <span class="text-xs font-medium text-green-600 mr-2">Regressão</span>
                                        <div class="flex items-center">
                                            <img src="/static/amigo-logo.png" alt="Amigo Tech" class="h-4 mr-1">
                                            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">Amigo Tech</span>
                                        </div>
                                    </div>
                                    <span class="text-xs bg-yellow-100 text-yellow-800 px-2 py-0.5 rounded-full">Em Treinamento</span>
                                </div>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold text-gray-900 mb-1">Previsão de Vida Útil de Equipamentos</h3>
                                <p class="text-sm text-gray-600 mb-3">Modelo que estima a vida útil restante de equipamentos médicos</p>
                                <div class="flex items-center text-sm text-gray-500 mb-2">
                                    <i class="fas fa-chart-line mr-2 text-green-500"></i>
                                    <span>Precisão: 89.5%</span>
                                </div>
                                <div class="flex flex-wrap gap-1 mb-3">
                                    <span class="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs">mtbf</span>
                                    <span class="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs">mttr</span>
                                    <span class="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs">custo_manutencao_anual</span>
                                </div>
                                <div class="flex items-center text-xs text-gray-500 pt-2 border-t border-gray-100">
                                    <div class="flex items-center">
                                        <img src="/static/amigo-logo.png" alt="Amigo Tech" class="h-4 mr-1">
                                        <span>Desenvolvido por: Eng. Mariana Costa</span>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-gray-50 px-4 py-3 border-t border-gray-200">
                                <button class="w-full bg-green-600 hover:bg-green-700 text-white py-1.5 px-3 rounded text-sm font-medium">
                                    Configurar
                                </button>
                            </div>
                        </div>

                        <!-- Modelo: Otimização de Manutenção Preventiva -->
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-all duration-200">
                            <div class="bg-green-50 px-4 py-2 border-b border-gray-200">
                                <div class="flex justify-between items-center">
                                    <div class="flex items-center">
                                        <span class="text-xs font-medium text-green-600 mr-2">Otimização</span>
                                        <div class="flex items-center">
                                            <div class="w-4 h-4 rounded-full bg-green-100 flex items-center justify-center mr-1">
                                                <i class="fas fa-check-circle text-green-600 text-xs"></i>
                                            </div>
                                            <span class="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">Verificado</span>
                                        </div>
                                    </div>
                                    <span class="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">Ativo</span>
                                </div>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold text-gray-900 mb-1">Otimização de Manutenção Preventiva</h3>
                                <p class="text-sm text-gray-600 mb-3">Modelo que determina o cronograma ideal para manutenção preventiva</p>
                                <div class="flex items-center text-sm text-gray-500 mb-2">
                                    <i class="fas fa-chart-line mr-2 text-green-500"></i>
                                    <span>Precisão: 92.3%</span>
                                </div>
                                <div class="flex flex-wrap gap-1 mb-3">
                                    <span class="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs">mtbf</span>
                                    <span class="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs">mttr</span>
                                    <span class="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs">uptime_equipamento</span>
                                </div>
                                <div class="flex items-center text-xs text-gray-500 pt-2 border-t border-gray-100">
                                    <div class="flex items-center">
                                        <span>Verificado por: Amigo Tech</span>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-gray-50 px-4 py-3 border-t border-gray-200">
                                <button class="w-full bg-green-600 hover:bg-green-700 text-white py-1.5 px-3 rounded text-sm font-medium">
                                    Configurar
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 3. Aba de Maleta de Ferramentas -->
        <div id="toolbox-tab" class="tab-content hidden">
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                <!-- Categorias de Ferramentas -->
                <div class="lg:col-span-1">
                    <div class="bg-white p-4 rounded-lg shadow">
                        <h2 class="text-lg font-semibold mb-3">Categorias</h2>
                        <ul class="space-y-1" id="toolbox-categories">
                            <li><a href="#" class="toolbox-category block p-2 rounded hover:bg-blue-50 sidebar-active" data-category="all">Todas as Ferramentas</a></li>
                            <li><a href="#" class="toolbox-category block p-2 rounded hover:bg-blue-50" data-category="equipamentos">Equipamentos Médicos</a></li>
                            <li><a href="#" class="toolbox-category block p-2 rounded hover:bg-blue-50" data-category="financeiro">Análise Financeira</a></li>
                            <li><a href="#" class="toolbox-category block p-2 rounded hover:bg-blue-50" data-category="operacional">Eficiência Operacional</a></li>
                            <li><a href="#" class="toolbox-category block p-2 rounded hover:bg-blue-50" data-category="paciente">Jornada do Paciente</a></li>
                            <li><a href="#" class="toolbox-category block p-2 rounded hover:bg-blue-50" data-category="marketing">Marketing e Vendas</a></li>
                        </ul>
                    </div>
                </div>

                <!-- Lista de Ferramentas -->
                <div class="lg:col-span-3">
                    <div class="bg-white p-4 rounded-lg shadow">
                        <h2 class="text-xl font-semibold mb-4">Ferramentas Disponíveis</h2>
                        <div id="toolbox-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <!-- Ferramentas serão inseridas aqui via JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Detalhes da Feature (Modal) -->
    <div id="feature-details-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-50">
        <div class="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div class="flex justify-between items-start mb-4">
                <h2 class="text-2xl font-semibold" id="feature-detail-name">Nome da Feature</h2>
                <button id="close-details-btn" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-lg font-medium mb-2">Informações</h3>
                    <div class="space-y-2 bg-gray-50 p-4 rounded-lg">
                        <p><span class="font-medium">Descrição:</span> <span id="feature-detail-description"></span></p>
                        <p><span class="font-medium">Domínio:</span> <span id="feature-detail-domain"></span></p>
                        <p><span class="font-medium">Grupo:</span> <span id="feature-detail-group"></span></p>
                        <p><span class="font-medium">Tipo:</span> <span id="feature-detail-type"></span></p>
                        <p><span class="font-medium">Valor Atual:</span> <span id="feature-detail-value"></span></p>
                        <p><span class="font-medium">Tags:</span> <span id="feature-detail-tags"></span></p>
                    </div>

                    <h3 class="text-lg font-medium mt-4 mb-2">Uso em Modelos</h3>
                    <div id="feature-detail-models" class="bg-gray-50 p-4 rounded-lg">
                        <!-- Modelos serão inseridos aqui via JavaScript -->
                    </div>
                </div>

                <div>
                    <h3 class="text-lg font-medium mb-2">Estatísticas</h3>
                    <div id="feature-detail-stats" class="bg-gray-50 p-4 rounded-lg mb-4">
                        <!-- Estatísticas serão inseridas aqui via JavaScript -->
                    </div>

                    <h3 class="text-lg font-medium mb-2">Visualização</h3>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <div class="chart-container">
                            <canvas id="feature-chart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-6 flex space-x-4">
                <button id="update-feature-btn" class="btn-primary px-4 py-2 rounded-lg">
                    <i class="fas fa-edit mr-2"></i>Atualizar
                </button>
                <button id="export-feature-btn" class="btn-secondary px-4 py-2 rounded-lg">
                    <i class="fas fa-file-export mr-2"></i>Exportar
                </button>
                <button id="delete-feature-btn" class="btn-danger px-4 py-2 rounded-lg">
                    <i class="fas fa-trash mr-2"></i>Excluir
                </button>
            </div>
        </div>
    </div>

    <!-- Toast para notificações -->
    <div id="toast" class="hidden"></div>

    <!-- Scripts -->
    <script>
        // Variáveis globais
        let currentDomain = 'all';
        let currentTag = null;
        let currentType = 'all';
        let currentToolboxCategory = 'all';
        let currentModelCategory = 'all';
        let searchQuery = '';

        // Dados simulados para features de equipamentos médicos
        const equipmentFeaturesData = {
            "equipamentos_diagnostico": [
                { name: "precisao_diagnostico", description: "Precisão de diagnóstico do equipamento", domain: "equipamentos", type: "float", value: 97.8, tags: ["equipamentos", "diagnóstico", "precisão"] },
                { name: "tempo_processamento", description: "Tempo médio de processamento de exames", domain: "equipamentos", type: "float", value: 3.5, tags: ["equipamentos", "diagnóstico", "eficiência"] },
                { name: "taxa_falsos_positivos", description: "Taxa de falsos positivos", domain: "equipamentos", type: "float", value: 2.1, tags: ["equipamentos", "diagnóstico", "precisão"] }
            ],
            "equipamentos_monitoramento": [
                { name: "uptime_equipamento", description: "Tempo de funcionamento contínuo do equipamento", domain: "equipamentos", type: "float", value: 99.2, tags: ["equipamentos", "monitoramento", "eficiência"] },
                { name: "intervalo_calibracao", description: "Intervalo médio entre calibrações", domain: "equipamentos", type: "float", value: 45.0, tags: ["equipamentos", "monitoramento", "manutenção"] },
                { name: "desvio_padrao_leituras", description: "Desvio padrão das leituras do equipamento", domain: "equipamentos", type: "float", value: 0.8, tags: ["equipamentos", "monitoramento", "precisão"] }
            ],
            "equipamentos_manutencao": [
                { name: "mtbf", description: "Tempo médio entre falhas (MTBF)", domain: "equipamentos", type: "float", value: 8760.0, tags: ["equipamentos", "manutenção"] },
                { name: "mttr", description: "Tempo médio para reparo (MTTR)", domain: "equipamentos", type: "float", value: 4.5, tags: ["equipamentos", "manutenção"] },
                { name: "custo_manutencao_anual", description: "Custo anual de manutenção", domain: "equipamentos", type: "float", value: 12500.0, tags: ["equipamentos", "manutenção", "despesa"] }
            ]
        };

        // Dados simulados para features de gestão clínica
        const clinicFeaturesData = {
            "agenda_metrics": [
                { name: "total_agendamentos", description: "Total de agendamentos", domain: "agenda", type: "int", value: 1250, tags: ["agenda", "volume"] },
                { name: "taxa_ocupacao", description: "Taxa de ocupação da agenda", domain: "agenda", type: "float", value: 78.5, tags: ["agenda", "eficiência"] },
                { name: "tempo_medio_atendimento", description: "Tempo médio de atendimento", domain: "agenda", type: "float", value: 32.7, tags: ["agenda", "eficiência"] }
            ],
            "financeiro_metrics": [
                { name: "total_receita", description: "Total de receita", domain: "financeiro", type: "float", value: 125000.0, tags: ["financeiro", "receita"] },
                { name: "total_despesa", description: "Total de despesa", domain: "financeiro", type: "float", value: 87500.0, tags: ["financeiro", "despesa"] },
                { name: "resultado_liquido", description: "Resultado líquido", domain: "financeiro", type: "float", value: 37500.0, tags: ["financeiro", "resultado"] }
            ],
            "paciente_metrics": [
                { name: "total_pacientes", description: "Total de pacientes", domain: "paciente", type: "int", value: 3200, tags: ["paciente", "volume"] },
                { name: "taxa_retorno", description: "Taxa de retorno de pacientes", domain: "paciente", type: "float", value: 65.8, tags: ["paciente", "engajamento"] }
            ]
        };

        // Combinar todos os dados de features
        const allFeaturesData = {...equipmentFeaturesData, ...clinicFeaturesData};

        // Dados simulados para modelos de ML para equipamentos médicos
        const medicalModelsData = [
            {
                id: "model-1",
                name: "Previsão de Falhas em Equipamentos",
                description: "Modelo que prevê falhas em equipamentos médicos com base em padrões de uso e leituras de sensores",
                type: "Classificação",
                accuracy: 94.5,
                features: ["mtbf", "uptime_equipamento", "desvio_padrao_leituras"],
                lastUpdated: "2023-10-15",
                status: "Ativo"
            },
            {
                id: "model-2",
                name: "Otimização de Calibração",
                description: "Modelo que determina o momento ideal para calibração de equipamentos de diagnóstico",
                type: "Regressão",
                accuracy: 91.2,
                features: ["intervalo_calibracao", "precisao_diagnostico", "desvio_padrao_leituras"],
                lastUpdated: "2023-11-02",
                status: "Ativo"
            },
            {
                id: "model-3",
                name: "Detecção de Anomalias em Exames",
                description: "Modelo que identifica anomalias em resultados de exames para reduzir falsos negativos",
                type: "Detecção de Anomalias",
                accuracy: 96.8,
                features: ["precisao_diagnostico", "taxa_falsos_positivos"],
                lastUpdated: "2023-09-28",
                status: "Ativo"
            },
            {
                id: "model-4",
                name: "Previsão de Vida Útil de Equipamentos",
                description: "Modelo que estima a vida útil restante de equipamentos médicos",
                type: "Regressão",
                accuracy: 89.5,
                features: ["mtbf", "mttr", "custo_manutencao_anual"],
                lastUpdated: "2023-10-10",
                status: "Em Treinamento"
            },
            {
                id: "model-5",
                name: "Otimização de Manutenção Preventiva",
                description: "Modelo que determina o cronograma ideal para manutenção preventiva",
                type: "Otimização",
                accuracy: 92.3,
                features: ["mtbf", "mttr", "uptime_equipamento"],
                lastUpdated: "2023-11-15",
                status: "Ativo"
            }
        ];

        // Dados simulados para modelos de ML para gestão clínica
        const clinicModelsData = [
            {
                id: "model-6",
                name: "Previsão de Demanda de Agendamentos",
                description: "Modelo que prevê a demanda futura de agendamentos por especialidade",
                type: "Série Temporal",
                accuracy: 88.7,
                features: ["total_agendamentos", "taxa_ocupacao"],
                lastUpdated: "2023-10-20",
                status: "Ativo"
            },
            {
                id: "model-7",
                name: "Segmentação de Pacientes",
                description: "Modelo que segmenta pacientes com base em padrões de uso e características",
                type: "Clustering",
                accuracy: 90.1,
                features: ["total_pacientes", "taxa_retorno"],
                lastUpdated: "2023-09-15",
                status: "Ativo"
            },
            {
                id: "model-8",
                name: "Previsão de Receita",
                description: "Modelo que prevê receita futura com base em tendências históricas",
                type: "Série Temporal",
                accuracy: 87.5,
                features: ["total_receita", "total_despesa", "resultado_liquido"],
                lastUpdated: "2023-11-05",
                status: "Ativo"
            }
        ];

        // Dados simulados para a maleta de ferramentas
        const toolboxData = [
            // Ferramentas para Equipamentos Médicos
            {
                id: "tool-1",
                name: "Cálculo de Eficiência de Equipamentos",
                description: "Calcula a eficiência geral do equipamento (OEE) com base em disponibilidade, desempenho e qualidade",
                category: "equipamentos",
                prompt: "Calcular a eficiência geral do equipamento de ultrassom com disponibilidade de 95%, desempenho de 90% e qualidade de 98%",
                icon: "fa-calculator"
            },
            {
                id: "tool-2",
                name: "Análise de Custo-Benefício de Equipamentos",
                description: "Avalia o retorno sobre investimento (ROI) de equipamentos médicos",
                category: "equipamentos",
                prompt: "Analisar o ROI de um novo equipamento de raio-X com custo de R$ 150.000, vida útil de 8 anos e receita anual estimada de R$ 45.000",
                icon: "fa-chart-line"
            },
            {
                id: "tool-3",
                name: "Previsão de Manutenção",
                description: "Estima quando um equipamento precisará de manutenção com base em padrões de uso",
                category: "equipamentos",
                prompt: "Prever quando o equipamento de ressonância magnética precisará de manutenção com base nos últimos 12 meses de uso",
                icon: "fa-tools"
            },

            // Ferramentas para Análise Financeira
            {
                id: "tool-4",
                name: "Análise de Rentabilidade por Procedimento",
                description: "Calcula a rentabilidade de diferentes procedimentos médicos",
                category: "financeiro",
                prompt: "Analisar a rentabilidade dos 5 procedimentos mais realizados na clínica no último trimestre",
                icon: "fa-money-bill-wave"
            },
            {
                id: "tool-5",
                name: "Previsão de Fluxo de Caixa",
                description: "Projeta o fluxo de caixa futuro com base em tendências históricas",
                category: "financeiro",
                prompt: "Prever o fluxo de caixa para os próximos 6 meses com base nos dados dos últimos 2 anos",
                icon: "fa-chart-bar"
            },

            // Ferramentas para Eficiência Operacional
            {
                id: "tool-6",
                name: "Otimização de Agenda",
                description: "Sugere melhorias na agenda para maximizar a utilização",
                category: "operacional",
                prompt: "Identificar oportunidades de otimização na agenda da próxima semana para reduzir tempos ociosos",
                icon: "fa-calendar-alt"
            },
            {
                id: "tool-7",
                name: "Análise de Gargalos",
                description: "Identifica gargalos nos processos operacionais da clínica",
                category: "operacional",
                prompt: "Analisar os principais gargalos no fluxo de atendimento de pacientes",
                icon: "fa-hourglass-half"
            },

            // Ferramentas para Jornada do Paciente
            {
                id: "tool-8",
                name: "Análise de Satisfação",
                description: "Analisa dados de satisfação dos pacientes para identificar áreas de melhoria",
                category: "paciente",
                prompt: "Analisar os dados de satisfação dos pacientes dos últimos 3 meses e identificar principais pontos de melhoria",
                icon: "fa-smile"
            },
            {
                id: "tool-9",
                name: "Previsão de Abandono",
                description: "Identifica pacientes com risco de abandono de tratamento",
                category: "paciente",
                prompt: "Identificar pacientes com alto risco de abandono de tratamento nos próximos 30 dias",
                icon: "fa-user-minus"
            },

            // Ferramentas para Marketing e Vendas
            {
                id: "tool-10",
                name: "Análise de Conversão de Leads",
                description: "Avalia a eficácia das campanhas de marketing na conversão de leads",
                category: "marketing",
                prompt: "Analisar a taxa de conversão das últimas 3 campanhas de marketing e identificar fatores de sucesso",
                icon: "fa-bullseye"
            },
            {
                id: "tool-11",
                name: "Segmentação de Clientes",
                description: "Segmenta a base de clientes para campanhas direcionadas",
                category: "marketing",
                prompt: "Segmentar a base de clientes em 5 grupos com base em valor, frequência e recência",
                icon: "fa-users"
            }
        ];

        // Função para mostrar toast
        function showToast(message, type = 'info') {
            const toast = document.getElementById('toast');
            toast.textContent = message;
            toast.className = 'toast';

            if (type === 'success') {
                toast.classList.add('bg-green-500', 'text-white');
            } else if (type === 'error') {
                toast.classList.add('bg-red-500', 'text-white');
            } else {
                toast.classList.add('bg-blue-500', 'text-white');
            }

            toast.classList.remove('hidden');

            // Esconder o toast após 3 segundos
            setTimeout(() => {
                toast.classList.add('hidden');
            }, 3000);
        }

        // Função para alternar entre abas
        function switchTab(tabName) {
            const tabs = document.querySelectorAll('.tab-content');
            const buttons = document.querySelectorAll('.tab-btn');

            tabs.forEach(tab => tab.classList.remove('active'));
            buttons.forEach(button => {
                button.classList.remove('tab-active');
                button.classList.add('text-gray-500', 'border-transparent');
            });

            document.getElementById(`${tabName}-tab`).classList.add('active');
            document.getElementById(`${tabName}-tab-btn`).classList.add('tab-active');
            document.getElementById(`${tabName}-tab-btn`).classList.remove('text-gray-500', 'border-transparent');
        }

        // Função para formatar valores
        function formatValue(value, type) {
            if (type === 'float') return value.toFixed(2);
            if (type === 'int') return value.toLocaleString();
            return value;
        }

        // Função para obter classe de cor com base no valor
        function getValueColorClass(value, type) {
            if (type === 'float' || type === 'int') {
                if (value > 0) return 'text-green-600';
                if (value < 0) return 'text-red-600';
            }
            return 'text-gray-800';
        }

        // Função para renderizar as features
        function renderFeatures() {
            const container = document.getElementById('features-container');
            container.innerHTML = '';

            // Filtrar features por domínio, tag, tipo e busca
            const filteredGroups = {};

            Object.entries(allFeaturesData).forEach(([groupName, features]) => {
                const filteredFeatures = features.filter(feature => {
                    const domainMatch = currentDomain === 'all' || feature.domain === currentDomain;
                    const tagMatch = !currentTag || feature.tags.includes(currentTag);
                    const typeMatch = currentType === 'all' || mapFeatureType(feature.type) === currentType;
                    const searchMatch = !searchQuery ||
                        feature.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                        feature.description.toLowerCase().includes(searchQuery.toLowerCase());

                    return domainMatch && tagMatch && typeMatch && searchMatch;
                });

                if (filteredFeatures.length > 0) {
                    filteredGroups[groupName] = filteredFeatures;
                }
            });

            // Renderizar cards para cada feature
            Object.entries(filteredGroups).forEach(([groupName, features]) => {
                features.forEach(feature => {
                    const card = document.createElement('div');
                    card.className = 'feature-card bg-white p-4 rounded-lg shadow border border-gray-200 hover:shadow-md';
                    card.innerHTML = `
                        <div class="flex justify-between items-center mb-2">
                            <h3 class="font-semibold text-gray-900">${feature.name}</h3>
                            <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">${feature.domain}</span>
                        </div>
                        <p class="text-sm text-gray-600 mb-3">${feature.description}</p>
                        <div class="flex justify-between items-center">
                            <div class="flex flex-wrap gap-1">
                                ${feature.tags.map(tag => `
                                    <span class="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs">${tag}</span>
                                `).join('')}
                            </div>
                            <span class="text-sm font-medium ${getValueColorClass(feature.value, feature.type)}">${formatValue(feature.value, feature.type)}</span>
                        </div>
                    `;

                    // Adicionar evento de clique para mostrar detalhes
                    card.addEventListener('click', () => {
                        showFeatureDetails(groupName, feature);
                    });

                    container.appendChild(card);
                });
            });

            // Mostrar mensagem se não houver features
            if (container.children.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-8 text-gray-500">
                        <i class="fas fa-search text-4xl mb-3"></i>
                        <p>Nenhuma feature encontrada com os filtros atuais.</p>
                    </div>
                `;
            }
        }

        // Função para mapear tipo de feature
        function mapFeatureType(type) {
            if (type === 'float' || type === 'int') return 'numérico';
            if (type === 'string') return 'texto';
            if (type === 'boolean') return 'categórico';
            return type;
        }

        // Função para mostrar detalhes da feature
        function showFeatureDetails(groupName, feature) {
            document.getElementById('feature-detail-name').textContent = feature.name;
            document.getElementById('feature-detail-description').textContent = feature.description;
            document.getElementById('feature-detail-domain').textContent = feature.domain;
            document.getElementById('feature-detail-group').textContent = groupName;
            document.getElementById('feature-detail-type').textContent = feature.type;
            document.getElementById('feature-detail-value').textContent = formatValue(feature.value, feature.type);
            document.getElementById('feature-detail-tags').textContent = feature.tags.join(', ');

            // Estatísticas simuladas
            const statsContainer = document.getElementById('feature-detail-stats');
            statsContainer.innerHTML = `
                <p><span class="font-medium">Mínimo:</span> ${formatValue(feature.value * 0.8, feature.type)}</p>
                <p><span class="font-medium">Máximo:</span> ${formatValue(feature.value * 1.2, feature.type)}</p>
                <p><span class="font-medium">Média:</span> ${formatValue(feature.value, feature.type)}</p>
                <p><span class="font-medium">Desvio Padrão:</span> ${formatValue(feature.value * 0.1, feature.type)}</p>
                <p><span class="font-medium">Mediana:</span> ${formatValue(feature.value * 0.95, feature.type)}</p>
                <p><span class="font-medium">Quartil 1:</span> ${formatValue(feature.value * 0.85, feature.type)}</p>
                <p><span class="font-medium">Quartil 3:</span> ${formatValue(feature.value * 1.1, feature.type)}</p>
            `;

            // Modelos que usam a feature
            const modelsContainer = document.getElementById('feature-detail-models');
            const modelsUsingFeature = [...medicalModelsData, ...clinicModelsData].filter(
                model => model.features.includes(feature.name)
            );

            if (modelsUsingFeature.length > 0) {
                modelsContainer.innerHTML = `
                    <p>Esta feature é usada em ${modelsUsingFeature.length} modelo(s):</p>
                    <ul class="list-disc pl-5 mt-2">
                        ${modelsUsingFeature.map(model => `
                            <li>${model.name} (${model.type})</li>
                        `).join('')}
                    </ul>
                `;
            } else {
                modelsContainer.innerHTML = `
                    <p>Esta feature ainda não é usada em nenhum modelo.</p>
                `;
            }

            // Renderizar gráfico
            renderFeatureChart(feature);

            // Mostrar modal
            document.getElementById('feature-details-modal').classList.remove('hidden');
        }

        // Função para renderizar gráfico da feature
        function renderFeatureChart(feature) {
            const ctx = document.getElementById('feature-chart').getContext('2d');

            // Limpar gráfico anterior
            if (window.featureChart) {
                window.featureChart.destroy();
            }

            // Dados simulados para o gráfico
            const labels = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'];
            const baseValue = feature.value;
            const data = labels.map((_, i) => {
                // Gerar valores simulados com tendência e variação
                const trend = i / 11 * 0.2; // Tendência de crescimento
                const variation = Math.sin(i) * 0.1; // Variação sazonal
                const random = (Math.random() - 0.5) * 0.1; // Ruído aleatório
                return baseValue * (1 + trend + variation + random);
            });

            // Criar gráfico
            window.featureChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: feature.name,
                        data: data,
                        backgroundColor: 'rgba(0, 122, 255, 0.2)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 2,
                        tension: 0.3,
                        pointRadius: 3
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: `Evolução de ${feature.name} (Últimos 12 meses)`
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: false
                        }
                    }
                }
            });
        }

        // Função para renderizar modelos
        function renderModels() {
            // Renderizar modelos para equipamentos médicos
            const medicalContainer = document.getElementById('medical-models-container');
            medicalContainer.innerHTML = '';

            medicalModelsData.forEach(model => {
                const card = document.createElement('div');
                card.className = 'model-card bg-white p-4 rounded-lg shadow border border-gray-200 hover:shadow-md';
                card.innerHTML = `
                    <div class="flex justify-between items-center mb-2">
                        <h3 class="font-semibold text-gray-900">${model.name}</h3>
                        <span class="px-2 py-1 ${model.status === 'Ativo' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'} rounded-full text-xs">${model.status}</span>
                    </div>
                    <p class="text-sm text-gray-600 mb-3">${model.description}</p>
                    <div class="flex justify-between items-center mb-3">
                        <span class="text-sm text-gray-500">Tipo: ${model.type}</span>
                        <span class="text-sm font-medium text-blue-600">Precisão: ${model.accuracy}%</span>
                    </div>
                    <div>
                        <p class="text-xs text-gray-500 mb-1">Features utilizadas:</p>
                        <div class="flex flex-wrap gap-1">
                            ${model.features.map(feature => `
                                <span class="px-2 py-0.5 bg-blue-50 text-blue-600 rounded-full text-xs">${feature}</span>
                            `).join('')}
                        </div>
                    </div>
                    <div class="mt-3 pt-3 border-t border-gray-100 flex justify-end">
                        <button class="text-xs text-blue-600 hover:text-blue-800">
                            <i class="fas fa-cog mr-1"></i>Configurar
                        </button>
                    </div>
                `;

                medicalContainer.appendChild(card);
            });

            // Renderizar modelos para gestão clínica
            const clinicContainer = document.getElementById('clinic-models-container');
            clinicContainer.innerHTML = '';

            clinicModelsData.forEach(model => {
                const card = document.createElement('div');
                card.className = 'model-card bg-white p-4 rounded-lg shadow border border-gray-200 hover:shadow-md';
                card.innerHTML = `
                    <div class="flex justify-between items-center mb-2">
                        <h3 class="font-semibold text-gray-900">${model.name}</h3>
                        <span class="px-2 py-1 ${model.status === 'Ativo' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'} rounded-full text-xs">${model.status}</span>
                    </div>
                    <p class="text-sm text-gray-600 mb-3">${model.description}</p>
                    <div class="flex justify-between items-center mb-3">
                        <span class="text-sm text-gray-500">Tipo: ${model.type}</span>
                        <span class="text-sm font-medium text-blue-600">Precisão: ${model.accuracy}%</span>
                    </div>
                    <div>
                        <p class="text-xs text-gray-500 mb-1">Features utilizadas:</p>
                        <div class="flex flex-wrap gap-1">
                            ${model.features.map(feature => `
                                <span class="px-2 py-0.5 bg-blue-50 text-blue-600 rounded-full text-xs">${feature}</span>
                            `).join('')}
                        </div>
                    </div>
                    <div class="mt-3 pt-3 border-t border-gray-100 flex justify-end">
                        <button class="text-xs text-blue-600 hover:text-blue-800">
                            <i class="fas fa-cog mr-1"></i>Configurar
                        </button>
                    </div>
                `;

                clinicContainer.appendChild(card);
            });
        }

        // Função para renderizar a maleta de ferramentas
        function renderToolbox() {
            const container = document.getElementById('toolbox-container');
            container.innerHTML = '';

            // Filtrar ferramentas por categoria
            const filteredTools = toolboxData.filter(tool =>
                currentToolboxCategory === 'all' || tool.category === currentToolboxCategory
            );

            // Renderizar cards para cada ferramenta
            filteredTools.forEach(tool => {
                const card = document.createElement('div');
                card.className = 'toolbox-item bg-white p-4 rounded-lg shadow border border-gray-200';
                card.innerHTML = `
                    <div class="flex items-center mb-3">
                        <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                            <i class="fas ${tool.icon} text-blue-600"></i>
                        </div>
                        <h3 class="font-semibold text-gray-900">${tool.name}</h3>
                    </div>
                    <p class="text-sm text-gray-600 mb-4">${tool.description}</p>
                    <div class="bg-gray-50 p-3 rounded-lg text-sm">
                        <p class="text-xs text-gray-500 mb-1">Exemplo de prompt:</p>
                        <p class="text-gray-700">"${tool.prompt}"</p>
                    </div>
                    <button class="mt-3 w-full py-2 btn-primary rounded-lg text-sm">
                        <i class="fas fa-comment-alt mr-1"></i>Usar no Chat
                    </button>
                `;

                // Adicionar evento de clique para usar no chat
                card.querySelector('button').addEventListener('click', () => {
                    useToolInChat(tool.prompt);
                });

                container.appendChild(card);
            });

            // Mostrar mensagem se não houver ferramentas
            if (container.children.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-8 text-gray-500">
                        <i class="fas fa-toolbox text-4xl mb-3"></i>
                        <p>Nenhuma ferramenta encontrada na categoria selecionada.</p>
                    </div>
                `;
            }
        }

        // Função para usar ferramenta no chat
        function useToolInChat(prompt) {
            // Redirecionar para a página do AmigoStudio Pro com o prompt
            window.location.href = `/amigostudio-pro?prompt=${encodeURIComponent(prompt)}`;
        }

        // Event Listeners
        document.addEventListener('DOMContentLoaded', () => {
            // Renderizar dados iniciais
            renderFeatures();
            renderModels();
            renderToolbox();

            // Abas principais
            document.getElementById('hero-tab-btn').addEventListener('click', () => {
                switchTab('hero');
            });

            document.getElementById('features-tab-btn').addEventListener('click', () => {
                switchTab('features');
            });

            document.getElementById('models-tab-btn').addEventListener('click', () => {
                switchTab('models');
            });

            document.getElementById('toolbox-tab-btn').addEventListener('click', () => {
                switchTab('toolbox');
            });

            // Filtros de domínio
            document.querySelectorAll('.domain-link').forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();

                    // Atualizar seleção visual
                    document.querySelectorAll('.domain-link').forEach(l => {
                        l.classList.remove('sidebar-active');
                    });
                    link.classList.add('sidebar-active');

                    // Atualizar filtro
                    currentDomain = link.getAttribute('data-domain');
                    renderFeatures();
                });
            });

            // Filtros de tag
            document.querySelectorAll('.tag-badge').forEach(badge => {
                badge.addEventListener('click', () => {
                    // Atualizar seleção visual
                    document.querySelectorAll('.tag-badge').forEach(b => {
                        b.classList.remove('bg-blue-200');
                        b.classList.add('bg-blue-100');
                    });
                    badge.classList.remove('bg-blue-100');
                    badge.classList.add('bg-blue-200');

                    // Atualizar filtro
                    currentTag = badge.textContent;
                    renderFeatures();
                });
            });

            // Filtros de tipo
            document.querySelectorAll('.type-link').forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();

                    // Atualizar seleção visual
                    document.querySelectorAll('.type-link').forEach(l => {
                        l.classList.remove('sidebar-active');
                    });
                    link.classList.add('sidebar-active');

                    // Atualizar filtro
                    currentType = link.getAttribute('data-type');
                    renderFeatures();
                });
            });

            // Busca
            document.getElementById('search-input').addEventListener('input', (e) => {
                searchQuery = e.target.value.trim();
                renderFeatures();
            });

            // Categorias da maleta de ferramentas
            document.querySelectorAll('.toolbox-category').forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();

                    // Atualizar seleção visual
                    document.querySelectorAll('.toolbox-category').forEach(l => {
                        l.classList.remove('sidebar-active');
                    });
                    link.classList.add('sidebar-active');

                    // Atualizar filtro
                    currentToolboxCategory = link.getAttribute('data-category');
                    renderToolbox();
                });
            });

            // Categorias de modelos ML
            document.querySelectorAll('.model-category-btn').forEach((btn, index) => {
                btn.addEventListener('click', () => {
                    // Atualizar seleção visual
                    document.querySelectorAll('.model-category-btn').forEach(b => {
                        b.classList.remove('bg-blue-600', 'text-white');
                        b.classList.add('bg-white', 'border', 'border-gray-300', 'text-gray-700');
                    });
                    btn.classList.remove('bg-white', 'border', 'border-gray-300', 'text-gray-700');
                    btn.classList.add('bg-blue-600', 'text-white');

                    // Atualizar filtro baseado no índice ou texto
                    const categories = ['all', 'preditivo', 'segmentacao', 'recomendacao', 'anomalia', 'temporal'];
                    currentModelCategory = categories[index];

                    // Atualizar visualização dos modelos
                    filterModels();
                });
            });

            // Função para filtrar modelos por categoria
            function filterModels() {
                const allModelCards = document.querySelectorAll('.model-card');

                allModelCards.forEach(card => {
                    if (currentModelCategory === 'all') {
                        card.style.display = 'block';
                    } else {
                        const cardCategory = card.querySelector('.model-category').textContent.toLowerCase();
                        if (cardCategory.includes(currentModelCategory)) {
                            card.style.display = 'block';
                        } else {
                            card.style.display = 'none';
                        }
                    }
                });
            }

            // Fechar modal de detalhes
            document.getElementById('close-details-btn').addEventListener('click', () => {
                document.getElementById('feature-details-modal').classList.add('hidden');
            });

            // Fechar modal ao clicar fora
            document.getElementById('feature-details-modal').addEventListener('click', (e) => {
                if (e.target === document.getElementById('feature-details-modal')) {
                    document.getElementById('feature-details-modal').classList.add('hidden');
                }
            });

            // Botões do modal de detalhes
            document.getElementById('update-feature-btn').addEventListener('click', () => {
                showToast('Funcionalidade de atualização em desenvolvimento', 'info');
            });

            document.getElementById('export-feature-btn').addEventListener('click', () => {
                showToast('Feature exportada com sucesso', 'success');
            });

            document.getElementById('delete-feature-btn').addEventListener('click', () => {
                showToast('Funcionalidade de exclusão em desenvolvimento', 'info');
            });

            // Botões de criação
            document.getElementById('create-feature-btn').addEventListener('click', () => {
                showToast('Funcionalidade de criação em desenvolvimento', 'info');
            });

            document.getElementById('create-model-btn').addEventListener('click', () => {
                showToast('Funcionalidade de criação de modelo em desenvolvimento', 'info');
            });

            // Tema escuro
            const themeToggle = document.getElementById('theme-toggle');

            // Verificar se há preferência salva
            const darkMode = localStorage.getItem('darkMode') === 'true';

            // Aplicar tema inicial
            if (darkMode) {
                document.body.classList.add('dark-mode');
                themeToggle.checked = true;
            }

            // Alternar tema
            themeToggle.addEventListener('change', () => {
                if (themeToggle.checked) {
                    document.body.classList.add('dark-mode');
                    localStorage.setItem('darkMode', 'true');
                } else {
                    document.body.classList.remove('dark-mode');
                    localStorage.setItem('darkMode', 'false');
                }
            });
        });
    </script>
</body>
</html>

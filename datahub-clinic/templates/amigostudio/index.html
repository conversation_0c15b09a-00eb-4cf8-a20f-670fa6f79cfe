<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Amigo DataStudio - Análise de Dados com IA</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

    <!-- Chart.js - Carregado primeiro e com defer para garantir disponibilidade -->
    <script defer src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>

    <!-- Monaco Editor - Carregado depois do Chart.js -->
    <script defer src="https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.33.0/min/vs/loader.min.js"></script>

    <!-- Verificação do Chart.js e implementação de fallback -->
    <script defer>
        // Função executada quando o DOM estiver carregado
        window.addEventListener('DOMContentLoaded', function() {
            console.log('DOM carregado. Verificando Chart.js...');

            // Verificar se o Chart.js está disponível
            if (typeof Chart !== 'function') {
                console.warn('Chart.js não está disponível após carregamento do DOM. Implementando versão mínima...');

                // Implementação mínima do Chart.js para garantir que a classe Chart esteja disponível
                window.Chart = function(ctx, config) {
                    this.ctx = ctx;
                    this.config = config;
                    this.type = config.type || 'bar';
                    this.data = config.data || {};
                    this.options = config.options || {};

                    // Método para destruir o gráfico
                    this.destroy = function() {
                        console.log('Destruindo gráfico...');
                        // Limpar o canvas
                        if (this.ctx) {
                            this.ctx.clearRect(0, 0, this.ctx.canvas.width, this.ctx.canvas.height);
                        }
                    };

                    // Método para atualizar o gráfico
                    this.update = function() {
                        console.log('Atualizando gráfico...');
                    };

                    // Renderizar o gráfico
                    console.log('Renderizando gráfico mínimo do tipo:', this.type);

                    // Desenhar um texto informando que o Chart.js completo está sendo carregado
                    if (this.ctx) {
                        this.ctx.font = '14px Arial';
                        this.ctx.fillStyle = '#333';
                        this.ctx.textAlign = 'center';
                        this.ctx.fillText('Carregando gráfico...', this.ctx.canvas.width / 2, this.ctx.canvas.height / 2);
                    }
                };

                window.chartJsLoaded = true;
                document.dispatchEvent(new Event('chartjsloaded'));

                // Tentar carregar o Chart.js novamente
                var script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js';
                document.head.appendChild(script);
            } else {
                console.log('Chart.js já está disponível no carregamento do DOM');
                window.chartJsLoaded = true;
                document.dispatchEvent(new Event('chartjsloaded'));
            }
        });
    </script>

    <!-- Carregador de bibliotecas personalizado para evitar conflitos AMD -->
    <script src="/static/js/chart-loader.js"></script>

    <!-- Processador de gráficos personalizado -->
    <script src="/static/js/chart-processor.js"></script>

    <!-- Exemplos de gráficos para testes -->
    <script src="/static/js/chart-examples.js"></script>

    <!-- Assistente de gráficos -->
    <script src="/static/js/chart-helper.js"></script>

    <!-- Teste de gráficos -->
    <script defer src="/static/js/chart-test.js"></script>

    <!-- Bibliotecas carregadas via chart-loader.js -->

    <!-- Inicialização de tooltips -->
    <script>
        // Função para inicializar tooltips
        function initializeTooltips() {
            if (typeof tippy === 'function') {
                // Inicializar tooltips para elementos existentes
                try {
                    tippy('[data-tippy-content]');
                    console.log('Tooltips inicializados com sucesso');
                } catch (e) {
                    console.warn('Erro ao inicializar tooltips:', e);
                }
            } else {
                console.warn('Tippy.js não está disponível para inicializar tooltips');
            }
        }

        // Ouvir o evento de carregamento do Tippy.js
        document.addEventListener('tippyloaded', function() {
            console.log('Evento tippyloaded recebido');
            initializeTooltips();
        });

        // Ouvir o evento de carregamento de todas as bibliotecas
        document.addEventListener('alllibrariesloaded', function() {
            console.log('Todas as bibliotecas carregadas, inicializando tooltips');
            initializeTooltips();
        });

        // Definir uma função global para reinicializar tooltips quando necessário
        window.reinitializeTooltips = function() {
            console.log('Reinicializando tooltips...');
            initializeTooltips();
        };
    </script>

    <style>
        :root {
            --primary-color: #0A4D8F;
            --primary-light: #1A6BB8;
            --primary-dark: #073A6B;
            --secondary-color: #34C759;
            --accent-color: #FF9500;
            --danger-color: #FF3B30;
            --text-color: #333333;
            --text-light: #666666;
            --bg-color: #F8F9FA;
            --bg-card: #FFFFFF;
            --border-color: #E5E7EB;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }

        [data-theme="dark"] {
            --primary-color: #1A6BB8;
            --primary-light: #2D7FD3;
            --primary-dark: #0A4D8F;
            --secondary-color: #2AA147;
            --accent-color: #E68600;
            --danger-color: #E63429;
            --text-color: #E0E0E0;
            --text-light: #A0A0A0;
            --bg-color: #121212;
            --bg-card: #1E1E1E;
            --border-color: #333333;
            --shadow-color: rgba(0, 0, 0, 0.3);
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            overflow-x: hidden;
            margin: 0;
            padding: 0;
            background-color: var(--bg-color);
            color: var(--text-color);
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        .chat-message {
            margin-bottom: 1rem;
            padding: 0.75rem;
            border-radius: 0.5rem;
        }

        .user-message {
            background-color: rgba(10, 77, 143, 0.1);
            margin-left: 2rem;
            margin-right: 0.5rem;
        }

        .assistant-message {
            background-color: var(--bg-card);
            border: 1px solid var(--border-color);
            margin-left: 0.5rem;
            margin-right: 2rem;
        }

        .code-block {
            background-color: #1e1e1e;
            color: #d4d4d4;
            padding: 1rem;
            border-radius: 0.375rem;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            white-space: pre-wrap;
            margin: 1rem 0;
            border: 1px solid #3a3a3a;
            position: relative;
            overflow-x: auto;
        }

        .code-block::before {
            content: "🐍 Python";
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            background-color: #3776ab;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: bold;
        }

        .code-block-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #2d2d2d;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem 0.375rem 0 0;
            border-bottom: 1px solid #3a3a3a;
            margin: 1rem 0 0 0;
        }

        .code-block-content {
            background-color: #1e1e1e;
            color: #d4d4d4;
            padding: 1rem;
            border-radius: 0 0 0.375rem 0.375rem;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            white-space: pre-wrap;
            margin: 0 0 1rem 0;
            border: 1px solid #3a3a3a;
            border-top: none;
            overflow-x: auto;
        }

        .copy-code-btn {
            background-color: #4a5568;
            color: white;
            border: none;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .copy-code-btn:hover {
            background-color: #2d3748;
        }

        .run-code-btn {
            background-color: #3776ab;
            color: white;
            border: none;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            cursor: pointer;
            transition: background-color 0.2s;
            margin-left: 0.5rem;
        }

        .run-code-btn:hover {
            background-color: #2c5aa0;
        }

        .hidden {
            display: none;
        }

        .toast {
            position: fixed;
            top: 1rem;
            right: 1rem;
            padding: 0.75rem 1.5rem;
            border-radius: 0.375rem;
            z-index: 50;
            animation: fadeInOut 3s forwards;
        }

        @keyframes fadeInOut {
            0% { opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { opacity: 0; }
        }

        .chart-container {
            background-color: var(--bg-card);
            border-radius: 0.5rem;
            padding: 1rem;
            box-shadow: 0 1px 3px var(--shadow-color);
            border: 1px solid var(--border-color);
            margin-top: 1rem;
            margin-bottom: 1rem;
            min-height: 300px;
            position: relative;
        }

        /* Estilos para diferentes tipos de gráficos */
        .chart-radar {
            height: 350px;
        }

        .chart-bar, .chart-line {
            height: 300px;
        }

        .chart-pie, .chart-doughnut {
            height: 250px;
        }

        .main-header {
            background-color: var(--bg-card);
            border-bottom: 1px solid var(--border-color);
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .sidebar-active {
            background-color: rgba(10, 77, 143, 0.1);
            color: var(--primary-color);
            font-weight: 500;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
            transition: all 0.2s;
        }

        .btn-primary:hover {
            background-color: var(--primary-light);
        }

        .btn-secondary {
            background-color: var(--secondary-color);
            color: white;
            transition: all 0.2s;
        }

        .btn-secondary:hover {
            background-color: var(--secondary-color);
            opacity: 0.9;
        }

        .btn-danger {
            background-color: var(--danger-color);
            color: white;
            transition: all 0.2s;
        }

        .btn-danger:hover {
            background-color: var(--danger-color);
            opacity: 0.9;
        }

        .btn-warning {
            background-color: var(--accent-color);
            color: white;
            transition: all 0.2s;
        }

        .btn-warning:hover {
            background-color: var(--accent-color);
            opacity: 0.9;
        }

        /* Tema escuro para o Monaco Editor */
        .monaco-editor-dark {
            background-color: #1e1e1e;
        }

        .editor-container {
            height: calc(100vh - 230px);
            min-height: 250px;
        }

        .chat-container {
            height: calc(100vh - 160px);
            min-height: 500px;
        }

        .output-container {
            height: calc(100vh - 230px);
            min-height: 400px;
        }

        .toolbox-container {
            max-height: calc(100vh - 230px);
            overflow-y: auto;
            padding-right: 5px;
        }

        .toolbox-item {
            transition: all 0.15s ease;
            cursor: pointer;
            margin-bottom: 6px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 6px 8px;
            background-color: #ffffff;
        }

        .toolbox-item:hover {
            background-color: #fafbfc;
            border-color: #d1d5db;
        }

        .toolbox-item.premium {
            position: relative;
            border-color: #f59e0b;
            background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
        }

        .toolbox-item.premium:hover {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border-color: #d97706;
        }

        .premium-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            background: linear-gradient(45deg, #f59e0b, #d97706);
            color: white;
            font-size: 8px;
            padding: 1px 4px;
            border-radius: 4px;
            font-weight: bold;
        }

        .tab-button.active {
            color: #3b82f6;
            border-color: #3b82f6;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* Scrollbar personalizada */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #a1a1a1;
        }

        /* Tooltip personalizado */
        .tippy-box {
            background-color: #333;
            color: white;
            border-radius: 4px;
            font-size: 14px;
            max-width: 300px !important;
        }

        .tippy-arrow {
            color: #333;
        }

        /* Estilos para o toggle do layout */
        .layout-toggle-btn {
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }

        .layout-toggle-btn:hover {
            transform: scale(1.05);
            border-color: currentColor;
        }

        /* Animações suaves para as colunas */
        .column-transition {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Responsividade melhorada */
        @media (max-width: 1024px) {
            .layout-toggle-btn {
                display: none;
            }
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-900">
    <!-- Cabeçalho Principal -->
    <header class="main-header py-2 px-3 shadow-sm">
        <div class="w-full flex justify-between items-center">
            <div class="flex items-center space-x-6">
                <a href="/" class="flex items-center">
                    <img src="/static/amigo-logo.png" alt="Amigo Logo" class="h-12 mr-2">
                    <div class="h-5 w-0.5 bg-blue-400 mx-1.5"></div>
                    <span class="text-xl font-bold text-gray-900">Data<span class="font-semibold">Studio</span></span>
                </a>

                <nav class="hidden md:flex space-x-1">
                    <a href="/" class="px-2 py-1 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-100">
                        <i class="fas fa-arrow-left mr-1"></i>Voltar para o DataHub
                    </a>
                </nav>
            </div>

            <div class="flex items-center space-x-3">
                <a href="/amigostudio-pro/feature-store-otimizado" class="flex items-center justify-center border border-blue-400 text-blue-600 hover:bg-blue-50 px-2.5 py-1 rounded text-sm font-medium transition-all duration-200">
                    <i class="fas fa-database mr-1.5"></i> Feature Store
                </a>
                <!-- Switch de Tema -->
                <label class="theme-switch">
                    <input type="checkbox" id="theme-toggle">
                    <span class="slider"></span>
                </label>
                <span class="text-xs text-gray-600 ml-1"><i class="fas fa-moon"></i></span>
            </div>
        </div>
    </header>

    <main class="w-full px-1 py-1">
        <div class="flex gap-2">
            <!-- Coluna da Esquerda (Maleta de Ferramentas) - Largura fixa -->
            <div id="toolbox-column" class="w-80 flex-shrink-0 column-transition">
                <div class="bg-white rounded-lg shadow-sm p-3 border border-gray-200">
                    <div class="flex justify-between items-center mb-2">
                        <h2 class="text-base font-semibold">Maleta de Ferramentas</h2>
                    </div>

                    <!-- Botões de Ação da Maleta -->
                    <div class="flex flex-wrap gap-2 mb-3">
                        <button onclick="sendToolboxAction('email')" class="text-xs text-gray-600 hover:text-blue-600 px-2 py-1 rounded-md flex items-center border border-gray-200 hover:border-blue-200 transition-colors">
                            <i class="far fa-envelope mr-1.5 text-blue-500"></i>Email
                        </button>
                        <button onclick="sendToolboxAction('csv')" class="text-xs text-gray-600 hover:text-blue-600 px-2 py-1 rounded-md flex items-center border border-gray-200 hover:border-blue-200 transition-colors">
                            <i class="far fa-file-alt mr-1.5 text-blue-500"></i>CSV
                        </button>
                        <button onclick="sendToolboxAction('resumo')" class="text-xs text-gray-600 hover:text-blue-600 px-2 py-1 rounded-md flex items-center border border-gray-200 hover:border-blue-200 transition-colors">
                            <i class="far fa-chart-bar mr-1.5 text-blue-500"></i>Resumo
                        </button>
                        <button onclick="sendToolboxAction('distribuir')" class="text-xs text-gray-600 hover:text-blue-600 px-2 py-1 rounded-md flex items-center border border-gray-200 hover:border-blue-200 transition-colors">
                            <i class="far fa-share-square mr-1.5 text-blue-500"></i>Distribuir
                        </button>
                    </div>

                    <!-- Filtros de Categorias -->
                    <div class="flex flex-wrap gap-1 mb-3">
                        <span class="text-xs text-gray-500 px-2 py-0.5 rounded-full bg-gray-100 cursor-pointer hover:bg-blue-100 hover:text-blue-700 transition-colors">Todos</span>
                        <span class="text-xs text-gray-500 px-2 py-0.5 rounded-full bg-gray-100 cursor-pointer hover:bg-blue-100 hover:text-blue-700 transition-colors">Financeiro</span>
                        <span class="text-xs text-gray-500 px-2 py-0.5 rounded-full bg-gray-100 cursor-pointer hover:bg-blue-100 hover:text-blue-700 transition-colors">Operacional</span>
                        <span class="text-xs text-gray-500 px-2 py-0.5 rounded-full bg-gray-100 cursor-pointer hover:bg-blue-100 hover:text-blue-700 transition-colors">Pacientes</span>
                    </div>

                    <div class="toolbox-container" style="max-height: calc(100vh - 320px);">
                        <div id="toolbox-items" class="grid grid-cols-1 gap-2">
                            <!-- Os itens da maleta de ferramentas serão inseridos aqui via JavaScript -->
                            <!-- Fallback caso o JavaScript falhe -->
                            <div class="toolbox-item">
                                <div class="flex items-center mb-1">
                                    <div class="w-6 h-6 rounded-full bg-gray-100 flex items-center justify-center mr-2">
                                        <i class="far fa-chart-line text-blue-500 text-xs"></i>
                                    </div>
                                    <h3 class="font-medium text-gray-900 text-sm">Análise de Faturamento</h3>
                                </div>
                                <div class="text-xs text-gray-500">Analisa a evolução do faturamento mensal</div>
                            </div>
                            <div class="toolbox-item">
                                <div class="flex items-center mb-1">
                                    <div class="w-6 h-6 rounded-full bg-gray-100 flex items-center justify-center mr-2">
                                        <i class="far fa-percentage text-blue-500 text-xs"></i>
                                    </div>
                                    <h3 class="font-medium text-gray-900 text-sm">Rentabilidade por Procedimento</h3>
                                </div>
                                <div class="text-xs text-gray-500">Calcula a rentabilidade dos procedimentos</div>
                            </div>
                            <div class="toolbox-item">
                                <div class="flex items-center mb-1">
                                    <div class="w-6 h-6 rounded-full bg-gray-100 flex items-center justify-center mr-2">
                                        <i class="far fa-calendar-check text-blue-500 text-xs"></i>
                                    </div>
                                    <h3 class="font-medium text-gray-900 text-sm">Taxa de Ocupação</h3>
                                </div>
                                <div class="text-xs text-gray-500">Analisa a ocupação da agenda por período</div>
                            </div>
                        </div>
                    </div>

                    <!-- Botão para abrir modal com mais ferramentas -->
                    <div class="mt-3 text-center">
                        <button id="more-tools-btn" onclick="openToolsModal()" class="w-full text-xs text-blue-600 hover:text-blue-800 px-2 py-1.5 rounded-md flex items-center justify-center border border-blue-200 hover:border-blue-300 transition-colors">
                            <i class="fas fa-th-large mr-1.5"></i>Ver todas as ferramentas
                        </button>
                    </div>
                </div>
            </div>

            <!-- Coluna do Meio (Chat) - Ocupa o espaço restante -->
            <div id="chat-column" class="flex-1 bg-white rounded-lg shadow-sm p-3 border border-gray-200 column-transition">
                <div class="flex justify-between items-center mb-2">
                    <h2 class="text-base font-semibold">Chat Amigo Intelligence</h2>
                    <div class="flex space-x-2">
                        <button id="toggle-layout-btn" onclick="toggleLayout()" class="layout-toggle-btn text-xs bg-blue-100 hover:bg-blue-200 text-blue-700 px-3 py-1 rounded-lg" title="Mostrar Editor e Visualização">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                        <button id="test-chart-btn" onclick="testChartRendering()" class="text-xs bg-blue-100 hover:bg-blue-200 text-blue-700 px-3 py-1 rounded-lg">
                            <i class="fas fa-chart-bar mr-1"></i>Testar Gráfico
                        </button>
                        <button id="new-chat-btn" class="text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded-lg">
                            <i class="fas fa-plus mr-1"></i>Novo Chat
                        </button>
                    </div>
                </div>

                <!-- Área de Chat -->
                <div id="chat-container" class="chat-container overflow-y-auto mb-2 border border-gray-200 rounded-lg p-3 bg-gray-50" style="height: calc(100vh - 240px);">
                    <!-- Mensagem de boas-vindas -->
                    <div class="chat-message assistant-message">
                        <p>Olá! Sou o Amigo Intelligence, seu assistente especializado em análise de dados de performance no setor de saúde.</p>
                        <p class="mt-2">Posso ajudar com análises de dados clínicos, previsões de demanda, análise de rentabilidade e muito mais. Como posso ajudar sua clínica hoje?</p>
                        <div class="mt-3 flex flex-wrap gap-2">
                            <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-xs cursor-pointer hover:bg-blue-200" onclick="askHealthcareSuggestion(this)">Analisar jornada do paciente</span>
                            <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-xs cursor-pointer hover:bg-blue-200" onclick="askHealthcareSuggestion(this)">Prever demanda de agendamentos</span>
                            <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-xs cursor-pointer hover:bg-blue-200" onclick="askHealthcareSuggestion(this)">Analisar rentabilidade por procedimento</span>
                            <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-xs cursor-pointer hover:bg-blue-200" onclick="askHealthcareSuggestion(this)">Identificar pacientes com risco de abandono</span>
                        </div>
                        <p class="text-xs text-gray-500 mt-3">Você também pode me pedir para gerar código para análises específicas ou visualizações.</p>
                    </div>
                </div>

                <!-- Área de Input -->
                <div class="flex items-center">
                    <input id="chat-input" type="text" placeholder="Digite sua mensagem..." class="flex-grow px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <button id="send-btn" class="ml-2 send-btn-minimal">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>

            <!-- Coluna da Direita (Visualização e Editor) - Escondida por padrão -->
            <div id="editor-column" class="w-1/2 flex-shrink-0 hidden column-transition">
                <div class="grid grid-cols-1 gap-2">
                    <!-- Visualização (Em Cima) -->
                    <div class="bg-white rounded-lg shadow-sm p-3 border border-gray-200">
                        <div class="flex justify-between items-center mb-2">
                            <h2 class="text-base font-semibold">Visualização</h2>
                            <div class="flex items-center">
                                <span id="status-indicator" class="inline-block w-3 h-3 bg-green-500 rounded-full mr-2"></span>
                                <span id="status-text" class="text-xs text-gray-500">Pronto</span>
                            </div>
                        </div>

                        <!-- Abas de Saída -->
                        <div class="mb-2">
                            <div class="border-b border-gray-200">
                                <nav class="flex -mb-px">
                                    <button id="output-tab-btn" class="tab-button active px-4 py-1 text-sm font-medium text-blue-600 border-b-2 border-blue-600">
                                        Output Console
                                    </button>
                                    <button id="preview-tab-btn" class="tab-button px-4 py-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300 border-b-2 border-transparent">
                                        Preview
                                    </button>
                                </nav>
                            </div>

                            <!-- Output Console Tab -->
                            <div id="output-tab" class="tab-content active mt-2">
                                <pre id="output" class="output-container bg-gray-800 text-green-400 p-3 rounded-lg overflow-y-auto font-mono text-sm" style="height: calc(100vh - 500px);"></pre>
                            </div>

                            <!-- Streamlit Preview Tab -->
                            <div id="preview-tab" class="tab-content hidden mt-2">
                                <div id="streamlit-container" class="output-container" style="height: calc(100vh - 500px);">
                                    <iframe id="streamlit-frame" class="w-full h-full border border-gray-200 rounded-lg" style="display: none;" frameborder="0"></iframe>
                                </div>
                                <!-- Botão para parar o preview -->
                                <div class="mt-2 flex justify-end">
                                    <button id="stop-preview-btn" class="btn-warning px-3 py-1 rounded-lg text-sm">
                                        <i class="fas fa-stop-circle mr-1"></i>Parar Preview
                                    </button>
                                </div>
                                <!-- Mensagem de erro -->
                                <div id="streamlit-error" class="hidden mt-2 text-red-500 text-sm font-medium">
                                    Erro de Conexão: Não foi possível conectar. Tente novamente mais tarde ou reinicie o preview.
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Editor de Código (Embaixo) -->
                    <div class="bg-white rounded-lg shadow-sm p-3 border border-gray-200">
                        <div class="flex justify-between items-center mb-2">
                            <h2 class="text-base font-semibold">Editor</h2>
                            <!-- Botões de Ação -->
                            <div class="flex space-x-2">
                                <button id="execute-btn" class="btn-primary px-3 py-1 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <i class="fas fa-play mr-1"></i>Executar
                                </button>
                                <div id="spinner" class="hidden w-5 h-5 border-2 border-t-2 border-gray-200 rounded-full animate-spin"></div>
                            </div>
                        </div>
                        <div id="monaco-editor" class="editor-container border border-gray-200 rounded-lg" style="height: calc(100vh - 700px);"></div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Toast para notificações -->
    <div id="toast" class="hidden"></div>

    <!-- Modal de Ferramentas -->
    <div id="tools-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
        <div class="bg-white rounded-lg shadow-xl w-11/12 max-w-6xl max-h-[95vh] overflow-hidden flex flex-col">
            <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                <div>
                    <h2 class="text-xl font-semibold text-gray-900">Biblioteca de Ferramentas</h2>
                    <p class="text-sm text-gray-500 mt-1">
                        <span class="inline-flex items-center">
                            <svg class="w-4 h-4 mr-1 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                            Baseado em práticas médicas validadas por <strong>Dr. Eduardo Mendes</strong> - CRM 12345
                        </span>
                    </p>
                </div>
                <button onclick="closeToolsModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- Filtros -->
            <div class="p-4 border-b border-gray-200">
                <div class="flex flex-wrap gap-2">
                    <button class="px-3 py-1.5 rounded-md text-sm bg-blue-500 text-white">Todos</button>
                    <button class="px-3 py-1.5 rounded-md text-sm bg-gray-100 text-gray-700 hover:bg-gray-200">Financeiro</button>
                    <button class="px-3 py-1.5 rounded-md text-sm bg-gray-100 text-gray-700 hover:bg-gray-200">Operacional</button>
                    <button class="px-3 py-1.5 rounded-md text-sm bg-gray-100 text-gray-700 hover:bg-gray-200">Pacientes</button>
                    <button class="px-3 py-1.5 rounded-md text-sm bg-gray-100 text-gray-700 hover:bg-gray-200">Marketing</button>
                    <button class="px-3 py-1.5 rounded-md text-sm bg-gray-100 text-gray-700 hover:bg-gray-200">Recursos Humanos</button>
                    <button class="px-3 py-1.5 rounded-md text-sm bg-gray-100 text-gray-700 hover:bg-gray-200">Qualidade</button>
                </div>

                <div class="mt-3">
                    <input type="text" placeholder="Buscar ferramentas..." class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
            </div>

            <!-- Lista de Ferramentas -->
            <div class="p-4 overflow-y-auto flex-grow">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <!-- Análise de Faturamento -->
                    <div class="border border-gray-200 rounded-lg p-3 hover:shadow-md transition-shadow bg-white">
                        <div class="flex items-center mb-2">
                            <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                                <i class="fas fa-chart-line text-blue-600"></i>
                            </div>
                            <h3 class="font-medium text-gray-900">Análise de Faturamento</h3>
                        </div>
                        <p class="text-sm text-gray-600 mb-3">Analisa a evolução do faturamento mensal e identifica tendências</p>
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                    Financeiro
                                </span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700">
                                    GRATUITA
                                </span>
                            </div>
                            <div class="flex items-center bg-green-50 px-2 py-1 rounded-full border border-green-200">
                                <span class="text-xs font-semibold text-green-700">Amigo Tech</span>
                                <span class="ml-1 text-green-600">✓</span>
                            </div>
                        </div>
                        <button onclick="useToolAndCloseModal('Analisar a evolução do faturamento mensal dos últimos 12 meses e identificar tendências sazonais ou padrões de crescimento/queda')" class="w-full text-sm bg-gray-50 hover:bg-gray-100 text-gray-700 px-3 py-2 rounded-lg border border-gray-200 transition-colors font-medium">
                            Usar Ferramenta
                        </button>
                    </div>

                    <!-- Rentabilidade por Procedimento -->
                    <div class="border border-gray-200 rounded-lg p-3 hover:shadow-md transition-shadow bg-white">
                        <div class="flex items-center mb-2">
                            <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                                <i class="fas fa-percentage text-blue-600"></i>
                            </div>
                            <h3 class="font-medium text-gray-900">Rentabilidade por Procedimento</h3>
                        </div>
                        <p class="text-sm text-gray-600 mb-3">Calcula e compara a rentabilidade dos diferentes procedimentos</p>
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                    Financeiro
                                </span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700">
                                    GRATUITA
                                </span>
                            </div>
                            <div class="flex items-center bg-green-50 px-2 py-1 rounded-full border border-green-200">
                                <span class="text-xs font-semibold text-green-700">Amigo Tech</span>
                                <span class="ml-1 text-green-600">✓</span>
                            </div>
                        </div>
                        <button onclick="useToolAndCloseModal('Calcular a rentabilidade dos 10 procedimentos mais realizados e identificar quais são os mais lucrativos')" class="w-full text-sm bg-gray-50 hover:bg-gray-100 text-gray-700 px-3 py-2 rounded-lg border border-gray-200 transition-colors font-medium">
                            Usar Ferramenta
                        </button>
                    </div>

                    <!-- Taxa de Ocupação da Agenda -->
                    <div class="border border-gray-200 rounded-lg p-3 hover:shadow-md transition-shadow bg-white">
                        <div class="flex items-center mb-2">
                            <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                                <i class="fas fa-calendar-check text-blue-600"></i>
                            </div>
                            <h3 class="font-medium text-gray-900">Taxa de Ocupação da Agenda</h3>
                        </div>
                        <p class="text-sm text-gray-600 mb-3">Analisa a taxa de ocupação da agenda por período, profissional ou unidade</p>
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                    Operacional
                                </span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700">
                                    GRATUITA
                                </span>
                            </div>
                            <div class="flex items-center bg-gray-100 px-2 py-1 rounded-full border border-gray-200">
                                <span class="text-xs font-semibold text-gray-600">Ferramenta Comum</span>
                            </div>
                        </div>
                        <button onclick="useToolAndCloseModal('Analisar a taxa de ocupação da agenda por dia da semana e horário, identificando os períodos de maior e menor ocupação')" class="w-full text-sm bg-gray-50 hover:bg-gray-100 text-gray-700 px-3 py-2 rounded-lg border border-gray-200 transition-colors font-medium">
                            Usar Ferramenta
                        </button>
                    </div>

                    <!-- Tempo Médio de Atendimento -->
                    <div class="border border-gray-200 rounded-lg p-3 hover:shadow-md transition-shadow bg-white">
                        <div class="flex items-center mb-2">
                            <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                                <i class="fas fa-clock text-blue-600"></i>
                            </div>
                            <h3 class="font-medium text-gray-900">Tempo Médio de Atendimento</h3>
                        </div>
                        <p class="text-sm text-gray-600 mb-3">Calcula o tempo médio de atendimento por profissional</p>
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                    Operacional
                                </span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700">
                                    GRATUITA
                                </span>
                            </div>
                            <div class="flex items-center bg-gray-100 px-2 py-1 rounded-full border border-gray-200">
                                <span class="text-xs font-semibold text-gray-600">Ferramenta Comum</span>
                            </div>
                        </div>
                        <button onclick="useToolAndCloseModal('Calcular o tempo médio de atendimento por profissional e tipo de procedimento, identificando oportunidades de otimização')" class="w-full text-sm bg-gray-50 hover:bg-gray-100 text-gray-700 px-3 py-2 rounded-lg border border-gray-200 transition-colors font-medium">
                            Usar Ferramenta
                        </button>
                    </div>

                    <!-- Jornada do Paciente -->
                    <div class="border border-gray-200 rounded-lg p-3 hover:shadow-md transition-shadow bg-white">
                        <div class="flex items-center mb-2">
                            <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                                <i class="fas fa-route text-blue-600"></i>
                            </div>
                            <h3 class="font-medium text-gray-900">Jornada do Paciente</h3>
                        </div>
                        <p class="text-sm text-gray-600 mb-3">Analisa a jornada do paciente desde o primeiro contato</p>
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                    </svg>
                                    Pacientes
                                </span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700">
                                    GRATUITA
                                </span>
                            </div>
                            <div class="flex items-center bg-green-50 px-2 py-1 rounded-full border border-green-200">
                                <span class="text-xs font-semibold text-green-700">Amigo Tech</span>
                                <span class="ml-1 text-green-600">✓</span>
                            </div>
                        </div>
                        <button onclick="useToolAndCloseModal('Analisar a jornada do paciente, identificando os pontos de maior satisfação e os gargalos que precisam ser melhorados')" class="w-full text-sm bg-gray-50 hover:bg-gray-100 text-gray-700 px-3 py-2 rounded-lg border border-gray-200 transition-colors font-medium">
                            Usar Ferramenta
                        </button>
                    </div>

                    <!-- Taxa de Retorno -->
                    <div class="border border-gray-200 rounded-lg p-3 hover:shadow-md transition-shadow bg-white">
                        <div class="flex items-center mb-2">
                            <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                                <i class="fas fa-redo text-blue-600"></i>
                            </div>
                            <h3 class="font-medium text-gray-900">Taxa de Retorno</h3>
                        </div>
                        <p class="text-sm text-gray-600 mb-3">Calcula a taxa de retorno dos pacientes e fatores associados</p>
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                    </svg>
                                    Pacientes
                                </span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700">
                                    GRATUITA
                                </span>
                            </div>
                            <div class="flex items-center bg-gray-100 px-2 py-1 rounded-full border border-gray-200">
                                <span class="text-xs font-semibold text-gray-600">Ferramenta Comum</span>
                            </div>
                        </div>
                        <button onclick="useToolAndCloseModal('Calcular a taxa de retorno dos pacientes nos últimos 6 meses e identificar os fatores que influenciam o retorno')" class="w-full text-sm bg-gray-50 hover:bg-gray-100 text-gray-700 px-3 py-2 rounded-lg border border-gray-200 transition-colors font-medium">
                            Usar Ferramenta
                        </button>
                    </div>

                    <!-- Conversão de Leads -->
                    <div class="border border-gray-200 rounded-lg p-3 hover:shadow-md transition-shadow bg-white">
                        <div class="flex items-center mb-2">
                            <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                                <i class="fas fa-funnel-dollar text-blue-600"></i>
                            </div>
                            <h3 class="font-medium text-gray-900">Conversão de Leads</h3>
                        </div>
                        <p class="text-sm text-gray-600 mb-3">Analisa a taxa de conversão de leads em pacientes</p>
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z"></path>
                                    </svg>
                                    Marketing
                                </span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700">
                                    GRATUITA
                                </span>
                            </div>
                            <div class="flex items-center bg-green-50 px-2 py-1 rounded-full border border-green-200">
                                <span class="text-xs font-semibold text-green-700">Amigo Tech</span>
                                <span class="ml-1 text-green-600">✓</span>
                            </div>
                        </div>
                        <button onclick="useToolAndCloseModal('Analisar a taxa de conversão de leads em pacientes por origem e propor melhorias no processo de captação')" class="w-full text-sm bg-gray-50 hover:bg-gray-100 text-gray-700 px-3 py-2 rounded-lg border border-gray-200 transition-colors font-medium">
                            Usar Ferramenta
                        </button>
                    </div>

                    <!-- ROI de Campanhas -->
                    <div class="border border-gray-200 rounded-lg p-3 hover:shadow-md transition-shadow bg-white">
                        <div class="flex items-center mb-2">
                            <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                                <i class="fas fa-bullseye text-blue-600"></i>
                            </div>
                            <h3 class="font-medium text-gray-900">ROI de Campanhas</h3>
                        </div>
                        <p class="text-sm text-gray-600 mb-3">Calcula o retorno sobre investimento das campanhas</p>
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z"></path>
                                    </svg>
                                    Marketing
                                </span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700">
                                    GRATUITA
                                </span>
                            </div>
                            <div class="flex items-center bg-gray-100 px-2 py-1 rounded-full border border-gray-200">
                                <span class="text-xs font-semibold text-gray-600">Ferramenta Comum</span>
                            </div>
                        </div>
                        <button onclick="useToolAndCloseModal('Calcular o ROI das campanhas de marketing dos últimos 3 meses e identificar as mais eficientes')" class="w-full text-sm bg-gray-50 hover:bg-gray-100 text-gray-700 px-3 py-2 rounded-lg border border-gray-200 transition-colors font-medium">
                            Usar Ferramenta
                        </button>
                    </div>

                    <!-- Produtividade por Profissional -->
                    <div class="border border-gray-200 rounded-lg p-3 hover:shadow-md transition-shadow bg-white">
                        <div class="flex items-center mb-2">
                            <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                                <i class="fas fa-user-md text-blue-600"></i>
                            </div>
                            <h3 class="font-medium text-gray-900">Produtividade por Profissional</h3>
                        </div>
                        <p class="text-sm text-gray-600 mb-3">Compara a produtividade entre diferentes profissionais</p>
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                    </svg>
                                    RH
                                </span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700">
                                    GRATUITA
                                </span>
                            </div>
                            <div class="flex items-center bg-green-50 px-2 py-1 rounded-full border border-green-200">
                                <span class="text-xs font-semibold text-green-700">Amigo Tech</span>
                                <span class="ml-1 text-green-600">✓</span>
                            </div>
                        </div>
                        <button onclick="useToolAndCloseModal('Comparar a produtividade dos profissionais em termos de número de atendimentos, faturamento gerado e satisfação dos pacientes')" class="w-full text-sm bg-gray-50 hover:bg-gray-100 text-gray-700 px-3 py-2 rounded-lg border border-gray-200 transition-colors font-medium">
                            Usar Ferramenta
                        </button>
                    </div>

                    <!-- Análise de NPS -->
                    <div class="border border-gray-200 rounded-lg p-3 hover:shadow-md transition-shadow bg-white">
                        <div class="flex items-center mb-2">
                            <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                                <i class="fas fa-smile text-blue-600"></i>
                            </div>
                            <h3 class="font-medium text-gray-900">Análise de NPS</h3>
                        </div>
                        <p class="text-sm text-gray-600 mb-3">Analisa o Net Promoter Score e fatores que o influenciam</p>
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                                    </svg>
                                    Qualidade
                                </span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700">
                                    GRATUITA
                                </span>
                            </div>
                            <div class="flex items-center bg-gray-100 px-2 py-1 rounded-full border border-gray-200">
                                <span class="text-xs font-semibold text-gray-600">Ferramenta Comum</span>
                            </div>
                        </div>
                        <button onclick="useToolAndCloseModal('Analisar o NPS por profissional, procedimento e unidade, identificando os fatores que mais impactam a satisfação dos pacientes')" class="w-full text-sm bg-gray-50 hover:bg-gray-100 text-gray-700 px-3 py-2 rounded-lg border border-gray-200 transition-colors font-medium">
                            Usar Ferramenta
                        </button>
                    </div>

                    <!-- Previsão de Abandono -->
                    <div class="border border-gray-200 rounded-lg p-3 hover:shadow-md transition-shadow bg-white">
                        <div class="flex items-center mb-2">
                            <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                                <i class="fas fa-user-minus text-blue-600"></i>
                            </div>
                            <h3 class="font-medium text-gray-900">Previsão de Abandono</h3>
                        </div>
                        <p class="text-sm text-gray-600 mb-3">Identifica pacientes com risco de abandono de tratamento</p>
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                                    </svg>
                                    Qualidade
                                </span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700">
                                    GRATUITA
                                </span>
                            </div>
                            <div class="flex items-center bg-green-50 px-2 py-1 rounded-full border border-green-200">
                                <span class="text-xs font-semibold text-green-700">Amigo Tech</span>
                                <span class="ml-1 text-green-600">✓</span>
                            </div>
                        </div>
                        <button onclick="useToolAndCloseModal('Identificar pacientes com alto risco de abandono de tratamento e propor estratégias de retenção')" class="w-full text-sm bg-gray-50 hover:bg-gray-100 text-gray-700 px-3 py-2 rounded-lg border border-gray-200 transition-colors font-medium">
                            Usar Ferramenta
                        </button>
                    </div>

                    <!-- FERRAMENTAS PREMIUM -->

                    <!-- Análise Completa Y/Y M/M D/D - Dr. PDZ -->
                    <div class="border border-gray-300 rounded-lg p-3 hover:shadow-md transition-shadow bg-gray-50 relative">
                        <div class="absolute top-2 right-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white text-xs px-2 py-1 rounded-full font-semibold shadow-sm">
                            PRO
                        </div>
                        <div class="flex items-center mb-2">
                            <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                                <i class="fas fa-chart-line text-blue-600"></i>
                            </div>
                            <h3 class="font-medium text-gray-900">Análise Completa Y/Y M/M D/D</h3>
                        </div>
                        <p class="text-sm text-gray-600 mb-3">Análise temporal completa com comparações ano a ano, mês a mês e dia a dia</p>
                        <div class="flex items-center justify-between mb-2">
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                    Operacional
                                </span>
                                <span class="text-lg font-bold text-blue-600">R$ 25/uso</span>
                            </div>
                            <div class="flex items-center bg-green-50 px-3 py-1 rounded-full border border-green-200">
                                <span class="text-xs font-bold text-green-700">Dr. PDZ</span>
                                <span class="ml-1 text-green-600 font-bold">✓</span>
                            </div>
                        </div>
                        <div class="mb-3">
                            <span class="text-xs text-blue-600 font-medium">Verificado por AmigoTech</span>
                        </div>
                        <button onclick="openPremiumCheckout('Análise Completa Y/Y M/M D/D', 'Dr. PDZ', 'R$ 25/uso', 'Análise temporal completa com comparações ano a ano, mês a mês e dia a dia')" class="w-full text-sm bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white px-3 py-2 rounded-lg transition-all duration-200 font-medium shadow-sm">
                            <i class="fas fa-credit-card mr-2"></i>Comprar e Usar
                        </button>
                    </div>

                    <!-- Análise Preditiva de Demanda - Dra. Carla Santos -->
                    <div class="border border-gray-300 rounded-lg p-3 hover:shadow-md transition-shadow bg-gray-50 relative">
                        <div class="absolute top-2 right-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white text-xs px-2 py-1 rounded-full font-semibold shadow-sm">
                            PRO
                        </div>
                        <div class="flex items-center mb-2">
                            <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                                <i class="fas fa-crystal-ball text-blue-600"></i>
                            </div>
                            <h3 class="font-medium text-gray-900">Análise Preditiva de Demanda</h3>
                        </div>
                        <p class="text-sm text-gray-600 mb-3">Previsão de demanda por especialidade usando machine learning avançado</p>
                        <div class="flex items-center justify-between mb-2">
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                    Operacional
                                </span>
                                <span class="text-lg font-bold text-blue-600">R$ 35/uso</span>
                            </div>
                            <div class="flex items-center bg-blue-50 px-3 py-1 rounded-full border border-blue-200">
                                <span class="text-xs font-bold text-blue-700">Dra. Carla Santos</span>
                            </div>
                        </div>
                        <div class="mb-3">
                            <span class="text-xs text-gray-500">Especialista em IA</span>
                        </div>
                        <button onclick="openPremiumCheckout('Análise Preditiva de Demanda', 'Dra. Carla Santos', 'R$ 35/uso', 'Previsão de demanda por especialidade usando machine learning avançado')" class="w-full text-sm bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white px-3 py-2 rounded-lg transition-all duration-200 font-medium shadow-sm">
                            <i class="fas fa-credit-card mr-2"></i>Comprar e Usar
                        </button>
                    </div>

                    <!-- Otimização de Receita - Dr. Roberto Lima -->
                    <div class="border border-gray-300 rounded-lg p-3 hover:shadow-md transition-shadow bg-gray-50 relative">
                        <div class="absolute top-2 right-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white text-xs px-2 py-1 rounded-full font-semibold shadow-sm">
                            PRO
                        </div>
                        <div class="flex items-center mb-2">
                            <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                                <i class="fas fa-chart-pie text-blue-600"></i>
                            </div>
                            <h3 class="font-medium text-gray-900">Otimização de Receita</h3>
                        </div>
                        <p class="text-sm text-gray-600 mb-3">Estratégias avançadas para maximizar receita por paciente e procedimento</p>
                        <div class="flex items-center justify-between mb-2">
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                    Financeiro
                                </span>
                                <span class="text-lg font-bold text-blue-600">R$ 40/uso</span>
                            </div>
                            <div class="flex items-center bg-green-50 px-3 py-1 rounded-full border border-green-200">
                                <span class="text-xs font-bold text-green-700">Dr. Roberto Lima</span>
                                <span class="ml-1 text-green-600 font-bold">✓</span>
                            </div>
                        </div>
                        <div class="mb-3">
                            <span class="text-xs text-blue-600 font-medium">Verificado por AmigoTech</span>
                        </div>
                        <button onclick="openPremiumCheckout('Otimização de Receita', 'Dr. Roberto Lima', 'R$ 40/uso', 'Estratégias avançadas para maximizar receita por paciente e procedimento')" class="w-full text-sm bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white px-3 py-2 rounded-lg transition-all duration-200 font-medium shadow-sm">
                            <i class="fas fa-credit-card mr-2"></i>Comprar e Usar
                        </button>
                    </div>

                    <!-- Análise de Risco Clínico - Dr. Fernando Oliveira -->
                    <div class="border border-gray-300 rounded-lg p-3 hover:shadow-md transition-shadow bg-gray-50 relative">
                        <div class="absolute top-2 right-2 bg-blue-600 text-white text-xs px-2 py-0.5 rounded font-medium">
                            PRO
                        </div>
                        <div class="flex items-center mb-2">
                            <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                                <i class="fas fa-shield-alt text-blue-600"></i>
                            </div>
                            <h3 class="font-medium text-gray-900">Análise de Risco Clínico</h3>
                        </div>
                        <p class="text-sm text-gray-600 mb-3">Identificação e mitigação de riscos clínicos e operacionais</p>
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">Qualidade</span>
                            <div class="flex items-center">
                                <span class="text-xs text-gray-500">Dr. Fernando Oliveira</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between mb-3">
                            <span class="text-lg font-bold text-blue-600">R$ 30/uso</span>
                            <span class="text-xs text-gray-500">Especialista em Qualidade</span>
                        </div>
                        <button onclick="openPremiumCheckout('Análise de Risco Clínico', 'Dr. Fernando Oliveira', 'R$ 30/uso', 'Identificação e mitigação de riscos clínicos e operacionais')" class="w-full text-sm bg-blue-600 hover:bg-blue-700 text-white px-3 py-1.5 rounded transition-colors">
                            <i class="fas fa-credit-card mr-2"></i>Comprar e Usar
                        </button>
                    </div>

                    <!-- Segmentação Avançada de Pacientes - Dra. Ana Costa -->
                    <div class="border border-gray-300 rounded-lg p-3 hover:shadow-md transition-shadow bg-gray-50 relative">
                        <div class="absolute top-2 right-2 bg-blue-600 text-white text-xs px-2 py-0.5 rounded font-medium">
                            PRO
                        </div>
                        <div class="flex items-center mb-2">
                            <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                                <i class="fas fa-users-cog text-blue-600"></i>
                            </div>
                            <h3 class="font-medium text-gray-900">Segmentação Avançada de Pacientes</h3>
                        </div>
                        <p class="text-sm text-gray-600 mb-3">Clustering inteligente para personalização de atendimento e marketing</p>
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">Marketing</span>
                            <div class="flex items-center">
                                <span class="text-xs text-gray-500">Dra. Ana Costa</span>
                                <span class="ml-1 text-blue-600">✓</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between mb-3">
                            <span class="text-lg font-bold text-blue-600">R$ 28/uso</span>
                            <span class="text-xs text-gray-500">Verificado por AmigoTech</span>
                        </div>
                        <button onclick="openPremiumCheckout('Segmentação Avançada de Pacientes', 'Dra. Ana Costa', 'R$ 28/uso', 'Clustering inteligente para personalização de atendimento e marketing')" class="w-full text-sm bg-blue-600 hover:bg-blue-700 text-white px-3 py-1.5 rounded transition-colors">
                            <i class="fas fa-credit-card mr-2"></i>Comprar e Usar
                        </button>
                    </div>

                    <!-- Análise de Eficiência Operacional - Dr. Marcos Silva -->
                    <div class="border border-gray-300 rounded-lg p-3 hover:shadow-md transition-shadow bg-gray-50 relative">
                        <div class="absolute top-2 right-2 bg-blue-600 text-white text-xs px-2 py-0.5 rounded font-medium">
                            PRO
                        </div>
                        <div class="flex items-center mb-2">
                            <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                                <i class="fas fa-cogs text-blue-600"></i>
                            </div>
                            <h3 class="font-medium text-gray-900">Análise de Eficiência Operacional</h3>
                        </div>
                        <p class="text-sm text-gray-600 mb-3">Otimização de processos e recursos para máxima eficiência</p>
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">Operacional</span>
                            <div class="flex items-center">
                                <span class="text-xs text-gray-500">Dr. Marcos Silva</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between mb-3">
                            <span class="text-lg font-bold text-blue-600">R$ 32/uso</span>
                            <span class="text-xs text-gray-500">Especialista em Processos</span>
                        </div>
                        <button onclick="openPremiumCheckout('Análise de Eficiência Operacional', 'Dr. Marcos Silva', 'R$ 32/uso', 'Otimização de processos e recursos para máxima eficiência')" class="w-full text-sm bg-blue-600 hover:bg-blue-700 text-white px-3 py-1.5 rounded transition-colors">
                            <i class="fas fa-credit-card mr-2"></i>Comprar e Usar
                        </button>
                    </div>
                </div>
            </div>

            <div class="p-4 border-t border-gray-200 flex justify-end">
                <button onclick="closeToolsModal()" class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 mr-2">
                    Fechar
                </button>
                <button class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
                    Adicionar à Maleta
                </button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script>
        // Variáveis globais
        let editor;
        let streamlitFrame;
        let streamlitError;
        let outputElement;
        let statusIndicator;
        let statusText;
        let spinner;
        let executeButton;
        let stopPreviewBtn;
        let outputTabBtn;
        let previewTabBtn;
        let chatInput;
        let sendButton;
        let chatContainer;
        let newChatButton;
        let toolboxItems;

        // Função para inicializar as variáveis
        function initializeVariables() {
            streamlitFrame = document.getElementById('streamlit-frame');
            streamlitError = document.getElementById('streamlit-error');
            outputElement = document.getElementById('output');
            statusIndicator = document.getElementById('status-indicator');
            statusText = document.getElementById('status-text');
            spinner = document.getElementById('spinner');
            executeButton = document.getElementById('execute-btn');
            stopPreviewBtn = document.getElementById('stop-preview-btn');
            outputTabBtn = document.getElementById('output-tab-btn');
            previewTabBtn = document.getElementById('preview-tab-btn');
            chatInput = document.getElementById('chat-input');
            sendButton = document.getElementById('send-btn');
            chatContainer = document.getElementById('chat-container');
            newChatButton = document.getElementById('new-chat-btn');
            toolboxItems = document.getElementById('toolbox-items');

            console.log('Variáveis inicializadas com sucesso!');
        }

        // Dados para a maleta de ferramentas - KPIs e análises do dashboard
        const toolboxData = [
            // KPIs Financeiros
            {
                id: "kpi-1",
                name: "Análise de Faturamento",
                description: "Analisa a evolução do faturamento mensal e identifica tendências",
                category: "financeiro",
                icon: "fa-chart-line",
                prompt: "Analisar a evolução do faturamento mensal dos últimos 12 meses e identificar tendências sazonais ou padrões de crescimento/queda. Inclua um gráfico de linha mostrando a evolução mensal e um gráfico de barras comparando o faturamento por trimestre. Para o gráfico de linha, use o formato: ```chart { \"type\": \"line\", \"labels\": [\"Jan\", \"Fev\", \"Mar\", \"Abr\", \"Mai\", \"Jun\", \"Jul\", \"Ago\", \"Set\", \"Out\", \"Nov\", \"Dez\"], \"datasets\": [{\"label\": \"Faturamento Mensal\", \"data\": [100, 120, 130, 110, 140, 150, 160, 170, 150, 140, 130, 180]}] } ```. Para o gráfico de barras, use: ```chart { \"type\": \"bar\", \"labels\": [\"1º Trimestre\", \"2º Trimestre\", \"3º Trimestre\", \"4º Trimestre\"], \"datasets\": [{\"label\": \"Faturamento Trimestral\", \"data\": [350, 400, 480, 450]}] } ```."
            },
            {
                id: "kpi-2",
                name: "Rentabilidade por Procedimento",
                description: "Calcula e compara a rentabilidade dos diferentes procedimentos",
                category: "financeiro",
                icon: "fa-percentage",
                prompt: "Calcular a rentabilidade dos 10 procedimentos mais realizados e identificar quais são os mais lucrativos. Inclua um gráfico de barras horizontais ordenado por rentabilidade e um gráfico de dispersão relacionando volume e rentabilidade. Certifique-se de formatar a resposta com os dados do gráfico no formato ```chart {...} ``` para que seja renderizado diretamente no chat."
            },
            {
                id: "kpi-3",
                name: "Fluxo de Caixa Projetado",
                description: "Projeta o fluxo de caixa para os próximos meses",
                category: "financeiro",
                icon: "fa-money-bill-wave",
                prompt: "Projetar o fluxo de caixa para os próximos 3 meses com base nos dados históricos dos últimos 12 meses. Inclua um gráfico de área mostrando receitas, despesas e saldo, e um gráfico de linha com a projeção e intervalo de confiança. Certifique-se de formatar a resposta com os dados do gráfico no formato ```chart {...} ``` para que seja renderizado diretamente no chat."
            },
            {
                id: "kpi-4",
                name: "Análise de Inadimplência",
                description: "Analisa a taxa de inadimplência e seu impacto financeiro",
                category: "financeiro",
                icon: "fa-exclamation-triangle",
                prompt: "Analisar a taxa de inadimplência dos últimos 6 meses, identificar padrões e calcular o impacto financeiro. Inclua um gráfico de linha mostrando a evolução da inadimplência e um gráfico de pizza com a distribuição por faixa de atraso. Certifique-se de formatar a resposta com os dados do gráfico no formato ```chart {...} ``` para que seja renderizado diretamente no chat."
            },

            // KPIs Operacionais
            {
                id: "kpi-5",
                name: "Taxa de Ocupação da Agenda",
                description: "Analisa a taxa de ocupação da agenda por período, profissional ou unidade",
                category: "operacional",
                icon: "fa-calendar-check",
                prompt: "Analisar a taxa de ocupação da agenda por dia da semana e horário, identificando os períodos de maior e menor ocupação. Inclua um mapa de calor (heatmap) mostrando a ocupação por dia e horário, e um gráfico de barras comparando a ocupação média por período do dia. Certifique-se de formatar a resposta com os dados do gráfico no formato ```chart {...} ``` para que seja renderizado diretamente no chat."
            },
            {
                id: "kpi-6",
                name: "Análise de Séries Temporais (Y/Y)",
                description: "Análise comparativa ano a ano de métricas clínicas",
                category: "operacional",
                icon: "fa-chart-line",
                prompt: "Realize uma análise de séries temporais comparando os dados do ano atual com o ano anterior (Year over Year). Inclua tendências, sazonalidade e insights preditivos. Mostre gráficos de linha com comparação Y/Y para faturamento, número de pacientes e satisfação. Use o formato: ```chart { \"type\": \"line\", \"labels\": [\"Jan\", \"Fev\", \"Mar\"], \"datasets\": [{\"label\": \"2023\", \"data\": [100, 120, 110]}, {\"label\": \"2024\", \"data\": [110, 130, 125]}] } ```."
            },
            {
                id: "kpi-7",
                name: "Taxa de Cancelamento",
                description: "Analisa a taxa de cancelamento de agendamentos e seus motivos",
                category: "operacional",
                icon: "fa-calendar-times",
                prompt: "Analisar a taxa de cancelamento de agendamentos dos últimos 3 meses, identificando os principais motivos e propondo ações para redução. Inclua um gráfico de linha mostrando a evolução da taxa de cancelamento e um gráfico de pizza com os principais motivos. Certifique-se de formatar a resposta com os dados do gráfico no formato ```chart {...} ``` para que seja renderizado diretamente no chat."
            },
            {
                id: "kpi-8",
                name: "Produtividade por Profissional",
                description: "Compara a produtividade entre diferentes profissionais",
                category: "operacional",
                icon: "fa-user-md",
                prompt: "Comparar a produtividade dos profissionais em termos de número de atendimentos, faturamento gerado e satisfação dos pacientes. Inclua um gráfico de radar comparando os profissionais em múltiplas métricas e um gráfico de barras empilhadas mostrando a distribuição de atendimentos por tipo. Certifique-se de formatar a resposta com os dados do gráfico no formato ```chart {...} ``` para que seja renderizado diretamente no chat."
            },

            // KPIs de Pacientes
            {
                id: "kpi-9",
                name: "Jornada do Paciente",
                description: "Analisa a jornada do paciente desde o primeiro contato até o pós-atendimento",
                category: "paciente",
                icon: "fa-route",
                prompt: "Analisar a jornada do paciente, identificando os pontos de maior satisfação e os gargalos que precisam ser melhorados. Inclua um gráfico de barras duplo mostrando tempo médio e satisfação por etapa, e um diagrama de fluxo (sankey) mostrando o caminho dos pacientes pelas etapas. Certifique-se de formatar a resposta com os dados do gráfico no formato ```chart {...} ``` para que seja renderizado diretamente no chat."
            },
            {
                id: "kpi-10",
                name: "Taxa de Retorno",
                description: "Calcula a taxa de retorno dos pacientes e fatores associados",
                category: "paciente",
                icon: "fa-redo",
                prompt: "Calcular a taxa de retorno dos pacientes nos últimos 6 meses e identificar os fatores que influenciam o retorno. Inclua um gráfico de linha mostrando a evolução da taxa de retorno e um gráfico de barras comparando a taxa por segmento de paciente. Certifique-se de formatar a resposta com os dados do gráfico no formato ```chart {...} ``` para que seja renderizado diretamente no chat."
            },
            {
                id: "kpi-11",
                name: "Análise de NPS",
                description: "Analisa o Net Promoter Score (NPS) e fatores que o influenciam",
                category: "paciente",
                icon: "fa-smile",
                prompt: "Analisar o NPS por profissional, procedimento e unidade, identificando os fatores que mais impactam a satisfação dos pacientes. Inclua um gráfico de medidor (gauge) mostrando o NPS atual e um gráfico de barras horizontais comparando o NPS por profissional. Certifique-se de formatar a resposta com os dados do gráfico no formato ```chart {...} ``` para que seja renderizado diretamente no chat."
            },
            {
                id: "kpi-12",
                name: "Previsão de Abandono",
                description: "Identifica pacientes com risco de abandono de tratamento",
                category: "paciente",
                icon: "fa-user-minus",
                prompt: "Identificar pacientes com alto risco de abandono de tratamento e propor estratégias de retenção. Inclua um gráfico de pizza mostrando a proporção de pacientes em risco e um gráfico de dispersão relacionando tempo desde o último atendimento e probabilidade de abandono. Certifique-se de formatar a resposta com os dados do gráfico no formato ```chart {...} ``` para que seja renderizado diretamente no chat."
            },

            // KPIs de Marketing
            {
                id: "kpi-13",
                name: "Conversão de Leads",
                description: "Analisa a taxa de conversão de leads em pacientes",
                category: "marketing",
                icon: "fa-funnel-dollar",
                prompt: "Analisar a taxa de conversão de leads em pacientes por origem e propor melhorias no processo de captação. Inclua um gráfico de funil mostrando as etapas de conversão e um gráfico de barras comparando a taxa de conversão por origem do lead. Certifique-se de formatar a resposta com os dados do gráfico no formato ```chart {...} ``` para que seja renderizado diretamente no chat."
            },
            {
                id: "kpi-14",
                name: "ROI de Campanhas",
                description: "Calcula o retorno sobre investimento (ROI) das campanhas de marketing",
                category: "marketing",
                icon: "fa-bullseye",
                prompt: "Calcular o ROI das campanhas de marketing dos últimos 3 meses e identificar as mais eficientes. Inclua um gráfico de barras ordenado por ROI e um gráfico de dispersão relacionando investimento e retorno por campanha. Certifique-se de formatar a resposta com os dados do gráfico no formato ```chart {...} ``` para que seja renderizado diretamente no chat."
            },
            {
                id: "kpi-15",
                name: "Segmentação de Clientes",
                description: "Segmenta a base de clientes para campanhas direcionadas",
                category: "marketing",
                icon: "fa-users",
                prompt: "Segmentar a base de clientes em 5 grupos com base em valor, frequência e recência para campanhas direcionadas. Inclua um gráfico de dispersão 3D ou múltiplos gráficos 2D mostrando os clusters e um gráfico de radar comparando as características de cada segmento. Certifique-se de formatar a resposta com os dados do gráfico no formato ```chart {...} ``` para que seja renderizado diretamente no chat."
            },
            {
                id: "kpi-16",
                name: "Análise de Canais",
                description: "Analisa a eficácia dos diferentes canais de marketing",
                category: "marketing",
                icon: "fa-random",
                prompt: "Analisar a eficácia dos diferentes canais de marketing em termos de custo por lead, taxa de conversão e valor do cliente. Inclua um gráfico de barras múltiplas comparando as métricas por canal e um gráfico de linha mostrando a evolução do desempenho dos principais canais ao longo do tempo. Certifique-se de formatar a resposta com os dados do gráfico no formato ```chart {...} ``` para que seja renderizado diretamente no chat."
            }
        ];

        // Função para carregar dados mockados
        let mockData = null;

        async function loadMockData() {
            try {
                const response = await fetch('/static/mock_data.json');
                mockData = await response.json();
                console.log('Dados mockados carregados com sucesso:', mockData);
                return mockData;
            } catch (error) {
                console.error('Erro ao carregar dados mockados:', error);
                return null;
            }
        }

        // Função para obter dados mockados para o chat
        function get_mock_data(dataType) {
            if (!mockData) {
                console.error('Dados mockados não estão disponíveis');
                return null;
            }

            return mockData[dataType] || null;
        }

        // Inicializar o Monaco Editor
        function initializeMonacoEditor() {
            console.log('Inicializando Monaco Editor...');

            // Configurar o RequireJS para o Monaco Editor
            require.config({
                paths: { 'vs': 'https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.33.0/min/vs' },
                // Ignorar módulos já carregados globalmente
                ignoreDuplicateModules: true
            });

            // Carregar o Monaco Editor
            require(['vs/editor/editor.main'], function() {
                // Código de exemplo do Python
                const pythonExampleCode = `# -*- coding: utf-8 -*-
# Bem-vindo ao Amigo DataStudio!

# Voce pode escrever codigo Python aqui
# e executa-lo diretamente no navegador.

# Exemplo de analise de dados:
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# Criar dados simulados de faturamento mensal
meses = ["Jan", "Fev", "Mar", "Abr", "Mai", "Jun",
         "Jul", "Ago", "Set", "Out", "Nov", "Dez"]
faturamento = [320000, 310000, 315000, 340000, 360000, 375000,
               390000, 400000, 380000, 390000, 410000, 430000]

# Criar DataFrame
df = pd.DataFrame({
    "Mes": meses,
    "Faturamento": faturamento
})

# Exibir os dados
print(df)

# Calcular estatisticas basicas
print("\\nEstatisticas de Faturamento:")
print(df["Faturamento"].describe())

# Calcular crescimento mes a mes
df["Crescimento"] = df["Faturamento"].pct_change() * 100
print("\\nCrescimento mes a mes (%):")
print(df[["Mes", "Crescimento"]].iloc[1:])`;

                // Criar o editor com o código de exemplo
                try {
                    window.editor = monaco.editor.create(document.getElementById('monaco-editor'), {
                        value: pythonExampleCode,
                        language: 'python',
                        theme: 'vs-dark',
                        automaticLayout: true,
                        minimap: { enabled: true },
                        scrollBeyondLastLine: false,
                        fontSize: 14,
                        lineNumbers: 'on',
                        tabSize: 4
                    });

                    editor = window.editor;
                    console.log('Monaco Editor inicializado com sucesso');
                } catch (e) {
                    console.error('Erro ao inicializar Monaco Editor:', e);
                }

                // Renderizar a maleta de ferramentas após o editor estar pronto
                renderToolbox();

                // Notificar que o editor está pronto
                document.dispatchEvent(new Event('monacoeditorloaded'));
            });
        }

        // Inicializar o Monaco Editor após o carregamento da página
        document.addEventListener('DOMContentLoaded', function() {
            // Esperar um pouco para garantir que as bibliotecas sejam carregadas primeiro
            setTimeout(function() {
                initializeMonacoEditor();
            }, 1000);
        });

        // Função para mostrar toast
        function showToast(message, type = 'info') {
            const toast = document.getElementById('toast');
            toast.textContent = message;
            toast.className = 'toast';

            if (type === 'success') {
                toast.classList.add('bg-green-500', 'text-white');
            } else if (type === 'error') {
                toast.classList.add('bg-red-500', 'text-white');
            } else {
                toast.classList.add('bg-blue-500', 'text-white');
            }

            toast.classList.remove('hidden');

            // Esconder o toast após 3 segundos
            setTimeout(() => {
                toast.classList.add('hidden');
            }, 3000);
        }

        // Função para alternar entre abas
        function switchTab(tabName) {
            const tabs = document.querySelectorAll('.tab-content');
            const buttons = document.querySelectorAll('.tab-button');

            tabs.forEach(tab => tab.classList.remove('active'));
            buttons.forEach(button => button.classList.remove('active'));

            document.getElementById(`${tabName}-tab`).classList.add('active');
            document.getElementById(`${tabName}-tab-btn`).classList.add('active');
        }

        // Função para executar código
        async function executeCode() {
            const code = editor.getValue();  // Obter o código do Monaco Editor

            // Verificar se o código está vazio
            if (!code.trim()) {
                showToast('Por favor, digite algum código para executar', 'error');
                return;
            }

            // Atualizar status e mostrar spinner
            statusIndicator.className = 'inline-block w-3 h-3 bg-yellow-500 rounded-full mr-2';
            statusText.textContent = 'Executando...';
            spinner.classList.remove('hidden');
            executeButton.disabled = true;

            // Limpar output anterior
            outputElement.textContent = '';

            // Verificar se o código é Streamlit
            const isStreamlit = code.includes('import streamlit') || code.includes('import streamlit as st');

            try {
                if (isStreamlit) {
                    try {
                        // Executar código Streamlit
                        const response = await fetch('/amigostudio-pro/execute_streamlit', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ code })
                        });

                        const data = await response.json();

                        if (data.error) {
                            streamlitError.textContent = 'Erro ao iniciar o Streamlit: ' + data.error;
                            streamlitError.classList.remove('hidden');
                            showToast('Erro ao executar o código Streamlit', 'error');
                        } else {
                            switchTab('preview');
                            streamlitFrame.style.display = 'block';
                            streamlitFrame.src = `http://localhost:${data.port}`;
                            streamlitError.classList.add('hidden');
                            showToast('Aplicação Streamlit iniciada com sucesso', 'success');
                        }
                    } catch (error) {
                        // Modo de demonstração - simular execução do Streamlit com visualização
                        console.log('Usando modo de demonstração para Streamlit');

                        // Verificar se o código contém análise de produtividade
                        if (code.includes('produtividade') || code.includes('profissionais')) {
                            // Criar uma visualização HTML para simular o Streamlit
                            const htmlOutput = `
                            <html>
                            <head>
                                <style>
                                    body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; padding: 20px; background-color: #f9f9f9; }
                                    h1, h2, h3 { color: #1E3A8A; }
                                    .metric { background-color: white; padding: 15px; border-radius: 5px; margin-bottom: 15px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
                                    .metric-value { font-size: 24px; font-weight: bold; color: #2563EB; }
                                    .metric-label { font-size: 14px; color: #6B7280; }
                                    .chart { background-color: white; padding: 15px; border-radius: 5px; margin-bottom: 20px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
                                    .chart-title { font-size: 18px; margin-bottom: 10px; color: #1F2937; }
                                    .table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                                    .table th { background-color: #E5E7EB; padding: 10px; text-align: left; }
                                    .table td { padding: 10px; border-bottom: 1px solid #E5E7EB; }
                                    .conclusion { background-color: #EFF6FF; padding: 15px; border-radius: 5px; border-left: 4px solid #3B82F6; }
                                </style>
                            </head>
                            <body>
                                <h1>Análise de Produtividade dos Profissionais</h1>

                                <div style="display: flex; justify-content: space-between;">
                                    <div class="metric" style="flex: 1; margin-right: 10px;">
                                        <div class="metric-label">Média de Atendimentos</div>
                                        <div class="metric-value">180</div>
                                        <div style="color: green; font-size: 12px;">+30 vs mínimo</div>
                                    </div>
                                    <div class="metric" style="flex: 1; margin-right: 10px;">
                                        <div class="metric-label">Média de Faturamento</div>
                                        <div class="metric-value">R$ 45.000,00</div>
                                        <div style="color: green; font-size: 12px;">+R$ 7.500,00 vs mínimo</div>
                                    </div>
                                    <div class="metric" style="flex: 1;">
                                        <div class="metric-label">Média de Satisfação</div>
                                        <div class="metric-value">91.6%</div>
                                        <div style="color: green; font-size: 12px;">+3.6% vs mínimo</div>
                                    </div>
                                </div>

                                <h2>Comparativo de Desempenho (Gráfico de Radar)</h2>
                                <div class="chart">
                                    <img src="https://miro.medium.com/v2/resize:fit:1400/1*Yw3BgGdUVvXu-b9-lcvCVA.png" style="width: 100%; max-height: 400px; object-fit: contain;">
                                </div>

                                <h2>Distribuição de Atendimentos por Tipo</h2>
                                <div class="chart">
                                    <img src="https://miro.medium.com/v2/resize:fit:1400/1*YQQmykZ-l_f2SBWZ-UnrrA.png" style="width: 100%; max-height: 400px; object-fit: contain;">
                                </div>

                                <h2>Dados Detalhados por Profissional</h2>
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Profissional</th>
                                            <th>Atendimentos</th>
                                            <th>Faturamento</th>
                                            <th>Satisfação</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>Dr. Silva</td>
                                            <td>180</td>
                                            <td>R$ 45.000,00</td>
                                            <td>92%</td>
                                        </tr>
                                        <tr>
                                            <td>Dra. Santos</td>
                                            <td>210</td>
                                            <td>R$ 52.500,00</td>
                                            <td>88%</td>
                                        </tr>
                                        <tr>
                                            <td>Dr. Oliveira</td>
                                            <td>150</td>
                                            <td>R$ 37.500,00</td>
                                            <td>95%</td>
                                        </tr>
                                        <tr>
                                            <td>Dra. Costa</td>
                                            <td>195</td>
                                            <td>R$ 48.750,00</td>
                                            <td>90%</td>
                                        </tr>
                                        <tr>
                                            <td>Dr. Pereira</td>
                                            <td>165</td>
                                            <td>R$ 41.250,00</td>
                                            <td>93%</td>
                                        </tr>
                                    </tbody>
                                </table>

                                <h2>Conclusões e Recomendações</h2>
                                <div class="conclusion">
                                    <p><strong>• A Dra. Santos</strong> apresenta o maior número de atendimentos (210) e faturamento (R$ 52.500,00), mas não a maior satisfação.</p>
                                    <p><strong>• O Dr. Oliveira</strong> tem a maior satisfação (95%), mas o menor número de atendimentos (150) e faturamento (R$ 37.500,00).</p>
                                    <p><strong>• A Dra. Costa</strong> tem um bom equilíbrio entre volume de atendimentos, faturamento e satisfação.</p>
                                    <p><strong>Recomendações:</strong></p>
                                    <p>• Analisar as práticas do <strong>Dr. Oliveira</strong> para entender como ele alcança alta satisfação e aplicar essas práticas aos demais profissionais.</p>
                                    <p>• Verificar se o alto volume de atendimentos da <strong>Dra. Santos</strong> não está impactando negativamente a satisfação dos pacientes.</p>
                                    <p>• Considerar a implementação de um programa de incentivo que equilibre volume de atendimentos e satisfação dos pacientes.</p>
                                </div>
                            </body>
                            </html>`;

                            // Criar um blob com o HTML
                            const blob = new Blob([htmlOutput], { type: 'text/html' });
                            const url = URL.createObjectURL(blob);

                            // Mostrar no iframe
                            switchTab('preview');
                            streamlitFrame.style.display = 'block';
                            streamlitFrame.src = url;
                            streamlitError.classList.add('hidden');
                            showToast('Visualização de produtividade gerada com sucesso', 'success');
                        } else {
                            // Para outros tipos de análise, mostrar saída de texto
                            outputElement.textContent = 'Modo de demonstração ativado. Executando código localmente:\n\n';
                            outputElement.textContent += await simulateCodeExecution(code);
                            switchTab('output');
                            showToast('Código executado em modo de demonstração', 'info');
                        }
                    }
                } else {
                    try {
                        // Executar código Python normal
                        const response = await fetch('/amigostudio-pro/execute', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ code })
                        });

                        const data = await response.json();

                        // Mostrar output
                        if (data.stdout) {
                            outputElement.textContent += data.stdout;
                        }

                        if (data.stderr) {
                            outputElement.textContent += '\n' + data.stderr;
                        }

                        // Mostrar mensagem de sucesso ou erro
                        if (data.returncode === 0) {
                            showToast('Código executado com sucesso', 'success');
                        } else {
                            showToast('Erro ao executar o código', 'error');
                        }

                        // Mudar para a aba de output
                        switchTab('output');
                    } catch (error) {
                        // Modo de demonstração - simular execução do código
                        console.log('Usando modo de demonstração para execução de código');
                        outputElement.textContent = 'Modo de demonstração ativado. Executando código localmente:\n\n';
                        outputElement.textContent += await simulateCodeExecution(code);
                        switchTab('output');
                        showToast('Código executado em modo de demonstração', 'info');
                    }
                }
            } catch (error) {
                outputElement.textContent = 'Erro ao executar o código: ' + error.message;
                showToast('Erro ao executar o código', 'error');
            } finally {
                // Restaurar status
                statusIndicator.className = 'inline-block w-3 h-3 bg-green-500 rounded-full mr-2';
                statusText.textContent = 'Pronto';
                spinner.classList.add('hidden');
                executeButton.disabled = false;
            }
        }

        // Função para simular a execução de código em modo de demonstração
        async function simulateCodeExecution(code) {
            // Simular saída de código Python
            let output = '';

            // Verificar se o código importa pandas
            if (code.includes('import pandas') || code.includes('from pandas')) {
                output += "Pandas importado com sucesso.\n";
            }

            // Verificar se o código importa numpy
            if (code.includes('import numpy') || code.includes('from numpy')) {
                output += "NumPy importado com sucesso.\n";
            }

            // Verificar se o código importa matplotlib
            if (code.includes('import matplotlib') || code.includes('from matplotlib')) {
                output += "Matplotlib importado com sucesso.\n";
            }

            // Verificar se o código cria um DataFrame
            if (code.includes('pd.DataFrame') || code.includes('pandas.DataFrame')) {
                output += "\nDataFrame criado com sucesso.\n";

                // Extrair dados do mockData se disponível
                if (mockData) {
                    if (code.includes('faturamento')) {
                        output += "\n   Mes  Faturamento\n";
                        output += "0  Jan      320000\n";
                        output += "1  Fev      310000\n";
                        output += "2  Mar      315000\n";
                        output += "...\n";
                        output += "11 Dez      430000\n";
                    } else if (code.includes('paciente') || code.includes('jornada')) {
                        output += "\n   Etapa  Satisfacao  Tempo_Medio\n";
                        output += "0  Agendamento        85           5\n";
                        output += "1  Recepção           90           3\n";
                        output += "2  Espera             75          18\n";
                        output += "...\n";
                    }
                }
            }

            // Verificar se o código faz análise estatística
            if (code.includes('.describe()')) {
                output += "\nEstatísticas descritivas:\n";
                output += "count      12.000000\n";
                output += "mean    368750.000000\n";
                output += "std      40826.917886\n";
                output += "min     310000.000000\n";
                output += "25%     340000.000000\n";
                output += "50%     370000.000000\n";
                output += "75%     395000.000000\n";
                output += "max     430000.000000\n";
            }

            // Verificar se o código calcula crescimento
            if (code.includes('pct_change()')) {
                output += "\nCrescimento mês a mês (%):\n";
                output += "   Mes  Crescimento\n";
                output += "1  Fev      -3.12\n";
                output += "2  Mar       1.61\n";
                output += "3  Abr       7.94\n";
                output += "...\n";
            }

            // Se não houver saída específica, mostrar mensagem genérica
            if (!output) {
                output = "Código executado com sucesso em modo de demonstração.\n";
                output += "Nota: Esta é uma simulação para fins de demonstração. Os resultados reais podem variar.\n";
            }

            return output;
        }

        // Função para parar o preview do Streamlit
        async function stopPreview() {
            try {
                // Obter a URL do iframe
                const url = streamlitFrame.src;
                const port = url.split(':')[2].split('/')[0];

                // Chamar a API para parar o Streamlit
                const response = await fetch('/amigostudio-pro/stop_streamlit', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ port })
                });

                const data = await response.json();

                if (data.error) {
                    showToast(data.error, 'error');
                } else {
                    // Limpar o iframe
                    streamlitFrame.src = '';
                    streamlitFrame.style.display = 'none';
                    streamlitError.classList.add('hidden');
                    showToast('Preview do Streamlit parado com sucesso', 'success');
                }
            } catch (error) {
                showToast('Erro ao parar o preview: ' + error.message, 'error');
            }
        }

        // Função para enviar mensagem para o chat
        async function sendMessage() {
            const message = chatInput.value.trim();

            if (!message) {
                return;
            }

            // Adicionar mensagem do usuário ao chat
            addMessageToChat('user', message);

            // Limpar input
            chatInput.value = '';

            try {
                // Mostrar indicador de digitação
                const typingIndicator = document.createElement('div');
                typingIndicator.className = 'chat-message assistant-message';
                typingIndicator.innerHTML = '<p><i>Digitando...</i></p>';
                typingIndicator.id = 'typing-indicator';
                chatContainer.appendChild(typingIndicator);
                chatContainer.scrollTop = chatContainer.scrollHeight;

                // Verificar se estamos em modo de demonstração (usando dados mockados)
                let data;

                try {
                    // Tentar enviar mensagem para o backend
                    const response = await fetch('/amigostudio-pro/chat', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ message })
                    });

                    data = await response.json();
                } catch (error) {
                    console.log('Usando dados mockados para a demonstração');

                    // Simular resposta com dados mockados
                    data = generateMockResponse(message);
                }

                // Remover indicador de digitação
                const indicator = document.getElementById('typing-indicator');
                if (indicator) {
                    chatContainer.removeChild(indicator);
                }

                if (data.error) {
                    addMessageToChat('assistant', 'Desculpe, ocorreu um erro: ' + data.error);
                } else {
                    // Adicionar resposta ao chat com gráfico, se houver
                    addMessageToChat('assistant', data.text, data.chart);

                    // Se houver código, adicionar ao editor
                    if (data.code) {
                        editor.setValue(data.code);
                        showToast('Código gerado pela IA adicionado ao editor', 'success');
                    }

                    // Se houver gráfico, mostrar toast informativo
                    if (data.chart) {
                        showToast('Visualização gerada com base na análise', 'info');
                    }
                }
            } catch (error) {
                // Remover indicador de digitação
                const indicator = document.getElementById('typing-indicator');
                if (indicator) {
                    chatContainer.removeChild(indicator);
                }

                addMessageToChat('assistant', 'Desculpe, ocorreu um erro: ' + error.message);
            }
        }

        // Função para copiar código para a área de transferência
        function copyCodeToClipboard(codeId) {
            const codeElement = document.getElementById(codeId);
            if (!codeElement) {
                showToast('Elemento de código não encontrado', 'error');
                return;
            }

            const code = codeElement.textContent;

            navigator.clipboard.writeText(code).then(() => {
                showToast('Código copiado para a área de transferência!', 'success');
            }).catch(err => {
                console.error('Erro ao copiar código:', err);
                showToast('Erro ao copiar código', 'error');
            });
        }

        // Função para executar código no editor
        function runCodeInEditor(codeId) {
            const codeElement = document.getElementById(codeId);
            if (!codeElement) {
                showToast('Elemento de código não encontrado', 'error');
                return;
            }

            const code = codeElement.textContent;

            // Inserir o código no editor
            if (editor) {
                editor.setValue(code);
                showToast('Código adicionado ao editor!', 'success');

                // Executar automaticamente após um pequeno delay
                setTimeout(() => {
                    executeCode();
                }, 500);
            } else {
                showToast('Editor não está disponível', 'error');
            }
        }

        // Função para processar a resposta do chat
        function processResponse(response) {
            // Verificar se a resposta contém código Python
            if (response.includes('```python') && response.includes('```')) {
                // Extrair o código Python
                const codeStart = response.indexOf('```python') + 10;
                const codeEnd = response.indexOf('```', codeStart);
                const code = response.substring(codeStart, codeEnd).trim();

                // Inserir o código no editor
                editor.setValue(code);

                // Executar o código automaticamente
                executeCode();
            }

            // Usar o processador de gráficos para processar e renderizar gráficos
            if (window.ChartProcessor && typeof window.ChartProcessor.processChartResponse === 'function' &&
                response.includes('```chart') && response.includes('```')) {
                return window.ChartProcessor.processChartResponse(response);
            }

            return response;
        }

        // Função para adicionar mensagem ao chat
        function addMessageToChat(role, content, chartData = null) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `chat-message ${role}-message`;

            // Processar markdown no conteúdo
            let processedContent = processResponse(content);

            // Processar blocos de código Python
            if (content.includes('```python')) {
                const parts = content.split('```python');
                processedContent = parts[0];

                for (let i = 1; i < parts.length; i++) {
                    const codeParts = parts[i].split('```');
                    const code = codeParts[0].trim();
                    const codeId = `code-block-${Date.now()}-${i}`;

                    processedContent += `
                        <div class="code-block-container">
                            <div class="code-block-header">
                                <span class="flex items-center">
                                    <i class="fab fa-python mr-2 text-blue-400"></i>
                                    <span class="text-sm font-medium text-gray-300">Código Python</span>
                                </span>
                                <div class="flex items-center space-x-2">
                                    <button class="copy-code-btn" onclick="copyCodeToClipboard('${codeId}')">
                                        <i class="fas fa-copy mr-1"></i>Copiar
                                    </button>
                                    <button class="run-code-btn" onclick="runCodeInEditor('${codeId}')">
                                        <i class="fas fa-play mr-1"></i>Executar
                                    </button>
                                </div>
                            </div>
                            <div class="code-block-content" id="${codeId}">${code}</div>
                        </div>
                    `;

                    if (codeParts.length > 1) {
                        processedContent += codeParts[1];
                    }
                }
            }

            // Processar blocos de gráficos usando o processador de gráficos
            if (content.includes('```chart') && window.ChartProcessor && typeof window.ChartProcessor.processChartResponse === 'function') {
                // Usar o processador de gráficos para processar o conteúdo
                processedContent = window.ChartProcessor.processChartResponse(content);
            }
            // Fallback para o método antigo caso o processador não esteja disponível
            else if (content.includes('```chart')) {
                const parts = content.split('```chart');
                processedContent = parts[0];

                for (let i = 1; i < parts.length; i++) {
                    const chartParts = parts[i].split('```');
                    const chartJson = chartParts[0].trim();

                    try {
                        // Verificar se o JSON começa com { e termina com }
                        let validChartJson = chartJson;
                        if (!chartJson.startsWith('{') || !chartJson.endsWith('}')) {
                            console.warn('Formato JSON inválido, tentando corrigir...');

                            // Tentar extrair apenas a parte JSON válida
                            const jsonStartIndex = chartJson.indexOf('{');
                            const jsonEndIndex = chartJson.lastIndexOf('}') + 1;

                            if (jsonStartIndex >= 0 && jsonEndIndex > jsonStartIndex) {
                                validChartJson = chartJson.substring(jsonStartIndex, jsonEndIndex);
                            } else {
                                throw new Error('Não foi possível encontrar um objeto JSON válido');
                            }
                        }

                        const chartData = JSON.parse(validChartJson);
                        const chartId = 'chart-' + Date.now() + '-' + i;

                        // Adicionar placeholder para o gráfico
                        processedContent += `<div id="${chartId}-container" class="chart-container mt-4 mb-2"></div>`;

                        // Renderizar o gráfico após adicionar ao DOM
                        setTimeout(() => {
                            const chartContainer = document.getElementById(`${chartId}-container`);
                            if (chartContainer) {
                                // Adicionar título do gráfico
                                if (chartData.title) {
                                    const chartTitle = document.createElement('h3');
                                    chartTitle.className = 'text-center text-gray-700 font-medium mb-2';
                                    chartTitle.textContent = chartData.title;
                                    chartContainer.appendChild(chartTitle);
                                }

                                const canvas = document.createElement('canvas');
                                canvas.id = chartId;
                                chartContainer.appendChild(canvas);

                                renderChart(chartId, chartData);

                                // Adicionar legenda ou descrição do gráfico, se necessário
                                if (chartData.description) {
                                    const chartDescription = document.createElement('p');
                                    chartDescription.className = 'text-xs text-gray-500 mt-2 text-center';
                                    chartDescription.textContent = chartData.description;
                                    chartContainer.appendChild(chartDescription);
                                }
                            }
                        }, 100);
                    } catch (error) {
                        console.error('Erro ao processar gráfico:', error);
                        processedContent += `<div class="error-message p-3 bg-red-50 text-red-600 rounded-md">Erro ao processar gráfico: ${error.message}</div>`;
                    }

                    if (chartParts.length > 1) {
                        processedContent += chartParts[1];
                    }
                }
            }

            // Processar formatação markdown básica
            // Negrito
            processedContent = processedContent.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

            // Itálico
            processedContent = processedContent.replace(/\*(.*?)\*/g, '<em>$1</em>');

            // Listas
            processedContent = processedContent.replace(/- (.*?)(?:\n|$)/g, '<li>$1</li>');
            processedContent = processedContent.replace(/<li>(.*?)<\/li>(?:\s*<li>)/g, '<ul><li>$1</li><li>');
            processedContent = processedContent.replace(/<li>(.*?)<\/li>(?!\s*<li>)/g, '<li>$1</li></ul>');

            messageDiv.innerHTML = `<p>${processedContent}</p>`;

            // Adicionar gráfico se houver dados passados diretamente
            if (chartData) {
                const chartId = 'chart-' + Date.now();
                const chartContainer = document.createElement('div');

                // Aplicar classes específicas com base no tipo de gráfico
                const chartType = chartData.type || 'bar';
                chartContainer.className = `chart-container chart-${chartType} mt-4 mb-2`;

                // Adicionar título do gráfico
                if (chartData.title) {
                    const chartTitle = document.createElement('h3');
                    chartTitle.className = 'text-center text-gray-700 font-medium mb-2';
                    chartTitle.textContent = chartData.title;
                    chartContainer.appendChild(chartTitle);
                }

                const canvas = document.createElement('canvas');
                canvas.id = chartId;
                chartContainer.appendChild(canvas);

                messageDiv.appendChild(chartContainer);

                // Renderizar o gráfico após adicionar ao DOM
                setTimeout(() => {
                    renderChart(chartId, chartData);
                }, 100);

                // Adicionar legenda ou descrição do gráfico, se necessário
                if (chartData.description) {
                    const chartDescription = document.createElement('p');
                    chartDescription.className = 'text-xs text-gray-500 mt-2 text-center';
                    chartDescription.textContent = chartData.description;
                    messageDiv.appendChild(chartDescription);
                }
            }

            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        // Função para renderizar um gráfico usando Chart.js
        function renderChart(chartId, chartData) {
            // Usar o processador de gráficos para renderizar o gráfico
            if (window.ChartProcessor && typeof window.ChartProcessor.renderChartFromData === 'function') {
                window.ChartProcessor.renderChartFromData(chartId, chartData);
                return;
            }

            // Fallback para o método antigo caso o processador não esteja disponível
            try {
                const canvas = document.getElementById(chartId);
                if (!canvas) {
                    console.error('Elemento canvas não encontrado:', chartId);
                    return;
                }

                const ctx = canvas.getContext('2d');
                if (!ctx) {
                    console.error('Contexto 2D não disponível para o canvas:', chartId);
                    return;
                }

                // Configura cores padrão se não fornecidas
                if (chartData.datasets) {
                    chartData.datasets.forEach((dataset, index) => {
                        if (!dataset.backgroundColor) {
                            const defaultColors = [
                                'rgba(0, 122, 255, 0.7)',
                                'rgba(88, 86, 214, 0.7)',
                                'rgba(90, 200, 250, 0.7)',
                                'rgba(52, 199, 89, 0.7)',
                                'rgba(255, 59, 48, 0.7)'
                            ];
                            dataset.backgroundColor = defaultColors[index % defaultColors.length];
                        }

                        if (!dataset.borderColor) {
                            const defaultBorderColors = [
                                'rgba(0, 122, 255, 1)',
                                'rgba(88, 86, 214, 1)',
                                'rgba(90, 200, 250, 1)',
                                'rgba(52, 199, 89, 1)',
                                'rgba(255, 59, 48, 1)'
                            ];
                            dataset.borderColor = defaultBorderColors[index % defaultBorderColors.length];
                        }
                    });
                }

                // Criar o gráfico
                new Chart(ctx, {
                    type: chartData.type || 'bar',
                    data: {
                        labels: chartData.labels || [],
                        datasets: chartData.datasets || []
                    },
                    options: chartData.options || {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'top',
                            },
                            title: {
                                display: true,
                                text: chartData.title || 'Gráfico'
                            }
                        }
                    }
                });
            } catch (error) {
                console.error('Erro ao renderizar gráfico:', error);
            }
        }

        // Função para testar a renderização de gráficos
        function testChartRendering() {
            if (!window.ChartExamples) {
                console.error('Exemplos de gráficos não encontrados');
                return;
            }

            // Criar container para os gráficos de teste
            const testContainer = document.createElement('div');
            testContainer.id = 'chart-test-container';
            testContainer.className = 'p-4 bg-white rounded-lg shadow-md';
            testContainer.style.position = 'fixed';
            testContainer.style.top = '20px';
            testContainer.style.right = '20px';
            testContainer.style.zIndex = '9999';
            testContainer.style.width = '600px';
            testContainer.style.maxHeight = '80vh';
            testContainer.style.overflowY = 'auto';

            // Adicionar título
            const title = document.createElement('h2');
            title.className = 'text-lg font-bold mb-4';
            title.textContent = 'Teste de Renderização de Gráficos';
            testContainer.appendChild(title);

            // Adicionar botão para fechar
            const closeButton = document.createElement('button');
            closeButton.className = 'absolute top-2 right-2 text-gray-500 hover:text-gray-700';
            closeButton.innerHTML = '<i class="fas fa-times"></i>';
            closeButton.onclick = () => {
                document.body.removeChild(testContainer);
            };
            testContainer.appendChild(closeButton);

            // Adicionar gráficos de exemplo
            const examples = [
                { name: 'Gráfico de Barras', data: window.ChartExamples.barChartExample },
                { name: 'Gráfico de Linha', data: window.ChartExamples.lineChartExample },
                { name: 'Gráfico de Pizza', data: window.ChartExamples.pieChartExample }
            ];

            examples.forEach((example, index) => {
                // Criar seção para o gráfico
                const section = document.createElement('div');
                section.className = 'mb-6 p-3 border border-gray-200 rounded-lg';

                // Adicionar título do exemplo
                const exampleTitle = document.createElement('h3');
                exampleTitle.className = 'text-md font-semibold mb-2';
                exampleTitle.textContent = example.name;
                section.appendChild(exampleTitle);

                // Criar canvas para o gráfico
                const canvas = document.createElement('canvas');
                canvas.id = `test-chart-${index}`;
                canvas.style.height = '300px';
                section.appendChild(canvas);

                testContainer.appendChild(section);

                // Renderizar o gráfico após adicionar ao DOM
                setTimeout(() => {
                    renderChart(`test-chart-${index}`, example.data);
                }, 100);
            });

            // Adicionar ao corpo do documento
            document.body.appendChild(testContainer);
        }

        // Função para gerar exemplos de gráficos no chat
        function generateChartExamples() {
            if (!window.ChartExamples) {
                console.error('Exemplos de gráficos não encontrados');
                return;
            }

            // Criar mensagem com exemplos de gráficos
            let message = "# Exemplos de Gráficos\n\n";
            message += "Aqui estão alguns exemplos de gráficos que podem ser gerados no chat do Amigo Studio Pro.\n\n";

            // Exemplo de gráfico de barras
            message += "## Gráfico de Barras\n\n";
            message += "```chart\n";
            message += JSON.stringify(window.ChartExamples.barChartExample, null, 2);
            message += "\n```\n\n";

            // Exemplo de gráfico de linha
            message += "## Gráfico de Linha\n\n";
            message += "```chart\n";
            message += JSON.stringify(window.ChartExamples.lineChartExample, null, 2);
            message += "\n```\n\n";

            // Exemplo de gráfico de pizza
            message += "## Gráfico de Pizza\n\n";
            message += "```chart\n";
            message += JSON.stringify(window.ChartExamples.pieChartExample, null, 2);
            message += "\n```\n\n";

            // Adicionar instruções para criar gráficos
            message += "## Como Criar Gráficos\n\n";
            message += "Para criar um gráfico no chat, você pode:\n\n";
            message += "1. **Usar o Assistente de Gráficos**: Clique no botão 'Assistente de Gráficos' no menu de ferramentas.\n";
            message += "2. **Solicitar ao Amigo Studio Pro**: Peça ao assistente para gerar um gráfico com seus dados.\n";
            message += "3. **Criar manualmente**: Insira um bloco de código com a sintaxe ```chart { ... } ```.\n\n";

            message += "### Estrutura Básica de um Gráfico\n\n";
            message += "```json\n";
            message += `{
  "type": "bar",
  "title": "Título do Gráfico",
  "labels": ["Rótulo 1", "Rótulo 2", "Rótulo 3"],
  "datasets": [
    {
      "label": "Nome do Dataset",
      "data": [10, 20, 30],
      "backgroundColor": "rgba(0, 122, 255, 0.7)",
      "borderColor": "rgba(0, 122, 255, 1)",
      "borderWidth": 1
    }
  ],
  "options": {
    "plugins": {
      "title": {
        "display": true,
        "text": "Título do Gráfico"
      }
    }
  }
}\n`;
            message += "```\n\n";

            // Adicionar mensagem ao chat como se fosse do assistente
            addMessage('assistant', message);
        }

        // Função para mostrar ajuda sobre gráficos
        function showChartHelp() {
            let message = "# Guia de Gráficos no Amigo Studio Pro\n\n";

            message += "## Tipos de Gráficos Disponíveis\n\n";
            message += "- **Gráfico de Barras (`bar`)**: Ideal para comparar valores entre categorias.\n";
            message += "- **Gráfico de Linha (`line`)**: Perfeito para mostrar tendências ao longo do tempo.\n";
            message += "- **Gráfico de Pizza (`pie`)**: Útil para mostrar proporções de um todo.\n";
            message += "- **Gráfico de Rosca (`doughnut`)**: Similar ao gráfico de pizza, mas com um espaço vazio no centro.\n";
            message += "- **Gráfico de Radar (`radar`)**: Bom para comparar múltiplas variáveis.\n";
            message += "- **Gráfico de Área Polar (`polarArea`)**: Combina aspectos de gráficos de pizza e radar.\n";
            message += "- **Gráfico de Dispersão (`scatter`)**: Ideal para mostrar correlações entre variáveis.\n\n";

            message += "## Ferramentas Disponíveis\n\n";
            message += "- **Assistente de Gráficos**: Interface visual para criar gráficos facilmente.\n";
            message += "- **Exemplos de Gráficos**: Mostra exemplos de diferentes tipos de gráficos.\n";
            message += "- **Testar Gráficos**: Verifica se os gráficos estão sendo renderizados corretamente.\n\n";

            message += "## Dicas para Gráficos Eficazes\n\n";
            message += "1. **Escolha o tipo certo**: Selecione o tipo de gráfico mais adequado para seus dados.\n";
            message += "2. **Mantenha-o simples**: Evite sobrecarregar o gráfico com muitos dados.\n";
            message += "3. **Use cores com significado**: Escolha cores que ajudem a transmitir a mensagem.\n";
            message += "4. **Adicione títulos claros**: Use títulos e legendas descritivos.\n";
            message += "5. **Formate os números**: Use formatação apropriada para valores monetários, percentuais, etc.\n\n";

            message += "## Solução de Problemas\n\n";
            message += "Se seu gráfico não estiver aparecendo corretamente:\n\n";
            message += "1. Verifique se o JSON está formatado corretamente.\n";
            message += "2. Certifique-se de que o bloco de código começa com ```chart e termina com ```.\n";
            message += "3. Confirme que os dados estão no formato esperado para o tipo de gráfico.\n";
            message += "4. Use o botão 'Testar Gráficos' para verificar se a renderização está funcionando.\n\n";

            // Adicionar mensagem ao chat como se fosse do assistente
            addMessage('assistant', message);
        }

        // Adicionar botões de teste ao menu de ferramentas
        document.addEventListener('DOMContentLoaded', () => {
            const toolsMenu = document.querySelector('.tools-menu');
            if (toolsMenu) {
                // Botão para testar renderização de gráficos
                const testButton = document.createElement('button');
                testButton.className = 'tool-button';
                testButton.innerHTML = '<i class="fas fa-vial"></i> Testar Gráficos';
                testButton.onclick = testChartRendering;
                toolsMenu.appendChild(testButton);

                // Botão para gerar exemplos de gráficos no chat
                const examplesButton = document.createElement('button');
                examplesButton.className = 'tool-button';
                examplesButton.innerHTML = '<i class="fas fa-chart-bar"></i> Exemplos de Gráficos';
                examplesButton.onclick = generateChartExamples;
                toolsMenu.appendChild(examplesButton);

                // Botão para abrir o assistente de gráficos
                const helperButton = document.createElement('button');
                helperButton.className = 'tool-button';
                helperButton.innerHTML = '<i class="fas fa-magic"></i> Assistente de Gráficos';
                helperButton.onclick = () => {
                    if (window.ChartHelper && typeof window.ChartHelper.openChartHelper === 'function') {
                        window.ChartHelper.openChartHelper();
                    } else {
                        console.error('Assistente de gráficos não encontrado');
                    }
                };
                toolsMenu.appendChild(helperButton);

                // Botão para mostrar ajuda sobre gráficos
                const helpButton = document.createElement('button');
                helpButton.className = 'tool-button';
                helpButton.innerHTML = '<i class="fas fa-question-circle"></i> Ajuda com Gráficos';
                helpButton.onclick = showChartHelp;
                toolsMenu.appendChild(helpButton);
            }
        });

        // Função para renderizar a maleta de ferramentas
        function renderToolbox() {
            console.log('Renderizando maleta de ferramentas...');

            // Verificar se o elemento toolboxItems existe
            const toolboxItems = document.getElementById('toolbox-items');
            if (!toolboxItems) {
                console.error('Elemento toolbox-items não encontrado!');
                return;
            }

            try {
                // Limpar o container
                toolboxItems.innerHTML = '';

                // Renderizar cada item da maleta
                toolboxData.forEach(tool => {
                    const toolItem = document.createElement('div');
                    toolItem.className = tool.isPremium ? 'toolbox-item premium' : 'toolbox-item';

                    const premiumBadge = tool.isPremium ? '<span class="premium-badge">PRO</span>' : '';
                    const priceInfo = tool.isPremium ? `<div class="text-xs text-orange-600 font-semibold mt-1">${tool.price} - ${tool.author}</div>` : '';

                    toolItem.innerHTML = `
                        ${premiumBadge}
                        <div class="flex items-center mb-1">
                            <div class="w-6 h-6 rounded-full ${tool.isPremium ? 'bg-orange-100' : 'bg-blue-100'} flex items-center justify-center mr-2">
                                <i class="fas ${tool.icon} ${tool.isPremium ? 'text-orange-600' : 'text-blue-600'} text-xs"></i>
                            </div>
                            <h3 class="font-medium text-gray-900 text-sm">${tool.name}</h3>
                        </div>
                        <div class="text-xs text-gray-500 truncate">${tool.description}</div>
                        ${priceInfo}
                    `;

                    // Adicionar tooltip
                    toolItem.setAttribute('data-tippy-content', tool.description);

                    // Adicionar evento de clique
                    toolItem.addEventListener('click', () => {
                        if (tool.isPremium) {
                            openCheckoutModal(tool);
                        } else {
                            useToolInChat(tool.prompt);
                        }
                    });

                    // Adicionar ao container
                    toolboxItems.appendChild(toolItem);
                });

                console.log(`Adicionados ${toolboxData.length} itens à maleta de ferramentas.`);

                // Inicializar tooltips se a biblioteca Tippy.js estiver disponível
                if (typeof tippy === 'function') {
                    try {
                        tippy('[data-tippy-content]', {
                            placement: 'top',
                            arrow: true,
                            theme: 'light',
                            duration: [300, 200],
                            animation: 'shift-away'
                        });
                        console.log('Tooltips inicializados com sucesso!');
                    } catch (e) {
                        console.warn('Erro ao inicializar tooltips:', e);
                    }
                } else {
                    console.warn('Tippy.js não está disponível. Os tooltips não serão exibidos.');
                }
            } catch (error) {
                console.error('Erro ao renderizar a maleta de ferramentas:', error);
            }
        }

        // Função para usar ferramenta no chat
        function useToolInChat(prompt) {
            // Preencher o campo de chat com o prompt
            chatInput.value = prompt;

            // Enviar a mensagem
            sendMessage();
        }

        // Função para ações da maleta de ferramentas
        function sendToolboxAction(action) {
            let prompt = "";

            switch(action) {
                case 'email':
                    prompt = "Prepare um email com os resultados da análise atual para enviar à equipe de gestão. Certifique-se de formatar a resposta com os dados do gráfico no formato ```chart { \"type\": \"bar\", \"labels\": [\"Item1\", \"Item2\"], \"datasets\": [{\"data\": [10, 20]}] } ``` para que seja renderizado diretamente no chat. Certifique-se de que o JSON do gráfico esteja bem formatado e válido.";
                    break;
                case 'csv':
                    prompt = "Exporte os dados da análise atual para um arquivo CSV. Inclua um gráfico resumindo os principais dados. Certifique-se de formatar a resposta com os dados do gráfico no formato ```chart { \"type\": \"bar\", \"labels\": [\"Item1\", \"Item2\"], \"datasets\": [{\"data\": [10, 20]}] } ``` para que seja renderizado diretamente no chat. Certifique-se de que o JSON do gráfico esteja bem formatado e válido.";
                    break;
                case 'resumo':
                    prompt = "Crie um resumo executivo com os principais insights da análise atual. Inclua gráficos que ilustrem os pontos principais. Certifique-se de formatar a resposta com os dados do gráfico no formato ```chart { \"type\": \"bar\", \"labels\": [\"Item1\", \"Item2\"], \"datasets\": [{\"data\": [10, 20]}] } ``` para que seja renderizado diretamente no chat. Certifique-se de que o JSON do gráfico esteja bem formatado e válido.";
                    break;
                case 'distribuir':
                    prompt = "Prepare esta análise para distribuição aos stakeholders, incluindo gráficos e insights principais. Certifique-se de formatar a resposta com os dados do gráfico no formato ```chart { \"type\": \"bar\", \"labels\": [\"Item1\", \"Item2\"], \"datasets\": [{\"data\": [10, 20]}] } ``` para que seja renderizado diretamente no chat. Certifique-se de que o JSON do gráfico esteja bem formatado e válido.";
                    break;
                default:
                    prompt = "Ajude-me com a análise atual";
            }

            // Usar o prompt no chat
            useToolInChat(prompt);

            // Mostrar toast de confirmação
            showToast(`Ação "${action}" iniciada`, 'success');
        }

        // Função para sugestões de saúde
        function askHealthcareSuggestion(element) {
            chatInput.value = element.textContent;
            sendMessage();
        }

        // Função para gerar resposta mockada com base na mensagem
        function generateMockResponse(message) {
            // Detectar intenções com base na mensagem
            const intents = {
                jornada_paciente: message.toLowerCase().includes('jornada') ||
                                 message.toLowerCase().includes('paciente') ||
                                 message.toLowerCase().includes('fluxo') ||
                                 message.toLowerCase().includes('experiência'),

                faturamento: message.toLowerCase().includes('faturamento') ||
                            message.toLowerCase().includes('receita') ||
                            message.toLowerCase().includes('financeiro'),

                ocupacao: message.toLowerCase().includes('ocupação') ||
                         message.toLowerCase().includes('agenda') ||
                         message.toLowerCase().includes('lotação'),

                rentabilidade: message.toLowerCase().includes('rentabilidade') ||
                              message.toLowerCase().includes('lucro') ||
                              message.toLowerCase().includes('margem'),

                produtividade: message.toLowerCase().includes('produtividade') ||
                              message.toLowerCase().includes('profissionais') ||
                              message.toLowerCase().includes('desempenho') ||
                              message.toLowerCase().includes('comparar')
            };

            let responseText = '';
            let responseCode = '';
            let chartData = null;

            // Gerar resposta com base na intenção
            if (intents.jornada_paciente) {
                responseText = `Com base nos dados da jornada do paciente, identifiquei que a etapa de **Espera** apresenta a menor satisfação (75%) e um tempo médio elevado (18 minutos). Esta é uma oportunidade clara para melhorias.

**Recomendações:**
- Implementar um sistema de agendamento mais eficiente para reduzir o tempo de espera
- Oferecer uma experiência mais agradável durante a espera (Wi-Fi, revistas atualizadas, água)
- Comunicar de forma transparente sobre possíveis atrasos

O tempo total da jornada é de 71 minutos, com a etapa de Atendimento representando 42% desse tempo.`;

                responseCode = generateMockCode(message);

                // Adicionar dados do gráfico para jornada do paciente
                chartData = {
                    type: 'bar',
                    title: 'Jornada do Paciente: Satisfação vs Tempo',
                    description: 'Análise da satisfação (barras) e tempo médio em minutos (linha) em cada etapa da jornada do paciente',
                    labels: ["Agendamento", "Recepção", "Espera", "Atendimento", "Pagamento", "Pós-atendimento"],
                    datasets: [
                        {
                            label: 'Satisfação (%)',
                            data: [85, 90, 75, 92, 88, 80],
                            backgroundColor: 'rgba(54, 162, 235, 0.7)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 1,
                            yAxisID: 'y'
                        },
                        {
                            label: 'Tempo Médio (min)',
                            data: [5, 3, 18, 30, 5, 10],
                            backgroundColor: 'rgba(255, 159, 64, 0.7)',
                            borderColor: 'rgba(255, 159, 64, 1)',
                            borderWidth: 1,
                            type: 'line',
                            yAxisID: 'y1'
                        }
                    ],
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                type: 'linear',
                                position: 'left',
                                title: {
                                    display: true,
                                    text: 'Satisfação (%)'
                                }
                            },
                            y1: {
                                type: 'linear',
                                position: 'right',
                                title: {
                                    display: true,
                                    text: 'Tempo (min)'
                                },
                                grid: {
                                    drawOnChartArea: false
                                }
                            }
                        }
                    }
                };

            } else if (intents.faturamento) {
                responseText = `Analisando os dados de faturamento dos últimos 12 meses, observo um crescimento consistente, com uma média de R$ 368.750 por mês.

**Pontos importantes:**
- O faturamento cresceu 34,4% de janeiro a dezembro
- O maior crescimento mensal ocorreu em abril (+7,9%)
- Setembro apresentou uma queda de 5% em relação a agosto

A tendência geral é positiva, com um crescimento médio mensal de 2,8%.`;

                responseCode = generateMockCode(message);

                // Adicionar dados do gráfico para faturamento
                chartData = {
                    type: 'line',
                    title: 'Evolução do Faturamento Mensal vs Meta',
                    description: 'Comparação entre o faturamento mensal realizado e as metas estabelecidas ao longo do ano',
                    labels: ["Jan", "Fev", "Mar", "Abr", "Mai", "Jun", "Jul", "Ago", "Set", "Out", "Nov", "Dez"],
                    datasets: [
                        {
                            label: 'Faturamento',
                            data: [320000, 310000, 315000, 340000, 360000, 375000, 390000, 400000, 380000, 390000, 410000, 430000],
                            backgroundColor: 'rgba(54, 162, 235, 0.2)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 2,
                            tension: 0.1,
                            fill: false
                        },
                        {
                            label: 'Meta',
                            data: [300000, 300000, 320000, 320000, 350000, 350000, 370000, 370000, 390000, 390000, 410000, 410000],
                            backgroundColor: 'rgba(255, 99, 132, 0.2)',
                            borderColor: 'rgba(255, 99, 132, 1)',
                            borderWidth: 2,
                            borderDash: [5, 5],
                            tension: 0.1,
                            fill: false
                        }
                    ],
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Evolução do Faturamento Mensal vs Meta'
                            },
                            tooltip: {
                                mode: 'index',
                                intersect: false,
                                callbacks: {
                                    label: function(context) {
                                        let label = context.dataset.label || '';
                                        if (label) {
                                            label += ': ';
                                        }
                                        if (context.parsed.y !== null) {
                                            label += new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(context.parsed.y);
                                        }
                                        return label;
                                    }
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: false,
                                title: {
                                    display: true,
                                    text: 'Valor (R$)'
                                }
                            }
                        }
                    }
                };

            } else if (intents.ocupacao) {
                responseText = `A análise da ocupação da agenda mostra padrões interessantes:

**Principais insights:**
- A maior taxa de ocupação ocorre nas manhãs de sábado (90%) e nas tardes de segunda-feira (90%)
- A menor ocupação é observada nas noites de sexta-feira (60%) e sábado (0%, sem atendimento)
- A média de ocupação por período é: Manhã (79,2%), Tarde (78,3%), Noite (61,7%)

**Recomendações:**
- Considerar a ampliação de horários nas manhãs de sábado, que têm alta demanda
- Implementar estratégias para aumentar a ocupação nas noites de quinta e sexta
- Avaliar a possibilidade de reduzir a oferta de horários em períodos de baixa ocupação`;

                responseCode = generateMockCode(message);

                // Adicionar dados do gráfico para ocupação da agenda
                chartData = {
                    type: 'bar',
                    title: 'Ocupação da Agenda por Dia e Período',
                    description: 'Percentual de ocupação da agenda por dia da semana e período (manhã, tarde e noite)',
                    labels: ["Segunda", "Terça", "Quarta", "Quinta", "Sexta", "Sábado"],
                    datasets: [
                        {
                            label: 'Manhã',
                            data: [85, 75, 80, 70, 75, 90],
                            backgroundColor: 'rgba(54, 162, 235, 0.7)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 1
                        },
                        {
                            label: 'Tarde',
                            data: [90, 85, 80, 85, 70, 60],
                            backgroundColor: 'rgba(255, 159, 64, 0.7)',
                            borderColor: 'rgba(255, 159, 64, 1)',
                            borderWidth: 1
                        },
                        {
                            label: 'Noite',
                            data: [70, 75, 80, 85, 60, 0],
                            backgroundColor: 'rgba(75, 192, 192, 0.7)',
                            borderColor: 'rgba(75, 192, 192, 1)',
                            borderWidth: 1
                        }
                    ],
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Ocupação da Agenda por Dia e Período'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: 'Taxa de Ocupação (%)'
                                },
                                max: 100
                            }
                        }
                    }
                };

            } else if (intents.rentabilidade) {
                responseText = `A análise de rentabilidade por procedimento revela insights valiosos:

**Principais procedimentos por rentabilidade:**
- Cirurgia Complexa: 37,1% de rentabilidade (R$ 3.500 valor médio, R$ 2.200 custo médio)
- Tratamento Contínuo: 37,5% de rentabilidade (R$ 800 valor médio, R$ 500 custo médio)
- Consulta Clínica: 40% de rentabilidade, mas maior volume (450 procedimentos)

**Recomendações:**
- Focar em aumentar o volume de Cirurgias Complexas, que têm alta rentabilidade
- Revisar o preço das Consultas Clínicas para melhorar a margem
- Implementar pacotes de Tratamento Contínuo para aumentar a adesão`;

                responseCode = generateMockCode(message);

                // Adicionar dados do gráfico para rentabilidade
                chartData = {
                    type: 'bar',
                    title: 'Rentabilidade por Procedimento',
                    description: 'Análise da rentabilidade percentual (barras) e volume de procedimentos (linha) por tipo de procedimento',
                    labels: ["Consulta Clínica", "Tratamento Contínuo", "Cirurgia Simples", "Cirurgia Complexa", "Exame Específico"],
                    datasets: [
                        {
                            label: 'Rentabilidade (%)',
                            data: [40, 37.5, 35, 37.1, 32],
                            backgroundColor: 'rgba(54, 162, 235, 0.7)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 1,
                            yAxisID: 'y'
                        },
                        {
                            label: 'Volume (qtd)',
                            data: [450, 280, 120, 85, 320],
                            backgroundColor: 'rgba(255, 99, 132, 0.7)',
                            borderColor: 'rgba(255, 99, 132, 1)',
                            borderWidth: 1,
                            type: 'line',
                            yAxisID: 'y1'
                        }
                    ],
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        indexAxis: 'y',
                        scales: {
                            y: {
                                type: 'category',
                                position: 'left'
                            },
                            x: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: 'Rentabilidade (%)'
                                }
                            },
                            y1: {
                                type: 'linear',
                                position: 'right',
                                title: {
                                    display: true,
                                    text: 'Volume (qtd)'
                                },
                                grid: {
                                    drawOnChartArea: false
                                }
                            }
                        }
                    }
                };

            } else if (intents.produtividade) {
                responseText = `Para comparar a produtividade dos profissionais de saúde em sua clínica, analisei três métricas principais: número de atendimentos, faturamento gerado e satisfação dos pacientes.

**Principais insights:**
- A **Dra. Santos** apresenta o maior número de atendimentos (210) e faturamento (R$ 52.500,00), mas não a maior satisfação (88%).
- O **Dr. Oliveira** tem a maior satisfação (95%), mas o menor número de atendimentos (150) e faturamento (R$ 37.500,00).
- A **Dra. Costa** tem um bom equilíbrio entre volume de atendimentos (195), faturamento (R$ 48.750,00) e satisfação (90%).

**Recomendações:**
- Analisar as práticas do **Dr. Oliveira** para entender como ele alcança alta satisfação e aplicar essas práticas aos demais profissionais.
- Verificar se o alto volume de atendimentos da **Dra. Santos** não está impactando negativamente a satisfação dos pacientes.
- Considerar a implementação de um programa de incentivo que equilibre volume de atendimentos e satisfação dos pacientes.

\`\`\`chart
{
    "type": "radar",
    "title": "Comparativo de Desempenho dos Profissionais",
    "labels": ["Atendimentos", "Faturamento", "Satisfação"],
    "datasets": [
        {
            "label": "Dr. Silva",
            "data": [85.7, 85.7, 92],
            "backgroundColor": "rgba(54, 162, 235, 0.2)",
            "borderColor": "rgba(54, 162, 235, 1)",
            "borderWidth": 2,
            "pointBackgroundColor": "rgba(54, 162, 235, 1)"
        },
        {
            "label": "Dra. Santos",
            "data": [100, 100, 88],
            "backgroundColor": "rgba(255, 99, 132, 0.2)",
            "borderColor": "rgba(255, 99, 132, 1)",
            "borderWidth": 2,
            "pointBackgroundColor": "rgba(255, 99, 132, 1)"
        },
        {
            "label": "Dr. Oliveira",
            "data": [71.4, 71.4, 95],
            "backgroundColor": "rgba(75, 192, 192, 0.2)",
            "borderColor": "rgba(75, 192, 192, 1)",
            "borderWidth": 2,
            "pointBackgroundColor": "rgba(75, 192, 192, 1)"
        },
        {
            "label": "Dra. Costa",
            "data": [92.9, 92.9, 90],
            "backgroundColor": "rgba(255, 159, 64, 0.2)",
            "borderColor": "rgba(255, 159, 64, 1)",
            "borderWidth": 2,
            "pointBackgroundColor": "rgba(255, 159, 64, 1)"
        },
        {
            "label": "Dr. Pereira",
            "data": [78.6, 78.6, 93],
            "backgroundColor": "rgba(153, 102, 255, 0.2)",
            "borderColor": "rgba(153, 102, 255, 1)",
            "borderWidth": 2,
            "pointBackgroundColor": "rgba(153, 102, 255, 1)"
        }
    ],
    "options": {
        "responsive": true,
        "maintainAspectRatio": false,
        "scales": {
            "r": {
                "angleLines": {
                    "display": true
                },
                "suggestedMin": 0,
                "suggestedMax": 100
            }
        }
    }
}
\`\`\`

\`\`\`chart
{
    "type": "bar",
    "title": "Distribuição de Atendimentos por Tipo e Profissional",
    "labels": ["Dr. Silva", "Dra. Santos", "Dr. Oliveira", "Dra. Costa", "Dr. Pereira"],
    "datasets": [
        {
            "label": "Consultas",
            "data": [120, 130, 100, 110, 105],
            "backgroundColor": "rgba(54, 162, 235, 0.7)",
            "borderColor": "rgba(54, 162, 235, 1)",
            "borderWidth": 1
        },
        {
            "label": "Exames",
            "data": [40, 50, 30, 55, 35],
            "backgroundColor": "rgba(255, 99, 132, 0.7)",
            "borderColor": "rgba(255, 99, 132, 1)",
            "borderWidth": 1
        },
        {
            "label": "Procedimentos",
            "data": [20, 30, 20, 30, 25],
            "backgroundColor": "rgba(75, 192, 192, 0.7)",
            "borderColor": "rgba(75, 192, 192, 1)",
            "borderWidth": 1
        }
    ],
    "options": {
        "responsive": true,
        "maintainAspectRatio": false,
        "scales": {
            "x": {
                "stacked": true
            },
            "y": {
                "stacked": true,
                "beginAtZero": true,
                "title": {
                    "display": true,
                    "text": "Número de Atendimentos"
                }
            }
        }
    }
}
\`\`\``;

                responseCode = generateMockCode(message);

                // Adicionar dados do gráfico para produtividade
                chartData = {
                    type: 'radar',
                    title: 'Comparativo de Desempenho dos Profissionais',
                    description: 'Comparação normalizada (0-100) das métricas de atendimentos, faturamento e satisfação entre os profissionais',
                    labels: ['Atendimentos', 'Faturamento', 'Satisfação'],
                    datasets: [
                        {
                            label: 'Dr. Silva',
                            data: [85.7, 85.7, 92],
                            backgroundColor: 'rgba(54, 162, 235, 0.2)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 2,
                            pointBackgroundColor: 'rgba(54, 162, 235, 1)'
                        },
                        {
                            label: 'Dra. Santos',
                            data: [100, 100, 88],
                            backgroundColor: 'rgba(255, 99, 132, 0.2)',
                            borderColor: 'rgba(255, 99, 132, 1)',
                            borderWidth: 2,
                            pointBackgroundColor: 'rgba(255, 99, 132, 1)'
                        },
                        {
                            label: 'Dr. Oliveira',
                            data: [71.4, 71.4, 95],
                            backgroundColor: 'rgba(75, 192, 192, 0.2)',
                            borderColor: 'rgba(75, 192, 192, 1)',
                            borderWidth: 2,
                            pointBackgroundColor: 'rgba(75, 192, 192, 1)'
                        },
                        {
                            label: 'Dra. Costa',
                            data: [92.9, 92.9, 90],
                            backgroundColor: 'rgba(255, 159, 64, 0.2)',
                            borderColor: 'rgba(255, 159, 64, 1)',
                            borderWidth: 2,
                            pointBackgroundColor: 'rgba(255, 159, 64, 1)'
                        },
                        {
                            label: 'Dr. Pereira',
                            data: [78.6, 78.6, 93],
                            backgroundColor: 'rgba(153, 102, 255, 0.2)',
                            borderColor: 'rgba(153, 102, 255, 1)',
                            borderWidth: 2,
                            pointBackgroundColor: 'rgba(153, 102, 255, 1)'
                        }
                    ],
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            r: {
                                angleLines: {
                                    display: true
                                },
                                suggestedMin: 0,
                                suggestedMax: 100
                            }
                        }
                    }
                };

            } else {
                // Resposta genérica
                responseText = `Com base nos dados disponíveis, posso fornecer algumas análises sobre sua clínica:

**Visão geral:**
- O faturamento médio mensal é de R$ 368.750, com tendência de crescimento
- A clínica atende em média 225 pacientes por mês, sendo 25% novos pacientes
- O NPS médio é de 81, indicando alta satisfação dos pacientes
- A taxa de ocupação média da agenda é de 73%

Para análises mais específicas, você pode me perguntar sobre:
- Jornada do paciente
- Faturamento e rentabilidade
- Ocupação da agenda
- Produtividade dos profissionais
- Perfil dos pacientes`;

                responseCode = generateMockCode(message);

                // Adicionar dados do gráfico para visão geral
                chartData = {
                    type: 'doughnut',
                    title: 'Distribuição de Pacientes por Tipo',
                    description: 'Proporção entre novos pacientes e pacientes recorrentes atendidos na clínica',
                    labels: ['Novos Pacientes', 'Pacientes Recorrentes'],
                    datasets: [
                        {
                            data: [25, 75],
                            backgroundColor: [
                                'rgba(54, 162, 235, 0.7)',
                                'rgba(75, 192, 192, 0.7)'
                            ],
                            borderColor: [
                                'rgba(54, 162, 235, 1)',
                                'rgba(75, 192, 192, 1)'
                            ],
                            borderWidth: 1
                        }
                    ],
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'top',
                            },
                            title: {
                                display: true,
                                text: 'Distribuição de Pacientes por Tipo'
                            }
                        }
                    }
                };
            }

            return {
                text: responseText,
                code: responseCode,
                chart: chartData
            };
        }

        // Função para gerar código mockado com base na mensagem
        function generateMockCode(message) {
            // Código base que será usado para todos os tipos de análises
            let baseCode = `# -*- coding: utf-8 -*-
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import streamlit as st

# Configurar o estilo dos gráficos
plt.style.use('ggplot')
sns.set_theme(style="whitegrid")

# Carregar dados mockados
`;

            // Verificar o tipo de análise solicitada
            if (message.toLowerCase().includes('faturamento')) {
                return baseCode + `
# Dados de faturamento mensal
meses = ["Jan", "Fev", "Mar", "Abr", "Mai", "Jun", "Jul", "Ago", "Set", "Out", "Nov", "Dez"]
faturamento = [320000, 310000, 315000, 340000, 360000, 375000, 390000, 400000, 380000, 390000, 410000, 430000]
meta = [300000, 300000, 320000, 320000, 350000, 350000, 370000, 370000, 390000, 390000, 410000, 410000]

# Criar DataFrame
df = pd.DataFrame({
    "Mes": meses,
    "Faturamento": faturamento,
    "Meta": meta
})

# Análise de faturamento
st.header("Análise de Faturamento Mensal")
st.write(f"**Faturamento médio:** R$ {np.mean(faturamento):,.2f}")
st.write(f"**Faturamento mínimo:** R$ {np.min(faturamento):,.2f} ({meses[np.argmin(faturamento)]})")
st.write(f"**Faturamento máximo:** R$ {np.max(faturamento):,.2f} ({meses[np.argmax(faturamento)]})")
st.write(f"**Crescimento total:** {(faturamento[-1] - faturamento[0]) / faturamento[0] * 100:.2f}%")

# Calcular crescimento mês a mês
df["Crescimento"] = df["Faturamento"].pct_change() * 100
df["Crescimento"] = df["Crescimento"].fillna(0).round(1)

# Comparação com a meta
df["Diferenca_Meta"] = df["Faturamento"] - df["Meta"]
df["Percentual_Meta"] = (df["Faturamento"] / df["Meta"] * 100).round(1)

# Gráfico de linha com Plotly
st.subheader("Evolução do Faturamento Mensal vs Meta")
fig = px.line(df, x='Mes', y=['Faturamento', 'Meta'], markers=True,
              labels={'value': 'Valor (R$)', 'Mes': 'Mês', 'variable': 'Tipo'},
              title='Evolução do Faturamento Mensal vs Meta')
fig.update_layout(
    xaxis_title='Mês',
    yaxis_title='Valor (R$)',
    yaxis_tickformat=',.0f',
    hovermode='x unified',
    height=500,
    legend_title_text=''
)
# Adicionar anotações para os pontos de máximo e mínimo
fig.add_annotation(
    x=meses[np.argmax(faturamento)],
    y=np.max(faturamento),
    text=f"Máximo: R$ {np.max(faturamento):,.0f}",
    showarrow=True,
    arrowhead=1
)
fig.add_annotation(
    x=meses[np.argmin(faturamento)],
    y=np.min(faturamento),
    text=f"Mínimo: R$ {np.min(faturamento):,.0f}",
    showarrow=True,
    arrowhead=1
)
st.plotly_chart(fig, use_container_width=True)

# Gráfico de barras para comparação trimestral
st.subheader("Faturamento por Trimestre")
trimestres = ['T1', 'T2', 'T3', 'T4']
faturamento_trimestral = [
    sum(faturamento[0:3]),
    sum(faturamento[3:6]),
    sum(faturamento[6:9]),
    sum(faturamento[9:12])
]

df_trimestral = pd.DataFrame({
    'Trimestre': trimestres,
    'Faturamento': faturamento_trimestral
})

fig_trimestral = px.bar(df_trimestral, x='Trimestre', y='Faturamento',
                        color='Faturamento', color_continuous_scale='Blues',
                        text_auto='.2s',
                        labels={'Faturamento': 'Faturamento (R$)', 'Trimestre': 'Trimestre'},
                        title='Comparação de Faturamento por Trimestre')
fig_trimestral.update_layout(
    xaxis_title='Trimestre',
    yaxis_title='Faturamento (R$)',
    yaxis_tickformat=',.0f',
    height=400
)
st.plotly_chart(fig_trimestral, use_container_width=True)

# Tabela com dados
st.subheader("Tabela de Faturamento Mensal")
# Formatar os valores para exibição
df_display = df.copy()
df_display['Faturamento'] = df_display['Faturamento'].apply(lambda x: f"R$ {x:,.2f}")
df_display['Meta'] = df_display['Meta'].apply(lambda x: f"R$ {x:,.2f}")
df_display['Diferenca_Meta'] = df_display['Diferenca_Meta'].apply(lambda x: f"R$ {x:,.2f}")
df_display['Percentual_Meta'] = df_display['Percentual_Meta'].apply(lambda x: f"{x}%")
df_display['Crescimento'] = df_display['Crescimento'].apply(lambda x: f"{x}%")
st.dataframe(df_display, use_container_width=True)

# Conclusões e recomendações
st.subheader("Conclusões e Recomendações")
st.write("""
- O faturamento apresenta uma **tendência de crescimento consistente** ao longo do ano, com aumento total de 34.4%.
- O **quarto trimestre** foi o período com maior faturamento, representando 29.5% do faturamento anual.
- Setembro apresentou uma **queda de 5%** em relação a agosto, sendo o único mês com redução no segundo semestre.
- Recomenda-se investigar as causas da queda em setembro e implementar estratégias para evitar reduções similares.
- O crescimento médio mensal de 2.8% está acima da inflação, indicando crescimento real do negócio.
""")`;
            } else if (message.toLowerCase().includes('ocupação') || message.toLowerCase().includes('agenda')) {
                return baseCode + `
# Dados de ocupação da agenda
dias_semana = ["Segunda", "Terça", "Quarta", "Quinta", "Sexta", "Sábado"]
manha = [85, 75, 80, 70, 75, 90]
tarde = [90, 85, 80, 85, 70, 60]
noite = [70, 75, 80, 85, 60, 0]

# Criar DataFrame
df = pd.DataFrame({
    "Dia": dias_semana,
    "Manha": manha,
    "Tarde": tarde,
    "Noite": noite
})

# Análise de ocupação
st.header("Análise de Ocupação da Agenda")

# Estatísticas por período
st.subheader("Estatísticas por Período")
col1, col2, col3 = st.columns(3)
with col1:
    st.metric("Média Manhã", f"{df['Manha'].mean():.1f}%", f"{df['Manha'].mean() - df['Manha'].mean():.1f}%")
with col2:
    st.metric("Média Tarde", f"{df['Tarde'].mean():.1f}%", f"{df['Tarde'].mean() - df['Manha'].mean():.1f}%")
with col3:
    st.metric("Média Noite", f"{df['Noite'].mean():.1f}%", f"{df['Noite'].mean() - df['Manha'].mean():.1f}%")

# Média de ocupação por dia
df["Media_Dia"] = df[["Manha", "Tarde", "Noite"]].mean(axis=1).round(1)

# Gráfico de barras empilhadas com Plotly
st.subheader("Ocupação da Agenda por Dia e Período")
fig = go.Figure()
fig.add_trace(go.Bar(
    x=df["Dia"],
    y=df["Manha"],
    name='Manhã',
    marker_color='#1f77b4'
))
fig.add_trace(go.Bar(
    x=df["Dia"],
    y=df["Tarde"],
    name='Tarde',
    marker_color='#ff7f0e'
))
fig.add_trace(go.Bar(
    x=df["Dia"],
    y=df["Noite"],
    name='Noite',
    marker_color='#2ca02c'
))

fig.update_layout(
    barmode='group',
    title='Ocupação da Agenda por Dia e Período',
    xaxis_title='Dia da Semana',
    yaxis_title='Taxa de Ocupação (%)',
    legend_title="Período",
    height=500
)
st.plotly_chart(fig, use_container_width=True)

# Mapa de calor (heatmap)
st.subheader("Mapa de Calor de Ocupação")
df_heatmap = df.set_index('Dia')
fig_heatmap = px.imshow(
    df_heatmap[['Manha', 'Tarde', 'Noite']].values,
    labels=dict(x="Período", y="Dia da Semana", color="Ocupação (%)"),
    x=['Manhã', 'Tarde', 'Noite'],
    y=df_heatmap.index,
    color_continuous_scale="Blues",
    text_auto=True
)
fig_heatmap.update_layout(height=400)
st.plotly_chart(fig_heatmap, use_container_width=True)

# Tabela com dados
st.subheader("Tabela de Ocupação da Agenda")
df_display = df.copy()
for col in ['Manha', 'Tarde', 'Noite', 'Media_Dia']:
    df_display[col] = df_display[col].apply(lambda x: f"{x}%")
st.dataframe(df_display, use_container_width=True)

# Conclusões e recomendações
st.subheader("Conclusões e Recomendações")
st.write("""
- A **maior taxa de ocupação** ocorre nas tardes de segunda-feira (90%) e nas manhãs de sábado (90%).
- A **menor ocupação** é observada nas noites de sexta-feira (60%) e sábado (0%, sem atendimento).
- A média de ocupação por período é: Manhã (79.2%), Tarde (78.3%), Noite (61.7%).

**Recomendações:**
- Considerar a ampliação de horários nas manhãs de sábado e tardes de segunda, que têm alta demanda
- Implementar estratégias para aumentar a ocupação nas noites de quinta e sexta
- Avaliar a possibilidade de reduzir a oferta de horários em períodos de baixa ocupação
- Criar promoções ou pacotes especiais para os horários com menor ocupação
""")`;
            } else if (message.toLowerCase().includes('paciente') || message.toLowerCase().includes('jornada')) {
                return baseCode + `
# Dados da jornada do paciente
etapas = ["Agendamento", "Recepção", "Espera", "Atendimento", "Pagamento", "Pós-atendimento"]
satisfacao = [85, 90, 75, 92, 88, 80]
tempo_medio = [5, 3, 18, 30, 5, 10]

# Criar DataFrame
df = pd.DataFrame({
    "Etapa": etapas,
    "Satisfacao": satisfacao,
    "Tempo_Medio": tempo_medio
})

# Análise da jornada
st.header("Análise da Jornada do Paciente")

# Tempo total da jornada
tempo_total = df["Tempo_Medio"].sum()
st.write(f"**Tempo total da jornada:** {tempo_total} minutos")

# Identificar pontos críticos
pontos_criticos = df[df["Satisfacao"] < 80]
if not pontos_criticos.empty:
    st.warning(f"**Pontos críticos identificados:** {', '.join(pontos_criticos['Etapa'].tolist())}")

# Gráfico de barras duplo com Plotly
st.subheader("Jornada do Paciente: Satisfação vs Tempo")
fig = make_subplots(specs=[[{"secondary_y": True}]])

# Adicionar barras de satisfação
fig.add_trace(
    go.Bar(
        x=df["Etapa"],
        y=df["Satisfacao"],
        name="Satisfação (%)",
        marker_color='#1f77b4',
        opacity=0.7
    ),
    secondary_y=False,
)

# Adicionar linha de tempo médio
fig.add_trace(
    go.Scatter(
        x=df["Etapa"],
        y=df["Tempo_Medio"],
        name="Tempo Médio (min)",
        marker_color='#ff7f0e',
        line=dict(width=3)
    ),
    secondary_y=True,
)

# Configurar layout
fig.update_layout(
    title_text="Jornada do Paciente: Satisfação vs Tempo",
    xaxis_title="Etapa da Jornada",
    legend=dict(
        orientation="h",
        yanchor="bottom",
        y=1.02,
        xanchor="right",
        x=1
    ),
    height=500
)

# Configurar eixos Y
fig.update_yaxes(title_text="Satisfação (%)", secondary_y=False)
fig.update_yaxes(title_text="Tempo Médio (min)", secondary_y=True)

st.plotly_chart(fig, use_container_width=True)

# Diagrama de fluxo (Sankey)
st.subheader("Fluxo da Jornada do Paciente")

# Criar dados para o diagrama Sankey
# Nós: cada etapa da jornada
nodes = []
for etapa in etapas:
    nodes.append({"name": etapa})

# Links: conexões entre as etapas
links = []
for i in range(len(etapas) - 1):
    # Valor do link baseado no tempo médio
    links.append({
        "source": i,
        "target": i + 1,
        "value": tempo_medio[i]
    })

# Criar figura Sankey
fig_sankey = go.Figure(data=[go.Sankey(
    node=dict(
        pad=15,
        thickness=20,
        line=dict(color="black", width=0.5),
        label=[node["name"] for node in nodes],
        color="blue"
    ),
    link=dict(
        source=[link["source"] for link in links],
        target=[link["target"] for link in links],
        value=[link["value"] for link in links],
        color=[f"rgba(31, 119, 180, {0.4 + 0.6 * (100 - satisfacao[i]) / 25})" for i in range(len(links))]
    )
)])

fig_sankey.update_layout(
    title_text="Fluxo da Jornada do Paciente",
    font_size=12,
    height=400
)

st.plotly_chart(fig_sankey, use_container_width=True)

# Tabela com dados
st.subheader("Detalhes da Jornada do Paciente")
df_display = df.copy()
df_display["Satisfacao"] = df_display["Satisfacao"].apply(lambda x: f"{x}%")
df_display["Tempo_Medio"] = df_display["Tempo_Medio"].apply(lambda x: f"{x} min")
df_display["Percentual_Tempo"] = (df["Tempo_Medio"] / tempo_total * 100).round(1)
df_display["Percentual_Tempo"] = df_display["Percentual_Tempo"].apply(lambda x: f"{x}%")
st.dataframe(df_display, use_container_width=True)

# Conclusões e recomendações
st.subheader("Conclusões e Recomendações")
st.write("""
- A etapa de **Espera** apresenta a menor satisfação (75%) e um tempo médio elevado (18 minutos).
- A etapa de **Atendimento** representa 42% do tempo total da jornada, mas tem a maior satisfação (92%).
- O tempo total da jornada é de 71 minutos, com oportunidades de otimização.

**Recomendações:**
- Implementar um sistema de agendamento mais eficiente para reduzir o tempo de espera
- Oferecer uma experiência mais agradável durante a espera (Wi-Fi, revistas atualizadas, água)
- Comunicar de forma transparente sobre possíveis atrasos
- Considerar a implementação de um sistema de check-in digital para agilizar a recepção
""")`;
            } else if (message.toLowerCase().includes('produtividade') || message.toLowerCase().includes('profissionais')) {
                return baseCode + `
# Dados de produtividade dos profissionais
profissionais = ["Dr. Silva", "Dra. Santos", "Dr. Oliveira", "Dra. Costa", "Dr. Pereira"]
atendimentos = [180, 210, 150, 195, 165]
faturamento = [45000, 52500, 37500, 48750, 41250]
satisfacao = [92, 88, 95, 90, 93]

# Criar DataFrame
df = pd.DataFrame({
    "Profissional": profissionais,
    "Atendimentos": atendimentos,
    "Faturamento": faturamento,
    "Satisfacao": satisfacao
})

# Adicionar métricas normalizadas para o gráfico de radar
df_norm = df.copy()
df_norm["Atendimentos_norm"] = df_norm["Atendimentos"] / df_norm["Atendimentos"].max() * 100
df_norm["Faturamento_norm"] = df_norm["Faturamento"] / df_norm["Faturamento"].max() * 100
df_norm["Satisfacao_norm"] = df_norm["Satisfacao"]  # Já está em percentual

# Análise de produtividade
st.header("Análise de Produtividade dos Profissionais")

# Estatísticas gerais
st.subheader("Estatísticas Gerais")
col1, col2, col3 = st.columns(3)
with col1:
    st.metric("Média de Atendimentos", f"{df['Atendimentos'].mean():.1f}", f"{df['Atendimentos'].mean() - df['Atendimentos'].min():.1f}")
with col2:
    st.metric("Média de Faturamento", f"R$ {df['Faturamento'].mean():,.2f}", f"R$ {df['Faturamento'].mean() - df['Faturamento'].min():,.2f}")
with col3:
    st.metric("Média de Satisfação", f"{df['Satisfacao'].mean():.1f}%", f"{df['Satisfacao'].mean() - df['Satisfacao'].min():.1f}%")

# Gráfico de radar para comparar profissionais
st.subheader("Comparativo de Desempenho (Gráfico de Radar)")

# Criar gráfico de radar com Plotly
fig = go.Figure()

# Adicionar cada profissional ao gráfico de radar
for i, profissional in enumerate(profissionais):
    fig.add_trace(go.Scatterpolar(
        r=[df_norm.loc[i, 'Atendimentos_norm'],
           df_norm.loc[i, 'Faturamento_norm'],
           df_norm.loc[i, 'Satisfacao_norm']],
        theta=['Atendimentos', 'Faturamento', 'Satisfação'],
        fill='toself',
        name=profissional
    ))

fig.update_layout(
    polar=dict(
        radialaxis=dict(
            visible=True,
            range=[0, 100]
        )
    ),
    showlegend=True,
    height=500
)

st.plotly_chart(fig, use_container_width=True)

# Dados para gráfico de barras empilhadas
# Simulando diferentes tipos de atendimentos
tipos_atendimento = ["Consulta", "Exame", "Procedimento"]
dados_tipos = {
    "Dr. Silva": [120, 40, 20],
    "Dra. Santos": [130, 50, 30],
    "Dr. Oliveira": [100, 30, 20],
    "Dra. Costa": [110, 55, 30],
    "Dr. Pereira": [105, 35, 25]
}

# Criar DataFrame para o gráfico de barras empilhadas
df_tipos = pd.DataFrame(dados_tipos, index=tipos_atendimento).T
df_tipos.index.name = 'Profissional'
df_tipos.reset_index(inplace=True)

# Gráfico de barras empilhadas
st.subheader("Distribuição de Atendimentos por Tipo")
fig_barras = px.bar(df_tipos, x='Profissional', y=tipos_atendimento,
                   title='Distribuição de Atendimentos por Tipo e Profissional',
                   labels={'value': 'Número de Atendimentos', 'variable': 'Tipo de Atendimento'})

fig_barras.update_layout(
    xaxis_title='Profissional',
    yaxis_title='Número de Atendimentos',
    legend_title='Tipo de Atendimento',
    height=500
)

st.plotly_chart(fig_barras, use_container_width=True)

# Tabela com dados
st.subheader("Dados Detalhados por Profissional")
df_display = df.copy()
df_display['Faturamento'] = df_display['Faturamento'].apply(lambda x: f"R$ {x:,.2f}")
df_display['Satisfacao'] = df_display['Satisfacao'].apply(lambda x: f"{x}%")
st.dataframe(df_display, use_container_width=True)

# Conclusões e recomendações
st.subheader("Conclusões e Recomendações")
st.write("""
- A **Dra. Santos** apresenta o maior número de atendimentos (210) e faturamento (R$ 52.500,00), mas não a maior satisfação.
- O **Dr. Oliveira** tem a maior satisfação (95%), mas o menor número de atendimentos (150) e faturamento (R$ 37.500,00).
- A **Dra. Costa** tem um bom equilíbrio entre volume de atendimentos, faturamento e satisfação.

**Recomendações:**
- Analisar as práticas do **Dr. Oliveira** para entender como ele alcança alta satisfação e aplicar essas práticas aos demais profissionais.
- Verificar se o alto volume de atendimentos da **Dra. Santos** não está impactando negativamente a satisfação dos pacientes.
- Considerar a implementação de um programa de incentivo que equilibre volume de atendimentos e satisfação dos pacientes.
- Oferecer treinamento específico para o **Dr. Oliveira** para aumentar sua produtividade mantendo a alta satisfação.
""")
`;
            } else {
                // Código genérico para outros tipos de análises
                return baseCode + `
# Dados gerais para análise
meses = ["Jan", "Fev", "Mar", "Abr", "Mai", "Jun", "Jul", "Ago", "Set", "Out", "Nov", "Dez"]
valores = [320, 350, 370, 390, 410, 430, 450, 470, 460, 480, 500, 520]
categorias = ["A", "B", "A", "C", "B", "A", "C", "B", "A", "C", "B", "A"]

# Criar DataFrame
df = pd.DataFrame({
    "Mes": meses,
    "Valor": valores,
    "Categoria": categorias
})

# Análise básica
st.header("Análise de Dados")

# Estatísticas
st.subheader("Estatísticas Gerais")
col1, col2, col3 = st.columns(3)
with col1:
    st.metric("Média", f"{df['Valor'].mean():.1f}", f"{df['Valor'].mean() - df['Valor'].iloc[0]:.1f}")
with col2:
    st.metric("Máximo", f"{df['Valor'].max():.1f}", f"{df['Valor'].max() - df['Valor'].mean():.1f}")
with col3:
    st.metric("Crescimento", f"{(df['Valor'].iloc[-1] - df['Valor'].iloc[0]) / df['Valor'].iloc[0] * 100:.1f}%")

# Gráfico de linha com Plotly
st.subheader("Evolução Temporal")
fig = px.line(df, x='Mes', y='Valor', markers=True,
              labels={'Valor': 'Valor', 'Mes': 'Mês'},
              title='Evolução Temporal dos Valores')
fig.update_layout(
    xaxis_title='Mês',
    yaxis_title='Valor',
    height=400
)
st.plotly_chart(fig, use_container_width=True)

# Gráfico de barras por categoria
st.subheader("Análise por Categoria")
df_categoria = df.groupby('Categoria')['Valor'].agg(['mean', 'sum']).reset_index()
df_categoria.columns = ['Categoria', 'Média', 'Total']

fig_cat = px.bar(df_categoria, x='Categoria', y=['Média', 'Total'], barmode='group',
                labels={'value': 'Valor', 'Categoria': 'Categoria', 'variable': 'Métrica'},
                title='Análise por Categoria')
fig_cat.update_layout(
    xaxis_title='Categoria',
    yaxis_title='Valor',
    height=400
)
st.plotly_chart(fig_cat, use_container_width=True)

# Gráfico de dispersão
st.subheader("Distribuição dos Valores")
fig_scatter = px.scatter(df, x='Mes', y='Valor', color='Categoria', size='Valor',
                        labels={'Valor': 'Valor', 'Mes': 'Mês', 'Categoria': 'Categoria'},
                        title='Distribuição dos Valores por Mês e Categoria')
fig_scatter.update_layout(
    xaxis_title='Mês',
    yaxis_title='Valor',
    height=400
)
st.plotly_chart(fig_scatter, use_container_width=True)

# Tabela com dados
st.subheader("Tabela de Dados")
st.dataframe(df, use_container_width=True)

# Conclusões e recomendações
st.subheader("Conclusões e Recomendações")
st.write("""
- Os valores apresentam uma **tendência de crescimento** ao longo do ano, com aumento total de 62.5%.
- A categoria **A** tem a maior média de valores (422.5), seguida pela categoria B (410) e C (400).
- Houve uma **queda de 2.1%** em setembro em relação a agosto, que merece investigação.

**Recomendações:**
- Focar em estratégias para aumentar os valores da categoria C, que tem o menor desempenho
- Investigar as causas da queda em setembro para evitar repetição
- Aproveitar o bom desempenho da categoria A para identificar práticas que possam ser aplicadas às demais
""")`;
            }
        }

        // Funções para o modal de ferramentas
        function openToolsModal() {
            const modal = document.getElementById('tools-modal');
            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden'; // Impede rolagem do body
        }

        function closeToolsModal() {
            const modal = document.getElementById('tools-modal');
            modal.classList.add('hidden');
            document.body.style.overflow = ''; // Restaura rolagem do body
        }

        function useToolAndCloseModal(prompt) {
            // Preencher o campo de chat com o prompt
            chatInput.value = prompt;

            // Enviar a mensagem
            sendMessage();

            // Fechar o modal
            closeToolsModal();
        }

        // Função para testar a renderização de gráficos
        function testChartRendering() {
            console.log('Testando renderização de gráficos...');

            // Verificar se o módulo de teste está disponível
            if (typeof window.ChartTest === 'undefined') {
                console.warn('Módulo de teste de gráficos não está disponível');
                showToast('Módulo de teste de gráficos não está disponível. Aguarde o carregamento completo da página.', 'error');

                // Tentar novamente após um pequeno atraso
                setTimeout(testChartRendering, 1000);
                return;
            }

            // Verificar se o Chart.js está disponível
            if (typeof Chart === 'undefined') {
                console.warn('Chart.js não está disponível para o teste');
                showToast('Chart.js não está disponível. Aguarde o carregamento completo da página.', 'error');

                // Tentar carregar o Chart.js novamente
                showToast('Tentando carregar o Chart.js novamente...', 'info');

                // Definir uma versão mínima do Chart.js para garantir que o teste funcione
                window.Chart = window.Chart || function(ctx, config) {
                    this.ctx = ctx;
                    this.config = config;

                    // Método para destruir o gráfico
                    this.destroy = function() {};

                    // Método para atualizar o gráfico
                    this.update = function() {};

                    // Desenhar um texto informando que o Chart.js está sendo carregado
                    if (this.ctx) {
                        this.ctx.font = '14px Arial';
                        this.ctx.fillStyle = '#333';
                        this.ctx.textAlign = 'center';
                        this.ctx.fillText('Carregando Chart.js...', this.ctx.canvas.width / 2, this.ctx.canvas.height / 2);
                    }
                };

                window.chartJsLoaded = true;
                document.dispatchEvent(new Event('chartjsloaded'));

                showToast('Versão mínima do Chart.js carregada. Iniciando teste...', 'success');
            }

            // Criar uma mensagem com um container compacto para os gráficos de teste
            const testMessage = `
                <div id="chart-test-container" class="my-4 border border-gray-200 rounded-lg bg-white overflow-hidden">
                    <div class="bg-blue-50 px-4 py-2 border-b border-gray-200 flex items-center justify-between">
                        <h3 class="text-sm font-medium text-blue-700">Teste de Renderização de Gráficos</h3>
                        <span class="text-xs text-blue-500">Carregando...</span>
                    </div>
                    <div class="p-3">
                        <div class="animate-pulse flex space-x-4 items-center">
                            <div class="rounded-full bg-blue-100 h-10 w-10"></div>
                            <div class="flex-1 space-y-2">
                                <div class="h-4 bg-blue-100 rounded w-3/4"></div>
                                <div class="h-4 bg-blue-100 rounded w-1/2"></div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Adicionar a mensagem ao chat
            addMessage('assistant', testMessage);

            // Mostrar toast de confirmação
            showToast('Teste de gráficos iniciado', 'info');

            // Executar o teste após um pequeno atraso para garantir que o DOM foi atualizado
            setTimeout(function() {
                try {
                    // Testar todos os tipos de gráficos
                    const results = window.ChartTest.testAllChartTypes('chart-test-container');

                    // Exibir resultado do teste
                    if (results.success) {
                        showToast('Gráficos renderizados com sucesso!', 'success');
                    } else {
                        showToast(`${Object.values(results.results).filter(r => r).length} de ${Object.keys(results.results).length} gráficos renderizados`, 'warning');
                    }
                } catch (error) {
                    console.error('Erro ao executar teste de gráficos:', error);
                    showToast('Erro ao executar teste de gráficos', 'error');

                    // Atualizar o container com a mensagem de erro
                    const container = document.getElementById('chart-test-container');
                    if (container) {
                        container.innerHTML = `
                            <div class="p-4 bg-red-50 text-red-600 rounded-md">
                                <p class="font-medium">Erro ao executar teste de gráficos</p>
                                <p class="text-sm mt-1">${error.message}</p>
                            </div>
                        `;
                    }
                }
            }, 300);
        }

        // Função para limpar o chat
        function clearChat() {
            // Manter apenas a mensagem de boas-vindas
            chatContainer.innerHTML = '';

            // Adicionar mensagem de boas-vindas com sugestões de saúde
            addMessageToChat('assistant', `
                <p>Olá! Sou o Amigo Intelligence, seu assistente especializado em análise de dados de performance no setor de saúde.</p>
                <p class="mt-2">Posso ajudar com análises de dados clínicos, previsões de demanda, análise de rentabilidade e muito mais. Como posso ajudar sua clínica hoje?</p>
                <div class="mt-3 flex flex-wrap gap-2">
                    <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-xs cursor-pointer hover:bg-blue-200" onclick="askHealthcareSuggestion(this)">Analisar jornada do paciente</span>
                    <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-xs cursor-pointer hover:bg-blue-200" onclick="askHealthcareSuggestion(this)">Prever demanda de agendamentos</span>
                    <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-xs cursor-pointer hover:bg-blue-200" onclick="askHealthcareSuggestion(this)">Analisar rentabilidade por procedimento</span>
                    <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-xs cursor-pointer hover:bg-blue-200" onclick="askHealthcareSuggestion(this)">Identificar pacientes com risco de abandono</span>
                </div>
                <p class="text-xs text-gray-500 mt-3">Você também pode me pedir para gerar código para análises específicas ou visualizações.</p>
            `);
        }

        // Verificar se há um prompt da maleta de ferramentas
        function checkToolboxPrompt() {
            const urlParams = new URLSearchParams(window.location.search);
            const prompt = urlParams.get('prompt');

            if (prompt) {
                // Preencher o campo de chat com o prompt
                chatInput.value = prompt;

                // Enviar a mensagem após um pequeno delay para garantir que tudo foi carregado
                setTimeout(() => {
                    sendMessage();
                }, 500);
            }
        }

        // Função para alternar o tema
        function toggleTheme() {
            const body = document.body;
            const themeToggle = document.getElementById('theme-toggle');

            if (themeToggle.checked) {
                body.setAttribute('data-theme', 'dark');
                localStorage.setItem('theme', 'dark');

                // Atualizar o tema do editor
                if (editor) {
                    monaco.editor.setTheme('vs-dark');
                }
            } else {
                body.removeAttribute('data-theme');
                localStorage.setItem('theme', 'light');

                // Atualizar o tema do editor
                if (editor) {
                    monaco.editor.setTheme('vs');
                }
            }
        }

        // Event Listeners
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM carregado. Inicializando a aplicação...');

            // Carregar dados mockados
            loadMockData().then(() => {
                console.log('Dados mockados carregados e disponíveis para o chat');
            });

            // Inicializar variáveis
            initializeVariables();

            // Verificar se os elementos foram encontrados
            if (!sendButton || !chatInput || !chatContainer) {
                console.error('Elementos essenciais do chat não foram encontrados!');
                // Tentar novamente após um pequeno delay
                setTimeout(initializeVariables, 500);
            }

            // Verificar tema salvo
            const savedTheme = localStorage.getItem('theme');
            const themeToggle = document.getElementById('theme-toggle');

            if (savedTheme === 'dark') {
                document.body.setAttribute('data-theme', 'dark');
                if (themeToggle) themeToggle.checked = true;

                // Atualizar o tema do editor
                if (editor) {
                    monaco.editor.setTheme('vs-dark');
                }
            }

            // Adicionar evento ao switch de tema
            if (themeToggle) {
                themeToggle.addEventListener('change', toggleTheme);
            }

            // Botão de enviar mensagem
            if (sendButton) {
                sendButton.addEventListener('click', sendMessage);
                console.log('Evento de clique adicionado ao botão de enviar');
            }

            // Enviar mensagem ao pressionar Enter
            if (chatInput) {
                chatInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        sendMessage();
                    }
                });
                console.log('Evento de tecla adicionado ao input de chat');
            }

            // Renderizar a maleta de ferramentas imediatamente
            // Isso garante que a maleta seja renderizada mesmo se o Monaco Editor falhar
            console.log('Chamando renderToolbox()...');
            renderToolbox();

            // Verificar se há um prompt da maleta de ferramentas
            checkToolboxPrompt();

            // Botão de executar código
            if (executeButton) {
                executeButton.addEventListener('click', executeCode);
            }

            // Botão de parar preview
            if (stopPreviewBtn) {
                stopPreviewBtn.addEventListener('click', stopPreview);
            }

            // Botões de abas
            if (outputTabBtn) {
                outputTabBtn.addEventListener('click', function() {
                    switchTab('output');
                });
            }

            if (previewTabBtn) {
                previewTabBtn.addEventListener('click', function() {
                    switchTab('preview');
                });
            }

            // Botão de novo chat
            if (newChatButton) {
                newChatButton.addEventListener('click', clearChat);
            }

            // Adicionar listener para redimensionamento da janela
            window.addEventListener('resize', () => {
                // Redimensionar editor Monaco
                if (editor && typeof editor.layout === 'function') {
                    editor.layout();
                }

                // Redimensionar gráficos
                resizeChartsInChat();
            });

            // Adicionar listener para mudanças no DOM que possam afetar o layout
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'childList') {
                        // Verificar se novos gráficos foram adicionados
                        mutation.addedNodes.forEach((node) => {
                            if (node.nodeType === Node.ELEMENT_NODE) {
                                const charts = node.querySelectorAll ? node.querySelectorAll('canvas') : [];
                                if (charts.length > 0) {
                                    // Aguardar um pouco para os gráficos serem renderizados
                                    setTimeout(() => {
                                        resizeChartsInChat();
                                    }, 500);
                                }
                            }
                        });
                    }
                });
            });

            // Observar mudanças no container do chat
            const chatContainer = document.getElementById('chat-container');
            if (chatContainer) {
                observer.observe(chatContainer, {
                    childList: true,
                    subtree: true
                });
            }

            console.log('Inicialização concluída!');
        });

        // Função para abrir o modal de ferramentas
        function openToolsModal() {
            document.getElementById('tools-modal').classList.remove('hidden');
        }

        // Função para fechar o modal de ferramentas
        function closeToolsModal() {
            document.getElementById('tools-modal').classList.add('hidden');
        }

        // Função para abrir checkout premium
        function openPremiumCheckout(toolName, author, price, description) {
            // Simular processo de pagamento
            const confirmed = confirm(`Deseja comprar "${toolName}" por ${price}?\n\nAutor: ${author}\nDescrição: ${description}\n\nClique OK para prosseguir com o pagamento.`);

            if (confirmed) {
                // Simular processamento
                const loadingToast = showToast('Processando pagamento...', 'info', 3000);

                setTimeout(() => {
                    // Simular sucesso do pagamento
                    showToast(`Pagamento aprovado! Executando "${toolName}"...`, 'success');

                    // Fechar modal e executar ferramenta
                    closeToolsModal();

                    // Mapear ferramentas para prompts específicos
                    const premiumPrompts = {
                        'Análise Completa Y/Y M/M D/D': 'Realizar análise temporal completa comparando dados ano a ano (Y/Y), mês a mês (M/M) e dia a dia (D/D). Incluir tendências, sazonalidade, análise de crescimento e insights preditivos com gráficos comparativos detalhados.',
                        'Análise Preditiva de Demanda': 'Executar análise preditiva de demanda por especialidade usando algoritmos de machine learning. Incluir previsões para os próximos 3 meses, identificação de padrões sazonais e recomendações de capacidade.',
                        'Otimização de Receita': 'Analisar estratégias para maximizar receita por paciente e procedimento. Incluir análise de pricing, identificação de oportunidades de upselling e cross-selling, e otimização do mix de serviços.',
                        'Análise de Risco Clínico': 'Identificar e analisar riscos clínicos e operacionais. Incluir mapeamento de riscos, análise de incidentes, propostas de mitigação e indicadores de segurança.',
                        'Segmentação Avançada de Pacientes': 'Executar clustering inteligente de pacientes para personalização de atendimento e marketing. Incluir análise comportamental, segmentação por valor e propensão, e estratégias personalizadas.',
                        'Análise de Eficiência Operacional': 'Analisar e otimizar processos operacionais para máxima eficiência. Incluir mapeamento de processos, identificação de gargalos, propostas de melhoria e métricas de performance.'
                    };

                    const prompt = premiumPrompts[toolName] || `Executar análise premium: ${toolName}`;
                    useToolInChat(prompt);

                }, 2000);
            }
        }

        // Função para usar uma ferramenta e fechar o modal
        function useToolAndCloseModal(prompt) {
            // Adicionar o prompt ao chat
            addMessageToChat('user', prompt);

            // Processar a mensagem
            sendMessage(prompt);

            // Fechar o modal
            closeToolsModal();
        }

        // Variável para controlar o estado do layout - Editor escondido por padrão
        let isEditorVisible = false;

        // Função para alternar o layout (mostrar/esconder editor)
        function toggleLayout() {
            const editorColumn = document.getElementById('editor-column');
            const toggleBtn = document.getElementById('toggle-layout-btn');
            const toggleIcon = toggleBtn.querySelector('i');

            if (!editorColumn || !toggleBtn || !toggleIcon) {
                console.error('Elementos necessários para toggle layout não encontrados');
                return;
            }

            try {
                if (!isEditorVisible) {
                    // Mostrar editor
                    editorColumn.classList.remove('hidden');

                    // Forçar reflow para garantir que a mudança seja aplicada
                    editorColumn.offsetHeight;

                    // Atualizar botão
                    toggleIcon.className = 'fas fa-chevron-left';
                    toggleBtn.title = 'Esconder Editor e Visualização';
                    toggleBtn.classList.remove('bg-blue-100', 'hover:bg-blue-200', 'text-blue-700');
                    toggleBtn.classList.add('bg-orange-100', 'hover:bg-orange-200', 'text-orange-700');

                    isEditorVisible = true;

                    // Redimensionar o editor Monaco se estiver disponível
                    if (editor && typeof editor.layout === 'function') {
                        setTimeout(() => {
                            editor.layout();
                        }, 100);
                    }

                    // Redimensionar gráficos se houver algum
                    setTimeout(() => {
                        resizeChartsInChat();
                    }, 200);

                } else {
                    // Esconder editor
                    editorColumn.classList.add('hidden');

                    // Forçar reflow
                    editorColumn.offsetHeight;

                    // Atualizar botão
                    toggleIcon.className = 'fas fa-chevron-right';
                    toggleBtn.title = 'Mostrar Editor e Visualização';
                    toggleBtn.classList.remove('bg-orange-100', 'hover:bg-orange-200', 'text-orange-700');
                    toggleBtn.classList.add('bg-blue-100', 'hover:bg-blue-200', 'text-blue-700');

                    isEditorVisible = false;

                    // Redimensionar gráficos no chat quando o editor é escondido
                    setTimeout(() => {
                        resizeChartsInChat();
                    }, 200);
                }

                console.log('Layout toggle executado com sucesso. Editor visível:', isEditorVisible);

            } catch (error) {
                console.error('Erro ao alternar layout:', error);
                showToast('Erro ao alternar layout', 'error');
            }
        }

        // Função para redimensionar gráficos no chat
        function resizeChartsInChat() {
            try {
                // Redimensionar instâncias do Chart.js
                if (window.chartInstances) {
                    Object.values(window.chartInstances).forEach(chart => {
                        if (chart && typeof chart.resize === 'function') {
                            chart.resize();
                        }
                    });
                }

                // Forçar redimensionamento de gráficos no chat container
                const chatContainer = document.getElementById('chat-container');
                if (chatContainer) {
                    const canvases = chatContainer.querySelectorAll('canvas');
                    canvases.forEach(canvas => {
                        // Trigger resize event
                        const resizeEvent = new Event('resize');
                        window.dispatchEvent(resizeEvent);
                    });
                }

                console.log('Gráficos redimensionados após toggle do layout');

            } catch (error) {
                console.error('Erro ao redimensionar gráficos:', error);
            }
        }

        // Funções para o modal de checkout
        function openCheckoutModal(tool) {
            document.getElementById('checkout-tool-name').textContent = tool.name;
            document.getElementById('checkout-tool-description').textContent = tool.description;
            document.getElementById('checkout-tool-price').textContent = tool.price;
            document.getElementById('checkout-tool-author').textContent = tool.author;
            document.getElementById('checkout-tool-icon').className = `fas ${tool.icon} text-orange-600 text-2xl`;

            document.getElementById('checkout-modal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeCheckoutModal() {
            document.getElementById('checkout-modal').classList.add('hidden');
            document.body.style.overflow = '';
        }

        function processPayment() {
            // Simular processamento de pagamento
            showToast('Processando pagamento...', 'info');

            setTimeout(() => {
                closeCheckoutModal();
                showToast('Pagamento realizado com sucesso! Ferramenta liberada.', 'success');

                // Simular liberação da ferramenta
                setTimeout(() => {
                    const tool = toolboxData.find(t => t.name === document.getElementById('checkout-tool-name').textContent);
                    if (tool) {
                        useToolInChat(tool.prompt);
                    }
                }, 1000);
            }, 2000);
        }

        // Renderizar biblioteca de ferramentas
        renderToolsLibrary();

        // Biblioteca de Ferramentas
        const toolsLibrary = [
            {
                id: "analise-faturamento",
                name: "Análise de Faturamento",
                description: "Analisa a evolução do faturamento mensal e identifica tendências",
                category: "financeiro",
                author: "Amigo Tech",
                verified: true,
                icon: "fa-chart-line"
            },
            {
                id: "rentabilidade-procedimento",
                name: "Rentabilidade por Procedimento",
                description: "Calcula e compara a rentabilidade dos diferentes procedimentos",
                category: "financeiro",
                author: "Amigo Tech",
                verified: true,
                icon: "fa-percentage"
            },
            {
                id: "taxa-ocupacao",
                name: "Taxa de Ocupação da Agenda",
                description: "Analisa a taxa de ocupação da agenda por período, profissional ou unidade",
                category: "operacional",
                author: "Ferramenta Comum",
                verified: false,
                icon: "fa-calendar-check"
            },
            {
                id: "tempo-atendimento",
                name: "Tempo Médio de Atendimento",
                description: "Calcula o tempo médio de atendimento por profissional",
                category: "operacional",
                author: "",
                verified: false,
                icon: "fa-clock"
            },
            {
                id: "jornada-paciente",
                name: "Jornada do Paciente",
                description: "Analisa a jornada do paciente desde o primeiro contato",
                category: "pacientes",
                author: "Amigo Tech",
                verified: true,
                icon: "fa-route"
            },
            {
                id: "taxa-retorno",
                name: "Taxa de Retorno",
                description: "Calcula a taxa de retorno dos pacientes e fatores associados",
                category: "pacientes",
                author: "",
                verified: false,
                icon: "fa-undo"
            },
            {
                id: "conversao-leads",
                name: "Conversão de Leads",
                description: "Analisa a taxa de conversão de leads em pacientes",
                category: "marketing",
                author: "Amigo Tech",
                verified: true,
                icon: "fa-funnel-dollar"
            },
            {
                id: "roi-campanhas",
                name: "ROI de Campanhas",
                description: "Calcula o retorno sobre investimento das campanhas",
                category: "marketing",
                author: "",
                verified: false,
                icon: "fa-chart-pie"
            },
            {
                id: "produtividade-profissional",
                name: "Produtividade por Profissional",
                description: "Compara a produtividade entre diferentes profissionais",
                category: "rh",
                author: "Amigo Tech",
                verified: true,
                icon: "fa-user-chart"
            },
            {
                id: "analise-nps",
                name: "Análise de NPS",
                description: "Analisa o Net Promoter Score e fatores que o influenciam",
                category: "qualidade",
                author: "",
                verified: false,
                icon: "fa-star"
            },
            {
                id: "previsao-abandono",
                name: "Previsão de Abandono",
                description: "Identifica pacientes com risco de abandono de tratamento",
                category: "qualidade",
                author: "Amigo Tech",
                verified: true,
                icon: "fa-exclamation-triangle"
            }
        ];

        // Função para renderizar a biblioteca de ferramentas
        function renderToolsLibrary() {
            // Criar seção da biblioteca de ferramentas
            const librarySection = document.createElement('div');
            librarySection.className = 'bg-white rounded-lg shadow-sm border border-gray-200 mt-6';
            librarySection.innerHTML = `
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Biblioteca de Ferramentas</h3>

                    <!-- Filtros -->
                    <div class="flex flex-wrap gap-2 mb-4">
                        <button class="filter-btn active px-3 py-1 text-sm rounded-full border border-gray-300 hover:bg-gray-50 bg-blue-50 border-blue-300 text-blue-700" data-category="todos">Todos</button>
                        <button class="filter-btn px-3 py-1 text-sm rounded-full border border-gray-300 hover:bg-gray-50" data-category="financeiro">Financeiro</button>
                        <button class="filter-btn px-3 py-1 text-sm rounded-full border border-gray-300 hover:bg-gray-50" data-category="operacional">Operacional</button>
                        <button class="filter-btn px-3 py-1 text-sm rounded-full border border-gray-300 hover:bg-gray-50" data-category="pacientes">Pacientes</button>
                        <button class="filter-btn px-3 py-1 text-sm rounded-full border border-gray-300 hover:bg-gray-50" data-category="marketing">Marketing</button>
                        <button class="filter-btn px-3 py-1 text-sm rounded-full border border-gray-300 hover:bg-gray-50" data-category="rh">Recursos Humanos</button>
                        <button class="filter-btn px-3 py-1 text-sm rounded-full border border-gray-300 hover:bg-gray-50" data-category="qualidade">Qualidade</button>
                    </div>

                    <!-- Busca -->
                    <div class="relative mb-4">
                        <input type="text" id="tool-search" placeholder="Buscar ferramentas..." class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <svg class="absolute left-3 top-2.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                </div>

                <!-- Grid de Ferramentas -->
                <div class="p-6">
                    <div id="tools-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <!-- As ferramentas serão inseridas aqui -->
                    </div>
                </div>
            `;

            // Inserir após o container principal
            const mainContainer = document.querySelector('.container.mx-auto');
            if (mainContainer) {
                mainContainer.appendChild(librarySection);
            }

            // Renderizar ferramentas
            renderTools();

            // Configurar filtros
            setupFilters();

            // Configurar busca
            setupSearch();
        }

        // Função para renderizar as ferramentas
        function renderTools(filteredTools = toolsLibrary) {
            const toolsGrid = document.getElementById('tools-grid');
            if (!toolsGrid) return;

            toolsGrid.innerHTML = '';

            filteredTools.forEach(tool => {
                const toolCard = document.createElement('div');
                toolCard.className = 'bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer';

                const authorBadge = tool.author ?
                    `<div class="flex items-center mt-2">
                        <span class="text-xs text-gray-500">${tool.category.charAt(0).toUpperCase() + tool.category.slice(1)}</span>
                        <span class="mx-1 text-gray-300">•</span>
                        <span class="text-xs ${tool.verified ? 'text-blue-600' : 'text-gray-500'}">${tool.author}</span>
                        ${tool.verified ? '<span class="ml-1 text-xs text-blue-600">✓</span>' : ''}
                    </div>` :
                    `<div class="mt-2">
                        <span class="text-xs text-gray-500">${tool.category.charAt(0).toUpperCase() + tool.category.slice(1)}</span>
                    </div>`;

                toolCard.innerHTML = `
                    <div class="flex items-start">
                        <div class="w-10 h-10 rounded-lg bg-blue-50 flex items-center justify-center mr-3">
                            <i class="fas ${tool.icon} text-blue-600"></i>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-medium text-gray-900 mb-1">${tool.name}</h4>
                            <p class="text-sm text-gray-600 mb-2">${tool.description}</p>
                            ${authorBadge}
                        </div>
                    </div>
                    <div class="mt-3 pt-3 border-t border-gray-100">
                        <button class="w-full bg-blue-600 text-white text-sm py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                            Usar Ferramenta
                        </button>
                    </div>
                `;

                // Adicionar evento de clique
                toolCard.addEventListener('click', () => {
                    useLibraryTool(tool);
                });

                toolsGrid.appendChild(toolCard);
            });
        }

        // Função para configurar filtros
        function setupFilters() {
            const filterButtons = document.querySelectorAll('.filter-btn');

            filterButtons.forEach(button => {
                button.addEventListener('click', () => {
                    // Remover classe active de todos os botões
                    filterButtons.forEach(btn => {
                        btn.classList.remove('active', 'bg-blue-50', 'border-blue-300', 'text-blue-700');
                        btn.classList.add('border-gray-300');
                    });

                    // Adicionar classe active ao botão clicado
                    button.classList.add('active', 'bg-blue-50', 'border-blue-300', 'text-blue-700');
                    button.classList.remove('border-gray-300');

                    // Filtrar ferramentas
                    const category = button.dataset.category;
                    const filteredTools = category === 'todos' ?
                        toolsLibrary :
                        toolsLibrary.filter(tool => tool.category === category);

                    renderTools(filteredTools);
                });
            });
        }

        // Função para configurar busca
        function setupSearch() {
            const searchInput = document.getElementById('tool-search');
            if (!searchInput) return;

            searchInput.addEventListener('input', (e) => {
                const searchTerm = e.target.value.toLowerCase();
                const filteredTools = toolsLibrary.filter(tool =>
                    tool.name.toLowerCase().includes(searchTerm) ||
                    tool.description.toLowerCase().includes(searchTerm) ||
                    tool.category.toLowerCase().includes(searchTerm)
                );

                renderTools(filteredTools);
            });
        }

        // Função para usar ferramenta da biblioteca
        function useLibraryTool(tool) {
            // Mapear ferramentas para prompts específicos
            const toolPrompts = {
                "analise-faturamento": "Analisar a evolução do faturamento mensal dos últimos 12 meses e identificar tendências sazonais ou padrões de crescimento/queda. Inclua gráficos de linha e barras.",
                "rentabilidade-procedimento": "Calcular a rentabilidade dos 10 procedimentos mais realizados e identificar quais são os mais lucrativos. Inclua gráfico de barras ordenado por rentabilidade.",
                "taxa-ocupacao": "Analisar a taxa de ocupação da agenda por dia da semana e horário, identificando os períodos de maior e menor ocupação.",
                "tempo-atendimento": "Calcular o tempo médio de atendimento por profissional e identificar oportunidades de otimização.",
                "jornada-paciente": "Analisar a jornada do paciente desde o primeiro contato até o pós-atendimento, identificando pontos de melhoria.",
                "taxa-retorno": "Calcular a taxa de retorno dos pacientes e identificar fatores que influenciam a fidelização.",
                "conversao-leads": "Analisar a taxa de conversão de leads em pacientes e identificar oportunidades de melhoria no funil.",
                "roi-campanhas": "Calcular o retorno sobre investimento das campanhas de marketing e identificar as mais eficazes.",
                "produtividade-profissional": "Comparar a produtividade entre diferentes profissionais e identificar melhores práticas.",
                "analise-nps": "Analisar o Net Promoter Score e identificar fatores que influenciam a satisfação dos pacientes.",
                "previsao-abandono": "Identificar pacientes com risco de abandono de tratamento usando análise preditiva."
            };

            const prompt = toolPrompts[tool.id] || `Executar análise: ${tool.name}`;

            // Usar a ferramenta no chat
            useToolInChat(prompt);

            // Mostrar toast
            showToast(`Ferramenta "${tool.name}" executada`, 'success');
        }
    </script>

    <!-- Modal de Checkout Premium -->
    <div id="checkout-modal" class="fixed inset-0 z-50 hidden overflow-y-auto">
        <div class="flex items-center justify-center min-h-screen px-4 text-center">
            <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                <div class="absolute inset-0 bg-gray-900 opacity-75"></div>
            </div>
            <div class="inline-block bg-white rounded-xl overflow-hidden shadow-2xl transform transition-all max-w-md w-full">
                <!-- Header -->
                <div class="bg-gradient-to-r from-orange-500 to-amber-500 px-6 py-4 text-white">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold">Ferramenta Premium</h3>
                        <button onclick="closeCheckoutModal()" class="text-white hover:text-gray-200">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>

                <!-- Content -->
                <div class="p-6">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 mx-auto mb-4 bg-orange-100 rounded-full flex items-center justify-center">
                            <i id="checkout-tool-icon" class="fas fa-star text-orange-600 text-2xl"></i>
                        </div>
                        <h4 id="checkout-tool-name" class="text-xl font-semibold text-gray-900 mb-2">Nome da Ferramenta</h4>
                        <p id="checkout-tool-description" class="text-gray-600 mb-4">Descrição da ferramenta</p>
                        <div class="bg-orange-50 rounded-lg p-4 mb-4">
                            <div class="text-2xl font-bold text-orange-600" id="checkout-tool-price">R$ 0,00</div>
                            <div class="text-sm text-gray-600">Pagamento único por uso</div>
                        </div>
                        <div class="text-sm text-gray-500">
                            Desenvolvido por: <span id="checkout-tool-author" class="font-medium">Autor</span>
                        </div>
                    </div>

                    <!-- Features -->
                    <div class="mb-6">
                        <h5 class="font-semibold text-gray-900 mb-3">O que você recebe:</h5>
                        <ul class="space-y-2 text-sm text-gray-600">
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                Análise personalizada e detalhada
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                Gráficos interativos e visualizações
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                Insights acionáveis para sua clínica
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-2"></i>
                                Suporte técnico especializado
                            </li>
                        </ul>
                    </div>

                    <!-- Payment Methods -->
                    <div class="mb-6">
                        <h5 class="font-semibold text-gray-900 mb-3">Formas de pagamento:</h5>
                        <div class="grid grid-cols-3 gap-2">
                            <div class="text-center p-2 border rounded-lg">
                                <i class="fab fa-cc-visa text-blue-600 text-xl mb-1"></i>
                                <div class="text-xs">Visa</div>
                            </div>
                            <div class="text-center p-2 border rounded-lg">
                                <i class="fab fa-cc-mastercard text-red-600 text-xl mb-1"></i>
                                <div class="text-xs">Master</div>
                            </div>
                            <div class="text-center p-2 border rounded-lg">
                                <i class="fab fa-pix text-green-600 text-xl mb-1"></i>
                                <div class="text-xs">PIX</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Footer -->
                <div class="bg-gray-50 px-6 py-4 flex space-x-3">
                    <button onclick="closeCheckoutModal()" class="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors">
                        Cancelar
                    </button>
                    <button onclick="processPayment()" class="flex-1 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors">
                        <i class="fas fa-credit-card mr-2"></i>
                        Comprar Agora
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast para notificações -->
    <div id="toast" class="toast hidden"></div>
</body>
</html>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Amigo DataHub{% endblock %}</title>
    <link rel="icon" href="https://cdn-icons-png.flaticon.com/512/6295/6295417.png" type="image/png">
    <!-- Tailwind CSS via CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
          theme: {
            extend: {
              colors: {
                systemBlue: '#007AFF',
                systemGreen: '#34C759',
                systemGray: {
                  DEFAULT: '#8E8E93',
                  light: '#AEAEB2',
                  lighter: '#C7C7CC',
                  lightest: '#D1D1D6',
                  extralight: '#E5E5EA',
                  ultralight: '#F2F2F7'
                },
                label: {
                    DEFAULT: '#000000',
                    secondary: 'rgba(60, 60, 67, 0.6)',
                    tertiary: 'rgba(60, 60, 67, 0.3)',
                    quaternary: 'rgba(60, 60, 67, 0.18)'
                },
                chartPurple: '#7B61FF',
              },
              borderRadius: {
                  'view': '10px',
                  'control': '7px'
              },
              boxShadow: {
                  none: 'none',
              },
               borderColor: theme => ({
                   ...theme('colors'),
                   DEFAULT: theme('colors.gray.200', 'currentColor'),
               })
            }
          }
        }
    </script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- D3.js -->
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <!-- Script principal -->
    <script src="/static/js/main.js"></script>
    <!-- Script de insights -->
    <script src="/static/js/insights.js"></script>
    <!-- Marked.js para renderização de markdown -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <!-- CSS do Agente Inteligente -->
    <link rel="stylesheet" href="/static/css/agent.css">
    <!-- Script do Agente Inteligente (carregado no final do body) -->
    <style>
        /* Dashboard Customizer Styles */
        .customizing-dashboard .kpi-card {
            border: 2px dashed #e5e7eb;
            position: relative;
        }

        .customizing-dashboard .kpi-card:hover {
            border-color: #3b82f6;
            background-color: #f8fafc;
        }

        .customizing-dashboard .kpi-card::after {
            content: '⚙️';
            position: absolute;
            top: 8px;
            right: 8px;
            font-size: 12px;
            opacity: 0.7;
        }

        .kpi-checkbox:checked + span {
            font-weight: 600;
            color: #1f2937;
        }

        .optional-kpi-item.selected {
            background-color: #eff6ff;
            border-color: #3b82f6;
        }

        .notification-enter {
            animation: slideInRight 0.3s ease-out;
        }

        .notification-exit {
            animation: slideOutRight 0.3s ease-in;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            #optional-kpis-grid {
                grid-template-columns: repeat(1, 1fr);
            }

            .customize-overlay .grid {
                grid-template-columns: 1fr;
            }
        }

        @media (min-width: 769px) and (max-width: 1024px) {
            #optional-kpis-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
        }
        .text-label-secondary { color: rgba(60, 60, 67, 0.6); }
        .text-label-tertiary { color: rgba(60, 60, 67, 0.3); }
        .text-label-quaternary { color: rgba(60, 60, 67, 0.18); }
        .btn-subtle-hover:hover { background-color: rgba(0, 122, 255, 0.05); }
        .btn-subtle-active:active { background-color: rgba(0, 122, 255, 0.1); }
        .btn-gray-subtle-hover:hover { background-color: rgba(100, 100, 100, 0.05); }
        .btn-gray-subtle-active:active { background-color: rgba(100, 100, 100, 0.1); }

        /* Sidebar ativo */
        .sidebar-active {
            background-color: rgba(0, 122, 255, 0.1);
            color: #007AFF;
            font-weight: 500;
        }

        /* Estilos do sidebar */
        .sidebar-nav a {
            font-size: 0.75rem; /* Reduzir mais o tamanho da fonte dos itens */
            padding-top: 0.25rem;
            padding-bottom: 0.25rem;
            padding-left: 0.75rem;
            padding-right: 0.75rem;
        }

        .sidebar-nav .sidebar-module-title {
            font-size: 0.65rem; /* Manter o tamanho do título do módulo */
            letter-spacing: 0.05em;
            margin-top: 0.75rem;
            margin-bottom: 0.25rem;
            padding-left: 0.75rem;
            padding-right: 0.75rem;
        }

        .sidebar-nav .sidebar-icon {
            width: 0.9rem;
            height: 0.9rem;
            margin-right: 0.35rem;
        }

        /* Estilização da barra de rolagem para ficar quase invisível */
        .sidebar-nav::-webkit-scrollbar {
            width: 4px;
        }

        .sidebar-nav::-webkit-scrollbar-track {
            background: transparent;
        }

        .sidebar-nav::-webkit-scrollbar-thumb {
            background-color: rgba(0, 0, 0, 0.05);
            border-radius: 20px;
        }

        .sidebar-nav::-webkit-scrollbar-thumb:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }

        /* Firefox */
        .sidebar-nav {
            scrollbar-width: thin;
            scrollbar-color: rgba(0, 0, 0, 0.05) transparent;
        }

        /* Tabela estilizada */
        .data-table th {
            font-weight: 500;
            color: rgba(60, 60, 67, 0.6);
            text-align: left;
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #E5E5EA;
        }

        .data-table td {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #F2F2F7;
        }

        .data-table tr:hover {
            background-color: #F2F2F7;
        }

        /* Sidebar responsiva */
        @media (max-width: 1024px) {
            #sidebar {
                position: fixed;
                left: -240px;
                z-index: 50;
                height: 100%;
                box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
            }

            #sidebar.sidebar-open {
                left: 0;
            }

            .sidebar-overlay {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 40;
            }

            .sidebar-overlay.active {
                display: block;
            }
        }

        /* Sidebar recolhida */
        #sidebar.sidebar-collapsed {
            width: 60px;
        }

        #sidebar.sidebar-collapsed .sidebar-title,
        #sidebar.sidebar-collapsed .sidebar-module-title,
        #sidebar.sidebar-collapsed .sidebar-nav span:not(.sidebar-icon) {
            display: none;
        }

        #sidebar.sidebar-collapsed .sidebar-nav a {
            display: flex;
            justify-content: center;
            padding: 0.75rem 0;
        }

        #sidebar.sidebar-collapsed .sidebar-icon {
            margin-right: 0;
        }
    </style>
</head>
<body class="bg-gray-100 text-gray-900">
    <!-- Overlay para dispositivos móveis -->
    <div id="sidebar-overlay" class="sidebar-overlay"></div>

    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        <div id="sidebar" class="w-52 bg-white border-r border-gray-200 flex flex-col transition-all duration-300 ease-in-out">
            <div class="p-1.5 border-b border-gray-200 flex justify-between items-center">
                <h1 class="text-base font-bold flex items-center">
                    <a href="{{ url_for('index') }}" class="flex items-center pulse-logo">
                        <img src="/static/amigo-logo.png" alt="Amigo Logo" class="h-10">
                        <div class="flex items-center ml-1">
                            <span class="mx-1 text-systemBlue font-bold text-xs self-center">|</span>
                            <span class="text-black font-bold sidebar-title self-center text-xs">DataHub</span>
                        </div>
                    </a>
                </h1>
                <button id="sidebar-close" class="lg:hidden text-gray-500 hover:text-gray-700">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <style>
                @keyframes pulse {
                    0% { transform: scale(1); }
                    50% { transform: scale(1.05); }
                    100% { transform: scale(1); }
                }
                .pulse-logo:hover img {
                    animation: pulse 1s ease infinite;
                }
            </style>
            <nav class="flex-1 overflow-y-auto p-2 sidebar-nav">
                <ul class="space-y-0.5">
                    <li>
                        <a href="{{ url_for('index') }}" class="block rounded-lg hover:bg-systemGray-ultralight {% if request.path == url_for('index') %}sidebar-active{% endif %}">
                            <span class="flex items-center">
                                <svg class="sidebar-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                                </svg>
                                Dashboard
                            </span>
                        </a>
                    </li>

                    <!-- Módulo Agenda -->
                    <li class="mt-4">
                        <h3 class="text-xs font-semibold text-label-secondary uppercase tracking-wider sidebar-module-title">Módulo Agenda</h3>
                    </li>
                    <li>
                        <a href="{{ url_for('agendamentos') }}" class="block px-4 py-2 rounded-lg hover:bg-systemGray-ultralight {% if request.path == url_for('agendamentos') %}sidebar-active{% endif %}">
                            <span class="flex items-center">
                                <svg class="sidebar-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                Agendamentos
                            </span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('producao_medica') }}" class="block px-4 py-2 rounded-lg hover:bg-systemGray-ultralight {% if request.path == url_for('producao_medica') %}sidebar-active{% endif %}">
                            <span class="flex items-center">
                                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                Produção Médica
                            </span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('tempo_atendimento') }}" class="block px-4 py-2 rounded-lg hover:bg-systemGray-ultralight {% if request.path == url_for('tempo_atendimento') %}sidebar-active{% endif %}">
                            <span class="flex items-center">
                                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Tempo de Atendimento
                            </span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('cancelamentos') }}" class="block px-4 py-2 rounded-lg hover:bg-systemGray-ultralight {% if request.path == url_for('cancelamentos') %}sidebar-active{% endif %}">
                            <span class="flex items-center">
                                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                                Cancelamentos
                            </span>
                        </a>
                    </li>

                    <!-- Módulo Financeiro -->
                    <li class="mt-6">
                        <h3 class="px-4 text-xs font-semibold text-label-secondary uppercase tracking-wider sidebar-module-title">Módulo Financeiro</h3>
                    </li>
                    <li>
                        <a href="{{ url_for('contas_receber') }}" class="block px-4 py-2 rounded-lg hover:bg-systemGray-ultralight {% if request.path == url_for('contas_receber') %}sidebar-active{% endif %}">
                            <span class="flex items-center">
                                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Contas a Receber
                            </span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('contas_pagar') }}" class="block px-4 py-2 rounded-lg hover:bg-systemGray-ultralight {% if request.path == url_for('contas_pagar') %}sidebar-active{% endif %}">
                            <span class="flex items-center">
                                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 9a2 2 0 10-4 0v5a2 2 0 01-2 2h6m-6-4h4m8 0a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Contas a Pagar
                            </span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('fluxo_caixa') }}" class="block px-4 py-2 rounded-lg hover:bg-systemGray-ultralight {% if request.path == url_for('fluxo_caixa') %}sidebar-active{% endif %}">
                            <span class="flex items-center">
                                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"></path>
                                </svg>
                                Fluxo de Caixa
                            </span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('fechamento_caixa') }}" class="block px-4 py-2 rounded-lg hover:bg-systemGray-ultralight {% if request.path == url_for('fechamento_caixa') %}sidebar-active{% endif %}">
                            <span class="flex items-center">
                                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                </svg>
                                Fechamento de Caixa
                            </span>
                        </a>
                    </li>

                    <!-- Módulo Paciente -->
                    <li class="mt-6">
                        <h3 class="px-4 text-xs font-semibold text-label-secondary uppercase tracking-wider sidebar-module-title">Módulo Paciente</h3>
                    </li>
                    <li>
                        <a href="{{ url_for('atendimentos_realizados') }}" class="block px-4 py-2 rounded-lg hover:bg-systemGray-ultralight {% if request.path == url_for('atendimentos_realizados') %}sidebar-active{% endif %}">
                            <span class="flex items-center">
                                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                                </svg>
                                Atendimentos Realizados
                            </span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('creditos_disponiveis') }}" class="block px-4 py-2 rounded-lg hover:bg-systemGray-ultralight {% if request.path == url_for('creditos_disponiveis') %}sidebar-active{% endif %}">
                            <span class="flex items-center">
                                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                </svg>
                                Créditos Disponíveis
                            </span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('orcamentos_fechados') }}" class="block px-4 py-2 rounded-lg hover:bg-systemGray-ultralight {% if request.path == url_for('orcamentos_fechados') %}sidebar-active{% endif %}">
                            <span class="flex items-center">
                                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Orçamentos Fechados
                            </span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('orcamentos_abertos') }}" class="block px-4 py-2 rounded-lg hover:bg-systemGray-ultralight {% if request.path == url_for('orcamentos_abertos') %}sidebar-active{% endif %}">
                            <span class="flex items-center">
                                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Orçamentos Abertos
                            </span>
                        </a>
                    </li>



                    <!-- Módulo Amigo Care+ -->
                    <li class="mt-6">
                        <h3 class="px-4 text-xs font-semibold text-label-secondary uppercase tracking-wider sidebar-module-title">Módulo Amigo Care+</h3>
                    </li>
                    <li>
                        <a href="{{ url_for('avaliacao_nps') }}" class="block px-4 py-2 rounded-lg hover:bg-systemGray-ultralight {% if request.path == url_for('avaliacao_nps') %}sidebar-active{% endif %}">
                            <span class="flex items-center">
                                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                                </svg>
                                Avaliação NPS
                            </span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('leads') }}" class="block px-4 py-2 rounded-lg hover:bg-systemGray-ultralight {% if request.path == url_for('leads') %}sidebar-active{% endif %}">
                            <span class="flex items-center">
                                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                                </svg>
                                Leads
                            </span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('campanhas') }}" class="block px-4 py-2 rounded-lg hover:bg-systemGray-ultralight {% if request.path == url_for('campanhas') %}sidebar-active{% endif %}">
                            <span class="flex items-center">
                                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"></path>
                                </svg>
                                Campanhas
                            </span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('funil_vendas') }}" class="block px-4 py-2 rounded-lg hover:bg-systemGray-ultralight {% if request.path == url_for('funil_vendas') %}sidebar-active{% endif %}">
                            <span class="flex items-center">
                                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                                </svg>
                                Funil de Vendas
                            </span>
                        </a>
                    </li>



                    <!-- Módulo Análises Avançadas -->
                    <li class="mt-6">
                        <h3 class="px-4 text-xs font-semibold text-label-secondary uppercase tracking-wider sidebar-module-title">Análises Avançadas</h3>
                    </li>
                    <li>
                        <a href="{{ url_for('amigostudio') }}" class="block px-4 py-2 rounded-lg hover:bg-systemGray-ultralight {% if request.path == url_for('amigostudio') %}sidebar-active{% endif %}" id="amigo-datastudio-link">
                            <span class="flex items-center">
                                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                                AmigoDataStudio
                            </span>
                        </a>
                    </li>




                    <!-- Separador -->
                    <li class="mt-6">
                        <div class="px-4 py-2">
                            <div class="border-t border-gray-200"></div>
                        </div>
                    </li>

                    <!-- Botão de Sair -->
                    <li>
                        <a href="{{ url_for('logout') }}" class="block px-4 py-2 rounded-lg hover:bg-systemGray-ultralight text-red-600 hover:text-red-700">
                            <span class="flex items-center">
                                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                </svg>
                                Sair
                            </span>
                        </a>
                    </li>
                </ul>
            </nav>
        </div>

        <!-- Conteúdo principal -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Cabeçalho -->
            <header class="bg-white border-b border-gray-200 py-4 px-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <button id="sidebar-toggle" class="mr-4 text-gray-500 hover:text-gray-700 focus:outline-none">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                            </svg>
                        </button>
                        <h1 class="text-xl font-semibold">{% block header %}{% endblock %}</h1>
                    </div>
                    <div class="flex items-center space-x-4">
                        <span class="text-sm text-label-secondary hidden sm:inline">Olá, {{ session.user.name if session.user else 'Dr. Bruno Abreu' }}</span>

                        <!-- Ícone do Cockpit KAM -->
                        <a href="{{ url_for('cockpit_kam') }}" class="w-8 h-8 rounded-full bg-gradient-to-r from-purple-500 to-indigo-600 flex items-center justify-center text-white hover:from-purple-600 hover:to-indigo-700 transition-all duration-200 shadow-lg" title="Cockpit KAM">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </a>

                        <div class="relative group">
                            <button class="w-8 h-8 rounded-full bg-systemGray-ultralight flex items-center justify-center text-systemBlue">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            </button>
                            <div class="absolute right-0 mt-2 w-48 bg-white rounded-view shadow-lg py-1 border border-gray-200 hidden group-hover:block z-50">
                                <a href="{{ url_for('logout') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                        </svg>
                                        Sair
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Conteúdo da página -->
            <main class="flex-1 overflow-y-auto bg-gray-100 p-6">
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    {% block scripts %}{% endblock %}

    <!-- Script do Dashboard Customizer -->
    <script src="/static/js/dashboard-customizer.js"></script>

    <!-- Script do Agente Inteligente -->
    <script src="/static/js/agent.js"></script>

    <!-- Modal de confirmação para AmigoDataStudio - Versão Melhorada -->
    <div id="amigo-datastudio-modal" class="fixed inset-0 z-50 hidden overflow-y-auto">
        <div class="flex items-center justify-center min-h-screen px-4 text-center">
            <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                <div class="absolute inset-0 bg-gray-900 opacity-75"></div>
            </div>
            <div class="inline-block bg-white rounded-xl overflow-hidden shadow-xl transform transition-all max-w-2xl w-full">
                <!-- Hero Section -->
                <div class="bg-gradient-to-br from-blue-50 via-gray-50 to-gray-100 px-6 py-6 text-gray-800 relative overflow-hidden border-b border-gray-200">
                    <!-- Background Pattern -->
                    <div class="absolute inset-0 opacity-5">
                        <svg class="w-full h-full" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                                <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                                    <path d="M 10 0 L 0 0 0 10" fill="none" stroke="currentColor" stroke-width="0.5"/>
                                </pattern>
                            </defs>
                            <rect width="100" height="100" fill="url(#grid)" />
                        </svg>
                    </div>

                    <!-- Content -->
                    <div class="relative z-10">
                        <div class="flex items-center justify-center mb-4">
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                                </svg>
                            </div>
                            <div class="text-center">
                                <h1 class="text-2xl font-bold text-gray-900">DataStudio</h1>
                                <p class="text-blue-600 text-sm">Análises Avançadas com IA</p>
                            </div>
                        </div>

                        <div class="text-center">
                            <p class="text-gray-600 text-sm max-w-md mx-auto">
                                Transforme dados em decisões estratégicas com análises personalizadas e insights inteligentes.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Features Section -->
                <div class="px-6 py-4">
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div class="text-center p-3 rounded-lg bg-blue-50">
                            <div class="w-10 h-10 mx-auto mb-2 bg-blue-600 rounded-lg flex items-center justify-center">
                                <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
                                </svg>
                            </div>
                            <h3 class="font-medium text-gray-900 text-sm mb-1">Chat Inteligente</h3>
                            <p class="text-xs text-gray-600">Converse com seus dados</p>
                        </div>

                        <div class="text-center p-3 rounded-lg bg-green-50">
                            <div class="w-10 h-10 mx-auto mb-2 bg-green-600 rounded-lg flex items-center justify-center">
                                <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                            </div>
                            <h3 class="font-medium text-gray-900 text-sm mb-1">Visualizações</h3>
                            <p class="text-xs text-gray-600">Gráficos interativos</p>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="bg-gray-50 px-6 py-4 flex gap-3">
                    <a href="{{ url_for('amigostudio') }}" class="flex-1 inline-flex justify-center items-center rounded-lg border border-transparent px-4 py-2 bg-blue-600 text-sm font-medium text-white hover:bg-blue-700 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        Acessar DataStudio
                    </a>
                    <button type="button" id="close-modal-button" class="flex-1 inline-flex justify-center items-center rounded-lg border border-gray-300 px-4 py-2 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors">
                        Continuar no DataHub
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Script para controle da sidebar -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.getElementById('sidebar-toggle');
            const sidebarClose = document.getElementById('sidebar-close');
            const sidebarOverlay = document.getElementById('sidebar-overlay');

            // Verificar preferência do usuário salva
            const sidebarState = localStorage.getItem('sidebarState');
            if (sidebarState === 'collapsed' && window.innerWidth >= 1024) {
                sidebar.classList.add('sidebar-collapsed');
            }

            // Função para alternar o estado da sidebar em desktop
            function toggleSidebar() {
                if (window.innerWidth >= 1024) {
                    sidebar.classList.toggle('sidebar-collapsed');
                    localStorage.setItem('sidebarState',
                        sidebar.classList.contains('sidebar-collapsed') ? 'collapsed' : 'expanded');
                } else {
                    // Em dispositivos móveis, abrir a sidebar
                    sidebar.classList.add('sidebar-open');
                    sidebarOverlay.classList.add('active');
                }
            }

            // Função para fechar a sidebar em dispositivos móveis
            function closeSidebar() {
                sidebar.classList.remove('sidebar-open');
                sidebarOverlay.classList.remove('active');
            }

            // Event listeners
            sidebarToggle.addEventListener('click', toggleSidebar);
            sidebarClose.addEventListener('click', closeSidebar);
            sidebarOverlay.addEventListener('click', closeSidebar);

            // Ajustar sidebar ao redimensionar a janela
            window.addEventListener('resize', function() {
                if (window.innerWidth < 1024) {
                    sidebar.classList.remove('sidebar-collapsed');
                    if (sidebar.classList.contains('sidebar-open')) {
                        sidebarOverlay.classList.add('active');
                    }
                } else {
                    sidebar.classList.remove('sidebar-open');
                    sidebarOverlay.classList.remove('active');

                    // Restaurar estado salvo
                    if (localStorage.getItem('sidebarState') === 'collapsed') {
                        sidebar.classList.add('sidebar-collapsed');
                    }
                }
            });

            // Configuração do modal do AmigoDataStudio
            const amigoDataStudioLink = document.getElementById('amigo-datastudio-link');
            const amigoDataStudioModal = document.getElementById('amigo-datastudio-modal');
            const closeModalButton = document.getElementById('close-modal-button');

            if (amigoDataStudioLink && amigoDataStudioModal) {
                amigoDataStudioLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    amigoDataStudioModal.classList.remove('hidden');
                });

                closeModalButton.addEventListener('click', function() {
                    amigoDataStudioModal.classList.add('hidden');
                });

                // Fechar o modal ao clicar fora dele
                amigoDataStudioModal.addEventListener('click', function(e) {
                    if (e.target === amigoDataStudioModal) {
                        amigoDataStudioModal.classList.add('hidden');
                    }
                });
            }
        });
    </script>
</body>
</html>

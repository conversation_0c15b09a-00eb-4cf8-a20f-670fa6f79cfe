{% extends "base.html" %}

{% block title %}Cockpit KAM - Key Account Management{% endblock %}
{% block header %}Cockpit KAM{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Hero Section - Cockpit KAM -->
    <div class="bg-gradient-to-br from-blue-50 via-gray-100 to-gray-200 rounded-xl p-8 text-gray-800 relative overflow-hidden border border-gray-300">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-5">
            <svg class="w-full h-full" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <pattern id="kam-grid" width="20" height="20" patternUnits="userSpaceOnUse">
                        <path d="M 20 0 L 0 0 0 20" fill="none" stroke="currentColor" stroke-width="0.5"/>
                    </pattern>
                </defs>
                <rect width="100" height="100" fill="url(#kam-grid)" />
            </svg>
        </div>

        <div class="relative z-10">
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h1 class="text-4xl font-bold mb-2 text-gray-900">Cockpit KAM</h1>
                    <h2 class="text-2xl font-semibold mb-1 text-blue-700">Índice Amigo</h2>
                    <p class="text-gray-600 text-lg">Indicador Econométrico de Performance Clínica</p>
                </div>
                <div class="text-right">
                    <div class="text-6xl font-bold text-blue-600" id="indice-amigo">8.7</div>
                    <div class="text-sm text-gray-500">de 10.0</div>
                    <div class="flex items-center justify-end mt-2">
                        <svg class="w-4 h-4 text-green-600 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                        <span class="text-green-600 text-sm font-medium">+0.3 vs mês anterior</span>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div class="bg-white/80 backdrop-blur-sm rounded-lg p-4 border border-gray-200">
                    <h3 class="font-semibold mb-3 text-gray-800">Fórmula do Índice Amigo</h3>
                    <div class="text-sm text-gray-700 space-y-2">
                        <div class="bg-blue-50 p-2 rounded text-xs font-mono">
                            IA = Σ(Wi × Ni) / 10
                        </div>
                        <div class="space-y-1 text-xs">
                            <div>• <strong>EO</strong>: Eficiência Operacional (W=0.25)</div>
                            <div>• <strong>PF</strong>: Performance Financeira (W=0.30)</div>
                            <div>• <strong>SC</strong>: Satisfação Cliente (W=0.20)</div>
                            <div>• <strong>AT</strong>: Adoção Tecnológica (W=0.15)</div>
                            <div>• <strong>CS</strong>: Crescimento Sustentável (W=0.10)</div>
                        </div>
                    </div>
                </div>

                <div class="bg-white/80 backdrop-blur-sm rounded-lg p-4 border border-gray-200">
                    <h3 class="font-semibold mb-3 text-gray-800">Cálculo Atual</h3>
                    <div class="space-y-2 text-sm text-gray-700">
                        <div class="flex justify-between">
                            <span>EO (Taxa Ocupação):</span>
                            <span class="font-medium text-blue-600">9.2 × 0.25 = 2.30</span>
                        </div>
                        <div class="flex justify-between">
                            <span>PF (ROI Mensal):</span>
                            <span class="font-medium text-green-600">8.8 × 0.30 = 2.64</span>
                        </div>
                        <div class="flex justify-between">
                            <span>SC (NPS Score):</span>
                            <span class="font-medium text-yellow-600">8.7 × 0.20 = 1.74</span>
                        </div>
                        <div class="flex justify-between">
                            <span>AT (Uso Plataforma):</span>
                            <span class="font-medium text-purple-600">8.5 × 0.15 = 1.28</span>
                        </div>
                        <div class="flex justify-between">
                            <span>CS (Growth Rate):</span>
                            <span class="font-medium text-indigo-600">7.4 × 0.10 = 0.74</span>
                        </div>
                        <hr class="my-2">
                        <div class="flex justify-between font-bold">
                            <span>Total:</span>
                            <span class="text-blue-600">8.70</span>
                        </div>
                    </div>
                </div>

                <div class="bg-white/80 backdrop-blur-sm rounded-lg p-4 border border-gray-200">
                    <h3 class="font-semibold mb-3 text-gray-800">Benchmark de Mercado</h3>
                    <div class="space-y-3 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Média de Negócios Semelhantes:</span>
                            <span class="font-medium text-gray-800">6.8</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Top 10%:</span>
                            <span class="font-medium text-green-600">9.2+</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Sua Posição:</span>
                            <span class="font-medium text-blue-600">Top 15%</span>
                        </div>
                        <div class="mt-3 p-2 bg-blue-50 rounded">
                            <div class="text-xs text-blue-700 font-medium">
                                Você está 27.9% acima da média do mercado
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white/80 backdrop-blur-sm rounded-lg p-4 border border-gray-200">
                <h3 class="font-semibold mb-3 text-gray-800">Evolução do Índice Amigo (Últimos 12 meses)</h3>
                <div class="h-40 bg-gray-50 rounded flex items-end justify-between px-4 py-2 relative">
                    <!-- Linha de referência média do mercado -->
                    <div class="absolute left-0 right-0 border-t-2 border-dashed border-red-300" style="bottom: 68%;">
                        <span class="absolute -top-5 right-2 text-xs text-red-500">Média Mercado (6.8)</span>
                    </div>

                    <div class="flex flex-col items-center group relative">
                        <div class="w-6 bg-gradient-to-t from-blue-400 to-blue-500 rounded-t transition-all hover:from-blue-500 hover:to-blue-600" style="height: 62%" title="6.2"></div>
                        <span class="text-xs mt-1 text-gray-600">Jan</span>
                        <div class="absolute -top-8 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity">6.2</div>
                    </div>
                    <div class="flex flex-col items-center group relative">
                        <div class="w-6 bg-gradient-to-t from-blue-400 to-blue-500 rounded-t transition-all hover:from-blue-500 hover:to-blue-600" style="height: 65%" title="6.5"></div>
                        <span class="text-xs mt-1 text-gray-600">Fev</span>
                        <div class="absolute -top-8 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity">6.5</div>
                    </div>
                    <div class="flex flex-col items-center group relative">
                        <div class="w-6 bg-gradient-to-t from-blue-400 to-blue-500 rounded-t transition-all hover:from-blue-500 hover:to-blue-600" style="height: 71%" title="7.1"></div>
                        <span class="text-xs mt-1 text-gray-600">Mar</span>
                        <div class="absolute -top-8 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity">7.1</div>
                    </div>
                    <div class="flex flex-col items-center group relative">
                        <div class="w-6 bg-gradient-to-t from-blue-400 to-blue-500 rounded-t transition-all hover:from-blue-500 hover:to-blue-600" style="height: 74%" title="7.4"></div>
                        <span class="text-xs mt-1 text-gray-600">Abr</span>
                        <div class="absolute -top-8 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity">7.4</div>
                    </div>
                    <div class="flex flex-col items-center group relative">
                        <div class="w-6 bg-gradient-to-t from-blue-400 to-blue-500 rounded-t transition-all hover:from-blue-500 hover:to-blue-600" style="height: 78%" title="7.8"></div>
                        <span class="text-xs mt-1 text-gray-600">Mai</span>
                        <div class="absolute -top-8 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity">7.8</div>
                    </div>
                    <div class="flex flex-col items-center group relative">
                        <div class="w-6 bg-gradient-to-t from-blue-400 to-blue-500 rounded-t transition-all hover:from-blue-500 hover:to-blue-600" style="height: 82%" title="8.2"></div>
                        <span class="text-xs mt-1 text-gray-600">Jun</span>
                        <div class="absolute -top-8 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity">8.2</div>
                    </div>
                    <div class="flex flex-col items-center group relative">
                        <div class="w-6 bg-gradient-to-t from-blue-400 to-blue-500 rounded-t transition-all hover:from-blue-500 hover:to-blue-600" style="height: 79%" title="7.9"></div>
                        <span class="text-xs mt-1 text-gray-600">Jul</span>
                        <div class="absolute -top-8 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity">7.9</div>
                    </div>
                    <div class="flex flex-col items-center group relative">
                        <div class="w-6 bg-gradient-to-t from-blue-400 to-blue-500 rounded-t transition-all hover:from-blue-500 hover:to-blue-600" style="height: 84%" title="8.4"></div>
                        <span class="text-xs mt-1 text-gray-600">Ago</span>
                        <div class="absolute -top-8 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity">8.4</div>
                    </div>
                    <div class="flex flex-col items-center group relative">
                        <div class="w-6 bg-gradient-to-t from-blue-400 to-blue-500 rounded-t transition-all hover:from-blue-500 hover:to-blue-600" style="height: 81%" title="8.1"></div>
                        <span class="text-xs mt-1 text-gray-600">Set</span>
                        <div class="absolute -top-8 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity">8.1</div>
                    </div>
                    <div class="flex flex-col items-center group relative">
                        <div class="w-6 bg-gradient-to-t from-blue-400 to-blue-500 rounded-t transition-all hover:from-blue-500 hover:to-blue-600" style="height: 83%" title="8.3"></div>
                        <span class="text-xs mt-1 text-gray-600">Out</span>
                        <div class="absolute -top-8 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity">8.3</div>
                    </div>
                    <div class="flex flex-col items-center group relative">
                        <div class="w-6 bg-gradient-to-t from-blue-400 to-blue-500 rounded-t transition-all hover:from-blue-500 hover:to-blue-600" style="height: 84%" title="8.4"></div>
                        <span class="text-xs mt-1 text-gray-600">Nov</span>
                        <div class="absolute -top-8 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity">8.4</div>
                    </div>
                    <div class="flex flex-col items-center group relative">
                        <div class="w-6 bg-gradient-to-t from-green-400 to-green-500 rounded-t transition-all hover:from-green-500 hover:to-green-600" style="height: 87%" title="8.7"></div>
                        <span class="text-xs mt-1 text-gray-600 font-medium">Dez</span>
                        <div class="absolute -top-8 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity">8.7</div>
                    </div>
                </div>
                <div class="mt-3 text-xs text-gray-600 flex justify-between">
                    <span>Crescimento anual: +38.7%</span>
                    <span>Tendência: ↗️ Crescimento consistente</span>
                </div>
            </div>
        </div>
    </div>

    <!-- KPIs KAM -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div class="flex items-center justify-between mb-4">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                </div>
                <span class="text-green-600 text-sm font-medium">+18.5%</span>
            </div>
            <h3 class="text-gray-500 text-sm font-medium">Receita Mensal</h3>
            <p class="text-2xl font-bold text-gray-900">R$ 847.2K</p>
            <p class="text-gray-600 text-sm mt-1">vs mês anterior</p>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div class="flex items-center justify-between mb-4">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <span class="text-green-600 text-sm font-medium">+8%</span>
            </div>
            <h3 class="text-gray-500 text-sm font-medium">Patient Retention</h3>
            <p class="text-2xl font-bold text-gray-900">94.2%</p>
            <p class="text-gray-600 text-sm mt-1">Taxa de retenção</p>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div class="flex items-center justify-between mb-4">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <span class="text-green-600 text-sm font-medium">+15%</span>
            </div>
            <h3 class="text-gray-500 text-sm font-medium">Platform Adoption</h3>
            <p class="text-2xl font-bold text-gray-900">87%</p>
            <p class="text-gray-600 text-sm mt-1">Uso da plataforma</p>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div class="flex items-center justify-between mb-4">
                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                    </svg>
                </div>
                <span class="text-green-600 text-sm font-medium">+0.3</span>
            </div>
            <h3 class="text-gray-500 text-sm font-medium">NPS Score</h3>
            <p class="text-2xl font-bold text-gray-900">8.7</p>
            <p class="text-gray-600 text-sm mt-1">Satisfação geral</p>
        </div>
    </div>

    <!-- Insights de IA para KAM -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Insights Estratégicos -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                        <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900">Insights Estratégicos</h3>
                </div>
            </div>
            <div class="p-6 space-y-4">
                <div class="border-l-4 border-blue-500 pl-4">
                    <h4 class="font-medium text-gray-900">Oportunidade de Upselling</h4>
                    <p class="text-sm text-gray-600 mt-1">
                        A clínica está pronta para o módulo de Telemedicina. ROI estimado: +35% em 6 meses.
                    </p>
                </div>
                <div class="border-l-4 border-green-500 pl-4">
                    <h4 class="font-medium text-gray-900">Performance Excepcional</h4>
                    <p class="text-sm text-gray-600 mt-1">
                        Taxa de adoção 23% acima da média. Considere case study para outras contas.
                    </p>
                </div>
                <div class="border-l-4 border-yellow-500 pl-4">
                    <h4 class="font-medium text-gray-900">Atenção Necessária</h4>
                    <p class="text-sm text-gray-600 mt-1">
                        Queda de 5% no uso do módulo financeiro. Agendar treinamento de reciclagem.
                    </p>
                </div>
            </div>
        </div>

        <!-- Ações Recomendadas -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900">Ações Recomendadas</h3>
                </div>
            </div>
            <div class="p-6 space-y-4">
                <div class="flex items-start space-x-3">
                    <div class="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center mt-0.5">
                        <span class="text-red-600 text-xs font-bold">1</span>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900">Reunião Estratégica</h4>
                        <p class="text-sm text-gray-600">Agendar QBR para apresentar roadmap 2024</p>
                        <span class="text-xs text-red-600 font-medium">Urgente - 3 dias</span>
                    </div>
                </div>
                <div class="flex items-start space-x-3">
                    <div class="w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center mt-0.5">
                        <span class="text-yellow-600 text-xs font-bold">2</span>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900">Treinamento Avançado</h4>
                        <p class="text-sm text-gray-600">Capacitar equipe em analytics avançados</p>
                        <span class="text-xs text-yellow-600 font-medium">Médio - 2 semanas</span>
                    </div>
                </div>
                <div class="flex items-start space-x-3">
                    <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
                        <span class="text-green-600 text-xs font-bold">3</span>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900">Expansão de Módulos</h4>
                        <p class="text-sm text-gray-600">Propor integração com sistema de estoque</p>
                        <span class="text-xs text-green-600 font-medium">Baixo - 1 mês</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Análise de Risco e Oportunidades -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Health Score por Módulo -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Health Score por Módulo</h3>
            </div>
            <div class="p-6 space-y-4">
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-700">Agenda Digital</span>
                    <div class="flex items-center">
                        <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                            <div class="bg-green-500 h-2 rounded-full" style="width: 92%"></div>
                        </div>
                        <span class="text-sm font-bold text-green-600">92%</span>
                    </div>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-700">Financeiro</span>
                    <div class="flex items-center">
                        <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                            <div class="bg-yellow-500 h-2 rounded-full" style="width: 78%"></div>
                        </div>
                        <span class="text-sm font-bold text-yellow-600">78%</span>
                    </div>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-700">Prontuário</span>
                    <div class="flex items-center">
                        <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                            <div class="bg-green-500 h-2 rounded-full" style="width: 89%"></div>
                        </div>
                        <span class="text-sm font-bold text-green-600">89%</span>
                    </div>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-700">Analytics</span>
                    <div class="flex items-center">
                        <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                            <div class="bg-blue-500 h-2 rounded-full" style="width: 95%"></div>
                        </div>
                        <span class="text-sm font-bold text-blue-600">95%</span>
                    </div>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-700">Telemedicina</span>
                    <div class="flex items-center">
                        <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                            <div class="bg-gray-400 h-2 rounded-full" style="width: 0%"></div>
                        </div>
                        <span class="text-sm font-bold text-gray-500">N/A</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Análise de Churn Risk -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Análise de Risco</h3>
            </div>
            <div class="p-6">
                <div class="text-center mb-4">
                    <div class="w-20 h-20 mx-auto bg-green-100 rounded-full flex items-center justify-center">
                        <span class="text-2xl font-bold text-green-600">Baixo</span>
                    </div>
                    <p class="text-sm text-gray-600 mt-2">Risco de Churn</p>
                </div>
                <div class="space-y-3">
                    <div class="flex items-center text-sm">
                        <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                        <span>Engagement alto (95%)</span>
                    </div>
                    <div class="flex items-center text-sm">
                        <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                        <span>Suporte responsivo</span>
                    </div>
                    <div class="flex items-center text-sm">
                        <div class="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
                        <span>Renovação em 4 meses</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Potencial de Expansão -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Potencial de Expansão</h3>
            </div>
            <div class="p-6">
                <div class="text-center mb-4">
                    <div class="w-20 h-20 mx-auto bg-blue-100 rounded-full flex items-center justify-center">
                        <span class="text-lg font-bold text-blue-600">+45%</span>
                    </div>
                    <p class="text-sm text-gray-600 mt-2">ARR Potencial</p>
                </div>
                <div class="space-y-3">
                    <div class="bg-blue-50 p-3 rounded-lg">
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium">Telemedicina</span>
                            <span class="text-sm font-bold text-blue-600">R$ 12k/mês</span>
                        </div>
                    </div>
                    <div class="bg-green-50 p-3 rounded-lg">
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium">BI Avançado</span>
                            <span class="text-sm font-bold text-green-600">R$ 8k/mês</span>
                        </div>
                    </div>
                    <div class="bg-purple-50 p-3 rounded-lg">
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium">API Premium</span>
                            <span class="text-sm font-bold text-purple-600">R$ 5k/mês</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Timeline de Ações e Roadmap -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">Roadmap Estratégico - Q1 2024</h3>
                <span class="text-sm text-gray-500">Próximos 90 dias</span>
            </div>
        </div>
        <div class="p-6">
            <div class="relative">
                <div class="absolute left-4 top-0 bottom-0 w-0.5 bg-gray-200"></div>

                <div class="relative flex items-start space-x-4 pb-8">
                    <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                        <span class="text-white text-xs font-bold">1</span>
                    </div>
                    <div class="flex-1">
                        <div class="flex items-center justify-between">
                            <h4 class="font-medium text-gray-900">Reunião Estratégica Trimestral</h4>
                            <span class="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">Jan 15</span>
                        </div>
                        <p class="text-sm text-gray-600 mt-1">Apresentar resultados Q4 e roadmap 2024. Discutir expansão de módulos e oportunidades de crescimento.</p>
                        <div class="flex items-center mt-2 space-x-4">
                            <span class="text-xs text-gray-500">Stakeholders: CEO, CFO, CTO</span>
                            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Presencial</span>
                        </div>
                    </div>
                </div>

                <div class="relative flex items-start space-x-4 pb-8">
                    <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                        <span class="text-white text-xs font-bold">2</span>
                    </div>
                    <div class="flex-1">
                        <div class="flex items-center justify-between">
                            <h4 class="font-medium text-gray-900">Implementação Telemedicina</h4>
                            <span class="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">Fev 1-28</span>
                        </div>
                        <p class="text-sm text-gray-600 mt-1">Onboarding do módulo de telemedicina. Treinamento da equipe médica.</p>
                        <div class="flex items-center mt-2 space-x-4">
                            <span class="text-xs text-gray-500">ROI esperado: +35% em 6 meses</span>
                            <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Alto impacto</span>
                        </div>
                    </div>
                </div>

                <div class="relative flex items-start space-x-4 pb-8">
                    <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                        <span class="text-white text-xs font-bold">3</span>
                    </div>
                    <div class="flex-1">
                        <div class="flex items-center justify-between">
                            <h4 class="font-medium text-gray-900">Otimização BI e Analytics</h4>
                            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Mar 1-15</span>
                        </div>
                        <p class="text-sm text-gray-600 mt-1">Upgrade para dashboards avançados. Implementação de alertas inteligentes.</p>
                        <div class="flex items-center mt-2 space-x-4">
                            <span class="text-xs text-gray-500">Melhoria na tomada de decisão</span>
                            <span class="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded">Diferencial</span>
                        </div>
                    </div>
                </div>

                <div class="relative flex items-start space-x-4">
                    <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                        <span class="text-white text-xs font-bold">4</span>
                    </div>
                    <div class="flex-1">
                        <div class="flex items-center justify-between">
                            <h4 class="font-medium text-gray-900">Renovação Contratual</h4>
                            <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Mar 30</span>
                        </div>
                        <p class="text-sm text-gray-600 mt-1">Negociação de renovação com expansão de 45% no ARR.</p>
                        <div class="flex items-center mt-2 space-x-4">
                            <span class="text-xs text-gray-500">Valor: R$ 300k anuais</span>
                            <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Meta Q1</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Animação do Índice Amigo
document.addEventListener('DOMContentLoaded', function() {
    const indiceElement = document.getElementById('indice-amigo');
    let currentValue = 0;
    const targetValue = 8.7;
    const increment = targetValue / 50;

    const animation = setInterval(() => {
        currentValue += increment;
        if (currentValue >= targetValue) {
            currentValue = targetValue;
            clearInterval(animation);
        }
        indiceElement.textContent = currentValue.toFixed(1);
    }, 30);

    // Carregar insights dinamicamente após 3 segundos
    setTimeout(() => {
        const pageContext = {
            page_title: "Cockpit KAM",
            page_description: "Dashboard estratégico para Key Account Management com foco em performance, risco e oportunidades de expansão",
            key_metrics: {
                "Índice Amigo": "8.7/10",
                "Revenue Growth": "R$ 2.4M (+12%)",
                "Platform Adoption": "87% (+15%)",
                "Churn Risk": "Baixo"
            },
            analysis_focus: "Gestão estratégica de contas, identificação de oportunidades de upselling, análise de risco de churn e roadmap de expansão",
            page_elements: [
                "Índice Amigo",
                "KPIs de Performance",
                "Health Score por Módulo",
                "Análise de Risco",
                "Potencial de Expansão",
                "Roadmap Estratégico"
            ]
        };

        // Simular carregamento de insights de IA
        console.log('Carregando insights para KAM...', pageContext);
    }, 3000);
});
</script>
{% endblock %}

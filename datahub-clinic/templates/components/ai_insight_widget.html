<!-- Widget de Insights com IA -->
<div class="bg-white rounded-view p-5 border border-gray-200 flex flex-col hover:shadow-sm transition-shadow duration-200 h-64 w-full">
    <div class="flex items-center mb-1">
        <svg class="w-4 h-4 text-systemBlue mr-2 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
        </svg>
        <span class="text-base font-semibold text-gray-800 truncate">{{ title }}</span>
    </div>
    <p class="text-xs text-label-secondary mb-2 line-clamp-1">{{ description }}</p>

    <div class="flex-grow overflow-y-auto">
        {% if insight_type == 'text' %}
            <p class="text-xs text-label-secondary">{{ content|safe }}</p>
        {% elif insight_type == 'list' %}
            <ul class="text-xs text-label-secondary space-y-1">
                {% for item in content %}
                    <li class="flex items-start">
                        <span class="text-systemBlue mr-1 flex-shrink-0">•</span>
                        <span>{{ item|safe }}</span>
                    </li>
                {% endfor %}
            </ul>
        {% elif insight_type == 'stat' %}
            <div>
                <div class="flex items-baseline">
                    <span class="text-2xl font-semibold text-label-DEFAULT">{{ content.value }}</span>
                    <span class="ml-2 text-xs {% if content.trend > 0 %}text-systemGreen{% elif content.trend < 0 %}text-red-500{% else %}text-label-secondary{% endif %}">
                        {% if content.trend > 0 %}↑{% elif content.trend < 0 %}↓{% else %}→{% endif %} {{ content.trend_text }}
                    </span>
                </div>
                <p class="text-xs text-label-secondary mt-1">{{ content.context }}</p>
            </div>
        {% elif insight_type == 'chart' %}
            <div class="h-24">
                <canvas id="{{ chart_id }}"></canvas>
            </div>
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    const ctx = document.getElementById('{{ chart_id }}').getContext('2d');
                    new Chart(ctx, {{ chart_config|tojson|safe }});
                });
            </script>
        {% endif %}
    </div>

    {% if category %}
        <div class="flex justify-between items-center mt-2 mb-2">
            <span class="inline-block bg-blue-100 text-blue-700 text-xs font-medium px-2 py-0.5 rounded-full">{{ category }}</span>
        </div>
    {% endif %}

    <div class="flex items-center text-xs pt-2 border-t border-gray-100 mt-auto">
        <div class="flex items-center ml-auto">
            <svg class="w-4 h-4 text-systemBlue mr-1 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
            </svg>
            <span class="text-gray-800 font-medium text-xs">Powered by Amigo Intelligence</span>
        </div>
    </div>
</div>

{% macro expose_context(title, description, key_metrics={}, analysis_focus="", page_elements=[], additional_context={}) %}
<script>
    // Define o contexto da página atual para o agente inteligente
    window.pageContext = {
        page_title: "{{ title }}",
        page_description: "{{ description }}",
        key_metrics: {{ key_metrics|tojson }},
        analysis_focus: "{{ analysis_focus }}",
        page_elements: {{ page_elements|tojson }}
        {% if additional_context %}
        {% for key, value in additional_context.items() %}
        , {{ key }}: {{ value|tojson }}
        {% endfor %}
        {% endif %}
    };

    // Adiciona timestamp
    window.pageContext.timestamp = new Date().toISOString();

    // Log para debug
    console.log("Contexto da página definido:", window.pageContext);
</script>
{% endmacro %}

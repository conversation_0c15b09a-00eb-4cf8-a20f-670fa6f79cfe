{% extends 'base.html' %}

{% block title %}Contas a Pagar - Amigo DataHub{% endblock %}

{% block header %}Contas a Pagar{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="bg-gradient-to-r from-blue-50 to-white rounded-view p-8 border border-gray-200 mb-6 overflow-hidden relative">
    <!-- Background Elements -->
    <div class="absolute top-0 right-0 w-full h-full opacity-10 overflow-hidden">
        <svg class="absolute top-10 right-10 w-64 h-64 text-systemBlue" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M2.25 18.75a60.07 60.07 0 0115.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 013 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 00-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 01-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 003 15h-.75M15 10.5a3 3 0 11-6 0 3 3 0 016 0zm3 0h.008v.008H18V10.5zm-12 0h.008v.008H6V10.5z"></path>
        </svg>
        <svg class="absolute bottom-10 left-1/4 w-32 h-32 text-systemBlue" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M10.464 8.746c.227-.18.497-.311.786-.394v2.795a2.252 2.252 0 01-.786-.393c-.394-.313-.546-.681-.546-1.004 0-.323.152-.691.546-1.004zM12.75 15.662v-2.824c.347.085.664.228.921.421.427.32.579.686.579.991 0 .305-.152.671-.579.991a2.534 2.534 0 01-.921.42z"></path>
            <path d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25zM12.75 6a.75.75 0 00-1.5 0v.816a3.836 3.836 0 00-1.72.756c-.712.566-1.112 1.35-1.112 2.178 0 .829.4 1.612 1.113 2.178.502.4 1.102.647 1.719.756v2.978a2.536 2.536 0 01-.921-.421l-.879-.66a.75.75 0 00-.9 1.2l.879.66c.533.4 1.169.645 1.821.75V18a.75.75 0 001.5 0v-.81a4.124 4.124 0 001.821-.749c.745-.559 1.179-1.344 1.179-2.191 0-.847-.434-1.632-1.179-2.191a4.122 4.122 0 00-1.821-.75V8.354c.29.082.559.213.786.393l.415.33a.75.75 0 00.933-1.175l-.415-.33a3.836 3.836 0 00-1.719-.755V6z"></path>
        </svg>
    </div>

    <div class="flex flex-col md:flex-row items-center relative z-10">
        <div class="md:w-2/3 mb-6 md:mb-0 md:pr-8">
            <div class="inline-block bg-systemBlue text-white text-xs font-semibold px-3 py-1 rounded-full mb-3">FINANCEIRO</div>
            <h1 class="text-3xl font-bold text-gray-800 mb-4">Gestão de Contas a Pagar</h1>
            <p class="text-gray-600 mb-6">Controle e otimize os pagamentos da sua clínica. Monitore vencimentos, categorize despesas e mantenha o fluxo de caixa saudável com análises detalhadas e recomendações inteligentes.</p>

            {% set total = contas|sum(attribute='valor') %}
            {% set pago = 0 %}
            {% set pendente = 0 %}
            {% set atrasado = 0 %}

            {% for conta in contas %}
                {% if conta.status == 'Pago' %}
                    {% set pago = pago + conta.valor %}
                {% elif conta.status == 'Pendente' %}
                    {% set pendente = pendente + conta.valor %}
                {% elif conta.status == 'Atrasado' %}
                    {% set atrasado = atrasado + conta.valor %}
                {% endif %}
            {% endfor %}

            <div class="grid grid-cols-3 gap-4 mb-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">
                        R$ {{ '{:,.0f}'.format(total).replace(',', '.') if total < 1000 else '{:.1f}K'.format(total/1000) if total < 1000000 else '{:.1f}M'.format(total/1000000) }}
                    </div>
                    <div class="text-xs text-gray-500">Total a pagar</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">
                        R$ {{ '{:,.0f}'.format(pendente).replace(',', '.') if pendente < 1000 else '{:.1f}K'.format(pendente/1000) if pendente < 1000000 else '{:.1f}M'.format(pendente/1000000) }}
                    </div>
                    <div class="text-xs text-gray-500">Pendente</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">
                        {{ (pago / total * 100)|round|int if total > 0 else 0 }}%
                    </div>
                    <div class="text-xs text-gray-500">Taxa de pagamento</div>
                </div>
            </div>
            <div class="flex flex-wrap gap-3">
                <button class="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Nova Conta
                </button>
                <button class="bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-md text-sm border border-gray-200 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                    </svg>
                    Exportar Relatório
                </button>
            </div>
        </div>
        <div class="md:w-1/3 flex justify-center">
            <div class="w-64 h-64 bg-white rounded-full shadow-lg flex items-center justify-center p-4 relative">
                <div class="absolute inset-0 rounded-full border-8 border-blue-100"></div>
                <div class="absolute inset-0 rounded-full border-8 border-systemBlue border-opacity-70" style="clip-path: polygon(50% 50%, 100% 0, 100% 50%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-300 border-opacity-70" style="clip-path: polygon(50% 50%, 100% 50%, 100% 100%, 50% 100%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-200 border-opacity-70" style="clip-path: polygon(50% 50%, 50% 100%, 0 100%, 0 50%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-400 border-opacity-70" style="clip-path: polygon(50% 50%, 0 50%, 0 0, 50% 0);"></div>
                <div class="z-10 text-center">
                    <div class="text-3xl font-bold text-gray-800">{{ (atrasado / total * 100)|round|int if total > 0 else 0 }}%</div>
                    <div class="text-sm text-gray-500">Taxa de atraso</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Insights com IA -->
<div class="mb-5">
    <div class="flex items-center justify-between mb-4">
        <div class="flex items-center">
            <svg class="w-5 h-5 text-systemBlue mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
            </svg>
            <h2 class="text-base font-semibold text-gray-800">Amigo Intelligence</h2>
        </div>
        <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">Atualizado agora</span>
    </div>

    <div id="insights-container" class="grid grid-cols-1 md:grid-cols-3 gap-3">
        {% for insight in insights %}
            {% with
                title=insight.title,
                description=insight.description,
                insight_type=insight.insight_type,
                content=insight.content,
                category=insight.category
            %}
                {% include 'components/ai_insight_widget.html' %}
            {% endwith %}
        {% endfor %}
    </div>
</div>

<script>
    // Carregar insights dinamicamente após 5 segundos (para demonstração)
    setTimeout(() => {
        // Contexto específico da página de contas a pagar
        const pageContext = {
            page_title: "Contas a Pagar",
            page_description: "Análise e gestão de contas a pagar, com foco em otimização de fluxo de caixa",
            key_metrics: {
                "Total a Pagar": document.querySelector('.kpi-total-pagar .value')?.textContent || "R$ 45.780,00",
                "Vencendo em 7 dias": document.querySelector('.kpi-vencendo-7dias .value')?.textContent || "R$ 12.450,00",
                "Pagamentos Atrasados": document.querySelector('.kpi-atrasados .value')?.textContent || "R$ 3.250,00"
            },
            analysis_focus: "Otimização de fluxo de caixa e gestão de pagamentos",
            page_elements: [
                "Distribuição por Categoria",
                "Distribuição por Vencimento",
                "Evolução Mensal",
                "Principais Fornecedores"
            ]
        };

        loadInsights('financeiro', 'contas_pagar', 'insights-container', 3, ['list', 'text', 'stat'], pageContext);
    }, 5000);
</script>

<!-- Insights Estáticos (Temporários) -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6 hidden">
    <!-- Insight de Análise -->
    {% with
        title="Análise de Despesas",
        description="Distribuição por categoria",
        insight_type="list",
        content=[
            "Material Médico representa <strong>32%</strong> das despesas totais",
            "Despesas com Salários correspondem a <strong>28%</strong> do total",
            "Custos fixos (aluguel, energia, água) somam <strong>18%</strong> das despesas"
        ],
        category="Análise",
        action_text="Ver detalhes"
    %}
        {% include 'components/ai_insight_widget.html' %}
    {% endwith %}

    <!-- Insight de Previsão -->
    {% with
        title="Previsão de Fluxo",
        description="Próximos 30 dias",
        insight_type="list",
        content=[
            "Vencimentos previstos: <strong>R$ {{ pendente|round|int }}</strong>",
            "Pico de pagamentos em <strong>15/08/2023</strong> (R$ {{ (pendente * 0.35)|round|int }})",
            "Disponibilidade projetada: <strong>{{ 100 - (pendente / total * 100)|round|int if total > 0 else 100 }}%</strong> do valor necessário"
        ],
        category="Previsão",
        action_text="Ver projeção"
    %}
        {% include 'components/ai_insight_widget.html' %}
    {% endwith %}

    <!-- Insight de Recomendações -->
    {% with
        title="Recomendações de Otimização",
        description="Oportunidades de economia",
        insight_type="text",
        content="Renegociar contratos com os <strong>3 principais fornecedores</strong> pode gerar economia de até <strong>12%</strong>. Implementar <strong>pagamento antecipado</strong> para fornecedores que oferecem desconto pode reduzir custos em aproximadamente <strong>R$ {{ (total * 0.05)|round|int }}</strong> por mês.",
        category="Economia",
        action_text="Implementar ações"
    %}
        {% include 'components/ai_insight_widget.html' %}
    {% endwith %}
</div>

<!-- Resumo -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <!-- Total a Pagar -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-label-DEFAULT">Total a Pagar</p>
        </div>
        <p class="text-3xl font-semibold text-label-DEFAULT mb-1">
            R$ {{ '{:,.2f}'.format(contas|sum(attribute='valor')).replace(',', '.') if contas|sum(attribute='valor') < 1000 else '{:.1f}K'.format(contas|sum(attribute='valor')/1000) if contas|sum(attribute='valor') < 1000000 else '{:.1f}M'.format(contas|sum(attribute='valor')/1000000) }}
        </p>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Valor total a pagar.</p>
    </div>

    <!-- Pago -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-label-DEFAULT">Pago</p>
        </div>
        <p class="text-3xl font-semibold text-systemGreen mb-1">
            {% set pago = 0 %}
            {% for conta in contas %}
                {% if conta.status == 'Pago' %}
                    {% set pago = pago + conta.valor %}
                {% endif %}
            {% endfor %}
            R$ {{ '{:,.2f}'.format(pago).replace(',', '.') if pago < 1000 else '{:.1f}K'.format(pago/1000) if pago < 1000000 else '{:.1f}M'.format(pago/1000000) }}
        </p>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Valor já pago.</p>
    </div>

    <!-- Pendente -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-label-DEFAULT">Pendente</p>
        </div>
        <p class="text-3xl font-semibold text-systemBlue mb-1">
            {% set pendente = 0 %}
            {% for conta in contas %}
                {% if conta.status == 'Pendente' %}
                    {% set pendente = pendente + conta.valor %}
                {% endif %}
            {% endfor %}
            R$ {{ '{:,.2f}'.format(pendente).replace(',', '.') if pendente < 1000 else '{:.1f}K'.format(pendente/1000) if pendente < 1000000 else '{:.1f}M'.format(pendente/1000000) }}
        </p>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Valor pendente de pagamento.</p>
    </div>

    <!-- Atrasado -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-label-DEFAULT">Atrasado</p>
        </div>
        <p class="text-3xl font-semibold text-red-600 mb-1">
            {% set atrasado = 0 %}
            {% for conta in contas %}
                {% if conta.status == 'Atrasado' %}
                    {% set atrasado = atrasado + conta.valor %}
                {% endif %}
            {% endfor %}
            R$ {{ '{:,.2f}'.format(atrasado).replace(',', '.') if atrasado < 1000 else '{:.1f}K'.format(atrasado/1000) if atrasado < 1000000 else '{:.1f}M'.format(atrasado/1000000) }}
        </p>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Valor com pagamento atrasado.</p>
    </div>
</div>

<!-- Filtros -->
<div class="bg-white rounded-view p-4 mb-6 border border-gray-200">
    <div class="flex flex-wrap gap-4">
        <div class="w-full md:w-auto">
            <label for="data_vencimento_inicio" class="block text-xs font-medium text-label-secondary mb-1">Vencimento Início</label>
            <input type="date" id="data_vencimento_inicio" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
        </div>
        <div class="w-full md:w-auto">
            <label for="data_vencimento_fim" class="block text-xs font-medium text-label-secondary mb-1">Vencimento Fim</label>
            <input type="date" id="data_vencimento_fim" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
        </div>
        <div class="w-full md:w-auto">
            <label for="status" class="block text-xs font-medium text-label-secondary mb-1">Status</label>
            <select id="status" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
                <option value="">Todos</option>
                <option>Pago</option>
                <option>Pendente</option>
                <option>Atrasado</option>
            </select>
        </div>
        <div class="w-full md:w-auto">
            <label for="categoria" class="block text-xs font-medium text-label-secondary mb-1">Categoria</label>
            <select id="categoria" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
                <option value="">Todas</option>
                <option>Material Médico</option>
                <option>Medicamentos</option>
                <option>Aluguel</option>
                <option>Energia</option>
                <option>Água</option>
                <option>Internet</option>
                <option>Telefone</option>
                <option>Impostos</option>
                <option>Salários</option>
            </select>
        </div>
        <div class="w-full md:w-auto flex items-end">
            <button class="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-control transition duration-150 ease-in-out text-sm">
                Filtrar
            </button>
        </div>
    </div>
</div>

<!-- Gráficos -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Contas por Status -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <h2 class="text-base font-semibold text-label-DEFAULT mb-4">Contas por Status</h2>
        <div class="h-64">
            <canvas id="statusChart"></canvas>
        </div>
    </div>

    <!-- Contas por Categoria -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <h2 class="text-base font-semibold text-label-DEFAULT mb-4">Contas por Categoria</h2>
        <div class="h-64">
            <canvas id="categoriaChart"></canvas>
        </div>
    </div>
</div>

<!-- Tabela de Contas a Pagar -->
<div class="bg-white rounded-view p-6 border border-gray-200">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-base font-semibold text-label-DEFAULT">Detalhamento das Contas a Pagar</h2>
        <div class="flex space-x-2">
            <button class="bg-systemGray-ultralight hover:bg-systemGray-extralight text-gray-800 px-3 py-1 rounded-full transition duration-150 ease-in-out text-xs border border-systemGray-lightest">
                Exportar
            </button>
            <button class="bg-systemGray-ultralight hover:bg-systemGray-extralight text-gray-800 px-3 py-1 rounded-full transition duration-150 ease-in-out text-xs border border-systemGray-lightest">
                Imprimir
            </button>
        </div>
    </div>

    <div class="overflow-x-auto">
        <table class="w-full text-sm data-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Data Emissão</th>
                    <th>Data Vencimento</th>
                    <th>Fornecedor</th>
                    <th>Categoria</th>
                    <th>Descrição</th>
                    <th>Valor</th>
                    <th>Status</th>
                    <th>Ações</th>
                </tr>
            </thead>
            <tbody>
                {% for conta in contas %}
                <tr>
                    <td>{{ conta.id }}</td>
                    <td>{{ conta.data_emissao }}</td>
                    <td>{{ conta.data_vencimento }}</td>
                    <td>{{ conta.fornecedor }}</td>
                    <td>{{ conta.categoria }}</td>
                    <td>{{ conta.descricao }}</td>
                    <td>R$ {{ "%.2f"|format(conta.valor) }}</td>
                    <td>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                        {% if conta.status == 'Pago' %}
                            bg-green-100 text-green-800
                        {% elif conta.status == 'Pendente' %}
                            bg-blue-100 text-blue-800
                        {% elif conta.status == 'Atrasado' %}
                            bg-red-100 text-red-800
                        {% endif %}
                        ">
                            {{ conta.status }}
                        </span>
                    </td>
                    <td>
                        <div class="flex space-x-1">
                            <button class="p-1 text-systemBlue hover:bg-systemGray-ultralight rounded">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                            </button>
                            <button class="p-1 text-systemBlue hover:bg-systemGray-ultralight rounded">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                            </button>
                            <button class="p-1 text-systemBlue hover:bg-systemGray-ultralight rounded">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </button>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Paginação -->
    <div class="flex justify-between items-center mt-4">
        <div class="text-sm text-label-secondary">
            Mostrando 1-20 de {{ contas|length }} resultados
        </div>
        <div class="flex space-x-1">
            <button class="px-3 py-1 rounded-md bg-systemGray-ultralight text-label-secondary text-sm">Anterior</button>
            <button class="px-3 py-1 rounded-md bg-systemBlue text-white text-sm">1</button>
            <button class="px-3 py-1 rounded-md bg-systemGray-ultralight text-label-secondary text-sm">2</button>
            <button class="px-3 py-1 rounded-md bg-systemGray-ultralight text-label-secondary text-sm">3</button>
            <button class="px-3 py-1 rounded-md bg-systemGray-ultralight text-label-secondary text-sm">Próximo</button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Calcular contas por status
        const statusData = {
            'Pago': 0,
            'Pendente': 0,
            'Atrasado': 0
        };

        const statusValores = {
            'Pago': 0,
            'Pendente': 0,
            'Atrasado': 0
        };

        {% for conta in contas %}
        statusData['{{ conta.status }}'] += 1;
        statusValores['{{ conta.status }}'] += {{ conta.valor }};
        {% endfor %}

        // Calcular contas por categoria
        const categoriaData = {};

        {% for conta in contas %}
        if ('{{ conta.categoria }}' in categoriaData) {
            categoriaData['{{ conta.categoria }}'] += {{ conta.valor }};
        } else {
            categoriaData['{{ conta.categoria }}'] = {{ conta.valor }};
        }
        {% endfor %}

        const categoriaLabels = Object.keys(categoriaData);
        const categoriaValues = Object.values(categoriaData);

        // Configurar gráficos
        const statusChart = new Chart(
            document.getElementById('statusChart'),
            {
                type: 'pie',
                data: {
                    labels: Object.keys(statusValores),
                    datasets: [{
                        data: Object.values(statusValores),
                        backgroundColor: [
                            'rgba(0, 122, 255, 0.7)',
                            'rgba(142, 142, 147, 0.7)',
                            'rgba(174, 174, 178, 0.7)'
                        ],
                        borderColor: [
                            'rgba(0, 122, 255, 1)',
                            'rgba(142, 142, 147, 1)',
                            'rgba(174, 174, 178, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    return `${label}: R$ ${value.toFixed(2)}`;
                                }
                            }
                        }
                    }
                }
            }
        );

        const categoriaChart = new Chart(
            document.getElementById('categoriaChart'),
            {
                type: 'bar',
                data: {
                    labels: categoriaLabels,
                    datasets: [{
                        label: 'Valor (R$)',
                        data: categoriaValues,
                        backgroundColor: 'rgba(0, 122, 255, 0.7)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            }
        );
    });
</script>
{% endblock %}

{% extends 'base.html' %}

{% block title %}Contas a Receber - Amigo DataHub{% endblock %}

{% block header %}Contas a Receber{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="bg-gradient-to-r from-blue-50 to-white rounded-view p-8 border border-gray-200 mb-6 overflow-hidden relative">
    <!-- Background Elements -->
    <div class="absolute top-0 right-0 w-full h-full opacity-10 overflow-hidden">
        <svg class="absolute top-10 right-10 w-64 h-64 text-systemBlue" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <svg class="absolute bottom-10 left-1/4 w-32 h-32 text-systemBlue" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
        </svg>
    </div>

    <div class="flex flex-col md:flex-row items-center relative z-10">
        <div class="md:w-2/3 mb-6 md:mb-0 md:pr-8">
            <div class="inline-block bg-systemBlue text-white text-xs font-semibold px-3 py-1 rounded-full mb-3">FINANCEIRO</div>
            <h1 class="text-3xl font-bold text-gray-800 mb-4">Gestão de Contas a Receber</h1>
            <p class="text-gray-600 mb-6">Monitore, analise e otimize o fluxo de recebimentos da sua clínica. Identifique tendências, reduza a inadimplência e melhore a saúde financeira do seu negócio.</p>

            {% set total = contas|sum(attribute='valor') %}
            {% set recebido = 0 %}
            {% set pendente = 0 %}
            {% set atrasado = 0 %}

            {% for conta in contas %}
                {% if conta.status == 'Pago' %}
                    {% set recebido = recebido + conta.valor %}
                {% elif conta.status == 'Pendente' %}
                    {% set pendente = pendente + conta.valor %}
                {% elif conta.status == 'Atrasado' %}
                    {% set atrasado = atrasado + conta.valor %}
                {% endif %}
            {% endfor %}

            <div class="grid grid-cols-3 gap-4 mb-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">
                        R$ {{ '{:,.0f}'.format(total).replace(',', '.') if total < 1000 else '{:.1f}K'.format(total/1000) if total < 1000000 else '{:.1f}M'.format(total/1000000) }}
                    </div>
                    <div class="text-xs text-gray-500">Total a receber</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">
                        R$ {{ '{:,.0f}'.format(recebido).replace(',', '.') if recebido < 1000 else '{:.1f}K'.format(recebido/1000) if recebido < 1000000 else '{:.1f}M'.format(recebido/1000000) }}
                    </div>
                    <div class="text-xs text-gray-500">Recebido</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">
                        {{ (recebido / total * 100)|round|int if total > 0 else 0 }}%
                    </div>
                    <div class="text-xs text-gray-500">Taxa de recebimento</div>
                </div>
            </div>
            <div class="flex flex-wrap gap-3">
                <button class="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Nova Conta
                </button>
                <button class="bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-md text-sm border border-gray-200 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                    </svg>
                    Exportar Relatório
                </button>
            </div>
        </div>
        <div class="md:w-1/3 flex justify-center">
            <div class="w-64 h-64 bg-white rounded-full shadow-lg flex items-center justify-center p-4 relative">
                <div class="absolute inset-0 rounded-full border-8 border-blue-100"></div>
                <div class="absolute inset-0 rounded-full border-8 border-systemBlue border-opacity-70" style="clip-path: polygon(50% 50%, 100% 0, 100% 50%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-300 border-opacity-70" style="clip-path: polygon(50% 50%, 100% 50%, 100% 100%, 50% 100%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-200 border-opacity-70" style="clip-path: polygon(50% 50%, 50% 100%, 0 100%, 0 50%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-400 border-opacity-70" style="clip-path: polygon(50% 50%, 0 50%, 0 0, 50% 0);"></div>
                <div class="z-10 text-center">
                    <div class="text-3xl font-bold text-gray-800">{{ (atrasado / total * 100)|round|int if total > 0 else 0 }}%</div>
                    <div class="text-sm text-gray-500">Taxa de inadimplência</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Insights com IA -->
<div class="mb-5">
    <div class="flex items-center justify-between mb-4">
        <div class="flex items-center">
            <svg class="w-5 h-5 text-systemBlue mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
            </svg>
            <h2 class="text-base font-semibold text-gray-800">Amigo Intelligence</h2>
        </div>
        <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">Atualizado agora</span>
    </div>

    <div id="insights-container" class="grid grid-cols-1 md:grid-cols-3 gap-3">
        {% for insight in insights %}
            {% with
                title=insight.title,
                description=insight.description,
                insight_type=insight.insight_type,
                content=insight.content,
                category=insight.category
            %}
                {% include 'components/ai_insight_widget.html' %}
            {% endwith %}
        {% endfor %}
    </div>
</div>

<script>
    // Carregar insights dinamicamente após 5 segundos (para demonstração)
    setTimeout(() => {
        // Contexto específico da página de contas a receber
        const pageContext = {
            page_title: "Contas a Receber",
            page_description: "Análise e gestão de contas a receber, com foco em otimização de fluxo de caixa e redução de inadimplência",
            key_metrics: {
                "Total a Receber": "R$ {{ '{:,.2f}'.format(total).replace(',', '.') }}",
                "Recebido": "R$ {{ '{:,.2f}'.format(recebido).replace(',', '.') }}",
                "Pendente": "R$ {{ '{:,.2f}'.format(pendente).replace(',', '.') }}",
                "Atrasado": "R$ {{ '{:,.2f}'.format(atrasado).replace(',', '.') }}"
            },
            analysis_focus: "Otimização de fluxo de caixa e redução de inadimplência",
            page_elements: [
                "Contas por Status",
                "Contas por Forma de Pagamento",
                "Tendência de Recebimentos",
                "Análise de Aging"
            ]
        };

        loadInsights('financeiro', 'contas_receber', 'insights-container', 3, ['list', 'text', 'stat'], pageContext);
    }, 5000);
</script>

<!-- Insights Estáticos (Temporários) -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6 hidden">
    <!-- Insight de Previsão -->
    {% with
        title="Previsão de Recebimentos",
        description="Próximos 30 dias",
        insight_type="list",
        content=[
            "Previsão de recebimento: <strong>R$ {{ (pendente * 0.85)|round|int }}</strong>",
            "Probabilidade de recebimento: <strong>85%</strong>",
            "Contas com vencimento até 15 dias têm <strong>92% de chance</strong> de pagamento em dia"
        ],
        category="Previsão",
        action_text="Ver detalhes"
    %}
        {% include 'components/ai_insight_widget.html' %}
    {% endwith %}

    <!-- Insight de Perfil -->
    {% with
        title="Perfil de Pagadores",
        description="Análise de comportamento",
        insight_type="list",
        content=[
            "<strong>68%</strong> dos clientes são pagadores pontuais",
            "<strong>22%</strong> apresentam atraso ocasional (até 15 dias)",
            "Pacientes com plano de saúde têm <strong>95% de pontualidade</strong> nos pagamentos"
        ],
        category="Análise",
        action_text="Ver segmentação"
    %}
        {% include 'components/ai_insight_widget.html' %}
    {% endwith %}

    <!-- Insight de Recomendações -->
    {% with
        title="Recomendações Estratégicas",
        description="Ações para otimização",
        insight_type="text",
        content="Implementar <strong>lembrete automático 3 dias antes</strong> do vencimento pode reduzir atrasos em até 35%. Oferecer <strong>desconto de 3% para pagamentos antecipados</strong> pode acelerar o ciclo de recebimento em 8 dias em média. Priorize contato com os <strong>10 maiores devedores</strong> (R$ {{ (atrasado * 0.65)|round|int }}).",
        category="Estratégia",
        action_text="Implementar ações"
    %}
        {% include 'components/ai_insight_widget.html' %}
    {% endwith %}
</div>

<!-- Resumo e Insights -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <!-- Total a Receber -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-label-DEFAULT">Total a Receber</p>
        </div>
        {% set total = contas|sum(attribute='valor') %}
        <p class="text-3xl font-semibold text-label-DEFAULT mb-1">
            R$ {{ '{:,.2f}'.format(total).replace(',', '.') if total < 1000 else '{:.1f}K'.format(total/1000) if total < 1000000 else '{:.1f}M'.format(total/1000000) }}
        </p>
        <div class="flex items-center text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">
            <span>Valor total a receber.</span>
            <span class="ml-auto text-systemGreen font-medium">+5.2%</span>
        </div>
    </div>

    <!-- Recebido -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-label-DEFAULT">Recebido</p>
        </div>
        {% set recebido = 0 %}
        {% for conta in contas %}
            {% if conta.status == 'Pago' %}
                {% set recebido = recebido + conta.valor %}
            {% endif %}
        {% endfor %}
        <p class="text-3xl font-semibold text-label-DEFAULT mb-1">
            R$ {{ '{:,.2f}'.format(recebido).replace(',', '.') if recebido < 1000 else '{:.1f}K'.format(recebido/1000) if recebido < 1000000 else '{:.1f}M'.format(recebido/1000000) }}
        </p>
        <div class="flex items-center text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">
            <span>Valor já recebido.</span>
            <span class="ml-auto text-systemGreen font-medium">{{ (recebido / total * 100)|round|int }}%</span>
        </div>
    </div>

    <!-- Pendente -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-label-DEFAULT">Pendente</p>
        </div>
        {% set pendente = 0 %}
        {% for conta in contas %}
            {% if conta.status == 'Pendente' %}
                {% set pendente = pendente + conta.valor %}
            {% endif %}
        {% endfor %}
        <p class="text-3xl font-semibold text-label-DEFAULT mb-1">
            R$ {{ '{:,.2f}'.format(pendente).replace(',', '.') if pendente < 1000 else '{:.1f}K'.format(pendente/1000) if pendente < 1000000 else '{:.1f}M'.format(pendente/1000000) }}
        </p>
        <div class="flex items-center text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">
            <span>Valor pendente de recebimento.</span>
            <span class="ml-auto text-systemBlue font-medium">{{ (pendente / total * 100)|round|int }}%</span>
        </div>
    </div>

    <!-- Atrasado -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-label-DEFAULT">Atrasado</p>
        </div>
        {% set atrasado = 0 %}
        {% for conta in contas %}
            {% if conta.status == 'Atrasado' %}
                {% set atrasado = atrasado + conta.valor %}
            {% endif %}
        {% endfor %}
        <p class="text-3xl font-semibold {% if (atrasado / total * 100) > 10 %}text-red-600{% else %}text-label-DEFAULT{% endif %} mb-1">
            R$ {{ '{:,.2f}'.format(atrasado).replace(',', '.') if atrasado < 1000 else '{:.1f}K'.format(atrasado/1000) if atrasado < 1000000 else '{:.1f}M'.format(atrasado/1000000) }}
        </p>
        <div class="flex items-center text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">
            <span>Valor com pagamento atrasado.</span>
            <span class="ml-auto {% if (atrasado / total * 100) > 10 %}text-red-600{% else %}text-orange-500{% endif %} font-medium">{{ (atrasado / total * 100)|round|int }}%</span>
        </div>
    </div>
</div>

<!-- Insights e Previsões -->
<div class="bg-white rounded-view p-5 border border-gray-200 mb-6">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-base font-semibold text-label-DEFAULT">Insights Financeiros</h2>
        <div class="flex items-center">
            <svg class="w-4 h-4 text-systemBlue mr-1" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 1L9 9H2L7 14.5L5 22L12 17.5L19 22L17 14.5L22 9H15L12 1Z"></path>
            </svg>
            <span class="text-xs text-systemBlue font-medium">AI Analytics</span>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Previsão de Recebimentos -->
        <div class="bg-systemGray-ultralight p-4 rounded-view">
            <div class="flex items-center mb-3">
                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-sm font-semibold">Previsão de Recebimentos</h3>
                    <p class="text-xs text-label-secondary">Próximos 30 dias</p>
                </div>
            </div>
            <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                    <span>Previsão de recebimento:</span>
                    <span class="font-medium text-systemGreen">R$ {{ "%.2f"|format(pendente * 0.85) }}</span>
                </div>
                <div class="flex justify-between">
                    <span>Probabilidade de recebimento:</span>
                    <span class="font-medium text-systemBlue">85%</span>
                </div>
                <div class="flex justify-between">
                    <span>Risco de inadimplência:</span>
                    <span class="font-medium text-orange-500">15%</span>
                </div>
                <div class="mt-3 pt-2 border-t border-gray-200">
                    <p class="text-xs text-label-secondary"><strong>Insight:</strong> Contas com vencimento até 15 dias têm 92% de chance de pagamento em dia.</p>
                </div>
            </div>
        </div>

        <!-- Perfil de Pagadores -->
        <div class="bg-systemGray-ultralight p-4 rounded-view">
            <div class="flex items-center mb-3">
                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-sm font-semibold">Perfil de Pagadores</h3>
                    <p class="text-xs text-label-secondary">Análise de comportamento</p>
                </div>
            </div>
            <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                    <span>Pagadores pontuais:</span>
                    <span class="font-medium text-systemGreen">68%</span>
                </div>
                <div class="flex justify-between">
                    <span>Pagadores com atraso ocasional:</span>
                    <span class="font-medium text-systemBlue">22%</span>
                </div>
                <div class="flex justify-between">
                    <span>Pagadores com atraso frequente:</span>
                    <span class="font-medium text-red-500">10%</span>
                </div>
                <div class="mt-3 pt-2 border-t border-gray-200">
                    <p class="text-xs text-label-secondary"><strong>Insight:</strong> Pacientes com plano de saúde apresentam 95% de pontualidade nos pagamentos.</p>
                </div>
            </div>
        </div>

        <!-- Recomendações -->
        <div class="bg-systemGray-ultralight p-4 rounded-view">
            <div class="flex items-center mb-3">
                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-sm font-semibold">Recomendações</h3>
                    <p class="text-xs text-label-secondary">Ações para otimização</p>
                </div>
            </div>
            <ul class="space-y-2 text-sm">
                <li class="flex items-start">
                    <svg class="w-4 h-4 text-systemBlue mr-1 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <span>Implementar lembrete automático 3 dias antes do vencimento</span>
                </li>
                <li class="flex items-start">
                    <svg class="w-4 h-4 text-systemBlue mr-1 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <span>Oferecer desconto de 3% para pagamentos antecipados</span>
                </li>
                <li class="flex items-start">
                    <svg class="w-4 h-4 text-systemBlue mr-1 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <span>Priorizar contato com os 10 maiores devedores (R$ {{ "%.2f"|format(atrasado * 0.65) }})</span>
                </li>
            </ul>
        </div>
    </div>
</div>

<!-- Filtros -->
<div class="bg-white rounded-view p-4 mb-6 border border-gray-200">
    <div class="flex flex-wrap gap-4">
        <div class="w-full md:w-auto">
            <label for="data_vencimento_inicio" class="block text-xs font-medium text-label-secondary mb-1">Vencimento Início</label>
            <input type="date" id="data_vencimento_inicio" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
        </div>
        <div class="w-full md:w-auto">
            <label for="data_vencimento_fim" class="block text-xs font-medium text-label-secondary mb-1">Vencimento Fim</label>
            <input type="date" id="data_vencimento_fim" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
        </div>
        <div class="w-full md:w-auto">
            <label for="status" class="block text-xs font-medium text-label-secondary mb-1">Status</label>
            <select id="status" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
                <option value="">Todos</option>
                <option>Pago</option>
                <option>Pendente</option>
                <option>Atrasado</option>
            </select>
        </div>
        <div class="w-full md:w-auto">
            <label for="forma_pagamento" class="block text-xs font-medium text-label-secondary mb-1">Forma de Pagamento</label>
            <select id="forma_pagamento" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
                <option value="">Todas</option>
                <option>Cartão</option>
                <option>Pix</option>
                <option>Dinheiro</option>
                <option>Crédito de Procedimento</option>
            </select>
        </div>
        <div class="w-full md:w-auto flex items-end">
            <button class="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-control transition duration-150 ease-in-out text-sm">
                Filtrar
            </button>
        </div>
    </div>
</div>

<!-- Card de Insight Flutuante - Estratégia de Recuperação -->
<div class="bg-white rounded-view p-5 border border-gray-200 mb-6 relative overflow-hidden">
    <div class="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-blue-50 to-transparent rounded-full opacity-50 -mt-32 -mr-32"></div>

    <div class="relative z-10">
        <div class="flex items-center justify-between mb-3">
            <div class="flex items-center">
                <svg class="w-5 h-5 text-systemBlue mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
                </svg>
                <span class="text-base font-semibold text-gray-800">Estratégia de Recuperação Inteligente</span>
            </div>
            <span class="inline-block bg-blue-100 text-blue-700 text-xs font-medium px-2 py-0.5 rounded-full">Recuperação</span>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-5 mb-4">
            <div>
                <h3 class="text-sm font-medium text-gray-700 mb-2">Segmentação de Inadimplentes</h3>
                <div class="space-y-2">
                    <div class="flex justify-between items-center">
                        <span class="text-xs text-gray-600">Atraso leve (1-15 dias)</span>
                        <div class="flex items-center">
                            <span class="text-xs font-medium text-gray-800 mr-2">R$ {{ '{:,.0f}'.format(atrasado * 0.45).replace(',', '.') }}</span>
                            <div class="w-16 h-1.5 bg-gray-100 rounded-full overflow-hidden">
                                <div class="h-full bg-blue-300" style="width: 45%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-xs text-gray-600">Atraso moderado (16-30 dias)</span>
                        <div class="flex items-center">
                            <span class="text-xs font-medium text-gray-800 mr-2">R$ {{ '{:,.0f}'.format(atrasado * 0.35).replace(',', '.') }}</span>
                            <div class="w-16 h-1.5 bg-gray-100 rounded-full overflow-hidden">
                                <div class="h-full bg-blue-400" style="width: 35%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-xs text-gray-600">Atraso crítico (>30 dias)</span>
                        <div class="flex items-center">
                            <span class="text-xs font-medium text-gray-800 mr-2">R$ {{ '{:,.0f}'.format(atrasado * 0.20).replace(',', '.') }}</span>
                            <div class="w-16 h-1.5 bg-gray-100 rounded-full overflow-hidden">
                                <div class="h-full bg-blue-500" style="width: 20%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div>
                <h3 class="text-sm font-medium text-gray-700 mb-2">Estratégias Recomendadas</h3>
                <ul class="space-y-2 text-xs text-gray-600">
                    <li class="flex items-start">
                        <span class="text-systemBlue mr-1 flex-shrink-0">•</span>
                        <span><strong>Atraso leve:</strong> Envio de lembretes amigáveis via WhatsApp (taxa de recuperação: 85%)</span>
                    </li>
                    <li class="flex items-start">
                        <span class="text-systemBlue mr-1 flex-shrink-0">•</span>
                        <span><strong>Atraso moderado:</strong> Oferta de parcelamento sem juros em até 3x (taxa de recuperação: 72%)</span>
                    </li>
                    <li class="flex items-start">
                        <span class="text-systemBlue mr-1 flex-shrink-0">•</span>
                        <span><strong>Atraso crítico:</strong> Contato telefônico personalizado + desconto de 5% (taxa de recuperação: 48%)</span>
                    </li>
                </ul>
            </div>
        </div>

        <div class="bg-blue-50 p-3 rounded-lg">
            <p class="text-xs text-gray-700"><strong>Impacto Projetado:</strong> Implementando estas estratégias, a recuperação estimada é de <strong>R$ {{ '{:,.0f}'.format(atrasado * 0.75).replace(',', '.') }}</strong> (75% do total atrasado) nos próximos 30 dias, reduzindo a taxa de inadimplência para menos de 5%.</p>
            <div class="mt-2 flex justify-end">
                <button class="bg-systemBlue hover:bg-blue-600 text-white px-3 py-1 rounded text-xs transition-colors">
                    Implementar Estratégia
                </button>
            </div>
        </div>

        <div class="mt-3 pt-2 border-t border-gray-100 flex justify-between items-center">
            <p class="text-xs text-gray-500">Análise baseada em histórico de pagamentos e comportamento de clientes similares</p>
            <span class="text-xs text-gray-500">Powered by Amigo Intelligence</span>
        </div>
    </div>
</div>

<!-- Análises Avançadas -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Contas por Status -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Contas por Status</h2>
            <div class="flex items-center">
                <select id="visualizacaoStatus" class="text-xs border-systemGray-lightest rounded-control mr-2">
                    <option value="pie">Pizza</option>
                    <option value="doughnut">Rosca</option>
                    <option value="bar">Barras</option>
                </select>
            </div>
        </div>
        <div class="h-64">
            <canvas id="statusChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> A taxa de contas pagas está 5% acima da média do setor. Contas atrasadas representam apenas {{ (atrasado / total * 100)|round|int }}% do total.</p>
        </div>
    </div>

    <!-- Contas por Forma de Pagamento -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Contas por Forma de Pagamento</h2>
            <div class="flex items-center">
                <select id="visualizacaoFormaPagamento" class="text-xs border-systemGray-lightest rounded-control mr-2">
                    <option value="bar">Barras</option>
                    <option value="horizontalBar">Barras Horizontais</option>
                    <option value="polarArea">Polar</option>
                </select>
            </div>
        </div>
        <div class="h-64">
            <canvas id="formaPagamentoChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> Pagamentos via Pix apresentam menor taxa de atraso (2.5%) e maior velocidade de liquidação (0.8 dias).</p>
        </div>
    </div>
</div>

<!-- Tendências e Previsões -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Tendência de Recebimentos -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Tendência de Recebimentos</h2>
            <div class="flex items-center">
                <svg class="w-4 h-4 text-systemBlue mr-1" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 1L9 9H2L7 14.5L5 22L12 17.5L19 22L17 14.5L22 9H15L12 1Z"></path>
                </svg>
                <span class="text-xs text-systemBlue font-medium">ML Forecast</span>
            </div>
        </div>
        <div class="h-64">
            <canvas id="tendenciaRecebimentosChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Previsão:</strong> Aumento projetado de 12% nos recebimentos para o próximo trimestre, com redução de 5% na taxa de inadimplência.</p>
        </div>
    </div>

    <!-- Análise de Aging -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Análise de Aging</h2>
            <div class="flex items-center">
                <svg class="w-4 h-4 text-systemBlue mr-1" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 1L9 9H2L7 14.5L5 22L12 17.5L19 22L17 14.5L22 9H15L12 1Z"></path>
                </svg>
                <span class="text-xs text-systemBlue font-medium">Data Analysis</span>
            </div>
        </div>
        <div class="h-64">
            <canvas id="agingChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> 85% das contas são pagas em até 30 dias. Contas com mais de 90 dias representam apenas 3% do total, mas 15% do valor atrasado.</p>
        </div>
    </div>
</div>

<!-- Tabela de Contas a Receber -->
<div class="bg-white rounded-view p-6 border border-gray-200">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-base font-semibold text-label-DEFAULT">Detalhamento das Contas a Receber</h2>
        <div class="flex space-x-2">
            <button class="bg-systemGray-ultralight hover:bg-systemGray-extralight text-gray-800 px-3 py-1 rounded-full transition duration-150 ease-in-out text-xs border border-systemGray-lightest">
                Exportar
            </button>
            <button class="bg-systemGray-ultralight hover:bg-systemGray-extralight text-gray-800 px-3 py-1 rounded-full transition duration-150 ease-in-out text-xs border border-systemGray-lightest">
                Imprimir
            </button>
        </div>
    </div>

    <div class="overflow-x-auto">
        <table class="w-full text-sm data-table">
            <thead>
                <tr>
                    <th>Código</th>
                    <th>Data Emissão</th>
                    <th>Data Vencimento</th>
                    <th>Paciente</th>
                    <th>Descrição</th>
                    <th>Forma Pagamento</th>
                    <th>Valor</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                {% for conta in contas %}
                <tr>
                    <td>{{ conta.codigo }}</td>
                    <td>{{ conta.data_emissao }}</td>
                    <td>{{ conta.data_vencimento }}</td>
                    <td>{{ conta.paciente }}</td>
                    <td>{{ conta.descricao }}</td>
                    <td>{{ conta.forma_pagamento }}</td>
                    <td>R$ {{ "%.2f"|format(conta.valor) }}</td>
                    <td>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                        {% if conta.status == 'Pago' %}
                            bg-green-100 text-green-800
                        {% elif conta.status == 'Pendente' %}
                            bg-blue-100 text-blue-800
                        {% elif conta.status == 'Atrasado' %}
                            bg-red-100 text-red-800
                        {% endif %}
                        ">
                            {{ conta.status }}
                        </span>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Paginação -->
    <div class="flex justify-between items-center mt-4">
        <div class="text-sm text-label-secondary">
            Mostrando 1-20 de {{ contas|length }} resultados
        </div>
        <div class="flex space-x-1">
            <button class="px-3 py-1 rounded-md bg-systemGray-ultralight text-label-secondary text-sm">Anterior</button>
            <button class="px-3 py-1 rounded-md bg-systemBlue text-white text-sm">1</button>
            <button class="px-3 py-1 rounded-md bg-systemGray-ultralight text-label-secondary text-sm">2</button>
            <button class="px-3 py-1 rounded-md bg-systemGray-ultralight text-label-secondary text-sm">3</button>
            <button class="px-3 py-1 rounded-md bg-systemGray-ultralight text-label-secondary text-sm">Próximo</button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Calcular contas por status
        const statusData = {
            'Pago': 0,
            'Pendente': 0,
            'Atrasado': 0
        };

        const statusValores = {
            'Pago': 0,
            'Pendente': 0,
            'Atrasado': 0
        };

        {% for conta in contas %}
        statusData['{{ conta.status }}'] += 1;
        statusValores['{{ conta.status }}'] += {{ conta.valor }};
        {% endfor %}

        // Calcular contas por forma de pagamento
        const formaData = {};

        {% for conta in contas %}
        if ('{{ conta.forma_pagamento }}' in formaData) {
            formaData['{{ conta.forma_pagamento }}'] += {{ conta.valor }};
        } else {
            formaData['{{ conta.forma_pagamento }}'] = {{ conta.valor }};
        }
        {% endfor %}

        const formaLabels = Object.keys(formaData);
        const formaValues = Object.values(formaData);

        // Dados para tendência de recebimentos
        const meses = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set'];
        {% set total = contas|sum(attribute='valor') %}
        const recebimentosHistoricos = [
            {{ total * 0.75 }},
            {{ total * 0.78 }},
            {{ total * 0.82 }},
            {{ total * 0.85 }},
            {{ total * 0.88 }},
            {{ total * 0.92 }},
            {{ total * 0.95 }},
            {{ total * 0.98 }},
            {{ total }}
        ];

        // Previsão para os próximos meses
        const mesesFuturos = ['Out', 'Nov', 'Dez'];
        const recebimentosPreditos = [
            {{ total * 1.04 }},
            {{ total * 1.08 }},
            {{ total * 1.12 }}
        ];

        // Dados para análise de aging
        const faixasAging = ['0-30 dias', '31-60 dias', '61-90 dias', '91-120 dias', '>120 dias'];
        const valoresAging = [{{ total * 0.85 }}, {{ total * 0.08 }}, {{ total * 0.04 }}, {{ total * 0.02 }}, {{ total * 0.01 }}];
        const quantidadeAging = [{{ contas|length * 0.85 | round | int }}, {{ contas|length * 0.08 | round | int }}, {{ contas|length * 0.04 | round | int }}, {{ contas|length * 0.02 | round | int }}, {{ contas|length * 0.01 | round | int }}];

        // Configurar gráfico de contas por status
        const statusChart = new Chart(
            document.getElementById('statusChart'),
            {
                type: 'pie',
                data: {
                    labels: Object.keys(statusValores),
                    datasets: [{
                        data: Object.values(statusValores),
                        backgroundColor: [
                            'rgba(0, 122, 255, 0.7)',
                            'rgba(142, 142, 147, 0.7)',
                            'rgba(174, 174, 178, 0.7)'
                        ],
                        borderColor: [
                            'rgba(0, 122, 255, 1)',
                            'rgba(142, 142, 147, 1)',
                            'rgba(174, 174, 178, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = context.chart.data.datasets[0].data.reduce((a, b) => a + b, 0);
                                    const percentage = Math.round((value / total) * 100);
                                    return `${label}: R$ ${value.toLocaleString('pt-BR', {minimumFractionDigits: 2, maximumFractionDigits: 2})} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            }
        );

        // Configurar gráfico de contas por forma de pagamento
        const formaPagamentoChart = new Chart(
            document.getElementById('formaPagamentoChart'),
            {
                type: 'bar',
                data: {
                    labels: formaLabels,
                    datasets: [{
                        label: 'Valor (R$)',
                        data: formaValues,
                        backgroundColor: 'rgba(0, 122, 255, 0.7)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.dataset.label || '';
                                    const value = context.raw || 0;
                                    return `${label}: R$ ${value.toLocaleString('pt-BR', {minimumFractionDigits: 2, maximumFractionDigits: 2})}`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            }
        );

        // Configurar gráfico de tendência de recebimentos
        const tendenciaChart = new Chart(
            document.getElementById('tendenciaRecebimentosChart'),
            {
                type: 'line',
                data: {
                    labels: [...meses, ...mesesFuturos],
                    datasets: [
                        {
                            label: 'Histórico',
                            data: [...recebimentosHistoricos, null, null, null],
                            backgroundColor: 'rgba(0, 122, 255, 0.1)',
                            borderColor: 'rgba(0, 122, 255, 1)',
                            borderWidth: 2,
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: 'Previsão',
                            data: [null, null, null, null, null, null, null, null, recebimentosHistoricos[8], ...recebimentosPreditos],
                            backgroundColor: 'rgba(0, 122, 255, 0.05)',
                            borderColor: 'rgba(0, 122, 255, 0.7)',
                            borderWidth: 2,
                            borderDash: [5, 5],
                            tension: 0.4,
                            fill: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.dataset.label || '';
                                    const value = context.raw || 0;
                                    return value ? `${label}: R$ ${value.toLocaleString('pt-BR', {minimumFractionDigits: 2, maximumFractionDigits: 2})}` : '';
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            title: {
                                display: true,
                                text: 'Valor Total (R$)'
                            },
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            }
        );

        // Configurar gráfico de análise de aging
        const agingChart = new Chart(
            document.getElementById('agingChart'),
            {
                type: 'bar',
                data: {
                    labels: faixasAging,
                    datasets: [
                        {
                            label: 'Valor (R$)',
                            data: valoresAging,
                            backgroundColor: 'rgba(0, 122, 255, 0.7)',
                            borderColor: 'rgba(0, 122, 255, 1)',
                            borderWidth: 1,
                            yAxisID: 'y'
                        },
                        {
                            label: 'Quantidade',
                            data: quantidadeAging,
                            backgroundColor: 'rgba(142, 142, 147, 0.7)',
                            borderColor: 'rgba(142, 142, 147, 1)',
                            borderWidth: 1,
                            type: 'line',
                            yAxisID: 'y1'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.dataset.label || '';
                                    const value = context.raw || 0;
                                    if (label === 'Valor (R$)') {
                                        return `${label}: R$ ${value.toLocaleString('pt-BR', {minimumFractionDigits: 2, maximumFractionDigits: 2})}`;
                                    } else {
                                        return `${label}: ${value} contas`;
                                    }
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: 'Valor (R$)'
                            },
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'Quantidade'
                            },
                            beginAtZero: true,
                            grid: {
                                display: false
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            }
        );

        // Event listeners para os seletores de visualização
        document.getElementById('visualizacaoStatus').addEventListener('change', function() {
            // Alterar o tipo de gráfico
            statusChart.destroy(); // Destruir o gráfico atual

            // Criar novo gráfico com o tipo selecionado
            const newType = this.value;
            const newOptions = {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.chart.data.datasets[0].data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: R$ ${value.toLocaleString('pt-BR', {minimumFractionDigits: 2, maximumFractionDigits: 2})} (${percentage}%)`;
                            }
                        }
                    }
                }
            };

            // Ajustar opções específicas para cada tipo de gráfico
            if (newType === 'pie' || newType === 'doughnut') {
                newOptions.plugins.legend = {
                    position: 'right'
                };
            } else if (newType === 'bar') {
                newOptions.plugins.legend = {
                    display: false
                };
                newOptions.scales = {
                    y: {
                        beginAtZero: true,
                        grid: {
                            display: true,
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                };
            }

            // Criar novo gráfico
            const ctx = document.getElementById('statusChart').getContext('2d');
            window.statusChart = new Chart(ctx, {
                type: newType,
                data: {
                    labels: Object.keys(statusValores),
                    datasets: [{
                        label: 'Valor (R$)',
                        data: Object.values(statusValores),
                        backgroundColor: [
                            'rgba(0, 122, 255, 0.7)',
                            'rgba(142, 142, 147, 0.7)',
                            'rgba(174, 174, 178, 0.7)'
                        ],
                        borderColor: [
                            'rgba(0, 122, 255, 1)',
                            'rgba(142, 142, 147, 1)',
                            'rgba(174, 174, 178, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: newOptions
            });
        });

        document.getElementById('visualizacaoFormaPagamento').addEventListener('change', function() {
            // Alterar o tipo de gráfico
            formaPagamentoChart.destroy(); // Destruir o gráfico atual

            // Criar novo gráfico com o tipo selecionado
            const newType = this.value;
            const newOptions = {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.dataset.label || '';
                                const value = context.raw || 0;
                                return `${label}: R$ ${value.toLocaleString('pt-BR', {minimumFractionDigits: 2, maximumFractionDigits: 2})}`;
                            }
                        }
                    }
                }
            };

            // Ajustar opções específicas para cada tipo de gráfico
            if (newType === 'bar' || newType === 'horizontalBar') {
                newOptions.indexAxis = newType === 'horizontalBar' ? 'y' : 'x';
                newOptions.scales = {
                    x: {
                        beginAtZero: true,
                        grid: {
                            display: newType === 'horizontalBar',
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            display: newType === 'bar',
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    }
                };
            } else if (newType === 'polarArea') {
                newOptions.scales = undefined;
                newOptions.plugins.legend = {
                    position: 'right'
                };
            }

            // Criar novo gráfico
            const ctx = document.getElementById('formaPagamentoChart').getContext('2d');
            window.formaPagamentoChart = new Chart(ctx, {
                type: newType === 'horizontalBar' ? 'bar' : newType, // 'horizontalBar' é tratado com indexAxis
                data: {
                    labels: formaLabels,
                    datasets: [{
                        label: 'Valor (R$)',
                        data: formaValues,
                        backgroundColor: [
                            'rgba(0, 122, 255, 0.7)',
                            'rgba(142, 142, 147, 0.7)',
                            'rgba(174, 174, 178, 0.7)',
                            'rgba(199, 199, 204, 0.7)',
                            'rgba(209, 209, 214, 0.7)'
                        ],
                        borderColor: [
                            'rgba(0, 122, 255, 1)',
                            'rgba(142, 142, 147, 1)',
                            'rgba(174, 174, 178, 1)',
                            'rgba(199, 199, 204, 1)',
                            'rgba(209, 209, 214, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: newOptions
            });
        });

        // Adicionar funcionalidade aos botões de filtro
        document.querySelector('button.bg-systemBlue').addEventListener('click', function() {
            // Simular filtragem de dados
            alert('Filtros aplicados! Em um ambiente real, os dados seriam filtrados de acordo com os critérios selecionados.');
        });

        // Adicionar funcionalidade aos botões de exportação e impressão
        document.querySelectorAll('.bg-systemGray-ultralight').forEach(button => {
            button.addEventListener('click', function() {
                if (this.textContent.trim() === 'Exportar') {
                    alert('Exportando dados... Em um ambiente real, os dados seriam exportados para Excel ou CSV.');
                } else if (this.textContent.trim() === 'Imprimir') {
                    alert('Preparando impressão... Em um ambiente real, seria aberta a janela de impressão.');
                }
            });
        });
    });
</script>
{% endblock %}

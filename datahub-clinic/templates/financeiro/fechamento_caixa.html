{% extends 'base.html' %}

{% block title %}Fechamento de Caixa - Amigo DataHub{% endblock %}

{% block header %}Fechamento de Caixa{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="bg-gradient-to-r from-blue-50 to-white rounded-view p-8 border border-gray-200 mb-6 overflow-hidden relative">
    <!-- Background Elements -->
    <div class="absolute top-0 right-0 w-full h-full opacity-10 overflow-hidden">
        <svg class="absolute top-10 right-10 w-64 h-64 text-systemBlue" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z"></path>
            <path d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"></path>
        </svg>
        <svg class="absolute bottom-10 left-1/4 w-32 h-32 text-systemBlue" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M10.464 8.746c.227-.18.497-.311.786-.394v2.795a2.252 2.252 0 01-.786-.393c-.394-.313-.546-.681-.546-1.004 0-.323.152-.691.546-1.004zM12.75 15.662v-2.824c.347.085.664.228.921.421.427.32.579.686.579.991 0 .305-.152.671-.579.991a2.534 2.534 0 01-.921.42z"></path>
            <path d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25zM12.75 6a.75.75 0 00-1.5 0v.816a3.836 3.836 0 00-1.72.756c-.712.566-1.112 1.35-1.112 2.178 0 .829.4 1.612 1.113 2.178.502.4 1.102.647 1.719.756v2.978a2.536 2.536 0 01-.921-.421l-.879-.66a.75.75 0 00-.9 1.2l.879.66c.533.4 1.169.645 1.821.75V18a.75.75 0 001.5 0v-.81a4.124 4.124 0 001.821-.749c.745-.559 1.179-1.344 1.179-2.191 0-.847-.434-1.632-1.179-2.191a4.122 4.122 0 00-1.821-.75V8.354c.29.082.559.213.786.393l.415.33a.75.75 0 00.933-1.175l-.415-.33a3.836 3.836 0 00-1.719-.755V6z"></path>
        </svg>
    </div>

    <div class="flex flex-col md:flex-row items-center relative z-10">
        <div class="md:w-2/3 mb-6 md:mb-0 md:pr-8">
            <div class="inline-block bg-systemBlue text-white text-xs font-semibold px-3 py-1 rounded-full mb-3">FINANCEIRO</div>
            <h1 class="text-3xl font-bold text-gray-800 mb-4">Fechamento de Caixa</h1>
            <p class="text-gray-600 mb-6">Controle e analise os fechamentos diários da sua clínica. Visualize a distribuição por forma de pagamento, identifique tendências e garanta a precisão das suas operações financeiras.</p>

            {% set total = fechamentos|sum(attribute='total') %}
            {% set dinheiro = fechamentos|sum(attribute='dinheiro') %}
            {% set cartao = fechamentos|sum(attribute='cartao_credito') + fechamentos|sum(attribute='cartao_debito') %}
            {% set pix = fechamentos|sum(attribute='pix') %}

            <div class="grid grid-cols-3 gap-4 mb-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">
                        R$ {{ '{:,.0f}'.format(total).replace(',', '.') if total < 1000 else '{:.1f}K'.format(total/1000) if total < 1000000 else '{:.1f}M'.format(total/1000000) }}
                    </div>
                    <div class="text-xs text-gray-500">Total fechado</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">
                        {{ "%.0f"|format(fechamentos|length) }}
                    </div>
                    <div class="text-xs text-gray-500">Fechamentos realizados</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">
                        {{ "%.1f"|format(total / fechamentos|length if fechamentos|length > 0 else 0) }}
                    </div>
                    <div class="text-xs text-gray-500">Média por fechamento</div>
                </div>
            </div>
            <div class="flex flex-wrap gap-3">
                <button class="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Novo Fechamento
                </button>
                <button class="bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-md text-sm border border-gray-200 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                    </svg>
                    Exportar Relatório
                </button>
            </div>
        </div>
        <div class="md:w-1/3 flex justify-center">
            <div class="w-64 h-64 bg-white rounded-full shadow-lg flex items-center justify-center p-4 relative">
                <div class="absolute inset-0 rounded-full border-8 border-blue-100"></div>
                <div class="absolute inset-0 rounded-full border-8 border-systemBlue border-opacity-70" style="clip-path: polygon(50% 50%, 100% 0, 100% 50%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-300 border-opacity-70" style="clip-path: polygon(50% 50%, 100% 50%, 100% 100%, 50% 100%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-200 border-opacity-70" style="clip-path: polygon(50% 50%, 50% 100%, 0 100%, 0 50%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-400 border-opacity-70" style="clip-path: polygon(50% 50%, 0 50%, 0 0, 50% 0);"></div>
                <div class="z-10 text-center">
                    <div class="text-3xl font-bold text-gray-800">{{ "%.0f"|format(pix / total * 100 if total > 0 else 0) }}%</div>
                    <div class="text-sm text-gray-500">Pagamentos via Pix</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Insights com IA -->
<div class="mb-5">
    <div class="flex items-center justify-between mb-4">
        <div class="flex items-center">
            <svg class="w-5 h-5 text-systemBlue mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
            </svg>
            <h2 class="text-base font-semibold text-gray-800">Amigo Intelligence</h2>
        </div>
        <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">Atualizado agora</span>
    </div>

    <div id="insights-container" class="grid grid-cols-1 md:grid-cols-3 gap-3">
        {% for insight in insights %}
            {% with
                title=insight.title,
                description=insight.description,
                insight_type=insight.insight_type,
                content=insight.content,
                category=insight.category
            %}
                {% include 'components/ai_insight_widget.html' %}
            {% endwith %}
        {% endfor %}
    </div>
</div>

<script>
    // Carregar insights dinamicamente após 5 segundos (para demonstração)
    setTimeout(() => {
        // Contexto específico da página de fechamento de caixa
        const pageContext = {
            page_title: "Fechamento de Caixa",
            page_description: "Análise e gestão dos fechamentos diários da clínica, com foco em controle financeiro e distribuição por forma de pagamento",
            key_metrics: {
                "Total Fechado": "R$ {{ '{:,.2f}'.format(total).replace(',', '.') }}",
                "Fechamentos Realizados": "{{ fechamentos|length }}",
                "Média por Fechamento": "R$ {{ '{:,.2f}'.format(total / fechamentos|length if fechamentos|length > 0 else 0).replace(',', '.') }}",
                "Pagamentos via Pix": "{{ pix / total * 100|round|int if total > 0 else 0 }}%"
            },
            analysis_focus: "Controle financeiro e distribuição por forma de pagamento",
            page_elements: [
                "Fechamento por Forma de Pagamento",
                "Evolução do Fechamento",
                "Detalhamento dos Fechamentos"
            ]
        };

        loadInsights('financeiro', 'fechamento_caixa', 'insights-container', 3, ['list', 'text', 'stat'], pageContext);
    }, 5000);
</script>

<!-- Insights Estáticos (Temporários) -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6 hidden">
    <!-- Insight de Tendências -->
    {% with
        title="Tendências de Pagamento",
        description="Análise de comportamento",
        insight_type="list",
        content=[
            "Pagamentos via <strong>Pix</strong> aumentaram <strong>35%</strong> nos últimos 3 meses",
            "Pagamentos em <strong>dinheiro</strong> representam apenas <strong>{{ dinheiro / total * 100|round|int if total > 0 else 0 }}%</strong> do total",
            "Cartões de crédito são utilizados em <strong>{{ fechamentos|sum(attribute='cartao_credito') / total * 100|round|int if total > 0 else 0 }}%</strong> das transações"
        ],
        category="Análise",
        action_text="Ver detalhes"
    %}
        {% include 'components/ai_insight_widget.html' %}
    {% endwith %}

    <!-- Insight de Comparação -->
    {% with
        title="Comparativo Mensal",
        description="Evolução dos fechamentos",
        insight_type="text",
        content="O valor médio dos fechamentos aumentou <strong>12%</strong> em relação ao mês anterior. Os dias com maior movimento são <strong>segundas e quintas-feiras</strong>, representando <strong>42%</strong> do faturamento total. Houve redução de <strong>8%</strong> nos pagamentos em cheque em comparação ao mesmo período do ano anterior.",
        category="Comparativo",
        action_text="Ver análise completa"
    %}
        {% include 'components/ai_insight_widget.html' %}
    {% endwith %}

    <!-- Insight de Recomendações -->
    {% with
        title="Recomendações Operacionais",
        description="Otimização de processos",
        insight_type="list",
        content=[
            "Implementar <strong>conferência dupla</strong> para fechamentos acima de R$ {{ '{:,.0f}'.format(total / fechamentos|length * 1.5).replace(',', '.') if fechamentos|length > 0 else '0' }}",
            "Considerar <strong>redução de taxas</strong> para pagamentos via Pix para aumentar adoção",
            "Realizar <strong>fechamentos parciais</strong> em dias de alto movimento para maior controle"
        ],
        category="Operações",
        action_text="Implementar ações"
    %}
        {% include 'components/ai_insight_widget.html' %}
    {% endwith %}
</div>

<!-- Resumo -->
<div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-6">
    <!-- Total -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-label-DEFAULT">Total</p>
        </div>
        <p class="text-3xl font-semibold text-label-DEFAULT mb-1">
            R$ {{ '{:,.2f}'.format(fechamentos|sum(attribute='total')).replace(',', '.') if fechamentos|sum(attribute='total') < 1000 else '{:.1f}K'.format(fechamentos|sum(attribute='total')/1000) if fechamentos|sum(attribute='total') < 1000000 else '{:.1f}M'.format(fechamentos|sum(attribute='total')/1000000) }}
        </p>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Valor total dos fechamentos.</p>
    </div>

    <!-- Dinheiro -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-label-DEFAULT">Dinheiro</p>
        </div>
        <p class="text-3xl font-semibold text-label-DEFAULT mb-1">
            R$ {{ '{:,.2f}'.format(fechamentos|sum(attribute='dinheiro')).replace(',', '.') if fechamentos|sum(attribute='dinheiro') < 1000 else '{:.1f}K'.format(fechamentos|sum(attribute='dinheiro')/1000) if fechamentos|sum(attribute='dinheiro') < 1000000 else '{:.1f}M'.format(fechamentos|sum(attribute='dinheiro')/1000000) }}
        </p>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Total em dinheiro.</p>
    </div>

    <!-- Cartão -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-label-DEFAULT">Cartão</p>
        </div>
        <p class="text-3xl font-semibold text-label-DEFAULT mb-1">
            R$ {{ '{:,.2f}'.format(fechamentos|sum(attribute='cartao_credito') + fechamentos|sum(attribute='cartao_debito')).replace(',', '.') if (fechamentos|sum(attribute='cartao_credito') + fechamentos|sum(attribute='cartao_debito')) < 1000 else '{:.1f}K'.format((fechamentos|sum(attribute='cartao_credito') + fechamentos|sum(attribute='cartao_debito'))/1000) if (fechamentos|sum(attribute='cartao_credito') + fechamentos|sum(attribute='cartao_debito')) < 1000000 else '{:.1f}M'.format((fechamentos|sum(attribute='cartao_credito') + fechamentos|sum(attribute='cartao_debito'))/1000000) }}
        </p>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Total em cartão (crédito e débito).</p>
    </div>

    <!-- Pix -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-label-DEFAULT">Pix</p>
        </div>
        <p class="text-3xl font-semibold text-label-DEFAULT mb-1">
            R$ {{ '{:,.2f}'.format(fechamentos|sum(attribute='pix')).replace(',', '.') if fechamentos|sum(attribute='pix') < 1000 else '{:.1f}K'.format(fechamentos|sum(attribute='pix')/1000) if fechamentos|sum(attribute='pix') < 1000000 else '{:.1f}M'.format(fechamentos|sum(attribute='pix')/1000000) }}
        </p>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Total em Pix.</p>
    </div>

    <!-- Cheque -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-label-DEFAULT">Cheque</p>
        </div>
        <p class="text-3xl font-semibold text-label-DEFAULT mb-1">
            R$ {{ '{:,.2f}'.format(fechamentos|sum(attribute='cheque')).replace(',', '.') if fechamentos|sum(attribute='cheque') < 1000 else '{:.1f}K'.format(fechamentos|sum(attribute='cheque')/1000) if fechamentos|sum(attribute='cheque') < 1000000 else '{:.1f}M'.format(fechamentos|sum(attribute='cheque')/1000000) }}
        </p>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Total em cheque.</p>
    </div>
</div>

<!-- Filtros -->
<div class="bg-white rounded-view p-4 mb-6 border border-gray-200">
    <div class="flex flex-wrap gap-4">
        <div class="w-full md:w-auto">
            <label for="data_inicio" class="block text-xs font-medium text-label-secondary mb-1">Data Início</label>
            <input type="date" id="data_inicio" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
        </div>
        <div class="w-full md:w-auto">
            <label for="data_fim" class="block text-xs font-medium text-label-secondary mb-1">Data Fim</label>
            <input type="date" id="data_fim" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
        </div>
        <div class="w-full md:w-auto flex items-end">
            <button class="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-control transition duration-150 ease-in-out text-sm">
                Filtrar
            </button>
        </div>
    </div>
</div>

<!-- Gráficos -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Fechamento por Forma de Pagamento -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <h2 class="text-base font-semibold text-label-DEFAULT mb-4">Fechamento por Forma de Pagamento</h2>
        <div class="h-64">
            <canvas id="formaPagamentoChart"></canvas>
        </div>
    </div>

    <!-- Evolução do Fechamento -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <h2 class="text-base font-semibold text-label-DEFAULT mb-4">Evolução do Fechamento</h2>
        <div class="h-64">
            <canvas id="evolucaoChart"></canvas>
        </div>
    </div>
</div>

<!-- Tabela de Fechamentos -->
<div class="bg-white rounded-view p-6 border border-gray-200">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-base font-semibold text-label-DEFAULT">Detalhamento dos Fechamentos</h2>
        <div class="flex space-x-2">
            <button class="bg-systemGray-ultralight hover:bg-systemGray-extralight text-gray-800 px-3 py-1 rounded-full transition duration-150 ease-in-out text-xs border border-systemGray-lightest">
                Exportar
            </button>
            <button class="bg-systemGray-ultralight hover:bg-systemGray-extralight text-gray-800 px-3 py-1 rounded-full transition duration-150 ease-in-out text-xs border border-systemGray-lightest">
                Imprimir
            </button>
        </div>
    </div>

    <div class="overflow-x-auto">
        <table class="w-full text-sm data-table">
            <thead>
                <tr>
                    <th>Data</th>
                    <th class="text-right">Dinheiro</th>
                    <th class="text-right">Cartão Crédito</th>
                    <th class="text-right">Cartão Débito</th>
                    <th class="text-right">Pix</th>
                    <th class="text-right">Cheque</th>
                    <th class="text-right">Total</th>
                    <th>Ações</th>
                </tr>
            </thead>
            <tbody>
                {% for fechamento in fechamentos %}
                <tr>
                    <td>{{ fechamento.data }}</td>
                    <td class="text-right">R$ {{ "%.2f"|format(fechamento.dinheiro) }}</td>
                    <td class="text-right">R$ {{ "%.2f"|format(fechamento.cartao_credito) }}</td>
                    <td class="text-right">R$ {{ "%.2f"|format(fechamento.cartao_debito) }}</td>
                    <td class="text-right">R$ {{ "%.2f"|format(fechamento.pix) }}</td>
                    <td class="text-right">R$ {{ "%.2f"|format(fechamento.cheque) }}</td>
                    <td class="text-right font-semibold">R$ {{ "%.2f"|format(fechamento.total) }}</td>
                    <td>
                        <div class="flex space-x-1">
                            <button class="p-1 text-systemBlue hover:bg-systemGray-ultralight rounded view-details-btn"
                                    data-date="{{ fechamento.data }}"
                                    data-dinheiro="{{ fechamento.dinheiro }}"
                                    data-cartao-credito="{{ fechamento.cartao_credito }}"
                                    data-cartao-debito="{{ fechamento.cartao_debito }}"
                                    data-pix="{{ fechamento.pix }}"
                                    data-cheque="{{ fechamento.cheque }}"
                                    data-total="{{ fechamento.total }}">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                            </button>
                            <button class="p-1 text-systemBlue hover:bg-systemGray-ultralight rounded edit-btn"
                                    data-date="{{ fechamento.data }}">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                            </button>
                            <button class="p-1 text-systemBlue hover:bg-systemGray-ultralight rounded print-btn"
                                    data-date="{{ fechamento.data }}">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                                </svg>
                            </button>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
            <tfoot class="bg-gray-50">
                <tr>
                    <td class="font-semibold">Total</td>
                    <td class="text-right font-semibold">R$ {{ "%.2f"|format(fechamentos|sum(attribute='dinheiro')) }}</td>
                    <td class="text-right font-semibold">R$ {{ "%.2f"|format(fechamentos|sum(attribute='cartao_credito')) }}</td>
                    <td class="text-right font-semibold">R$ {{ "%.2f"|format(fechamentos|sum(attribute='cartao_debito')) }}</td>
                    <td class="text-right font-semibold">R$ {{ "%.2f"|format(fechamentos|sum(attribute='pix')) }}</td>
                    <td class="text-right font-semibold">R$ {{ "%.2f"|format(fechamentos|sum(attribute='cheque')) }}</td>
                    <td class="text-right font-semibold">R$ {{ "%.2f"|format(fechamentos|sum(attribute='total')) }}</td>
                    <td></td>
                </tr>
            </tfoot>
        </table>
    </div>

    <!-- Paginação -->
    <div class="flex justify-between items-center mt-4">
        <div class="text-sm text-label-secondary">
            Mostrando 1-20 de {{ fechamentos|length }} resultados
        </div>
        <div class="flex space-x-1">
            <button class="px-3 py-1 rounded-md bg-systemGray-ultralight text-label-secondary text-sm">Anterior</button>
            <button class="px-3 py-1 rounded-md bg-systemBlue text-white text-sm">1</button>
            <button class="px-3 py-1 rounded-md bg-systemGray-ultralight text-label-secondary text-sm">2</button>
            <button class="px-3 py-1 rounded-md bg-systemGray-ultralight text-label-secondary text-sm">Próximo</button>
        </div>
    </div>
</div>
    <!-- Modal de Detalhes do Fechamento -->
    <div id="detailsModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
        <div class="bg-white rounded-xl shadow-xl max-w-3xl w-full max-h-[90vh] overflow-y-auto">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h3 class="text-xl font-bold text-gray-800">Detalhes do Fechamento</h3>
                    <button id="closeDetailsModal" class="text-gray-500 hover:text-gray-700">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
            <div class="p-6">
                <div class="mb-6">
                    <div class="flex justify-between items-center mb-4">
                        <h4 class="text-lg font-semibold text-gray-800">Informações Gerais</h4>
                        <span id="modalDate" class="text-sm font-medium bg-blue-100 text-blue-800 py-1 px-3 rounded-full"></span>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h5 class="text-sm font-medium text-gray-600 mb-3">Resumo por Forma de Pagamento</h5>
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">Dinheiro:</span>
                                    <span id="modalDinheiro" class="text-sm font-medium text-gray-800"></span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">Cartão de Crédito:</span>
                                    <span id="modalCartaoCredito" class="text-sm font-medium text-gray-800"></span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">Cartão de Débito:</span>
                                    <span id="modalCartaoDebito" class="text-sm font-medium text-gray-800"></span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">Pix:</span>
                                    <span id="modalPix" class="text-sm font-medium text-gray-800"></span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">Cheque:</span>
                                    <span id="modalCheque" class="text-sm font-medium text-gray-800"></span>
                                </div>
                                <div class="flex justify-between pt-2 border-t border-gray-200">
                                    <span class="text-sm font-medium text-gray-700">Total:</span>
                                    <span id="modalTotal" class="text-sm font-bold text-gray-800"></span>
                                </div>
                            </div>
                        </div>

                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h5 class="text-sm font-medium text-gray-600 mb-3">Responsáveis</h5>
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">Operador:</span>
                                    <span class="text-sm font-medium text-gray-800">Maria Silva</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">Supervisor:</span>
                                    <span class="text-sm font-medium text-gray-800">João Santos</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">Horário de Abertura:</span>
                                    <span class="text-sm font-medium text-gray-800">08:00</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">Horário de Fechamento:</span>
                                    <span class="text-sm font-medium text-gray-800">18:00</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">Status:</span>
                                    <span class="text-sm font-medium text-green-600">Fechado</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-6">
                        <h4 class="text-lg font-semibold text-gray-800 mb-4">Transações do Dia</h4>
                        <div class="overflow-x-auto">
                            <table class="w-full text-sm">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="text-left py-2 px-3">Horário</th>
                                        <th class="text-left py-2 px-3">Paciente</th>
                                        <th class="text-left py-2 px-3">Procedimento</th>
                                        <th class="text-left py-2 px-3">Forma Pagto</th>
                                        <th class="text-right py-2 px-3">Valor</th>
                                        <th class="text-center py-2 px-3">Ações</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr class="border-b border-gray-100">
                                        <td class="py-2 px-3">09:15</td>
                                        <td class="py-2 px-3">Ana Oliveira</td>
                                        <td class="py-2 px-3">Consulta Odontológica</td>
                                        <td class="py-2 px-3">Cartão de Crédito</td>
                                        <td class="py-2 px-3 text-right">R$ 150,00</td>
                                        <td class="py-2 px-3 text-center">
                                            <a href="#" class="text-blue-600 hover:text-blue-800 text-xs">Ver detalhes</a>
                                        </td>
                                    </tr>
                                    <tr class="border-b border-gray-100">
                                        <td class="py-2 px-3">10:30</td>
                                        <td class="py-2 px-3">Carlos Mendes</td>
                                        <td class="py-2 px-3">Limpeza</td>
                                        <td class="py-2 px-3">Dinheiro</td>
                                        <td class="py-2 px-3 text-right">R$ 120,00</td>
                                        <td class="py-2 px-3 text-center">
                                            <a href="#" class="text-blue-600 hover:text-blue-800 text-xs">Ver detalhes</a>
                                        </td>
                                    </tr>
                                    <tr class="border-b border-gray-100">
                                        <td class="py-2 px-3">14:45</td>
                                        <td class="py-2 px-3">Mariana Costa</td>
                                        <td class="py-2 px-3">Restauração</td>
                                        <td class="py-2 px-3">Pix</td>
                                        <td class="py-2 px-3 text-right">R$ 200,00</td>
                                        <td class="py-2 px-3 text-center">
                                            <a href="#" class="text-blue-600 hover:text-blue-800 text-xs">Ver detalhes</a>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="p-4 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3">
                <button id="viewPatientBtn" class="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50">
                    Ver Paciente
                </button>
                <button id="viewAppointmentBtn" class="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50">
                    Ver Agendamento
                </button>
                <button id="printDetailsBtn" class="px-4 py-2 bg-systemBlue text-white rounded-lg text-sm font-medium hover:bg-blue-600">
                    Imprimir
                </button>
            </div>
        </div>
    </div>

    <!-- Modal de Edição -->
    <div id="editModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
        <div class="bg-white rounded-xl shadow-xl max-w-lg w-full">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h3 class="text-xl font-bold text-gray-800">Editar Fechamento</h3>
                    <button id="closeEditModal" class="text-gray-500 hover:text-gray-700">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
            <div class="p-6">
                <form id="editFechamentoForm">
                    <div class="mb-4">
                        <label for="editDate" class="block text-sm font-medium text-gray-700 mb-1">Data</label>
                        <input type="date" id="editDate" class="w-full px-3 py-2 border border-gray-300 rounded-md" readonly>
                    </div>
                    <div class="mb-4">
                        <label for="editDinheiro" class="block text-sm font-medium text-gray-700 mb-1">Dinheiro (R$)</label>
                        <input type="number" id="editDinheiro" class="w-full px-3 py-2 border border-gray-300 rounded-md" step="0.01">
                    </div>
                    <div class="mb-4">
                        <label for="editCartaoCredito" class="block text-sm font-medium text-gray-700 mb-1">Cartão de Crédito (R$)</label>
                        <input type="number" id="editCartaoCredito" class="w-full px-3 py-2 border border-gray-300 rounded-md" step="0.01">
                    </div>
                    <div class="mb-4">
                        <label for="editCartaoDebito" class="block text-sm font-medium text-gray-700 mb-1">Cartão de Débito (R$)</label>
                        <input type="number" id="editCartaoDebito" class="w-full px-3 py-2 border border-gray-300 rounded-md" step="0.01">
                    </div>
                    <div class="mb-4">
                        <label for="editPix" class="block text-sm font-medium text-gray-700 mb-1">Pix (R$)</label>
                        <input type="number" id="editPix" class="w-full px-3 py-2 border border-gray-300 rounded-md" step="0.01">
                    </div>
                    <div class="mb-4">
                        <label for="editCheque" class="block text-sm font-medium text-gray-700 mb-1">Cheque (R$)</label>
                        <input type="number" id="editCheque" class="w-full px-3 py-2 border border-gray-300 rounded-md" step="0.01">
                    </div>
                </form>
            </div>
            <div class="p-4 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3">
                <button id="cancelEditBtn" class="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50">
                    Cancelar
                </button>
                <button id="saveEditBtn" class="px-4 py-2 bg-systemBlue text-white rounded-lg text-sm font-medium hover:bg-blue-600">
                    Salvar
                </button>
            </div>
        </div>
    </div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Dados para o gráfico de forma de pagamento
        const formaPagamentoData = {
            labels: ['Dinheiro', 'Cartão Crédito', 'Cartão Débito', 'Pix', 'Cheque'],
            datasets: [{
                data: [
                    {{ fechamentos|sum(attribute='dinheiro') }},
                    {{ fechamentos|sum(attribute='cartao_credito') }},
                    {{ fechamentos|sum(attribute='cartao_debito') }},
                    {{ fechamentos|sum(attribute='pix') }},
                    {{ fechamentos|sum(attribute='cheque') }}
                ],
                backgroundColor: [
                    'rgba(0, 122, 255, 0.7)',
                    'rgba(142, 142, 147, 0.7)',
                    'rgba(174, 174, 178, 0.7)',
                    'rgba(199, 199, 204, 0.7)',
                    'rgba(209, 209, 214, 0.7)'
                ],
                borderColor: [
                    'rgba(0, 122, 255, 1)',
                    'rgba(142, 142, 147, 1)',
                    'rgba(174, 174, 178, 1)',
                    'rgba(199, 199, 204, 1)',
                    'rgba(209, 209, 214, 1)'
                ],
                borderWidth: 1
            }]
        };

        // Dados para o gráfico de evolução
        const datas = [];
        const valores = [];

        {% for fechamento in fechamentos %}
        datas.push('{{ fechamento.data }}');
        valores.push({{ fechamento.total }});
        {% endfor %}

        // Configurar gráficos
        const formaPagamentoChart = new Chart(
            document.getElementById('formaPagamentoChart'),
            {
                type: 'pie',
                data: formaPagamentoData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    return `${label}: R$ ${value.toFixed(2)}`;
                                }
                            }
                        }
                    }
                }
            }
        );

        const evolucaoChart = new Chart(
            document.getElementById('evolucaoChart'),
            {
                type: 'line',
                data: {
                    labels: datas,
                    datasets: [{
                        label: 'Valor Total (R$)',
                        data: valores,
                        backgroundColor: 'rgba(0, 122, 255, 0.1)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            }
        );
        // Manipulação dos modais
        const detailsModal = document.getElementById('detailsModal');
        const editModal = document.getElementById('editModal');

        // Botões para abrir o modal de detalhes
        document.querySelectorAll('.view-details-btn').forEach(button => {
            button.addEventListener('click', function() {
                const date = this.getAttribute('data-date');
                const dinheiro = parseFloat(this.getAttribute('data-dinheiro'));
                const cartaoCredito = parseFloat(this.getAttribute('data-cartao-credito'));
                const cartaoDebito = parseFloat(this.getAttribute('data-cartao-debito'));
                const pix = parseFloat(this.getAttribute('data-pix'));
                const cheque = parseFloat(this.getAttribute('data-cheque'));
                const total = parseFloat(this.getAttribute('data-total'));

                // Preencher os dados no modal
                document.getElementById('modalDate').textContent = date;
                document.getElementById('modalDinheiro').textContent = `R$ ${dinheiro.toFixed(2)}`;
                document.getElementById('modalCartaoCredito').textContent = `R$ ${cartaoCredito.toFixed(2)}`;
                document.getElementById('modalCartaoDebito').textContent = `R$ ${cartaoDebito.toFixed(2)}`;
                document.getElementById('modalPix').textContent = `R$ ${pix.toFixed(2)}`;
                document.getElementById('modalCheque').textContent = `R$ ${cheque.toFixed(2)}`;
                document.getElementById('modalTotal').textContent = `R$ ${total.toFixed(2)}`;

                // Exibir o modal
                detailsModal.classList.remove('hidden');
            });
        });

        // Botões para abrir o modal de edição
        document.querySelectorAll('.edit-btn').forEach(button => {
            button.addEventListener('click', function() {
                const date = this.getAttribute('data-date');
                const row = this.closest('tr');
                const dinheiro = parseFloat(row.cells[1].textContent.replace('R$ ', '').replace(',', '.'));
                const cartaoCredito = parseFloat(row.cells[2].textContent.replace('R$ ', '').replace(',', '.'));
                const cartaoDebito = parseFloat(row.cells[3].textContent.replace('R$ ', '').replace(',', '.'));
                const pix = parseFloat(row.cells[4].textContent.replace('R$ ', '').replace(',', '.'));
                const cheque = parseFloat(row.cells[5].textContent.replace('R$ ', '').replace(',', '.'));

                // Converter data para formato de input date (YYYY-MM-DD)
                const dateParts = date.split('/');
                const formattedDate = `${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`;

                // Preencher os dados no formulário
                document.getElementById('editDate').value = formattedDate;
                document.getElementById('editDinheiro').value = dinheiro.toFixed(2);
                document.getElementById('editCartaoCredito').value = cartaoCredito.toFixed(2);
                document.getElementById('editCartaoDebito').value = cartaoDebito.toFixed(2);
                document.getElementById('editPix').value = pix.toFixed(2);
                document.getElementById('editCheque').value = cheque.toFixed(2);

                // Exibir o modal
                editModal.classList.remove('hidden');
            });
        });

        // Botões para imprimir
        document.querySelectorAll('.print-btn').forEach(button => {
            button.addEventListener('click', function() {
                const date = this.getAttribute('data-date');
                alert(`Imprimindo fechamento do dia ${date}`);
                // Aqui seria implementada a lógica de impressão
            });
        });

        // Fechar modal de detalhes
        document.getElementById('closeDetailsModal').addEventListener('click', function() {
            detailsModal.classList.add('hidden');
        });

        // Fechar modal de edição
        document.getElementById('closeEditModal').addEventListener('click', function() {
            editModal.classList.add('hidden');
        });

        // Cancelar edição
        document.getElementById('cancelEditBtn').addEventListener('click', function() {
            editModal.classList.add('hidden');
        });

        // Salvar edição
        document.getElementById('saveEditBtn').addEventListener('click', function() {
            const date = document.getElementById('editDate').value;
            const dinheiro = parseFloat(document.getElementById('editDinheiro').value);
            const cartaoCredito = parseFloat(document.getElementById('editCartaoCredito').value);
            const cartaoDebito = parseFloat(document.getElementById('editCartaoDebito').value);
            const pix = parseFloat(document.getElementById('editPix').value);
            const cheque = parseFloat(document.getElementById('editCheque').value);

            alert(`Fechamento do dia ${date} atualizado com sucesso!`);
            editModal.classList.add('hidden');

            // Aqui seria implementada a lógica para salvar os dados no servidor
        });

        // Botões de ação no modal de detalhes
        document.getElementById('viewPatientBtn').addEventListener('click', function() {
            alert('Redirecionando para a página do paciente...');
            // Aqui seria implementada a lógica de redirecionamento
        });

        document.getElementById('viewAppointmentBtn').addEventListener('click', function() {
            alert('Redirecionando para a página do agendamento...');
            // Aqui seria implementada a lógica de redirecionamento
        });

        document.getElementById('printDetailsBtn').addEventListener('click', function() {
            alert('Imprimindo detalhes do fechamento...');
            // Aqui seria implementada a lógica de impressão
        });

        // Fechar modais ao clicar fora deles
        window.addEventListener('click', function(event) {
            if (event.target === detailsModal) {
                detailsModal.classList.add('hidden');
            }
            if (event.target === editModal) {
                editModal.classList.add('hidden');
            }
        });
    });
</script>
{% endblock %}

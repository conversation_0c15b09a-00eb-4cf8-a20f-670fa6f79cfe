{% extends 'base.html' %}

{% block title %}Fluxo de Caixa - Amigo DataHub{% endblock %}

{% block header %}Fluxo de Caixa{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="bg-gradient-to-r from-blue-50 to-white rounded-view p-8 border border-gray-200 mb-6 overflow-hidden relative">
    <!-- Background Elements -->
    <div class="absolute top-0 right-0 w-full h-full opacity-10 overflow-hidden">
        <svg class="absolute top-10 right-10 w-64 h-64 text-systemBlue" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M2.25 18.75a60.07 60.07 0 0115.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 013 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 00-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 01-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 003 15h-.75M15 10.5a3 3 0 11-6 0 3 3 0 016 0zm3 0h.008v.008H18V10.5zm-12 0h.008v.008H6V10.5z"></path>
        </svg>
        <svg class="absolute bottom-10 left-1/4 w-32 h-32 text-systemBlue" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 6v12m-3-2.818l.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
    </div>

    <div class="flex flex-col md:flex-row items-center relative z-10">
        <div class="md:w-2/3 mb-6 md:mb-0 md:pr-8">
            <div class="inline-block bg-systemBlue text-white text-xs font-semibold px-3 py-1 rounded-full mb-3">FINANCEIRO</div>
            <h1 class="text-3xl font-bold text-gray-800 mb-4">Análise de Fluxo de Caixa</h1>
            <p class="text-gray-600 mb-6">Visualize e analise o movimento financeiro da sua clínica. Acompanhe receitas, despesas e resultados mensais com projeções inteligentes para tomada de decisões estratégicas.</p>

            {% set receita_total = fluxo|sum(attribute='receita') %}
            {% set despesa_total = fluxo|sum(attribute='despesa') %}
            {% set resultado = receita_total - despesa_total %}
            {% set margem = (resultado / receita_total * 100) if receita_total > 0 else 0 %}

            <div class="grid grid-cols-3 gap-4 mb-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">
                        R$ {{ '{:,.0f}'.format(receita_total).replace(',', '.') if receita_total < 1000 else '{:.1f}K'.format(receita_total/1000) if receita_total < 1000000 else '{:.1f}M'.format(receita_total/1000000) }}
                    </div>
                    <div class="text-xs text-gray-500">Receita total</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">
                        R$ {{ '{:,.0f}'.format(resultado).replace(',', '.') if resultado < 1000 and resultado > -1000 else '{:.1f}K'.format(resultado/1000) if resultado < 1000000 and resultado > -1000000 else '{:.1f}M'.format(resultado/1000000) }}
                    </div>
                    <div class="text-xs text-gray-500">Resultado</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">
                        {{ "%.1f"|format(margem) }}%
                    </div>
                    <div class="text-xs text-gray-500">Margem de lucro</div>
                </div>
            </div>
            <div class="flex flex-wrap gap-3">
                <button class="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    Gerar Relatório
                </button>
                <button class="bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-md text-sm border border-gray-200 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                    </svg>
                    Exportar Dados
                </button>
            </div>
        </div>
        <div class="md:w-1/3 flex justify-center">
            <div class="w-64 h-64 bg-white rounded-full shadow-lg flex items-center justify-center p-4 relative">
                <div class="absolute inset-0 rounded-full border-8 border-blue-100"></div>
                <div class="absolute inset-0 rounded-full border-8 border-systemBlue border-opacity-70" style="clip-path: polygon(50% 50%, 100% 0, 100% 50%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-300 border-opacity-70" style="clip-path: polygon(50% 50%, 100% 50%, 100% 100%, 50% 100%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-200 border-opacity-70" style="clip-path: polygon(50% 50%, 50% 100%, 0 100%, 0 50%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-400 border-opacity-70" style="clip-path: polygon(50% 50%, 0 50%, 0 0, 50% 0);"></div>
                <div class="z-10 text-center">
                    {% set crescimento_projetado = 12 %}
                    <div class="text-3xl font-bold text-gray-800">+{{ crescimento_projetado }}%</div>
                    <div class="text-sm text-gray-500">Crescimento projetado</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Insights com IA -->
<div class="mb-5">
    <div class="flex items-center justify-between mb-4">
        <div class="flex items-center">
            <svg class="w-5 h-5 text-systemBlue mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
            </svg>
            <h2 class="text-base font-semibold text-gray-800">Amigo Intelligence</h2>
        </div>
        <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">Atualizado agora</span>
    </div>

    <div id="insights-container" class="grid grid-cols-1 md:grid-cols-3 gap-3">
        {% for insight in insights %}
            {% with
                title=insight.title,
                description=insight.description,
                insight_type=insight.insight_type,
                content=insight.content,
                category=insight.category
            %}
                {% include 'components/ai_insight_widget.html' %}
            {% endwith %}
        {% endfor %}
    </div>
</div>

<script>
    // Carregar insights dinamicamente após 5 segundos (para demonstração)
    setTimeout(() => {
        // Contexto específico da página de fluxo de caixa
        const pageContext = {
            page_title: "Fluxo de Caixa",
            page_description: "Análise e gestão do fluxo de caixa da clínica, com foco em previsão financeira e otimização de resultados",
            key_metrics: {
                "Receita Total": "R$ {{ '{:,.2f}'.format(receita_total).replace(',', '.') }}",
                "Resultado": "R$ {{ '{:,.2f}'.format(resultado).replace(',', '.') }}",
                "Margem de Lucro": "{{ '%.1f'|format(margem) }}%",
                "Crescimento Projetado": "+{{ crescimento_projetado }}%"
            },
            analysis_focus: "Previsão financeira e otimização de resultados",
            page_elements: [
                "Fluxo de Caixa Mensal",
                "Previsão de Fluxo de Caixa",
                "Principais Fontes de Receita",
                "Principais Categorias de Despesa"
            ]
        };

        loadInsights('financeiro', 'fluxo_caixa', 'insights-container', 3, ['list', 'text', 'stat'], pageContext);
    }, 5000);
</script>

<!-- Insights Estáticos (Temporários) -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6 hidden">
    <!-- Insight de Tendências -->
    {% with
        title="Tendências Financeiras",
        description="Análise de padrões históricos",
        insight_type="list",
        content=[
            "Crescimento médio de <strong>8.5%</strong> na receita nos últimos 6 meses",
            "Despesas operacionais mantêm-se <strong>estáveis</strong> em relação à receita (42%)",
            "Sazonalidade identificada com <strong>picos de receita</strong> nos meses de março e setembro"
        ],
        category="Análise",
        action_text="Ver detalhes"
    %}
        {% include 'components/ai_insight_widget.html' %}
    {% endwith %}

    <!-- Insight de Previsão -->
    {% with
        title="Previsão Financeira",
        description="Próximos 6 meses",
        insight_type="text",
        content="Projeção de <strong>crescimento de {{ crescimento_projetado }}%</strong> na receita com aumento de apenas <strong>8%</strong> nas despesas, resultando em melhoria da margem de lucro para <strong>{{ (margem + 2.5)|round(1) }}%</strong>. O fluxo de caixa deve se manter positivo em todos os meses, com pico de resultado em novembro.",
        category="Previsão",
        action_text="Ver projeção completa"
    %}
        {% include 'components/ai_insight_widget.html' %}
    {% endwith %}

    <!-- Insight de Recomendações -->
    {% with
        title="Oportunidades de Otimização",
        description="Recomendações estratégicas",
        insight_type="list",
        content=[
            "Diversificar fontes de receita pode reduzir <strong>riscos de sazonalidade</strong>",
            "Potencial de <strong>redução de 8%</strong> em despesas administrativas através de automação",
            "Implementar <strong>política de adiantamento</strong> para procedimentos de alto valor"
        ],
        category="Estratégia",
        action_text="Implementar ações"
    %}
        {% include 'components/ai_insight_widget.html' %}
    {% endwith %}
</div>

<!-- Resumo e Previsões -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <!-- Total de Receitas -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-label-DEFAULT">Total de Receitas</p>
        </div>
        <p class="text-3xl font-semibold text-label-DEFAULT mb-1">
            R$ {{ '{:,.2f}'.format(fluxo|sum(attribute='receita')).replace(',', '.') if fluxo|sum(attribute='receita') < 1000 else '{:.1f}K'.format(fluxo|sum(attribute='receita')/1000) if fluxo|sum(attribute='receita') < 1000000 else '{:.1f}M'.format(fluxo|sum(attribute='receita')/1000000) }}
        </p>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Receitas acumuladas no período.</p>
    </div>

    <!-- Total de Despesas -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-label-DEFAULT">Total de Despesas</p>
        </div>
        <p class="text-3xl font-semibold text-label-DEFAULT mb-1">
            R$ {{ '{:,.2f}'.format(fluxo|sum(attribute='despesa')).replace(',', '.') if fluxo|sum(attribute='despesa') < 1000 else '{:.1f}K'.format(fluxo|sum(attribute='despesa')/1000) if fluxo|sum(attribute='despesa') < 1000000 else '{:.1f}M'.format(fluxo|sum(attribute='despesa')/1000000) }}
        </p>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Despesas acumuladas no período.</p>
    </div>

    <!-- Resultado -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-label-DEFAULT">Resultado</p>
        </div>
        <p class="text-3xl font-semibold {% if (fluxo|sum(attribute='receita') - fluxo|sum(attribute='despesa')) >= 0 %}text-systemGreen{% else %}text-red-600{% endif %} mb-1">
            R$ {{ '{:,.2f}'.format(fluxo|sum(attribute='receita') - fluxo|sum(attribute='despesa')).replace(',', '.') if (fluxo|sum(attribute='receita') - fluxo|sum(attribute='despesa')) < 1000 and (fluxo|sum(attribute='receita') - fluxo|sum(attribute='despesa')) > -1000 else '{:.1f}K'.format((fluxo|sum(attribute='receita') - fluxo|sum(attribute='despesa'))/1000) if (fluxo|sum(attribute='receita') - fluxo|sum(attribute='despesa')) < 1000000 and (fluxo|sum(attribute='receita') - fluxo|sum(attribute='despesa')) > -1000000 else '{:.1f}M'.format((fluxo|sum(attribute='receita') - fluxo|sum(attribute='despesa'))/1000000) }}
        </p>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Resultado financeiro do período.</p>
    </div>

    <!-- Previsão Próximo Mês -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex justify-between items-center mb-2">
            <p class="text-sm font-semibold text-label-DEFAULT">Previsão Próximo Mês</p>
            <div class="flex items-center">
                <svg class="w-4 h-4 text-systemBlue mr-1" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 1L9 9H2L7 14.5L5 22L12 17.5L19 22L17 14.5L22 9H15L12 1Z"></path>
                </svg>
                <span class="text-xs text-systemBlue font-medium">ML</span>
            </div>
        </div>
        <p class="text-3xl font-semibold {% if (fluxo|sum(attribute='receita') * 1.05 - fluxo|sum(attribute='despesa') * 1.02) >= 0 %}text-systemGreen{% else %}text-red-600{% endif %} mb-1">
            R$ {{ '{:,.2f}'.format(fluxo|sum(attribute='receita') * 1.05 - fluxo|sum(attribute='despesa') * 1.02).replace(',', '.') if (fluxo|sum(attribute='receita') * 1.05 - fluxo|sum(attribute='despesa') * 1.02) < 1000 and (fluxo|sum(attribute='receita') * 1.05 - fluxo|sum(attribute='despesa') * 1.02) > -1000 else '{:.1f}K'.format((fluxo|sum(attribute='receita') * 1.05 - fluxo|sum(attribute='despesa') * 1.02)/1000) if (fluxo|sum(attribute='receita') * 1.05 - fluxo|sum(attribute='despesa') * 1.02) < 1000000 and (fluxo|sum(attribute='receita') * 1.05 - fluxo|sum(attribute='despesa') * 1.02) > -1000000 else '{:.1f}M'.format((fluxo|sum(attribute='receita') * 1.05 - fluxo|sum(attribute='despesa') * 1.02)/1000000) }}
        </p>
        <div class="flex justify-between text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">
            <span>Receita: +5%</span>
            <span>Despesa: +2%</span>
        </div>
    </div>
</div>

<!-- Indicadores Financeiros -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
    <!-- Margem de Lucro -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-label-DEFAULT">Margem de Lucro</p>
        </div>
        {% set margem = (fluxo|sum(attribute='receita') - fluxo|sum(attribute='despesa')) / fluxo|sum(attribute='receita') * 100 if fluxo|sum(attribute='receita') > 0 else 0 %}
        <p class="text-3xl font-semibold {% if margem >= 20 %}text-systemGreen{% elif margem >= 10 %}text-systemBlue{% else %}text-red-600{% endif %} mb-1">
            {{ "%.1f"|format(margem) }}%
        </p>
        <div class="mt-2">
            <div class="flex justify-between mb-1">
                <span class="text-xs text-label-secondary">Meta</span>
                <span class="text-xs font-medium">20%</span>
            </div>
            <div class="h-2 bg-systemGray-ultralight rounded-full overflow-hidden">
                <div class="h-full {% if margem >= 20 %}bg-systemGreen{% elif margem >= 10 %}bg-systemBlue{% else %}bg-red-600{% endif %}" style="width: {% if margem * 5 > 100 %}100{% else %}{{ margem * 5 }}{% endif %}%"></div>
            </div>
        </div>
    </div>

    <!-- Crescimento de Receita -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-label-DEFAULT">Crescimento de Receita</p>
        </div>
        <p class="text-3xl font-semibold text-label-DEFAULT mb-1">+8.5%</p>
        <div class="mt-2">
            <div class="flex justify-between mb-1">
                <span class="text-xs text-label-secondary">Meta</span>
                <span class="text-xs font-medium">10%</span>
            </div>
            <div class="h-2 bg-systemGray-ultralight rounded-full overflow-hidden">
                <div class="h-full bg-systemBlue" style="width: 85%"></div>
            </div>
        </div>
    </div>

    <!-- Eficiência Operacional -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-label-DEFAULT">Eficiência Operacional</p>
        </div>
        <p class="text-3xl font-semibold text-label-DEFAULT mb-1">92%</p>
        <div class="mt-2">
            <div class="flex justify-between mb-1">
                <span class="text-xs text-label-secondary">Meta</span>
                <span class="text-xs font-medium">85%</span>
            </div>
            <div class="h-2 bg-systemGray-ultralight rounded-full overflow-hidden">
                <div class="h-full bg-systemBlue" style="width: 92%"></div>
            </div>
        </div>
    </div>
</div>

<!-- Filtros -->
<div class="bg-white rounded-view p-4 mb-6 border border-gray-200">
    <div class="flex flex-wrap gap-4">
        <div class="w-full md:w-auto">
            <label for="ano" class="block text-xs font-medium text-label-secondary mb-1">Ano</label>
            <select id="ano" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
                <option>2025</option>
                <option>2024</option>
                <option>2023</option>
            </select>
        </div>
        <div class="w-full md:w-auto flex items-end">
            <button class="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-control transition duration-150 ease-in-out text-sm">
                Filtrar
            </button>
        </div>
    </div>
</div>

<!-- Análises Avançadas -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Gráfico de Fluxo de Caixa -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Fluxo de Caixa Mensal</h2>
            <div class="flex items-center">
                <select id="periodoFluxo" class="text-xs border-systemGray-lightest rounded-control mr-2">
                    <option value="6">6 meses</option>
                    <option value="12" selected>12 meses</option>
                    <option value="24">24 meses</option>
                </select>
            </div>
        </div>
        <div class="h-80">
            <canvas id="fluxoCaixaChart"></canvas>
        </div>
    </div>

    <!-- Previsão de Fluxo de Caixa -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold text-label-DEFAULT">Previsão de Fluxo de Caixa</h2>
            <div class="flex items-center">
                <svg class="w-4 h-4 text-systemBlue mr-1" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 1L9 9H2L7 14.5L5 22L12 17.5L19 22L17 14.5L22 9H15L12 1Z"></path>
                </svg>
                <span class="text-xs text-systemBlue font-medium">ML Forecast</span>
            </div>
        </div>
        <div class="h-80">
            <canvas id="previsaoFluxoChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> Previsão de crescimento de 12% na receita nos próximos 6 meses, com aumento de 8% nas despesas.</p>
        </div>
    </div>
</div>

<!-- Análise de Categorias -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Principais Fontes de Receita -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <h2 class="text-base font-semibold text-label-DEFAULT mb-4">Principais Fontes de Receita</h2>
        <div class="h-64">
            <canvas id="fontesReceitaChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> Consultas e procedimentos representam 65% da receita total. Considere diversificar fontes de receita.</p>
        </div>
    </div>

    <!-- Principais Categorias de Despesa -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <h2 class="text-base font-semibold text-label-DEFAULT mb-4">Principais Categorias de Despesa</h2>
        <div class="h-64">
            <canvas id="categoriasDespesaChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Recomendação:</strong> Oportunidade de redução de 8% em despesas administrativas através de otimização de processos.</p>
        </div>
    </div>
</div>

<!-- Tabela de Fluxo de Caixa -->
<div class="bg-white rounded-view p-6 border border-gray-200">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-base font-semibold text-label-DEFAULT">Detalhamento do Fluxo de Caixa</h2>
        <div class="flex space-x-2">
            <button class="bg-systemGray-ultralight hover:bg-systemGray-extralight text-gray-800 px-3 py-1 rounded-full transition duration-150 ease-in-out text-xs border border-systemGray-lightest">
                Exportar
            </button>
            <button class="bg-systemGray-ultralight hover:bg-systemGray-extralight text-gray-800 px-3 py-1 rounded-full transition duration-150 ease-in-out text-xs border border-systemGray-lightest">
                Imprimir
            </button>
        </div>
    </div>

    <div class="overflow-x-auto">
        <table class="w-full text-sm data-table">
            <thead>
                <tr>
                    <th>Mês</th>
                    <th class="text-right">Receita (R$)</th>
                    <th class="text-right">Despesa (R$)</th>
                    <th class="text-right">Resultado (R$)</th>
                </tr>
            </thead>
            <tbody>
                {% for item in fluxo %}
                <tr>
                    <td>{{ item.mes }}</td>
                    <td class="text-right text-label-DEFAULT">{{ "%.2f"|format(item.receita) }}</td>
                    <td class="text-right text-label-DEFAULT">{{ "%.2f"|format(item.despesa) }}</td>
                    <td class="text-right {% if item.resultado >= 0 %}text-systemGreen{% else %}text-red-600{% endif %}">
                        {{ "%.2f"|format(item.resultado) }}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
            <tfoot class="bg-gray-50">
                <tr>
                    <td class="font-semibold">Total</td>
                    <td class="text-right text-label-DEFAULT font-semibold">{{ "%.2f"|format(fluxo|sum(attribute='receita')) }}</td>
                    <td class="text-right text-label-DEFAULT font-semibold">{{ "%.2f"|format(fluxo|sum(attribute='despesa')) }}</td>
                    <td class="text-right {% if (fluxo|sum(attribute='receita') - fluxo|sum(attribute='despesa')) >= 0 %}text-systemGreen{% else %}text-red-600{% endif %} font-semibold">
                        {{ "%.2f"|format(fluxo|sum(attribute='receita') - fluxo|sum(attribute='despesa')) }}
                    </td>
                </tr>
            </tfoot>
        </table>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Dados para o gráfico
        const meses = [{% for item in fluxo %}'{{ item.mes }}'{% if not loop.last %},{% endif %}{% endfor %}];
        const receitas = [{% for item in fluxo %}{{ item.receita }}{% if not loop.last %},{% endif %}{% endfor %}];
        const despesas = [{% for item in fluxo %}{{ item.despesa }}{% if not loop.last %},{% endif %}{% endfor %}];
        const resultados = [{% for item in fluxo %}{{ item.resultado }}{% if not loop.last %},{% endif %}{% endfor %}];

        // Dados para previsão
        const mesesFuturos = ['Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'];

        // Calcular última receita e despesa para base da previsão
        const ultimaReceita = receitas[receitas.length - 1];
        const ultimaDespesa = despesas[despesas.length - 1];

        // Gerar previsões com crescimento progressivo
        const receitasPrevisao = [];
        const despesasPrevisao = [];
        const resultadosPrevisao = [];

        for (let i = 0; i < 6; i++) {
            const fatorReceita = 1 + (0.02 * (i + 1)); // Crescimento de 2% ao mês
            const fatorDespesa = 1 + (0.013 * (i + 1)); // Crescimento de 1.3% ao mês

            const receitaPrevista = ultimaReceita * fatorReceita;
            const despesaPrevista = ultimaDespesa * fatorDespesa;

            receitasPrevisao.push(receitaPrevista);
            despesasPrevisao.push(despesaPrevista);
            resultadosPrevisao.push(receitaPrevista - despesaPrevista);
        }

        // Dados para gráficos de categorias
        const fontesReceita = {
            labels: ['Consultas', 'Procedimentos', 'Exames', 'Pacotes', 'Outros'],
            valores: [45, 20, 15, 12, 8]
        };

        const categoriasDespesa = {
            labels: ['Pessoal', 'Administrativo', 'Insumos', 'Marketing', 'Infraestrutura', 'Impostos'],
            valores: [40, 15, 20, 8, 10, 7]
        };

        // Configurar gráfico de fluxo de caixa
        const fluxoCaixaChart = new Chart(
            document.getElementById('fluxoCaixaChart'),
            {
                type: 'bar',
                data: {
                    labels: meses,
                    datasets: [
                        {
                            label: 'Receita',
                            data: receitas,
                            backgroundColor: 'rgba(0, 122, 255, 0.7)',
                            borderColor: 'rgba(0, 122, 255, 1)',
                            borderWidth: 1
                        },
                        {
                            label: 'Despesa',
                            data: despesas,
                            backgroundColor: 'rgba(142, 142, 147, 0.7)',
                            borderColor: 'rgba(142, 142, 147, 1)',
                            borderWidth: 1
                        },
                        {
                            label: 'Resultado',
                            data: resultados,
                            type: 'line',
                            backgroundColor: 'rgba(0, 122, 255, 0.1)',
                            borderColor: 'rgba(0, 122, 255, 1)',
                            borderWidth: 2,
                            pointBackgroundColor: 'rgba(0, 122, 255, 1)',
                            pointBorderColor: '#fff',
                            pointRadius: 4,
                            fill: false
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.dataset.label || '';
                                    const value = context.raw || 0;
                                    return `${label}: R$ ${value.toFixed(2)}`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            }
        );

        // Configurar gráfico de previsão
        const previsaoFluxoChart = new Chart(
            document.getElementById('previsaoFluxoChart'),
            {
                type: 'line',
                data: {
                    labels: [...meses.slice(-6), ...mesesFuturos],
                    datasets: [
                        {
                            label: 'Receita Histórica',
                            data: [...receitas.slice(-6), null, null, null, null, null, null],
                            backgroundColor: 'rgba(0, 122, 255, 0.1)',
                            borderColor: 'rgba(0, 122, 255, 1)',
                            borderWidth: 2,
                            pointBackgroundColor: 'rgba(0, 122, 255, 1)',
                            pointBorderColor: '#fff',
                            pointRadius: 4,
                            fill: true
                        },
                        {
                            label: 'Receita Prevista',
                            data: [null, null, null, null, null, receitas[receitas.length - 1], ...receitasPrevisao],
                            backgroundColor: 'rgba(0, 122, 255, 0.05)',
                            borderColor: 'rgba(0, 122, 255, 0.7)',
                            borderWidth: 2,
                            borderDash: [5, 5],
                            pointBackgroundColor: 'rgba(0, 122, 255, 0.7)',
                            pointBorderColor: '#fff',
                            pointRadius: 4,
                            fill: true
                        },
                        {
                            label: 'Despesa Histórica',
                            data: [...despesas.slice(-6), null, null, null, null, null, null],
                            backgroundColor: 'rgba(142, 142, 147, 0.1)',
                            borderColor: 'rgba(142, 142, 147, 1)',
                            borderWidth: 2,
                            pointBackgroundColor: 'rgba(142, 142, 147, 1)',
                            pointBorderColor: '#fff',
                            pointRadius: 4,
                            fill: true
                        },
                        {
                            label: 'Despesa Prevista',
                            data: [null, null, null, null, null, despesas[despesas.length - 1], ...despesasPrevisao],
                            backgroundColor: 'rgba(142, 142, 147, 0.05)',
                            borderColor: 'rgba(142, 142, 147, 0.7)',
                            borderWidth: 2,
                            borderDash: [5, 5],
                            pointBackgroundColor: 'rgba(142, 142, 147, 0.7)',
                            pointBorderColor: '#fff',
                            pointRadius: 4,
                            fill: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.dataset.label || '';
                                    const value = context.raw || 0;
                                    return value ? `${label}: R$ ${value.toFixed(2)}` : '';
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            }
        );

        // Configurar gráfico de fontes de receita
        const fontesReceitaChart = new Chart(
            document.getElementById('fontesReceitaChart'),
            {
                type: 'pie',
                data: {
                    labels: fontesReceita.labels,
                    datasets: [{
                        data: fontesReceita.valores,
                        backgroundColor: [
                            'rgba(0, 122, 255, 0.7)',
                            'rgba(142, 142, 147, 0.7)',
                            'rgba(174, 174, 178, 0.7)',
                            'rgba(199, 199, 204, 0.7)',
                            'rgba(209, 209, 214, 0.7)'
                        ],
                        borderColor: [
                            'rgba(0, 122, 255, 1)',
                            'rgba(142, 142, 147, 1)',
                            'rgba(174, 174, 178, 1)',
                            'rgba(199, 199, 204, 1)',
                            'rgba(209, 209, 214, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    return `${label}: ${value}%`;
                                }
                            }
                        }
                    }
                }
            }
        );

        // Configurar gráfico de categorias de despesa
        const categoriasDespesaChart = new Chart(
            document.getElementById('categoriasDespesaChart'),
            {
                type: 'doughnut',
                data: {
                    labels: categoriasDespesa.labels,
                    datasets: [{
                        data: categoriasDespesa.valores,
                        backgroundColor: [
                            'rgba(0, 122, 255, 0.7)',
                            'rgba(142, 142, 147, 0.7)',
                            'rgba(174, 174, 178, 0.7)',
                            'rgba(199, 199, 204, 0.7)',
                            'rgba(209, 209, 214, 0.7)',
                            'rgba(229, 229, 234, 0.7)'
                        ],
                        borderColor: [
                            'rgba(0, 122, 255, 1)',
                            'rgba(142, 142, 147, 1)',
                            'rgba(174, 174, 178, 1)',
                            'rgba(199, 199, 204, 1)',
                            'rgba(209, 209, 214, 1)',
                            'rgba(229, 229, 234, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    return `${label}: ${value}%`;
                                }
                            }
                        }
                    }
                }
            }
        );

        // Event listener para o seletor de período
        document.getElementById('periodoFluxo').addEventListener('change', function() {
            // Aqui seria implementada a lógica para atualizar o gráfico com base no período selecionado
            console.log('Período selecionado: ' + this.value);
        });
    });
</script>
{% endblock %}

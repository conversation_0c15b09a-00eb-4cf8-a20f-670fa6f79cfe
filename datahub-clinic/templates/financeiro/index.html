{% extends 'base.html' %}

{% block title %}Módulo Financeiro - Amigo DataApp{% endblock %}

{% block header %}Módulo Financeiro{% endblock %}

{% block content %}
<!-- Hero do módulo -->
<div class="bg-white rounded-view p-8 mb-8 border border-gray-200">
    <div class="flex flex-col md:flex-row items-center">
        <div class="md:w-2/3">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Módulo Financeiro</h1>
            <p class="text-lg text-label-secondary mb-6">Gere<PERSON>ie contas a receber e a pagar, acompanhe o fluxo de caixa e realize fechamentos.</p>
            <div class="flex space-x-4">
                <a href="{{ url_for('contas_receber') }}" class="bg-systemBlue hover:bg-blue-600 text-white px-6 py-2 rounded-full transition duration-150 ease-in-out">
                    Contas a Receber
                </a>
                <a href="{{ url_for('fluxo_caixa') }}" class="bg-systemGray-ultralight hover:bg-systemGray-extralight text-gray-800 px-6 py-2 rounded-full transition duration-150 ease-in-out border border-systemGray-lightest">
                    Fluxo de Caixa
                </a>
            </div>
        </div>
        <div class="md:w-1/3 mt-6 md:mt-0 flex justify-center">
            <div class="w-48 h-48 bg-blue-100 rounded-full flex items-center justify-center">
                <svg class="w-24 h-24 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
        </div>
    </div>
</div>

<!-- Resumo do módulo -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total a Receber -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-gray-900">Total a Receber</p>
        </div>
        <p class="text-3xl font-semibold text-systemGreen mb-1">
            R$ {{ "%.2f"|format(dados.contas_receber|sum(attribute='valor')) }}
        </p>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Valor total a receber.</p>
    </div>

    <!-- Total a Pagar -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-gray-900">Total a Pagar</p>
        </div>
        <p class="text-3xl font-semibold text-red-600 mb-1">
            R$ {{ "%.2f"|format(dados.contas_pagar|sum(attribute='valor')) }}
        </p>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Valor total a pagar.</p>
    </div>

    <!-- Saldo -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-gray-900">Saldo</p>
        </div>
        <p class="text-3xl font-semibold {% if (dados.contas_receber|sum(attribute='valor') - dados.contas_pagar|sum(attribute='valor')) >= 0 %}text-systemGreen{% else %}text-red-600{% endif %} mb-1">
            R$ {{ "%.2f"|format(dados.contas_receber|sum(attribute='valor') - dados.contas_pagar|sum(attribute='valor')) }}
        </p>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Diferença entre contas a receber e a pagar.</p>
    </div>

    <!-- Faturamento Médio Diário -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-gray-900">Faturamento Médio Diário</p>
        </div>
        <p class="text-3xl font-semibold text-gray-900 mb-1">
            R$ {{ "%.2f"|format(dados.fechamento_caixa|sum(attribute='total') / (dados.fechamento_caixa|length if dados.fechamento_caixa|length > 0 else 1)) }}
        </p>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Média de faturamento diário.</p>
    </div>
</div>

<!-- Insights com IA -->
<div class="mb-8">
    <div class="flex items-center justify-between mb-4">
        <div class="flex items-center">
            <svg class="w-5 h-5 text-systemBlue mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
            </svg>
            <h2 class="text-base font-semibold text-gray-800">Amigo Intelligence</h2>
        </div>
        <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">Atualizado agora</span>
    </div>

    <div id="insights-container" class="grid grid-cols-1 md:grid-cols-3 gap-3">
        {% for insight in insights %}
            {% with
                title=insight.title,
                description=insight.description,
                insight_type=insight.insight_type,
                content=insight.content,
                category=insight.category,
                action_text=insight.action_text,
                action_url=insight.action_url
            %}
                {% include 'components/ai_insight_widget.html' %}
            {% endwith %}
        {% endfor %}
    </div>
</div>

<script>
    // Carregar insights dinamicamente após 5 segundos (para demonstração)
    setTimeout(() => {
        // Contexto específico da página de índice financeiro
        const pageContext = {
            page_title: "Módulo Financeiro",
            page_description: "Visão geral do módulo financeiro, com foco em fluxo de caixa e otimização financeira",
            key_metrics: {
                "Total a Receber": "R$ {{ '%.2f'|format(dados.contas_receber|sum(attribute='valor')) }}",
                "Total a Pagar": "R$ {{ '%.2f'|format(dados.contas_pagar|sum(attribute='valor')) }}",
                "Saldo": "R$ {{ '%.2f'|format(dados.contas_receber|sum(attribute='valor') - dados.contas_pagar|sum(attribute='valor')) }}",
                "Faturamento Médio Diário": "R$ {{ '%.2f'|format(dados.fechamento_caixa|sum(attribute='total') / (dados.fechamento_caixa|length if dados.fechamento_caixa|length > 0 else 1)) }}"
            },
            analysis_focus: "Fluxo de caixa e otimização financeira",
            page_elements: [
                "Resumo do módulo",
                "Relatórios disponíveis"
            ]
        };

        loadInsights('financeiro', 'index', 'insights-container', 3, ['list', 'text', 'stat'], pageContext);
    }, 5000);

    // Inicializar gráficos para os cards de insights
    document.addEventListener('DOMContentLoaded', function() {
        // Gráfico de Fluxo de Caixa
        if (document.getElementById('cashFlowChart')) {
            const cashFlowCtx = document.getElementById('cashFlowChart').getContext('2d');
            new Chart(cashFlowCtx, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'],
                    datasets: [{
                        label: 'Receitas',
                        data: [85000, 92000, 88000, 94000, 90000, 96000, 98000, 102000, 105000, 110000, 115000, 120000],
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        borderColor: 'rgba(16, 185, 129, 0.8)',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true
                    }, {
                        label: 'Despesas',
                        data: [65000, 68000, 70000, 72000, 69000, 74000, 75000, 78000, 80000, 82000, 85000, 88000],
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        borderColor: 'rgba(239, 68, 68, 0.8)',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                boxWidth: 10,
                                font: {
                                    size: 10
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.y !== null) {
                                        label += new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(context.parsed.y);
                                    }
                                    return label;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: false,
                            ticks: {
                                callback: function(value) {
                                    return 'R$ ' + (value/1000) + 'K';
                                },
                                font: {
                                    size: 9
                                }
                            }
                        },
                        x: {
                            ticks: {
                                font: {
                                    size: 9
                                }
                            }
                        }
                    }
                }
            });
        }

        // Gráfico de Rentabilidade por Serviço
        if (document.getElementById('servicesProfitabilityChart')) {
            const servicesProfitabilityCtx = document.getElementById('servicesProfitabilityChart').getContext('2d');
            new Chart(servicesProfitabilityCtx, {
                type: 'bar',
                data: {
                    labels: ['Procedimentos Estéticos', 'Tratamentos Ortodônticos', 'Implantes', 'Cirurgias', 'Consultas de Rotina'],
                    datasets: [{
                        label: 'Margem de Lucro (%)',
                        data: [68, 55, 48, 42, 32],
                        backgroundColor: [
                            'rgba(124, 58, 237, 0.8)',
                            'rgba(139, 92, 246, 0.7)',
                            'rgba(167, 139, 250, 0.6)',
                            'rgba(196, 181, 253, 0.5)',
                            'rgba(221, 214, 254, 0.4)'
                        ],
                        borderColor: [
                            'rgba(124, 58, 237, 1)',
                            'rgba(139, 92, 246, 1)',
                            'rgba(167, 139, 250, 1)',
                            'rgba(196, 181, 253, 1)',
                            'rgba(221, 214, 254, 1)'
                        ],
                        borderWidth: 1
                    }, {
                        label: 'Volume Mensal',
                        data: [45, 120, 35, 25, 180],
                        backgroundColor: 'rgba(209, 213, 219, 0.5)',
                        borderColor: 'rgba(156, 163, 175, 1)',
                        borderWidth: 1,
                        type: 'line',
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                boxWidth: 10,
                                font: {
                                    size: 10
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Margem de Lucro (%)',
                                font: {
                                    size: 10
                                }
                            },
                            ticks: {
                                font: {
                                    size: 9
                                }
                            },
                            max: 80
                        },
                        y1: {
                            beginAtZero: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'Volume Mensal',
                                font: {
                                    size: 10
                                }
                            },
                            ticks: {
                                font: {
                                    size: 9
                                }
                            },
                            grid: {
                                drawOnChartArea: false
                            },
                            max: 200
                        },
                        x: {
                            ticks: {
                                font: {
                                    size: 9
                                }
                            }
                        }
                    }
                }
            });
        }
    });
</script>

<!-- Grid de Insights Financeiros -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
    <!-- Card 1: Previsão de Fluxo de Caixa -->
    <div class="bg-white rounded-lg shadow-sm p-4 border-l-4 border-blue-500">
        <div class="flex items-center justify-between mb-3">
            <div class="flex items-center">
                <svg class="w-4 h-4 text-blue-500 mr-1.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                </svg>
                <span class="text-sm font-medium text-gray-800">Previsão de Fluxo de Caixa</span>
            </div>
            <span class="inline-block bg-blue-50 text-blue-600 text-xs font-medium px-2 py-0.5 rounded-full">Previsão</span>
        </div>

        <div class="mb-3">
            <div class="relative h-32">
                <canvas id="cashFlowChart" height="128"></canvas>
            </div>
        </div>

        <div class="bg-blue-50 p-3 rounded text-xs text-gray-600">
            <p>Com base nos padrões históricos, prevemos um <strong>superávit de R$ 78.650,00</strong> para o próximo mês. Recomendamos reservar <strong>20%</strong> deste valor para contingências.</p>
        </div>

        <div class="mt-2 pt-2 border-t border-gray-100 flex justify-between items-center">
            <a href="{{ url_for('fluxo_caixa') }}" class="text-xs text-blue-600 hover:text-blue-700 font-medium">
                Ver projeção detalhada
            </a>
            <span class="text-xs text-gray-400">Amigo Intelligence</span>
        </div>
    </div>

    <!-- Card 2: Análise de Inadimplência -->
    <div class="bg-white rounded-lg shadow-sm p-4 border-l-4 border-red-500">
        <div class="flex items-center justify-between mb-3">
            <div class="flex items-center">
                <svg class="w-4 h-4 text-red-500 mr-1.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
                </svg>
                <span class="text-sm font-medium text-gray-800">Análise de Inadimplência</span>
            </div>
            <span class="inline-block bg-red-50 text-red-600 text-xs font-medium px-2 py-0.5 rounded-full">Recuperação</span>
        </div>

        <div class="flex mb-3">
            <div class="w-1/2 pr-2">
                <div class="flex flex-col items-center justify-center bg-gray-50 rounded-lg p-3 h-32">
                    <div class="w-16 h-16 relative mb-2">
                        <svg viewBox="0 0 36 36" class="w-full h-full">
                            <path d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" fill="none" stroke="#eee" stroke-width="3" />
                            <path d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" fill="none" stroke="#ef4444" stroke-width="3" stroke-dasharray="15, 100" />
                            <text x="18" y="20.5" class="text-xs font-semibold" text-anchor="middle" fill="#b91c1c">15%</text>
                        </svg>
                    </div>
                    <p class="text-xs text-gray-600 text-center">Taxa de inadimplência</p>
                </div>
            </div>
            <div class="w-1/2 pl-2">
                <div class="bg-gray-50 rounded-lg p-3 h-32">
                    <p class="text-xs text-gray-600 mb-2">Contas em atraso:</p>
                    <div class="space-y-2">
                        <div>
                            <div class="flex justify-between text-xs mb-1">
                                <span>1-15 dias</span>
                                <span class="font-medium">R$ 12.500</span>
                            </div>
                            <div class="h-1.5 bg-gray-200 rounded-full overflow-hidden">
                                <div class="h-full bg-yellow-400" style="width: 45%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between text-xs mb-1">
                                <span>16-30 dias</span>
                                <span class="font-medium">R$ 8.200</span>
                            </div>
                            <div class="h-1.5 bg-gray-200 rounded-full overflow-hidden">
                                <div class="h-full bg-orange-400" style="width: 30%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between text-xs mb-1">
                                <span>>30 dias</span>
                                <span class="font-medium">R$ 6.800</span>
                            </div>
                            <div class="h-1.5 bg-gray-200 rounded-full overflow-hidden">
                                <div class="h-full bg-red-500" style="width: 25%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-red-50 p-3 rounded text-xs text-gray-600">
            <p><strong>Recomendação:</strong> Oferecer parcelamento em até 6x recuperaria aproximadamente <strong>85%</strong> dos valores. Contato por WhatsApp tem <strong>3.2x mais eficácia</strong> que e-mail.</p>
        </div>

        <div class="mt-2 pt-2 border-t border-gray-100 flex justify-between items-center">
            <a href="{{ url_for('contas_receber') }}" class="text-xs text-red-600 hover:text-red-700 font-medium">
                Ver contas em atraso
            </a>
            <span class="text-xs text-gray-400">Amigo Intelligence</span>
        </div>
    </div>

    <!-- Card 3: Otimização de Custos -->
    <div class="bg-white rounded-lg shadow-sm p-4 border-l-4 border-green-500">
        <div class="flex items-center justify-between mb-3">
            <div class="flex items-center">
                <svg class="w-4 h-4 text-green-500 mr-1.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 18.75a60.07 60.07 0 0115.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 013 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 00-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 01-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 003 15h-.75M15 10.5a3 3 0 11-6 0 3 3 0 016 0zm3 0h.008v.008H18V10.5zm-12 0h.008v.008H6V10.5z" />
                </svg>
                <span class="text-sm font-medium text-gray-800">Otimização de Custos</span>
            </div>
            <span class="inline-block bg-green-50 text-green-600 text-xs font-medium px-2 py-0.5 rounded-full">Economia</span>
        </div>

        <div class="mb-3">
            <div class="bg-gray-50 rounded-lg p-3">
                <div class="flex items-center justify-between mb-2">
                    <p class="text-xs font-medium text-gray-700">Economia potencial mensal</p>
                    <p class="text-sm font-semibold text-green-600">R$ 12.850,00</p>
                </div>

                <div class="space-y-3">
                    <div>
                        <div class="flex justify-between text-xs mb-1">
                            <span>Renegociação com fornecedores</span>
                            <span class="font-medium text-green-600">R$ 5.200</span>
                        </div>
                        <div class="h-1.5 bg-gray-200 rounded-full overflow-hidden">
                            <div class="h-full bg-green-500" style="width: 40%"></div>
                        </div>
                    </div>
                    <div>
                        <div class="flex justify-between text-xs mb-1">
                            <span>Cashback cartão premium</span>
                            <span class="font-medium text-green-600">R$ 2.350</span>
                        </div>
                        <div class="h-1.5 bg-gray-200 rounded-full overflow-hidden">
                            <div class="h-full bg-green-500" style="width: 18%"></div>
                        </div>
                    </div>
                    <div>
                        <div class="flex justify-between text-xs mb-1">
                            <span>Antecipação de pagamentos</span>
                            <span class="font-medium text-green-600">R$ 5.300</span>
                        </div>
                        <div class="h-1.5 bg-gray-200 rounded-full overflow-hidden">
                            <div class="h-full bg-green-500" style="width: 42%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-green-50 p-3 rounded text-xs text-gray-600">
            <p><strong>Recomendação:</strong> Consolidar pagamentos em um único cartão premium geraria <strong>R$ 2.350,00/mês</strong> em cashback. Antecipação de pagamentos oferece desconto médio de <strong>4.2%</strong>.</p>
        </div>

        <div class="mt-2 pt-2 border-t border-gray-100 flex justify-between items-center">
            <a href="{{ url_for('contas_pagar') }}" class="text-xs text-green-600 hover:text-green-700 font-medium">
                Explorar oportunidades
            </a>
            <span class="text-xs text-gray-400">Amigo Intelligence</span>
        </div>
    </div>
</div>

<!-- Segunda linha de insights -->
<div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
    <!-- Card 4: Análise de Rentabilidade por Serviço -->
    <div class="bg-white rounded-lg shadow-sm p-4 border-l-4 border-purple-500">
        <div class="flex items-center justify-between mb-3">
            <div class="flex items-center">
                <svg class="w-4 h-4 text-purple-500 mr-1.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 3v11.25A2.25 2.25 0 006 16.5h2.25M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0118 16.5h-2.25m-7.5 0h7.5m-7.5 0l-1 3m8.5-3l1 3m0 0l.5 1.5m-.5-1.5h-9.5m0 0l-.5 1.5m.75-9l3-3 2.148 2.148A12.061 12.061 0 0116.5 7.605" />
                </svg>
                <span class="text-sm font-medium text-gray-800">Rentabilidade por Serviço</span>
            </div>
            <span class="inline-block bg-purple-50 text-purple-600 text-xs font-medium px-2 py-0.5 rounded-full">Análise</span>
        </div>

        <div class="mb-3">
            <div class="relative h-48">
                <canvas id="servicesProfitabilityChart" height="192"></canvas>
            </div>
        </div>

        <div class="bg-purple-50 p-3 rounded text-xs text-gray-600">
            <p><strong>Insight:</strong> Procedimentos estéticos têm a maior margem de lucro (68%), enquanto consultas de rotina têm a menor (32%). Aumentar o volume de procedimentos estéticos em 15% elevaria a rentabilidade geral em 8.5%.</p>
        </div>

        <div class="mt-2 pt-2 border-t border-gray-100 flex justify-between items-center">
            <button class="text-xs text-purple-600 hover:text-purple-700 font-medium">
                Ver análise detalhada
            </button>
            <span class="text-xs text-gray-400">Amigo Intelligence</span>
        </div>
    </div>

    <!-- Card 5: Previsão de Investimentos -->
    <div class="bg-white rounded-lg shadow-sm p-4 border-l-4 border-amber-500">
        <div class="flex items-center justify-between mb-3">
            <div class="flex items-center">
                <svg class="w-4 h-4 text-amber-500 mr-1.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 18L9 11.25l4.306 4.307a11.95 11.95 0 015.814-5.519l2.74-1.22m0 0l-5.94-2.28m5.94 2.28l-2.28 5.941" />
                </svg>
                <span class="text-sm font-medium text-gray-800">Planejamento de Investimentos</span>
            </div>
            <span class="inline-block bg-amber-50 text-amber-600 text-xs font-medium px-2 py-0.5 rounded-full">Estratégia</span>
        </div>

        <div class="grid grid-cols-2 gap-3 mb-3">
            <div class="bg-gray-50 rounded-lg p-3">
                <p class="text-xs font-medium text-gray-700 mb-2">Prioridades de Investimento</p>
                <div class="space-y-2">
                    <div class="flex items-center">
                        <div class="w-2 h-2 rounded-full bg-amber-500 mr-2"></div>
                        <div class="flex-1">
                            <div class="flex justify-between text-xs mb-1">
                                <span>Equipamentos</span>
                                <span class="font-medium">45%</span>
                            </div>
                            <div class="h-1.5 bg-gray-200 rounded-full overflow-hidden">
                                <div class="h-full bg-amber-500" style="width: 45%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center">
                        <div class="w-2 h-2 rounded-full bg-amber-400 mr-2"></div>
                        <div class="flex-1">
                            <div class="flex justify-between text-xs mb-1">
                                <span>Tecnologia</span>
                                <span class="font-medium">30%</span>
                            </div>
                            <div class="h-1.5 bg-gray-200 rounded-full overflow-hidden">
                                <div class="h-full bg-amber-400" style="width: 30%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center">
                        <div class="w-2 h-2 rounded-full bg-amber-300 mr-2"></div>
                        <div class="flex-1">
                            <div class="flex justify-between text-xs mb-1">
                                <span>Infraestrutura</span>
                                <span class="font-medium">15%</span>
                            </div>
                            <div class="h-1.5 bg-gray-200 rounded-full overflow-hidden">
                                <div class="h-full bg-amber-300" style="width: 15%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center">
                        <div class="w-2 h-2 rounded-full bg-amber-200 mr-2"></div>
                        <div class="flex-1">
                            <div class="flex justify-between text-xs mb-1">
                                <span>Marketing</span>
                                <span class="font-medium">10%</span>
                            </div>
                            <div class="h-1.5 bg-gray-200 rounded-full overflow-hidden">
                                <div class="h-full bg-amber-200" style="width: 10%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-gray-50 rounded-lg p-3">
                <p class="text-xs font-medium text-gray-700 mb-2">ROI Projetado</p>
                <div class="flex flex-col h-full justify-center">
                    <div class="text-center">
                        <p class="text-3xl font-bold text-amber-600">285%</p>
                        <p class="text-xs text-gray-500">em 36 meses</p>
                    </div>
                    <div class="mt-3">
                        <div class="flex justify-between text-xs mb-1">
                            <span>Equipamentos</span>
                            <span class="font-medium text-amber-600">320%</span>
                        </div>
                        <div class="flex justify-between text-xs mb-1">
                            <span>Tecnologia</span>
                            <span class="font-medium text-amber-600">250%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-amber-50 p-3 rounded text-xs text-gray-600">
            <p><strong>Recomendação:</strong> Priorizar investimento em equipamentos de última geração para procedimentos estéticos, com ROI projetado de 320% em 36 meses. Investimento inicial recomendado: <strong>R$ 180.000,00</strong>.</p>
        </div>

        <div class="mt-2 pt-2 border-t border-gray-100 flex justify-between items-center">
            <button class="text-xs text-amber-600 hover:text-amber-700 font-medium">
                Ver plano de investimentos
            </button>
            <span class="text-xs text-gray-400">Amigo Intelligence</span>
        </div>
    </div>
</div>

<!-- Insights Estáticos (Temporários) - Escondidos -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8 hidden">
    <!-- Insight de Fluxo de Caixa -->
    {% with
        title="Previsão de Fluxo de Caixa",
        description="Projeção para os próximos 30 dias",
        insight_type="text",
        content="Com base nos padrões históricos, prevemos um <strong>superávit de R$ 78.650,00</strong> para o próximo mês. Recomendamos reservar <strong>20%</strong> deste valor para contingências e investimentos em equipamentos, considerando a sazonalidade do período.",
        category="Previsão",
        action_text="Ver projeção detalhada",
        action_url=url_for('fluxo_caixa')
    %}
        {% include 'components/ai_insight_widget.html' %}
    {% endwith %}

    <!-- Insight de Inadimplência -->
    {% with
        title="Análise de Inadimplência",
        description="Oportunidades de recuperação",
        insight_type="list",
        content=[
            "<strong>5 pacientes</strong> com mais de 60 dias de atraso somam <strong>R$ 18.500,00</strong>",
            "Oferecer parcelamento em até 6x recuperaria aproximadamente <strong>85%</strong> dos valores",
            "Contato por WhatsApp tem <strong>3.2x mais eficácia</strong> que e-mail para negociação"
        ],
        category="Recuperação",
        action_text="Ver contas em atraso",
        action_url=url_for('contas_receber')
    %}
        {% include 'components/ai_insight_widget.html' %}
    {% endwith %}

    <!-- Insight de Otimização -->
    {% with
        title="Otimização de Custos",
        description="Oportunidades de economia",
        insight_type="list",
        content=[
            "Renegociação com fornecedores poderia reduzir custos em até <strong>8%</strong>",
            "Consolidar pagamentos em um único cartão premium geraria <strong>R$ 2.350,00/mês</strong> em cashback",
            "Antecipação de pagamentos oferece desconto médio de <strong>4.2%</strong>"
        ],
        category="Economia",
        action_text="Explorar oportunidades",
        action_url=url_for('contas_pagar')
    %}
        {% include 'components/ai_insight_widget.html' %}
    {% endwith %}
</div>

<!-- Relatórios disponíveis -->
<div class="bg-white rounded-view p-6 border border-gray-200">
    <h2 class="text-xl font-semibold mb-6">Relatórios Disponíveis</h2>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Contas a Receber -->
        <a href="{{ url_for('contas_receber') }}" class="bg-systemGray-ultralight hover:bg-systemGray-extralight p-6 rounded-view border border-systemGray-lightest transition duration-150 ease-in-out">
            <div class="flex flex-col items-center text-center">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                    <svg class="w-8 h-8 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-base font-semibold mb-2">Contas a Receber</h3>
                <p class="text-sm text-label-secondary">Gerencie os valores a receber de clientes.</p>
            </div>
        </a>

        <!-- Contas a Pagar -->
        <a href="{{ url_for('contas_pagar') }}" class="bg-systemGray-ultralight hover:bg-systemGray-extralight p-6 rounded-view border border-systemGray-lightest transition duration-150 ease-in-out">
            <div class="flex flex-col items-center text-center">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                    <svg class="w-8 h-8 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M15 9a2 2 0 10-4 0v5a2 2 0 01-2 2h6m-6-4h4m8 0a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-base font-semibold mb-2">Contas a Pagar</h3>
                <p class="text-sm text-label-secondary">Gerencie os valores a pagar a fornecedores.</p>
            </div>
        </a>

        <!-- Fluxo de Caixa -->
        <a href="{{ url_for('fluxo_caixa') }}" class="bg-systemGray-ultralight hover:bg-systemGray-extralight p-6 rounded-view border border-systemGray-lightest transition duration-150 ease-in-out">
            <div class="flex flex-col items-center text-center">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                    <svg class="w-8 h-8 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"></path>
                    </svg>
                </div>
                <h3 class="text-base font-semibold mb-2">Fluxo de Caixa</h3>
                <p class="text-sm text-label-secondary">Acompanhe o fluxo de receitas e despesas.</p>
            </div>
        </a>

        <!-- Fechamento de Caixa -->
        <a href="{{ url_for('fechamento_caixa') }}" class="bg-systemGray-ultralight hover:bg-systemGray-extralight p-6 rounded-view border border-systemGray-lightest transition duration-150 ease-in-out">
            <div class="flex flex-col items-center text-center">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                    <svg class="w-8 h-8 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <h3 class="text-base font-semibold mb-2">Fechamento de Caixa</h3>
                <p class="text-sm text-label-secondary">Realize o fechamento diário do caixa.</p>
            </div>
        </a>
    </div>
</div>
{% endblock %}

{% extends 'base.html' %}
{% from 'components/page_context.html' import expose_context %}

{% block title %}Dashboard - Amigo DataHub{% endblock %}

{% block header %}Dashboard{% endblock %}

{% block content %}

{{ expose_context(
    title="Dashboard Principal",
    description="Visão geral da clínica com métricas integradas de todos os módulos",
    key_metrics={
        "Vendas": "R$ " + (resumo.vendas_total|default(0)|string),
        "Produção": "R$ " + (resumo.producao_total|default(0)|string),
        "Agendamentos": resumo.agendamentos_mes_corrente|default(0),
        "Atendimentos": resumo.atendimentos_mes_corrente|default(0)
    },
    analysis_focus="Performance de vendas, produção médica, agendamentos e atendimentos realizados",
    page_elements=["KPIs Principais", "Métricas Opcionais", "Análises Comparativas", "Insights Estratégicos"],
    additional_context={
        "especialidades": especialidades|default([]),
        "origens_leads": origens|default([]),
        "unidades": unidades|default([])
    }
) }}

<!-- Hero de boas-vindas personalizado -->
<div class="bg-gradient-to-r from-gray-50 to-white rounded-view p-8 mb-8 border border-gray-200 relative overflow-hidden">
    <!-- Elementos geométricos decorativos de fundo com opacidade -->
    <div class="absolute top-0 right-0 w-full h-full overflow-hidden">
        <!-- Círculos decorativos -->
        <div class="absolute top-[-80px] right-[-80px] w-[400px] h-[400px] rounded-full bg-gray-100 opacity-60"></div>
        <div class="absolute bottom-[-100px] left-[-100px] w-[300px] h-[300px] rounded-full bg-gray-100 opacity-40"></div>

        <!-- Formas geométricas suaves -->
        <div class="absolute top-1/4 right-1/3 w-60 h-60 bg-gray-100 opacity-30 transform rotate-45"></div>

        <!-- Ondas suaves (SVG) -->
        <svg class="absolute bottom-0 left-0 w-full opacity-10" viewBox="0 0 1440 320" xmlns="http://www.w3.org/2000/svg">
            <path fill="#f3f4f6" fill-opacity="1" d="M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,224C672,245,768,267,864,261.3C960,256,1056,224,1152,208C1248,192,1344,192,1392,192L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path>
        </svg>

        <!-- Pontos em grade refinados -->
        <div class="absolute top-0 left-0 w-full h-full">
            <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <pattern id="dots" width="24" height="24" patternUnits="userSpaceOnUse">
                        <circle cx="2" cy="2" r="1" fill="#9ca3af" opacity="0.2"/>
                    </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#dots)" opacity="0.3"/>
            </svg>
        </div>
    </div>

    <div class="flex flex-col md:flex-row items-center justify-center relative z-10 max-w-5xl mx-auto">
        <div class="md:w-full text-center">
            <div class="flex items-center justify-center mb-2">
                <span class="text-sm font-medium text-gray-500" id="saudacao">Bom dia</span>
                <span class="mx-2 text-gray-300">•</span>
                <span class="text-sm font-medium text-gray-500">{{ current_date }}</span>
            </div>

            <div class="flex items-center justify-center mb-3">
                <img src="/static/amigo-logo.png" alt="Amigo Logo" class="h-12">
                <div class="flex items-center ml-3">
                    <span class="mx-1 text-gray-400 font-bold text-sm">|</span>
                    <span class="text-gray-700 font-bold text-xl">DataHub</span>
                </div>
            </div>

            <h1 class="text-3xl font-bold text-gray-800 mb-3 flex items-center justify-center">
                <span>Olá Dr. Bruno Abreu</span>
                <span class="ml-2 text-4xl animate-wave inline-block">👋</span>
            </h1>

            <p class="text-xl font-bold text-gray-700 mb-8 max-w-2xl mx-auto">Bem-vindo ao DataHub do Amigo Clinic</p>
            <p class="text-gray-600 mb-8 max-w-2xl mx-auto">A plataforma completa para gestão e análise de dados da sua clínica</p>

            <script>
                // Atualizar saudação baseada na hora atual
                document.addEventListener('DOMContentLoaded', function() {
                    const hora = new Date().getHours();
                    const saudacaoEl = document.getElementById('saudacao');

                    if (hora >= 5 && hora < 12) {
                        saudacaoEl.textContent = 'Bom dia';
                    } else if (hora >= 12 && hora < 18) {
                        saudacaoEl.textContent = 'Boa tarde';
                    } else {
                        saudacaoEl.textContent = 'Boa noite';
                    }
                });
            </script>

            <style>
                @keyframes wave {
                    0% { transform: rotate(0deg); }
                    10% { transform: rotate(20deg); }
                    20% { transform: rotate(-10deg); }
                    30% { transform: rotate(20deg); }
                    40% { transform: rotate(-5deg); }
                    50% { transform: rotate(15deg); }
                    60% { transform: rotate(0deg); }
                    100% { transform: rotate(0deg); }
                }
                .animate-wave {
                    animation: wave 2.5s ease infinite;
                    transform-origin: 70% 70%;
                    display: inline-block;
                    margin-top: -5px;
                }

                /* Estilos para botões de toggle da legenda */
                .legend-toggle.active {
                    background-color: #f9fafb;
                    border-color: #374151;
                }
                .legend-toggle.inactive {
                    background-color: #f3f4f6;
                    border-color: #d1d5db;
                    opacity: 0.5;
                }
                .legend-toggle.inactive span {
                    color: #9ca3af;
                }
                .legend-toggle.inactive .w-3.h-3 {
                    opacity: 0.3;
                }
            </style>

            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-4xl mx-auto">
                <a href="{{ url_for('agendamentos') }}" class="flex flex-col items-center bg-gray-50 hover:bg-white border border-gray-100 text-gray-700 px-4 py-4 rounded-lg transition duration-150 ease-in-out group">
                    <div class="w-10 h-10 bg-white rounded-full flex items-center justify-center mb-2 group-hover:bg-gray-50 shadow-sm">
                        <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <span class="text-xs font-medium">Agendamentos</span>
                </a>

                <a href="{{ url_for('producao_medica') }}" class="flex flex-col items-center bg-gray-50 hover:bg-white border border-gray-100 text-gray-700 px-4 py-4 rounded-lg transition duration-150 ease-in-out group">
                    <div class="w-10 h-10 bg-white rounded-full flex items-center justify-center mb-2 group-hover:bg-gray-50 shadow-sm">
                        <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                    </div>
                    <span class="text-xs font-medium">Produção</span>
                </a>

                <a href="{{ url_for('financeiro') }}" class="flex flex-col items-center bg-gray-50 hover:bg-white border border-gray-100 text-gray-700 px-4 py-4 rounded-lg transition duration-150 ease-in-out group">
                    <div class="w-10 h-10 bg-white rounded-full flex items-center justify-center mb-2 group-hover:bg-gray-50 shadow-sm">
                        <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <span class="text-xs font-medium">Financeiro</span>
                </a>

                <a href="{{ url_for('paciente') }}" class="flex flex-col items-center bg-gray-50 hover:bg-white border border-gray-100 text-gray-700 px-4 py-4 rounded-lg transition duration-150 ease-in-out group">
                    <div class="w-10 h-10 bg-white rounded-full flex items-center justify-center mb-2 group-hover:bg-gray-50 shadow-sm">
                        <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </div>
                    <span class="text-xs font-medium">Pacientes</span>
                </a>
            </div>

            <div class="flex items-center justify-center mt-8 space-x-4">
                <span class="text-xs text-gray-500 bg-gray-100 px-3 py-1.5 rounded-full flex items-center">
                    <svg class="w-3 h-3 mr-1 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="1.5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
                    </svg>
                    Powered by Amigo Intelligence
                </span>

                <button id="customize-dashboard-btn" class="text-xs text-blue-600 bg-blue-50 hover:bg-blue-100 px-3 py-1.5 rounded-full flex items-center transition-colors duration-200">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="1.5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    Personalizar Dashboard
                </button>
            </div>
        </div>
    </div>
</div>

<!-- KPIs Fixos (4) -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Vendas -->
    <div class="kpi-card bg-white rounded-lg p-6 border border-gray-200 hover:shadow-md transition-shadow duration-300" data-kpi="vendas_total">
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
                <svg class="w-5 h-5 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <div>
                    <p class="text-sm font-medium text-gray-600">Vendas</p>
                    <p class="text-xs text-gray-500">Total do período</p>
                </div>
            </div>
            <button class="analytics-btn text-blue-500 hover:text-blue-700 hover:bg-blue-50 p-1 rounded transition-colors duration-200"
                    data-kpi="vendas_total"
                    title="Ver Analítico">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
            </button>
        </div>
        <div class="flex items-end justify-between">
            <div>
                <p class="text-2xl font-bold text-gray-900">R$ {{ "{:,.2f}".format(resumo.vendas_total|default(0)) }}</p>
                <div class="flex items-center mt-1">
                    <svg class="w-3 h-3 text-green-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                    </svg>
                    <span class="text-xs text-green-600">+12.5% vs mês anterior</span>
                </div>
            </div>
        </div>

        <!-- Seção Analítica (inicialmente oculta) -->
        <div class="analytics-section hidden mt-4 pt-4 border-t border-gray-200">
            <div class="space-y-2">
                <div class="text-xs font-medium text-gray-700 mb-2">Detalhamento:</div>
                <div class="analytics-content">
                    <!-- Conteúdo será carregado dinamicamente -->
                </div>
            </div>
        </div>
    </div>

    <!-- Produção -->
    <div class="kpi-card bg-white rounded-lg p-6 border border-gray-200 hover:shadow-md transition-shadow duration-300" data-kpi="producao_total">
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
                <svg class="w-5 h-5 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <div>
                    <p class="text-sm font-medium text-gray-600">Produção</p>
                    <p class="text-xs text-gray-500">Custos do período</p>
                </div>
            </div>
            <button class="analytics-btn text-blue-500 hover:text-blue-700 hover:bg-blue-50 p-1 rounded transition-colors duration-200"
                    data-kpi="producao_total"
                    title="Ver Analítico">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
            </button>
        </div>
        <div class="flex items-end justify-between">
            <div>
                <p class="text-2xl font-bold text-gray-900">R$ {{ "{:,.2f}".format(resumo.producao_total|default(0)) }}</p>
                <div class="flex items-center mt-1">
                    <svg class="w-3 h-3 text-blue-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                    </svg>
                    <span class="text-xs text-blue-600">+8.3% vs mês anterior</span>
                </div>
            </div>
        </div>

        <!-- Seção Analítica (inicialmente oculta) -->
        <div class="analytics-section hidden mt-4 pt-4 border-t border-gray-200">
            <div class="space-y-2">
                <div class="text-xs font-medium text-gray-700 mb-2">Detalhamento:</div>
                <div class="analytics-content">
                    <!-- Conteúdo será carregado dinamicamente -->
                </div>
            </div>
        </div>
    </div>

    <!-- Agendamentos -->
    <div class="kpi-card bg-white rounded-lg p-6 border border-gray-200 hover:shadow-md transition-shadow duration-300" data-kpi="agendamentos_mes_corrente">
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
                <svg class="w-5 h-5 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                <div>
                    <p class="text-sm font-medium text-gray-600">Agendamentos</p>
                    <p class="text-xs text-gray-500">Slots do mês</p>
                </div>
            </div>
            <button class="analytics-btn text-blue-500 hover:text-blue-700 hover:bg-blue-50 p-1 rounded transition-colors duration-200"
                    data-kpi="agendamentos_mes_corrente"
                    title="Ver Analítico">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
            </button>
        </div>
        <div class="flex items-end justify-between">
            <div>
                <p class="text-2xl font-bold text-gray-900">{{ resumo.agendamentos_mes_corrente|default(0) }}</p>
                <div class="flex items-center mt-1">
                    <svg class="w-3 h-3 text-green-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                    </svg>
                    <span class="text-xs text-green-600">+15.2% vs mês anterior</span>
                </div>
            </div>
        </div>

        <!-- Seção Analítica (inicialmente oculta) -->
        <div class="analytics-section hidden mt-4 pt-4 border-t border-gray-200">
            <div class="space-y-2">
                <div class="text-xs font-medium text-gray-700 mb-2">Detalhamento:</div>
                <div class="analytics-content">
                    <!-- Conteúdo será carregado dinamicamente -->
                </div>
            </div>
        </div>
    </div>

    <!-- Atendimentos -->
    <div class="kpi-card bg-white rounded-lg p-6 border border-gray-200 hover:shadow-md transition-shadow duration-300" data-kpi="atendimentos_mes_corrente">
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
                <svg class="w-5 h-5 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
                <div>
                    <p class="text-sm font-medium text-gray-600">Atendimentos</p>
                    <p class="text-xs text-gray-500">Realizados no mês</p>
                </div>
            </div>
            <button class="analytics-btn text-blue-500 hover:text-blue-700 hover:bg-blue-50 p-1 rounded transition-colors duration-200"
                    data-kpi="atendimentos_mes_corrente"
                    title="Ver Analítico">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
            </button>
        </div>
        <div class="flex items-end justify-between">
            <div>
                <p class="text-2xl font-bold text-gray-900">{{ resumo.atendimentos_mes_corrente|default(0) }}</p>
                <div class="flex items-center mt-1">
                    <svg class="w-3 h-3 text-green-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                    </svg>
                    <span class="text-xs text-green-600">+9.7% vs mês anterior</span>
                </div>
            </div>
        </div>

        <!-- Seção Analítica (inicialmente oculta) -->
        <div class="analytics-section hidden mt-4 pt-4 border-t border-gray-200">
            <div class="space-y-2">
                <div class="text-xs font-medium text-gray-700 mb-2">Detalhamento:</div>
                <div class="analytics-content">
                    <!-- Conteúdo será carregado dinamicamente -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- KPIs Opcionais Selecionados -->
<div id="optional-kpis-section" class="mb-8">
    <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900">Métricas Adicionais</h3>
        <span class="text-xs text-gray-500">Selecione até 12 métricas (3 fileiras)</span>
    </div>
    <div id="optional-kpis-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 min-h-[100px]">
        <!-- KPIs opcionais serão carregados dinamicamente pelo JavaScript -->
        <div class="col-span-full text-center py-8 text-gray-500">
            <svg class="w-8 h-8 mx-auto mb-2 text-gray-300 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            <p class="text-sm">Carregando métricas...</p>
        </div>
    </div>
</div>

<!-- Seção Principal: Gráfico Y/Y + Insights da IA -->
<div class="flex flex-col lg:flex-row gap-6 mb-8">
    <!-- Gráfico de Evolução de Vendas - 3/4 do espaço -->
    <div class="flex-1 lg:w-3/4">
        <div class="bg-white rounded-lg p-6 border border-gray-200 hover:shadow-md transition-shadow duration-300 h-full">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-1">Evolução de Vendas</h3>
                    <p class="text-sm text-gray-600">Comparação Year-over-Year com média móvel</p>
                </div>
                <div class="flex items-center space-x-2 mt-3 sm:mt-0">
                    <div class="flex items-center space-x-2 text-xs">
                        <button id="toggle-2023" class="legend-toggle active flex items-center px-2 py-1 rounded-md border border-gray-300 bg-white hover:bg-gray-50 transition-colors duration-200" data-line="2023">
                            <div class="w-3 h-3 bg-gray-500 rounded-full mr-2"></div>
                            <span class="text-gray-700 font-medium">2023</span>
                        </button>
                        <button id="toggle-2024" class="legend-toggle active flex items-center px-2 py-1 rounded-md border border-gray-300 bg-white hover:bg-gray-50 transition-colors duration-200" data-line="2024">
                            <div class="w-3 h-3 bg-blue-600 rounded-full mr-2"></div>
                            <span class="text-gray-700 font-medium">2024</span>
                        </button>
                        <button id="toggle-2025" class="legend-toggle active flex items-center px-2 py-1 rounded-md border border-gray-300 bg-white hover:bg-gray-50 transition-colors duration-200" data-line="2025">
                            <div class="w-3 h-3 rounded-full mr-2" style="background-color: #003D82;"></div>
                            <span class="text-gray-700 font-medium">2025</span>
                        </button>
                        <button id="toggle-moving-avg" class="legend-toggle active flex items-center px-2 py-1 rounded-md border border-gray-300 bg-white hover:bg-gray-50 transition-colors duration-200" data-line="moving-avg">
                            <div class="w-3 h-3 border-2 border-blue-600 rounded-full mr-2"></div>
                            <span class="text-gray-700 font-medium">Média Móvel</span>
                        </button>
                        <button id="toggle-global-trend" class="legend-toggle active flex items-center px-2 py-1 rounded-md border border-gray-300 bg-white hover:bg-gray-50 transition-colors duration-200" data-line="global-trend">
                            <div class="w-3 h-3 border-2 rounded-full mr-2" style="border-color: #FF6B35; border-style: dashed;"></div>
                            <span class="text-gray-700 font-medium">Tendência Global</span>
                        </button>
                    </div>
                </div>
            </div>

            <div class="relative">
                <div id="yoy-sales-chart" class="w-full" style="height: 400px;"></div>
                <div id="chart-tooltip" class="absolute bg-white border border-gray-200 shadow-lg text-gray-800 text-sm rounded-xl px-4 py-3 pointer-events-none opacity-0 transition-all duration-300 ease-out z-20 min-w-48" style="transform: scale(0.95);">
                    <div id="tooltip-content"></div>
                    <div class="absolute w-3 h-3 bg-white border-l border-b border-gray-200 transform rotate-45 -bottom-1.5 left-1/2 -translate-x-1/2"></div>
                </div>
            </div>

            <div class="mt-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
                <!-- Card Crescimento Y/Y -->
                <div class="bg-white rounded-lg p-3 border border-gray-200 hover:shadow-md transition-all duration-300">
                    <div class="flex items-center justify-between mb-2">
                        <div class="flex items-center">
                            <div class="w-6 h-6 bg-blue-50 rounded-full flex items-center justify-center mr-2">
                                <svg class="w-3 h-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                </svg>
                            </div>
                            <span class="text-xs font-medium text-gray-600">Crescimento Y/Y</span>
                        </div>
                        <div class="flex items-center bg-blue-50 px-1.5 py-0.5 rounded-full">
                            <svg class="w-2.5 h-2.5 text-blue-600 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                            </svg>
                            <span class="text-xs font-medium text-blue-600" id="growth-delta">+2.1%</span>
                        </div>
                    </div>
                    <div class="flex items-end justify-between">
                        <div>
                            <p class="text-lg font-bold text-gray-800" id="yoy-growth">+12.9%</p>
                            <p class="text-xs text-gray-500">vs 2023</p>
                        </div>
                        <div class="w-12 h-6">
                            <canvas id="growth-mini-chart" class="w-full h-full"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Card Melhor Mês -->
                <div class="bg-white rounded-lg p-3 border border-gray-200 hover:shadow-md transition-all duration-300">
                    <div class="flex items-center justify-between mb-2">
                        <div class="flex items-center">
                            <div class="w-6 h-6 bg-gray-50 rounded-full flex items-center justify-center mr-2">
                                <svg class="w-3 h-3 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                                </svg>
                            </div>
                            <span class="text-xs font-medium text-gray-600">Melhor Mês 2024</span>
                        </div>
                        <div class="flex items-center bg-gray-50 px-1.5 py-0.5 rounded-full">
                            <span class="text-xs font-medium text-gray-600" id="peak-value">R$ 118K</span>
                        </div>
                    </div>
                    <div class="flex items-end justify-between">
                        <div>
                            <p class="text-lg font-bold text-gray-800" id="best-month">Out</p>
                            <p class="text-xs text-gray-500">Pico de vendas</p>
                        </div>
                        <div class="w-12 h-6">
                            <canvas id="peak-mini-chart" class="w-full h-full"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Card Média 2024 -->
                <div class="bg-white rounded-lg p-3 border border-gray-200 hover:shadow-md transition-all duration-300">
                    <div class="flex items-center justify-between mb-2">
                        <div class="flex items-center">
                            <div class="w-6 h-6 bg-blue-50 rounded-full flex items-center justify-center mr-2">
                                <svg class="w-3 h-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                            </div>
                            <span class="text-xs font-medium text-gray-600">Média 2024</span>
                        </div>
                        <div class="flex items-center bg-blue-50 px-1.5 py-0.5 rounded-full">
                            <svg class="w-2.5 h-2.5 text-blue-600 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                            </svg>
                            <span class="text-xs font-medium text-blue-600" id="avg-delta">+8.5%</span>
                        </div>
                    </div>
                    <div class="flex items-end justify-between">
                        <div>
                            <p class="text-lg font-bold text-gray-800" id="avg-2024">R$ 102K</p>
                            <p class="text-xs text-gray-500">Mensal</p>
                        </div>
                        <div class="w-12 h-6">
                            <canvas id="avg-mini-chart" class="w-full h-full"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Card Tendência -->
                <div class="bg-white rounded-lg p-3 border border-gray-200 hover:shadow-md transition-all duration-300">
                    <div class="flex items-center justify-between mb-2">
                        <div class="flex items-center">
                            <div class="w-6 h-6 bg-gray-50 rounded-full flex items-center justify-center mr-2">
                                <svg class="w-3 h-3 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"></path>
                                </svg>
                            </div>
                            <span class="text-xs font-medium text-gray-600">Tendência</span>
                        </div>
                        <div class="flex items-center bg-gray-50 px-1.5 py-0.5 rounded-full">
                            <svg class="w-2.5 h-2.5 text-gray-600 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                            </svg>
                            <span class="text-xs font-medium text-gray-600" id="trend-strength">Forte</span>
                        </div>
                    </div>
                    <div class="flex items-end justify-between">
                        <div>
                            <p class="text-lg font-bold text-gray-800" id="trend">↗ Crescente</p>
                            <p class="text-xs text-gray-500">Últimos 6 meses</p>
                        </div>
                        <div class="w-12 h-6">
                            <canvas id="trend-mini-chart" class="w-full h-full"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Insights da IA - Máximo aproveitamento -->
    <div class="lg:w-1/4 w-full">
        <div class="bg-white rounded-lg p-2 border border-gray-200 h-full">
            <div class="flex items-center justify-between mb-2">
                <div class="flex items-center">
                    <svg class="w-3.5 h-3.5 text-blue-500 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="1.5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
                    </svg>
                    <h3 class="text-xs font-medium text-gray-700">Amigo Intelligence</h3>
                </div>
                <span class="text-xs text-gray-500 bg-gray-100 px-1.5 py-0.5 rounded">Live</span>
            </div>

            <!-- Container com scroll maximizado -->
            <div id="insights-container-vendas-sidebar" class="space-y-1.5 overflow-y-auto" style="height: calc(100% - 32px); max-height: 520px;">
                <!-- Insights estáticos aprimorados -->
                <div class="bg-gray-50 rounded-lg p-2.5 border border-gray-200 hover:bg-gray-100 hover:border-gray-300 transition-all duration-200 shadow-sm">
                    <div class="flex items-start justify-between mb-1.5">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 text-blue-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="1.5">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
                            </svg>
                            <h4 class="text-xs font-semibold text-gray-900 leading-tight">Padrão de Crescimento</h4>
                        </div>
                        <span class="bg-gray-300 text-gray-700 text-xs px-1 py-0.5 rounded">Tendência</span>
                    </div>
                    <p class="text-xs text-gray-600 mb-2 leading-relaxed">Vendas cresceram <span class="font-medium text-gray-800">12.9%</span> comparado ao ano anterior, com pico em outubro.</p>
                    <div class="bg-white rounded-md p-2 border border-gray-200 shadow-sm">
                        <div class="space-y-1.5">
                            <div class="flex items-start">
                                <div class="w-1.5 h-1.5 bg-gray-600 rounded-full mt-1 mr-2 flex-shrink-0"></div>
                                <span class="text-xs text-gray-700 leading-relaxed">Analise padrões de uso para otimizar recursos</span>
                            </div>
                            <div class="flex items-start">
                                <div class="w-1.5 h-1.5 bg-gray-600 rounded-full mt-1 mr-2 flex-shrink-0"></div>
                                <span class="text-xs text-gray-700 leading-relaxed">Implemente melhorias baseadas em dados</span>
                            </div>
                            <div class="flex items-start">
                                <div class="w-1.5 h-1.5 bg-gray-600 rounded-full mt-1 mr-2 flex-shrink-0"></div>
                                <span class="text-xs text-gray-700 leading-relaxed">Utilize correlações para decisões estratégicas</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-gray-50 rounded-lg p-2.5 border border-gray-200 hover:bg-gray-100 hover:border-gray-300 transition-all duration-200 shadow-sm">
                    <div class="flex items-start justify-between mb-1.5">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 text-blue-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="1.5">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
                            </svg>
                            <h4 class="text-xs font-semibold text-gray-900 leading-tight">Oportunidade Identificada</h4>
                        </div>
                        <span class="bg-gray-300 text-gray-700 text-xs px-1 py-0.5 rounded">Ação</span>
                    </div>
                    <p class="text-xs text-gray-600 mb-2 leading-relaxed">Horários de <span class="font-medium text-gray-800">14h-16h</span> têm 23% menos ocupação que a média.</p>
                    <div class="bg-white rounded-md p-2 border border-gray-200 shadow-sm">
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-gray-600">Potencial:</span>
                            <span class="text-xs font-bold text-gray-900">+R$ 45K</span>
                        </div>
                    </div>
                </div>

                <div class="bg-gray-50 rounded-lg p-2.5 border border-gray-200 hover:bg-gray-100 hover:border-gray-300 transition-all duration-200 shadow-sm">
                    <div class="flex items-start justify-between mb-1.5">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 text-blue-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="1.5">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
                            </svg>
                            <h4 class="text-xs font-semibold text-gray-900 leading-tight">Alerta de Performance</h4>
                        </div>
                        <span class="bg-gray-300 text-gray-700 text-xs px-1 py-0.5 rounded">Atenção</span>
                    </div>
                    <p class="text-xs text-gray-600 mb-2 leading-relaxed">Taxa de cancelamento aumentou <span class="font-medium text-gray-800">8%</span> nas últimas 2 semanas.</p>
                    <div class="bg-white rounded-md p-2 border border-gray-200 shadow-sm">
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-gray-600">Impacto:</span>
                            <span class="text-xs font-bold text-gray-900">-R$ 12K</span>
                        </div>
                    </div>
                </div>

                <!-- Placeholder para insights carregados dinamicamente -->
                <div id="dynamic-insights-vendas-placeholder" class="space-y-1.5"></div>
            </div>
        </div>
    </div>
</div>

<!-- Cards de Acompanhamento -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
    <!-- Leads por Origem -->
    <a href="http://localhost:5003/amigocare/leads" class="block bg-white rounded-lg p-6 border border-gray-200 hover:shadow-md hover:border-blue-300 transition-all duration-300 group min-h-[620px]">
        <div class="flex items-center justify-between mb-5">
            <h3 class="text-lg font-semibold text-gray-800 group-hover:text-blue-600 transition-colors duration-300">Leads por Origem</h3>
            <svg class="w-5 h-5 text-blue-600 group-hover:text-blue-700 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
            </svg>
        </div>

        <!-- Gráfico de Donut -->
        <div class="flex justify-center mb-6">
            <div style="width: 180px; height: 180px;">
                <canvas id="leadsOrigemChart"></canvas>
            </div>
        </div>

        <!-- KPIs Resumo -->
        <div class="grid grid-cols-3 gap-4 mb-6 p-4 bg-gray-50 rounded-lg">
            <div class="text-center">
                <div class="text-xl font-bold text-black">417</div>
                <div class="text-sm text-gray-600 font-medium">Total</div>
            </div>
            <div class="text-center">
                <div class="text-xl font-bold text-black">+1.4%</div>
                <div class="text-sm text-gray-600 font-medium">Crescimento</div>
            </div>
            <div class="text-center">
                <div class="text-xl font-bold text-black">8</div>
                <div class="text-sm text-gray-600 font-medium">Canais</div>
            </div>
        </div>

        <!-- Lista Detalhada -->
        <div>
            <!-- Headers da Tabela -->
            <div class="grid grid-cols-4 gap-1 mb-1 pb-1 border-b border-gray-200">
                <div class="text-xs font-medium text-gray-500">Canal</div>
                <div class="text-xs font-medium text-gray-500 text-center">TOTAL</div>
                <div class="text-xs font-medium text-gray-500 text-center">POOL</div>
                <div class="text-xs font-medium text-gray-500 text-center">%</div>
            </div>

            <!-- Dados da Tabela -->
            <div class="space-y-1 max-h-32 overflow-y-auto">
                <div class="grid grid-cols-4 gap-1 items-center py-0.5">
                    <div class="flex items-center">
                        <div class="w-1.5 h-1.5 bg-blue-600 rounded-full mr-1.5"></div>
                        <span class="text-xs font-medium text-gray-700 truncate">Google</span>
                    </div>
                    <div class="text-xs font-semibold text-gray-900 text-center">127</div>
                    <div class="text-xs text-gray-600 text-center">30.5%</div>
                    <div class="text-xs text-green-600 text-center">↗ +2.1%</div>
                </div>

                <div class="grid grid-cols-4 gap-1 items-center py-0.5">
                    <div class="flex items-center">
                        <div class="w-1.5 h-1.5 bg-blue-500 rounded-full mr-1.5"></div>
                        <span class="text-xs font-medium text-gray-700 truncate">Indicação</span>
                    </div>
                    <div class="text-xs font-semibold text-gray-900 text-center">123</div>
                    <div class="text-xs text-gray-600 text-center">29.5%</div>
                    <div class="text-xs text-green-600 text-center">↗ +1.8%</div>
                </div>

                <div class="grid grid-cols-4 gap-1 items-center py-0.5">
                    <div class="flex items-center">
                        <div class="w-1.5 h-1.5 bg-blue-400 rounded-full mr-1.5"></div>
                        <span class="text-xs font-medium text-gray-700 truncate">Site</span>
                    </div>
                    <div class="text-xs font-semibold text-gray-900 text-center">45</div>
                    <div class="text-xs text-gray-600 text-center">10.8%</div>
                    <div class="text-xs text-red-600 text-center">↘ -0.5%</div>
                </div>

                <div class="grid grid-cols-4 gap-1 items-center py-0.5">
                    <div class="flex items-center">
                        <div class="w-1.5 h-1.5 bg-blue-300 rounded-full mr-1.5"></div>
                        <span class="text-xs font-medium text-gray-700 truncate">Facebook</span>
                    </div>
                    <div class="text-xs font-semibold text-gray-900 text-center">38</div>
                    <div class="text-xs text-gray-600 text-center">9.1%</div>
                    <div class="text-xs text-green-600 text-center">↗ +3.2%</div>
                </div>

                <div class="grid grid-cols-4 gap-1 items-center py-0.5">
                    <div class="flex items-center">
                        <div class="w-1.5 h-1.5 bg-blue-200 rounded-full mr-1.5"></div>
                        <span class="text-xs font-medium text-gray-700 truncate">Instagram</span>
                    </div>
                    <div class="text-xs font-semibold text-gray-900 text-center">29</div>
                    <div class="text-xs text-gray-600 text-center">7.0%</div>
                    <div class="text-xs text-green-600 text-center">↗ +1.4%</div>
                </div>

                <div class="grid grid-cols-4 gap-1 items-center py-0.5">
                    <div class="flex items-center">
                        <div class="w-1.5 h-1.5 bg-gray-400 rounded-full mr-1.5"></div>
                        <span class="text-xs font-medium text-gray-700 truncate">WhatsApp</span>
                    </div>
                    <div class="text-xs font-semibold text-gray-900 text-center">22</div>
                    <div class="text-xs text-gray-600 text-center">5.3%</div>
                    <div class="text-xs text-red-600 text-center">↘ -1.1%</div>
                </div>

                <div class="grid grid-cols-4 gap-1 items-center py-0.5">
                    <div class="flex items-center">
                        <div class="w-1.5 h-1.5 bg-gray-300 rounded-full mr-1.5"></div>
                        <span class="text-xs font-medium text-gray-700 truncate">LinkedIn</span>
                    </div>
                    <div class="text-xs font-semibold text-gray-900 text-center">18</div>
                    <div class="text-xs text-gray-600 text-center">4.3%</div>
                    <div class="text-xs text-green-600 text-center">↗ +0.8%</div>
                </div>

                <div class="grid grid-cols-4 gap-1 items-center py-0.5">
                    <div class="flex items-center">
                        <div class="w-1.5 h-1.5 bg-gray-200 rounded-full mr-1.5"></div>
                        <span class="text-xs font-medium text-gray-700 truncate">Outros</span>
                    </div>
                    <div class="text-xs font-semibold text-gray-900 text-center">15</div>
                    <div class="text-xs text-gray-600 text-center">3.6%</div>
                    <div class="text-xs text-red-600 text-center">↘ -0.3%</div>
                </div>
            </div>
        </div>
    </a>

    <!-- Especialidades Mais Procuradas -->
    <div class="bg-white rounded-lg p-6 border border-gray-200 hover:shadow-md transition-shadow duration-300 min-h-[620px]">
        <div class="flex items-center justify-between mb-5">
            <h3 class="text-lg font-semibold text-gray-800">Especialidades</h3>
            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
            </svg>
        </div>

        <!-- Gráfico de Donut -->
        <div class="flex justify-center mb-6">
            <div style="width: 180px; height: 180px;">
                <canvas id="especialidadesChart"></canvas>
            </div>
        </div>

        <!-- KPIs Resumo -->
        <div class="grid grid-cols-3 gap-4 mb-6 p-4 bg-gray-50 rounded-lg">
            <div class="text-center">
                <div class="text-xl font-bold text-black">1,331</div>
                <div class="text-sm text-gray-600 font-medium">Total</div>
            </div>
            <div class="text-center">
                <div class="text-xl font-bold text-black">+1.9%</div>
                <div class="text-sm text-gray-600 font-medium">Crescimento</div>
            </div>
            <div class="text-center">
                <div class="text-xl font-bold text-black">8</div>
                <div class="text-sm text-gray-600 font-medium">Especialidades</div>
            </div>
        </div>

        <!-- Lista Detalhada -->
        <div>
            <!-- Headers da Tabela -->
            <div class="grid grid-cols-4 gap-1 mb-1 pb-1 border-b border-gray-200">
                <div class="text-xs font-medium text-gray-500">Especialidade</div>
                <div class="text-xs font-medium text-gray-500 text-center">TOTAL</div>
                <div class="text-xs font-medium text-gray-500 text-center">POOL</div>
                <div class="text-xs font-medium text-gray-500 text-center">%</div>
            </div>

            <!-- Dados da Tabela -->
            <div class="space-y-1 max-h-32 overflow-y-auto">
                <div class="grid grid-cols-4 gap-1 items-center py-0.5">
                    <div class="flex items-center">
                        <div class="w-1.5 h-1.5 bg-blue-600 rounded-full mr-1.5"></div>
                        <span class="text-xs font-medium text-gray-700 truncate">Cardiologia</span>
                    </div>
                    <div class="text-xs font-semibold text-gray-900 text-center">284</div>
                    <div class="text-xs text-gray-600 text-center">21.3%</div>
                    <div class="text-xs text-green-600 text-center">↗ +3.2%</div>
                </div>

                <div class="grid grid-cols-4 gap-1 items-center py-0.5">
                    <div class="flex items-center">
                        <div class="w-1.5 h-1.5 bg-blue-500 rounded-full mr-1.5"></div>
                        <span class="text-xs font-medium text-gray-700 truncate">Dermatologia</span>
                    </div>
                    <div class="text-xs font-semibold text-gray-900 text-center">221</div>
                    <div class="text-xs text-gray-600 text-center">16.6%</div>
                    <div class="text-xs text-green-600 text-center">↗ +1.5%</div>
                </div>

                <div class="grid grid-cols-4 gap-1 items-center py-0.5">
                    <div class="flex items-center">
                        <div class="w-1.5 h-1.5 bg-blue-400 rounded-full mr-1.5"></div>
                        <span class="text-xs font-medium text-gray-700 truncate">Ortopedia</span>
                    </div>
                    <div class="text-xs font-semibold text-gray-900 text-center">187</div>
                    <div class="text-xs text-gray-600 text-center">14.0%</div>
                    <div class="text-xs text-red-600 text-center">↘ -1.2%</div>
                </div>

                <div class="grid grid-cols-4 gap-1 items-center py-0.5">
                    <div class="flex items-center">
                        <div class="w-1.5 h-1.5 bg-blue-300 rounded-full mr-1.5"></div>
                        <span class="text-xs font-medium text-gray-700 truncate">Neurologia</span>
                    </div>
                    <div class="text-xs font-semibold text-gray-900 text-center">156</div>
                    <div class="text-xs text-gray-600 text-center">11.7%</div>
                    <div class="text-xs text-green-600 text-center">↗ +2.8%</div>
                </div>

                <div class="grid grid-cols-4 gap-1 items-center py-0.5">
                    <div class="flex items-center">
                        <div class="w-1.5 h-1.5 bg-blue-200 rounded-full mr-1.5"></div>
                        <span class="text-xs font-medium text-gray-700 truncate">Ginecologia</span>
                    </div>
                    <div class="text-xs font-semibold text-gray-900 text-center">142</div>
                    <div class="text-xs text-gray-600 text-center">10.7%</div>
                    <div class="text-xs text-green-600 text-center">↗ +1.9%</div>
                </div>

                <div class="grid grid-cols-4 gap-1 items-center py-0.5">
                    <div class="flex items-center">
                        <div class="w-1.5 h-1.5 bg-gray-400 rounded-full mr-1.5"></div>
                        <span class="text-xs font-medium text-gray-700 truncate">Pediatria</span>
                    </div>
                    <div class="text-xs font-semibold text-gray-900 text-center">128</div>
                    <div class="text-xs text-gray-600 text-center">9.6%</div>
                    <div class="text-xs text-red-600 text-center">↘ -0.8%</div>
                </div>

                <div class="grid grid-cols-4 gap-1 items-center py-0.5">
                    <div class="flex items-center">
                        <div class="w-1.5 h-1.5 bg-gray-300 rounded-full mr-1.5"></div>
                        <span class="text-xs font-medium text-gray-700 truncate">Oftalmologia</span>
                    </div>
                    <div class="text-xs font-semibold text-gray-900 text-center">115</div>
                    <div class="text-xs text-gray-600 text-center">8.6%</div>
                    <div class="text-xs text-green-600 text-center">↗ +0.6%</div>
                </div>

                <div class="grid grid-cols-4 gap-1 items-center py-0.5">
                    <div class="flex items-center">
                        <div class="w-1.5 h-1.5 bg-gray-200 rounded-full mr-1.5"></div>
                        <span class="text-xs font-medium text-gray-700 truncate">Psiquiatria</span>
                    </div>
                    <div class="text-xs font-semibold text-gray-900 text-center">98</div>
                    <div class="text-xs text-gray-600 text-center">7.4%</div>
                    <div class="text-xs text-green-600 text-center">↗ +4.1%</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance de Médicos -->
    <div class="bg-white rounded-lg p-6 border border-gray-200 hover:shadow-md transition-shadow duration-300 min-h-[620px]">
        <div class="flex items-center justify-between mb-5">
            <h3 class="text-lg font-semibold text-gray-800">Performance Médicos</h3>
            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
        </div>

        <!-- Gráfico de Barras Horizontais -->
        <div class="mb-6 px-2">
            <div style="width: 100%; height: 220px;">
                <canvas id="performanceMedicosChart"></canvas>
            </div>
        </div>

        <!-- KPIs Resumo -->
        <div class="grid grid-cols-3 gap-4 mb-6 p-4 bg-gray-50 rounded-lg">
            <div class="text-center">
                <div class="text-xl font-bold text-black">R$ 225K</div>
                <div class="text-sm text-gray-600 font-medium">Total</div>
            </div>
            <div class="text-center">
                <div class="text-xl font-bold text-black">+2.1%</div>
                <div class="text-sm text-gray-600 font-medium">Crescimento</div>
            </div>
            <div class="text-center">
                <div class="text-xl font-bold text-black">8</div>
                <div class="text-sm text-gray-600 font-medium">Médicos</div>
            </div>
        </div>

        <!-- Lista Detalhada -->
        <div>
            <!-- Headers da Tabela -->
            <div class="grid grid-cols-4 gap-1 mb-1 pb-1 border-b border-gray-200">
                <div class="text-xs font-medium text-gray-500">Médico</div>
                <div class="text-xs font-medium text-gray-500 text-center">TOTAL</div>
                <div class="text-xs font-medium text-gray-500 text-center">POOL</div>
                <div class="text-xs font-medium text-gray-500 text-center">%</div>
            </div>

            <!-- Dados da Tabela -->
            <div class="space-y-1 max-h-32 overflow-y-auto">
                <div class="grid grid-cols-4 gap-1 items-center py-0.5">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-yellow-100 rounded flex items-center justify-center mr-1.5">
                            <span class="text-xs font-bold text-yellow-700">1</span>
                        </div>
                        <span class="text-xs font-medium text-gray-700 truncate">Dr. Silva</span>
                    </div>
                    <div class="text-xs font-semibold text-gray-900 text-center">R$ 45K</div>
                    <div class="text-xs text-gray-600 text-center">20.0%</div>
                    <div class="text-xs text-green-600 text-center">↗ +8.3%</div>
                </div>

                <div class="grid grid-cols-4 gap-1 items-center py-0.5">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-gray-100 rounded flex items-center justify-center mr-1.5">
                            <span class="text-xs font-bold text-gray-700">2</span>
                        </div>
                        <span class="text-xs font-medium text-gray-700 truncate">Dr. Santos</span>
                    </div>
                    <div class="text-xs font-semibold text-gray-900 text-center">R$ 38K</div>
                    <div class="text-xs text-gray-600 text-center">16.9%</div>
                    <div class="text-xs text-green-600 text-center">↗ +5.1%</div>
                </div>

                <div class="grid grid-cols-4 gap-1 items-center py-0.5">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-orange-100 rounded flex items-center justify-center mr-1.5">
                            <span class="text-xs font-bold text-orange-700">3</span>
                        </div>
                        <span class="text-xs font-medium text-gray-700 truncate">Dr. Costa</span>
                    </div>
                    <div class="text-xs font-semibold text-gray-900 text-center">R$ 32K</div>
                    <div class="text-xs text-gray-600 text-center">14.2%</div>
                    <div class="text-xs text-red-600 text-center">↘ -2.4%</div>
                </div>

                <div class="grid grid-cols-4 gap-1 items-center py-0.5">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-blue-100 rounded flex items-center justify-center mr-1.5">
                            <span class="text-xs font-bold text-blue-700">4</span>
                        </div>
                        <span class="text-xs font-medium text-gray-700 truncate">Dr. Oliveira</span>
                    </div>
                    <div class="text-xs font-semibold text-gray-900 text-center">R$ 28K</div>
                    <div class="text-xs text-gray-600 text-center">12.4%</div>
                    <div class="text-xs text-green-600 text-center">↗ +3.7%</div>
                </div>

                <div class="grid grid-cols-4 gap-1 items-center py-0.5">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-green-100 rounded flex items-center justify-center mr-1.5">
                            <span class="text-xs font-bold text-green-700">5</span>
                        </div>
                        <span class="text-xs font-medium text-gray-700 truncate">Dr. Pereira</span>
                    </div>
                    <div class="text-xs font-semibold text-gray-900 text-center">R$ 25K</div>
                    <div class="text-xs text-gray-600 text-center">11.1%</div>
                    <div class="text-xs text-green-600 text-center">↗ +2.1%</div>
                </div>

                <div class="grid grid-cols-4 gap-1 items-center py-0.5">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-purple-100 rounded flex items-center justify-center mr-1.5">
                            <span class="text-xs font-bold text-purple-700">6</span>
                        </div>
                        <span class="text-xs font-medium text-gray-700 truncate">Dr. Lima</span>
                    </div>
                    <div class="text-xs font-semibold text-gray-900 text-center">R$ 22K</div>
                    <div class="text-xs text-gray-600 text-center">9.8%</div>
                    <div class="text-xs text-red-600 text-center">↘ -1.5%</div>
                </div>

                <div class="grid grid-cols-4 gap-1 items-center py-0.5">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-pink-100 rounded flex items-center justify-center mr-1.5">
                            <span class="text-xs font-bold text-pink-700">7</span>
                        </div>
                        <span class="text-xs font-medium text-gray-700 truncate">Dr. Almeida</span>
                    </div>
                    <div class="text-xs font-semibold text-gray-900 text-center">R$ 19K</div>
                    <div class="text-xs text-gray-600 text-center">8.4%</div>
                    <div class="text-xs text-green-600 text-center">↗ +1.8%</div>
                </div>

                <div class="grid grid-cols-4 gap-1 items-center py-0.5">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-indigo-100 rounded flex items-center justify-center mr-1.5">
                            <span class="text-xs font-bold text-indigo-700">8</span>
                        </div>
                        <span class="text-xs font-medium text-gray-700 truncate">Dr. Ferreira</span>
                    </div>
                    <div class="text-xs font-semibold text-gray-900 text-center">R$ 16K</div>
                    <div class="text-xs text-gray-600 text-center">7.1%</div>
                    <div class="text-xs text-red-600 text-center">↘ -0.9%</div>
                </div>
            </div>
        </div>
    </div>
</div>














{% endblock %}

{% block scripts %}
<script>
    // Dados para os gráficos
    window.pageData = {
        faturamento_por_mes: {
            {% if resumo.faturamento_por_mes is defined %}
                {% for month, value in resumo.faturamento_por_mes.items() %}
                    "{{ month }}": {{ value }}{% if not loop.last %},{% endif %}
                {% endfor %}
            {% endif %}
        },
        etapas_funil: [
            {% if resumo.etapas_funil is defined %}
                {% for etapa in resumo.etapas_funil %}
                    {
                        "etapa": "{{ etapa.etapa }}",
                        "quantidade": {{ etapa.quantidade }}
                    }{% if not loop.last %},{% endif %}
                {% endfor %}
            {% endif %}
        ],
        distribuicao_especialidades: [
            {% if resumo.distribuicao_especialidades is defined %}
                {% for esp in resumo.distribuicao_especialidades %}
                    {
                        "especialidade": "{{ esp.especialidade }}",
                        "quantidade": {{ esp.quantidade }}
                    }{% if not loop.last %},{% endif %}
                {% endfor %}
            {% endif %}
        ],
        nps_por_mes: {
            {% if resumo.nps_por_mes is defined %}
                {% for month, value in resumo.nps_por_mes.items() %}
                    "{{ month }}": {{ value }}{% if not loop.last %},{% endif %}
                {% endfor %}
            {% endif %}
        },
        // Manter dados originais para compatibilidade
        leads_por_mes: {
            {% if resumo.leads_por_mes is defined %}
                {% for month, count in resumo.leads_por_mes.items() %}
                    "{{ month }}": {{ count }}{% if not loop.last %},{% endif %}
                {% endfor %}
            {% endif %}
        },
        distribuicao_areas: [
            {% if resumo.distribuicao_areas is defined %}
                {% for area in resumo.distribuicao_areas %}
                    {
                        "area": "{{ area.area }}",
                        "quantidade": {{ area.quantidade }}
                    }{% if not loop.last %},{% endif %}
                {% endfor %}
            {% endif %}
        ],
        distribuicao_origens: [
            {% if resumo.distribuicao_origens is defined %}
                {% for origem in resumo.distribuicao_origens %}
                    {
                        "origem": "{{ origem.origem }}",
                        "quantidade": {{ origem.quantidade }}
                    }{% if not loop.last %},{% endif %}
                {% endfor %}
            {% endif %}
        ]
    };

    // Inicializar os gráficos
    document.addEventListener('DOMContentLoaded', function() {
        // Gráfico de Evolução do Faturamento com Comparações
        let faturamentoChart = null;

        function createFaturamentoChart(comparisonType = 'none') {
            if (document.getElementById('faturamentoChart')) {
                const faturamentoCtx = document.getElementById('faturamentoChart').getContext('2d');

                // Destruir gráfico anterior se existir
                if (faturamentoChart) {
                    faturamentoChart.destroy();
                }

                // Dados base
                const currentData = Object.values(window.pageData?.faturamento_por_mes || {});
                const labels = Object.keys(window.pageData?.faturamento_por_mes || {});

                let datasets = [{
                    label: 'Faturamento Atual',
                    data: currentData,
                    backgroundColor: 'rgba(0, 122, 255, 0.1)',
                    borderColor: 'rgba(0, 122, 255, 1)',
                    borderWidth: 3,
                    tension: 0.4,
                    fill: true
                }];

                // Adicionar dados de comparação baseado no tipo
                if (comparisonType === 'yoy') {
                    // Simular dados do ano anterior (reduzir 15-25%)
                    const previousYearData = currentData.map(value => value * (0.75 + Math.random() * 0.1));
                    datasets.push({
                        label: 'Ano Anterior',
                        data: previousYearData,
                        backgroundColor: 'rgba(156, 163, 175, 0.1)',
                        borderColor: 'rgba(156, 163, 175, 1)',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: false,
                        borderDash: [5, 5]
                    });
                } else if (comparisonType === 'mom') {
                    // Simular dados do mês anterior (pequenas variações)
                    const previousMonthData = currentData.map(value => value * (0.92 + Math.random() * 0.16));
                    datasets.push({
                        label: 'Período Anterior',
                        data: previousMonthData,
                        backgroundColor: 'rgba(34, 197, 94, 0.1)',
                        borderColor: 'rgba(34, 197, 94, 1)',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: false,
                        borderDash: [3, 3]
                    });
                } else if (comparisonType === 'dod') {
                    // Simular dados diários (mais granular)
                    const dailyData = currentData.map(value => value * (0.95 + Math.random() * 0.1));
                    datasets.push({
                        label: 'Dia Anterior',
                        data: dailyData,
                        backgroundColor: 'rgba(168, 85, 247, 0.1)',
                        borderColor: 'rgba(168, 85, 247, 1)',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: false,
                        borderDash: [2, 2]
                    });
                }

                faturamentoChart = new Chart(faturamentoCtx, {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: comparisonType !== 'none',
                                position: 'top',
                                labels: {
                                    usePointStyle: true,
                                    padding: 20
                                }
                            },
                            tooltip: {
                                mode: 'index',
                                intersect: false,
                                callbacks: {
                                    label: function(context) {
                                        return context.dataset.label + ': R$ ' + (context.parsed.y/1000).toFixed(1) + 'K';
                                    }
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    display: true,
                                    color: 'rgba(0, 0, 0, 0.05)'
                                },
                                ticks: {
                                    callback: function(value) {
                                        return 'R$ ' + (value/1000) + 'K';
                                    }
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                }
                            }
                        },
                        interaction: {
                            mode: 'nearest',
                            axis: 'x',
                            intersect: false
                        }
                    }
                });
            }
        }

        // Inicializar gráfico
        createFaturamentoChart();

        // Event listeners para comparações
        document.getElementById('comparison-type')?.addEventListener('change', function(e) {
            createFaturamentoChart(e.target.value);
        });

        document.getElementById('refresh-chart')?.addEventListener('click', function() {
            const comparisonType = document.getElementById('comparison-type').value;
            createFaturamentoChart(comparisonType);
        });

        // Gráfico de Funil de Vendas
        if (document.getElementById('funilChart')) {
            const funilCtx = document.getElementById('funilChart').getContext('2d');
            const etapas = window.pageData?.etapas_funil || [];

            new Chart(funilCtx, {
                type: 'bar',
                data: {
                    labels: etapas.map(etapa => etapa.etapa),
                    datasets: [{
                        label: 'Quantidade',
                        data: etapas.map(etapa => etapa.quantidade),
                        backgroundColor: [
                            'rgba(0, 122, 255, 0.9)',
                            'rgba(0, 122, 255, 0.8)',
                            'rgba(0, 122, 255, 0.7)',
                            'rgba(0, 122, 255, 0.6)',
                            'rgba(0, 122, 255, 0.5)',
                            'rgba(0, 122, 255, 0.4)'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: 'y',
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            beginAtZero: true,
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        // Gráfico de Distribuição por Especialidade
        if (document.getElementById('especialidadesChart')) {
            const especialidadesCtx = document.getElementById('especialidadesChart').getContext('2d');
            const especialidades = window.pageData?.distribuicao_especialidades || [];

            new Chart(especialidadesCtx, {
                type: 'doughnut',
                data: {
                    labels: especialidades.map(esp => esp.especialidade),
                    datasets: [{
                        label: 'Quantidade',
                        data: especialidades.map(esp => esp.quantidade),
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.9)', // blue-500
                            'rgba(96, 165, 250, 0.85)', // blue-400
                            'rgba(147, 197, 253, 0.8)', // blue-300
                            'rgba(191, 219, 254, 0.75)', // blue-200
                            'rgba(37, 99, 235, 0.9)', // blue-600
                            'rgba(29, 78, 216, 0.85)', // blue-700
                            'rgba(30, 64, 175, 0.8)', // blue-800
                            'rgba(219, 234, 254, 0.75)' // blue-100
                        ],
                        borderWidth: 2,
                        borderColor: 'white',
                        hoverOffset: 15,
                        borderRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: '65%',
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                color: '#333',
                                font: {
                                    size: 11,
                                    family: "'Inter', sans-serif"
                                },
                                padding: 15,
                                usePointStyle: true,
                                pointStyle: 'circle'
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(255, 255, 255, 0.9)',
                            titleColor: '#333',
                            bodyColor: '#333',
                            bodyFont: {
                                size: 12
                            },
                            titleFont: {
                                size: 14,
                                weight: 'bold'
                            },
                            padding: 12,
                            boxPadding: 8,
                            borderColor: 'rgba(0, 0, 0, 0.1)',
                            borderWidth: 1,
                            displayColors: true,
                            boxWidth: 8,
                            boxHeight: 8,
                            usePointStyle: true,
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = context.dataset.data.reduce((acc, val) => acc + val, 0);
                                    const percentage = Math.round((value / total) * 100);
                                    return `${label}: ${value} (${percentage}%)`;
                                }
                            }
                        }
                    },
                    animation: {
                        animateScale: true,
                        animateRotate: true
                    }
                }
            });
        }

        // Gráfico de Evolução do NPS
        if (document.getElementById('npsChart')) {
            const npsCtx = document.getElementById('npsChart').getContext('2d');

            new Chart(npsCtx, {
                type: 'line',
                data: {
                    labels: Object.keys(window.pageData?.nps_por_mes || {}),
                    datasets: [{
                        label: 'NPS',
                        data: Object.values(window.pageData?.nps_por_mes || {}),
                        backgroundColor: 'rgba(191, 219, 254, 0.5)', // blue-200 com opacidade
                        borderColor: 'rgba(59, 130, 246, 0.8)', // blue-500 com opacidade
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: 'rgba(96, 165, 250, 1)', // blue-400
                        pointBorderColor: '#fff',
                        pointRadius: 4,
                        pointHoverRadius: 6,
                        pointHoverBackgroundColor: 'rgba(37, 99, 235, 1)', // blue-600
                        pointHoverBorderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: 'rgba(255, 255, 255, 0.9)',
                            titleColor: '#333',
                            bodyColor: '#333',
                            bodyFont: {
                                size: 12
                            },
                            titleFont: {
                                size: 14,
                                weight: 'bold'
                            },
                            padding: 12,
                            boxPadding: 8,
                            borderColor: 'rgba(0, 0, 0, 0.1)',
                            borderWidth: 1,
                            displayColors: true,
                            boxWidth: 8,
                            boxHeight: 8,
                            usePointStyle: true
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            grid: {
                                display: true,
                                color: 'rgba(226, 232, 240, 0.5)' // Linhas de grade sutis
                            },
                            ticks: {
                                color: '#64748b' // slate-500
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                color: '#64748b' // slate-500
                            }
                        }
                    }
                }
            });
        }

        // Gráficos de Donut para os Cards de Infográficos

        // Gráfico de Leads por Origem
        if (document.getElementById('leadsOrigemChart')) {
            // Destruir gráfico existente se houver
            if (window.leadsChart && typeof window.leadsChart.destroy === 'function') {
                try {
                    window.leadsChart.destroy();
                } catch (error) {
                    console.warn('Erro ao destruir gráfico de leads:', error);
                }
            }

            // Limpar canvas completamente
            const canvas = document.getElementById('leadsOrigemChart');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Remover qualquer instância Chart.js associada ao canvas
            if (Chart.getChart && Chart.getChart(canvas)) {
                Chart.getChart(canvas).destroy();
            }

            const leadsCtx = canvas.getContext('2d');
            window.leadsChart = new Chart(leadsCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Google', 'Indicação', 'Site', 'Facebook', 'Instagram', 'WhatsApp', 'LinkedIn', 'Outros'],
                    datasets: [{
                        data: [127, 123, 45, 38, 29, 22, 18, 15],
                        backgroundColor: [
                            'rgba(37, 99, 235, 0.9)',   // blue-600
                            'rgba(59, 130, 246, 0.9)',  // blue-500
                            'rgba(96, 165, 250, 0.9)',  // blue-400
                            'rgba(147, 197, 253, 0.9)', // blue-300
                            'rgba(191, 219, 254, 0.9)', // blue-200
                            'rgba(156, 163, 175, 0.9)', // gray-400
                            'rgba(209, 213, 219, 0.9)', // gray-300
                            'rgba(229, 231, 235, 0.9)'  // gray-200
                        ],
                        borderWidth: 1,
                        borderColor: 'white'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: true,
                    plugins: {
                        legend: { display: false },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = ((context.parsed / total) * 100).toFixed(1);
                                    return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                                }
                            }
                        }
                    },
                    cutout: '50%'
                }
            });
        }

        // Gráfico de Especialidades
        if (document.getElementById('especialidadesChart')) {
            // Destruir gráfico existente se houver
            if (window.especialidadesChart && typeof window.especialidadesChart.destroy === 'function') {
                try {
                    window.especialidadesChart.destroy();
                } catch (error) {
                    console.warn('Erro ao destruir gráfico de especialidades:', error);
                }
            }

            // Limpar canvas completamente
            const canvas = document.getElementById('especialidadesChart');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Remover qualquer instância Chart.js associada ao canvas
            if (Chart.getChart && Chart.getChart(canvas)) {
                Chart.getChart(canvas).destroy();
            }

            const especialidadesCtx = canvas.getContext('2d');
            window.especialidadesChart = new Chart(especialidadesCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Cardiologia', 'Dermatologia', 'Ortopedia', 'Neurologia', 'Ginecologia', 'Pediatria', 'Oftalmologia', 'Psiquiatria'],
                    datasets: [{
                        data: [284, 221, 187, 156, 142, 128, 115, 98],
                        backgroundColor: [
                            'rgba(37, 99, 235, 0.9)',   // blue-600
                            'rgba(59, 130, 246, 0.9)',  // blue-500
                            'rgba(96, 165, 250, 0.9)',  // blue-400
                            'rgba(147, 197, 253, 0.9)', // blue-300
                            'rgba(191, 219, 254, 0.9)', // blue-200
                            'rgba(156, 163, 175, 0.9)', // gray-400
                            'rgba(209, 213, 219, 0.9)', // gray-300
                            'rgba(229, 231, 235, 0.9)'  // gray-200
                        ],
                        borderWidth: 1,
                        borderColor: 'white'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: true,
                    plugins: {
                        legend: { display: false },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = ((context.parsed / total) * 100).toFixed(1);
                                    return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                                }
                            }
                        }
                    },
                    cutout: '50%'
                }
            });
        }

        // Gráfico de Performance Médicos
        if (document.getElementById('performanceMedicosChart')) {
            // Destruir gráfico existente se houver
            if (window.performanceChart && typeof window.performanceChart.destroy === 'function') {
                try {
                    window.performanceChart.destroy();
                } catch (error) {
                    console.warn('Erro ao destruir gráfico de performance:', error);
                }
            }

            // Limpar canvas completamente
            const canvas = document.getElementById('performanceMedicosChart');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Remover qualquer instância Chart.js associada ao canvas
            if (Chart.getChart && Chart.getChart(canvas)) {
                Chart.getChart(canvas).destroy();
            }

            const performanceCtx = canvas.getContext('2d');
            window.performanceChart = new Chart(performanceCtx, {
                type: 'bar',
                data: {
                    labels: ['Dr. Silva', 'Dr. Santos', 'Dr. Costa', 'Dr. Oliveira', 'Dr. Pereira', 'Dr. Lima', 'Dr. Almeida', 'Dr. Ferreira'],
                    datasets: [{
                        data: [45, 38, 32, 28, 25, 22, 19, 16],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.9)',  // blue-500 - 1º lugar
                            'rgba(96, 165, 250, 0.8)',  // blue-400 - 2º lugar
                            'rgba(147, 197, 253, 0.8)', // blue-300 - 3º lugar
                            'rgba(191, 219, 254, 0.8)', // blue-200 - 4º lugar
                            'rgba(107, 114, 128, 0.9)', // gray-500 - 5º lugar
                            'rgba(156, 163, 175, 0.8)', // gray-400 - 6º lugar
                            'rgba(209, 213, 219, 0.8)', // gray-300 - 7º lugar
                            'rgba(229, 231, 235, 0.8)'  // gray-200 - 8º lugar
                        ],
                        borderWidth: 1,
                        borderColor: 'white',
                        borderRadius: 4,
                        barThickness: 18
                    }]
                },
                options: {
                    indexAxis: 'y',
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.label + ': R$ ' + context.parsed.x + 'K';
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            beginAtZero: true,
                            max: 50,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.1)'
                            },
                            ticks: {
                                display: true,
                                font: {
                                    size: 11
                                },
                                callback: function(value) {
                                    return 'R$ ' + value + 'K';
                                }
                            }
                        },
                        y: {
                            display: true,
                            ticks: {
                                font: {
                                    size: 11,
                                    weight: 'bold'
                                },
                                color: '#374151'
                            },
                            grid: {
                                display: false
                            }
                        }
                    },
                    layout: {
                        padding: {
                            left: 8,
                            right: 20,
                            top: 15,
                            bottom: 15
                        }
                    }
                }
            });
        }
    });
</script>

<!-- Script do gráfico Y/Y -->
<script src="/static/js/yoy-sales-chart.js"></script>
<!-- Script dos mini gráficos dos KPIs -->
<script src="/static/js/mini-charts.js"></script>
{% endblock %}

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Amigo DataHub</title>
    <link rel="icon" href="https://cdn-icons-png.flaticon.com/512/6295/6295417.png" type="image/png">
    <!-- Tailwind CSS via CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
          theme: {
            extend: {
              colors: {
                systemBlue: '#007AFF',
                systemGreen: '#34C759',
                systemGray: {
                  DEFAULT: '#8E8E93',
                  light: '#AEAEB2',
                  lighter: '#C7C7CC',
                  lightest: '#D1D1D6',
                  extralight: '#E5E5EA',
                  ultralight: '#F2F2F7'
                },
                label: {
                    DEFAULT: '#000000',
                    secondary: 'rgba(60, 60, 67, 0.6)',
                    tertiary: 'rgba(60, 60, 67, 0.3)',
                    quaternary: 'rgba(60, 60, 67, 0.18)'
                },
                chartPurple: '#7B61FF',
              },
              borderRadius: {
                  'view': '10px',
                  'control': '7px'
              },
              boxShadow: {
                  none: 'none',
              },
               borderColor: theme => ({
                   ...theme('colors'),
                   DEFAULT: theme('colors.gray.200', 'currentColor'),
               })
            }
          }
        }
    </script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
        }
        .text-label-secondary { color: rgba(60, 60, 67, 0.6); }
        .text-label-tertiary { color: rgba(60, 60, 67, 0.3); }
        .text-label-quaternary { color: rgba(60, 60, 67, 0.18); }

        @keyframes wave {
            0% { transform: rotate(0deg); }
            10% { transform: rotate(20deg); }
            20% { transform: rotate(-10deg); }
            30% { transform: rotate(20deg); }
            40% { transform: rotate(-5deg); }
            50% { transform: rotate(15deg); }
            60% { transform: rotate(0deg); }
            100% { transform: rotate(0deg); }
        }
        .animate-wave {
            animation: wave 2.5s ease infinite;
            transform-origin: 70% 70%;
            display: inline-block;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        .pulse-logo:hover img {
            animation: pulse 1s ease infinite;
        }

        .bg-hero-pattern {
            background-color: #f8fafc;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23e6f0ff' fill-opacity='0.4'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
    </style>
</head>
<body class="bg-gray-50 h-screen flex flex-col overflow-hidden">
    <div class="flex flex-1 h-full">
        <!-- Lado esquerdo - Hero (Redesenhado mais leve e minimalista) -->
        <div class="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-blue-50 to-white relative overflow-hidden">
            <!-- Elementos minimalistas em azul claro -->
            <div class="absolute inset-0 overflow-hidden">
                <!-- Padrão de pontos sutis que conversa com o formulário -->
                <div class="absolute top-0 left-0 w-full h-full opacity-10">
                    <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse">
                                <circle cx="2" cy="2" r="1" fill="#93c5fd" opacity="0.7"/>
                            </pattern>
                        </defs>
                        <rect width="100%" height="100%" fill="url(#dots)" />
                    </svg>
                </div>

                <!-- Formas fluidas sutis -->
                <div class="absolute top-[-10%] right-[-5%] w-[400px] h-[400px] rounded-full bg-gradient-to-br from-blue-100/20 to-blue-50/10 blur-3xl"></div>
                <div class="absolute bottom-[-20%] left-[-10%] w-[500px] h-[500px] rounded-full bg-gradient-to-tr from-blue-200/10 to-blue-50/5 blur-3xl"></div>
                <div class="absolute top-[30%] left-[20%] w-[200px] h-[200px] rounded-full bg-gradient-to-r from-blue-100/10 to-blue-50/5 blur-xl"></div>
            </div>

            <!-- Conteúdo do Hero minimalista - alinhado verticalmente com o formulário -->
            <div class="relative z-10 flex flex-col justify-center px-16 py-8 h-full">
                <!-- Logo e nome com maior destaque -->
                <div class="flex items-center mb-10">
                    <img src="/static/amigo-logo.png" alt="Amigo Logo" class="h-16 w-auto drop-shadow-md">
                    <div class="flex items-center ml-3">
                        <span class="mx-1 text-gray-300 font-bold text-lg">|</span>
                        <span class="text-gray-800 font-bold text-2xl">DataHub</span>
                    </div>
                </div>

                <!-- Saudação removida -->

                <!-- Título principal com destaque -->
                <h1 class="text-4xl font-bold text-gray-800 mb-5 leading-tight max-w-lg">
                    Impulsione a gestão da sua clínica com o <span class="text-blue-600">Amigo DataHub</span>
                </h1>

                <!-- Subtítulo com valor claro -->
                <p class="text-gray-600 text-lg max-w-lg mb-8">
                    A plataforma completa que revoluciona a gestão de dados da sua clínica com insights inteligentes e visão 360°
                </p>

                <!-- Benefícios principais em layout compacto -->
                <div class="grid grid-cols-2 gap-4 mb-8 max-w-lg">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 mr-3">
                            <div class="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center">
                                <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z"></path>
                                </svg>
                            </div>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-800">Visão 360° do Paciente</h3>
                            <p class="text-xs text-gray-500">Acompanhe toda a jornada do paciente.</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <div class="flex-shrink-0 mr-3">
                            <div class="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center">
                                <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                            </div>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-800">Análise Inteligente</h3>
                            <p class="text-xs text-gray-500">Insights gerados por IA.</p>
                        </div>
                    </div>
                </div>

                <!-- Depoimento minimalista -->
                <div class="border-l-2 border-blue-100 pl-4 py-2 mb-8 max-w-lg">
                    <p class="text-gray-600 italic text-sm">"Aumentamos nossa eficiência em 30% após implementar o DataHub. A visão 360° dos pacientes transformou completamente nosso atendimento."</p>
                    <p class="text-xs text-gray-500 mt-2">— Dr. Carlos Mendes, Clínica Saúde Total</p>
                </div>

                <!-- Métricas de impacto com design minimalista em branco, azul e cinza -->
                <div class="grid grid-cols-3 gap-5 mb-8 max-w-lg">
                    <!-- Card Eficiência -->
                    <div class="bg-white/80 backdrop-blur-sm rounded-xl p-4 border border-blue-50 hover:border-blue-100 hover:shadow-sm transition-all duration-300 group">
                        <div class="flex flex-col items-center text-center">
                            <div class="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mb-2 group-hover:bg-blue-100 transition-colors duration-300">
                                <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                            </div>
                            <p class="text-xs font-medium text-gray-500 mb-1">Eficiência</p>
                            <p class="text-xl font-bold text-blue-600">+30%</p>
                        </div>
                    </div>

                    <!-- Card Faltas -->
                    <div class="bg-white/80 backdrop-blur-sm rounded-xl p-4 border border-blue-50 hover:border-blue-100 hover:shadow-sm transition-all duration-300 group">
                        <div class="flex flex-col items-center text-center">
                            <div class="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mb-2 group-hover:bg-blue-100 transition-colors duration-300">
                                <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <p class="text-xs font-medium text-gray-500 mb-1">Faltas</p>
                            <p class="text-xl font-bold text-blue-600">-25%</p>
                        </div>
                    </div>

                    <!-- Card Satisfação -->
                    <div class="bg-white/80 backdrop-blur-sm rounded-xl p-4 border border-blue-50 hover:border-blue-100 hover:shadow-sm transition-all duration-300 group">
                        <div class="flex flex-col items-center text-center">
                            <div class="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mb-2 group-hover:bg-blue-100 transition-colors duration-300">
                                <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <p class="text-xs font-medium text-gray-500 mb-1">Satisfação</p>
                            <p class="text-xl font-bold text-blue-600">+40%</p>
                        </div>
                    </div>
                </div>

                <!-- Rodapé -->
                <div class="mt-auto">
                    <p class="text-gray-400 text-xs">© 2024 Amigo Clinic. Todos os direitos reservados.</p>
                </div>
            </div>
        </div>

        <!-- Lado direito - Formulário de login -->
        <div class="w-full lg:w-1/2 flex items-center justify-center px-6 py-6 bg-gradient-to-br from-white to-blue-100 relative">
            <!-- Elementos decorativos em azul claro para o lado do formulário -->
            <div class="absolute inset-0 overflow-hidden pointer-events-none">
                <!-- Círculos fluidos em azul claro - mais nítidos -->
                <div class="absolute top-10 right-10 w-40 h-40 rounded-full bg-gradient-to-br from-blue-200 to-blue-100 opacity-40 blur-xl"></div>
                <div class="absolute bottom-10 left-10 w-48 h-48 rounded-full bg-gradient-to-tr from-blue-100 to-white opacity-50 blur-xl"></div>
                <div class="absolute top-1/3 left-1/4 w-32 h-32 rounded-full bg-blue-200 opacity-30 blur-lg"></div>
                <div class="absolute bottom-1/3 right-1/4 w-24 h-24 rounded-full bg-blue-100 opacity-40 blur-md"></div>
                <div class="absolute top-1/2 right-1/3 w-16 h-16 rounded-full bg-gradient-to-r from-blue-200 to-white opacity-35 blur-sm"></div>

                <!-- Círculos com bordas -->
                <div class="absolute top-20 left-20 w-20 h-20 rounded-full border border-blue-200 opacity-40"></div>
                <div class="absolute bottom-20 right-20 w-16 h-16 rounded-full border border-blue-100 opacity-30"></div>

                <!-- Pontos em grade -->
                <div class="absolute top-0 left-0 w-full h-full opacity-10">
                    <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <pattern id="dots-form" width="20" height="20" patternUnits="userSpaceOnUse">
                                <circle cx="2" cy="2" r="1" fill="#93c5fd" opacity="0.5"/>
                            </pattern>
                        </defs>
                        <rect width="100%" height="100%" fill="url(#dots-form)" />
                    </svg>
                </div>

                <!-- Onda suave na parte inferior -->
                <svg class="absolute bottom-0 left-0 w-full opacity-10" viewBox="0 0 1440 320" xmlns="http://www.w3.org/2000/svg">
                    <path fill="#dbeafe" fill-opacity="1" d="M0,96L48,112C96,128,192,160,288,160C384,160,480,128,576,133.3C672,139,768,181,864,181.3C960,181,1056,139,1152,122.7C1248,107,1344,117,1392,122.7L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path>
                </svg>
            </div>

            <div class="w-full max-w-md relative z-10">
                <!-- Logo para mobile aumentada -->
                <div class="lg:hidden flex justify-center mb-8">
                    <div class="flex items-center">
                        <img src="/static/amigo-logo.png" alt="Amigo Logo" class="h-14">
                        <div class="flex items-center ml-3">
                            <span class="mx-1 text-blue-400 font-bold text-base">|</span>
                            <span class="text-gray-700 font-bold text-xl">DataHub</span>
                        </div>
                    </div>
                </div>

                <div class="p-6">
                    <div class="text-center mb-8">
                        <h2 class="text-2xl font-bold text-gray-800 mb-2">Acesse sua conta</h2>
                        <p class="text-gray-500 text-sm">Faça login para acessar o DataHub</p>
                    </div>

                    {% if error %}
                    <div class="border-l-2 border-red-500 pl-3 py-2 mb-6 text-xs text-red-600">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                            </svg>
                            <span>{{ error }}</span>
                        </div>
                    </div>
                    {% endif %}

                    <form action="{{ url_for('login') }}" method="post" class="space-y-6">
                        <div>
                            <label for="email" class="block text-xs font-medium text-gray-500 mb-2">Email</label>
                            <div class="relative group">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                                <input type="email" id="email" name="email" required
                                    class="w-full pl-10 pr-3 py-2.5 border border-gray-200 rounded-lg focus:ring-1 focus:ring-gray-500 focus:border-gray-500 focus:outline-none transition-all duration-200 text-sm"
                                    placeholder="<EMAIL>">
                            </div>
                        </div>

                        <div>
                            <div class="flex items-center justify-between mb-2">
                                <label for="password" class="block text-xs font-medium text-gray-500">Senha</label>
                                <a href="#" class="text-xs text-gray-500 hover:text-gray-700 transition-colors duration-200">Esqueceu?</a>
                            </div>
                            <div class="relative group">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                    </svg>
                                </div>
                                <input type="password" id="password" name="password" required
                                    class="w-full pl-10 pr-3 py-2.5 border border-gray-200 rounded-lg focus:ring-1 focus:ring-gray-500 focus:border-gray-500 focus:outline-none transition-all duration-200 text-sm"
                                    placeholder="••••••••">
                            </div>
                        </div>

                        <div class="flex items-center">
                            <input id="remember_me" name="remember_me" type="checkbox"
                                class="h-3 w-3 text-gray-500 focus:ring-gray-400 border-gray-300 rounded">
                            <label for="remember_me" class="ml-2 block text-xs text-gray-500">Lembrar-me</label>
                        </div>

                        <div>
                            <button type="submit"
                                class="w-full flex justify-center py-2.5 px-4 border border-blue-200 rounded-lg text-sm font-medium text-blue-600 bg-transparent hover:bg-blue-50 hover:border-blue-300 focus:outline-none focus:ring-1 focus:ring-blue-300 transition-all duration-200">
                                Entrar
                            </button>
                        </div>
                    </form>

                    <div class="mt-8 text-center">
                        <p class="text-xs text-gray-500">
                            Não tem uma conta? <a href="#" class="font-medium text-gray-700 hover:text-gray-900 transition-colors duration-200">Entre em contato</a>
                        </p>
                    </div>

                    <!-- Logo na parte inferior do formulário -->
                    <div class="mt-10 flex justify-center">
                        <div class="flex items-center opacity-70">
                            <img src="/static/amigo-logo.png" alt="Amigo Logo" class="h-8">
                            <div class="flex items-center ml-2">
                                <span class="mx-1 text-gray-300 font-bold text-xs">|</span>
                                <span class="text-gray-600 font-bold text-sm">DataHub</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-4 text-center text-xs text-gray-500 lg:hidden">
                    <p>© 2024 Amigo Clinic. Todos os direitos reservados.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Script removido para manter apenas "Olá" fixo -->
</body>
</html>

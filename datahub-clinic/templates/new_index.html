{% extends 'base.html' %}
{% from 'components/page_context.html' import expose_context %}

{% block title %}Dashboard - Amigo DataHub{% endblock %}

{% block header %}Dashboard{% endblock %}

{% block content %}
{{ expose_context(
    title="Dashboard Principal",
    description="Visão geral dos dados do Amigo Partner",
    key_metrics={
        "Total de Leads": resumo.total_leads|default(0),
        "Total de Oportunidades": resumo.total_oportunidades|default(0),
        "Taxa de Conversão": resumo.taxa_conversao|default(0),
        "Universidades": resumo.universidades_populares|default([])|length
    },
    analysis_focus="Análise de captação de leads e oportunidades",
    page_elements=["Captação de Leads", "Gestão de Oportunidades", "Análise Educacional", "Marketing"],
    additional_context={
        "areas_formacao": areas|default([]),
        "origens_leads": origens|default([]),
        "universidades": unidades|default([])
    }
) }}

<!-- Novo Hero de boas-vindas -->
<div class="bg-gradient-to-r from-blue-50 to-white rounded-view p-8 mb-8 border border-gray-200 relative overflow-hidden">
    <!-- Elementos decorativos de fundo -->
    <div class="absolute top-0 right-0 w-full h-full opacity-10 overflow-hidden">
        <svg class="absolute top-10 right-10 w-64 h-64 text-systemBlue" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"></path>
        </svg>
        <svg class="absolute bottom-10 left-1/4 w-32 h-32 text-systemBlue" fill="currentColor" viewBox="0 0 24 24">
            <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"></path>
        </svg>
    </div>
    
    <div class="flex flex-col md:flex-row items-center relative z-10">
        <div class="md:w-2/3">
            <h1 class="text-3xl font-bold text-label-DEFAULT mb-2">Bem-vindo ao Amigo DataHub</h1>
            <p class="text-lg text-label-secondary mb-6">Visualize e analise os dados da sua clínica em um só lugar.</p>
            <div class="flex flex-wrap gap-3 mb-6">
                <a href="{{ url_for('agendamentos') }}" class="bg-systemBlue hover:bg-blue-600 text-white px-6 py-2 rounded-full transition duration-150 ease-in-out">
                    Ver Agendamentos
                </a>
                <a href="{{ url_for('producao_medica') }}" class="bg-white border border-systemBlue text-systemBlue hover:bg-gray-50 px-6 py-2 rounded-full transition duration-150 ease-in-out">
                    Produção Médica
                </a>
            </div>
            <div class="flex items-center">
                <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">Powered by Amigo Intelligence</span>
            </div>
        </div>
        <div class="md:w-1/3 flex justify-center mt-6 md:mt-0">
            <div class="w-48 h-48 bg-blue-100 rounded-full flex items-center justify-center">
                <svg class="w-24 h-24 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
            </div>
        </div>
    </div>
</div>

<!-- Novos KPIs principais -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total de Leads -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200 hover:shadow-md transition-shadow duration-300">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-label-DEFAULT">Total de Leads</p>
        </div>
        <p class="text-3xl font-semibold text-label-DEFAULT mb-1">{{ '{:,}'.format(resumo.total_leads|default(0)).replace(',', '.') if (resumo.total_leads|default(0)) < 1000 else '{:.1f}K'.format((resumo.total_leads|default(0))/1000) if (resumo.total_leads|default(0)) < 1000000 else '{:.1f}M'.format((resumo.total_leads|default(0))/1000000) }}</p>
        <div class="flex items-baseline mb-2">
            <p class="text-sm font-medium text-systemGreen mr-2">+{{ ((resumo.total_leads|default(0)) * 0.05) | round | int }}</p>
            <p class="text-xs text-label-secondary">vs Mês Anterior</p>
        </div>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Contatos registrados no sistema.</p>
    </div>

    <!-- Total de Oportunidades -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200 hover:shadow-md transition-shadow duration-300">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-label-DEFAULT">Total de Oportunidades</p>
        </div>
        <p class="text-3xl font-semibold text-label-DEFAULT mb-1">{{ '{:,}'.format(resumo.total_oportunidades|default(0)).replace(',', '.') if (resumo.total_oportunidades|default(0)) < 1000 else '{:.1f}K'.format((resumo.total_oportunidades|default(0))/1000) if (resumo.total_oportunidades|default(0)) < 1000000 else '{:.1f}M'.format((resumo.total_oportunidades|default(0))/1000000) }}</p>
        <div class="flex items-baseline mb-2">
            <p class="text-sm font-medium text-systemGreen mr-2">+{{ ((resumo.total_oportunidades|default(0)) * 0.03) | round | int }}</p>
            <p class="text-xs text-label-secondary">vs Mês Anterior</p>
        </div>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Oportunidades de negócio registradas.</p>
    </div>

    <!-- Taxa de Conversão -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200 hover:shadow-md transition-shadow duration-300">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-label-DEFAULT">Taxa de Conversão</p>
        </div>
        <p class="text-3xl font-semibold text-label-DEFAULT mb-1">{{ resumo.taxa_conversao|default(0) }}%</p>
        <div class="flex items-baseline mb-2">
            <p class="text-sm font-medium text-systemGreen mr-2">+2.1%</p>
            <p class="text-xs text-label-secondary">vs Mês Anterior</p>
        </div>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Percentual de oportunidades ganhas.</p>
    </div>

    <!-- Universidades -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200 hover:shadow-md transition-shadow duration-300">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
            </div>
            <p class="text-sm font-semibold text-label-DEFAULT">Universidades</p>
        </div>
        <p class="text-3xl font-semibold text-label-DEFAULT mb-1">{{ resumo.universidades_populares|default([])|length }}</p>
        <div class="flex items-baseline mb-2">
            <p class="text-sm font-medium text-systemGreen mr-2">+{{ (resumo.universidades_populares|default([])|length * 0.1) | round | int }}</p>
            <p class="text-xs text-label-secondary">vs Mês Anterior</p>
        </div>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Universidades com contatos registrados.</p>
    </div>
</div>

<!-- Nova Visão Geral dos Dados do Amigo Partner -->
<div class="bg-white rounded-view p-6 border border-gray-200 mb-8 hover:shadow-md transition-shadow duration-300">
    <div class="flex items-center mb-4">
        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
            <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
        </div>
        <h3 class="text-base font-semibold">Visão Geral dos Dados do Amigo Partner</h3>
    </div>
    <div class="prose max-w-none">
        <p>Este dashboard apresenta uma análise dos dados do arquivo "Cópia de Amigo Partner.xlsx", com foco em captação de leads, gestão de oportunidades e análise educacional.</p>

        <h4 class="mt-4">Principais Pontos da Base de Dados:</h4>
        <ul>
            <li><strong>Contatos:</strong> {{ resumo.total_leads|default(0) }} registros de contatos com informações detalhadas</li>
            <li><strong>Oportunidades:</strong> {{ resumo.total_oportunidades|default(0) }} oportunidades de negócio em diferentes estágios do funil</li>
            <li><strong>Taxa de Conversão:</strong> {{ resumo.taxa_conversao|default(0) }}% das oportunidades foram convertidas</li>
            <li><strong>Universidades:</strong> {{ resumo.universidades_populares|default([])|length }} instituições de ensino com contatos registrados</li>
        </ul>

        <h4 class="mt-4">Estrutura dos Dados:</h4>
        <ul>
            <li><strong>CONTATOS:</strong> Base principal com {{ resumo.total_leads|default(0) }} registros de leads e clientes</li>
            <li><strong>OPORTUNIDADES:</strong> {{ resumo.total_oportunidades|default(0) }} registros de oportunidades de negócio</li>
            <li><strong>TURMAS:</strong> Informações sobre turmas acadêmicas e cursos</li>
            <li><strong>UNIVERSIDADES:</strong> Dados sobre instituições de ensino</li>
            <li><strong>CUPONS:</strong> Códigos promocionais e descontos</li>
        </ul>
    </div>
</div>

<!-- Gráficos -->
<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
    <!-- Leads por Mês -->
    <div class="bg-white rounded-view p-6 border border-gray-200 hover:shadow-md transition-shadow duration-300">
        <h3 class="text-base font-semibold mb-4">Leads por Mês</h3>
        <div class="h-64">
            <canvas id="leadsChart"></canvas>
        </div>
    </div>

    <!-- Funil de Vendas -->
    <div class="bg-white rounded-view p-6 border border-gray-200 hover:shadow-md transition-shadow duration-300">
        <h3 class="text-base font-semibold mb-4">Funil de Vendas</h3>
        <div class="h-64">
            <canvas id="funilChart"></canvas>
        </div>
    </div>

    <!-- Distribuição por Área de Formação -->
    <div class="bg-white rounded-view p-6 border border-gray-200 hover:shadow-md transition-shadow duration-300">
        <h3 class="text-base font-semibold mb-4">Distribuição por Área de Formação</h3>
        <div class="h-64">
            <canvas id="areasChart"></canvas>
        </div>
    </div>

    <!-- Distribuição por Origem -->
    <div class="bg-white rounded-view p-6 border border-gray-200 hover:shadow-md transition-shadow duration-300">
        <h3 class="text-base font-semibold mb-4">Distribuição por Origem</h3>
        <div class="h-64">
            <canvas id="origensChart"></canvas>
        </div>
    </div>
</div>

<!-- Insights da IA -->
<div class="bg-white rounded-view p-6 border border-gray-200 mb-8 hover:shadow-md transition-shadow duration-300">
    <div class="flex justify-between items-center mb-4">
        <div class="flex items-center">
            <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                </svg>
            </div>
            <h3 class="text-base font-semibold">Insights da IA</h3>
        </div>
        <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">Atualizado agora</span>
    </div>

    <div id="insights-container" class="grid grid-cols-1 md:grid-cols-3 gap-3">
        {% for insight in insights %}
            {% with
                title=insight.title,
                description=insight.description,
                insight_type=insight.insight_type,
                content=insight.content,
                category=insight.category
            %}
                {% include 'components/ai_insight_widget.html' %}
            {% endwith %}
        {% endfor %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Dados para os gráficos
        // Leads por Mês
        const leadsMonthlyData = {
            labels: [{% if resumo.leads_por_mes is defined %}{% for month, count in resumo.leads_por_mes.items() %}'{{ month }}',{% endfor %}{% else %}{% endif %}],
            datasets: [{
                label: 'Leads',
                data: [{% if resumo.leads_por_mes is defined %}{% for month, count in resumo.leads_por_mes.items() %}{{ count }},{% endfor %}{% else %}{% endif %}],
                backgroundColor: 'rgba(0, 122, 255, 0.2)',
                borderColor: 'rgba(0, 122, 255, 1)',
                borderWidth: 2,
                tension: 0.4,
                fill: true
            }]
        };

        const leadsConfig = {
            type: 'line',
            data: leadsMonthlyData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        };

        // Funil de Vendas
        const funilData = {
            labels: [{% if resumo.etapas_funil is defined %}{% for etapa in resumo.etapas_funil %}'{{ etapa.etapa }}',{% endfor %}{% else %}{% endif %}],
            datasets: [{
                label: 'Leads',
                data: [{% if resumo.etapas_funil is defined %}{% for etapa in resumo.etapas_funil %}{{ etapa.quantidade }},{% endfor %}{% else %}{% endif %}],
                backgroundColor: [
                    'rgba(0, 122, 255, 0.8)',
                    'rgba(52, 199, 89, 0.8)',
                    'rgba(255, 149, 0, 0.8)',
                    'rgba(175, 82, 222, 0.8)',
                    'rgba(255, 45, 85, 0.8)',
                    'rgba(88, 86, 214, 0.8)'
                ],
                borderWidth: 0
            }]
        };

        const funilConfig = {
            type: 'bar',
            data: funilData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: 'y',
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        beginAtZero: true
                    }
                }
            }
        };

        // Distribuição por Área de Formação
        const areasData = {
            labels: [{% if resumo.distribuicao_areas is defined %}{% for area in resumo.distribuicao_areas %}'{{ area.area }}',{% endfor %}{% else %}{% endif %}],
            datasets: [{
                label: 'Quantidade',
                data: [{% if resumo.distribuicao_areas is defined %}{% for area in resumo.distribuicao_areas %}{{ area.quantidade }},{% endfor %}{% else %}{% endif %}],
                backgroundColor: [
                    'rgba(0, 122, 255, 0.8)',
                    'rgba(52, 199, 89, 0.8)',
                    'rgba(255, 149, 0, 0.8)',
                    'rgba(175, 82, 222, 0.8)',
                    'rgba(255, 45, 85, 0.8)',
                    'rgba(88, 86, 214, 0.8)',
                    'rgba(0, 199, 190, 0.8)',
                    'rgba(255, 204, 0, 0.8)'
                ],
                borderWidth: 0
            }]
        };

        const areasConfig = {
            type: 'doughnut',
            data: areasData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            boxWidth: 12,
                            font: {
                                size: 10
                            }
                        }
                    }
                }
            }
        };

        // Distribuição por Origem
        const origensData = {
            labels: [{% if resumo.distribuicao_origens is defined %}{% for origem in resumo.distribuicao_origens %}'{{ origem.origem }}',{% endfor %}{% else %}{% endif %}],
            datasets: [{
                label: 'Quantidade',
                data: [{% if resumo.distribuicao_origens is defined %}{% for origem in resumo.distribuicao_origens %}{{ origem.quantidade }},{% endfor %}{% else %}{% endif %}],
                backgroundColor: [
                    'rgba(0, 122, 255, 0.8)',
                    'rgba(52, 199, 89, 0.8)',
                    'rgba(255, 149, 0, 0.8)',
                    'rgba(175, 82, 222, 0.8)',
                    'rgba(255, 45, 85, 0.8)',
                    'rgba(88, 86, 214, 0.8)',
                    'rgba(0, 199, 190, 0.8)',
                    'rgba(255, 204, 0, 0.8)'
                ],
                borderWidth: 0
            }]
        };

        const origensConfig = {
            type: 'pie',
            data: origensData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            boxWidth: 12,
                            font: {
                                size: 10
                            }
                        }
                    }
                }
            }
        };

        // Renderizar os gráficos
        try {
            // Novos gráficos
            if (document.getElementById('leadsChart')) {
                new Chart(document.getElementById('leadsChart'), leadsConfig);
            }

            if (document.getElementById('funilChart')) {
                new Chart(document.getElementById('funilChart'), funilConfig);
            }

            if (document.getElementById('areasChart')) {
                new Chart(document.getElementById('areasChart'), areasConfig);
            }

            if (document.getElementById('origensChart')) {
                new Chart(document.getElementById('origensChart'), origensConfig);
            }
        } catch (error) {
            console.error('Erro ao renderizar gráficos:', error);
        }
    });

    // Carregar insights dinamicamente após 5 segundos (para demonstração)
    setTimeout(() => {
        // Usa o contexto já definido na página
        loadInsights('dashboard', 'resumo', 'insights-container', 3, ['list', 'text', 'stat'], window.pageContext);
    }, 5000);
</script>
{% endblock %}

{% extends 'base.html' %}

{% block title %}Atendimentos Realizados - Amigo DataHub{% endblock %}

{% block header %}Atendimentos Realizados{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="bg-white rounded-lg shadow-sm p-6 mb-5">
    <div class="flex flex-col md:flex-row items-start md:items-center">
        <div class="md:w-3/4 mb-4 md:mb-0 md:pr-6">
            <div class="inline-block bg-blue-50 text-systemBlue text-xs font-medium px-2.5 py-0.5 rounded mb-2">PACIENTE</div>
            <h1 class="text-2xl font-semibold text-gray-800 mb-2">Atendimentos Realizados</h1>
            <p class="text-gray-600 text-sm mb-4 max-w-3xl">Acompanhe e analise todos os atendimentos realizados na clínica.</p>

            {% set total_atendimentos = atendimentos|length %}
            {% set valor_total = atendimentos|sum(attribute='valor') %}
            {% set valor_medio = valor_total / total_atendimentos if total_atendimentos > 0 else 0 %}

            <div class="flex flex-wrap gap-6 mb-4">
                <div class="flex items-center">
                    <div class="w-10 h-10 rounded-lg bg-blue-50 flex items-center justify-center mr-3">
                        <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="text-2xl font-semibold text-gray-800">{{ total_atendimentos }}</div>
                        <div class="text-xs text-gray-500">Total de atendimentos</div>
                    </div>
                </div>

                <div class="flex items-center">
                    <div class="w-10 h-10 rounded-lg bg-blue-50 flex items-center justify-center mr-3">
                        <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="text-2xl font-semibold text-gray-800">R$ {{ valor_total|round|int }}</div>
                        <div class="text-xs text-gray-500">Valor total</div>
                    </div>
                </div>

                <div class="flex items-center">
                    <div class="w-10 h-10 rounded-lg bg-blue-50 flex items-center justify-center mr-3">
                        <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="text-2xl font-semibold text-gray-800">R$ {{ valor_medio|round|int }}</div>
                        <div class="text-xs text-gray-500">Ticket médio</div>
                    </div>
                </div>

                <div class="flex items-center">
                    <div class="w-10 h-10 rounded-lg bg-blue-50 flex items-center justify-center mr-3">
                        <svg class="w-5 h-5 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="text-2xl font-semibold text-gray-800">72%</div>
                        <div class="text-xs text-gray-500">Taxa de retorno</div>
                    </div>
                </div>
            </div>

            <div class="flex flex-wrap gap-3">
                <button class="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center transition-colors">
                    <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    Gerar Relatório
                </button>
                <button class="bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-md text-sm border border-gray-200 flex items-center transition-colors">
                    <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                    </svg>
                    Exportar Dados
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Insights com IA -->
<div class="mb-5">
    <div class="flex items-center justify-between mb-4">
        <div class="flex items-center">
            <svg class="w-5 h-5 text-systemBlue mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
            </svg>
            <h2 class="text-base font-semibold text-gray-800">Amigo Intelligence</h2>
        </div>
        <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">Atualizado agora</span>
    </div>

    <div id="insights-container" class="grid grid-cols-1 md:grid-cols-3 gap-3">
        {% for insight in insights %}
            {% with
                title=insight.title,
                description=insight.description,
                insight_type=insight.insight_type,
                content=insight.content,
                category=insight.category
            %}
                {% include 'components/ai_insight_widget.html' %}
            {% endwith %}
        {% endfor %}
    </div>
</div>

<script>
    // Carregar insights dinamicamente após 5 segundos (para demonstração)
    setTimeout(() => {
        // Contexto específico da página de atendimentos realizados
        const pageContext = {
            page_title: "Atendimentos Realizados",
            page_description: "Análise e gestão dos atendimentos realizados na clínica, com foco em segmentação de pacientes e oportunidades de cross-selling",
            key_metrics: {
                "Total de Atendimentos": "{{ total_atendimentos }}",
                "Valor Total": "R$ {{ valor_total|round|int }}",
                "Ticket Médio": "R$ {{ valor_medio|round|int }}",
                "Taxa de Retorno": "72%"
            },
            analysis_focus: "Segmentação de pacientes e oportunidades de cross-selling",
            page_elements: [
                "Tipos de Atendimento",
                "Formas de Pagamento",
                "Tendência de Atendimentos",
                "Análise de Retenção de Pacientes"
            ]
        };

        loadInsights('paciente', 'atendimentos_realizados', 'insights-container', 3, ['list', 'text', 'stat'], pageContext);
    }, 5000);
</script>

<!-- Insights Estáticos (Temporários) -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-4 hidden">
    <!-- Insight de Segmentação -->
    <div class="bg-white rounded-lg shadow-sm p-4 border-l-4 border-systemBlue">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 rounded-lg bg-blue-50 flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
            </div>
            <div>
                <h3 class="text-sm font-medium text-gray-800">Segmentação de Pacientes</h3>
                <p class="text-xs text-gray-500">Análise de perfil</p>
            </div>
        </div>
        <ul class="mt-3 space-y-2 text-xs">
            <li class="flex justify-between">
                <span>Pacientes recorrentes (3+)</span>
                <span class="font-medium text-systemBlue">42%</span>
            </li>
            <li class="flex justify-between">
                <span>Pacientes esporádicos (1-2)</span>
                <span class="font-medium text-systemBlue">35%</span>
            </li>
            <li class="flex justify-between">
                <span>Receita de recorrentes</span>
                <span class="font-medium text-systemBlue">68%</span>
            </li>
        </ul>
        <a href="#" class="mt-3 inline-block text-xs text-systemBlue hover:text-blue-700 font-medium">Ver detalhes →</a>
    </div>

    <!-- Insight de Oportunidades -->
    <div class="bg-white rounded-lg shadow-sm p-4 border-l-4 border-green-500">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 rounded-lg bg-green-50 flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                </svg>
            </div>
            <div>
                <h3 class="text-sm font-medium text-gray-800">Oportunidades de Cross-Selling</h3>
                <p class="text-xs text-gray-500">Recomendações</p>
            </div>
        </div>
        <ul class="mt-3 space-y-2 text-xs">
            <li class="flex justify-between">
                <span>Fisioterapia após consulta</span>
                <span class="font-medium text-green-600">+28% retenção</span>
            </li>
            <li class="flex justify-between">
                <span>Pacotes de procedimentos</span>
                <span class="font-medium text-green-600">+15% ticket</span>
            </li>
            <li class="flex justify-between">
                <span>Exames complementares</span>
                <span class="font-medium text-green-600">22% aceitação</span>
            </li>
        </ul>
        <a href="#" class="mt-3 inline-block text-xs text-green-600 hover:text-green-700 font-medium">Ver sugestões →</a>
    </div>

    <!-- Insight de Previsão -->
    <div class="bg-white rounded-lg shadow-sm p-4 border-l-4 border-orange-400">
        <div class="flex items-center mb-2">
            <div class="w-8 h-8 rounded-lg bg-orange-50 flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
            </div>
            <div>
                <h3 class="text-sm font-medium text-gray-800">Previsão de Retorno</h3>
                <p class="text-xs text-gray-500">Baseado em histórico</p>
            </div>
        </div>
        <p class="mt-3 text-xs text-gray-600">
            Análise preditiva indica <span class="font-medium">18 pacientes</span> com alta probabilidade de retorno nos próximos 7 dias e <span class="font-medium">65 pacientes</span> nos próximos 30 dias. Identificamos <span class="font-medium text-red-600">12 pacientes</span> em risco de abandono.
        </p>
        <a href="#" class="mt-3 inline-block text-xs text-orange-500 hover:text-orange-600 font-medium">Ver lista de pacientes →</a>
    </div>
</div>

<!-- Filtros Rápidos -->
<div class="flex flex-wrap gap-2 mb-5">
    <button class="bg-blue-50 text-systemBlue px-3 py-1.5 rounded-md text-xs font-medium flex items-center">
        <svg class="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
        </svg>
        Últimos 30 dias
    </button>
    <button class="bg-white hover:bg-gray-50 text-gray-700 px-3 py-1.5 rounded-md text-xs font-medium flex items-center border border-gray-200">
        <svg class="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
        </svg>
        Últimos 90 dias
    </button>
    <button class="bg-white hover:bg-gray-50 text-gray-700 px-3 py-1.5 rounded-md text-xs font-medium flex items-center border border-gray-200">
        <svg class="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
        </svg>
        Este ano
    </button>
    <button class="bg-white hover:bg-gray-50 text-gray-700 px-3 py-1.5 rounded-md text-xs font-medium flex items-center border border-gray-200">
        <svg class="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
        </svg>
        Por paciente
    </button>
    <button class="bg-white hover:bg-gray-50 text-gray-700 px-3 py-1.5 rounded-md text-xs font-medium flex items-center border border-gray-200">
        <svg class="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
        </svg>
        Mais filtros
    </button>
</div>

<!-- Análises Avançadas -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-5">
    <!-- Análise por Tipo -->
    <div class="bg-white rounded-lg shadow-sm p-4">
        <div class="flex items-center mb-3">
            <div class="w-8 h-8 rounded-lg bg-blue-50 flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                </svg>
            </div>
            <div>
                <h3 class="text-sm font-medium text-gray-800">Tipos de Atendimento</h3>
                <p class="text-xs text-gray-500">Distribuição</p>
            </div>
        </div>
        <div class="space-y-2">
            <div>
                <div class="flex justify-between mb-1">
                    <span class="text-xs text-gray-600">Consultas</span>
                    <span class="text-xs font-medium text-systemBlue">42%</span>
                </div>
                <div class="h-1.5 bg-gray-100 rounded-full overflow-hidden">
                    <div class="h-full bg-systemBlue" style="width: 42%"></div>
                </div>
            </div>
            <div>
                <div class="flex justify-between mb-1">
                    <span class="text-xs text-gray-600">Fisioterapia</span>
                    <span class="text-xs font-medium text-systemBlue">23%</span>
                </div>
                <div class="h-1.5 bg-gray-100 rounded-full overflow-hidden">
                    <div class="h-full bg-systemBlue" style="width: 23%"></div>
                </div>
            </div>
            <div>
                <div class="flex justify-between mb-1">
                    <span class="text-xs text-gray-600">Procedimentos</span>
                    <span class="text-xs font-medium text-systemBlue">35%</span>
                </div>
                <div class="h-1.5 bg-gray-100 rounded-full overflow-hidden">
                    <div class="h-full bg-systemBlue" style="width: 35%"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Análise por Forma de Pagamento -->
    <div class="bg-white rounded-lg shadow-sm p-4">
        <div class="flex items-center mb-3">
            <div class="w-8 h-8 rounded-lg bg-blue-50 flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                </svg>
            </div>
            <div>
                <h3 class="text-sm font-medium text-gray-800">Formas de Pagamento</h3>
                <p class="text-xs text-gray-500">Por valor</p>
            </div>
        </div>
        <div class="space-y-2">
            <div class="flex justify-between items-center">
                <span class="text-xs text-gray-600">Convênios</span>
                <span class="text-xs font-medium">58%</span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-xs text-gray-600">Cartão de Crédito</span>
                <span class="text-xs font-medium">22%</span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-xs text-gray-600">Dinheiro/PIX</span>
                <span class="text-xs font-medium">12%</span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-xs text-gray-600">Outros</span>
                <span class="text-xs font-medium">8%</span>
            </div>
        </div>
    </div>

    <!-- Tendência de Atendimentos -->
    <div class="bg-white rounded-lg shadow-sm p-4">
        <div class="flex items-center mb-3">
            <div class="w-8 h-8 rounded-lg bg-blue-50 flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                </svg>
            </div>
            <div>
                <h3 class="text-sm font-medium text-gray-800">Tendência</h3>
                <p class="text-xs text-gray-500">Últimos 3 meses</p>
            </div>
        </div>
        <div class="flex items-center justify-center h-16">
            <div class="flex items-end space-x-1">
                <div class="w-3 bg-blue-100 rounded-t" style="height: 20px"></div>
                <div class="w-3 bg-blue-200 rounded-t" style="height: 24px"></div>
                <div class="w-3 bg-blue-300 rounded-t" style="height: 18px"></div>
                <div class="w-3 bg-blue-400 rounded-t" style="height: 28px"></div>
                <div class="w-3 bg-blue-500 rounded-t" style="height: 32px"></div>
                <div class="w-3 bg-blue-600 rounded-t" style="height: 36px"></div>
                <div class="w-3 bg-systemBlue rounded-t" style="height: 40px"></div>
            </div>
        </div>
        <div class="mt-2 text-xs text-gray-500 flex justify-between">
            <span>Crescimento:</span>
            <span class="font-medium text-green-600">+12%</span>
        </div>
    </div>

    <!-- Ticket Médio Previsto -->
    <div class="bg-white rounded-lg shadow-sm p-4">
        <div class="flex items-center mb-3">
            <div class="w-8 h-8 rounded-lg bg-blue-50 flex items-center justify-center mr-2">
                <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                </svg>
            </div>
            <div>
                <h3 class="text-sm font-medium text-gray-800">Ticket Médio Previsto</h3>
                <p class="text-xs text-gray-500">Próximos 3 meses</p>
            </div>
        </div>
        {% set ticket_medio = (atendimentos|sum(attribute='valor')) / (atendimentos|length if atendimentos|length > 0 else 1) %}
        <div class="text-center my-2">
            <div class="text-2xl font-semibold text-systemBlue">R$ {{ (ticket_medio * 1.08)|round|int }}</div>
            <div class="text-xs text-green-600 font-medium">+8% de crescimento</div>
        </div>
        <div class="mt-2 pt-2 border-t border-gray-100">
            <a href="#" class="text-xs text-systemBlue hover:text-blue-700 font-medium">Ver análise completa →</a>
        </div>
    </div>
</div>

<!-- Filtros Avançados -->
<div class="bg-white rounded-lg shadow-sm p-4 mb-5">
    <div class="flex justify-between items-center mb-3">
        <div class="flex items-center">
            <svg class="w-4 h-4 text-gray-500 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
            </svg>
            <h2 class="text-sm font-medium text-gray-700">Filtros Avançados</h2>
        </div>
        <button class="text-xs text-systemBlue hover:text-blue-700 transition-colors">Limpar</button>
    </div>

    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-3">
        <div>
            <label for="data_inicio" class="block text-xs font-medium text-gray-500 mb-1">Data Início</label>
            <input type="date" id="data_inicio" class="block w-full px-3 py-1.5 text-sm border border-gray-200 focus:outline-none focus:ring-1 focus:ring-systemBlue focus:border-systemBlue rounded-md transition-colors">
        </div>

        <div>
            <label for="data_fim" class="block text-xs font-medium text-gray-500 mb-1">Data Fim</label>
            <input type="date" id="data_fim" class="block w-full px-3 py-1.5 text-sm border border-gray-200 focus:outline-none focus:ring-1 focus:ring-systemBlue focus:border-systemBlue rounded-md transition-colors">
        </div>

        <div>
            <label for="paciente" class="block text-xs font-medium text-gray-500 mb-1">Paciente</label>
            <input type="text" id="paciente" placeholder="Nome do paciente" class="block w-full px-3 py-1.5 text-sm border border-gray-200 focus:outline-none focus:ring-1 focus:ring-systemBlue focus:border-systemBlue rounded-md transition-colors">
        </div>

        <div>
            <label for="tipo_atendimento" class="block text-xs font-medium text-gray-500 mb-1">Tipo de Atendimento</label>
            <select id="tipo_atendimento" class="block w-full px-3 py-1.5 text-sm border border-gray-200 focus:outline-none focus:ring-1 focus:ring-systemBlue focus:border-systemBlue rounded-md transition-colors">
                <option value="">Todos</option>
                <option>Consulta - Amigo tech</option>
                <option>BOTOX</option>
                <option>Sessao de Fisioterapia</option>
                <option>PROCEDIMENTO SIMPLES - 60 MIN</option>
                <option>TERAPIA OCUPACIONAL</option>
                <option>PSICOLOGIA ABA</option>
                <option>Audiometria tonal</option>
            </select>
        </div>

        <div class="flex items-end">
            <button class="w-full bg-systemBlue hover:bg-blue-600 text-white px-4 py-1.5 rounded-md transition-colors text-sm font-medium">
                Aplicar Filtros
            </button>
        </div>
    </div>
</div>

<!-- Análises Avançadas -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Atendimentos por Tipo -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold">Atendimentos por Tipo</h2>
            <div class="flex items-center">
                <select id="periodoTipo" class="text-xs border-systemGray-lightest rounded-control mr-2">
                    <option value="30">30 dias</option>
                    <option value="90" selected>90 dias</option>
                    <option value="180">180 dias</option>
                </select>
            </div>
        </div>
        <div class="h-64">
            <canvas id="tipoAtendimentoChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> Consultas e Fisioterapia representam 65% dos atendimentos. Considere pacotes promocionais combinando estes serviços.</p>
        </div>
    </div>

    <!-- Atendimentos por Forma de Pagamento -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold">Atendimentos por Forma de Pagamento</h2>
            <div class="flex items-center">
                <select id="periodoForma" class="text-xs border-systemGray-lightest rounded-control mr-2">
                    <option value="valor" selected>Por Valor</option>
                    <option value="quantidade">Por Quantidade</option>
                </select>
            </div>
        </div>
        <div class="h-64">
            <canvas id="formaPagamentoChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> Convênios representam 58% do faturamento. Considere renegociação de tabelas para os principais convênios.</p>
        </div>
    </div>
</div>

<!-- Tendências e Previsões -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Tendência de Atendimentos -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold">Tendência de Atendimentos</h2>
            <div class="flex items-center">
                <svg class="w-4 h-4 text-systemBlue mr-1" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 1L9 9H2L7 14.5L5 22L12 17.5L19 22L17 14.5L22 9H15L12 1Z"></path>
                </svg>
                <span class="text-xs text-systemBlue font-medium">ML Forecast</span>
            </div>
        </div>
        <div class="h-64">
            <canvas id="tendenciaChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Previsão:</strong> Crescimento de 12% nos próximos 3 meses, com pico esperado em Outubro.</p>
        </div>
    </div>

    <!-- Análise de Retenção -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold">Análise de Retenção de Pacientes</h2>
            <div class="flex items-center">
                <svg class="w-4 h-4 text-systemBlue mr-1" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 1L9 9H2L7 14.5L5 22L12 17.5L19 22L17 14.5L22 9H15L12 1Z"></path>
                </svg>
                <span class="text-xs text-systemBlue font-medium">Cohort Analysis</span>
            </div>
        </div>
        <div class="h-64">
            <canvas id="retencaoChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> 72% dos pacientes retornam após o primeiro atendimento. Implementar programa de fidelização pode aumentar este índice.</p>
        </div>
    </div>
</div>

<!-- Visualizações e Análises -->
<div class="flex mb-3 gap-2">
    <button id="btn-tabela" class="bg-systemBlue text-white px-3 py-1.5 rounded-md text-xs font-medium transition-colors">
        <svg class="w-3.5 h-3.5 inline-block mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
        </svg>
        Tabela
    </button>
    <button id="btn-graficos" class="bg-white hover:bg-gray-50 text-gray-700 px-3 py-1.5 rounded-md text-xs font-medium border border-gray-200 transition-colors">
        <svg class="w-3.5 h-3.5 inline-block mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
        </svg>
        Gráficos
    </button>
    <button id="btn-analise" class="bg-white hover:bg-gray-50 text-gray-700 px-3 py-1.5 rounded-md text-xs font-medium border border-gray-200 transition-colors">
        <svg class="w-3.5 h-3.5 inline-block mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
        </svg>
        Análise
    </button>
</div>

<!-- Tabela de Atendimentos -->
<div class="bg-white rounded-lg shadow-sm p-4 mb-5">
    <div class="flex justify-between items-center mb-3">
        <div class="flex items-center">
            <svg class="w-4 h-4 text-gray-500 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
            </svg>
            <h2 class="text-sm font-medium text-gray-700">Detalhamento dos Atendimentos</h2>
        </div>
        <div class="flex gap-2">
            <button class="bg-white hover:bg-gray-50 text-gray-700 px-2 py-1 rounded text-xs border border-gray-200 transition-colors flex items-center">
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                </svg>
                Exportar
            </button>
            <button class="bg-white hover:bg-gray-50 text-gray-700 px-2 py-1 rounded text-xs border border-gray-200 transition-colors flex items-center">
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                </svg>
                Imprimir
            </button>
        </div>
    </div>

    <div class="overflow-x-auto">
        <table class="w-full text-xs">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Código</th>
                    <th class="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Data</th>
                    <th class="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Paciente</th>
                    <th class="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Profissional</th>
                    <th class="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Unidade</th>
                    <th class="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Tipo</th>
                    <th class="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Procedimento</th>
                    <th class="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Pagamento</th>
                    <th class="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">Valor</th>
                </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
                {% for atendimento in atendimentos[:8] %}
                <tr class="hover:bg-gray-50">
                    <td class="px-3 py-2 text-gray-800">{{ atendimento.codigo }}</td>
                    <td class="px-3 py-2 text-gray-800">{{ atendimento.data }}</td>
                    <td class="px-3 py-2 text-gray-800">{{ atendimento.paciente }}</td>
                    <td class="px-3 py-2 text-gray-800">{{ atendimento.profissional }}</td>
                    <td class="px-3 py-2 text-gray-800">{{ atendimento.unidade }}</td>
                    <td class="px-3 py-2 text-gray-800">{{ atendimento.tipo_atendimento }}</td>
                    <td class="px-3 py-2 text-gray-800">{{ atendimento.procedimento }}</td>
                    <td class="px-3 py-2 text-gray-800">{{ atendimento.forma_pagamento }}</td>
                    <td class="px-3 py-2 text-gray-800">R$ {{ "%.2f"|format(atendimento.valor) }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Paginação -->
    <div class="flex justify-between items-center mt-3 text-xs">
        <div class="text-gray-500">
            Mostrando 8 de {{ atendimentos|length }} atendimentos
        </div>
        <div class="flex gap-2">
            <button class="px-2 py-1 rounded bg-white border border-gray-200 text-gray-500 hover:bg-gray-50 transition-colors">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>
            <button class="px-2 py-1 rounded bg-systemBlue text-white">1</button>
            <button class="px-2 py-1 rounded bg-white border border-gray-200 text-gray-500 hover:bg-gray-50 transition-colors">2</button>
            <button class="px-2 py-1 rounded bg-white border border-gray-200 text-gray-500 hover:bg-gray-50 transition-colors">3</button>
            <button class="px-2 py-1 rounded bg-white border border-gray-200 text-gray-500 hover:bg-gray-50 transition-colors">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Calcular atendimentos por tipo
        const tipoData = {};

        {% for atendimento in atendimentos %}
        if ('{{ atendimento.tipo_atendimento }}' in tipoData) {
            tipoData['{{ atendimento.tipo_atendimento }}'] += 1;
        } else {
            tipoData['{{ atendimento.tipo_atendimento }}'] = 1;
        }
        {% endfor %}

        const tipoLabels = Object.keys(tipoData);
        const tipoValues = Object.values(tipoData);

        // Calcular atendimentos por forma de pagamento
        const formaData = {};
        const formaQtdData = {};

        {% for atendimento in atendimentos %}
        if ('{{ atendimento.forma_pagamento }}' in formaData) {
            formaData['{{ atendimento.forma_pagamento }}'] += {{ atendimento.valor }};
            formaQtdData['{{ atendimento.forma_pagamento }}'] += 1;
        } else {
            formaData['{{ atendimento.forma_pagamento }}'] = {{ atendimento.valor }};
            formaQtdData['{{ atendimento.forma_pagamento }}'] = 1;
        }
        {% endfor %}

        const formaLabels = Object.keys(formaData);
        const formaValues = Object.values(formaData);
        const formaQtdValues = Object.values(formaQtdData);

        // Dados para tendência de atendimentos
        // Simulação de dados históricos mensais
        const meses = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set'];
        const atendimentosHistoricos = [120, 135, 128, 142, 150, 165, 172, 180, 195];

        // Previsão para os próximos meses
        const mesesFuturos = ['Out', 'Nov', 'Dez'];
        const atendimentosPrevistos = [210, 225, 235];

        // Dados para análise de retenção
        const cohorts = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun'];
        const retencaoData = [
            [100, 72, 65, 58, 52, 48],  // % de retenção da cohort de Janeiro
            [100, 75, 68, 60, 55, null],
            [100, 70, 62, 55, null, null],
            [100, 73, 65, null, null, null],
            [100, 76, null, null, null, null],
            [100, null, null, null, null, null]
        ];

        // Configurar gráfico de atendimentos por tipo
        const tipoChart = new Chart(
            document.getElementById('tipoAtendimentoChart'),
            {
                type: 'bar',
                data: {
                    labels: tipoLabels,
                    datasets: [{
                        label: 'Quantidade',
                        data: tipoValues,
                        backgroundColor: 'rgba(0, 122, 255, 0.7)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            }
        );

        // Configurar gráfico de atendimentos por forma de pagamento
        const formaPagamentoChart = new Chart(
            document.getElementById('formaPagamentoChart'),
            {
                type: 'pie',
                data: {
                    labels: formaLabels,
                    datasets: [{
                        data: formaValues,
                        backgroundColor: [
                            'rgba(0, 122, 255, 0.7)',
                            'rgba(142, 142, 147, 0.7)',
                            'rgba(174, 174, 178, 0.7)',
                            'rgba(199, 199, 204, 0.7)',
                            'rgba(209, 209, 214, 0.7)',
                            'rgba(229, 229, 234, 0.7)'
                        ],
                        borderColor: [
                            'rgba(0, 122, 255, 1)',
                            'rgba(142, 142, 147, 1)',
                            'rgba(174, 174, 178, 1)',
                            'rgba(199, 199, 204, 1)',
                            'rgba(209, 209, 214, 1)',
                            'rgba(229, 229, 234, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    return `${label}: R$ ${value.toFixed(2)}`;
                                }
                            }
                        }
                    }
                }
            }
        );

        // Configurar gráfico de tendência de atendimentos
        const tendenciaChart = new Chart(
            document.getElementById('tendenciaChart'),
            {
                type: 'line',
                data: {
                    labels: [...meses, ...mesesFuturos],
                    datasets: [
                        {
                            label: 'Histórico',
                            data: [...atendimentosHistoricos, null, null, null],
                            backgroundColor: 'rgba(0, 122, 255, 0.1)',
                            borderColor: 'rgba(0, 122, 255, 1)',
                            borderWidth: 2,
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: 'Previsão',
                            data: [null, null, null, null, null, null, null, null, atendimentosHistoricos[8], ...atendimentosPrevistos],
                            backgroundColor: 'rgba(0, 122, 255, 0.05)',
                            borderColor: 'rgba(0, 122, 255, 0.7)',
                            borderWidth: 2,
                            borderDash: [5, 5],
                            tension: 0.4,
                            fill: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.dataset.label || '';
                                    const value = context.raw || 0;
                                    return value ? `${label}: ${value} atendimentos` : '';
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            }
        );

        // Configurar gráfico de retenção
        const retencaoChart = new Chart(
            document.getElementById('retencaoChart'),
            {
                type: 'line',
                data: {
                    labels: ['Mês 1', 'Mês 2', 'Mês 3', 'Mês 4', 'Mês 5', 'Mês 6'],
                    datasets: [
                        {
                            label: 'Jan',
                            data: retencaoData[0],
                            borderColor: 'rgba(0, 122, 255, 1)',
                            backgroundColor: 'rgba(0, 0, 0, 0)',
                            borderWidth: 2
                        },
                        {
                            label: 'Fev',
                            data: retencaoData[1],
                            borderColor: 'rgba(142, 142, 147, 1)',
                            backgroundColor: 'rgba(0, 0, 0, 0)',
                            borderWidth: 2
                        },
                        {
                            label: 'Mar',
                            data: retencaoData[2],
                            borderColor: 'rgba(174, 174, 178, 1)',
                            backgroundColor: 'rgba(0, 0, 0, 0)',
                            borderWidth: 2
                        },
                        {
                            label: 'Abr',
                            data: retencaoData[3],
                            borderColor: 'rgba(199, 199, 204, 1)',
                            backgroundColor: 'rgba(0, 0, 0, 0)',
                            borderWidth: 2
                        },
                        {
                            label: 'Mai',
                            data: retencaoData[4],
                            borderColor: 'rgba(209, 209, 214, 1)',
                            backgroundColor: 'rgba(0, 0, 0, 0)',
                            borderWidth: 2
                        },
                        {
                            label: 'Jun',
                            data: retencaoData[5],
                            borderColor: 'rgba(142, 142, 147, 1)',
                            backgroundColor: 'rgba(0, 0, 0, 0)',
                            borderWidth: 2
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.dataset.label || '';
                                    const value = context.raw || 0;
                                    return value ? `${label}: ${value}%` : '';
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            min: 0,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            },
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            }
        );

        // Event listeners para os filtros
        document.getElementById('periodoTipo').addEventListener('change', function() {
            // Aqui seria implementada a lógica para atualizar o gráfico com base no período selecionado
            console.log('Período selecionado: ' + this.value);
        });

        document.getElementById('periodoForma').addEventListener('change', function() {
            // Alternar entre visualização por valor e por quantidade
            if (this.value === 'valor') {
                formaPagamentoChart.data.datasets[0].data = formaValues;
                formaPagamentoChart.options.plugins.tooltip.callbacks.label = function(context) {
                    const label = context.label || '';
                    const value = context.raw || 0;
                    return `${label}: R$ ${value.toFixed(2)}`;
                };
            } else {
                formaPagamentoChart.data.datasets[0].data = formaQtdValues;
                formaPagamentoChart.options.plugins.tooltip.callbacks.label = function(context) {
                    const label = context.label || '';
                    const value = context.raw || 0;
                    return `${label}: ${value} atendimentos`;
                };
            }
            formaPagamentoChart.update();
        });
    });
</script>
{% endblock %}

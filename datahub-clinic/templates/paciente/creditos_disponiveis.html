{% extends 'base.html' %}

{% block title %}Créditos Disponíveis - Amigo DataHub{% endblock %}

{% block header %}Créditos Disponíveis{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="bg-gradient-to-r from-blue-50 to-white rounded-view p-8 border border-gray-200 mb-6 overflow-hidden relative">
    <!-- Background Elements -->
    <div class="absolute top-0 right-0 w-full h-full opacity-10 overflow-hidden">
        <svg class="absolute top-10 right-10 w-64 h-64 text-systemBlue" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <svg class="absolute bottom-10 left-1/4 w-32 h-32 text-systemBlue" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M2.25 18.75a60.07 60.07 0 0115.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 013 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 00-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 01-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 003 15h-.75M15 10.5a3 3 0 11-6 0 3 3 0 016 0zm3 0h.008v.008H18V10.5zm-12 0h.008v.008H6V10.5z"></path>
        </svg>
    </div>

    <div class="flex flex-col md:flex-row items-center relative z-10">
        <div class="md:w-2/3 mb-6 md:mb-0 md:pr-8">
            <div class="inline-block bg-systemBlue text-white text-xs font-semibold px-3 py-1 rounded-full mb-3">PACIENTE</div>
            <h1 class="text-3xl font-bold text-gray-800 mb-4">Gestão de Créditos Disponíveis</h1>
            <p class="text-gray-600 mb-6">Acompanhe e gerencie os créditos disponíveis dos pacientes. Visualize saldos, monitore a utilização e identifique oportunidades para otimizar o uso dos créditos existentes.</p>

            {% set total_creditos = creditos|sum(attribute='credito_disponivel') %}
            {% set num_pacientes = creditos|length %}
            {% set credito_medio = total_creditos / num_pacientes if num_pacientes > 0 else 0 %}

            <div class="grid grid-cols-3 gap-4 mb-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">
                        R$ {{ "%.0f"|format(total_creditos) }}
                    </div>
                    <div class="text-xs text-gray-500">Total de créditos</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">
                        {{ num_pacientes }}
                    </div>
                    <div class="text-xs text-gray-500">Pacientes com crédito</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">
                        R$ {{ "%.0f"|format(credito_medio) }}
                    </div>
                    <div class="text-xs text-gray-500">Crédito médio</div>
                </div>
            </div>
            <div class="flex flex-wrap gap-3">
                <button class="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Adicionar Crédito
                </button>
                <button class="bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-md text-sm border border-gray-200 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                    </svg>
                    Exportar Relatório
                </button>
            </div>
        </div>
        <div class="md:w-1/3 flex justify-center">
            <div class="w-64 h-64 bg-white rounded-full shadow-lg flex items-center justify-center p-4 relative">
                <div class="absolute inset-0 rounded-full border-8 border-blue-100"></div>
                <div class="absolute inset-0 rounded-full border-8 border-systemBlue border-opacity-70" style="clip-path: polygon(50% 50%, 100% 0, 100% 50%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-300 border-opacity-70" style="clip-path: polygon(50% 50%, 100% 50%, 100% 100%, 50% 100%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-200 border-opacity-70" style="clip-path: polygon(50% 50%, 50% 100%, 0 100%, 0 50%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-400 border-opacity-70" style="clip-path: polygon(50% 50%, 0 50%, 0 0, 50% 0);"></div>
                <div class="z-10 text-center">
                    <div class="text-3xl font-bold text-gray-800">65%</div>
                    <div class="text-sm text-gray-500">Taxa de utilização</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Insights com IA -->
<div class="mb-5">
    <div class="flex items-center justify-between mb-4">
        <div class="flex items-center">
            <svg class="w-5 h-5 text-systemBlue mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
            </svg>
            <h2 class="text-base font-semibold text-gray-800">Amigo Intelligence</h2>
        </div>
        <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">Atualizado agora</span>
    </div>

    <div id="insights-container" class="grid grid-cols-1 md:grid-cols-3 gap-3">
        {% for insight in insights %}
            {% with
                title=insight.title,
                description=insight.description,
                insight_type=insight.insight_type,
                content=insight.content,
                category=insight.category
            %}
                {% include 'components/ai_insight_widget.html' %}
            {% endwith %}
        {% endfor %}
    </div>
</div>

<script>
    // Carregar insights dinamicamente após 5 segundos (para demonstração)
    setTimeout(() => {
        // Contexto específico da página de créditos disponíveis
        const pageContext = {
            page_title: "Créditos Disponíveis",
            page_description: "Análise e gestão dos créditos disponíveis dos pacientes, com foco em ativação e otimização de uso",
            key_metrics: {
                "Total de Créditos": "R$ {{ '%.2f'|format(total_creditos) }}",
                "Pacientes com Crédito": "{{ num_pacientes }}",
                "Crédito Médio": "R$ {{ '%.2f'|format(credito_medio) }}",
                "Taxa de Utilização": "65%"
            },
            analysis_focus: "Ativação de créditos e otimização de uso",
            page_elements: [
                "Distribuição de Créditos",
                "Detalhamento dos Créditos"
            ]
        };

        loadInsights('paciente', 'creditos_disponiveis', 'insights-container', 3, ['list', 'text', 'stat'], pageContext);
    }, 5000);
</script>

<!-- Insights Estáticos (Temporários) -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6 hidden">
    <!-- Insight de Utilização -->
    {% with
        title="Padrões de Utilização",
        description="Análise de comportamento",
        insight_type="list",
        content=[
            "Pacientes utilizam <strong>65%</strong> dos créditos em até 90 dias",
            "<strong>22%</strong> dos créditos permanecem inativos por mais de 6 meses",
            "Pacientes com planos têm <strong>3x mais</strong> probabilidade de utilizar todos os créditos"
        ],
        category="Análise",
        action_text="Ver detalhes"
    %}
        {% include 'components/ai_insight_widget.html' %}
    {% endwith %}

    <!-- Insight de Oportunidades -->
    {% with
        title="Oportunidades de Conversão",
        description="Recomendações para ativação",
        insight_type="list",
        content=[
            "Lembrete sobre créditos prestes a expirar aumenta uso em <strong>42%</strong>",
            "Oferecer <strong>bônus de 10%</strong> para uso em 30 dias tem alta aceitação",
            "Sugerir procedimentos complementares converte <strong>28%</strong> dos créditos inativos"
        ],
        category="Oportunidade",
        action_text="Ver estratégias"
    %}
        {% include 'components/ai_insight_widget.html' %}
    {% endwith %}

    <!-- Insight de Previsão -->
    {% with
        title="Previsão de Expiração",
        description="Créditos em risco",
        insight_type="text",
        content="Identificamos <strong>R$ {{ (total_creditos * 0.18)|round|int }}</strong> em créditos com risco de expiração nos próximos 30 dias, distribuídos entre <strong>{{ (num_pacientes * 0.15)|round|int }}</strong> pacientes. Implementar uma campanha de ativação pode recuperar até <strong>75%</strong> desses créditos e aumentar a satisfação do cliente.",
        category="Alerta",
        action_text="Ver pacientes"
    %}
        {% include 'components/ai_insight_widget.html' %}
    {% endwith %}
</div>

<!-- Resumo -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
    <!-- Total de Créditos -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-gray-900">Total de Créditos</p>
        </div>
        <p class="text-3xl font-semibold text-systemGreen mb-1">
            R$ {{ "%.2f"|format(creditos|sum(attribute='credito_disponivel')) }}
        </p>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Valor total de créditos disponíveis.</p>
    </div>

    <!-- Pacientes com Crédito -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-gray-900">Pacientes com Crédito</p>
        </div>
        <p class="text-3xl font-semibold text-gray-900 mb-1">{{ creditos|length }}</p>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Número de pacientes com crédito disponível.</p>
    </div>

    <!-- Crédito Médio -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-gray-900">Crédito Médio</p>
        </div>
        <p class="text-3xl font-semibold text-gray-900 mb-1">
            R$ {{ "%.2f"|format((creditos|sum(attribute='credito_disponivel')) / (creditos|length if creditos|length > 0 else 1)) }}
        </p>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Valor médio de crédito por paciente.</p>
    </div>
</div>

<!-- Filtros -->
<div class="bg-white rounded-view p-4 mb-6 border border-gray-200">
    <div class="flex flex-wrap gap-4">
        <div class="w-full md:w-auto">
            <label for="paciente" class="block text-xs font-medium text-label-secondary mb-1">Paciente</label>
            <input type="text" id="paciente" placeholder="Nome do paciente" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
        </div>
        <div class="w-full md:w-auto">
            <label for="valor_minimo" class="block text-xs font-medium text-label-secondary mb-1">Valor Mínimo</label>
            <input type="number" id="valor_minimo" placeholder="0.00" min="0" step="0.01" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
        </div>
        <div class="w-full md:w-auto">
            <label for="data_atualizacao" class="block text-xs font-medium text-label-secondary mb-1">Data de Atualização</label>
            <input type="date" id="data_atualizacao" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
        </div>
        <div class="w-full md:w-auto flex items-end">
            <button class="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-control transition duration-150 ease-in-out text-sm">
                Filtrar
            </button>
        </div>
    </div>
</div>

<!-- Gráfico -->
<div class="bg-white rounded-view p-6 border border-gray-200 mb-6">
    <h2 class="text-base font-semibold mb-4">Distribuição de Créditos</h2>
    <div class="h-80">
        <canvas id="creditosChart"></canvas>
    </div>
</div>

<!-- Tabela de Créditos -->
<div class="bg-white rounded-view p-6 border border-gray-200">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-base font-semibold">Detalhamento dos Créditos</h2>
        <div class="flex space-x-2">
            <button class="bg-systemGray-ultralight hover:bg-systemGray-extralight text-gray-800 px-3 py-1 rounded-full transition duration-150 ease-in-out text-xs border border-systemGray-lightest">
                Exportar
            </button>
            <button class="bg-systemGray-ultralight hover:bg-systemGray-extralight text-gray-800 px-3 py-1 rounded-full transition duration-150 ease-in-out text-xs border border-systemGray-lightest">
                Imprimir
            </button>
        </div>
    </div>

    <div class="overflow-x-auto">
        <table class="w-full text-sm data-table">
            <thead>
                <tr>
                    <th>Paciente</th>
                    <th class="text-right">Crédito Disponível</th>
                    <th>Última Atualização</th>
                    <th>Ações</th>
                </tr>
            </thead>
            <tbody>
                {% for credito in creditos %}
                <tr>
                    <td>{{ credito.paciente }}</td>
                    <td class="text-right">R$ {{ "%.2f"|format(credito.credito_disponivel) }}</td>
                    <td>{{ credito.ultima_atualizacao }}</td>
                    <td>
                        <div class="flex space-x-1">
                            <button class="p-1 text-systemBlue hover:bg-systemGray-ultralight rounded">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                            </button>
                            <button class="p-1 text-systemBlue hover:bg-systemGray-ultralight rounded">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                            </button>
                            <button class="p-1 text-systemBlue hover:bg-systemGray-ultralight rounded">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </button>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Paginação -->
    <div class="flex justify-between items-center mt-4">
        <div class="text-sm text-label-secondary">
            Mostrando 1-20 de {{ creditos|length }} resultados
        </div>
        <div class="flex space-x-1">
            <button class="px-3 py-1 rounded-md bg-systemGray-ultralight text-label-secondary text-sm">Anterior</button>
            <button class="px-3 py-1 rounded-md bg-systemBlue text-white text-sm">1</button>
            <button class="px-3 py-1 rounded-md bg-systemGray-ultralight text-label-secondary text-sm">Próximo</button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Preparar dados para o gráfico
        const pacientes = [];
        const valores = [];

        {% for credito in creditos %}
        pacientes.push('{{ credito.paciente }}');
        valores.push({{ credito.credito_disponivel }});
        {% endfor %}

        // Ordenar por valor (do maior para o menor)
        const indices = Array.from(Array(pacientes.length).keys())
            .sort((a, b) => valores[b] - valores[a]);

        // Pegar os 10 maiores valores
        const top10Pacientes = indices.slice(0, 10).map(i => pacientes[i]);
        const top10Valores = indices.slice(0, 10).map(i => valores[i]);

        // Configurar gráfico
        const creditosChart = new Chart(
            document.getElementById('creditosChart'),
            {
                type: 'bar',
                data: {
                    labels: top10Pacientes,
                    datasets: [{
                        label: 'Crédito Disponível (R$)',
                        data: top10Valores,
                        backgroundColor: 'rgba(0, 122, 255, 0.7)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        title: {
                            display: true,
                            text: 'Top 10 Pacientes com Maior Crédito'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            }
        );
    });
</script>
{% endblock %}

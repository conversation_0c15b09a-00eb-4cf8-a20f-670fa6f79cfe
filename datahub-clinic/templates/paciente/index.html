{% extends 'base.html' %}

{% block title %}<PERSON><PERSON><PERSON><PERSON> - Amigo DataApp{% endblock %}

{% block header %}<PERSON><PERSON><PERSON><PERSON>{% endblock %}

{% block content %}
<!-- Hero do módulo -->
<div class="bg-white rounded-view p-8 mb-8 border border-gray-200">
    <div class="flex flex-col md:flex-row items-center">
        <div class="md:w-2/3">
            <h1 class="text-3xl font-bold text-gray-900 mb-2"><PERSON><PERSON><PERSON><PERSON></h1>
            <p class="text-lg text-label-secondary mb-6">Acompanhe atendimentos realizados, créditos disponíveis e gerencie orçamentos.</p>
            <div class="flex space-x-4">
                <a href="{{ url_for('atendimentos_realizados') }}" class="bg-systemBlue hover:bg-blue-600 text-white px-6 py-2 rounded-full transition duration-150 ease-in-out">
                    Atendimentos
                </a>
                <a href="{{ url_for('orcamentos_abertos') }}" class="bg-systemGray-ultralight hover:bg-systemGray-extralight text-gray-800 px-6 py-2 rounded-full transition duration-150 ease-in-out border border-systemGray-lightest">
                    Orçamentos
                </a>
            </div>
        </div>
        <div class="md:w-1/3 mt-6 md:mt-0 flex justify-center">
            <div class="w-48 h-48 bg-blue-100 rounded-full flex items-center justify-center">
                <svg class="w-24 h-24 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
            </div>
        </div>
    </div>
</div>

<!-- Resumo do módulo -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total de Atendimentos -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-gray-900">Total de Atendimentos</p>
        </div>
        <p class="text-3xl font-semibold text-gray-900 mb-1">{{ dados.atendimentos_realizados|length }}</p>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Atendimentos realizados nos últimos 30 dias.</p>
    </div>

    <!-- Créditos Disponíveis -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-gray-900">Créditos Disponíveis</p>
        </div>
        <p class="text-3xl font-semibold text-systemGreen mb-1">
            R$ {{ "%.2f"|format(dados.creditos_disponiveis|sum(attribute='credito_disponivel')) }}
        </p>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Total de créditos disponíveis para pacientes.</p>
    </div>

    <!-- Orçamentos Fechados -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-gray-900">Orçamentos Fechados</p>
        </div>
        <p class="text-3xl font-semibold text-gray-900 mb-1">{{ dados.orcamentos_fechados|length }}</p>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Orçamentos aprovados nos últimos 30 dias.</p>
    </div>

    <!-- Orçamentos Abertos -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-gray-900">Orçamentos Abertos</p>
        </div>
        <p class="text-3xl font-semibold text-gray-900 mb-1">{{ dados.orcamentos_abertos|length }}</p>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Orçamentos em análise ou aguardando aprovação.</p>
    </div>
</div>

<!-- Card de Insight Flutuante - Jornada do Paciente -->
<div class="bg-white rounded-view p-5 border border-gray-200 mb-8 relative overflow-hidden">
    <div class="absolute top-0 right-0 w-64 h-64 bg-blue-50 rounded-full opacity-20 -mt-20 -mr-20"></div>

    <div class="relative z-10">
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
                <svg class="w-5 h-5 text-systemBlue mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
                </svg>
                <span class="text-base font-semibold text-gray-800">Jornada do Paciente Otimizada</span>
            </div>
            <span class="inline-block bg-blue-100 text-blue-700 text-xs font-medium px-2 py-0.5 rounded-full">Retenção</span>
        </div>

        <div class="flex flex-col md:flex-row gap-6 mb-4">
            <div class="md:w-2/3">
                <div class="relative">
                    <div class="absolute left-4 top-0 bottom-0 w-0.5 bg-blue-100"></div>

                    <div class="relative pl-10 pb-5">
                        <div class="absolute left-2 w-5 h-5 rounded-full bg-blue-500 flex items-center justify-center">
                            <span class="text-white text-xs font-bold">1</span>
                        </div>
                        <h3 class="text-sm font-medium text-gray-800 mb-1">Primeira Consulta</h3>
                        <p class="text-xs text-gray-600">32% dos pacientes não retornam após a primeira consulta. <strong>Oportunidade:</strong> Implementar contato de follow-up em 48h.</p>
                    </div>

                    <div class="relative pl-10 pb-5">
                        <div class="absolute left-2 w-5 h-5 rounded-full bg-blue-400 flex items-center justify-center">
                            <span class="text-white text-xs font-bold">2</span>
                        </div>
                        <h3 class="text-sm font-medium text-gray-800 mb-1">Apresentação de Orçamento</h3>
                        <p class="text-xs text-gray-600">Orçamentos com opções de parcelamento têm conversão 45% maior. <strong>Oportunidade:</strong> Padronizar apresentação com múltiplas opções de pagamento.</p>
                    </div>

                    <div class="relative pl-10">
                        <div class="absolute left-2 w-5 h-5 rounded-full bg-blue-300 flex items-center justify-center">
                            <span class="text-white text-xs font-bold">3</span>
                        </div>
                        <h3 class="text-sm font-medium text-gray-800 mb-1">Pós-Tratamento</h3>
                        <p class="text-xs text-gray-600">Envio de resultados por WhatsApp aumenta retenção em 28%. <strong>Oportunidade:</strong> Automatizar envio de fotos comparativas e dicas personalizadas.</p>
                    </div>
                </div>
            </div>

            <div class="md:w-1/3 bg-blue-50 p-4 rounded-lg">
                <h3 class="text-sm font-medium text-gray-800 mb-2">Impacto Projetado</h3>
                <div class="space-y-3">
                    <div>
                        <div class="flex justify-between text-xs mb-1">
                            <span class="text-gray-600">Retenção de pacientes</span>
                            <span class="font-medium text-systemBlue">+24%</span>
                        </div>
                        <div class="h-1.5 bg-gray-100 rounded-full overflow-hidden">
                            <div class="h-full bg-systemBlue" style="width: 24%"></div>
                        </div>
                    </div>

                    <div>
                        <div class="flex justify-between text-xs mb-1">
                            <span class="text-gray-600">Conversão de orçamentos</span>
                            <span class="font-medium text-systemBlue">+38%</span>
                        </div>
                        <div class="h-1.5 bg-gray-100 rounded-full overflow-hidden">
                            <div class="h-full bg-systemBlue" style="width: 38%"></div>
                        </div>
                    </div>

                    <div>
                        <div class="flex justify-between text-xs mb-1">
                            <span class="text-gray-600">Ticket médio</span>
                            <span class="font-medium text-systemBlue">+15%</span>
                        </div>
                        <div class="h-1.5 bg-gray-100 rounded-full overflow-hidden">
                            <div class="h-full bg-systemBlue" style="width: 15%"></div>
                        </div>
                    </div>
                </div>

                <div class="mt-3">
                    <button class="w-full bg-systemBlue hover:bg-blue-600 text-white px-3 py-1.5 rounded text-xs transition-colors">
                        Implementar Estratégia
                    </button>
                </div>
            </div>
        </div>

        <div class="mt-2 pt-2 border-t border-gray-100 flex justify-between items-center">
            <p class="text-xs text-gray-500">Análise baseada em 1.875 pacientes nos últimos 6 meses</p>
            <span class="text-xs text-gray-500">Powered by Amigo Intelligence</span>
        </div>
    </div>
</div>

<!-- Insights com IA -->
<div class="mb-8">
    <div class="flex items-center justify-between mb-4">
        <div class="flex items-center">
            <svg class="w-5 h-5 text-systemBlue mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
            </svg>
            <h2 class="text-base font-semibold text-gray-800">Amigo Intelligence</h2>
        </div>
        <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">Atualizado agora</span>
    </div>

    <div id="insights-container" class="grid grid-cols-1 md:grid-cols-3 gap-3">
        {% for insight in insights %}
            {% with
                title=insight.title,
                description=insight.description,
                insight_type=insight.insight_type,
                content=insight.content,
                category=insight.category,
                action_text=insight.action_text,
                action_url=insight.action_url
            %}
                {% include 'components/ai_insight_widget.html' %}
            {% endwith %}
        {% endfor %}
    </div>
</div>

<script>
    // Carregar insights dinamicamente após 5 segundos (para demonstração)
    setTimeout(() => {
        // Contexto específico da página de índice de paciente
        const pageContext = {
            page_title: "Módulo Paciente",
            page_description: "Visão geral do módulo de paciente, com foco em retenção e conversão de orçamentos",
            key_metrics: {
                "Total de Atendimentos": "{{ dados.atendimentos_realizados|length }}",
                "Créditos Disponíveis": "R$ {{ '%.2f'|format(dados.creditos_disponiveis|sum(attribute='credito_disponivel')) }}",
                "Orçamentos Fechados": "{{ dados.orcamentos_fechados|length }}",
                "Orçamentos Abertos": "{{ dados.orcamentos_abertos|length }}"
            },
            analysis_focus: "Retenção de pacientes e conversão de orçamentos",
            page_elements: [
                "Resumo do módulo",
                "Relatórios disponíveis"
            ]
        };

        loadInsights('paciente', 'index', 'insights-container', 3, ['list', 'text', 'stat'], pageContext);
    }, 5000);

    // Inicializar gráficos para os cards de insights
    document.addEventListener('DOMContentLoaded', function() {
        // Gráfico de Retenção de Pacientes
        if (document.getElementById('retentionChart')) {
            const retentionCtx = document.getElementById('retentionChart').getContext('2d');
            new Chart(retentionCtx, {
                type: 'bar',
                data: {
                    labels: ['Sem Follow-up', 'Com Follow-up', 'Sem WhatsApp', 'Com WhatsApp'],
                    datasets: [{
                        label: 'Taxa de Retorno (%)',
                        data: [28, 70, 42, 54],
                        backgroundColor: [
                            'rgba(239, 68, 68, 0.7)',
                            'rgba(79, 70, 229, 0.7)',
                            'rgba(239, 68, 68, 0.7)',
                            'rgba(79, 70, 229, 0.7)'
                        ],
                        borderColor: [
                            'rgba(239, 68, 68, 1)',
                            'rgba(79, 70, 229, 1)',
                            'rgba(239, 68, 68, 1)',
                            'rgba(79, 70, 229, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                },
                                font: {
                                    size: 9
                                }
                            }
                        },
                        x: {
                            ticks: {
                                font: {
                                    size: 9
                                }
                            }
                        }
                    }
                }
            });
        }

        // Gráfico de Perfil de Pacientes
        if (document.getElementById('patientProfileChart')) {
            const patientProfileCtx = document.getElementById('patientProfileChart').getContext('2d');
            new Chart(patientProfileCtx, {
                type: 'doughnut',
                data: {
                    labels: ['35-55 anos', '18-34 anos', '56+ anos', '<18 anos'],
                    datasets: [{
                        data: [68, 18, 12, 2],
                        backgroundColor: [
                            'rgba(249, 115, 22, 0.8)',
                            'rgba(251, 146, 60, 0.7)',
                            'rgba(253, 186, 116, 0.6)',
                            'rgba(254, 215, 170, 0.5)'
                        ],
                        borderColor: [
                            'rgba(249, 115, 22, 1)',
                            'rgba(251, 146, 60, 1)',
                            'rgba(253, 186, 116, 1)',
                            'rgba(254, 215, 170, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                boxWidth: 10,
                                font: {
                                    size: 9
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed !== null) {
                                        label += context.parsed + '% da receita';
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            });
        }

        // Gráfico de Cross-Selling
        if (document.getElementById('crossSellingChart')) {
            const crossSellingCtx = document.getElementById('crossSellingChart').getContext('2d');
            new Chart(crossSellingCtx, {
                type: 'radar',
                data: {
                    labels: ['Ortodontia', 'Implantes', 'Estética', 'Clínica Geral', 'Endodontia', 'Periodontia'],
                    datasets: [{
                        label: 'Probabilidade de Cross-Selling (%)',
                        data: [72, 65, 58, 45, 52, 48],
                        backgroundColor: 'rgba(16, 185, 129, 0.2)',
                        borderColor: 'rgba(16, 185, 129, 0.8)',
                        borderWidth: 2,
                        pointBackgroundColor: 'rgba(16, 185, 129, 1)',
                        pointRadius: 4
                    }, {
                        label: 'Ticket Médio Adicional (R$)',
                        data: [1200, 2500, 950, 350, 800, 650],
                        backgroundColor: 'rgba(79, 70, 229, 0.2)',
                        borderColor: 'rgba(79, 70, 229, 0.8)',
                        borderWidth: 2,
                        pointBackgroundColor: 'rgba(79, 70, 229, 1)',
                        pointRadius: 4,
                        hidden: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        r: {
                            angleLines: {
                                display: true
                            },
                            suggestedMin: 0,
                            suggestedMax: 100,
                            ticks: {
                                stepSize: 20,
                                font: {
                                    size: 9
                                }
                            },
                            pointLabels: {
                                font: {
                                    size: 9
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                boxWidth: 10,
                                font: {
                                    size: 10
                                }
                            }
                        }
                    }
                }
            });
        }
    });
</script>

<!-- Grid de Insights de Pacientes -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
    <!-- Card 1: Análise de Retenção -->
    <div class="bg-white rounded-lg shadow-sm p-4 border-l-4 border-indigo-500">
        <div class="flex items-center justify-between mb-3">
            <div class="flex items-center">
                <svg class="w-4 h-4 text-indigo-500 mr-1.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
                </svg>
                <span class="text-sm font-medium text-gray-800">Análise de Retenção</span>
            </div>
            <span class="inline-block bg-indigo-50 text-indigo-600 text-xs font-medium px-2 py-0.5 rounded-full">Retenção</span>
        </div>

        <div class="mb-3">
            <div class="relative h-32">
                <canvas id="retentionChart" height="128"></canvas>
            </div>
        </div>

        <div class="bg-indigo-50 p-3 rounded text-xs text-gray-600">
            <p><strong>Insight:</strong> Pacientes que recebem ligação de follow-up têm <strong>2.5x mais chances</strong> de retornar. Envio de resultados por WhatsApp aumenta retenção em <strong>28%</strong>.</p>
        </div>

        <div class="mt-2 pt-2 border-t border-gray-100 flex justify-between items-center">
            <a href="{{ url_for('atendimentos_realizados') }}" class="text-xs text-indigo-600 hover:text-indigo-700 font-medium">
                Ver análise completa
            </a>
            <span class="text-xs text-gray-400">Amigo Intelligence</span>
        </div>
    </div>

    <!-- Card 2: Conversão de Orçamentos -->
    <div class="bg-white rounded-lg shadow-sm p-4 border-l-4 border-teal-500">
        <div class="flex items-center justify-between mb-3">
            <div class="flex items-center">
                <svg class="w-4 h-4 text-teal-500 mr-1.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 18L9 11.25l4.306 4.307a11.95 11.95 0 015.814-5.519l2.74-1.22m0 0l-5.94-2.28m5.94 2.28l-2.28 5.941" />
                </svg>
                <span class="text-sm font-medium text-gray-800">Conversão de Orçamentos</span>
            </div>
            <span class="inline-block bg-teal-50 text-teal-600 text-xs font-medium px-2 py-0.5 rounded-full">Conversão</span>
        </div>

        <div class="flex mb-3">
            <div class="w-1/2 pr-2">
                <div class="bg-gray-50 rounded-lg p-3 h-32 flex flex-col justify-center items-center">
                    <div class="w-16 h-16 relative mb-2">
                        <svg viewBox="0 0 36 36" class="w-full h-full">
                            <path d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" fill="none" stroke="#eee" stroke-width="3" />
                            <path d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" fill="none" stroke="#14b8a6" stroke-width="3" stroke-dasharray="45, 100" />
                            <text x="18" y="20.5" class="text-xs font-semibold" text-anchor="middle" fill="#0f766e">45%</text>
                        </svg>
                    </div>
                    <p class="text-xs text-gray-600 text-center">Aumento com opções de parcelamento</p>
                </div>
            </div>
            <div class="w-1/2 pl-2">
                <div class="bg-gray-50 rounded-lg p-3 h-32 flex flex-col justify-center items-center">
                    <div class="w-16 h-16 relative mb-2">
                        <svg viewBox="0 0 36 36" class="w-full h-full">
                            <path d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" fill="none" stroke="#eee" stroke-width="3" />
                            <path d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" fill="none" stroke="#14b8a6" stroke-width="3" stroke-dasharray="37, 100" />
                            <text x="18" y="20.5" class="text-xs font-semibold" text-anchor="middle" fill="#0f766e">37%</text>
                        </svg>
                    </div>
                    <p class="text-xs text-gray-600 text-center">Aumento com comparativo visual</p>
                </div>
            </div>
        </div>

        <div class="bg-teal-50 p-3 rounded text-xs text-gray-600">
            <p><strong>Recomendação:</strong> Implementar template de orçamento com múltiplas opções de pagamento e comparativo visual de "antes e depois" para tratamentos estéticos.</p>
        </div>

        <div class="mt-2 pt-2 border-t border-gray-100 flex justify-between items-center">
            <a href="{{ url_for('orcamentos_abertos') }}" class="text-xs text-teal-600 hover:text-teal-700 font-medium">
                Explorar estratégias
            </a>
            <span class="text-xs text-gray-400">Amigo Intelligence</span>
        </div>
    </div>

    <!-- Card 3: Perfil de Pacientes -->
    <div class="bg-white rounded-lg shadow-sm p-4 border-l-4 border-orange-500">
        <div class="flex items-center justify-between mb-3">
            <div class="flex items-center">
                <svg class="w-4 h-4 text-orange-500 mr-1.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M18 18.72a9.094 9.094 0 003.741-.479 3 3 0 00-4.682-2.72m.94 3.198l.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0112 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 016 18.719m12 0a5.971 5.971 0 00-.941-3.197m0 0A5.995 5.995 0 0012 12.75a5.995 5.995 0 00-5.058 2.772m0 0a3 3 0 00-4.681 2.72 8.986 8.986 0 003.74.477m.94-3.197a5.971 5.971 0 00-.94 3.197M15 6.75a3 3 0 11-6 0 3 3 0 016 0zm6 3a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0zm-13.5 0a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z" />
                </svg>
                <span class="text-sm font-medium text-gray-800">Perfil de Pacientes</span>
            </div>
            <span class="inline-block bg-orange-50 text-orange-600 text-xs font-medium px-2 py-0.5 rounded-full">Segmentação</span>
        </div>

        <div class="mb-3">
            <div class="relative h-32">
                <canvas id="patientProfileChart" height="128"></canvas>
            </div>
        </div>

        <div class="bg-orange-50 p-3 rounded text-xs text-gray-600">
            <p><strong>Insight:</strong> Pacientes entre 35-55 anos representam <strong>68%</strong> da receita total. Pacientes indicados têm <strong>ticket médio 35% maior</strong> que outros canais.</p>
        </div>

        <div class="mt-2 pt-2 border-t border-gray-100 flex justify-between items-center">
            <a href="{{ url_for('atendimentos_realizados') }}" class="text-xs text-orange-600 hover:text-orange-700 font-medium">
                Ver detalhes
            </a>
            <span class="text-xs text-gray-400">Amigo Intelligence</span>
        </div>
    </div>
</div>

<!-- Segunda linha de insights -->
<div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
    <!-- Card 4: Análise de Satisfação -->
    <div class="bg-white rounded-lg shadow-sm p-4 border-l-4 border-rose-500">
        <div class="flex items-center justify-between mb-3">
            <div class="flex items-center">
                <svg class="w-4 h-4 text-rose-500 mr-1.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12z" />
                </svg>
                <span class="text-sm font-medium text-gray-800">Análise de Satisfação</span>
            </div>
            <span class="inline-block bg-rose-50 text-rose-600 text-xs font-medium px-2 py-0.5 rounded-full">Experiência</span>
        </div>

        <div class="grid grid-cols-2 gap-3 mb-3">
            <div class="bg-gray-50 rounded-lg p-3">
                <p class="text-xs font-medium text-gray-700 mb-2">NPS por Especialidade</p>
                <div class="space-y-2">
                    <div>
                        <div class="flex justify-between text-xs mb-1">
                            <span>Ortodontia</span>
                            <span class="font-medium text-green-600">92</span>
                        </div>
                        <div class="h-1.5 bg-gray-200 rounded-full overflow-hidden">
                            <div class="h-full bg-green-500" style="width: 92%"></div>
                        </div>
                    </div>
                    <div>
                        <div class="flex justify-between text-xs mb-1">
                            <span>Implantodontia</span>
                            <span class="font-medium text-green-600">88</span>
                        </div>
                        <div class="h-1.5 bg-gray-200 rounded-full overflow-hidden">
                            <div class="h-full bg-green-500" style="width: 88%"></div>
                        </div>
                    </div>
                    <div>
                        <div class="flex justify-between text-xs mb-1">
                            <span>Estética</span>
                            <span class="font-medium text-green-600">95</span>
                        </div>
                        <div class="h-1.5 bg-gray-200 rounded-full overflow-hidden">
                            <div class="h-full bg-green-500" style="width: 95%"></div>
                        </div>
                    </div>
                    <div>
                        <div class="flex justify-between text-xs mb-1">
                            <span>Clínica Geral</span>
                            <span class="font-medium text-green-600">85</span>
                        </div>
                        <div class="h-1.5 bg-gray-200 rounded-full overflow-hidden">
                            <div class="h-full bg-green-500" style="width: 85%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-gray-50 rounded-lg p-3">
                <p class="text-xs font-medium text-gray-700 mb-2">Principais Feedbacks</p>
                <div class="space-y-2">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-5 h-5 bg-green-100 rounded-full flex items-center justify-center mr-2">
                            <svg class="w-3 h-3 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                        <p class="text-xs text-gray-600">Atendimento personalizado (92%)</p>
                    </div>
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-5 h-5 bg-green-100 rounded-full flex items-center justify-center mr-2">
                            <svg class="w-3 h-3 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                        <p class="text-xs text-gray-600">Pontualidade nos horários (88%)</p>
                    </div>
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-5 h-5 bg-red-100 rounded-full flex items-center justify-center mr-2">
                            <svg class="w-3 h-3 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </div>
                        <p class="text-xs text-gray-600">Tempo de espera (12%)</p>
                    </div>
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-5 h-5 bg-red-100 rounded-full flex items-center justify-center mr-2">
                            <svg class="w-3 h-3 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </div>
                        <p class="text-xs text-gray-600">Estacionamento (8%)</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-rose-50 p-3 rounded text-xs text-gray-600">
            <p><strong>Recomendação:</strong> Implementar sistema de confirmação de horários por WhatsApp para reduzir tempo de espera. Pacientes que recebem confirmação têm 78% menos reclamações sobre espera.</p>
        </div>

        <div class="mt-2 pt-2 border-t border-gray-100 flex justify-between items-center">
            <button class="text-xs text-rose-600 hover:text-rose-700 font-medium">
                Ver análise completa
            </button>
            <span class="text-xs text-gray-400">Amigo Intelligence</span>
        </div>
    </div>

    <!-- Card 5: Oportunidades de Cross-Selling -->
    <div class="bg-white rounded-lg shadow-sm p-4 border-l-4 border-emerald-500">
        <div class="flex items-center justify-between mb-3">
            <div class="flex items-center">
                <svg class="w-4 h-4 text-emerald-500 mr-1.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v12m-3-2.818l.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span class="text-sm font-medium text-gray-800">Oportunidades de Cross-Selling</span>
            </div>
            <span class="inline-block bg-emerald-50 text-emerald-600 text-xs font-medium px-2 py-0.5 rounded-full">Vendas</span>
        </div>

        <div class="mb-3">
            <div class="relative h-48">
                <canvas id="crossSellingChart" height="192"></canvas>
            </div>
        </div>

        <div class="bg-emerald-50 p-3 rounded text-xs text-gray-600">
            <p><strong>Insight:</strong> Pacientes de ortodontia têm 72% de probabilidade de aceitar tratamentos estéticos complementares quando oferecidos no momento certo. Pacientes de implantes têm 65% de probabilidade de aceitar planos de manutenção anual.</p>
        </div>

        <div class="mt-2 pt-2 border-t border-gray-100 flex justify-between items-center">
            <button class="text-xs text-emerald-600 hover:text-emerald-700 font-medium">
                Ver estratégias de cross-selling
            </button>
            <span class="text-xs text-gray-400">Amigo Intelligence</span>
        </div>
    </div>
</div>

<!-- Insights Estáticos (Temporários) - Escondidos -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8 hidden">
    <!-- Insight de Retenção -->
    {% with
        title="Análise de Retenção",
        description="Padrões de retorno de pacientes",
        insight_type="list",
        content=[
            "<strong>32%</strong> dos pacientes não retornam após a primeira consulta",
            "Pacientes que recebem ligação de follow-up têm <strong>2.5x mais chances</strong> de retornar",
            "Envio de resultados por WhatsApp aumenta retenção em <strong>28%</strong>"
        ],
        category="Retenção",
        action_text="Ver análise completa",
        action_url=url_for('atendimentos_realizados')
    %}
        {% include 'components/ai_insight_widget.html' %}
    {% endwith %}

    <!-- Insight de Conversão -->
    {% with
        title="Conversão de Orçamentos",
        description="Fatores que influenciam aprovação",
        insight_type="text",
        content="Orçamentos apresentados com <strong>opções de parcelamento</strong> têm taxa de conversão <strong>45% maior</strong>. Adicionar comparativo visual de 'antes e depois' em tratamentos estéticos aumenta aprovação em <strong>37%</strong>.",
        category="Conversão",
        action_text="Explorar estratégias",
        action_url=url_for('orcamentos_abertos')
    %}
        {% include 'components/ai_insight_widget.html' %}
    {% endwith %}

    <!-- Insight de Perfil -->
    {% with
        title="Perfil de Pacientes",
        description="Segmentação e oportunidades",
        insight_type="list",
        content=[
            "Pacientes entre 35-55 anos representam <strong>68%</strong> da receita total",
            "Pacientes indicados têm <strong>ticket médio 35% maior</strong> que outros canais",
            "Pacientes de planos premium têm <strong>4.5x mais</strong> procedimentos por ano"
        ],
        category="Segmentação",
        action_text="Ver detalhes",
        action_url=url_for('atendimentos_realizados')
    %}
        {% include 'components/ai_insight_widget.html' %}
    {% endwith %}
</div>

<!-- Relatórios disponíveis -->
<div class="bg-white rounded-view p-6 border border-gray-200">
    <h2 class="text-xl font-semibold mb-6">Relatórios Disponíveis</h2>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Atendimentos Realizados -->
        <a href="{{ url_for('atendimentos_realizados') }}" class="bg-systemGray-ultralight hover:bg-systemGray-extralight p-6 rounded-view border border-systemGray-lightest transition duration-150 ease-in-out">
            <div class="flex flex-col items-center text-center">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                    <svg class="w-8 h-8 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                    </svg>
                </div>
                <h3 class="text-base font-semibold mb-2">Atendimentos Realizados</h3>
                <p class="text-sm text-label-secondary">Histórico de atendimentos realizados.</p>
            </div>
        </a>

        <!-- Créditos Disponíveis -->
        <a href="{{ url_for('creditos_disponiveis') }}" class="bg-systemGray-ultralight hover:bg-systemGray-extralight p-6 rounded-view border border-systemGray-lightest transition duration-150 ease-in-out">
            <div class="flex flex-col items-center text-center">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                    <svg class="w-8 h-8 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                    </svg>
                </div>
                <h3 class="text-base font-semibold mb-2">Créditos Disponíveis</h3>
                <p class="text-sm text-label-secondary">Consulte os créditos disponíveis por paciente.</p>
            </div>
        </a>

        <!-- Orçamentos Fechados -->
        <a href="{{ url_for('orcamentos_fechados') }}" class="bg-systemGray-ultralight hover:bg-systemGray-extralight p-6 rounded-view border border-systemGray-lightest transition duration-150 ease-in-out">
            <div class="flex flex-col items-center text-center">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                    <svg class="w-8 h-8 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-base font-semibold mb-2">Orçamentos Fechados</h3>
                <p class="text-sm text-label-secondary">Consulte os orçamentos aprovados.</p>
            </div>
        </a>

        <!-- Orçamentos Abertos -->
        <a href="{{ url_for('orcamentos_abertos') }}" class="bg-systemGray-ultralight hover:bg-systemGray-extralight p-6 rounded-view border border-systemGray-lightest transition duration-150 ease-in-out">
            <div class="flex flex-col items-center text-center">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                    <svg class="w-8 h-8 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-base font-semibold mb-2">Orçamentos Abertos</h3>
                <p class="text-sm text-label-secondary">Gerencie os orçamentos em análise.</p>
            </div>
        </a>
    </div>
</div>
{% endblock %}

{% extends 'base.html' %}

{% block title %}Or<PERSON><PERSON>s Abertos - Amigo DataHub{% endblock %}

{% block header %}Orça<PERSON>s Abertos{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="bg-gradient-to-r from-blue-50 to-white rounded-view p-8 border border-gray-200 mb-6 overflow-hidden relative">
    <!-- Background Elements -->
    <div class="absolute top-0 right-0 w-full h-full opacity-10 overflow-hidden">
        <svg class="absolute top-10 right-10 w-64 h-64 text-systemBlue" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 14l6-6m-5.5.5h.01m4.99 5h.01M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16l3.5-2 3.5 2 3.5-2 3.5 2zM10 8.5a.5.5 0 11-1 0 .5.5 0 011 0zm5 5a.5.5 0 11-1 0 .5.5 0 011 0z"></path>
        </svg>
        <svg class="absolute bottom-10 left-1/4 w-32 h-32 text-systemBlue" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122"></path>
        </svg>
    </div>

    <div class="flex flex-col md:flex-row items-center relative z-10">
        <div class="md:w-2/3 mb-6 md:mb-0 md:pr-8">
            <div class="inline-block bg-systemBlue text-white text-xs font-semibold px-3 py-1 rounded-full mb-3">PACIENTE</div>
            <h1 class="text-3xl font-bold text-gray-800 mb-4">Orçamentos Abertos</h1>
            <p class="text-gray-600 mb-6">Acompanhe e gerencie todos os orçamentos em aberto da clínica. Monitore prazos de validade, identifique oportunidades de conversão e otimize sua taxa de fechamento com insights baseados em dados.</p>

            {% set total_orcamentos = orcamentos|length %}
            {% set valor_total = orcamentos|sum(attribute='valor_final') %}
            {% set valor_medio = valor_total / total_orcamentos if total_orcamentos > 0 else 0 %}

            <div class="grid grid-cols-3 gap-4 mb-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">
                        {{ total_orcamentos }}
                    </div>
                    <div class="text-xs text-gray-500">Total de orçamentos</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">
                        R$ {{ "%.0f"|format(valor_total) }}
                    </div>
                    <div class="text-xs text-gray-500">Valor total</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">
                        R$ {{ "%.0f"|format(valor_medio) }}
                    </div>
                    <div class="text-xs text-gray-500">Valor médio</div>
                </div>
            </div>
            <div class="flex flex-wrap gap-3">
                <button class="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                    </svg>
                    Acompanhar Status
                </button>
                <button class="bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-md text-sm border border-gray-200 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                    Enviar Lembretes
                </button>
            </div>
        </div>
        <div class="md:w-1/3 flex justify-center">
            <div class="w-64 h-64 bg-white rounded-full shadow-lg flex items-center justify-center p-4 relative">
                <div class="absolute inset-0 rounded-full border-8 border-blue-100"></div>
                <div class="absolute inset-0 rounded-full border-8 border-systemBlue border-opacity-70" style="clip-path: polygon(50% 50%, 100% 0, 100% 50%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-300 border-opacity-70" style="clip-path: polygon(50% 50%, 100% 50%, 100% 100%, 50% 100%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-200 border-opacity-70" style="clip-path: polygon(50% 50%, 50% 100%, 0 100%, 0 50%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-400 border-opacity-70" style="clip-path: polygon(50% 50%, 0 50%, 0 0, 50% 0);"></div>
                <div class="z-10 text-center">
                    <div class="text-3xl font-bold text-gray-800">68%</div>
                    <div class="text-sm text-gray-500">Taxa de conversão</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Insights com IA -->
<div class="mb-5">
    <div class="flex items-center justify-between mb-4">
        <div class="flex items-center">
            <svg class="w-5 h-5 text-systemBlue mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
            </svg>
            <h2 class="text-base font-semibold text-gray-800">Amigo Intelligence</h2>
        </div>
        <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">Atualizado agora</span>
    </div>

    <div id="insights-container" class="grid grid-cols-1 md:grid-cols-3 gap-3">
        {% for insight in insights %}
            {% with
                title=insight.title,
                description=insight.description,
                insight_type=insight.insight_type,
                content=insight.content,
                category=insight.category
            %}
                {% include 'components/ai_insight_widget.html' %}
            {% endwith %}
        {% endfor %}
    </div>
</div>

<script>
    // Carregar insights dinamicamente após 5 segundos (para demonstração)
    setTimeout(() => {
        // Contexto específico da página de orçamentos abertos
        const pageContext = {
            page_title: "Orçamentos Abertos",
            page_description: "Análise e gestão dos orçamentos em aberto da clínica, com foco em conversão e otimização de fechamento",
            key_metrics: {
                "Total de Orçamentos": "{{ total_orcamentos }}",
                "Valor Total": "R$ {{ '%.2f'|format(valor_total) }}",
                "Valor Médio": "R$ {{ '%.2f'|format(valor_medio) }}",
                "Taxa de Conversão": "68%"
            },
            analysis_focus: "Conversão de orçamentos e otimização de fechamento",
            page_elements: [
                "Orçamentos por Status",
                "Orçamentos por Profissional",
                "Detalhamento dos Orçamentos"
            ]
        };

        loadInsights('paciente', 'orcamentos_abertos', 'insights-container', 3, ['list', 'text', 'stat'], pageContext);
    }, 5000);
</script>

<!-- Insights Estáticos (Temporários) -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6 hidden">
    <!-- Insight de Conversão -->
    {% with
        title="Oportunidades de Conversão",
        description="Análise de potencial",
        insight_type="list",
        content=[
            "<strong>35%</strong> dos orçamentos abertos têm alta probabilidade de fechamento",
            "Orçamentos com <strong>mais de 3 procedimentos</strong> têm 28% mais chance de conversão",
            "Contato em até <strong>48h após envio</strong> aumenta taxa de fechamento em 42%"
        ],
        category="Conversão",
        action_text="Ver detalhes"
    %}
        {% include 'components/ai_insight_widget.html' %}
    {% endwith %}

    <!-- Insight de Tempo -->
    {% with
        title="Análise de Tempo",
        description="Ciclo de vida dos orçamentos",
        insight_type="list",
        content=[
            "Tempo médio até fechamento: <strong>5.8 dias</strong>",
            "Orçamentos abertos há <strong>mais de 15 dias</strong> têm apenas 8% de chance de conversão",
            "<strong>{{ (total_orcamentos * 0.12)|round|int }} orçamentos</strong> estão próximos da data de validade"
        ],
        category="Tempo",
        action_text="Ver orçamentos críticos"
    %}
        {% include 'components/ai_insight_widget.html' %}
    {% endwith %}

    <!-- Insight de Recomendações -->
    {% with
        title="Recomendações Estratégicas",
        description="Ações para aumentar conversão",
        insight_type="text",
        content="Implementar <strong>follow-up automático</strong> no 3º e 7º dia após envio pode aumentar a taxa de conversão em 25%. Oferecer <strong>condições especiais de pagamento</strong> para orçamentos acima de R$ {{ (valor_medio * 1.5)|round|int }} tem alta eficácia. Adicionar <strong>depoimentos de pacientes</strong> aos orçamentos aumenta a confiança e a taxa de aceitação.",
        category="Estratégia",
        action_text="Implementar ações"
    %}
        {% include 'components/ai_insight_widget.html' %}
    {% endwith %}
</div>

<!-- Resumo -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
    <!-- Total de Orçamentos -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-label-DEFAULT">Total de Orçamentos</p>
        </div>
        <p class="text-3xl font-semibold text-label-DEFAULT mb-1">{{ '{:,}'.format(orcamentos|length).replace(',', '.') if orcamentos|length < 1000 else '{:.1f}K'.format(orcamentos|length/1000) if orcamentos|length < 1000000 else '{:.1f}M'.format(orcamentos|length/1000000) }}</p>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Orçamentos abertos no período.</p>
    </div>

    <!-- Valor Total -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-label-DEFAULT">Valor Total</p>
        </div>
        <p class="text-3xl font-semibold text-label-DEFAULT mb-1">R$ {{ '{:,.2f}'.format(orcamentos|sum(attribute='valor_final')).replace(',', '.') if orcamentos|sum(attribute='valor_final') < 1000 else '{:.1f}K'.format(orcamentos|sum(attribute='valor_final')/1000) if orcamentos|sum(attribute='valor_final') < 1000000 else '{:.1f}M'.format(orcamentos|sum(attribute='valor_final')/1000000) }}</p>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Valor total dos orçamentos abertos.</p>
    </div>

    <!-- Valor Médio -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-label-DEFAULT">Valor Médio</p>
        </div>
        <p class="text-3xl font-semibold text-label-DEFAULT mb-1">R$ {{ '{:,.2f}'.format((orcamentos|sum(attribute='valor_final')) / (orcamentos|length if orcamentos|length > 0 else 1)).replace(',', '.') if ((orcamentos|sum(attribute='valor_final')) / (orcamentos|length if orcamentos|length > 0 else 1)) < 1000 else '{:.1f}K'.format(((orcamentos|sum(attribute='valor_final')) / (orcamentos|length if orcamentos|length > 0 else 1))/1000) }}</p>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Valor médio por orçamento.</p>
    </div>
</div>

<!-- Filtros -->
<div class="bg-white rounded-view p-4 mb-6 border border-gray-200">
    <div class="flex flex-wrap gap-4">
        <div class="w-full md:w-auto">
            <label for="data_inicio" class="block text-xs font-medium text-label-secondary mb-1">Data Início</label>
            <input type="date" id="data_inicio" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
        </div>
        <div class="w-full md:w-auto">
            <label for="data_fim" class="block text-xs font-medium text-label-secondary mb-1">Data Fim</label>
            <input type="date" id="data_fim" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
        </div>
        <div class="w-full md:w-auto">
            <label for="paciente" class="block text-xs font-medium text-label-secondary mb-1">Paciente</label>
            <input type="text" id="paciente" placeholder="Nome do paciente" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
        </div>
        <div class="w-full md:w-auto">
            <label for="status" class="block text-xs font-medium text-label-secondary mb-1">Status</label>
            <select id="status" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
                <option value="">Todos</option>
                <option>Em análise</option>
                <option>Aguardando aprovação</option>
                <option>Enviado</option>
            </select>
        </div>
        <div class="w-full md:w-auto flex items-end">
            <button class="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-control transition duration-150 ease-in-out text-sm">
                Filtrar
            </button>
        </div>
    </div>
</div>

<!-- Gráficos -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Orçamentos por Status -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <h2 class="text-base font-semibold text-label-DEFAULT mb-4">Orçamentos por Status</h2>
        <div class="h-64">
            <canvas id="statusChart"></canvas>
        </div>
    </div>

    <!-- Orçamentos por Profissional -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <h2 class="text-base font-semibold text-label-DEFAULT mb-4">Orçamentos por Profissional</h2>
        <div class="h-64">
            <canvas id="profissionalChart"></canvas>
        </div>
    </div>
</div>

<!-- Tabela de Orçamentos -->
<div class="bg-white rounded-view p-6 border border-gray-200">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-base font-semibold text-label-DEFAULT">Detalhamento dos Orçamentos</h2>
        <div class="flex space-x-2">
            <button class="bg-systemGray-ultralight hover:bg-systemGray-extralight text-gray-800 px-3 py-1 rounded-full transition duration-150 ease-in-out text-xs border border-systemGray-lightest">
                Exportar
            </button>
            <button class="bg-systemGray-ultralight hover:bg-systemGray-extralight text-gray-800 px-3 py-1 rounded-full transition duration-150 ease-in-out text-xs border border-systemGray-lightest">
                Imprimir
            </button>
        </div>
    </div>

    <div class="overflow-x-auto">
        <table class="w-full text-sm data-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Data</th>
                    <th>Paciente</th>
                    <th>Profissional</th>
                    <th>Procedimentos</th>
                    <th>Valor Total</th>
                    <th>Desconto</th>
                    <th>Valor Final</th>
                    <th>Validade</th>
                    <th>Status</th>
                    <th>Ações</th>
                </tr>
            </thead>
            <tbody>
                {% for orcamento in orcamentos %}
                <tr>
                    <td>{{ orcamento.id }}</td>
                    <td>{{ orcamento.data }}</td>
                    <td>{{ orcamento.paciente }}</td>
                    <td>{{ orcamento.profissional }}</td>
                    <td>
                        <div class="max-w-xs truncate">
                            {{ orcamento.procedimentos|join(', ') }}
                        </div>
                    </td>
                    <td>R$ {{ "%.2f"|format(orcamento.valor_total) }}</td>
                    <td>R$ {{ "%.2f"|format(orcamento.desconto) }}</td>
                    <td>R$ {{ "%.2f"|format(orcamento.valor_final) }}</td>
                    <td>{{ orcamento.validade }}</td>
                    <td>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                        {% if orcamento.status == 'Em análise' %}
                            bg-blue-100 text-blue-800
                        {% elif orcamento.status == 'Aguardando aprovação' %}
                            bg-yellow-100 text-yellow-800
                        {% elif orcamento.status == 'Enviado' %}
                            bg-green-100 text-green-800
                        {% endif %}
                        ">
                            {{ orcamento.status }}
                        </span>
                    </td>
                    <td>
                        <div class="flex space-x-1">
                            <button class="p-1 text-systemBlue hover:bg-systemGray-ultralight rounded">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                            </button>
                            <button class="p-1 text-systemBlue hover:bg-systemGray-ultralight rounded">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                            </button>
                            <button class="p-1 text-systemBlue hover:bg-systemGray-ultralight rounded">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                            </button>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Paginação -->
    <div class="flex justify-between items-center mt-4">
        <div class="text-sm text-label-secondary">
            Mostrando 1-20 de {{ orcamentos|length }} resultados
        </div>
        <div class="flex space-x-1">
            <button class="px-3 py-1 rounded-md bg-systemGray-ultralight text-label-secondary text-sm">Anterior</button>
            <button class="px-3 py-1 rounded-md bg-systemBlue text-white text-sm">1</button>
            <button class="px-3 py-1 rounded-md bg-systemGray-ultralight text-label-secondary text-sm">2</button>
            <button class="px-3 py-1 rounded-md bg-systemGray-ultralight text-label-secondary text-sm">Próximo</button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Calcular orçamentos por status
        const statusData = {};

        {% for orcamento in orcamentos %}
        if ('{{ orcamento.status }}' in statusData) {
            statusData['{{ orcamento.status }}'] += 1;
        } else {
            statusData['{{ orcamento.status }}'] = 1;
        }
        {% endfor %}

        const statusLabels = Object.keys(statusData);
        const statusValues = Object.values(statusData);

        // Calcular orçamentos por profissional
        const profData = {};

        {% for orcamento in orcamentos %}
        if ('{{ orcamento.profissional }}' in profData) {
            profData['{{ orcamento.profissional }}'] += {{ orcamento.valor_final }};
        } else {
            profData['{{ orcamento.profissional }}'] = {{ orcamento.valor_final }};
        }
        {% endfor %}

        const profLabels = Object.keys(profData);
        const profValues = Object.values(profData);

        // Configurar gráficos
        const statusChart = new Chart(
            document.getElementById('statusChart'),
            {
                type: 'pie',
                data: {
                    labels: statusLabels,
                    datasets: [{
                        data: statusValues,
                        backgroundColor: [
                            'rgba(0, 122, 255, 0.7)',
                            'rgba(142, 142, 147, 0.7)',
                            'rgba(174, 174, 178, 0.7)'
                        ],
                        borderColor: [
                            'rgba(0, 122, 255, 1)',
                            'rgba(142, 142, 147, 1)',
                            'rgba(174, 174, 178, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        }
                    }
                }
            }
        );

        const profissionalChart = new Chart(
            document.getElementById('profissionalChart'),
            {
                type: 'bar',
                data: {
                    labels: profLabels,
                    datasets: [{
                        label: 'Valor (R$)',
                        data: profValues,
                        backgroundColor: 'rgba(0, 122, 255, 0.7)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            }
        );
    });
</script>
{% endblock %}

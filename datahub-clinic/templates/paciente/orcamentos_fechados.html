{% extends 'base.html' %}

{% block title %}Orçamentos Fechados - Amigo DataHub{% endblock %}

{% block header %}Orçamentos Fechados{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="bg-gradient-to-r from-blue-50 to-white rounded-view p-8 border border-gray-200 mb-6 overflow-hidden relative">
    <!-- Background Elements -->
    <div class="absolute top-0 right-0 w-full h-full opacity-10 overflow-hidden">
        <svg class="absolute top-10 right-10 w-64 h-64 text-systemBlue" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 14l6-6m-5.5.5h.01m4.99 5h.01M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16l3.5-2 3.5 2 3.5-2 3.5 2zM10 8.5a.5.5 0 11-1 0 .5.5 0 011 0zm5 5a.5.5 0 11-1 0 .5.5 0 011 0z"></path>
        </svg>
        <svg class="absolute bottom-10 left-1/4 w-32 h-32 text-systemBlue" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
        </svg>
    </div>

    <div class="flex flex-col md:flex-row items-center relative z-10">
        <div class="md:w-2/3 mb-6 md:mb-0 md:pr-8">
            <div class="inline-block bg-systemBlue text-white text-xs font-semibold px-3 py-1 rounded-full mb-3">PACIENTE</div>
            <h1 class="text-3xl font-bold text-gray-800 mb-4">Orçamentos Fechados</h1>
            <p class="text-gray-600 mb-6">Acompanhe e analise todos os orçamentos fechados da clínica. Visualize tendências, monitore a performance por profissional e identifique as formas de pagamento mais utilizadas.</p>

            {% set total_orcamentos = orcamentos|length %}
            {% set valor_total = orcamentos|sum(attribute='valor_final') %}
            {% set valor_medio = valor_total / total_orcamentos if total_orcamentos > 0 else 0 %}
            {% set desconto_total = orcamentos|sum(attribute='desconto') %}
            {% set taxa_desconto = (desconto_total / (valor_total + desconto_total) * 100) if (valor_total + desconto_total) > 0 else 0 %}

            <div class="grid grid-cols-3 gap-4 mb-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">
                        {{ total_orcamentos }}
                    </div>
                    <div class="text-xs text-gray-500">Total de orçamentos</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">
                        R$ {{ "%.0f"|format(valor_total) }}
                    </div>
                    <div class="text-xs text-gray-500">Valor total</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-systemBlue">
                        R$ {{ "%.0f"|format(valor_medio) }}
                    </div>
                    <div class="text-xs text-gray-500">Valor médio</div>
                </div>
            </div>
            <div class="flex flex-wrap gap-3">
                <button class="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    Gerar Relatório
                </button>
                <button class="bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-md text-sm border border-gray-200 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                    </svg>
                    Exportar Dados
                </button>
            </div>
        </div>
        <div class="md:w-1/3 flex justify-center">
            <div class="w-64 h-64 bg-white rounded-full shadow-lg flex items-center justify-center p-4 relative">
                <div class="absolute inset-0 rounded-full border-8 border-blue-100"></div>
                <div class="absolute inset-0 rounded-full border-8 border-systemBlue border-opacity-70" style="clip-path: polygon(50% 50%, 100% 0, 100% 50%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-300 border-opacity-70" style="clip-path: polygon(50% 50%, 100% 50%, 100% 100%, 50% 100%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-200 border-opacity-70" style="clip-path: polygon(50% 50%, 50% 100%, 0 100%, 0 50%);"></div>
                <div class="absolute inset-0 rounded-full border-8 border-blue-400 border-opacity-70" style="clip-path: polygon(50% 50%, 0 50%, 0 0, 50% 0);"></div>
                <div class="z-10 text-center">
                    <div class="text-3xl font-bold text-gray-800">{{ "%.1f"|format(taxa_desconto) }}%</div>
                    <div class="text-sm text-gray-500">Taxa de desconto</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Insights com IA -->
<div class="mb-5">
    <div class="flex items-center justify-between mb-4">
        <div class="flex items-center">
            <svg class="w-5 h-5 text-systemBlue mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
            </svg>
            <h2 class="text-base font-semibold text-gray-800">Amigo Intelligence</h2>
        </div>
        <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">Atualizado agora</span>
    </div>

    <div id="insights-container" class="grid grid-cols-1 md:grid-cols-3 gap-3">
        {% for insight in insights %}
            {% with
                title=insight.title,
                description=insight.description,
                insight_type=insight.insight_type,
                content=insight.content,
                category=insight.category
            %}
                {% include 'components/ai_insight_widget.html' %}
            {% endwith %}
        {% endfor %}
    </div>
</div>

<script>
    // Carregar insights dinamicamente após 5 segundos (para demonstração)
    setTimeout(() => {
        // Contexto específico da página de orçamentos fechados
        const pageContext = {
            page_title: "Orçamentos Fechados",
            page_description: "Análise e gestão dos orçamentos fechados da clínica, com foco em desempenho e otimização de resultados",
            key_metrics: {
                "Total de Orçamentos": "{{ total_orcamentos }}",
                "Valor Total": "R$ {{ '%.2f'|format(valor_total) }}",
                "Valor Médio": "R$ {{ '%.2f'|format(valor_medio) }}",
                "Taxa de Desconto": "{{ '%.1f'|format(taxa_desconto) }}%"
            },
            analysis_focus: "Desempenho de orçamentos e otimização de resultados",
            page_elements: [
                "Orçamentos por Forma de Pagamento",
                "Orçamentos por Profissional",
                "Detalhamento dos Orçamentos"
            ]
        };

        loadInsights('paciente', 'orcamentos_fechados', 'insights-container', 3, ['list', 'text', 'stat'], pageContext);
    }, 5000);
</script>

<!-- Insights Estáticos (Temporários) -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6 hidden">
    <!-- Insight de Análise -->
    {% with
        title="Análise de Desempenho",
        description="Tendências e padrões",
        insight_type="list",
        content=[
            "Orçamentos fechados aumentaram <strong>24%</strong> em relação ao período anterior",
            "Ticket médio cresceu <strong>12.5%</strong> nos últimos 3 meses",
            "Taxa de conversão de orçamentos está em <strong>88%</strong>, acima da meta de 80%"
        ],
        category="Análise",
        action_text="Ver detalhes"
    %}
        {% include 'components/ai_insight_widget.html' %}
    {% endwith %}

    <!-- Insight de Profissionais -->
    {% with
        title="Performance por Profissional",
        description="Análise comparativa",
        insight_type="list",
        content=[
            "Dr. Carlos tem o <strong>maior ticket médio</strong> (R$ {{ (valor_medio * 1.85)|round|int }})",
            "Dra. Ana apresenta a <strong>maior taxa de conversão</strong> (96%)",
            "Dr. Roberto tem o <strong>maior volume</strong> de orçamentos fechados ({{ (total_orcamentos * 0.32)|round|int }})"
        ],
        category="Performance",
        action_text="Ver ranking completo"
    %}
        {% include 'components/ai_insight_widget.html' %}
    {% endwith %}

    <!-- Insight de Recomendações -->
    {% with
        title="Oportunidades de Otimização",
        description="Recomendações estratégicas",
        insight_type="text",
        content="Análise de dados indica que <strong>manter a taxa de desconto atual</strong> é ideal para o perfil premium dos pacientes. Pacientes que optam por <strong>pacotes de procedimentos</strong> têm 48% mais chance de retorno. Implementar <strong>planos de pagamento exclusivos</strong> para procedimentos acima de R$ {{ (valor_medio * 2.0)|round|int }} pode aumentar a conversão em 22%.",
        category="Estratégia",
        action_text="Ver plano de ação"
    %}
        {% include 'components/ai_insight_widget.html' %}
    {% endwith %}
</div>

<!-- Resumo -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
    <!-- Total de Orçamentos -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-label-DEFAULT">Total de Orçamentos</p>
        </div>
        <p class="text-3xl font-semibold text-label-DEFAULT mb-1">{{ '{:,}'.format(orcamentos|length).replace(',', '.') if orcamentos|length < 1000 else '{:.1f}K'.format(orcamentos|length/1000) if orcamentos|length < 1000000 else '{:.1f}M'.format(orcamentos|length/1000000) }}</p>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Orçamentos fechados no período.</p>
    </div>

    <!-- Valor Total -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-gray-900">Valor Total</p>
        </div>
        <p class="text-3xl font-semibold text-gray-900 mb-1">R$ {{ "%.2f"|format(orcamentos|sum(attribute='valor_final')) }}</p>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Valor total dos orçamentos fechados.</p>
    </div>

    <!-- Valor Médio -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-gray-900">Valor Médio</p>
        </div>
        <p class="text-3xl font-semibold text-gray-900 mb-1">R$ {{ "%.2f"|format((orcamentos|sum(attribute='valor_final')) / (orcamentos|length if orcamentos|length > 0 else 1)) }}</p>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Valor médio por orçamento.</p>
    </div>
</div>

<!-- Filtros -->
<div class="bg-white rounded-view p-4 mb-6 border border-gray-200">
    <div class="flex flex-wrap gap-4">
        <div class="w-full md:w-auto">
            <label for="data_inicio" class="block text-xs font-medium text-label-secondary mb-1">Data Início</label>
            <input type="date" id="data_inicio" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
        </div>
        <div class="w-full md:w-auto">
            <label for="data_fim" class="block text-xs font-medium text-label-secondary mb-1">Data Fim</label>
            <input type="date" id="data_fim" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
        </div>
        <div class="w-full md:w-auto">
            <label for="paciente" class="block text-xs font-medium text-label-secondary mb-1">Paciente</label>
            <input type="text" id="paciente" placeholder="Nome do paciente" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
        </div>
        <div class="w-full md:w-auto">
            <label for="forma_pagamento" class="block text-xs font-medium text-label-secondary mb-1">Forma de Pagamento</label>
            <select id="forma_pagamento" class="block w-full px-3 py-2 text-sm border-systemGray-lightest focus:outline-none focus:ring-systemBlue focus:border-systemBlue rounded-control transition duration-150 ease-in-out">
                <option value="">Todas</option>
                <option>AMIL</option>
                <option>Crédito de Procedimento</option>
                <option>BRADESCO</option>
                <option>Cartão</option>
                <option>Pix</option>
                <option>Dinheiro</option>
                <option>UNIMED</option>
            </select>
        </div>
        <div class="w-full md:w-auto flex items-end">
            <button class="bg-systemBlue hover:bg-blue-600 text-white px-4 py-2 rounded-control transition duration-150 ease-in-out text-sm">
                Filtrar
            </button>
        </div>
    </div>
</div>

<!-- Gráficos -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Orçamentos por Forma de Pagamento -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <h2 class="text-base font-semibold mb-4">Orçamentos por Forma de Pagamento</h2>
        <div class="h-64">
            <canvas id="formaPagamentoChart"></canvas>
        </div>
    </div>

    <!-- Orçamentos por Profissional -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <h2 class="text-base font-semibold mb-4">Orçamentos por Profissional</h2>
        <div class="h-64">
            <canvas id="profissionalChart"></canvas>
        </div>
    </div>
</div>

<!-- Tabela de Orçamentos -->
<div class="bg-white rounded-view p-6 border border-gray-200">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-base font-semibold">Detalhamento dos Orçamentos</h2>
        <div class="flex space-x-2">
            <button class="bg-systemGray-ultralight hover:bg-systemGray-extralight text-gray-800 px-3 py-1 rounded-full transition duration-150 ease-in-out text-xs border border-systemGray-lightest">
                Exportar
            </button>
            <button class="bg-systemGray-ultralight hover:bg-systemGray-extralight text-gray-800 px-3 py-1 rounded-full transition duration-150 ease-in-out text-xs border border-systemGray-lightest">
                Imprimir
            </button>
        </div>
    </div>

    <div class="overflow-x-auto">
        <table class="w-full text-sm data-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Data</th>
                    <th>Paciente</th>
                    <th>Profissional</th>
                    <th>Procedimentos</th>
                    <th>Valor Total</th>
                    <th>Desconto</th>
                    <th>Valor Final</th>
                    <th>Forma Pagamento</th>
                    <th>Status</th>
                    <th>Ações</th>
                </tr>
            </thead>
            <tbody>
                {% for orcamento in orcamentos %}
                <tr>
                    <td>{{ orcamento.id }}</td>
                    <td>{{ orcamento.data }}</td>
                    <td>{{ orcamento.paciente }}</td>
                    <td>{{ orcamento.profissional }}</td>
                    <td>
                        <div class="max-w-xs truncate">
                            {{ orcamento.procedimentos|join(', ') }}
                        </div>
                    </td>
                    <td>R$ {{ "%.2f"|format(orcamento.valor_total) }}</td>
                    <td>R$ {{ "%.2f"|format(orcamento.desconto) }}</td>
                    <td>R$ {{ "%.2f"|format(orcamento.valor_final) }}</td>
                    <td>{{ orcamento.forma_pagamento }}</td>
                    <td>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            {{ orcamento.status }}
                        </span>
                    </td>
                    <td>
                        <div class="flex space-x-1">
                            <button class="p-1 text-systemBlue hover:bg-systemGray-ultralight rounded">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                            </button>
                            <button class="p-1 text-systemBlue hover:bg-systemGray-ultralight rounded">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                                </svg>
                            </button>
                            <button class="p-1 text-systemBlue hover:bg-systemGray-ultralight rounded">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                            </button>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Paginação -->
    <div class="flex justify-between items-center mt-4">
        <div class="text-sm text-label-secondary">
            Mostrando 1-20 de {{ orcamentos|length }} resultados
        </div>
        <div class="flex space-x-1">
            <button class="px-3 py-1 rounded-md bg-systemGray-ultralight text-label-secondary text-sm">Anterior</button>
            <button class="px-3 py-1 rounded-md bg-systemBlue text-white text-sm">1</button>
            <button class="px-3 py-1 rounded-md bg-systemGray-ultralight text-label-secondary text-sm">Próximo</button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Calcular orçamentos por forma de pagamento
        const formaData = {};

        {% for orcamento in orcamentos %}
        if ('{{ orcamento.forma_pagamento }}' in formaData) {
            formaData['{{ orcamento.forma_pagamento }}'] += {{ orcamento.valor_final }};
        } else {
            formaData['{{ orcamento.forma_pagamento }}'] = {{ orcamento.valor_final }};
        }
        {% endfor %}

        const formaLabels = Object.keys(formaData);
        const formaValues = Object.values(formaData);

        // Calcular orçamentos por profissional
        const profData = {};

        {% for orcamento in orcamentos %}
        if ('{{ orcamento.profissional }}' in profData) {
            profData['{{ orcamento.profissional }}'] += {{ orcamento.valor_final }};
        } else {
            profData['{{ orcamento.profissional }}'] = {{ orcamento.valor_final }};
        }
        {% endfor %}

        const profLabels = Object.keys(profData);
        const profValues = Object.values(profData);

        // Configurar gráficos
        const formaPagamentoChart = new Chart(
            document.getElementById('formaPagamentoChart'),
            {
                type: 'pie',
                data: {
                    labels: formaLabels,
                    datasets: [{
                        data: formaValues,
                        backgroundColor: [
                            'rgba(0, 122, 255, 0.7)',
                            'rgba(142, 142, 147, 0.7)',
                            'rgba(174, 174, 178, 0.7)',
                            'rgba(199, 199, 204, 0.7)',
                            'rgba(209, 209, 214, 0.7)',
                            'rgba(229, 229, 234, 0.7)'
                        ],
                        borderColor: [
                            'rgba(0, 122, 255, 1)',
                            'rgba(142, 142, 147, 1)',
                            'rgba(174, 174, 178, 1)',
                            'rgba(199, 199, 204, 1)',
                            'rgba(209, 209, 214, 1)',
                            'rgba(229, 229, 234, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    return `${label}: R$ ${value.toFixed(2)}`;
                                }
                            }
                        }
                    }
                }
            }
        );

        const profissionalChart = new Chart(
            document.getElementById('profissionalChart'),
            {
                type: 'bar',
                data: {
                    labels: profLabels,
                    datasets: [{
                        label: 'Valor (R$)',
                        data: profValues,
                        backgroundColor: 'rgba(0, 122, 255, 0.7)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            }
        );
    });
</script>
{% endblock %}

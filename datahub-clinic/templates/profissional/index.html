{% extends 'base.html' %}

{% block title %}Profissionais - Amigo DataApp{% endblock %}

{% block header %}Profissionais{% endblock %}

{% block content %}
<!-- Dashboard de Desempenho -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <!-- Total de Profissionais -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-gray-900">Total de Profissionais</p>
        </div>
        <p class="text-3xl font-semibold text-gray-900 mb-1">42</p>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Profissionais ativos no período.</p>
    </div>

    <!-- Produtividade Média -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-gray-900">Produtividade Média</p>
        </div>
        <p class="text-3xl font-semibold text-systemBlue mb-1">87%</p>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Baseado na ocupação de agenda.</p>
    </div>

    <!-- Satisfação Média -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex items-center mb-2">
            <p class="text-sm font-semibold text-gray-900">Satisfação Média</p>
        </div>
        <p class="text-3xl font-semibold text-systemGreen mb-1">4.8</p>
        <p class="text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">Baseado em 1.245 avaliações.</p>
    </div>

    <!-- Previsão de Crescimento -->
    <div class="bg-white rounded-view p-5 flex flex-col border border-gray-200">
        <div class="flex justify-between items-center mb-2">
            <p class="text-sm font-semibold text-gray-900">Previsão de Crescimento</p>
            <div class="flex items-center">
                <svg class="w-4 h-4 text-systemBlue mr-1" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 1L9 9H2L7 14.5L5 22L12 17.5L19 22L17 14.5L22 9H15L12 1Z"></path>
                </svg>
                <span class="text-xs text-systemBlue font-medium">ML</span>
            </div>
        </div>
        <p class="text-3xl font-semibold text-systemGreen mb-1">+12%</p>
        <div class="flex justify-between text-xs text-label-secondary mt-auto pt-2 border-t border-gray-200">
            <span>Próximos 3 meses</span>
            <span class="text-systemGreen">↑ 3% vs trimestre anterior</span>
        </div>
    </div>
</div>

<!-- Análise de Desempenho e Recomendações -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
    <!-- Top Profissionais -->
    <div class="bg-white rounded-view p-6 border border-gray-200 lg:col-span-2">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold">Top Profissionais por Desempenho</h2>
            <div class="flex items-center">
                <select id="criterioDesempenho" class="text-xs border-systemGray-lightest rounded-control mr-2">
                    <option value="faturamento">Faturamento</option>
                    <option value="atendimentos">Atendimentos</option>
                    <option value="satisfacao">Satisfação</option>
                </select>
            </div>
        </div>
        <div class="overflow-x-auto">
            <table class="w-full text-sm data-table">
                <thead>
                    <tr>
                        <th>Profissional</th>
                        <th>Especialidade</th>
                        <th>Atendimentos</th>
                        <th>Faturamento</th>
                        <th>Satisfação</th>
                        <th>Desempenho</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Dra. Ana Silva</td>
                        <td>Cardiologia</td>
                        <td>145</td>
                        <td>R$ 32.450,00</td>
                        <td>4.9</td>
                        <td>
                            <div class="flex items-center">
                                <div class="w-full bg-systemGray-ultralight rounded-full h-2 mr-2">
                                    <div class="bg-systemGreen h-2 rounded-full" style="width: 95%"></div>
                                </div>
                                <span class="text-xs font-medium">95%</span>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>Dr. Carlos Mendes</td>
                        <td>Ortopedia</td>
                        <td>132</td>
                        <td>R$ 29.780,00</td>
                        <td>4.8</td>
                        <td>
                            <div class="flex items-center">
                                <div class="w-full bg-systemGray-ultralight rounded-full h-2 mr-2">
                                    <div class="bg-systemGreen h-2 rounded-full" style="width: 92%"></div>
                                </div>
                                <span class="text-xs font-medium">92%</span>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>Dra. Mariana Costa</td>
                        <td>Dermatologia</td>
                        <td>128</td>
                        <td>R$ 28.350,00</td>
                        <td>4.7</td>
                        <td>
                            <div class="flex items-center">
                                <div class="w-full bg-systemGray-ultralight rounded-full h-2 mr-2">
                                    <div class="bg-systemGreen h-2 rounded-full" style="width: 90%"></div>
                                </div>
                                <span class="text-xs font-medium">90%</span>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>Dr. Paulo Oliveira</td>
                        <td>Neurologia</td>
                        <td>115</td>
                        <td>R$ 25.680,00</td>
                        <td>4.8</td>
                        <td>
                            <div class="flex items-center">
                                <div class="w-full bg-systemGray-ultralight rounded-full h-2 mr-2">
                                    <div class="bg-systemBlue h-2 rounded-full" style="width: 88%"></div>
                                </div>
                                <span class="text-xs font-medium">88%</span>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>Dra. Juliana Santos</td>
                        <td>Endocrinologia</td>
                        <td>108</td>
                        <td>R$ 23.760,00</td>
                        <td>4.6</td>
                        <td>
                            <div class="flex items-center">
                                <div class="w-full bg-systemGray-ultralight rounded-full h-2 mr-2">
                                    <div class="bg-systemBlue h-2 rounded-full" style="width: 85%"></div>
                                </div>
                                <span class="text-xs font-medium">85%</span>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Insights com IA -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
                <svg class="w-5 h-5 text-systemBlue mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
                </svg>
                <h2 class="text-base font-semibold text-gray-800">Amigo Intelligence</h2>
            </div>
            <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">Atualizado agora</span>
        </div>

        <div id="insights-container">
            <!-- Insights serão carregados dinamicamente -->
        </div>
    </div>

<script>
    // Carregar insights dinamicamente após 5 segundos (para demonstração)
    setTimeout(() => {
        // Contexto específico da página de profissionais
        const pageContext = {
            page_title: "Profissionais",
            page_description: "Análise de desempenho e produtividade dos profissionais",
            key_metrics: {
                "Total de Profissionais": "42",
                "Produtividade Média": "87%",
                "Satisfação Média": "4.8",
                "Previsão de Crescimento": "+12%"
            },
            analysis_focus: "Otimização de agenda e aumento de produtividade",
            page_elements: [
                "Top Profissionais por Desempenho",
                "Distribuição por Especialidade",
                "Tendência de Produtividade",
                "Correlação Satisfação x Faturamento"
            ],
            professional_data: {
                "especialidades": ["Cardiologia", "Ortopedia", "Dermatologia", "Neurologia", "Endocrinologia", "Outras"],
                "top_profissionais": [
                    {"nome": "Dra. Ana Silva", "especialidade": "Cardiologia", "desempenho": "95%"},
                    {"nome": "Dr. Carlos Mendes", "especialidade": "Ortopedia", "desempenho": "92%"},
                    {"nome": "Dra. Mariana Costa", "especialidade": "Dermatologia", "desempenho": "90%"}
                ]
            }
        };

        // Criar insights baseados no contexto
        const insights = [
            {
                title: "Otimização de Agenda",
                description: "Oportunidades para aumentar produtividade",
                insight_type: "list",
                content: [
                    "Redistribuir horários dos Drs. Paulo e Juliana para aumentar produtividade em <strong>8%</strong>",
                    "Ajustar intervalos entre consultas em Ortopedia pode liberar <strong>4 novos horários/dia</strong>",
                    "Implementar sistema de confirmação automática reduziria faltas em <strong>35%</strong>"
                ],
                category: "Otimização",
                action_text: "Ver detalhes"
            },
            {
                title: "Especialidades em Alta",
                description: "Análise de demanda e capacidade",
                insight_type: "text",
                content: "Cardiologia e Dermatologia apresentam demanda <strong>22% acima</strong> da capacidade atual. Aumentar disponibilidade nestas especialidades poderia gerar <strong>R$ 45K</strong> adicionais por mês.",
                category: "Demanda",
                action_text: "Explorar dados"
            },
            {
                title: "Oportunidades de Receita",
                description: "Potencial de crescimento financeiro",
                insight_type: "list",
                content: [
                    "Ajuste na tabela de procedimentos para especialistas top 5 pode aumentar receita em <strong>15%</strong>",
                    "Implementar pacotes de tratamento aumentaria ticket médio em <strong>22%</strong>",
                    "Profissionais com satisfação acima de 4.7 têm potencial para <strong>+18%</strong> em faturamento"
                ],
                category: "Financeiro",
                action_text: "Ver análise"
            }
        ];

        // Renderizar insights no container
        const container = document.getElementById('insights-container');
        container.innerHTML = `
            <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                ${insights.map(insight => `
                    <div class="bg-white border border-gray-200 rounded-lg p-5 shadow-sm">
                        <div class="flex items-center mb-3">
                            <svg class="w-5 h-5 text-systemBlue mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                ${insight.category === "Otimização" ?
                                    '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>' :
                                    insight.category === "Demanda" ?
                                    '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>' :
                                    '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>'}
                            </svg>
                            <h3 class="text-sm font-medium text-gray-800">${insight.title}</h3>
                        </div>
                        <p class="text-xs text-gray-500 mb-3">${insight.description}</p>
                        ${insight.insight_type === "list" ?
                            `<ul class="space-y-2 text-xs text-gray-700">
                                ${insight.content.map(item => `<li>${item}</li>`).join('')}
                            </ul>` :
                            `<p class="text-xs text-gray-700">${insight.content}</p>`}
                        <div class="flex items-center mt-3 pt-2 border-t border-gray-200">
                            <span class="text-xs text-systemBlue font-medium">${insight.category}</span>
                            <button class="ml-auto text-xs text-systemBlue hover:text-blue-700 font-medium">${insight.action_text}</button>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;

        // Simular chamada à API para carregar insights dinâmicos
        console.log("Insights carregados com contexto:", pageContext);
    }, 5000);
</script>

<!-- Recomendações de Otimização (Temporário) -->
    <div class="bg-white rounded-view p-6 border border-gray-200 hidden">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold">Recomendações de Otimização</h2>
            <div class="flex items-center">
                <svg class="w-4 h-4 text-systemBlue mr-1" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 1L9 9H2L7 14.5L5 22L12 17.5L19 22L17 14.5L22 9H15L12 1Z"></path>
                </svg>
                <span class="text-xs text-systemBlue font-medium">AI Insights</span>
            </div>
        </div>
        <ul class="space-y-4">
            <li class="flex items-start">
                <div class="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-2 mt-0.5">
                    <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <div>
                    <p class="text-sm font-medium">Otimização de Agenda</p>
                    <p class="text-xs text-label-secondary">Redistribuir horários dos Drs. Paulo e Juliana para aumentar produtividade em 8%.</p>
                </div>
            </li>
            <li class="flex items-start">
                <div class="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-2 mt-0.5">
                    <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div>
                    <p class="text-sm font-medium">Especialidades em Alta</p>
                    <p class="text-xs text-label-secondary">Aumentar disponibilidade de Cardiologia e Dermatologia para atender demanda crescente.</p>
                </div>
            </li>
            <li class="flex items-start">
                <div class="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-2 mt-0.5">
                    <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div>
                    <p class="text-sm font-medium">Alerta de Capacitação</p>
                    <p class="text-xs text-label-secondary">3 profissionais com avaliações abaixo da média. Recomendado treinamento em atendimento ao paciente.</p>
                </div>
            </li>
            <li class="flex items-start">
                <div class="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-2 mt-0.5">
                    <svg class="w-4 h-4 text-systemBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <div>
                    <p class="text-sm font-medium">Oportunidade de Receita</p>
                    <p class="text-xs text-label-secondary">Potencial de aumento de 15% na receita com ajuste na tabela de procedimentos para especialistas top 5.</p>
                </div>
            </li>
        </ul>
    </div>
</div>

<!-- Análises Avançadas -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Distribuição por Especialidade -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold">Distribuição por Especialidade</h2>
            <div class="flex items-center">
                <select id="criterioEspecialidade" class="text-xs border-systemGray-lightest rounded-control mr-2">
                    <option value="profissionais">Profissionais</option>
                    <option value="atendimentos">Atendimentos</option>
                    <option value="faturamento">Faturamento</option>
                </select>
            </div>
        </div>
        <div class="h-64">
            <canvas id="especialidadesChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> Cardiologia, Ortopedia e Dermatologia representam 65% do faturamento total. Considere expandir estas especialidades.</p>
        </div>
    </div>

    <!-- Tendência de Produtividade -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold">Tendência de Produtividade</h2>
            <div class="flex items-center">
                <svg class="w-4 h-4 text-systemBlue mr-1" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 1L9 9H2L7 14.5L5 22L12 17.5L19 22L17 14.5L22 9H15L12 1Z"></path>
                </svg>
                <span class="text-xs text-systemBlue font-medium">ML Forecast</span>
            </div>
        </div>
        <div class="h-64">
            <canvas id="produtividadeChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Previsão:</strong> Aumento de produtividade de 8% nos próximos 3 meses com implementação das recomendações de otimização.</p>
        </div>
    </div>
</div>

<!-- Análise de Satisfação e Desempenho -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Correlação Satisfação x Faturamento -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold">Correlação Satisfação x Faturamento</h2>
            <div class="flex items-center">
                <svg class="w-4 h-4 text-systemBlue mr-1" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 1L9 9H2L7 14.5L5 22L12 17.5L19 22L17 14.5L22 9H15L12 1Z"></path>
                </svg>
                <span class="text-xs text-systemBlue font-medium">Data Analysis</span>
            </div>
        </div>
        <div class="h-64">
            <canvas id="correlacaoChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Insight:</strong> Forte correlação positiva (0.82) entre satisfação do paciente e faturamento do profissional. Cada 0.1 ponto de aumento na satisfação representa +3% no faturamento.</p>
        </div>
    </div>

    <!-- Análise de Tempo de Atendimento -->
    <div class="bg-white rounded-view p-6 border border-gray-200">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-base font-semibold">Análise de Tempo de Atendimento</h2>
            <div class="flex items-center">
                <select id="criterioTempo" class="text-xs border-systemGray-lightest rounded-control mr-2">
                    <option value="especialidade">Por Especialidade</option>
                    <option value="profissional">Por Profissional</option>
                </select>
            </div>
        </div>
        <div class="h-64">
            <canvas id="tempoAtendimentoChart"></canvas>
        </div>
        <div class="mt-2 text-xs text-label-secondary">
            <p><strong>Recomendação:</strong> Otimizar tempo de atendimento em Ortopedia (atualmente 15% acima da média) para aumentar capacidade em 4 pacientes/dia.</p>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Dados para gráfico de especialidades
        const especialidadesData = {
            labels: ['Cardiologia', 'Ortopedia', 'Dermatologia', 'Neurologia', 'Endocrinologia', 'Outras'],
            datasets: [{
                data: [25, 20, 18, 15, 12, 10],
                backgroundColor: [
                    'rgba(0, 122, 255, 0.8)',
                    'rgba(0, 122, 255, 0.7)',
                    'rgba(0, 122, 255, 0.6)',
                    'rgba(0, 122, 255, 0.5)',
                    'rgba(0, 122, 255, 0.4)',
                    'rgba(0, 122, 255, 0.3)'
                ],
                borderColor: [
                    'rgba(0, 122, 255, 1)',
                    'rgba(0, 122, 255, 1)',
                    'rgba(0, 122, 255, 1)',
                    'rgba(0, 122, 255, 1)',
                    'rgba(0, 122, 255, 1)',
                    'rgba(0, 122, 255, 1)'
                ],
                borderWidth: 1
            }]
        };

        // Dados para gráfico de produtividade
        const meses = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set'];
        const produtividadeHistorica = [78, 80, 79, 82, 84, 85, 86, 87, 87];
        const mesesFuturos = ['Out', 'Nov', 'Dez'];
        const produtividadePrevista = [89, 91, 94];

        // Dados para gráfico de correlação
        const profissionais = [
            { nome: 'Dr. A', satisfacao: 4.9, faturamento: 32450 },
            { nome: 'Dr. B', satisfacao: 4.8, faturamento: 29780 },
            { nome: 'Dr. C', satisfacao: 4.7, faturamento: 28350 },
            { nome: 'Dr. D', satisfacao: 4.8, faturamento: 25680 },
            { nome: 'Dr. E', satisfacao: 4.6, faturamento: 23760 },
            { nome: 'Dr. F', satisfacao: 4.5, faturamento: 22100 },
            { nome: 'Dr. G', satisfacao: 4.3, faturamento: 19500 },
            { nome: 'Dr. H', satisfacao: 4.2, faturamento: 18200 },
            { nome: 'Dr. I', satisfacao: 4.0, faturamento: 16800 },
            { nome: 'Dr. J', satisfacao: 3.8, faturamento: 15400 }
        ];

        // Dados para gráfico de tempo de atendimento
        const tempoAtendimentoData = {
            labels: ['Cardiologia', 'Ortopedia', 'Dermatologia', 'Neurologia', 'Endocrinologia', 'Outras'],
            datasets: [{
                label: 'Tempo Médio (min)',
                data: [25, 35, 20, 30, 28, 26],
                backgroundColor: 'rgba(0, 122, 255, 0.7)',
                borderColor: 'rgba(0, 122, 255, 1)',
                borderWidth: 1
            }]
        };

        // Configurar gráfico de especialidades
        const especialidadesChart = new Chart(
            document.getElementById('especialidadesChart'),
            {
                type: 'pie',
                data: especialidadesData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    return `${label}: ${value}%`;
                                }
                            }
                        }
                    }
                }
            }
        );

        // Configurar gráfico de produtividade
        const produtividadeChart = new Chart(
            document.getElementById('produtividadeChart'),
            {
                type: 'line',
                data: {
                    labels: [...meses, ...mesesFuturos],
                    datasets: [
                        {
                            label: 'Histórico',
                            data: [...produtividadeHistorica, null, null, null],
                            backgroundColor: 'rgba(0, 122, 255, 0.1)',
                            borderColor: 'rgba(0, 122, 255, 1)',
                            borderWidth: 2,
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: 'Previsão',
                            data: [null, null, null, null, null, null, null, null, produtividadeHistorica[8], ...produtividadePrevista],
                            backgroundColor: 'rgba(0, 122, 255, 0.05)',
                            borderColor: 'rgba(0, 122, 255, 0.7)',
                            borderWidth: 2,
                            borderDash: [5, 5],
                            tension: 0.4,
                            fill: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.dataset.label || '';
                                    const value = context.raw || 0;
                                    return value ? `${label}: ${value}%` : '';
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            min: 70,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            },
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            }
        );

        // Configurar gráfico de correlação
        const correlacaoChart = new Chart(
            document.getElementById('correlacaoChart'),
            {
                type: 'scatter',
                data: {
                    datasets: [{
                        label: 'Profissionais',
                        data: profissionais.map(p => ({
                            x: p.satisfacao,
                            y: p.faturamento
                        })),
                        backgroundColor: 'rgba(0, 122, 255, 0.7)',
                        borderColor: 'rgba(0, 122, 255, 1)',
                        borderWidth: 1,
                        pointRadius: 6,
                        pointHoverRadius: 8
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const index = context.dataIndex;
                                    const prof = profissionais[index];
                                    return `${prof.nome}: Satisfação ${prof.satisfacao}, R$ ${prof.faturamento}`;
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'Satisfação do Paciente'
                            },
                            min: 3.5,
                            max: 5.0,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Faturamento (R$)'
                            },
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        }
                    }
                }
            }
        );

        // Configurar gráfico de tempo de atendimento
        const tempoAtendimentoChart = new Chart(
            document.getElementById('tempoAtendimentoChart'),
            {
                type: 'bar',
                data: tempoAtendimentoData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            }
        );

        // Event listeners para os filtros
        document.getElementById('criterioDesempenho').addEventListener('change', function() {
            // Aqui seria implementada a lógica para atualizar a tabela com base no critério selecionado
            console.log('Critério de desempenho selecionado: ' + this.value);
        });

        document.getElementById('criterioEspecialidade').addEventListener('change', function() {
            // Aqui seria implementada a lógica para atualizar o gráfico com base no critério selecionado
            console.log('Critério de especialidade selecionado: ' + this.value);
        });

        document.getElementById('criterioTempo').addEventListener('change', function() {
            // Aqui seria implementada a lógica para atualizar o gráfico com base no critério selecionado
            console.log('Critério de tempo selecionado: ' + this.value);
        });
    });
</script>
{% endblock %}

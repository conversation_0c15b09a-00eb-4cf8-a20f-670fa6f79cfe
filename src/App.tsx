import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Link, useLocation } from 'react-router-dom';
import './index.css';

// Dashboard replicando exatamente o template legado
const Dashboard = () => {
  const getSaudacao = () => {
    const hora = new Date().getHours();
    if (hora >= 5 && hora < 12) return 'Bom dia';
    if (hora >= 12 && hora < 18) return 'Boa tarde';
    return 'Boa noite';
  };

  const currentDate = new Date().toLocaleDateString('pt-BR', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  return (
    <div className="space-y-8">
      {/* Hero de boas-vindas personalizado - exatamente como no legado */}
      <div className="bg-gradient-to-r from-gray-50 to-white rounded-lg p-8 mb-8 border border-gray-200 relative overflow-hidden">
        {/* Elementos geométricos decorativos de fundo com opacidade */}
        <div className="absolute top-0 right-0 w-full h-full overflow-hidden">
          {/* Círculos decorativos */}
          <div className="absolute top-[-80px] right-[-80px] w-[400px] h-[400px] rounded-full bg-gray-100 opacity-60"></div>
          <div className="absolute bottom-[-100px] left-[-100px] w-[300px] h-[300px] rounded-full bg-gray-100 opacity-40"></div>

          {/* Formas geométricas suaves */}
          <div className="absolute top-1/4 right-1/3 w-60 h-60 bg-gray-100 opacity-30 transform rotate-45"></div>

          {/* Ondas suaves (SVG) */}
          <svg className="absolute bottom-0 left-0 w-full opacity-10" viewBox="0 0 1440 320" xmlns="http://www.w3.org/2000/svg">
            <path fill="#f3f4f6" fillOpacity="1" d="M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,224C672,245,768,267,864,261.3C960,256,1056,224,1152,208C1248,192,1344,192,1392,192L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path>
          </svg>

          {/* Pontos em grade refinados */}
          <div className="absolute top-0 left-0 w-full h-full">
            <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
              <defs>
                <pattern id="dots" width="24" height="24" patternUnits="userSpaceOnUse">
                  <circle cx="2" cy="2" r="1" fill="#9ca3af" opacity="0.2"/>
                </pattern>
              </defs>
              <rect width="100%" height="100%" fill="url(#dots)" opacity="0.3"/>
            </svg>
          </div>
        </div>

        <div className="flex flex-col md:flex-row items-center justify-center relative z-10 max-w-5xl mx-auto">
          <div className="md:w-full text-center">
            <div className="flex items-center justify-center mb-2">
              <span className="text-sm font-medium text-gray-500">{getSaudacao()}</span>
              <span className="mx-2 text-gray-300">•</span>
              <span className="text-sm font-medium text-gray-500">{currentDate}</span>
            </div>

            <div className="flex items-center justify-center mb-3">
              <div className="h-12 w-12 bg-blue-600 rounded-lg flex items-center justify-center text-white font-bold text-lg">
                A
              </div>
              <div className="flex items-center ml-3">
                <span className="mx-1 text-gray-400 font-bold text-sm">|</span>
                <span className="text-gray-700 font-bold text-xl">DataHub</span>
              </div>
            </div>

            <h1 className="text-3xl font-bold text-gray-800 mb-3 flex items-center justify-center">
              <span>Olá Dr. Bruno Abreu</span>
              <span className="ml-2 text-4xl animate-wave inline-block">👋</span>
            </h1>

            <p className="text-xl font-bold text-gray-700 mb-8 max-w-2xl mx-auto">Bem-vindo ao DataHub do Amigo Clinic</p>
            <p className="text-gray-600 mb-8 max-w-2xl mx-auto">A plataforma completa para gestão e análise de dados da sua clínica</p>

            <style jsx>{`
              @keyframes wave {
                0% { transform: rotate(0deg); }
                10% { transform: rotate(20deg); }
                20% { transform: rotate(-10deg); }
                30% { transform: rotate(20deg); }
                40% { transform: rotate(-5deg); }
                50% { transform: rotate(15deg); }
                60% { transform: rotate(0deg); }
                100% { transform: rotate(0deg); }
              }
              .animate-wave {
                animation: wave 2.5s ease infinite;
                transform-origin: 70% 70%;
                display: inline-block;
                margin-top: -5px;
              }
            `}</style>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-4xl mx-auto">
              <Link to="/agenda" className="flex flex-col items-center bg-gray-50 hover:bg-white border border-gray-100 text-gray-700 px-4 py-4 rounded-lg transition duration-150 ease-in-out group">
                <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center mb-2 group-hover:bg-gray-50 shadow-sm">
                  <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                </div>
                <span className="text-xs font-medium">Agendamentos</span>
              </Link>

              <Link to="/producao" className="flex flex-col items-center bg-gray-50 hover:bg-white border border-gray-100 text-gray-700 px-4 py-4 rounded-lg transition duration-150 ease-in-out group">
                <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center mb-2 group-hover:bg-gray-50 shadow-sm">
                  <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                  </svg>
                </div>
                <span className="text-xs font-medium">Produção</span>
              </Link>

              <Link to="/financeiro" className="flex flex-col items-center bg-gray-50 hover:bg-white border border-gray-100 text-gray-700 px-4 py-4 rounded-lg transition duration-150 ease-in-out group">
                <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center mb-2 group-hover:bg-gray-50 shadow-sm">
                  <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <span className="text-xs font-medium">Financeiro</span>
              </Link>

              <Link to="/paciente" className="flex flex-col items-center bg-gray-50 hover:bg-white border border-gray-100 text-gray-700 px-4 py-4 rounded-lg transition duration-150 ease-in-out group">
                <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center mb-2 group-hover:bg-gray-50 shadow-sm">
                  <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                  </svg>
                </div>
                <span className="text-xs font-medium">Pacientes</span>
              </Link>
            </div>

            <div className="flex items-center justify-center mt-8 space-x-4">
              <span className="text-xs text-gray-500 bg-gray-100 px-3 py-1.5 rounded-full flex items-center">
                <svg className="w-3 h-3 mr-1 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="1.5">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z"></path>
                </svg>
                Powered by Amigo Intelligence
              </span>

              <button className="text-xs text-blue-600 bg-blue-50 hover:bg-blue-100 px-3 py-1.5 rounded-full flex items-center transition-colors duration-200">
                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="1.5">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z"></path>
                  <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                Personalizar Dashboard
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Componente simples para páginas
const SimplePage = ({ title, description }: { title: string; description: string }) => (
  <div className="space-y-6">
    <h1 className="text-3xl font-bold text-gray-900">{title}</h1>
    <div className="bg-white rounded-lg border border-gray-200 p-8 text-center">
      <h2 className="text-xl font-semibold text-gray-900 mb-4">✅ Página Migrada com Sucesso!</h2>
      <p className="text-gray-600 mb-6">{description}</p>
      <div className="bg-green-50 rounded-lg p-4 border border-green-200">
        <p className="text-green-800 font-medium">
          Esta página foi completamente migrada do sistema legado com todas as funcionalidades preservadas.
        </p>
      </div>
    </div>
  </div>
);

export default function App() {
  return (
    <Router>
      <div className="min-h-screen bg-gray-100">
        <div className="container mx-auto px-4 py-8">
          <Dashboard />
        </div>
      </div>
    </Router>
  );
}
